package com.bukuwarung.payments.deeplink.handler

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.dialogs.update.UpdateBottomSheet
import com.bukuwarung.neuro.api.Signal
import com.bukuwarung.payments.saldo.TopupSaldoActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject


class SaldoSignalHandler @Inject constructor() : BukuWarungSignalHandler() {

    companion object {
        const val saldoPath = "/saldo"
        const val saldoLink = "${AppConst.DEEPLINK_INTERNAL_URL}${saldoPath}"
    }

    override val paths: Set<String> = setOf(saldoPath)

    override fun handle(signal: Signal) {
        val context = signal.context
        val navigator = signal.navigator

        val entryPoint = signal.query[AnalyticsConst.ENTRY_POINT] as? String ?: AnalyticsConst.DEEPLINK

        checkIsLogin(context) {
            when {
                PaymentUtils.isUpdateRequired(PaymentConst.Feature.SALDO_IN) -> {
                    getFragmentManager(context)?.let {
                        UpdateBottomSheet.createInstance(
                            PaymentConst.Feature.SALDO_IN,
                            entryPoint
                        ).show(it, UpdateBottomSheet.TAG)
                    }
                }
                PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_SALDO_IN) -> {
                    getFragmentManager(context)?.let {
                        PaymentUtils.showKycKybStatusBottomSheet(it, entryPoint)
                    }
                }
                else -> navigator.navigate(TopupSaldoActivity.createIntent(context))
            }
        }
    }

    private fun getFragmentManager(context: Context): FragmentManager? {
        return try {
            (context as AppCompatActivity).supportFragmentManager
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
            null
        }
    }
}