package com.bukuwarung.payments.deeplink.handler

import android.content.Context
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Signal
import com.bukuwarung.payments.PaymentHistoryDetailsActivity
import com.bukuwarung.utils.isTrue
import javax.inject.Inject

class PaymentHistoryDetailsSignalHandler @Inject constructor() : BukuWarungSignalHandler() {

    override val paths: Set<String> = setOf(detailPath)

    override fun handle(signal: Signal) {
        val context = signal.context
        val navigator = signal.navigator

        checkIsLogin(context) {
            redirectToPaymentReceiptScreen(context, signal.query, navigator)
        }
    }

    private fun redirectToPaymentReceiptScreen(context: Context, queries: Map<String, Any>, navigator: Navigator) {
        val customerId = queries[CUSTOMER_ID] as? String
        val orderId = queries[ORDER_ID] as? String
        val paymentType = queries[PAYMENT_TYPE] as? String
        val isFromAssistPage = queries[IS_FROM_ASSIST_PAGE] as? Boolean
        val fromMainActivity = queries[FROM_MAIN_ACTIVITY] as? Boolean
        val isSuccess = queries[IS_SUCCESS] as? Boolean
        val displayName = queries[DISPLAY_NAME] as? String
        val entryPoint = queries[ENTRY_POINT] as? String
        val message = queries[MESSAGE] as? String
        val ledgerAccountId = queries[LEDGER_ACCOUNT_ID] as? String
        navigator.navigate(
            //if we add a new screen, we can add a condition here and redirect accordingly.
            PaymentHistoryDetailsActivity.createIntent(
                context = context,
                customerId = customerId,
                orderId = orderId,
                paymentType = paymentType,
                displayName = displayName,
                fromMainActivity = fromMainActivity.isTrue,
                isFromAssistPage = isFromAssistPage.isTrue,
                isSuccess = isSuccess.isTrue,
                entryPoint = entryPoint.orEmpty(),
                message = message.orEmpty(),
                ledgerAccountId = ledgerAccountId
            )
        )
    }

    companion object {
        private const val CUSTOMER_ID = "customerId"
        private const val ORDER_ID = "orderId"
        private const val PAYMENT_TYPE = "paymentType"
        private const val IS_FROM_ASSIST_PAGE = "isFromAssistPage"
        private const val IS_SUCCESS = "isSuccess"
        private const val DISPLAY_NAME = "displayName"
        private const val STATUS = "status"
        private const val ENTRY_POINT = "entryPoint"
        private const val MESSAGE = "message"
        private const val LEDGER_ACCOUNT_ID = "ledgerAccountId"
        private const val FROM_MAIN_ACTIVITY = "fromMainActivity"
        const val detailPath = "/payment/receipt"
        const val detailLink = "${AppConst.DEEPLINK_INTERNAL_URL}${detailPath}"

        fun getPaymentHistoryLink(
            orderId: String? = null,
            paymentType: String? = null,
            customerId: String? = null
        ) =
            "$detailLink?$ORDER_ID=${orderId}&$PAYMENT_TYPE=${paymentType}&$CUSTOMER_ID=${customerId}"
    }
}