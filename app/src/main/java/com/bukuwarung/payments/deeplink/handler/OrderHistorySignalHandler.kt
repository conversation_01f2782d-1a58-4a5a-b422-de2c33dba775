package com.bukuwarung.payments.deeplink.handler

import android.content.Context
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Signal
import com.bukuwarung.payments.history.OrderHistoryActivity
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utilities
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject


class OrderHistorySignalHandler @Inject constructor() : BukuWarungSignalHandler() {

    companion object {
        const val ordersPath = "/orders"
        private const val ordersLink = "${AppConst.DEEPLINK_INTERNAL_URL}${ordersPath}"
         fun getOrdersLink(activeTab: String, datePreset: String): String{
             return "$ordersLink?${OrderHistoryActivity.ACTIVE_TAB}=$activeTab"+
                     "&${OrderHistoryActivity.DATE_PRESET}=$datePreset"
         }
    }

    override val paths: Set<String> = setOf(ordersPath)

    override fun handle(signal: Signal) {
        val context = signal.context

        checkIsLogin(context) {
            val query = signal.query
            redirectToOrdersHistory(context, query, signal.navigator)
        }
    }

    private fun redirectToOrdersHistory(
        context: Context, queries: Map<String, Any>, navigator: Navigator
    ) {
        val tabString = queries[OrderHistoryActivity.ACTIVE_TAB] as? String
        val preselectedTab = tabString?.let {
            try {
                PaymentConst.HISTORY_TABS.valueOf(it)
            } catch (ex: Exception) {
                FirebaseCrashlytics.getInstance().recordException(ex)
                null
            }
        }

        val datePresetString = queries[OrderHistoryActivity.DATE_PRESET] as? String
        val datePreset = datePresetString?.let {
            try {
                PaymentConst.DATE_PRESET.valueOf(it)
            } catch (ex: Exception) {
                FirebaseCrashlytics.getInstance().recordException(ex)
                null
            }
        }

        val startDateInt = queries[OrderHistoryActivity.START_DATE] as? Int
        val endDateInt = queries[OrderHistoryActivity.END_DATE] as? Int
        val dateFilters: Pair<Long, Long>? =
            Utilities.safeLet(startDateInt, endDateInt) { startDate, endDate ->
                Pair(startDate.toLong(), endDate.toLong())
            }

        val productsString = queries[OrderHistoryActivity.PRODUCT_FILTERS] as? String
        val productFilters = productsString?.let { product ->
            product.split(",").map { it.trim() } as ArrayList<String>?
        }

        val statusString = queries[OrderHistoryActivity.STATUS_FILTERS] as? String
        val statusFilters = statusString?.let { status ->
            status.split(",").map { it.trim() } as ArrayList<String>?
        }

        navigator.navigate(
            OrderHistoryActivity.createIntent(
                context, SessionManager.getInstance().businessId,
                activeTab = preselectedTab,
                datePreset = datePreset,
                productFilters = productFilters,
                statusFilters = statusFilters,
                dateFilters = dateFilters,
                customerId = queries[OrderHistoryActivity.CUSTOMER_ID] as? String,
                billerCode = queries[OrderHistoryActivity.BILLER_CODE] as? String
            )
        )
    }
}