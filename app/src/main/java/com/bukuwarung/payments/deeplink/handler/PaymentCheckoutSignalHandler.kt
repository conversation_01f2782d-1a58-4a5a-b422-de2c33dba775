package com.bukuwarung.payments.deeplink.handler

import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.dialogs.update.UpdateBottomSheet
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Signal
import com.bukuwarung.payments.bottomsheet.PaymentOptionsBottomSheet
import com.bukuwarung.payments.checkout.PaymentCheckoutActivity
import com.bukuwarung.payments.constants.KycTier
import com.bukuwarung.payments.core.view.PaymentOutActivity
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject


class PaymentCheckoutSignalHandler @Inject constructor() : BukuWarungSignalHandler() {

    companion object {
        const val paymentInPath = "/payment_in"
        const val paymentOutPath = "/payment_out"
        const val paymentPath = "/payment_checkout"
        private const val paymentLink = "${AppConst.DEEPLINK_INTERNAL_URL}${paymentPath}"
        const val PAYMENT_TYPE = "paymentType"
        const val CUSTOMER_ID = "customerId"
        const val CUSTOMER_BALANCE = "customerBalance"
        const val ENTRY_POINT = "entryPoint"
        const val FROM = "from"
        const val PAYMENT_CATEGORY = "payment_category"
        fun getPaymentLink(
            paymentType: String, entryPoint: String? = null, from: String? = null,
            paymentCategory: String? = null,
            customerId: String? = null,
            customerBalance: Double? = null
        ): String {
            var deepLink = "$paymentLink?${PAYMENT_TYPE}=$paymentType" +
                    "&${ENTRY_POINT}=$entryPoint" +
                    "&${FROM}=$from" +
                    "&${PAYMENT_CATEGORY}=$paymentCategory" +
                    "&${CUSTOMER_BALANCE}=$customerBalance"
            if (customerId != null) {
                deepLink += "&${CUSTOMER_ID}=$customerId"
            }
            return deepLink
        }
    }

    override val paths: Set<String> = setOf(paymentInPath, paymentOutPath, paymentPath)

    override fun handle(signal: Signal) {
        val context = signal.context
        val navigator = signal.navigator

        checkIsLogin(context) {
            redirectToPaymentCheckout(context, signal.query, navigator)
        }
    }

    private fun redirectToPaymentCheckout(
        context: Context,
        queries: Map<String, Any>,
        navigator: Navigator
    ) {
        val from = queries[FROM] as? String
        val entryPoint =
            queries[ENTRY_POINT] as? String ?: AnalyticsConst.DEEPLINK
        val paymentTypeString = queries[PAYMENT_TYPE] as? String
        val paymentCategory = queries[PAYMENT_CATEGORY] as? String
        val customerId = queries[CUSTOMER_ID] as? String
        val customerBalance = queries[CUSTOMER_BALANCE] as? Double
        val paymentType =
            if (paymentTypeString == PaymentHistory.TYPE_PAYMENT_IN) PaymentConst.KYC_PAYMENT_IN else PaymentConst.KYC_PAYMENT_OUT
        val feature =
            if (paymentTypeString == PaymentHistory.TYPE_PAYMENT_IN) PaymentConst.Feature.PAYMENT_IN else PaymentConst.Feature.PAYMENT_OUT
        when {
            PaymentUtils.isUpdateRequired(feature) -> {
                try {
                    val fm = (context as FragmentActivity).supportFragmentManager
                    UpdateBottomSheet.createInstance(feature, entryPoint)
                        .show(fm, UpdateBottomSheet.TAG)
                } catch (ex: ClassCastException) {
                    FirebaseCrashlytics.getInstance().recordException(ex)
                }
            }
            PaymentUtils.shouldBeBlockedAsPerKycTier(paymentType) -> {
                try {
                    val fm = (context as FragmentActivity).supportFragmentManager
                    PaymentUtils.showKycKybStatusBottomSheet(fm, entryPoint)
                } catch (ex: ClassCastException) {
                    FirebaseCrashlytics.getInstance().recordException(ex)
                }
            }
            PaymentUtils.shouldBeBlockedDueToQuota(feature) -> {
                try {
                    // Since home page just triggers deeplink, we need to handle the case when
                    // the quota is reached and home page CTA is clicked, in which case we need to
                    // show KYB bottom sheet directly
                    val fm = (context as FragmentActivity).supportFragmentManager
                    if (entryPoint == AnalyticsConst.HOME || entryPoint == AnalyticsConst.HOME_PAGE) {
                        PaymentUtils.showKycKybStatusBottomSheet(fm, entryPoint)
                    } else {
                        PaymentOptionsBottomSheet.createInstance(entryPoint)
                            .show(fm, PaymentOptionsBottomSheet.TAG)
                    }
                } catch (ex: ClassCastException) {
                    FirebaseCrashlytics.getInstance().recordException(ex)
                }
            }
            else -> {
                if (paymentTypeString == PaymentHistory.TYPE_PAYMENT_OUT && !RemoteConfigUtils.showOldPaymentOutUi()) {
                    navigator.navigate(
                        PaymentOutActivity.createIntent(
                            context,
                            customerId = customerId,
                            customerBalance = customerBalance?.toDouble()
                        )
                    )
                } else {
                    if(PaymentPrefManager.getInstance().getKycTier() == KycTier.ADVANCED && PaymentUtils.isWhitelistedUser()){
                        //In case of whitelisted advance user, we dont want to allow the user to complete the payment in from android.
                        //do nothing
                    }else{
                        navigator.navigate(
                            PaymentCheckoutActivity.createIntent(
                                context,
                                paymentType = if (paymentTypeString == PaymentHistory.TYPE_PAYMENT_IN) PaymentConst.TYPE_PAYMENT_IN.toString() else PaymentConst.TYPE_PAYMENT_OUT.toString(),
                                customerId = customerId,
                                entryPoint = entryPoint,
                                bookId = SessionManager.getInstance().businessId,
                                from = from,
                                category = paymentCategory, customerBalance = customerBalance
                            )
                        )
                    }
                }

            }
        }
    }
}
