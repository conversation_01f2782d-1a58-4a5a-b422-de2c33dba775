package com.bukuwarung.payments.compoundviews

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.databinding.LayoutBillDetailExpandableBinding
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.constants.PpobConst.ADULT_TYPE
import com.bukuwarung.payments.constants.PpobConst.INFANT_TYPE
import com.bukuwarung.payments.data.model.Biller
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.TrainPassenger
import com.bukuwarung.utils.*

class BillDetailExpandableView : ConstraintLayout {

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr)

    val binding =
            LayoutBillDetailExpandableBinding.inflate(LayoutInflater.from(context), this, true)

    private var billDetailAdapter: BillDetailAdapter? = null
    private val billDetailList = ArrayList<BillDetailData>()
    private var iCommunicator: ICommunicator? = null
    private var isExpanded: Boolean = false
    private var isViewExpanded: Boolean = false

    companion object {
        private const val TOTAL_ITEM = 6
    }

    fun setTrainPassengerView(passenger: TrainPassenger) {
        billDetailList.clear()
        billDetailList.add(
            BillDetailData(
                context.getString(R.string.nama_pelanggan),
                passenger.name,
                R.color.black_80
            )
        )
        billDetailList.add(
            BillDetailData(
                if (passenger.type == ADULT_TYPE) context.getString(R.string.nik)
                else context.getString(R.string.nik_infant),
                passenger.idNumber
            )
        )

        if (passenger.type == ADULT_TYPE) {
            billDetailList.add(
                BillDetailData(
                    context.getString(R.string.seat),
                    passenger.seat
                )
            )
        }
        setBillDetailAdapter(orderFormPage = false)
    }

    fun setView(orderResponse: FinproOrderResponse?,
                orderFormPage: Boolean = false, biller: Biller? = null,
                iCommunicator: ICommunicator? = null, isTrainBillDetails:Boolean = false
    ) {
        this.iCommunicator = iCommunicator
        val item = orderResponse?.items?.firstOrNull()
        when (item?.beneficiary?.category.orEmpty()) {
            PpobConst.CATEGORY_PULSA -> {
                billDetailList.add(BillDetailData(context.getString(R.string.mobile_phone_label), item?.beneficiary?.phoneNumber.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.pulsa), item?.name.orDash))
            }
            PpobConst.CATEGORY_LISTRIK -> {
                if (item?.beneficiary?.code == PpobConst.CATEGORY_PLN_POSTPAID) {
                    billDetailList.add(BillDetailData(context.getString(R.string.customer_id_message), item.beneficiary.accountNumber.orDash, R.color.black_80))
                    billDetailList.add(BillDetailData(context.getString(R.string.input_customer_name), item.details.customerName.orDash, R.color.black_80))
                    item.beneficiary.phoneNumber?.let { if (Utilities.validatePhoneNumber(it,"")) billDetailList.add(BillDetailData(context.getString(R.string.customer_phone), it)) }
                    billDetailList.add(BillDetailData(context.getString(R.string.period), item.details.periode.orDash))
                    billDetailList.add(BillDetailData(context.getString(R.string.total_billing), item.details.totalLembarTagihan.orDash))
                    billDetailList.add(BillDetailData(context.getString(R.string.tarif), item.details.tarif.orDash))
                    billDetailList.add(BillDetailData(context.getString(R.string.bill_amount), Utility.formatAmount(orderResponse.cost), textColor = R.color.black_80, setHeadingStyle = true))
                } else {
                    billDetailList.add(BillDetailData(context.getString(R.string.input_customer_name), item?.details?.customerName.orDash, R.color.black_80))
                    billDetailList.add(BillDetailData(context.getString(R.string.customer_id_message), item?.beneficiary?.accountNumber.orDash, R.color.black_80))
                    billDetailList.add(BillDetailData(context.getString(R.string.nominal_token), item?.name.orDash))
                    billDetailList.add(BillDetailData(context.getString(R.string.tarif), item?.details?.tarif.orDash))
                    billDetailList.add(BillDetailData(context.getString(R.string.mobile_phone_label), item?.beneficiary?.phoneNumber.orDash))
                }
            }
            PpobConst.CATEGORY_EWALLET -> {
                billDetailList.add(BillDetailData(context.getString(R.string.input_customer_name), item?.details?.customerName.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.mobile_phone_label), item?.beneficiary?.accountNumber.orDash, R.color.black_80))
                billDetailList.add(
                    BillDetailData(
                        context.getString(R.string.ewallet_provider),
                        item?.details?.billerName.orDash
                    )
                )
                if (item?.details?.adminFee.isNotZero()) {
                    billDetailList.add(
                        BillDetailData(
                            context.getString(R.string.nominal_topup),
                            Utility.formatAmountWithoutRp(
                                item?.details?.amount ?: orderResponse?.amount
                            )
                        )
                    )
                    billDetailList.add(
                        BillDetailData(
                            context.getString(R.string.transaction_fees),
                            Utility.formatAmountWithoutRp(item?.details?.adminFee)
                        )
                    )
                } else {
                    billDetailList.add(
                        BillDetailData(
                            context.getString(R.string.nominal_topup),
                            item?.name.orDash
                        )
                    )
                }
            }
            PpobConst.CATEGORY_PAKET_DATA -> {
                billDetailList.add(BillDetailData(context.getString(R.string.mobile_phone_label), item?.beneficiary?.phoneNumber.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.packet_data), item?.name.orDash))
            }
            PpobConst.CATEGORY_PULSA_POSTPAID -> {
                billDetailList.add(BillDetailData(context.getString(R.string.input_customer_name), item?.details?.customerName.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.mobile_phone_label), item?.beneficiary?.phoneNumber.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.operator), item?.details?.productName.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.bill_amount), Utility.formatAmount(orderResponse?.cost), textColor = R.color.black_80, setHeadingStyle = true))
            }
            PpobConst.CATEGORY_VOUCHER_GAME -> {
                if (orderResponse?.metadata?.billerType.isNotNullOrBlank() && orderResponse?.metadata?.billerType == PpobConst.VOUCHER_TYPE_TOPUP) {
                    billDetailList.add(BillDetailData(context.getString(R.string.voucher_type), orderResponse.metadata.billerName.orDash))
                    billDetailList.add(BillDetailData(context.getString(R.string.produk), item?.name.orDash))
                    billDetailList.add(BillDetailData(orderResponse.metadata.idFieldName, orderResponse.metadata.idFieldValue.orDash))
                    item?.beneficiary?.phoneNumber?.let { if (Utilities.validatePhoneNumber(it,"")) billDetailList.add(BillDetailData(context.getString(R.string.customer_phone), it)) }
                } else {
                    billDetailList.add(BillDetailData(context.getString(R.string.mobile_phone_label), item?.beneficiary?.phoneNumber.orDash, R.color.black_80))
                    billDetailList.add(BillDetailData(context.getString(R.string.voucher_type), orderResponse?.metadata?.billerName.orDash))
                    billDetailList.add(BillDetailData(context.getString(R.string.produk), item?.name.orDash))
                }
            }
            PpobConst.CATEGORY_BPJS -> {
                billDetailList.add(BillDetailData(context.getString(R.string.input_customer_name), item?.details?.customerName.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.card_number), item?.details?.customerNumber.orDash, R.color.black_80))
                item?.beneficiary?.phoneNumber?.let { if (Utilities.validatePhoneNumber(it,"")) billDetailList.add(BillDetailData(context.getString(R.string.customer_phone), it)) }
                billDetailList.add(BillDetailData(context.getString(R.string.no_of_family), item?.details?.memberCount.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.period), item?.details?.period.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.bill_amount), Utility.formatAmount(orderResponse?.cost), textColor = R.color.black_80, setHeadingStyle = true))
            }
            PpobConst.CATEGORY_PDAM -> {
                billDetailList.add(BillDetailData(context.getString(R.string.input_customer_name), item?.details?.customerName.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.pdam_area), orderResponse?.metadata?.billerName.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.card_number), item?.details?.customerNumber.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.period), item?.details?.period.orDash))
                item?.beneficiary?.phoneNumber?.let { if (Utilities.validatePhoneNumber(it,"")) billDetailList.add(BillDetailData(context.getString(R.string.customer_phone), it)) }
                if (orderFormPage.isFalse && orderResponse?.metadata?.warningMessage.isNotNullOrBlank()) {
                    billDetailList.add(BillDetailData(context.getString(R.string.fine), Utility.formatAmount(orderResponse?.metadata?.fineAmount.orNil).orDash))
                }
                billDetailList.add(BillDetailData(context.getString(R.string.bill_amount), Utility.formatAmount(orderResponse?.cost), textColor = R.color.black_80, setHeadingStyle = true))
            }
            PpobConst.CATEGORY_MULTIFINANCE -> {
                billDetailList.add(BillDetailData(context.getString(R.string.input_customer_name), item?.details?.customerName.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.contract_number), item?.details?.customerNumber.orDash, R.color.black_80))
                item?.beneficiary?.phoneNumber?.let { if (Utilities.validatePhoneNumber(it,"")) billDetailList.add(BillDetailData(context.getString(R.string.customer_phone), it)) }
                billDetailList.add(BillDetailData(context.getString(R.string.loan_provider), orderResponse?.metadata?.billerName.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.period), item?.details?.period.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.installment_number), item?.details?.installmentNumber.orDash))
                item?.details?.fine?.let {
                    billDetailList.add(BillDetailData(context.getString(R.string.fine), it))
                }
                billDetailList.add(BillDetailData(context.getString(R.string.bill_amount), Utility.formatAmount(orderResponse?.cost), textColor = R.color.black_80, setHeadingStyle = true))
            }
            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                billDetailList.add(BillDetailData(context.getString(R.string.nama_pelanggan), item?.details?.customerName.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.customer_number), item?.details?.customerNumber.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.provider), orderResponse?.metadata?.billerName.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.period), item?.details?.period.orDash))
                item?.beneficiary?.phoneNumber?.let { if (Utilities.validatePhoneNumber(it,"")) billDetailList.add(BillDetailData(context.getString(R.string.customer_phone), it)) }
                if (orderResponse?.metadata?.warningMessage.isNotNullOrBlank()) {
                    billDetailList.add(BillDetailData(context.getString(R.string.fine), Utility.formatAmount(orderResponse?.metadata?.fineAmount.orNil)))
                }
                billDetailList.add(BillDetailData(context.getString(R.string.bill_amount), Utility.formatAmount(orderResponse?.cost), textColor = R.color.black_80, setHeadingStyle = true))
            }
            PpobConst.CATEGORY_VEHICLE_TAX -> {
                billDetailList.add(BillDetailData(context.getString(R.string.nama_pelanggan), item?.details?.customerName.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.policy_number), item?.details?.policyNumber.orDash, R.color.black_80))
                billDetailList.add(BillDetailData(context.getString(R.string.vehicle_brand), item?.details?.vehicleBrand.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.period), item?.details?.periode.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.mobile_phone_label), item?.details?.phoneNumber.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.bill_amount), Utility.formatAmount(orderResponse?.cost), textColor = R.color.black_80, setHeadingStyle = true))
                if (orderFormPage.isFalse && orderResponse?.metadata?.warningMessage.isNotNullOrBlank()) {
                    billDetailList.add(BillDetailData(context.getString(R.string.fine), Utility.formatAmount(orderResponse?.metadata?.fineAmount.orNil)))
                }
                billDetailList.add(BillDetailData(context.getString(R.string.transportation_type), item?.details?.vehicleName.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.vehicle_type), item?.details?.vehicleType.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.vehicle_colour), item?.details?.vehicleColor.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.vehicle_build_year), item?.details?.buildYear.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.machine_number), item?.details?.machineNumber.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.chassis_number), item?.details?.frameNumber.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.pkb), item?.details?.pkb.orDash))
                billDetailList.add(BillDetailData(context.getString(R.string.label_bill), Utility.formatAmount(item?.amount.orNil)))
            }
            PpobConst.CATEGORY_TRAIN_TICKET -> {
                val numberOfAdult =
                    item?.details?.trainPassenger?.filter { passenger -> passenger.type == ADULT_TYPE }?.size.orNil
                val numberOfChild =
                    item?.details?.trainPassenger?.filter { passenger -> passenger.type == INFANT_TYPE }?.size.orNil
                billDetailList.add(
                    BillDetailData(
                        context.getString(R.string.nama_pelanggan),
                        item?.details?.customerName.orDash,
                        R.color.black_80
                    )
                )
                billDetailList.add(
                    BillDetailData(
                        context.getString(R.string.mobile_phone_label),
                        item?.details?.customerNumber.orDash
                    )
                )
                billDetailList.add(
                    BillDetailData(
                        context.getString(R.string.e_mail_label),
                        item?.details?.customerEmail.orDash
                    )
                )
                if (!isTrainBillDetails) {
                    billDetailList.add(
                        BillDetailData(
                            context.getString(R.string.total_passenger),
                            if (numberOfChild > 0) {
                                "$numberOfAdult ${context.getString(R.string.adult)}, $numberOfChild ${
                                    context.getString(
                                        R.string.baby
                                    )
                                }"
                            } else {
                                "$numberOfAdult ${context.getString(R.string.adult)}"
                            }
                        )
                    )
                    billDetailList.add(
                        BillDetailData(
                            context.getString(R.string.route_label),
                            "${item?.details?.trainOriginStationCode} - ${item?.details?.trainDestinationStationCode}"
                        )
                    )
                    billDetailList.add(
                        BillDetailData(
                            context.getString(R.string.label_bill),
                            Utility.formatAmount(item?.amount.orNil)
                        )
                    )
                }
            }

        }
        setBillDetailAdapter(orderFormPage)
    }

    private fun setBillDetailAdapter(orderFormPage: Boolean) {
        with(binding) {
            rvBillDetail.layoutManager = LinearLayoutManager(context)
            billDetailAdapter = BillDetailAdapter(::expandViewClick, ::favButtonClick)
            rvBillDetail.adapter = billDetailAdapter
            if (billDetailList.size.orNil > TOTAL_ITEM) {
                billDetailAdapter?.setData(
                    billDetailList.subList(0, TOTAL_ITEM - 1),
                    showButtonItem = true,
                    showExpandedItem = false
                )
            } else {
                billDetailAdapter?.setData(
                    billDetailList,
                    showButtonItem = false,
                    showExpandedItem = false
                )
            }
            rvBillDetail.isNestedScrollingEnabled = false
            if (orderFormPage) {
                tvDetailTransaksi.setDrawable(right = R.drawable.ic_chevron_down)
                tvDetailTransaksi.setSingleClickListener {
                    if (isViewExpanded) {
                        tvDetailTransaksi.setDrawable(right = R.drawable.ic_chevron_down)
                        isViewExpanded = false
                        gpExpandable.hideView()
                    } else {
                        tvDetailTransaksi.setDrawable(right = R.drawable.ic_chevron_up)
                        isViewExpanded = true
                        gpExpandable.showView()
                    }
                }
            }
            gpExpandable.visibility = (!orderFormPage).asVisibility()
        }
    }

    private fun expandViewClick(showExpandView: Boolean) {
        isExpanded = showExpandView
        if (showExpandView) {
            billDetailAdapter?.setData(billDetailList, true, showExpandView)
        } else {
            billDetailAdapter?.setData(billDetailList.subList(0, TOTAL_ITEM - 1), true, showExpandView)
        }
        iCommunicator?.scrollViewDown()
    }

    private fun favButtonClick() {
        iCommunicator?.favButtonClick()
    }

    fun setFavouriteData(favName: String, isFavourite: Boolean) {
        if (isFavourite) {
            billDetailList.add(0, BillDetailData(context.getString(R.string.favourite_contact), favName, rightDrawable = R.drawable.ic_favourite_fill, isFavourite = true))
        } else {
            val favouriteItem = billDetailList.firstOrNull { it.key == context.getString(R.string.favourite_contact) }
            if (null != favouriteItem) {
                billDetailList.removeAt(billDetailList.indexOf(favouriteItem))
            }
        }
        val totalItem = if (isFavourite) {
            TOTAL_ITEM + 1
        } else {
            TOTAL_ITEM
        }
        if (!isExpanded && billDetailList.size.orNil > totalItem) {
            billDetailAdapter?.setData(billDetailList.subList(0, TOTAL_ITEM - 1), showButtonItem = true, showExpandedItem = isExpanded)
        } else {
            billDetailAdapter?.setData(billDetailList, showButtonItem = isExpanded, showExpandedItem = isExpanded)
        }
    }

    interface ICommunicator {
        fun scrollViewDown() {}
        fun favButtonClick() {}
    }
}