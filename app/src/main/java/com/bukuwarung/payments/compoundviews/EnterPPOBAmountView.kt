package com.bukuwarung.payments.compoundviews

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.R
import com.bukuwarung.databinding.TemplatePpobAmountBinding
import com.bukuwarung.payments.data.model.ppob.PricingType
import com.bukuwarung.payments.data.model.ppob.ProductsItem
import com.bukuwarung.payments.ppob.catalog.view.SetSellingPriceBottomSheet.Companion.SOURCE_ATUR
import com.bukuwarung.utils.*
import com.bukuwarung.utils.TooltipBuilder.Companion.builder
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip


class EnterPPOBAmountView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    val binding: TemplatePpobAmountBinding =
        TemplatePpobAmountBinding.inflate(LayoutInflater.from(context), this, true)
    private var stepChange: Double = 0.0
    private var sellingPrice = 0L
    private var tooltipTargetId = -1
    private var tooltip: SimpleTooltip? = null
    var shouldPreviousButtonBeEnabled: Boolean = false
    var shouldNextButtonBeEnabled: Boolean = false

    init {
        setClickListeners()
        setAmountHint()
    }

    fun setProductDetails(
        productItem: ProductsItem,
        pricingType: PricingType,
        stepChange: Double,
        minAdminFee: Double,
        maxAdminFee: Double,
        source: Int
    ) {
        with(binding) {
            etSellingPrice.requestFocus()
            resetButtonStates()
            <EMAIL> = stepChange
            <EMAIL> = productItem.pricing?.toLong() ?: 0L
            etSellingPrice.setNumberChangedAction { enteredNum, _ ->
                setSubmitAndArrowButtonsEnabled(enteredNum != sellingPrice && enteredNum != null)
                val number = enteredNum ?: 0L
                setMinusButtonEnabled(number >= stepChange)
                val totalPrice =
                    number + productItem.discountedFee.orNil - productItem.discount.orNil
                onProfitChanged(totalPrice - productItem.price.orNil)
                if (source == SOURCE_ATUR) {
                    if (minAdminFee == maxAdminFee) {
                        setTotalAmount(number + minAdminFee)
                    } else {
                        setTotalAmountRange(number + minAdminFee, number + maxAdminFee)
                    }
                } else {
                    setTotalAmount(totalPrice)
                }
                toggleSubmitButtonVisibility(true)
                toggleSubmittedTextViewVisibility(false)
            }
            etSellingPrice.setAmountInEditText(sellingPrice)
            etSellingPrice.setSelection(etSellingPrice.length())
            tvTitle.text = productItem.productName
            if (source == SOURCE_ATUR) {
                grpPrepaidElements.hideView()
                grpRoundingElements.visibility = (pricingType != PricingType.PROFIT_MARGIN).asVisibility()
            } else {
                if (pricingType == PricingType.SELLING_AMOUNT) {
                    tvProfitMarginTitle.hideView()
                    grpPostpaidElements.hideView()
                    tvHargaModalValue.text = Utility.formatAmount(productItem.price)
                } else {
                    grpPrepaidElements.hideView()
                }
            }
            setStepChange(stepChange)

            if (source == SOURCE_ATUR) {
                tvOriginalAdminFee.hideView()
                toggleArrowButtonsVisibility(false)
                if (minAdminFee == maxAdminFee) {
                    tvOriginalAdminFee.hideView()
                    tvReducedAdminFee.text = Utility.formatNonAbsoluteAmount(maxAdminFee)
                } else {
                    tvReducedAdminFee.text = tvReducedAdminFee.context.getString(
                        R.string.two_strings_separated_by_hyphen,
                        Utility.formatNonAbsoluteAmount(minAdminFee),
                        Utility.formatNonAbsoluteAmount(maxAdminFee)
                    )
                }
            } else {
                val adminFee = productItem.adminFee.orNil
                val discountedFee = productItem.discountedFee.orNil
                when (adminFee) {
                    0.0 -> {
                        tvOriginalAdminFee.hideView()
                        tvReducedAdminFee.text =
                            tvReducedAdminFee.context.getString(R.string.free_upper_case)
                    }
                    discountedFee -> {
                        tvOriginalAdminFee.hideView()
                        tvReducedAdminFee.text = Utility.formatAmount(adminFee)
                    }
                    else -> {
                        tvOriginalAdminFee.text = Utility.formatAmount(adminFee)
                        tvReducedAdminFee.text = Utility.formatAmount(discountedFee)
                    }
                }
                val discount = productItem.discount
                if (discount == 0.0) {
                    tvDiscountValue.hideView()
                    tvDiscount.hideView()
                } else {
                    tvDiscountValue.showView()
                    tvDiscount.showView()
                    tvDiscountValue.text = Utility.formatAmount(discount)
                }
            }
        }
    }

    fun setStepChange(stepChangeVal: Double) = binding.apply {
        tvStepDecrease.text = String.format("-%s", Utility.formatAmount(stepChangeVal))
        tvStepIncrease.text = String.format("+%s", Utility.formatAmount(stepChangeVal))
        stepChange = stepChangeVal
    }

    fun setTotalAmount(totalPrice: Double) = binding.apply {
        if (totalPrice >= 0) {
            tvSellingPriceValue.setTextColor(tvSellingPriceValue.context.getColorCompat(R.color.green_80))
        } else {
            tvSellingPriceValue.setTextColor(tvSellingPriceValue.context.getColorCompat(R.color.red_80))
        }
        tvSellingPriceValue.text = Utility.formatNonAbsoluteAmount(totalPrice)
    }

    fun setTotalAmountRange(minPrice: Double, maxPrice: Double) = binding.apply {
        tvSellingPriceValue.text = tvSellingPriceValue.context.getString(
            R.string.two_strings_separated_by_hyphen,
            Utility.formatNonAbsoluteAmount(minPrice), Utility.formatNonAbsoluteAmount(maxPrice)
        )
        if (minPrice >= 0) {
            tvSellingPriceValue.setTextColor(tvSellingPriceValue.context.getColorCompat(R.color.green_80))
        } else {
            tvSellingPriceValue.setTextColor(tvSellingPriceValue.context.getColorCompat(R.color.red_80))
        }

    }

    fun getTotalAmount() = binding.etSellingPrice.getNumberValue()

    fun toggleArrowButtonsVisibility(flag: Boolean) {
        with(binding) {
            ivPrevious.visibility = flag.asVisibility()
            ivNext.visibility = flag.asVisibility()
        }
    }

    fun toggleSubmitButtonVisibility(flag: Boolean) {
        with(binding) {
            btnSubmit.visibility = flag.asVisibility()
        }
    }

    fun toggleSubmittedTextViewVisibility(flag: Boolean) {
        binding.tvSaved.visibility = flag.asVisibility()
    }

    fun setPreviousButtonEnabled(flag: Boolean) {
        with(binding) {
            ivPrevious.setImageResource(if (flag) R.drawable.ic_white_back else R.drawable.ic_gray_back)
            ivPrevious.isEnabled = flag
        }
    }

    fun setNextButtonEnabled(flag: Boolean) {
        with(binding) {
            ivNext.setImageResource(if (flag) R.drawable.ic_white_forward else R.drawable.ic_gray_forward)
            ivNext.isEnabled = flag
        }
    }

    fun setSubmitAndArrowButtonsEnabled(flag: Boolean) {
        with(binding) {
            btnSubmit.isEnabled = flag
            setPreviousButtonEnabled(flag.not() && shouldPreviousButtonBeEnabled)
            setNextButtonEnabled(flag.not() && shouldNextButtonBeEnabled)
        }
    }

    private fun setMinusButtonEnabled(flag: Boolean) {
        with(binding) {
            ivMinus.alpha = if (flag) 1f else 0.5f
            ivMinus.isEnabled = flag
        }
    }

    fun updateSellingPrice() {
        sellingPrice = binding.etSellingPrice.getNumberValue()
    }

    private fun resetButtonStates() {
        toggleSubmitButtonVisibility(true)
        toggleSubmittedTextViewVisibility(false)
        setMinusButtonEnabled(true)
    }

    private fun setClickListeners() = binding.apply {
        ivMinus.setSingleClickListener { decreaseStepChange() }
        ivPlus.setSingleClickListener { increaseStepChange() }
        ivTooltip.setSingleClickListener {
            showTooltip(ivTooltip, context.getString(R.string.admin_fee_info))
        }
    }

    private fun increaseStepChange() = binding.etSellingPrice.apply {
        setAmountInEditText(getNumberValue() + stepChange.toLong())
        setSelection(length())
    }

    private fun decreaseStepChange() = binding.etSellingPrice.apply {
        setAmountInEditText(getNumberValue() - stepChange.toLong())
        setSelection(length())
    }

    private fun onProfitChanged(profit: Double) {
        with(binding) {
            if (profit >= 0) {
                tvProfitText.text = tvProfitText.context.getString(R.string.profit_text)
                tvProfitValue.setTextColor(tvProfitValue.context.getColorCompat(R.color.green_80))
                etSellingPrice.setBackgroundResource(R.drawable.bg_edittext_default)
            } else {
                tvProfitText.text = tvProfitText.context.getString(R.string.loss_text)
                tvProfitValue.setTextColor(tvProfitValue.context.getColorCompat(R.color.red_80))
                etSellingPrice.setBackgroundResource(R.drawable.bg_solid_red5_corner_8dp_stroke_red60)
            }
            tvProfitValue.text = Utility.formatAmount(profit)
        }
    }

    private fun setAmountHint() {
        val wordToSpan: Spannable = SpannableString("Rp 0")
        wordToSpan.setSpan(
            ForegroundColorSpan(Color.BLACK),
            0, 1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.etSellingPrice.hint = wordToSpan
    }

    private fun showTooltip(anchor: ImageView, message: String) {
        try {
            //hide existing tooltip if any
            if (tooltip != null && tooltip?.isShowing.isTrue) tooltip?.dismiss()

            //no need to create tooltip again if click on anchor again
            if (tooltipTargetId == anchor.id) {
                tooltipTargetId = -1
                return
            }

            //recreate tooltip
            val tooltipBuilder = builder(context)
                .setAnchor(anchor)
                .setText(message)
                .setBackgroundColor(R.color.blue_80)

            tooltip = tooltipBuilder.build()
            tooltip?.show()
            //ideally we should have a wrapper for simpleTooltip with id, simpletooltip, visibility fields and a builder
            tooltipTargetId = anchor.id
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }
}
