package com.bukuwarung.payments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.bukuwarung.base_android.extensions.observeEvent
import com.bukuwarung.databinding.DialogAddContactBinding
import com.bukuwarung.dialogs.base.BaseDialogFragment
import com.bukuwarung.payments.utils.InjectorUtils
import com.bukuwarung.payments.viewmodels.AddContactViewModel
import com.bukuwarung.payments.viewmodels.AddContactViewModel.NavigationCommand

class AddContactDialog(
        private val bookId: String,
        private val paymentType: Int,
        private val entryPoint: String,
        private val finishAction: (String, String) -> Unit
): BaseDialogFragment() {
    private val viewModel: AddContactViewModel by viewModels {
        InjectorUtils.provideCustomerListViewModelFactory(requireContext(), entryPoint)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val binding = DialogAddContactBinding.inflate(inflater, container, false)

        binding.apply {
            vm = viewModel
            lifecycleOwner = viewLifecycleOwner
            dialog = this@AddContactDialog
        }

        registerObservers()

        return binding.root
    }

    private fun registerObservers() {
        viewModel.navigation.observeEvent(viewLifecycleOwner, Observer {
            if (it is NavigationCommand.CustomerDetails) {
                finishAction(it.customerId, it.phone)
            }
            dismiss()
        })
    }

    companion object {
        fun show(manager: FragmentManager, bookId: String, paymentType: Int, entryPoint: String, finishAction: (String, String) -> Unit) {
            AddContactDialog(bookId, paymentType, entryPoint, finishAction).show(manager, "add-contact-dialog")
        }
    }
}