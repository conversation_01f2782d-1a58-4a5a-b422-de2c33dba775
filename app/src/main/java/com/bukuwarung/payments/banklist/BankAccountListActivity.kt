package com.bukuwarung.payments.banklist

import android.app.Activity
import android.content.Intent
import androidx.core.content.ContextCompat
import androidx.lifecycle.observe
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.profile.update.BusinessProfileFormActivity
import com.bukuwarung.activities.referral.main_referral.dialogs.NullProfileReferralDialog
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.UrlType
import com.bukuwarung.databinding.ActivityBankAccountListBinding
import com.bukuwarung.dialogs.confirmation_dialog.StandardConfirmationDialog
import com.bukuwarung.payments.DeleteBankPromptDialog
import com.bukuwarung.payments.adapters.BankAccountListAdapter
import com.bukuwarung.payments.adapters.RefundBankAccountListAdapter
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.payments.pin.NewPaymentPinActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.toBoolean
import com.bukuwarung.utils.toInt
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

class BankAccountListActivity : BaseActivity() {
    private lateinit var binding: ActivityBankAccountListBinding
    private val scope = MainScope()
    private lateinit var refundAdapter: RefundBankAccountListAdapter

    companion object {
        private const val isRedirectFromPaymentsToProfile = "isRedirectFromPaymentsToProfile"
        private const val PAYMENT_TYPE = "paymentType"
        private const val ENTRY_POINT = "entryPoint"
        private const val BOOK_ID = "bookId"
        private const val CUSTOMER_ID = "customerId"
        private const val IS_SELF_ONLY = "isSelfOnly"
        private const val IS_SELECT_ONLY = "isSelectOnly"
        private const val SELECTED_ACCOUNT_ID = "selectedAccountId"
        private const val SELECTED_BANK = "selectedBank"
        private const val SETTING_FIRST_TIME = "setting_first_time"
        private const val SET_QRIS_BANK = "set_qris_bank"
        const val BANK_ACCOUNT = "bankAccount"
        private const val RC_PIN_SELECT = 98
        private const val RC_PIN_DELETE = 99
        private const val RC_PIN_ADD = 97
        private const val RC_START_BUSINESS_PROFILE = 96
        private const val RC_ADD_BANK = 95
        private const val RC_ADD_QRIS_BANK = 94

        fun createIntent(
            origin: Activity,
            paymentType: String,
            bookId: String?,
            entryPoint: String? = null,
            customerId: String? = null,
            selectedAccountId: String? = null,
            isSelfOnly: String? = "false",
            isSelectOnly: String? = "false",
            settingFirstTime: Boolean = false,
            previousSelectedBank: String? = null,
            setQrisBank: Boolean = true
        ): Intent {
            val i = Intent(origin, BankAccountListActivity::class.java)
            i.putExtra(PAYMENT_TYPE, paymentType)
            i.putExtra(ENTRY_POINT, entryPoint)
            i.putExtra(BOOK_ID, bookId)
            i.putExtra(CUSTOMER_ID, customerId)
            i.putExtra(IS_SELF_ONLY, isSelfOnly)
            i.putExtra(IS_SELECT_ONLY, isSelectOnly)
            i.putExtra(SELECTED_ACCOUNT_ID, selectedAccountId)
            i.putExtra(SELECTED_BANK, previousSelectedBank)
            i.putExtra(SETTING_FIRST_TIME, settingFirstTime)
            i.putExtra(SET_QRIS_BANK, setQrisBank)
            return i
        }
    }
    private lateinit var adapter: BankAccountListAdapter

    private val paymentType by lazy { intent?.getStringExtra(PAYMENT_TYPE)?.toInt(0) ?: 0 }
    private val entryPoint by lazy { intent?.getStringExtra(ENTRY_POINT) ?: "" }
    private val bookId by lazy { intent?.getStringExtra(BOOK_ID) }
    private val customerId by lazy { intent?.getStringExtra(CUSTOMER_ID) }
    private val selectedAccountId by lazy { intent?.getStringExtra(SELECTED_ACCOUNT_ID) }
    private val isSelfOnly by lazy { intent?.getStringExtra(IS_SELF_ONLY).toBoolean() }
    private val isSelectOnly by lazy { intent?.getStringExtra(IS_SELECT_ONLY).toBoolean() }
    private val settingFirstTime by lazy {
        intent?.getBooleanExtra(SETTING_FIRST_TIME, false) ?: false
    }
    private val previousSelectedBank by lazy {
        intent?.getStringExtra(SELECTED_BANK)
    }
    private val setQrisBank by lazy { intent?.getBooleanExtra(SET_QRIS_BANK, true) ?: true }

    @Inject
    lateinit var viewModel: BankAccountListViewModel
    override fun setViewBinding() {
        binding = ActivityBankAccountListBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)
        viewModel.init(paymentType, entryPoint, customerId, bookId, selectedAccountId)
        if (viewModel.isPaymentIn() || viewModel.isForQris()) {
            binding.buttonAddBankAccount.text = getString(R.string.label_add_account)
            binding.toolbar.title = getString(R.string.fragment_bank_account_list_title)
        } else {
            binding.buttonAddBankAccount.text = getString(R.string.label_add_account_customer)
            binding.toolbar.title = getString(R.string.fragment_bank_account_customer_list_title)
        }
        adapter = BankAccountListAdapter(isSelfOnly, isSelectOnly, arrayListOf(), {
            if (customerId.isNullOrBlank()) return@BankAccountListAdapter
            AppAnalytics.trackEvent(if (viewModel.isPaymentIn() || viewModel.isForQris()) AnalyticsConst.EVENT_PAYMENT_SWITCH_USER_BANK else AnalyticsConst.EVENT_PAYMENT_SWITCH_RECIPIENT_BANK,
                    PropBuilder()
                            .put(AnalyticsConst.ENTRY_POINT, entryPoint)
                            .put(if (viewModel.isPaymentIn() || viewModel.isForQris()) AnalyticsConst.USER_BANK else AnalyticsConst.RECIPIENT_BANK, it.bankCode))

            if (viewModel.isPaymentIn() || viewModel.isForQris()) {
                viewModel.setCurrentSelectedAccount(it)
                startActivityForResult(NewPaymentPinActivity.createIntent(this, PinType.PIN_CONFIRM.toString()), RC_PIN_SELECT)
            } else {
                setResultSuccess(it)
            }
        }, {
            DeleteBankPromptDialog.show(
                    onPromptClicked = { confirmed ->
                        run {
                            if (confirmed) {
                                if (viewModel.isPaymentIn() || viewModel.isForQris()) {
                                    viewModel.setTempDeletedAccount(it)
                                    startActivityForResult(NewPaymentPinActivity.createIntent(this, PinType.PIN_CONFIRM.toString()), RC_PIN_DELETE)
                                } else {
                                    viewModel.deleteBankAccount(it)
                                }
                            }
                        }
                    },
                    manager = supportFragmentManager
            )
        }, { urlType, bankAccount ->
                clickLink(
                    urlType,
                    bankAccount,
                    paymentType = PaymentConst.PAYMENT_IN
                )
        }, disableSelectedBank = viewModel.isForQris(), isQris = viewModel.isForQris())
        refundAdapter = RefundBankAccountListAdapter(false, true, null, {
            setResult(Activity.RESULT_OK, Intent().apply {
                putExtra("bank_name", it.bankCode)
                putExtra("bank_logo", it.getBankLogoIfAvailable())
                putExtra("account_number", it.accountNumber)
                putExtra("user_name", it.accountHolderName)
            })
            finish()
        }, {}, { urlType, bankAccount ->
            clickLink(
                urlType,
                bankAccount,
                paymentType = PaymentConst.REFUND
            )
        },false)
        binding.rvBankAccounts.layoutManager = LinearLayoutManager(this@BankAccountListActivity)
        if (entryPoint == AnalyticsConst.IN_APP_TICKET)
            binding.rvBankAccounts.adapter = refundAdapter
        else
            binding.rvBankAccounts.adapter = adapter
        binding.buttonAddBankAccount.setOnClickListener {
            if (entryPoint == AnalyticsConst.IN_APP_TICKET) {
                setResult(Activity.RESULT_OK, Intent().apply {
                    putExtra("add_bank", true)
                })
                finish()
            } else {
                if (viewModel.isPaymentIn() || viewModel.isForQris()) {
                    startActivityForResult(
                        NewPaymentPinActivity.createIntent(
                            this@BankAccountListActivity,
                            PinType.PIN_CONFIRM.toString()
                        ), RC_PIN_ADD
                    )
                } else {
                    goToAddBank()
                }
                AppAnalytics.trackEvent(
                    if (viewModel.isPaymentIn() || viewModel.isForQris()) AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK else AnalyticsConst.EVENT_PAYMENT_ADD_RECIPIENT_BANK,
                    PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
                )
            }
        }

        binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_arrow_back)
        binding.toolbar.setNavigationOnClickListener {
            InputUtils.hideKeyBoardWithCheck(this)
            onBackPressed()
        }
    }

    private fun goToAddBank(paymentIn: Boolean = false) {
        if (viewModel.isForQris() && RemoteConfigUtils.getPaymentConfigs().enableNameMatching.isTrue) {
            val extraQueries =
                "kycAccountId=${SessionManager.getInstance().kycAccountId}&changingQrisBank=${!settingFirstTime}"
            val qrisBankurl =
                "${RemoteConfigUtils.getPaymentConfigs().qrisBankUrl}?$extraQueries"
            val i = WebviewActivity.createIntent(this, qrisBankurl, "")
            startActivityForResult(i, RC_ADD_QRIS_BANK)
        } else {
            startActivityForResult(
                AddBankAccountActivity.createIntent(
                    this, paymentType.toString(), bookId, entryPoint, customerId,
                    (adapter.itemCount > 0).toString(), false.toString(), paymentIn,
                    settingFirstTime = settingFirstTime,
                    setQrisBank = false
                ), RC_ADD_BANK
            )
        }
    }

    private fun setResultSuccess(bank: BankAccount?) {
        val i = Intent()
        i.putExtra(BANK_ACCOUNT, bank)
        setResult(Activity.RESULT_OK, i)
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK && requestCode == RC_START_BUSINESS_PROFILE) finish()
        if (resultCode != Activity.RESULT_OK) return
        when (requestCode) {
            RC_PIN_ADD -> goToAddBank(true)
            RC_PIN_SELECT -> {
                if (viewModel.isPaymentIn()) {
                    viewModel.getCurrentSelectedAccount()
                } else if (viewModel.isForQris()) {
                    viewModel.setQrisBankAccount(settingFirstTime, previousSelectedBank, setQrisBank)
                }
            }
            RC_PIN_DELETE -> viewModel.deleteBankAccount()
            RC_START_BUSINESS_PROFILE -> viewModel.checkProfileCompletion(selectedAccountId)
            RC_ADD_BANK -> {
                if (viewModel.isForQris()) {
                    setResultSuccess(data?.getParcelableExtra(AddBankAccountActivity.BANK_ACCOUNT))
                } else {
                    viewModel.addNewBankAccount(data?.getParcelableExtra(AddBankAccountActivity.BANK_ACCOUNT))
                }
            }
            RC_ADD_QRIS_BANK -> {
                val bankType = data?.getStringExtra(PaymentConst.QRIS_BANK_SET_FOR)
                val intent = Intent().apply {
                    putExtra(PaymentConst.QRIS_BANK_SET_FOR, bankType)
                }
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }

    override fun subscribeState() {
        viewModel.profileIncompleteEvent.observe(this) {
            when (it) {
                is BankAccountListViewModel.ProfileIncompleteEvent.ShowProfileDialog -> showProfileDialog()
            }
        }
        viewModel.mediatorLiveData.observe(this) {
            adapter.setData(it)
        }
        viewModel.observeEvent.observe(this) {
            when (it) {
                is BankAccountListViewModel.Event.ApiError -> handleErrorApi(it.message)
                is BankAccountListViewModel.Event.HasOngoingTransaction -> {
                    val dialog = StandardConfirmationDialog(this, "Error", getString(R.string.delete_bank_error_exist))
                    dialog.show()
                }
                is BankAccountListViewModel.Event.ShowBankList -> adapter.setData(it.list)
                is BankAccountListViewModel.Event.ReturnSelectedAccount -> setResultSuccess(it.currentSelectedAccount)
                is BankAccountListViewModel.Event.OnBackPressed -> handleOnBackPressedFinished(it.hasDeletedAnAccount, it.currentSelectedAccount)
                is BankAccountListViewModel.Event.ShowRefundBankList -> refundAdapter.setData(it.list)
            }
        }
    }

    private fun handleErrorApi(message: String?) {
        // if has message, should we show it? currently will follow previous text
        val dialog = StandardConfirmationDialog(this, "Error",
                if (!message.isNullOrBlank()) message else getString(R.string.no_internet_error))
        dialog.show()
    }

    private fun handleOnBackPressedFinished(hasDeletedAnAccount: Boolean, currentSelectedAccount: BankAccount?) {
        if (!hasDeletedAnAccount) super.onBackPressed()
        else setResultSuccess(currentSelectedAccount)
    }

    private fun showProfileDialog() {
        val dialog = NullProfileReferralDialog(
                this,
                R.string.null_profile_payment_content,
                hideBtn = true
        ) {}
        dialog.show()
        scope.launch {
            delay(1000)
            if (isFinishing || isDestroyed) return@launch
            dialog.dismiss()
            val intent = BusinessProfileFormActivity.getIntent(this@BankAccountListActivity)
            intent.putExtra(isRedirectFromPaymentsToProfile, true)
            startActivityForResult(intent, RC_START_BUSINESS_PROFILE)
        }
    }

    override fun onBackPressed() {
        viewModel.onBackPressed()
    }

    private fun clickLink(urlType: UrlType, bankAccount: String? = null, paymentType: String) {
        startActivity(
            PaymentUtils.getBankAccountRedirectionIntent(
                context = this,
                urlType = urlType,
                bankAccount = bankAccount,
                paymentType = paymentType,
                entryPoint = entryPoint
            )
        )
    }
}
