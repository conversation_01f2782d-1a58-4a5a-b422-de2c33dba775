package com.bukuwarung.payments.ppob.base.view

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.databinding.FragmentRecentAndFavouritesBinding
import com.bukuwarung.di.ViewModelFactory
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.payments.ppob.base.adapter.RecentAndFavouriteAdapterNew
import com.bukuwarung.payments.ppob.base.viewmodel.RecentAndFavouriteViewModel
import com.bukuwarung.utils.InputUtils
import com.google.android.material.tabs.TabLayoutMediator
import javax.inject.Inject

class RecentAndFavouriteFragment: BaseFragment() {

    @Inject
    internal lateinit var viewModelFactory: ViewModelFactory<RecentAndFavouriteViewModel>
    private lateinit var viewModel: RecentAndFavouriteViewModel
    private var _binding: FragmentRecentAndFavouritesBinding? = null
    private val binding get() = _binding!!
    private var recentFavAdapter: RecentAndFavouriteAdapterNew? = null
    private val category by lazy { arguments?.getString(ARG_CATEGORY).orEmpty() }
    private var iCommunicator: IRecentAndFavCommunicator? = null

    companion object {
        const val TAG = "recent_and_fav_fragment"
        private const val ARG_CATEGORY = "arg_category"
        private const val ARG_BILLER_CODE = "arg_biller_code"
        private const val FAVOURITE_POSITION = 0
        private const val RECENT_POSITION = 1

        fun createIntent(category: String, billerCode: String? = null): RecentAndFavouriteFragment {
            val fragment = RecentAndFavouriteFragment()
            fragment.arguments = Bundle().apply {
                putString(ARG_CATEGORY, category)
                putString(ARG_BILLER_CODE, billerCode)
            }
            return fragment
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding =
            FragmentRecentAndFavouritesBinding.inflate(layoutInflater, viewGroup, false)
        viewModel =
            ViewModelProvider(
                parentFragment as Fragment,
                viewModelFactory
            ).get(RecentAndFavouriteViewModel::class.java)
        return binding.root
    }

    override fun setupView(view: View) {
        with(binding) {
            recentFavAdapter = RecentAndFavouriteAdapterNew(
                this@RecentAndFavouriteFragment,
                category = category,
                billerCode = arguments?.getString(ARG_BILLER_CODE).orEmpty()
            )
            vpPager.adapter = recentFavAdapter
            TabLayoutMediator(tbLayout, vpPager) { tab, position ->
                tab.text = getTabTitle(position)
            }.attach()
            vpPager.offscreenPageLimit = 2
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.setCategory(arguments?.getString(ARG_CATEGORY).orEmpty())
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        iCommunicator = (parentFragment ?: context) as? IRecentAndFavCommunicator
    }

    private fun getTabTitle(position: Int): String {
        return when (position) {
            FAVOURITE_POSITION -> getString(R.string.favorite_customer_contact)
            else -> getString(R.string.recent_transaction)
        }
    }

    override fun subscribeState() {
        viewModel.observeEventState.observe(this) {
            when(it) {
                is RecentAndFavouriteViewModel.Event.SetData -> {
                    iCommunicator?.setData(it.profilesItem)
                }
                is RecentAndFavouriteViewModel.Event.SwitchToRecent -> {
                    binding.vpPager.currentItem = RECENT_POSITION
                    if (it.hideKeyboard) {
                        InputUtils.hideKeyBoardWithCheck(requireActivity())
                    }
                }
                else -> {}
            }
        }
    }

    fun refreshFavAndRecentTab() {
        viewModel.getPaymentList(category)
        viewModel.invalidateDataSource()
    }

    interface IRecentAndFavCommunicator {
        fun setData(profilesItem: ProfilesItem)
    }


}