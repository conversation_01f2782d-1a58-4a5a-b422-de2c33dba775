package com.bukuwarung.payments.ppob.confirmation.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isTrue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class LoyaltyTierDiscountsBSViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase
): BaseViewModel() {

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus

    sealed class Event {
        data class ShowLoyaltyDiscountsInfo(val loyaltyDiscountsInfo: String?) : Event()
        data class ShowErrorResponse(val isServerError: Boolean): Event()
    }

    fun getPaymentLoyaltyDiscounts(paymentType: String) = viewModelScope.launch(Dispatchers.IO) {
        when(val result = paymentUseCase.getLoyaltyTierDiscounts()){
            is ApiSuccessResponse -> {
                var fetchedInfo = false
                result.body.forEach {
                    if (it.transactionType == paymentType && it.loyaltyDiscountEnabled.isTrue && it.loyaltyDiscountsInfo.isNotNullOrBlank()) {
                        fetchedInfo = true
                        setEventStatus(Event.ShowLoyaltyDiscountsInfo(it.loyaltyDiscountsInfo))
                    }
                }
                if (!fetchedInfo) setEventStatus(Event.ShowErrorResponse(true))
            }
            is ApiErrorResponse -> {
                if(result.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE)
                    setEventStatus(Event.ShowErrorResponse(true))
                else
                    setEventStatus(Event.ShowErrorResponse(false))
            }
            else -> {}
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }
}