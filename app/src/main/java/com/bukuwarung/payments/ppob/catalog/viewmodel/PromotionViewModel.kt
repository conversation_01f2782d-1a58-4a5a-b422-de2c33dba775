package com.bukuwarung.payments.ppob.catalog.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.ppob.FilterGroupsItem
import com.bukuwarung.payments.data.model.ppob.PromoBannerDataResponse
import com.bukuwarung.payments.data.model.ppob.PromotionBannerFilterResponse
import com.bukuwarung.utils.Utilities.convertMapToQueryParam
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class PromotionViewModel @Inject constructor(
        private val finproUseCase: FinproUseCase) : BaseViewModel() {
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    var filterItem: FilterGroupsItem? = null
    var bannerUrl: String? = null
    var bannerData: PromoBannerDataResponse? = null
    var businessName: String = ""

    sealed class Event {
        data class ShowServerError(val message: String?, val filterItem: FilterGroupsItem?) : Event()
        data class ShowInternetError(val filterItem: FilterGroupsItem?) : Event()
        data class ShowFilters(val promotionBannerResponse: PromotionBannerFilterResponse) : Event()
        data class ShowBanner(val bannerData: PromoBannerDataResponse?, val bannerUrl: String?, val item: FilterGroupsItem?) : Event()
        object ShowLoader : Event()
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun getBannersFilter() = viewModelScope.launch {
        setEventStatus(Event.ShowLoader)
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getPromotionBannerFilter()) {
                is ApiSuccessResponse -> {
                    updateBanners(response.body.filterGroups?.getOrNull(0))
                    response.body.filterGroups?.getOrNull(0)?.isSelected = true
                    setEventStatus(Event.ShowFilters(response.body))
                }
                is ApiErrorResponse -> {
                    if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(Event.ShowServerError(response.errorMessage, null))
                    } else {
                        setEventStatus(Event.ShowInternetError(null))
                    }
                }
                else -> {}
            }
        }
    }

    fun getBannersData(sectionId: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getPromotionBannerData(sectionId)) {
                is ApiSuccessResponse -> {
                    bannerData = response.body
                    setEventStatus(Event.ShowBanner(bannerData, bannerUrl, filterItem))
                }
                is ApiErrorResponse -> {
                    if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(Event.ShowServerError(response.errorMessage, filterItem))
                    } else {
                        setEventStatus(Event.ShowInternetError(filterItem))
                    }
                }
                else -> {}
            }
        }
    }

    fun updateBanners(item: FilterGroupsItem?) {
        viewModelScope.launch {
            setEventStatus(Event.ShowLoader)
            filterItem = item
            val filters = ArrayList<String>()
            filterItem?.data?.toggleFilters?.forEach {
                if (it.isSelected) {
                    it.selectorKey?.let { key -> filters.add(key) }
                }
            }
            bannerUrl = filterItem?.data?.bannerUrl + convertMapToQueryParam(
                    hashMapOf<String?, List<String>>( "store_name" to listOf<String>(businessName), "filters" to filters)
            )
            item?.id?.let { getBannersData(it) }
        }
    }

    fun showBanner() {
        viewModelScope.launch {
            setEventStatus(Event.ShowBanner(bannerData, bannerUrl, filterItem))
        }
    }
}