package com.bukuwarung.payments.ppob.catalog.view

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.print.PdfPrint
import android.print.PrintAttributes
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentPromotionBannerBinding
import com.bukuwarung.payments.data.model.ppob.ToggleFiltersItem
import com.bukuwarung.payments.ppob.catalog.adapter.ToggleSelectorAdapter
import com.bukuwarung.payments.ppob.catalog.viewmodel.PromotionViewModel
import com.bukuwarung.utils.*
import com.google.gson.Gson
import java.io.ByteArrayInputStream

class PromotionBannerFragment : BaseFragment(), PdfPrint.PrintCallback {

    private var mIPromoBanner: IPromoBanner? = null
    private var fragmentPromotionBannerBinding: FragmentPromotionBannerBinding? = null
    private val binding get() = fragmentPromotionBannerBinding!!
    private val viewModel: PromotionViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        fragmentPromotionBannerBinding =
            FragmentPromotionBannerBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mIPromoBanner = context as? IPromoBanner
    }

    override fun setupView(view: View) {

    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this, {
            with(binding) {
                when (it) {
                    is PromotionViewModel.Event.ShowBanner -> {
                        includePreview.root.showView()
                        context?.let { context ->
                            if (it.item?.data?.flags?.get(PromotionActivity.IS_BANNER_EMPTY).isTrue) {
                                includePreview.tvPreviewCatalog.setTextColor(context.getColorCompat(R.color.red_60))
                                includePreview.tvSubtitle.setTextColor(context.getColorCompat(R.color.red_60))
                                includePreview.tvSubtitle.text =
                                    getString(R.string.complete_setting_selling_price)
                                includePreview.btnChange.setStyleButtonFillRed(context)
                                includePreview.btnChange.text = getString(R.string.manage_price_label)
                            } else {
                                includePreview.tvPreviewCatalog.setTextColor(context.getColorCompat(R.color.black_000000))
                                includePreview.tvSubtitle.setTextColor(context.getColorCompat(R.color.black_40))
                                includePreview.btnChange.text = getString(R.string.ubah_harga)
                                includePreview.btnChange.setStyleButtonOutlineBlue(context)
                                includePreview.tvSubtitle.text =
                                    getString(R.string.selling_price_filled)
                            }
                            includePreview.btnChange.setSingleClickListener {
                                AppAnalytics.trackEvent(AnalyticsConst.SET_PPOB_SELLING_PRICE, AppAnalytics.PropBuilder().put(AnalyticsConst.TYPE, it.item?.id).put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CATALOG_PAGE), true, false, false)
                                startActivity(
                                    CatalogActivity.createIntent(context).apply {
                                        flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
                                    })
                            }
                            includePreview.rvSelector.layoutManager =
                                LinearLayoutManager(context)
                            includePreview.rvSelector.adapter =
                                ToggleSelectorAdapter(it.item?.data?.toggleFilters, ::clickAction)
                        }
                        wvCatalog.showView()
                        wvCatalog.apply {
                            isVerticalScrollBarEnabled = true
                            settings.cacheMode = WebSettings.LOAD_NO_CACHE
                            settings.javaScriptEnabled = true
                            scrollBarStyle = View.SCROLLBARS_OUTSIDE_OVERLAY
                            webViewClient = object : WebViewClient() {
                                override fun shouldInterceptRequest(
                                    view: WebView,
                                    request: WebResourceRequest
                                ): WebResourceResponse? {
                                    return if (request.url.lastPathSegment == "banners-data") {
                                        WebResourceResponse(
                                            "application/json",
                                            "utf-8",
                                            ByteArrayInputStream(
                                                Gson().toJson(it.bannerData).toByteArray()
                                            )
                                        )
                                    } else {
                                        super.shouldInterceptRequest(view, request)
                                    }
                                }

                                override fun onPageStarted(
                                    view: WebView?,
                                    url: String?,
                                    favicon: Bitmap?
                                ) {
                                    super.onPageStarted(view, url, favicon)
                                    mIPromoBanner?.setProgress(true)
                                    mIPromoBanner?.renderBottomView(false)
                                }

                                override fun onPageFinished(view: WebView?, url: String?) {
                                    super.onPageFinished(view, url)
                                    mIPromoBanner?.setProgress(false)
                                    mIPromoBanner?.renderBottomView(true)
                                }
                            }
                            settings.loadWithOverviewMode = true
                            settings.useWideViewPort = true
                            it.bannerUrl?.let { bannerURL ->
                                loadUrl(bannerURL)
                            }
                        }
                    }
                    else -> {}
                }
            }
        })
    }

    private fun clickAction(item: ToggleFiltersItem) {
        val filters = ArrayList<String>()
        viewModel.filterItem?.data?.toggleFilters?.forEach {
            if (it.isSelected) {
                it.selectorKey?.let { key -> filters.add(key) }
            }
        }
        viewModel.bannerUrl = viewModel.filterItem?.data?.bannerUrl + Utilities.convertMapToQueryParam(
            hashMapOf<String?, List<String>>("store_name" to listOf<String>(viewModel.businessName), "filters" to filters)
        )
        viewModel.showBanner()
    }

    fun createWebPrintJob() {
        val jobName = getString(R.string.app_name) + " Document"
        val attributes = PrintAttributes.Builder()
            .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
            .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600))
            .setMinMargins(PrintAttributes.Margins.NO_MARGINS).build()
        val pdfPrint = PdfPrint(attributes)
        context?.cacheDir?.let {
            pdfPrint.print(
                binding.wvCatalog.createPrintDocumentAdapter(jobName),
                it,
                "Catalog-" + System.currentTimeMillis() + ".pdf",
                this,
                lifecycleScope
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentPromotionBannerBinding = null
        mIPromoBanner = null
    }

    override fun onCompleted(uri: Uri) {
        mIPromoBanner?.downloadCompleted(uri)
    }

    override fun onFailed() {
        mIPromoBanner?.downloadFailed()
    }

    interface IPromoBanner {
        fun showSnackBar()
        fun renderBottomView(show: Boolean)
        fun downloadCompleted(uri: Uri)
        fun downloadFailed()
        fun setProgress(show: Boolean)
    }

}