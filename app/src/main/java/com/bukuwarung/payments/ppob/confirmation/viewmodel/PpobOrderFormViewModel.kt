package com.bukuwarung.payments.ppob.confirmation.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.DISABLE
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.los.LosUseCase
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isFalseOrNull
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject

class PpobOrderFormViewModel@Inject constructor(
    private val losUseCase: LosUseCase,
    private val finproUseCase: FinproUseCase,
    private val paymentUseCase: PaymentUseCase,
    private val sessionManager: SessionManager
) : BaseViewModel()  {
    companion object {
        const val HEALTH_OK = 0
        const val HEALTH_WARNING = 1
        const val HEALTH_ERROR = 2
        const val SALES = "SALES"
        const val sales = "sales"
        const val purchase = "purchase"

    }

    sealed class Event {
        data class UpdateSaldoRewardView(val isChecked: Boolean, val saldoBonus: SaldoBonus?): Event()
        data class ShowPaymentMethod(val paymentMethod: FinproPaymentMethod?, val enoughSaldo: Boolean, val saldoBonus: SaldoBonus? = null, val isDisabled: Boolean = false, val isSaldoFreezed: Boolean) : Event()
        data class ShowPpobPaymentMethodsBs(val finproGetPaymentMethodsResponse: FinproGetPaymentMethodsV2Response?) : Event()
        data class ProfitChange(val amount: Double, val hasSellingPrice: Boolean) : Event()
        data class OnCheckoutSuccess(val url: String?, val prop: AppAnalytics.PropBuilder, val paymentMethodCode: String? = null) : Event()
        data class ShowServerError(val message: String?) : Event()
        object ShowInternetError : Event()
        object ShowPinForSaldo : Event()
        data class UpdateScreenWithFavouritesInfo(val isRemove: Boolean): Event()
        data class ShowBnplBottomsheet(val ppobBnplUserData: PpobBnplUserData? = null) : Event()
        data class SetBnplInfo(val showBnplInfo: Boolean, val transactionFeePercent: Double, val bnplLimit: Double) : Event()
    }

    data class ViewState(
            val showLoading: Boolean = false,
            val healthState: Int = HEALTH_OK,
            val enoughSaldo: Boolean = true,
            val showToastMessage: String? = null,
            val isDisabled: Boolean = false,
            val isSaldoFreezed: Boolean = false
    )

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private var paymentChannels = emptyList<FinproPaymentMethod>()
    private var paymentMethodsV2Response: FinproGetPaymentMethodsV2Response? = null
    private var sellingPrice = 0.0
    private lateinit var orderDetail: FinproOrderResponse
    private var adminFee = 0.0
    private var method: FinproGetPaymentMethodsResponse? = null
    private var channelCode = ""
    var category = ""
    private var code = ""
    private var purchaseType = "SALES"
    var isBnplAvailable = false
    private var bnplFee = 0.0
    private var ppobBnplUserDetails: PpobBnplUserDetails? = null
    private var useSaldoReward = false
    private var saldoBonus: SaldoBonus? = null
    private var selectedPaymentMethod: FinproPaymentMethod? = null

    fun init(orderDetail: FinproOrderResponse) {
        category = orderDetail.items?.firstOrNull()?.beneficiary?.category.orEmpty()
        code = orderDetail.items?.firstOrNull()?.beneficiary?.code.orEmpty()
        this.orderDetail = orderDetail
        adminFee = orderDetail.items?.firstOrNull()?.fee.orNil
        getPaymentMethods()
    }

    fun updateOrderDetail(orderDetail: FinproOrderResponse) {
        this.orderDetail = orderDetail
    }

    fun setCashbackPaymentMethod(paymentMethod: String?, paymentMethodFamily: String?) {
        val paymentMethodSelected = paymentChannels.filter { it.code == paymentMethod}.getOrNull(0)
        changePaymentMethod(paymentMethodSelected)
    }

    fun onSellingPriceChanged(amount: Long) {
        sellingPrice = amount.toDouble()
        var costOfProduct = orderDetail.amount.orNil + bnplFee
        if (useSaldoReward) costOfProduct -= saldoBonus?.available.orNil
        eventStatus.value = Event.ProfitChange(sellingPrice - costOfProduct, sellingPrice != 0.0)
    }

    fun setBnplFee(fee: Double){
        bnplFee = fee
    }

    fun getSelectedPaymentMethod() = selectedPaymentMethod

    fun changePaymentMethod(paymentMethod: FinproPaymentMethod?) = viewModelScope.launch {
        //update is_default in the paymentMethodsV2response
        selectedPaymentMethod = paymentMethod
        paymentMethodsV2Response?.highlightedPaymentChannels?.channels?.forEach {
            it.details?.isSelected = it.code?.equals(paymentMethod?.code).isTrue
        }
        paymentMethodsV2Response?.otherPaymentChannels?.methods?.forEach { finproPaymentMethod ->
            finproPaymentMethod.channels.forEach {
                it.details?.isSelected = it.code?.equals(paymentMethod?.code).isTrue
            }
        }
        for(channel in paymentChannels){
            if(paymentMethod?.code.equals(channel.code)){
                method = FinproGetPaymentMethodsResponse(code = channel.family.orEmpty())
                break
            }
        }
        channelCode = paymentMethod?.code.orEmpty()
        val enoughSaldo = checkEnoughSaldoBalance(paymentMethod)
        val isSaldoFreezed = paymentMethod?.details?.isSaldoFreezed.isTrue
        setViewState(currentViewState().copy(enoughSaldo = enoughSaldo, isSaldoFreezed = isSaldoFreezed, isDisabled = isPaymentMethodDisabled(paymentMethod)))
        eventStatus.value = Event.ShowPaymentMethod(paymentMethod, enoughSaldo, saldoBonus, isPaymentMethodDisabled(paymentMethod), isSaldoFreezed)
        eventStatus.value = Event.SetBnplInfo(
            paymentMethod?.code.equals(PaymentConst.BNPL),
            ppobBnplUserDetails?.data?.transactionFee.orNil,
            ppobBnplUserDetails?.data?.currentLimit.orNil
        )
    }

    private fun isPaymentMethodDisabled(paymentMethod: FinproPaymentMethod?) =
        paymentMethod?.tag?.tagType?.equals(DISABLE, true).isTrue ||
                paymentMethod?.tag?.tagType?.equals(PaymentConst.DAILY_LIMIT_REACHED, true).isTrue ||
                paymentMethod?.tag?.tagType?.equals(PaymentConst.MONTHLY_LIMIT_REACHED, true).isTrue ||
                paymentMethod?.code?.isBlank().isTrue ||
                paymentMethod?.family?.isBlank().isTrue

    private fun checkEnoughSaldoBalance(paymentMethod: FinproPaymentMethod?): Boolean {
        if (paymentMethod?.details?.saldoBalance != null) {
            val balance =
                if (useSaldoReward) paymentMethod.details.saldoBalance.toDouble() + saldoBonus?.available.orNil else paymentMethod.details.saldoBalance.toDouble()
            return balance >= adminFee + orderDetail.amount.orNil
        }
        return true // default value is true here because above if condition is used to check if the payment method is saldo and if other payment method is select then we should still let user continue to do the payment.
    }

    fun setUseSaldoReward(flag: Boolean) {
        useSaldoReward = flag
    }

    fun getUseSaldoReward() = useSaldoReward

    fun getAvailableSaldoBonus() = saldoBonus?.available.orNil
    fun getTotalSaldoBonus() = saldoBonus?.total.orNil

    fun updateSaldoRewardView(isChecked: Boolean) = viewModelScope.launch {
        setEventStatus(Event.UpdateSaldoRewardView(isChecked, saldoBonus))
    }

    private fun getPaymentMethods() = viewModelScope.launch {
        setViewState(currentViewState().copy(showLoading = true))
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getPaymentMethodsV2(orderDetail.amount.orNil, category, getCashbackProductCode())) {
                is ApiSuccessResponse -> {
                    paymentMethodsV2Response = response.body
                    paymentChannels = response.body.highlightedPaymentChannels?.channels.orEmpty()
                    response.body.otherPaymentChannels?.methods?.forEach {
                        paymentChannels = paymentChannels + it.channels
                    }
                    for (channel in paymentChannels) {
                        if(channel.details?.isSelected.isTrue){
                            selectedPaymentMethod = channel
                            break
                        }
                    }
                    getSaldoBonusAndFetchBnplInfo(selectedPaymentMethod?.code.equals(PaymentConst.BNPL))
                    if (selectedPaymentMethod == null && paymentChannels.isNotEmpty()) {
                        selectedPaymentMethod = paymentChannels.first()
                    }
                    method = FinproGetPaymentMethodsResponse(code = selectedPaymentMethod?.family.orEmpty())
                    channelCode = selectedPaymentMethod?.code.orEmpty()
                    val enoughSaldo = checkEnoughSaldoBalance(selectedPaymentMethod)
                    val isSaldoFreezed = selectedPaymentMethod?.details?.isSaldoFreezed.isTrue
                    setViewState(
                        currentViewState().copy(
                            showLoading = false,
                            enoughSaldo = enoughSaldo,
                            isSaldoFreezed = isSaldoFreezed,
                            isDisabled = isPaymentMethodDisabled(selectedPaymentMethod)
                        )
                    )
                    setEventStatus(
                        Event.ShowPaymentMethod(
                            selectedPaymentMethod,
                            enoughSaldo,
                            saldoBonus,
                            isPaymentMethodDisabled(selectedPaymentMethod),
                            isSaldoFreezed
                        )
                    )
                    doHealthCheck()
                }
                is ApiErrorResponse -> {
                    if (response.errorMessage.isNotBlank() && response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        setViewState(currentViewState().copy(showLoading = false))
                        setEventStatus(Event.ShowServerError(response.errorMessage))
                    } else {
                        setViewState(currentViewState().copy(showLoading = false))
                        setEventStatus(Event.ShowInternetError)
                    }
                }
                else -> {}
            }
        }
    }

    private fun getSaldoBonusAndFetchBnplInfo(showBnplInfo: Boolean){
        for(channel in paymentChannels){
            if (channel.code.equals((PaymentConst.SALDO))) {
                saldoBonus = channel.details?.saldoBonus
            } else if (channel.code.equals(PaymentConst.BNPL) && channel.tag?.tagType.isNullOrEmpty()) {
                isBnplAvailable = true
                fetchBnplDetails(showBnplInfo)
            }
        }
    }

    private fun getCashbackProductCode() =
        orderDetail.campaigns?.find {
            it.type == PpobConst.CASHBACK_TYPE && it.paymentMethodChannel == PpobConst.SALDO_METHOD
        }?.productCode ?: orderDetail.campaigns?.find {
            it.type == PpobConst.CASHBACK_TYPE && it.paymentMethodChannel == PpobConst.ALL_METHOD
        }?.productCode.orEmpty()

    private suspend fun doHealthCheck() {
        val request = PaymentHealthCheckRequest()
        when (val response = paymentUseCase.doHealthCheck(request)) {
            is ApiSuccessResponse -> {
                setViewState(currentViewState().copy(healthState = HEALTH_OK))
                iterateProviderHealthResponse(response.body.providers)
                if (isHealthNotError()) {
                    iterateMoneyOutHealthResponse(response.body.moneyOut)
                    if (isHealthNotError()) {
                        iterateMoneyInHealthResponse(response.body.moneyIn?.paymentOut)
                    }
                }
            }
            else -> {}
        }
        //TODO: currently need to call 2 endpoint. later should be only 1 when backend combine this
        if (isHealthNotError()) {
            when (val response = finproUseCase.doHealthCheck(request)) {
                is ApiSuccessResponse -> {
                    iterateProviderHealthResponse(response.body.providers)
                    if (isHealthNotError()) {
                        iterateMoneyOutHealthResponse(response.body.moneyOut)
                        if (isHealthNotError()) {
                            iterateMoneyInHealthResponse(response.body.moneyIn?.paymentOut)
                        }
                    }
                }
                else -> {}
            }
        }
        if (isHealthNotError()) {
            val calendar = Calendar.getInstance()
            val hourNow = calendar.get(Calendar.HOUR_OF_DAY)
            if (hourNow < 7 || hourNow >= 23)
                setViewState(currentViewState().copy(healthState = HEALTH_WARNING))
        }
    }

    private fun isHealthNotError() = currentViewState().healthState != HEALTH_ERROR

    private suspend fun iterateProviderHealthResponse(list: List<HealthStatus>?) {
        setViewState(currentViewState().copy(healthState = HEALTH_OK))
        if (list.isNullOrEmpty()) return
        var currentMessage = ""
        for (status in list) {
            if (true == status.enabled) return
            else currentMessage = status.message.orEmpty()
        }
        setViewState(currentViewState().copy(healthState = HEALTH_ERROR))
    }

    private suspend fun iterateMoneyOutHealthResponse(list: List<HealthStatus>?) {
        if (list.isNullOrEmpty()) return
        for (status in list) {
            if (false == status.enabled) {
                setViewState(currentViewState().copy(healthState = HEALTH_ERROR))
            }
        }
    }

    private suspend fun iterateMoneyInHealthResponse(list: List<HealthStatus>?) {
        if (list.isNullOrEmpty()) return
        for (status in list) {
            if (channelCode == status.name && false == status.enabled) {
                setViewState(currentViewState().copy(healthState = HEALTH_ERROR))
            }
        }
    }

    fun reloadData() {
        getPaymentMethods()
    }

    fun submit(isRecent: Boolean, isReminder: Boolean) {
        if (method?.code.isNullOrBlank() && channelCode.isBlank()) return
        if (!Utility.hasInternet()) {
            eventStatus.value = Event.ShowInternetError
            return
        }
        when(channelCode){
            PaymentConst.SALDO -> {
                eventStatus.value = Event.ShowPinForSaldo
                return
            }
            PaymentConst.BNPL -> {
                eventStatus.value = Event.ShowBnplBottomsheet(ppobBnplUserDetails?.data)
                return
            }
            else -> {
                checkout(isRecent, isReminder)
            }
        }
    }

    private fun fetchBnplDetails(showBnplInfo: Boolean) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when(val response = losUseCase.getPpobBnplUserDetails()) {
                is ApiSuccessResponse -> {
                    ppobBnplUserDetails = response.body
                    SessionManager.getInstance().setBnplInfo(ppobBnplUserDetails?.data?.isRegisteredUser.isFalseOrNull, ppobBnplUserDetails?.data?.currentLimit.orNil)
                    SessionManager.getInstance().hasRefreshedBnplData(true)
                    setEventStatus(Event.SetBnplInfo(showBnplInfo, ppobBnplUserDetails?.data?.transactionFee.orNil, ppobBnplUserDetails?.data?.currentLimit.orNil))
                }
                else -> { }
            }
        }
    }

    fun checkout(isRecent: Boolean, isReminder: Boolean, checkoutToken: String = "") = viewModelScope.launch {
        setViewState(currentViewState().copy(showLoading = true))
        withContext(Dispatchers.IO) {
            val request = FinproCheckoutOrderRequest(
                listOf(FinproCheckoutPayment(method?.code.orEmpty(), channelCode)),
                listOf(
                    FinproCheckoutItem(
                        orderDetail.items?.firstOrNull()?.sku.orEmpty(),
                        sellingPrice
                    )
                ),
                purchaseType,
                useSaldoReward
            )
            when (val response = finproUseCase.checkoutOrder(sessionManager.businessId, orderDetail.orderId.orEmpty(), request, checkoutToken)) {
                is ApiSuccessResponse -> {
                    if (!response.body.payments.isNullOrEmpty()) {
                        val paymentMethodCode = response.body.payments.firstOrNull()?.paymentMethod?.code
                        val prop = AppAnalytics.PropBuilder()
                        prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.PPOB_FORM)
                        prop.put(AnalyticsConst.ORDER_ID, response.body.orderId)
                        prop.put(
                                AnalyticsConst.PROVIDER, if (category == PpobConst.CATEGORY_PDAM || category == PpobConst.CATEGORY_MULTIFINANCE || category == PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE) {
                            response.body.items?.firstOrNull()?.name
                        } else {
                            response.body.items?.firstOrNull()?.beneficiary?.code
                        }
                        )
                        prop.put(AnalyticsConst.PACKAGE_PRICE, (response.body.amount.orNil))
                        prop.put(AnalyticsConst.BUYING_PRICE_SNAKE_CASE, (response.body.amount.orNil) + (response.body.fee.orNil))
                        prop.put(AnalyticsConst.SELLING_PRICE, sellingPrice)
                        prop.put(AnalyticsConst.PAYMENT_METHOD, paymentMethodCode.orEmpty().lowercase())
                        prop.put(AnalyticsConst.PAYMENT_STATUS, AnalyticsConst.INCOMPLETE)
                        prop.put(AnalyticsConst.PPOB_STATUS, AnalyticsConst.INCOMPLETE)
                        prop.put(AnalyticsConst.FAVOURITE_STATUS, orderDetail.customerProfile?.isFavorite)
                        prop.put(AnalyticsConst.FAVOURITE_CONTACT_NAME, orderDetail.customerProfile?.favouriteDetails?.alias)
                        // for saldo, url is null
                        setEventStatus(Event.OnCheckoutSuccess(response.body.payments.firstOrNull()?.paymentUrl, prop, paymentMethodCode))
                    }
                    setViewState(currentViewState().copy(showLoading = false))
                }
                is ApiErrorResponse -> {
                    if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        setViewState(currentViewState().copy(showLoading = false))
                        setEventStatus(Event.ShowServerError(response.errorMessage))
                    } else {
                        setViewState(currentViewState().copy(showLoading = false))
                        setEventStatus(Event.ShowInternetError)
                    }
                }
                else -> {}
            }
        }
    }

    fun removeFavourite(customerProfile: CustomerProfile?) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response =
                    finproUseCase.deleteFavourite(sessionManager.businessId, customerProfile?.favouriteDetails?.id.orEmpty())) {
                is ApiSuccessResponse -> {
                    setViewState(currentViewState().copy(showToastMessage = response.body.message?:"Berhasil menghapus pelanggan favorit"))
                    setEventStatus(Event.UpdateScreenWithFavouritesInfo(true))
                }
                is ApiErrorResponse -> {
                    setViewState(currentViewState().copy(showToastMessage = response.errorMessage))
                }
                else -> {}
            }
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    private suspend fun setViewState(state: ViewState) = withContext(Dispatchers.Main) {
        viewState.value = state
    }

    fun getAnalyticsType(): String {
        return PpobConst.CATEGORY_ANALYTICS_MAP[if (category == PpobConst.CATEGORY_LISTRIK && code == PpobConst.CATEGORY_PLN_POSTPAID) {
            PpobConst.CATEGORY_PLN_POSTPAID
        } else {
            category
        }].orEmpty()
    }

    fun isVoucherGame() = category == PpobConst.CATEGORY_VOUCHER_GAME

    fun isTrainCategory() = category == PpobConst.CATEGORY_TRAIN_TICKET

    fun getCategoryName() = category

    fun setPurchaseType(purchaseType: String) {
        this.purchaseType = purchaseType
    }

    fun getPurchaseTypeAnalytics(): String {
        return if(this.purchaseType == SALES) sales else purchase
    }

    fun getPaymentChannelsList(): List<FinproPaymentMethod> {
        return paymentChannels
    }

    fun showPpobBankAccountsBs(){
        eventStatus.value = Event.ShowPpobPaymentMethodsBs(paymentMethodsV2Response)
    }
}