package com.bukuwarung.payments.ppob.base.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.profile.update.BusinessProfileFormActivity
import com.bukuwarung.activities.referral.main_referral.dialogs.NullProfileReferralDialog
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.commonview.view.BukuTileViewBottomSheet
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst.*
import com.bukuwarung.databinding.ActivityPpobBinding
import com.bukuwarung.di.ViewModelFactory
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.PpobListItem
import com.bukuwarung.payments.data.model.State
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.adapter.PpobAdapter
import com.bukuwarung.payments.ppob.base.adapter.PpobCategoryAdapter
import com.bukuwarung.payments.ppob.base.listeners.PpobProductsListener
import com.bukuwarung.payments.ppob.base.model.PpobToolbarData
import com.bukuwarung.payments.ppob.base.viewmodel.PpobViewModel
import com.bukuwarung.payments.ppob.reminders.view.ReminderActivity
import com.bukuwarung.payments.ppob.train.view.TrainTicketWebviewActivity
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import kotlinx.android.synthetic.main.layout_activity_title.view.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

class PpobActivity : BaseActivity() {

    private lateinit var binding: ActivityPpobBinding
    private val category: String by lazy { intent.getStringExtra(PPOB_CATEGORY) ?: "" }
    private val accountNumber: String by lazy { intent.getStringExtra(ACCOUNT_NUMBER) ?: "" }
    private val phoneNumber: String by lazy { intent.getStringExtra(PHONE_NUMBER) ?: "" }
    private val code: String by lazy { intent.getStringExtra(CODE) ?: "" }
    private val layoutType: String by lazy { intent.getStringExtra(LAYOUT_TYPE).orEmpty() }
    private val machineNumber: String by lazy { intent.getStringExtra(MACHINE_NUMBER).orEmpty() }
    private val frameNumber: String by lazy { intent.getStringExtra(FRAME_NUMBER).orEmpty() }
    private val from: String by lazy { intent.getStringExtra(FROM) ?: "" }
    private lateinit var viewModel: PpobViewModel
    private var adapter: PpobAdapter? = null

    @Inject
    internal lateinit var ppobViewModelFactory: ViewModelFactory<PpobViewModel>
    private var versionFilteredList = emptyList<PpobListItem>()

    companion object {
        private const val isRedirectFromPaymentsToProfile = "isRedirectFromPaymentsToProfile"
        private const val PPOB_CATEGORY = "PPOB_CATEGORY"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        private const val CODE = "CODE"
        private const val FROM = "FROM"
        private const val LAYOUT_TYPE = "layout_type"
        private const val MACHINE_NUMBER = "machine_number"
        private const val FRAME_NUMBER = "frame_number"

        fun createIntent(
            context: Context,
            category: String,
            from: String = "",
            accountNumber: String = "",
            phoneNumber: String = "",
            code: String = "",
            layoutType: String = "",
            machineNumber: String = "",
            frameNumber: String = ""
        ): Intent {
            return Intent(context, PpobActivity::class.java).apply {
                putExtra(FROM, from)
                putExtra(PPOB_CATEGORY, category)
                putExtra(ACCOUNT_NUMBER, accountNumber)
                putExtra(PHONE_NUMBER, phoneNumber)
                putExtra(CODE, code)
                putExtra(LAYOUT_TYPE, layoutType)
                putExtra(MACHINE_NUMBER, machineNumber)
                putExtra(FRAME_NUMBER, frameNumber)
            }
        }
    }

    override fun setViewBinding() {
        binding = ActivityPpobBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        with(binding) {
            viewModel =
                ViewModelProvider(this@PpobActivity, ppobViewModelFactory).get(PpobViewModel::class.java)
            rvPpob.layoutManager = LinearLayoutManager(this@PpobActivity, RecyclerView.HORIZONTAL, false)
            versionFilteredList = viewModel.getVersionList().orEmpty()
            val defaultSelectedItemIndex =
                versionFilteredList.indexOfFirst { it.category == category }.orNil
            if (versionFilteredList.isNotEmpty())
                versionFilteredList[defaultSelectedItemIndex].isSelected = true
            val ppobFragmentList = viewModel.setFragmentList(
                versionFilteredList,
                from,
                accountNumber,
                phoneNumber,
                code,
                layoutType,
                machineNumber,
                frameNumber,
                category
            )
            adapter = PpobAdapter(
                versionFilteredList,
                ::onCategorySelected,
                defaultSelectedItemIndex
            )
            rvPpob.adapter = adapter
            vpPpob.adapter = PpobCategoryAdapter(ppobFragmentList, supportFragmentManager)
            vpPpob.disableSwipe()
            vpPpob.offscreenPageLimit = 1
            vpPpob.currentItem = defaultSelectedItemIndex
            rvPpob.scrollToPosition(defaultSelectedItemIndex)
            if (versionFilteredList.isNotEmpty())
                versionFilteredList[defaultSelectedItemIndex].category?.let { setCategoryData(it) }
            includeSemua.root.setSingleClickListener {
                val bukuTileBottomSheet = BukuTileViewBottomSheet.createInstance(
                    "ppob", isPaymentsScreen = true, showToolsSection = false
                )
                bukuTileBottomSheet.setPpobProductsListener(object : PpobProductsListener {
                    override fun onPpobSelected(fragmentBodyBlock: BodyBlock?) {
                        if (fragmentBodyBlock == null) return
                        if (fragmentBodyBlock.coming_soon) {
                            PpobUtils.showPpobComingSoonDialog(this@PpobActivity)
                        } else if (fragmentBodyBlock.is_available.not()) {
                            PpobUtils.showPpobUnAvailable(
                                this@PpobActivity,
                                fragmentBodyBlock.ppobCategoryName.orEmpty()
                            )
                        } else {
                            if (viewModel.isVoucherGame(fragmentBodyBlock.ppobCategoryName.orEmpty())) {
                                startActivity(
                                    WebviewActivity.createIntent(
                                        this@PpobActivity,
                                        BuildConfig.VOUCHER_GAME_URL + SessionManager.getInstance().businessId,
                                        getString(R.string.voucher_game1)
                                    )
                                )
                            } else if (viewModel.isTrainTicket(fragmentBodyBlock.ppobCategoryName.orEmpty())) {
                                startActivity(
                                    TrainTicketWebviewActivity.createIntent(this@PpobActivity)
                                )
                            } else {
                                versionFilteredList.forEach { it.isSelected = false }
                                val pos = versionFilteredList.indexOfFirst { it.category == fragmentBodyBlock.ppobCategoryName }
                                versionFilteredList[pos].isSelected = true
                                adapter?.notifyDataSetChanged()
                                binding.rvPpob.scrollToPosition(pos)
                                setCategoryData(fragmentBodyBlock.ppobCategoryName.orEmpty())
                                binding.vpPpob.currentItem = pos
                            }
                        }
                    }

                })
                bukuTileBottomSheet.show(supportFragmentManager, BukuTileViewBottomSheet.TAG)
            }
        }
    }

    override fun subscribeState() {
    }

    private fun setCategoryData(category: String) {
        setToolBarView(
            PpobToolbarData(
                title = getString(
                    PpobConst.CATEGORY_NAME_MAP[category]
                        ?: R.string.ppob
                ),
                helpUrl = PpobConst.CATEGORY_HELP_URL[category].orEmpty(),
                showReminderIcon = PpobConst.CATEGORY_SHOW_REMINDER_ICON[category].isTrue
            )
        )
    }

    fun setToolBarView(ppobToolbarData: PpobToolbarData) {
        with(binding.includeToolBar) {
            tbPpob.toolBarLabel.text = ppobToolbarData.title
            tbPpob.navigationIcon =
                ContextCompat.getDrawable(this@PpobActivity, R.drawable.ic_arrow_back)
            tbPpob.setNavigationOnClickListener {
                InputUtils.hideKeyBoardWithCheck(this@PpobActivity)
                onBackPressed()
            }
            ivHelp.setOnClickListener {
                if (AppConfigManager.getInstance().useWebView()) {
                    val intent = Intent(this@PpobActivity, HelpCenterActivity::class.java)
                    intent.putExtra(URL, ppobToolbarData.helpUrl)
                    intent.putExtra(TITLE, getString(R.string.help))
                    startActivity(intent)
                } else {
                    startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(ppobToolbarData.helpUrl)))
                }
            }
            if (ppobToolbarData.showReminderIcon && PaymentPrefManager.getInstance()
                    .shouldShowRemindersOptions()
            ) {
                tvReminder.showView()
                ivReminder.showView()
                ivReminder.setOnClickListener {
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_PPOB_REMINDERS,
                        AppAnalytics.PropBuilder()
                            .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PPOB_BUY_PAGE).put(
                                AnalyticsConst.TYPE,
                                AnalyticsConst.LISTRIK_POSTPAID
                            )
                    )
                    startActivity(ReminderActivity.createIntent(this@PpobActivity))
                }
            } else {
                tvReminder.hideView()
                ivReminder.hideView()
            }
        }
    }

    fun showDialog() {
        val dialog = NullProfileReferralDialog(
            this,
            R.string.null_profile_payment_content,
            hideBtn = true
        ) {}
        dialog.show()
        lifecycleScope.launch {
            delay(1000)
            if (isFinishing || isDestroyed) return@launch
            dialog.dismiss()
            val intent = BusinessProfileFormActivity.getIntent(this@PpobActivity)
            intent.putExtra(isRedirectFromPaymentsToProfile, true)
            startBusinessProfileFormForResult.launch(intent)
        }
    }

    fun onCategorySelected(ppobListItem: PpobListItem, position: Int) {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PPOB_BUY_BUTTON_CLICKED, AppAnalytics.PropBuilder().put("ppob_type", PpobConst.CATEGORY_ANALYTICS_MAP[ppobListItem.category.orEmpty()]).put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PPOB_BUY_PAGE), true, true, false
        )
        SurvicateAnalytics.invokeEventTracker(PpobConst.CATEGORY_BUY_BUTTON_ANALYTICS_MAP[category].orEmpty(), this)
        when (ppobListItem.state) {
            State.AVAILABLE -> {
                binding.rvPpob.scrollToPosition(position)
                setCategoryData(ppobListItem.category.orEmpty())
                binding.vpPpob.currentItem = position
                when {
                    viewModel.isVoucherGame(ppobListItem.category.orEmpty()) -> {
                        startActivity(
                            WebviewActivity.createIntent(
                                this@PpobActivity,
                                BuildConfig.VOUCHER_GAME_URL + SessionManager.getInstance().businessId,
                                getString(R.string.voucher_game1)
                            )
                        )
                    }
                    viewModel.isTrainTicket(ppobListItem.category.orEmpty()) -> {
                        startActivity(
                            TrainTicketWebviewActivity.createIntent(this)
                        )
                    }
                    else -> {  }
                }
            }
            State.NOT_AVAILABLE -> {
                PpobUtils.showPpobUnAvailable(this@PpobActivity, ppobListItem.category.orEmpty())
            }
            State.COMING_SOON -> {
                PpobUtils.showPpobComingSoonDialog(this@PpobActivity)
            }
            else -> {}
        }
    }

    private val startBusinessProfileFormForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode != Activity.RESULT_OK) {
                finish()
            }
        }

    override fun onDestroy() {
        binding.rvPpob.adapter = null
        super.onDestroy()
    }
}