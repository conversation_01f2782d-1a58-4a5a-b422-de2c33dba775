package com.bukuwarung.payments.ppob.base.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.ActivityEwalletBillersBinding
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.Biller
import com.bukuwarung.payments.ppob.base.adapter.EwalletBillersAdapter
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.viewmodel.EwalletBillersViewModel
import com.bukuwarung.payments.widget.ErrorBottomSheet
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.setSingleClickListener
import javax.inject.Inject

class EwalletBillersActivity : BaseActivity(), ErrorBottomSheet.Callback {

    @Inject
    lateinit var viewModel: EwalletBillersViewModel
    private lateinit var binding: ActivityEwalletBillersBinding
    private val selectedBiller by lazy { intent?.getParcelableExtra(SELECTED_BILLER) as? Biller }

    companion object {
        const val SELECTED_BILLER = "selected_biller"
        fun createIntent(context: Context?, selectedBiller: Biller?): Intent {
            val i = Intent(context, EwalletBillersActivity::class.java)
            i.putExtra(SELECTED_BILLER, selectedBiller)
            return i
        }
    }

    override fun setViewBinding() {
        binding = ActivityEwalletBillersBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setToolBar()
        viewModel.getBillerList()
    }

    private fun setToolBar() = with(binding.includeToolBar) {
        tvTitle.text = getString(R.string.choose_ewallet)
        btnBack.setSingleClickListener {
            onBackPressed()
        }
        tvHelp.setSingleClickListener {
            if (AppConfigManager.getInstance().useWebView()) {
                val intent = Intent(this@EwalletBillersActivity, HelpCenterActivity::class.java)
                intent.putExtra(
                    AppConst.URL,
                    PpobConst.CATEGORY_HELP_URL[PpobConst.CATEGORY_EWALLET].orEmpty()
                )
                intent.putExtra(AppConst.TITLE, getString(R.string.help))
                startActivity(intent)
            } else {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse(PpobConst.CATEGORY_HELP_URL[PpobConst.CATEGORY_EWALLET].orEmpty())
                    )
                )
            }
        }
    }

    override fun subscribeState() {
        viewModel.eventStatus.observe(this) {
            when (it) {
                is PpobEvent.ShowBillerList -> {
                    binding.pbProgress.hideView()
                    binding.rvBillers.adapter =
                        EwalletBillersAdapter(
                            it.list,
                            selectedBiller
                        ) {
                            val intent = Intent().apply {
                                putExtra(SELECTED_BILLER, it)
                            }
                            setResult(Activity.RESULT_OK, intent)
                            finish()
                        }
                }
                is PpobEvent.ServerError -> {
                    showPaymentDownBottomSheet(true, it.message)
                }
                is PpobEvent.InternetError -> {
                    showPaymentDownBottomSheet(false, it.message)
                }
                else -> {}
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        ErrorBottomSheet.createInstance(
            if (isServiceDown) {
                ErrorBottomSheet.Companion.ApiErrorType.API_ERROR
            } else {
                ErrorBottomSheet.Companion.ApiErrorType.CONNECTION_ERROR
            },
            message?.ifEmpty { getString(R.string.try_again_or_wait) }
        ).show(supportFragmentManager, ErrorBottomSheet.TAG)
    }

    override fun onDismiss() {
        finish()
    }

    override fun onButtonClicked() {
        finish()
    }
}