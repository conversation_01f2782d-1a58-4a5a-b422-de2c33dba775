package com.bukuwarung.payments.ppob.catalog.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.ppob.CatalogListResponse
import com.bukuwarung.payments.data.model.ppob.CategoryResponse
import com.bukuwarung.payments.data.model.ppob.PricingType
import com.bukuwarung.payments.data.model.ppob.SellingPriceRequest
import com.bukuwarung.payments.data.model.ppob.ProductsItem
import com.bukuwarung.payments.data.model.ppob.ValuesItem
import com.bukuwarung.utils.orNil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class PpobCatalogViewModel @Inject constructor(
        private val finproUseCase: FinproUseCase) : BaseViewModel() {

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    var categoryItem: ValuesItem? = null
    private lateinit var productItem: ProductsItem
    lateinit var pricingType: PricingType
    var stepChange: Double = 500.0 // Default value if not available from API
    private var minAdminFee: Double = 0.0
    private var maxAdminFee: Double = 0.0
    private var productsList = ArrayList<ProductsItem>()
    var currentExpandedItemPosition: Int = 0

    sealed class Event {
        data class ShowServerError(val message: String?, val category: String?) : Event()
        data class ShowInternetError(val category: String?) : Event()
        data class ShowBillerList(val catalogListResponse: CatalogListResponse, val position: Int) : Event()
        object ShowLoader : Event()
        data class ShowFilter(val categoryList: CategoryResponse) : Event()
        data class SellingPriceUpdated(val category: String?, val screenType:String) : Event()
        object DismissBottomSheet: Event()
        object ShowLoadingOnBottomSheet: Event()
        data class ShowServerErrorBottomSheet(val message: String?) : Event()
        object ShowInternetErrorBottomSheet : Event()
        data class CurrentProductItem(val productItem: ProductsItem, val pricingType: PricingType, val stepChange: Double, val category: String, val minAdminFee: Double, val maxAdminFee: Double): Event()
    }

    sealed class BannerType {
        object BannerPromotion: BannerType()
        object BannerAtur: BannerType()
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun getAllCategories() = viewModelScope.launch {
        setEventStatus(Event.ShowLoader)
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getCategory()) {
                is ApiSuccessResponse -> {
                    categoryItem = response.body.categoryGroups?.getOrNull(0)?.values?.getOrNull(0)
                    response.body.categoryGroups?.getOrNull(0)?.values?.getOrNull(0)?.isSelected = true
                    getBillersList(categoryItem?.categoryCode.orEmpty())
                    setEventStatus(Event.ShowFilter(response.body))
                }
                is ApiErrorResponse -> {
                    if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(Event.ShowServerError(response.errorMessage, null))
                    } else {
                        setEventStatus(Event.ShowInternetError(null))
                    }
                }
                else -> {}
            }
        }
    }

    fun getBillersList(category: String, position: Int = 0) = viewModelScope.launch {
        setEventStatus(Event.ShowLoader)
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getBillersList(category)) {
                is ApiSuccessResponse -> {
                    if (<EMAIL>?.categoryCode == category) {
                        productsList.clear()
                        response.body.billers?.forEach { billerItem ->
                            billerItem.products?.forEach { productItem ->
                                productsList.add(productItem)
                            }
                        }
                        minAdminFee = response.body.minAdminFee.orNil
                        maxAdminFee = response.body.maxAdminFee.orNil
                        response.body.pricingType?.let { type -> pricingType = type }
                        response.body.stepChange?.let { change -> stepChange = change }
                        setEventStatus(Event.ShowBillerList(response.body, position))
                    }
                }
                is ApiErrorResponse -> {
                    if (<EMAIL>?.categoryCode == category) {
                        if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                            setEventStatus(Event.ShowServerError(response.errorMessage, category))
                        } else {
                            setEventStatus(Event.ShowInternetError(category))
                        }
                    }
                }
                else -> {}
            }
        }
    }

    fun setCurrentProduct(productItem: ProductsItem) {
        <EMAIL> = productItem
    }

    fun getCurrentProduct() = viewModelScope.launch {
        setEventStatus(Event.CurrentProductItem(productItem, pricingType, stepChange, categoryItem?.categoryCode.orEmpty(), minAdminFee, maxAdminFee))
    }


    fun checkIfFirstProduct(): Boolean {
        return productsList.indexOf(productItem) == 0
    }

    fun checkIfLastProduct(): Boolean {
        return productsList.indexOf(productItem) == productsList.size - 1
    }

    fun getNextProduct() = viewModelScope.launch {
        try {
            productItem = productsList[productsList.indexOf(productItem)+1]
            setEventStatus(Event.CurrentProductItem(productItem, pricingType, stepChange, categoryItem?.categoryCode.orEmpty(), minAdminFee, maxAdminFee))
        } catch (exception: IndexOutOfBoundsException) {
            setEventStatus(Event.DismissBottomSheet)
        }

    }

    fun getPreviousProduct() = viewModelScope.launch {
        try {
            productItem = productsList[productsList.indexOf(productItem)-1]
            setEventStatus(Event.CurrentProductItem(productItem, pricingType, stepChange, categoryItem?.categoryCode.orEmpty(), minAdminFee, maxAdminFee))
        } catch (exception: IndexOutOfBoundsException) {
            setEventStatus(Event.DismissBottomSheet)
        }
    }

    fun setSellingPrice(pricing: Double, pricingType: PricingType, rounding: Boolean, category: String, code: String?, screenType: String) = viewModelScope.launch {
        setEventStatus(Event.ShowLoadingOnBottomSheet)
        withContext(Dispatchers.IO) {
            val request = SellingPriceRequest(pricing, pricingType, rounding)
            when (val response = finproUseCase.setSellingPrice(request, category, code)) {
                is ApiEmptyResponse -> {
                    setEventStatus(Event.SellingPriceUpdated(category, screenType))
                }
                is ApiErrorResponse -> {
                    setEventStatus(Event.DismissBottomSheet)
                    if (response.errorMessage == AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(Event.ShowServerErrorBottomSheet(response.errorMessage))
                    } else {
                        setEventStatus(Event.ShowInternetErrorBottomSheet)
                    }
                }
                else -> {}
            }
        }
    }
}