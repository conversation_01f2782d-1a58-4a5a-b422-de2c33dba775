package com.bukuwarung.payments.ppob.base.view

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentRecentNewBinding
import com.bukuwarung.payments.PaymentContactActivity
import com.bukuwarung.payments.PaymentsActivity
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.adapter.RecentItemAdapterNew
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.viewmodel.RecentAndFavouriteViewModel
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class NewRecentFragment : BaseFragment(), OnboardingWidget.OnboardingWidgetListener {

    private val viewModel: RecentAndFavouriteViewModel by viewModels(ownerProducer = { this.parentFragment?.parentFragment as Fragment })
    private var fragmentRecentBinding: FragmentRecentNewBinding? = null
    private val binding get() = fragmentRecentBinding!!
    private val category by lazy { arguments?.getString(CATEGORY).orEmpty() }
    private var profilesItem: ProfilesItem? = null
    private val recentAdapter: RecentItemAdapterNew by lazy {
        RecentItemAdapterNew(clickAction = {
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CLICK_REORDER,
                AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PPOB_BUY_PAGE),
                true, false, false
            )
            viewModel.setData(it)
        }, seeAllClickAction = {
            startHistoryActivity()
        }, clickFavourite = {
            loadUserContactActivity(it)
        }, clickDeleteFavourite = {
            this.profilesItem = it
            PpobUtils.showRemoveFavouriteDialog(requireContext()) {
                it.id?.let { id -> viewModel.removeFavourite(id, requireContext()) }
            }
        }, showOnBoardingView = {
            showOnBoarding(it)
        })
    }

    companion object {
        private const val KEY_CUSTOMER_ID = "customerId"
        private const val BILLER = "billerCode"
        private const val MESSAGE = "message"
        private const val KEY_BOOK_ID = "bookId"
        private const val KEY_PAYMENT_TYPE = "paymentType"
        private const val CATEGORY = "category"
        private const val BILLER_CODE = "biller_code"
        fun createIntent(category: String = "", billerCode: String = ""): NewRecentFragment {
            val bundle = Bundle().apply {
                putString(CATEGORY, category)
                putString(BILLER_CODE, billerCode)
            }
            return NewRecentFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        fragmentRecentBinding = FragmentRecentNewBinding.inflate(layoutInflater, container, false)
        viewModel.getPaymentList(category)
        return binding.root
    }

    override fun setupView(view: View) {
        with(binding) {
            bukuErrorView.addCallback(errorViewCallBack)
            rvRecent.layoutManager = LinearLayoutManager(context)
            rvRecent.adapter = recentAdapter
        }
    }

    override fun subscribeState() {
        viewModel.eventStatus.observe(this) {
            when (it) {
                is PpobEvent.RefreshFavourite -> {
                    Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_LONG
                    ).show()
                    if(it.refreshFavourite)
                        refreshRecentsTab()
                }
                else -> {}
            }
        }

        viewModel.observeEventState.observe(viewLifecycleOwner) {
            when (it) {
                is RecentAndFavouriteViewModel.Event.ShowPaymentList -> {
                    if (category == it.category) {
                        binding.rvRecent.showView()
                        binding.bukuErrorView.hideView()
                        binding.includeEmpty.root.hideView()
                        recentAdapter.setData(it.list, it.showOnBoarding)
                    }
                }
                is RecentAndFavouriteViewModel.Event.ShowRecentError -> {
                    with(binding) {
                        bukuErrorView.showView()
                        rvRecent.hideView()
                        includeEmpty.root.hideView()
                        if (it.showServerError) {
                            bukuErrorView.setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.sorry_disturbance),
                                getString(R.string.try_later),
                                getString(R.string.reload), R.drawable.ic_server_busy
                            )
                        } else {
                            bukuErrorView.setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.no_connection_title),
                                getString(R.string.no_connection_message),
                                getString(R.string.reload), R.drawable.ic_no_inet
                            )
                        }
                    }
                }
                is RecentAndFavouriteViewModel.Event.ShowRecentEmpty -> {
                    with(binding) {
                        rvRecent.hideView()
                        bukuErrorView.hideView()
                        includeEmpty.root.showView()
                    }
                }
                else -> {}
            }
        }

    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            viewModel.getPaymentList(category)
        }

        override fun messageClicked() {}

    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentRecentBinding = null
    }

    private fun refreshRecentsTab() {
        viewModel.getPaymentList(category)
        viewModel.invalidateDataSource()
    }

    private fun startHistoryActivity(paymentType: Int = 5) {
        val bundle = Bundle()
        bundle.putString(KEY_BOOK_ID, SessionManager.getInstance().businessId)
        bundle.putInt(KEY_PAYMENT_TYPE, paymentType)
        bundle.putString(KEY_CUSTOMER_ID, "")
        bundle.putString(BILLER, arguments?.getString(BILLER_CODE))
        bundle.putString(CATEGORY, category)
        startActivity(
            context?.let {
                PaymentsActivity.createIntent(
                    it,
                    bundle,
                    AnalyticsConst.PEMBAYARAN
                )
            }
        )
    }

    private fun loadUserContactActivity(orderId: String) {
        startPaymentContactActivityForResult.launch(
            PaymentContactActivity.createIntent(
                requireContext(),
                orderId,
                AnalyticsConst.PPOB_BUY_PAGE
            )
        )
    }

    private val startPaymentContactActivityForResult = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            Toast.makeText(
                context,
                result.data?.getStringExtra(MESSAGE),
                Toast.LENGTH_LONG
            ).show()
            viewModel.getPaymentList(category)
            viewModel.invalidateDataSource()
        }
    }

    private fun showOnBoarding(anchorView: View) {
        lifecycleScope.launch {
            delay(250)

            binding.rvRecent.post {
                OnboardingWidget.createInstance(
                    requireActivity(),
                    this@NewRecentFragment,
                    OnboardingPrefManager.TUTORIAL_FAVOURITE_CTA,
                    anchorView,
                    null,
                    getString(R.string.fav_on_boarding_title),
                    getString(R.string.fav_on_boarding),
                    "",
                    FocusGravity.CENTER,
                    ShapeType.RECTANGLE_FULL,
                    1,
                    1
                )
            }
        }
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        //no implementation required
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        //no implementation required
    }
}