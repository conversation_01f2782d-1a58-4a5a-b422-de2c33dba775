package com.bukuwarung.payments.ppob.base.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentFavouriteBinding
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.adapter.FavouriteItemAdapterNew
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.viewmodel.RecentAndFavouriteViewModel
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import kotlinx.coroutines.launch

class NewFavouriteFragment : BaseFragment() {


    private val viewModel: RecentAndFavouriteViewModel by viewModels(ownerProducer = { this.parentFragment?.parentFragment as Fragment })
    private var fragmentFavouriteBinding: FragmentFavouriteBinding? = null
    private val binding get() = fragmentFavouriteBinding!!
    private var profilesItem: ProfilesItem? = null
    private val favouriteProductAdapter by lazy {
        FavouriteItemAdapterNew( removeFavourite = { profilesItem ->
            this.profilesItem = profilesItem
            PpobUtils.showRemoveFavouriteDialog(requireContext()) {
                profilesItem?.id?.let { viewModel.removeFavourite(it, requireContext()) }
            }
        }, clickAction =  { profilesItem ->
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CLICK_REORDER,
                AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PPOB_BUY_PAGE),
                false, false, false
            )
            viewModel.setData(profilesItem)

        })
    }

    companion object {
        private const val CATEGORY = "category"
        fun createIntent(category: String = ""): NewFavouriteFragment {
            val bundle = Bundle().apply {
                putString(CATEGORY, category)
            }
            return NewFavouriteFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        fragmentFavouriteBinding =
            FragmentFavouriteBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun setupView(view: View) {
        binding.bukuErrorView.addCallback(errorViewCallBack)
        binding.rvFavourite.apply {
            layoutManager = LinearLayoutManager(context)
            itemAnimator = DefaultItemAnimator()
            adapter = favouriteProductAdapter
        }
    }

    private fun refreshFavourite() {
        viewModel.invalidateDataSource()
    }

    override fun subscribeState() {

        viewModel.eventStatus.observe(this) {
            when (it) {
                is PpobEvent.RefreshFavourite -> {
                    Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_LONG
                    ).show()
                    if (it.refreshFavourite) {
                        refreshFavourite()
                    }
                }
                else -> {}
            }
        }

        viewModel.productData.observe(this) {
            lifecycleScope.launch {
                favouriteProductAdapter.submitData(it)
            }
        }

        viewModel.pagingStatus.observe(this) { status ->
            when (status) {
                PagingStatus.Loading -> {
                    with(binding) {
                        tvTitle.hideView()
                        bukuErrorView.hideView()
                        includeLoading.root.showView()
                        includeEmpty.root.hideView()
                        rvFavourite.hideView()
                        includeLoading.root.startShimmer()
                        includeLoading.root.showShimmer(true)
                    }
                }
                is PagingStatus.LoadingNextPage -> {
                    binding.pbLoading.showView()
                }
                is PagingStatus.Loaded, PagingStatus.EmptyNextPage -> {
                    binding.apply {
                        tvTitle.showView()
                        bukuErrorView.hideView()
                        rvFavourite.showView()
                        pbLoading.hideView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        includeEmpty.root.hideView()
                    }
                }
                PagingStatus.Empty -> {
                    binding.apply {
                        tvTitle.hideView()
                        bukuErrorView.hideView()
                        includeEmpty.root.showView()
                        rvFavourite.hideView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        includeEmpty.btFavourite.setSingleClickListener {
                            viewModel.showRecent(showOnBoarding = true, hideKeyboard = false)
                        }
                    }
                }
                is PagingStatus.Error -> {
                    with(binding) {
                        tvTitle.hideView()
                        bukuErrorView.showView()
                        rvFavourite.hideView()
                        includeEmpty.root.hideView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.sorry_disturbance),
                            getString(R.string.try_later),
                            getString(R.string.reload), R.drawable.ic_server_busy
                        )
                    }
                }
                PagingStatus.NoInternet -> {
                    with(binding) {
                        tvTitle.hideView()
                        bukuErrorView.showView()
                        includeEmpty.root.hideView()
                        rvFavourite.hideView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.no_connection_title),
                            getString(R.string.no_connection_message),
                            getString(R.string.reload), R.drawable.ic_no_inet
                        )
                    }
                }
                else -> {}
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            refreshFavourite()
        }

        override fun messageClicked() {}

    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentFavouriteBinding = null
    }
}