package com.bukuwarung.payments.ppob.base.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class EwalletBillersViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase
) : BaseViewModel() {

    val eventStatus = MutableLiveData<PpobEvent>()

    private suspend fun setEventStatus(event: PpobEvent) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun getBillerList() = viewModelScope.launch(Dispatchers.IO) {
        when (val response = finproUseCase.getBillers(PpobConst.CATEGORY_EWALLET)) {
            is ApiSuccessResponse -> {
                setEventStatus(PpobEvent.ShowBillerList(response.body))
            }
            is ApiErrorResponse -> {
                if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE)
                    setEventStatus(PpobEvent.ServerError(response.errorMessage))
                else {
                    setEventStatus(PpobEvent.InternetError(response.errorMessage))
                }
            }
            else -> {}
        }
    }
}