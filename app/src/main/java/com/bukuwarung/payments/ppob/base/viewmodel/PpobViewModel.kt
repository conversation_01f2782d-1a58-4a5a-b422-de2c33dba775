package com.bukuwarung.payments.ppob.base.viewmodel

import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.CustomerListActivity
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.data.model.ppob.TrainEnrollmentRequest
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.model.PpobViewState
import com.bukuwarung.payments.ppob.base.view.BpjsFragment
import com.bukuwarung.payments.ppob.base.view.EWalletFragment
import com.bukuwarung.payments.ppob.base.view.PrepaidListrikNewFragment
import com.bukuwarung.payments.ppob.base.view.PrepaidPulsaFragment
import com.bukuwarung.payments.ppob.base.view.InternetAndTvCableFragment
import com.bukuwarung.payments.ppob.base.view.MultifinanceFragment
import com.bukuwarung.payments.ppob.base.view.PostpaidListrikNewFragment
import com.bukuwarung.payments.ppob.base.view.PacketDataFragment
import com.bukuwarung.payments.ppob.base.view.PdamFragment
import com.bukuwarung.payments.ppob.base.view.PostpaidPulsaFragment
import com.bukuwarung.payments.ppob.base.view.VehicleTaxFragment
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.toBoolean
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

open class PpobViewModel @Inject constructor(
        private val finproUseCase: FinproUseCase,
        private val sessionManager: SessionManager,
        private val businessUseCase: BusinessUseCase
) : BaseViewModel() {

    val eventStatus = MutableLiveData<PpobEvent>()
    val observeEvent: LiveData<PpobEvent> = eventStatus
    protected fun currentViewState(): PpobViewState? = viewState.value
    val viewState: MutableLiveData<PpobViewState> = MutableLiveData(PpobViewState())

    protected suspend fun setEventStatus(event: PpobEvent) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    protected suspend fun setViewState(ppobViewState: PpobViewState?) = withContext(Dispatchers.Main) {
        viewState.value = ppobViewState
    }

    fun checkProfileCompletion() = viewModelScope.launch {
        // no need of showProductsList now as we will first check for profile than only hid product API
        withContext(Dispatchers.IO) {
            val bookEntity = businessUseCase.getBusinessById(sessionManager.businessId)
            bookEntity?.run {
                if (!this.hasCompletedProfileWithoutOwnerName()) {
                    setEventStatus(PpobEvent.ShowProfileDialog)
                }
            }
        }
    }


    // To get PPOB products V2 API
    fun getPpobProductsWithBillerDetails(category: String, map: Map<String, String>, isSpecialProduct: Boolean? = null) = viewModelScope.launch {
        setViewState(currentViewState()?.copy(showShimmer = true))
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getPpobProductsWithBillerDetails(category, map, isSpecialProduct)) {
                is ApiSuccessResponse -> {
                    response.body.productsList?.let {
                        val list = it.toMutableList()
                        val popularIndex = it.indexOfFirst { ppobProduct ->
                            ppobProduct.productInfo?.isPopular?.toBoolean() == true
                        }
                        if (popularIndex > 0) {
                            val product = response.body.productsList[popularIndex]
                            list.removeAt(popularIndex)
                            list.add(0, product)
                        }
                        setViewState(currentViewState()?.copy(showShimmer = false))
                        setEventStatus(PpobEvent.ShowProductsList(list, billerDetails = response.body.billerDetails))
                    }
                }
                is ApiErrorResponse -> {
                    setViewState(currentViewState()?.copy(showShimmer = false))
                    if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(PpobEvent.ServerError(response.errorMessage))
                        setEventStatus(PpobEvent.SearchServerError(response.errorMessage))
                    } else {
                        setEventStatus(PpobEvent.InternetError(response.errorMessage))
                        setEventStatus(PpobEvent.SearchInternetError(response.errorMessage))
                    }
                }
                else -> {}
            }
        }
    }

    fun addToCart(request: FinproAddCartRequest) = viewModelScope.launch {
        setViewState(currentViewState()?.copy(showLoading = true))
        when (val response =
                finproUseCase.addItemToCart(sessionManager.businessId, request)) {
            is ApiSuccessResponse -> {
                setViewState(currentViewState()?.copy(showLoading = false))
                setEventStatus(PpobEvent.ToDetail(response.body))
            }
            is ApiErrorResponse -> {
                setViewState(currentViewState()?.copy(showLoading = false))
                if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                    /* statusCode:
                    461 -> invalid number, 462 -> bill already paid, 467 -> failure in the provider system, 468 -> Inquiry Failed
                    */
                    if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE)
                        if (response.statusCode in 400..499) {
                            setEventStatus(PpobEvent.OtherError(response.errorMessage))
                        } else {
                            setEventStatus(PpobEvent.ServerError(response.errorMessage))
                        }
                    else {
                        setEventStatus(PpobEvent.InternetError(response.errorMessage))
                    }
                }
            }
            else -> {}
        }
    }

    fun getTrainTicketUrl() = viewModelScope.launch {
        val accountId = SessionManager.getInstance().businessId
        val request = TrainEnrollmentRequest(productCategory = PpobConst.CATEGORY_TRAIN_TICKET)
        when (val response =
                finproUseCase.getTrainTicketUrl(accountId,request)) {
            is ApiSuccessResponse -> {
                setViewState(currentViewState()?.copy(showLoading = false))
                setEventStatus(PpobEvent.ShowTrainWebView(response.body))
            }
            is ApiErrorResponse -> {
                setViewState(currentViewState()?.copy(showLoading = false))
                if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE)
                    setEventStatus(PpobEvent.ServerError(response.errorMessage))
                else {
                    setEventStatus(PpobEvent.InternetError(response.errorMessage))
                }
            }
            else -> {}
        }
    }

    fun getTrainEnrollmentDetail(enrollmentId: String) = viewModelScope.launch(Dispatchers.IO) {
        val accountId = SessionManager.getInstance().businessId
        val response =
                finproUseCase.getTrainEnrollmentDetail(accountId, enrollmentId)
        if (response is ApiSuccessResponse) {
            setEventStatus(PpobEvent.ShowTrainTicketDetail(response.body))
        }
    }

    fun setFragmentList(
        versionFilteredList: List<PpobListItem>?,
        from: String,
        accountNumber: String,
        phoneNumber: String,
        code: String,
        layoutType: String,
        machineNumber: String,
        frameNumber: String,
        category: String
    ): List<Fragment> {
        val ppobFragmentList = ArrayList<Fragment>()
        versionFilteredList?.forEach {
            when (it.category) {
                PpobConst.CATEGORY_PULSA -> {
                    ppobFragmentList.add(
                        PrepaidPulsaFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_EWALLET -> {
                    ppobFragmentList.add(
                        EWalletFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code)
                        )
                    )
                }
                PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                    ppobFragmentList.add(
                        InternetAndTvCableFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_PULSA_POSTPAID -> {
                    ppobFragmentList.add(
                        PostpaidPulsaFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_MULTIFINANCE -> {
                    ppobFragmentList.add(
                        MultifinanceFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_VEHICLE_TAX -> {
                    ppobFragmentList.add(
                        VehicleTaxFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber),
                            getValueBasedOnCategory(category, it.category, layoutType),
                            getValueBasedOnCategory(category, it.category, machineNumber),
                            getValueBasedOnCategory(category, it.category, frameNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_PLN_POSTPAID, PpobConst.CATEGORY_LISTRIK_POSTPAID -> {
                    ppobFragmentList.add(
                        PostpaidListrikNewFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_LISTRIK -> {
                    ppobFragmentList.add(
                        PrepaidListrikNewFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_PAKET_DATA -> {
                    ppobFragmentList.add(
                        PacketDataFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_BPJS -> {
                    ppobFragmentList.add(
                        BpjsFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_PDAM -> {
                    ppobFragmentList.add(
                        PdamFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }
                PpobConst.CATEGORY_VOUCHER_GAME -> {
                    ppobFragmentList.add(Fragment())
                }
                PpobConst.CATEGORY_TRAIN_TICKET -> {
                    ppobFragmentList.add(Fragment())
                }
                else -> {}
            }
        }
        return ppobFragmentList
    }

    private fun getValueBasedOnCategory(
        selectedCategory: String,
        category: String,
        value: String
    ): String {
        return if (selectedCategory == category) value else ""
    }

    fun getVersionList() = RemoteConfigUtils.getPpobConfigs().ppobList?.filter { it.startVersion.orNil <= BuildConfig.VERSION_CODE && (it.endVersion.orNil >= BuildConfig.VERSION_CODE || it.endVersion == -1) }?.sortedBy { it.rank }

    fun checkPhoneNumberValidity(number: String) : Boolean{
        var phoneNumber = Utility.beautifyPhoneNumber(number)
        if (phoneNumber.startsWith("8")) phoneNumber = "0$phoneNumber"
        viewState.value = currentViewState()?.copy(numberLengthInvalid =  phoneNumber.length > 13 || phoneNumber.length < 10)
        return !(phoneNumber.length > 13 || phoneNumber.length < 10)
    }

    fun onSearchTextChanged(searchText: String, ppobProductList: List<PpobProduct>) = viewModelScope.launch {
        if (searchText.isNotBlank()) {
            setEventStatus(PpobEvent.ShowProductsList(list = ppobProductList.filter {
                it.name?.contains(
                    searchText,
                    ignoreCase = true
                ) ?: false
            }, isFilteredCall = true, searchTerm = searchText))
        } else {
            setEventStatus(PpobEvent.ShowProductsList(list = ppobProductList, isFilteredCall = true, searchTerm = searchText))
        }
    }

    fun removeFavourite(favouriteId: String, context: Context) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response =
                finproUseCase.deleteFavourite(
                    SessionManager.getInstance().businessId,
                    favouriteId
                )) {
                is ApiSuccessResponse -> {
                    setEventStatus(
                        PpobEvent.RefreshFavourite(
                            message = response.body.message
                                ?: context.getString(R.string.remove_favourite_success_msg),
                            refreshFavourite = true
                        )
                    )
                }
                is ApiErrorResponse -> {
                    setEventStatus(
                        PpobEvent.RefreshFavourite(
                            message = response.errorMessage,
                            false
                        )
                    )
                }
                else -> {}
            }
        }
    }

    fun getPhoneNumberFromActivityResult(intent: Intent?): String {
        var phoneNumber = intent?.getStringExtra(CustomerListActivity.PHONE_NUMBER).orEmpty()
        if (phoneNumber.startsWith("8")) phoneNumber = "0$phoneNumber"
        return phoneNumber
    }

    fun isVoucherGame(category: String) = category == PpobConst.CATEGORY_VOUCHER_GAME
    fun isTrainTicket(category: String) = category == PpobConst.CATEGORY_TRAIN_TICKET

    fun cancelOrder(accountId: String, orderId: String) = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.cancelOrder(accountId, orderId)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }
}