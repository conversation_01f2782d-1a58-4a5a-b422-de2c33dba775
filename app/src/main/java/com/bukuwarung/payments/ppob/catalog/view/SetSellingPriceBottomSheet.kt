package com.bukuwarung.payments.ppob.catalog.view

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.observe
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.BottomSheetSetSellingPriceBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.ppob.PricingType
import com.bukuwarung.payments.data.model.ppob.ProductsItem
import com.bukuwarung.payments.ppob.catalog.viewmodel.PpobCatalogViewModel
import com.bukuwarung.utils.*
import com.google.android.material.bottomsheet.BottomSheetBehavior

class SetSellingPriceBottomSheet: BaseBottomSheetDialogFragment() {

    private val viewModel: PpobCatalogViewModel by activityViewModels()
    private var _binding: BottomSheetSetSellingPriceBinding? = null
    private val binding get() = _binding!!
    private var source: Int? = null

    companion object {
        const val TAG = "SetSellingPriceBottomSheet"
        private const val ARG_SOURCE = "arg_source"
        const val SOURCE_ATUR = 0
        const val SOURCE_ATUR_HARGA = 1
        fun createIntent(source: Int): SetSellingPriceBottomSheet {
            val fragment = SetSellingPriceBottomSheet()
            fragment.arguments = Bundle().apply {
                putInt(ARG_SOURCE, source)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetSetSellingPriceBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.setOnShowListener {
            Utilities.showBottomSheet(it, BottomSheetBehavior.STATE_EXPANDED)
        }
        source = arguments?.getInt(ARG_SOURCE)
        viewModel.getCurrentProduct()
        subscribeState()
    }

    private fun setProductDetails(productItem: ProductsItem, pricingType: PricingType, stepChange: Double, category: String, minAdminFee: Double, maxAdminFee: Double) {
        with(binding.amountView) {
            source?.let {
                setProductDetails(productItem, pricingType, stepChange, minAdminFee, maxAdminFee, it)
                shouldPreviousButtonBeEnabled = !viewModel.checkIfFirstProduct()
                setPreviousButtonEnabled(shouldPreviousButtonBeEnabled)
                shouldNextButtonBeEnabled = !viewModel.checkIfLastProduct()
                setNextButtonEnabled(shouldNextButtonBeEnabled)
            }
            binding.btnSubmit.setSingleClickListener {
                viewModel.setSellingPrice( getTotalAmount().toDouble(),
                    if (source == SOURCE_ATUR) PricingType.PROFIT_MARGIN else pricingType,
                    binding.swRounding.isChecked, category,
                    if (source == SOURCE_ATUR_HARGA) productItem.productCode else null,
                PpobConst.SCREEN_CATALOG_BOTTOM_SHEET)
            }
            binding.ivPrevious.setSingleClickListener {
                viewModel.getPreviousProduct()
            }
            binding.ivNext.setSingleClickListener {
                viewModel.getNextProduct()
            }
        }
    }

    fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is PpobCatalogViewModel.Event.CurrentProductItem -> {
                    setProductDetails(it.productItem, it.pricingType, it.stepChange, it.category, it.minAdminFee, it.maxAdminFee)
                }
                is PpobCatalogViewModel.Event.SellingPriceUpdated -> {
                    if (it.screenType == PpobConst.SCREEN_CATALOG_BOTTOM_SHEET) {
                        it.category?.let { category -> viewModel.getBillersList(category) }
                        handlePriceUpdated()
                        toggleProgress(false)
                    }
                }
                is PpobCatalogViewModel.Event.ShowLoadingOnBottomSheet -> {
                    toggleProgress(true)
                }
                is PpobCatalogViewModel.Event.DismissBottomSheet -> {
                    dismiss()
                }
                else -> {}
            }
        }
    }

    private fun handlePriceUpdated() {
        with(binding.amountView) {
            updateSellingPrice()
            if (source == SOURCE_ATUR) {
                binding.btnSubmit.hideView()
                binding.tvSaved.showView()
            } else {
                toggleSubmitButtonVisibility(false)
                toggleSubmittedTextViewVisibility(true)
                setPreviousButtonEnabled(shouldPreviousButtonBeEnabled)
                setNextButtonEnabled(shouldNextButtonBeEnabled)
            }
        }
    }

    private fun toggleProgress(flag: Boolean) {
        with(binding) {
            progressBar.visibility = flag.asVisibility()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        InputUtils.hideKeyboard(context)
    }

}