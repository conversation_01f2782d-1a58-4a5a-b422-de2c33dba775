package com.bukuwarung.payments.ppob.reminders.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.PPOB_REMINDERS
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.FragmentReminderBinding
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.payments.PaymentHistoryDetailsActivity
import com.bukuwarung.payments.constants.PpobConst.ELIGIBLE_FOR_REMINDER
import com.bukuwarung.payments.constants.PpobConst.QUERY_STATUS
import com.bukuwarung.payments.constants.PpobConst.REMINDER_BILL_CHECKED
import com.bukuwarung.payments.constants.PpobConst.REMINDER_ORDER_CREATED
import com.bukuwarung.payments.constants.PpobConst.REMINDER_ORDER_EXPIRED
import com.bukuwarung.payments.constants.PpobConst.REMINDER_ORDER_FAILED
import com.bukuwarung.payments.constants.PpobConst.REMINDER_SENT
import com.bukuwarung.payments.constants.PpobConst.TAB_BILLS_LIST
import com.bukuwarung.payments.data.model.ppob.ReminderFilterResponse
import com.bukuwarung.payments.data.model.ppob.RemindersItem
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.payments.ppob.confirmation.view.PpobOrderFormActivity
import com.bukuwarung.payments.ppob.reminders.adapter.ReminderItemAdapter
import com.bukuwarung.payments.ppob.reminders.viewmodel.ReminderFilterViewModel
import com.bukuwarung.payments.ppob.reminders.viewmodel.ReminderViewModel
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.setStyleButtonOutlinePrimary
import com.bukuwarung.utils.setupForSearch
import com.bukuwarung.utils.showView
import kotlinx.coroutines.launch

class ReminderFragment : BaseFragment(), PaymentDownBottomSheet.PaymentDownBsListener {
    private val viewModel: ReminderViewModel by activityViewModels()
    private val filterViewModel: ReminderFilterViewModel by activityViewModels()
    private var fragmentReminderBinding: FragmentReminderBinding? = null
    private val binding get() = fragmentReminderBinding!!
    private val reminderProductAdapter by lazy {
        ReminderItemAdapter(::clickAction)
    }
    private var filterResponse: ReminderFilterResponse? = null

    override fun setupView(view: View) {
        viewModel.init(
            hashMapOf(
                QUERY_STATUS to listOf(
                    ELIGIBLE_FOR_REMINDER,
                    REMINDER_BILL_CHECKED,
                    REMINDER_SENT,
                    REMINDER_ORDER_CREATED, REMINDER_ORDER_EXPIRED, REMINDER_ORDER_FAILED
                )
            )
        )
        viewModel.providePagingSource()
        binding.bukuErrorView.addCallback(errorViewCallBack)
        binding.rvReminder.apply {
            layoutManager = LinearLayoutManager(context)
            itemAnimator = DefaultItemAnimator()
            adapter = reminderProductAdapter
        }
        search()
        viewModel.getFilter()
        binding.includeSearch.btnFilter.setSingleClickListener {
            ReminderFilterBottomSheet.createIntent(filterResponse, TAB_BILLS_LIST).show(childFragmentManager, "reminderFilterBottomSheet")
        }
    }

    override fun onCreateView(
            layoutInflater: LayoutInflater,
            viewGroup: ViewGroup?,
            bundle: Bundle?
    ): View {
        fragmentReminderBinding =
                FragmentReminderBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun subscribeState() {
        filterViewModel.observeFilterList.observe(this) {
            binding.includeSearch.btnFilter.setStyleButtonOutlinePrimary(context)
            binding.includeSearch.btnFilter.setIconTintResource(R.color.blue_60)
            viewModel.updateFilter(filterViewModel.createQueryParam(it))
        }
        viewModel.productData.observe(this) {
            lifecycleScope.launch {
                if (it != null) reminderProductAdapter.submitData(it)
            }

        }

        viewModel.observeEvent.observe(this) {
            when (it) {
                is ReminderViewModel.Event.ShowInternetError -> {
                    binding.progressBar.hideView()
                    showPaymentDownBottomSheet(false)
                }
                is ReminderViewModel.Event.ShowBillPaidError -> {
                    binding.progressBar.hideView()
                    NotificationUtils.alertErrorBottom(getString(R.string.bill_already_paid))
                }
                is ReminderViewModel.Event.ShowServerError -> {
                    binding.progressBar.hideView()
                    showPaymentDownBottomSheet(true, it.message)
                }
                is ReminderViewModel.Event.ShowLoader -> {
                    binding.progressBar.showView()
                }
                is ReminderViewModel.Event.HideLoader -> {
                    binding.progressBar.hideView()
                }
                is ReminderViewModel.Event.BillCheck -> {
                    binding.progressBar.hideView()
                    ReminderBottomSheet.createIntent(it.billResponse, it.source)
                            .show(childFragmentManager, "reminder_bottom_sheet")
                }

                is ReminderViewModel.Event.MarkedAsReminded -> {
                    viewModel.invalidateDataSource()
                }

                is ReminderViewModel.Event.ShowFilter -> {
                    filterResponse = it.filterList
                }
                is ReminderViewModel.Event.Checkout -> {
                    startActivity(context?.let { context ->
                        PpobOrderFormActivity.createIntent(
                            context, it.finproOrderResponse, isReminder = true
                        )
                    })
                }
                else -> {}
            }
        }

        viewModel.pagingStatus.observe(this) { status ->
            when (status) {
                PagingStatus.Loading -> {
                    with(binding) {
                        progressBar.showView()
                        includeEmpty.root.hideView()
                        bukuErrorView.hideView()
                        rvReminder.hideView()
                    }
                }
                PagingStatus.LoadingNextPage -> {
                    binding.progressBar.showView()
                }
                is PagingStatus.Loaded, PagingStatus.EmptyNextPage -> {
                    with(binding) {
                        progressBar.hideView()
                        includeEmpty.root.hideView()
                        bukuErrorView.hideView()
                        rvReminder.showView()
                    }
                }
                PagingStatus.Empty -> {
                    with(binding) {
                        progressBar.hideView()
                        includeEmpty.root.showView()
                        bukuErrorView.hideView()
                        rvReminder.hideView()
                        includeEmpty.btFavourite.hideView()
                        includeEmpty.ivImage.setImageResource(R.drawable.ic_reminder_empty)
                        includeEmpty.tvHeading.text = getString(R.string.customer_not_found)
                        includeEmpty.tvSubHeading.text =
                            getString(R.string.try_changing_another_name)
                    }
                }
                is PagingStatus.Error -> {
                    with(binding) {
                        progressBar.hideView()
                        includeEmpty.root.hideView()
                        bukuErrorView.showView()
                        rvReminder.hideView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.server_error_title),
                            getString(R.string.server_error_subtitle),
                            getString(R.string.reload), R.drawable.ic_server_down
                        )
                    }
                }
                PagingStatus.NoInternet -> {
                    with(binding) {
                        progressBar.hideView()
                        includeEmpty.root.hideView()
                        bukuErrorView.showView()
                        rvReminder.hideView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.no_connection_title),
                            getString(R.string.no_connection_message),
                            getString(R.string.reload), R.drawable.ic_no_inet
                        )
                    }
                }
                else -> {}
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            viewModel.invalidateDataSource()
        }

        override fun messageClicked() {}

    }

    private fun search() {
        with(binding.includeSearch) {
            etSearch.setupForSearch(lifecycleScope, enableImeAction = false) { newQuery ->
                viewModel.changeQueryText(newQuery)
            }
        }
    }

    private fun clickAction(remindersItem: RemindersItem) {
        when (remindersItem.status) {
            REMINDER_ORDER_CREATED -> {
                startActivity(
                    PaymentHistoryDetailsActivity.createIntent(
                        context = requireContext(),
                        orderId = remindersItem.createdOrderId,
                        paymentType = remindersItem.product?.category, entryPoint = PPOB_REMINDERS
                    )
                )
            }
            REMINDER_SENT -> {
                checkKyc {
                    remindersItem.id?.let {
                        viewModel.checkBill(it, ReminderBottomSheet.SOURCE_BUY)
                    }
                }
            }
            ELIGIBLE_FOR_REMINDER, REMINDER_BILL_CHECKED -> {
                checkKyc {
                    remindersItem.id?.let {
                        viewModel.checkBill( it, ReminderBottomSheet.SOURCE_REMIND)
                    }
                }
            }
            REMINDER_ORDER_EXPIRED, REMINDER_ORDER_FAILED -> {
                checkKyc {
                    remindersItem.id?.let {
                        viewModel.checkout(
                            it,
                            remindersItem.product?.sellingPrice.orNil,
                            remindersItem.product?.itemPrice.toString(),
                            true,
                            remindersItem.product?.category
                        )
                    }
                }
            }
            else -> {}
        }
    }

    private fun checkKyc(action: () -> Unit){
        if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PPOB)) {
            PaymentUtils.showKycKybStatusBottomSheet(childFragmentManager, AnalyticsConst.PPOB)
        } else {
            action()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentReminderBinding = null
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, "PaymentDownBottomSheet")
    }

    override fun onButtonClicked() {

    }

}