package com.bukuwarung.payments.checkout

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.databinding.ItemPaymentCategoryInfoBinding
import com.bukuwarung.payments.data.model.PaymentCategoryItem


class PaymentCategoryInfoAdapter :
    RecyclerView.Adapter<PaymentCategoryInfoAdapter.PaymentCategoryInfoViewHolder>() {
    private var list = arrayListOf<PaymentCategoryItem>()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PaymentCategoryInfoViewHolder {
        val itemBinding = ItemPaymentCategoryInfoBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PaymentCategoryInfoViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: PaymentCategoryInfoViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: ArrayList<PaymentCategoryItem>) {
        this.list = list
        notifyDataSetChanged()
    }

    inner class PaymentCategoryInfoViewHolder(private val binding: ItemPaymentCategoryInfoBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(category: PaymentCategoryItem) {
            binding.tvCategoryName.text = category.name
            binding.tvCategoryDescription.text = category.description
        }
    }
}