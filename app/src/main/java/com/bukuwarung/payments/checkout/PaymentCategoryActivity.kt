package com.bukuwarung.payments.checkout

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.text.Html
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.ActivityPaymentCategoryBinding
import com.bukuwarung.payments.adapters.PaymentCategoryItemAdapter
import com.bukuwarung.payments.data.model.PaymentCategory
import com.bukuwarung.payments.data.model.PaymentCategoryItem
import com.bukuwarung.utils.*
import javax.inject.Inject


class PaymentCategoryActivity : BaseActivity(), PaymentCategoriesInfoFragment.Callback,
    PaymentCategoryItemAdapter.Callback {

    @Inject
    lateinit var viewModel: PaymentCategoryViewModel

    private var categories: MutableList<PaymentCategoryItem>? = null
    private lateinit var binding: ActivityPaymentCategoryBinding
    private lateinit var paymentCategoryInfoAdapter: PaymentCategoryInfoAdapter
    private lateinit var paymentCategoryAdapter: PaymentCategoryItemAdapter
    private var infoFragment: PaymentCategoriesInfoFragment? = null
    private val paymentType by lazy { intent?.getStringExtra(PAYMENT_TYPE) }
    private val selectedCatId by lazy { intent?.getStringExtra(SELECTED_CAT_ID) }

    companion object {
        private const val PAYMENT_TYPE = "payment_type"
        private const val SELECTED_CAT_ID = "selected_cat_id"

        fun createIntent(
            context: Context, paymentType: String, selectedCategoryId: String?
        ) = Intent(context, PaymentCategoryActivity::class.java).apply {
            putExtra(PAYMENT_TYPE, paymentType)
            putExtra(SELECTED_CAT_ID, selectedCategoryId)
        }
    }

    override fun setViewBinding() {
        binding = ActivityPaymentCategoryBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        paymentCategoryInfoAdapter = PaymentCategoryInfoAdapter()
        viewModel.getPaymentCategoryList(paymentType ?: "")
        setToolBarView()
        paymentCategoryAdapter = PaymentCategoryItemAdapter(this)
        binding.rvCategories.apply {
            adapter = paymentCategoryAdapter
            layoutManager = LinearLayoutManager(context)
        }
        binding.includeError.backBtn.setSingleClickListener {
            finish()
        }
        binding.tvCategoryInfo.textHTML(getString(R.string.category_learn_more))
        binding.tvCategoryInfo.setOnClickListener {
            categories?.let {
                infoFragment = PaymentCategoriesInfoFragment.createInstance(it)
                supportFragmentManager.beginTransaction().add(
                    R.id.fragment_container_view,
                    infoFragment!!,
                    PaymentCategoriesInfoFragment.TAG
                ).commit()
                binding.fragmentContainerView.showView()
            }
        }
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is PaymentCategoryViewModel.Event.ShowCategoryList -> {
                    val categoryListWithUniqueInfo = mutableListOf<PaymentCategoryItem>()
                    it.list.forEach { category ->
                        category.categoryList.forEach { item ->
                            if (checkUniquePaymentCategory(item, categoryListWithUniqueInfo))
                                categoryListWithUniqueInfo += item
                        }
                    }
                    categoryListWithUniqueInfo.sortBy { it.priority }
                    categories = categoryListWithUniqueInfo
                    paymentCategoryAdapter.setData(categoryListWithUniqueInfo, selectedCatId)
                }
                is PaymentCategoryViewModel.Event.ShowLoader -> {
                    binding.pbProgress.visibility = it.showLoader.asVisibility()
                }
                is PaymentCategoryViewModel.Event.ShowServerDown -> {
                    binding.includeError.root.showView()
                }
            }
        }
    }

    override fun onBackPressed() {
        if (binding.fragmentContainerView.isVisible) {
            dismissFragment()
        } else super.onBackPressed()
    }

    private fun setToolBarView() {
        with(binding.includeToolBar) {
            tvTitle.text = if (paymentType == PaymentConst.PaymentRequest) {
                getString(
                    R.string.select_category_title_s,
                    getString(R.string.label_payment_in)
                )
            } else {
                getString(
                    R.string.select_category_title_s,
                    getString(R.string.label_payment_out)
                )
            }
            btnBack.setOnClickListener { onBackPressed() }
            tvHelp.hideView()
        }
    }

    private fun checkUniquePaymentCategory(
        paymentCategoryItem: PaymentCategoryItem,
        paymentCategoryList: List<PaymentCategoryItem>
    ): Boolean {
        return paymentCategoryList.firstOrNull { it.name == paymentCategoryItem.name } == null
    }

    override fun dismissFragment() {
        infoFragment?.let { supportFragmentManager.beginTransaction().remove(it).commit() }
        binding.fragmentContainerView.hideView()
    }

    override fun onCategorySelected(category: PaymentCategoryItem) {
        val eventName = if (paymentType == PaymentConst.PaymentRequest)
            AnalyticsConst.EVENT_CHOOSE_CATEGORY_PAYMENT_IN
        else
            AnalyticsConst.EVENT_CHOOSE_CATEGORY_PAYMENT_OUT
        AppAnalytics.trackEvent(
            eventName, AppAnalytics.PropBuilder()
                .put(AnalyticsConst.USE_CASE, category.title)
                .put(AnalyticsConst.CATEGORY, category.name)
        )
        val intent = Intent()
        intent.putExtra(PaymentCheckoutActivity.PAYMENT_CATEGORY, category)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

}