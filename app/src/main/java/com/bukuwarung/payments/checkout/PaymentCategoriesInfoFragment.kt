package com.bukuwarung.payments.checkout

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.databinding.FragmentPaymentCategoriesInfoBinding
import com.bukuwarung.payments.data.model.PaymentCategoryItem


class PaymentCategoriesInfoFragment : Fragment() {

    private lateinit var binding: FragmentPaymentCategoriesInfoBinding

    private var callback: Callback? = null

    interface Callback {
        fun dismissFragment()
    }

    companion object {
        const val TAG = "Categories_info_frag"
        const val CATEGORIES_DATA = "categories_data"

        fun createInstance(categories: List<PaymentCategoryItem>): PaymentCategoriesInfoFragment {
            val fragment = PaymentCategoriesInfoFragment()
            fragment.arguments = bundleOf(Pair(CATEGORIES_DATA, categories))
            return fragment
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater, viewGroup: ViewGroup?, bundle: Bundle?
    ): View {
        binding = FragmentPaymentCategoriesInfoBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { callback = it as? Callback }
        if (context is Callback) callback = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.includeToolBar.txtToolbarTitle.text =
            getString(R.string.product_category_description)
        binding.includeToolBar.backBtn.setOnClickListener {
            callback?.dismissFragment()
        }
        val categoryItems = arguments?.getParcelableArrayList<PaymentCategoryItem>(CATEGORIES_DATA)
        val infoAdapter = PaymentCategoryInfoAdapter()
        binding.rvCategoryInfo.apply {
            adapter = infoAdapter
            layoutManager = LinearLayoutManager(context)
        }
        categoryItems?.let { infoAdapter.setData(categoryItems) }
    }
}