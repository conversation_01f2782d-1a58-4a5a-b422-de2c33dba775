package com.bukuwarung.payments

import android.content.Context
import android.content.Intent
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.contact.ui.ContactSearchResultsFragment
import com.bukuwarung.contact.ui.CustomerSearchUseCase
import com.bukuwarung.contact.ui.UserContactFragment
import com.bukuwarung.databinding.ActivityContactPaymentBinding
import com.bukuwarung.payments.data.model.FavouriteRequest
import com.bukuwarung.payments.viewmodels.PaymentContactViewModel
import javax.inject.Inject

class PaymentContactActivity : BaseActivity(),
        ContactSearchResultsFragment.OnCustomerSelectedCallback,
        UserContactFragment.OnSaveButtonCallback {

    private lateinit var binding: ActivityContactPaymentBinding

    private var userContactFragment: UserContactFragment? = null

    companion object{
         private const val ORDER_ID = "orderId"
         private const val ENTRY_POINT = "entryPoint"

        fun createIntent(context: Context, orderId: String?, entryPoint: String): Intent {
            val intent = Intent(context, PaymentContactActivity::class.java)
            intent.putExtra(ORDER_ID, orderId)
            intent.putExtra(ENTRY_POINT, entryPoint)
            return intent
        }
    }

    @Inject
    lateinit var viewModel: PaymentContactViewModel

    override fun setViewBinding() {
        binding = ActivityContactPaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        userContactFragment = UserContactFragment.getInstance(
            getString(R.string.favourite_title),
            UserContactFragment.TRANSACTION_TYPE_FAVOURITE,
            CustomerSearchUseCase.FAVORITE,
            intent?.getStringExtra(ORDER_ID).orEmpty()
        ).also {
            supportFragmentManager.beginTransaction()
                .add(
                    binding.contactFragmentContainer.id,
                    it
                ).commit()
        }
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this, Observer {
            when (it) {
                is PaymentContactViewModel.Event.onAddFavourite -> {
                    if (it.favouriteDetail == null) {
                        userContactFragment?.showSameNameError(it.message)
                    } else {
                        val intent = Intent()
                        intent.putExtra("message", it.message)
                        intent.putExtra("favourite_detail", it.favouriteDetail)
                        setResult(RESULT_OK, intent)
                        finish()
                    }
                }
            }
        })
    }

    override fun onCustomerSelected(contact: Contact?, contactSource: String) {
        viewModel.addFavourite(
                FavouriteRequest(
                        contact?.name,
                        contact?.mobile,
                        intent?.getStringExtra(ORDER_ID)
                ), intent?.getStringExtra(ENTRY_POINT), this@PaymentContactActivity
        )
    }

    override fun onSave(name: String) {
        viewModel.addFavourite(FavouriteRequest(name, null, intent?.getStringExtra(ORDER_ID)), intent?.getStringExtra(ENTRY_POINT), this@PaymentContactActivity)
    }

}