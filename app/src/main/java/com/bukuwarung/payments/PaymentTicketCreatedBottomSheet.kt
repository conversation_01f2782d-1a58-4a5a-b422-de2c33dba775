package com.bukuwarung.payments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.BuildConfig
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.databinding.BottomSheetPaymentTicketCreatedBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.data.model.TicketDataItem
import com.bukuwarung.utils.*

class PaymentTicketCreatedBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        private const val ARG_TICKET_DATA = "ticket_data"
        fun createInstance(ticketData: TicketDataItem) =
            PaymentTicketCreatedBottomSheet().apply {
                this.arguments = Bundle().apply {
                    putParcelable(ARG_TICKET_DATA, ticketData)
                }
            }
    }

    private var _binding: BottomSheetPaymentTicketCreatedBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetPaymentTicketCreatedBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val ticketData = arguments?.getParcelable<TicketDataItem>(ARG_TICKET_DATA)
        ticketData?.let {
            with(binding) {
                btnConfirm.setSingleClickListener {
                    val url = "${RemoteConfigUtils.getAssistTicketPageUrl()}${ticketData.ticketId}"
                    startActivity(WebviewActivity.createIntent(context, url, ticketData.ticketId))
                    dismiss()
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}