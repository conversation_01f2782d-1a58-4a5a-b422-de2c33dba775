package com.bukuwarung.payments.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.databinding.ItemListCustomerBinding
import com.bukuwarung.databinding.ItemListSectionBinding

class CustomerListAdapter(private val onCustomerClickListener: OnCustomerClickListener) : ListAdapter<CustomerListAdapter.Item, RecyclerView.ViewHolder>(ItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == ITEM_TYPE_SECTION) {
            SectionViewHolder(
                    ItemListSectionBinding.inflate(
                            LayoutInflater.from(parent.context),
                            parent,
                            false
                    )
            )
        } else {
            CustomerViewHolder(
                    ItemListCustomerBinding.inflate(
                            LayoutInflater.from(parent.context),
                            parent,
                            false
                    ),
                    onCustomerClickListener
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is SectionViewHolder) {
            holder.bind(getItem(position).sectionTitle)
        } else if (holder is CustomerViewHolder) {
            holder.bind(getItem(position).data!!)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position).isSection) {
            ITEM_TYPE_SECTION
        } else {
            ITEM_TYPE_CUSTOMER
        }
    }

    inner class SectionViewHolder(private val binding: ItemListSectionBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(title: String) {
            binding.sectionTitle = title
        }
    }

    inner class CustomerViewHolder(private val binding: ItemListCustomerBinding, onCustomerClickListener: OnCustomerClickListener) : RecyclerView.ViewHolder(binding.root) {
        fun bind(customer: CustomerEntity) {
            binding.customer = customer
            binding.onCustomerClick = onCustomerClickListener
        }
    }

    data class Item(
            val isSection: Boolean = false,
            val sectionTitle: String = "",
            val data: CustomerEntity? = null
    )

    class ItemDiffCallback : DiffUtil.ItemCallback<Item>() {
        override fun areItemsTheSame(oldItem: Item, newItem: Item): Boolean {
            return if (oldItem.isSection && newItem.isSection) {
                oldItem.sectionTitle == newItem.sectionTitle
            } else if (oldItem.data != null && newItem.data != null)
                oldItem.data.customerId == newItem.data.customerId
            else {
                oldItem.toString() == newItem.toString()
            }
        }

        override fun areContentsTheSame(oldItem: Item, newItem: Item): Boolean {
            return oldItem.toString() == newItem.toString()
        }
    }

    companion object {
        const val ITEM_TYPE_SECTION = 0
        const val ITEM_TYPE_CUSTOMER = 1

        fun generateItems(sectionTitle: String? = null, customers: List<CustomerEntity>): MutableList<Item> {
            val generatedList = mutableListOf<Item>()

            if (customers.isNullOrEmpty()) return generatedList

            if (!sectionTitle.isNullOrEmpty()) {
                generatedList.add(Item(isSection = true, sectionTitle = sectionTitle))
            }

            customers.forEach {
                generatedList.add(Item(data = it))
            }
            return generatedList
        }
    }

    class OnCustomerClickListener(val clickListener: (customer: CustomerEntity) -> Unit) {
        fun onClick(customer: CustomerEntity) = clickListener(customer)
    }
}