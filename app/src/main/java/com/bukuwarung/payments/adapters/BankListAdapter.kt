package com.bukuwarung.payments.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.databinding.ItemListBankBinding
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.showView
import com.bumptech.glide.Glide


class BankListAdapter(private val clickAction: (Bank) -> Unit) : RecyclerView.Adapter<BankListAdapter.BankViewHolder>() {

    private var list = emptyList<Bank>()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BankViewHolder {
        val itemBinding = ItemListBankBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BankViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: BankViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<Bank>) {
        this.list = list
        notifyDataSetChanged()
    }

    class BankViewHolder(private val binding: ItemListBankBinding, private val clickAction: (Bank) -> Unit) : RecyclerView.ViewHolder(binding.root) {
        fun bind(bank: Bank) {
            with(binding) {
                val context = root.context
                Glide.with(context)
                        .load(bank.logo)
                        .placeholder(R.drawable.ic_bank)
                        .error(R.drawable.ic_bank)
                        .into(imageBank)
                txtBankTitle.text = bank.bankName
                if (bank.isDisabled.isTrue) {
                    clLayout.setBackgroundColor(ContextCompat.getColor(context, R.color.black_5))
                    tvPayMethodError.text = bank.message
                    tvPayMethodError.showView()
                    root.setOnClickListener(null)
                } else {
                    root.setOnClickListener {
                        clickAction(bank)
                    }
                    clLayout.setBackgroundColor(ContextCompat.getColor(context, R.color.white))
                    tvPayMethodError.hideView()
                }
            }
        }
    }
}
