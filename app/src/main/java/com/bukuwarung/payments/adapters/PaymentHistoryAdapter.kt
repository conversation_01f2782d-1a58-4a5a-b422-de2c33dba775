package com.bukuwarung.payments.adapters

import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.GradientDrawable
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.widget.TextViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.ItemListPaymentHistoryBinding
import com.bukuwarung.databinding.ItemLoadMoreBinding
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.utils.PaymentUtils.ICON_RES
import com.bukuwarung.payments.utils.PaymentUtils.TEXT_MESSAGE
import com.bukuwarung.payments.utils.PaymentUtils.TEXT_STYLE
import com.bukuwarung.utils.*
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlin.math.abs


class PaymentHistoryAdapter(
    private val callback: Callback
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_ITEM = 0
        const val VIEW_TYPE_SEE_ALL = 1
    }

    private var searchText = EMPTY_STRING
    private var filteredOrders = emptyList<PaymentHistory>()
    private var allOrders = emptyList<PaymentHistory>()

    interface Callback {
        fun openOrderDetail(order: PaymentHistory)
        fun openOrdersHistory()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return if (viewType == VIEW_TYPE_ITEM)
            PaymentHistoryViewHolder(
                ItemListPaymentHistoryBinding.inflate(inflater, parent, false)
            ) else LoadMorePaymentHistoryViewHolder(
            ItemLoadMoreBinding.inflate(inflater, parent, false)
        )
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is PaymentHistoryViewHolder) {
            holder.bind(filteredOrders[position])
        } else if (holder is LoadMorePaymentHistoryViewHolder) {
            holder.bind()
        }
    }

    override fun getItemCount() = filteredOrders.size

    fun setData(list: List<PaymentHistory>) {
        this.filteredOrders = list
        this.allOrders = list
        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int) = filteredOrders[position].viewType

    inner class PaymentHistoryViewHolder(private val binding: ItemListPaymentHistoryBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PaymentHistory) {
            binding.apply {
                root.setOnClickListener { callback.openOrderDetail(item) }
                updateTextWithQuery(tvOrderTitle, item.displayName, searchText)

                tvDate.text = item.formattedDate()
                val statusData = PaymentUtils.getPaymentStatusLabelData(
                    item.type, item.status, binding.tvStatus.context
                )

                if (statusData != null) {
                    (statusData[TEXT_MESSAGE] as String?)?.let { binding.tvStatus.text = it }
                    (statusData[ICON_RES] as Int?)?.let { ivOrderIcon.setImageResource(it) }
                    (statusData[TEXT_STYLE] as Int?)?.let {
                        TextViewCompat.setTextAppearance(binding.tvStatus, it)
                    }
                } else {
                    binding.tvStatus.text = ""
                    ivOrderIcon.setImageResource(android.R.color.transparent)
                }
                setPaymentAmount(item.type, item.status, item.amount.orNil)
                vwDivider.visibility = (adapterPosition != filteredOrders.size - 1).asVisibility()

                val tag = item.tags?.get(PaymentConst.TAG_PRICE_LABEL)
                tag?.let {
                    tvTag.showView()
                    tvTag.textHTML(it.displayText)
                    tvTag.setTextColor(Color.parseColor(tag.textColor.orEmpty()))
                    val bgShape = tvTag.background as? GradientDrawable
                    bgShape?.setColorFilter(
                        PorterDuffColorFilter(
                            Color.parseColor(tag.backgroundColor.orEmpty()),
                            PorterDuff.Mode.SRC_ATOP
                        )
                    )
                } ?: run {
                    tvTag.hideView()
                }

                val cashbackDetails = item.tags?.get(PaymentConst.TAG_HISTORY_DETAIL)
                cashbackDetails?.let {
                    tvTransactionDesc.showView()
                    tvTransactionDesc.text = it.displayText
                    if (it.textColor.isNotNullOrEmpty()) {
                        try {
                            tvTransactionDesc.setTextColor(Color.parseColor(it.textColor))
                        } catch (ex: Exception) {
                            tvTransactionDesc.setTextColor(root.context.getColorCompat(R.color.black_80))
                            FirebaseCrashlytics.getInstance().recordException(ex)
                        }
                    } else {
                        tvTransactionDesc.setTextColor(root.context.getColorCompat(R.color.black_80))
                    }
                } ?: run {
                    tvTransactionDesc.hideView()
                }
            }
        }

        private fun updateTextWithQuery(view: TextView, displayName: String?, searchText: String?) {
            Utilities.safeLet(displayName, searchText) { parentText, highLightedText ->
                if (parentText.contains(highLightedText, ignoreCase = true)) {
                    val lowerParent = parentText.lowercase()
                    val lowerHighlighted = highLightedText.lowercase()
                    val startPos: Int = lowerParent.indexOf(lowerHighlighted)
                    val endPos: Int = startPos + lowerHighlighted.length
                    val spanString: Spannable = SpannableString(parentText)
                    spanString.setSpan(
                        ForegroundColorSpan(
                            ContextCompat.getColor(view.context, R.color.colorPrimary)
                        ),
                        startPos,
                        endPos,
                        Spannable.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                    view.text = spanString
                } else {
                    view.text = displayName
                }
            } ?: run {
                view.text = displayName
            }
        }

        private fun setPaymentAmount(type: String?, status: String?, balance: Double) {
            binding.tvAmount.text = Utility.formatAmount(abs(balance))
            val statusData = PaymentUtils.getPaymentAmountData(
                type, status
            )
            if (statusData != null) {
                (statusData[TEXT_STYLE] as Int?)?.let {
                    TextViewCompat.setTextAppearance(binding.tvAmount, it)
                }
            }
        }
    }

    inner class LoadMorePaymentHistoryViewHolder(private val binding: ItemLoadMoreBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.seeAllTxt.setOnClickListener { callback.openOrdersHistory() }
        }
    }
}
