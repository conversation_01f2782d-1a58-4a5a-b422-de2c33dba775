package com.bukuwarung.payments.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.databinding.PhonebookContactItemBinding
import com.bukuwarung.utils.ProfileIconHelper
import com.bukuwarung.utils.Utility

class CustomerPhoneListAdapter(private val actionClicked: (String) -> Unit) : RecyclerView.Adapter<CustomerPhoneListAdapter.ContactViewHolder>() {

    private var list = emptyList<CustomerEntity>()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ContactViewHolder {
        return ContactViewHolder(
                    PhonebookContactItemBinding.inflate(
                            LayoutInflater.from(parent.context),
                            parent,
                            false
                    )
            )
    }

    fun setData(list: List<CustomerEntity>) {
        this.list = list
        notifyDataSetChanged()
    }
    override fun onBindViewHolder(holder: ContactViewHolder, position: Int) {
            holder.bind(list[position])
    }

    inner class ContactViewHolder(private val binding: PhonebookContactItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(customer: CustomerEntity) {
            binding.name.text = customer.name
            binding.mobile.text = Utility.beautifyPhoneNumber(customer.phone)
            binding.root.setOnClickListener { actionClicked(customer.phone) }
            ProfileIconHelper.setProfilePic(binding.contactPhoto.context, binding.contactPhoto, binding.nameInitials, customer.name, customer.image)

        }
    }

    override fun getItemCount() = list.size
}