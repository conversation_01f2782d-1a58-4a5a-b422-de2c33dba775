package com.bukuwarung.payments.adapters

import android.content.Context
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.database.entity.UrlType
import com.bukuwarung.databinding.ItemListBankAccountsBinding
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.google.gson.Gson

class RefundBankAccountListAdapter(
    private val isAddForSelfOnly: Boolean = false,
    private val isSelectOnly: Boolean = false,
    private val refundAmount: Double? = null,
    private val clickAction: (RefundBankAccount) -> Unit,
    private val clickDeleteAction: (RefundBankAccount) -> Unit,
    private val clickLink: (UrlType, String?) -> Unit,
    private val isDefaultSelectedShown :Boolean = true
) : RecyclerView.Adapter<RefundBankAccountListAdapter.RefundBankAccountViewHolder>() {

    private var list = emptyList<RefundBankAccount>()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RefundBankAccountViewHolder {
        val itemBinding = ItemListBankAccountsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return RefundBankAccountViewHolder(itemBinding, clickAction, clickDeleteAction, clickLink)
    }

    override fun onBindViewHolder(holder: RefundBankAccountViewHolder, position: Int) {
        holder.bind(list[position], isAddForSelfOnly, isSelectOnly, isDefaultSelectedShown, refundAmount)
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<RefundBankAccount>) {
        this.list = list
        notifyDataSetChanged()
    }

    class RefundBankAccountViewHolder(private val binding: ItemListBankAccountsBinding, private val clickAction: (RefundBankAccount) -> Unit,
                                private val clickDeleteAction: (RefundBankAccount) -> Unit, private val clickLink: (UrlType, String?) -> Unit) : RecyclerView.ViewHolder(binding.root) {
        fun bind(
            bankAccount: RefundBankAccount,
            isAddForSelfOnly: Boolean = false,
            isSelectOnly: Boolean = false,
            isDefaultSelectedShown: Boolean,
            refundAmount: Double?
        ) {
            val context = binding.root.context
            binding.apply {
                radioDefault.visibility = if (isAddForSelfOnly) View.GONE else View.VISIBLE
                if(isDefaultSelectedShown) {
                    radioDefault.isChecked = bankAccount.isSelected
                }else{
                    radioDefault.isChecked = false
                }
                txtBankTitle.text = Utility.dashDividedString(bankAccount.bankCode, bankAccount.accountHolderName)
                Glide.with(context)
                    .load(bankAccount.getBankLogoIfAvailable())
                    .placeholder(R.drawable.ic_bank)
                    .error(R.drawable.ic_bank)
                    .into(imageBank)
                txtBankTitle.clearDrawables()
                tvError.movementMethod = LinkMovementMethod.getInstance()
                tvInfo.movementMethod = LinkMovementMethod.getInstance()
                when {
                    !PaymentUtils.isSupportedForRefund(refundAmount, bankAccount) -> {
                        vDisabled.showView()
                        txtAccountNumber.text = context.getString(R.string.cant_be_processed)
                        txtAccountNumber.setTextColor(ContextCompat.getColor(context, R.color.red_80))
                        txtBankTitle.setTextColor(ContextCompat.getColor(context, R.color.black_60_50op))
                        imageBank.alpha = 0.5F
                        tvInfo.hideView()
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                    }
                    bankAccount.isManualMatchingRejected() -> {
                        vDisabled.showView()
                        txtBankTitle.setDrawable(left = R.drawable.ic_warning_triangle)
                        tvInfo.hideView()
                        val rejectedReason = bankAccount.manualMatchingInfo?.rejectedReason
                        val errorMessage = when {
                            rejectedReason != null -> rejectedReason + " ${context.getString(R.string.to_learn)}."
                            bankAccount.message.isNotNullOrBlank() -> bankAccount.message + " ${context.getString(R.string.to_learn)}."
                            else -> context.getString(R.string.name_matching_failed)
                        }
                        setErrorText(errorMessage, context.getString(R.string.to_learn), context, UrlType.MATCHING_INFO, bankAccount)
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(ContextCompat.getColor(context, R.color.black_40))
                        root.setOnClickListener(null)
                    }
                    bankAccount.isManualMatchingInProgress() -> {
                        vDisabled.showView()
                        txtBankTitle.setDrawable(left = R.drawable.ic_warning_triangle)
                        tvError.hideView()
                        setInfoText(context.getString(R.string.name_matching_in_progress), context.getString(R.string.to_learn))
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(ContextCompat.getColor(context, R.color.black_40))
                        root.setOnClickListener(null)
                    }
                    bankAccount.isManualMatchingRequired() -> {
                        vDisabled.showView()
                        txtBankTitle.setDrawable(left = R.drawable.ic_warning_triangle)
                        tvInfo.hideView()
                        setErrorText(context.getString(R.string.name_matching_error), context.getString(R.string.here), context, UrlType.APPEAL_FLOW, bankAccount)
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                        clItem.isEnabled = false
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(ContextCompat.getColor(context, R.color.black_40))
                        root.setOnClickListener(null)
                    }
                    bankAccount.isDisabled.isTrue -> {
                        vDisabled.showView()
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(ContextCompat.getColor(context, R.color.black_40))
                        txtBankTitle.setTextColor(ContextCompat.getColor(context, R.color.black_60))
                        imageBank.alpha = 1F
                        tvInfo.showView()
                        val errorText = bankAccount.message ?: context.getString(R.string.un_supported_text)
                        setErrorText(errorText + " " + context.getString(R.string.bold_text), context.getString(R.string.bold_text), context, UrlType.FAQ_BLOCKED_ACCOUNT)
                        radioDefault.isChecked = false
                        radioDefault.isEnabled = false
                    }
                    else -> {
                        vDisabled.hideView()
                        txtAccountNumber.text = bankAccount.accountNumber
                        txtAccountNumber.setTextColor(ContextCompat.getColor(context, R.color.black_40))
                        txtBankTitle.setTextColor(ContextCompat.getColor(context, R.color.black_60))
                        imageBank.alpha = 1F
                        tvInfo.hideView()
                        radioDefault.isEnabled = true
                        root.setOnClickListener {
                            clickAction(bankAccount)
                        }
                    }
                }
                iconMenu.visibility = when {
                    bankAccount.isManualMatchingInProgress() -> View.GONE
                    isSelectOnly -> View.GONE
                    else -> View.VISIBLE
                }
                iconMenu.setOnClickListener {
                    PopupMenu(it.context, it).apply {
                        setOnMenuItemClickListener {
                            clickDeleteAction(bankAccount)
                            true
                        }
                        inflate(R.menu.menu_delete)
                        show()
                    }
                }
            }
        }

        private fun setErrorText(errorText: String, clickableText: String, context: Context, urlType: UrlType, bankAccount: RefundBankAccount? = null) {
            with(binding) {
                tvError.showView()
                tvError.text = Utilities.makeSectionOfTextClickable(errorText, clickableText, object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        clickLink(urlType, Gson().toJson(bankAccount))
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = true
                        ds.color = context.getColorCompat(R.color.red_80)
                    }
                })
            }
        }

        private fun setInfoText(text: String, clickableText: String) {
            with(binding) {
                tvInfo.showView()
                tvInfo.text = Utilities.makeSectionOfTextClickable(text, clickableText, object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        clickLink(UrlType.MATCHING_INFO, null)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = true
                    }

                })
            }
        }
    }
}
