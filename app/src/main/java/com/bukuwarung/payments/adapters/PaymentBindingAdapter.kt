package com.bukuwarung.payments.adapters

import android.graphics.Color
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import androidx.databinding.BindingAdapter
import com.bukuwarung.R
import com.bukuwarung.utils.Utility
import kotlin.math.abs


@BindingAdapter("balanceWithCurrency", "styleBalance", "setBalanceIfEmpty", requireAll = false)
fun setBalanceWithCurrency(textView: TextView, balance: Double, styleBalance: Boolean = true, setIfEmpty: Boolean) {
    if (setIfEmpty && !textView.text.isNullOrEmpty() && Utility.extractAmountFromText(textView.text.toString()) > 0) return

    textView.text = String.format("%s %s", Utility.getCurrency(), Utility.formatCurrency(abs(balance)))
    if (styleBalance)
        textView.setTextColor(ContextCompat.getColor(textView.context, if (balance >= 0) R.color.in_green else R.color.out_red))
}

fun setBalance(textView: TextView, balance: Double) {
    textView.text = String.format("%d", Utility.formatCurrency(abs(balance)))
}

@BindingAdapter("ovalColor")
fun setOvalColor(textView: TextView, initial: String?) {
    val color = when (initial?.get(0)?.toInt()?.rem(7)) {
        0 -> Color.parseColor("#66bdff")
        1 -> Color.parseColor("#33a7ff")
        2 -> Color.parseColor("#03A9F4")
        3 -> Color.parseColor("#0084ff")
        4 -> Color.parseColor("#0077ff")
        5 -> Color.parseColor("#006bff")
        else -> Color.parseColor("#005eff")
    }

    DrawableCompat.setTint(textView.background, color)
}