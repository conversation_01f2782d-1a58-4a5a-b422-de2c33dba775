package com.bukuwarung.payments.adapters

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.ItemPaymentHistoryDetailTimelineBinding
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.PaymentProgress
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.utils.PaymentUtils.ICON_COLOR
import com.bukuwarung.payments.utils.PaymentUtils.ICON_RES
import com.bukuwarung.payments.utils.PaymentUtils.TEXT_COLOR
import com.bukuwarung.utils.*
import com.bukuwarung.utils.DateTimeUtils.getTimestampFromUtcDate
import java.util.Date

class PaymentHistoryTimelineAdapter(private val showPaymentCallback: (Boolean) -> Unit) : ListAdapter<PaymentProgress, PaymentHistoryTimelineAdapter.PaymentHistoryTimelineViewHolder>(ItemDiffCallback()) {

    private var showReceipt = false
    private var orderStatus:String? = null
    private var paymentType:String? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PaymentHistoryTimelineViewHolder {
        return PaymentHistoryTimelineViewHolder(
            ItemPaymentHistoryDetailTimelineBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
        ))
    }

    override fun onBindViewHolder(holder: PaymentHistoryTimelineViewHolder, position: Int) {
        getItem(position)?.let { paymentProgress ->
            val hasAdditionalInfo = paymentProgress.additional_info != null && paymentProgress.additional_info.isNotEmpty()
            if (position < itemCount - 1) {
                paymentProgress.hasNextTimestamp = getItem(position + 1).timestamp != null
            }
            if (position > 0) {
                paymentProgress.hasPreviousTimestamp = getItem(position - 1).timestamp != null
            }

            holder.bind(paymentProgress, position == itemCount - 1, hasAdditionalInfo, orderStatus)
        }
    }

    fun setOrderStatus(status: String?, paymentType: String?) {
        orderStatus = status
        this.paymentType = paymentType
    }

    fun setShowReceiptStatus(showReceipt: Boolean) {
        this.showReceipt = showReceipt
    }

    inner class PaymentHistoryTimelineViewHolder(private val binding: ItemPaymentHistoryDetailTimelineBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PaymentProgress, isLast: Boolean, hasAdditionalInfo: Boolean = false,
            orderStatus: String?
        ) {
            binding.apply {
                if (item.state == PaymentHistory.STATUS_PAID && item.timestamp != null && !item.hasNextTimestamp && (Date().time - getTimestampFromUtcDate(item.timestamp)) > (20 * 60 * 60000L)) {
                    txtInfoPending.visibility = View.VISIBLE
                } else {
                    txtInfoPending.visibility = View.GONE
                }
                viewPendingStatusTimeline.visibility = (!isLast).asVisibility()
                txtLabelStatusPending.text = item.description
                txtStatusPendingDate.text = item.getFormattedTimestamp()
                ComponentUtil.setVisible(txtStatusPendingDate, item.timestamp.isNotNullOrEmpty())
                txtAdditionalInfo.text = item.additional_info
                ComponentUtil.setVisible(txtAdditionalInfo,hasAdditionalInfo)
                val statusData = PaymentUtils.getPaymentProgressData(binding.root.context, item)
                (statusData[TEXT_COLOR] as Int?)?.let {
                    txtLabelStatusPending.setTextColor(it)
                    txtStatusPendingDate.setTextColor(it)
                    viewPendingStatusTimeline.background.mutate().colorFilter =
                        PorterDuffColorFilter(it, PorterDuff.Mode.SRC_ATOP)
                }
                (statusData[ICON_RES] as Int?)?.let { iconStatusPending1.setImageResource(it) }
                (statusData[ICON_COLOR] as Int?)?.let {
                    iconStatusPending1.setColorFilter(it, PorterDuff.Mode.SRC_ATOP)
                }

                txtShowReceipt.visibility = (orderStatus == PaymentHistory.STATUS_PAID && item.state == PaymentHistory.STATUS_PAID).asVisibility()
                if (txtShowReceipt.isVisible && paymentType == PaymentConst.TYPE_QRIS.toString()) {
                    txtShowReceipt.hideView()
                }
                txtShowReceipt.setSingleClickListener {
                    showReceipt = !showReceipt
                    showPaymentCallback.invoke(showReceipt)
                    txtShowReceipt.text = txtShowReceipt.context.getString(if(showReceipt) R.string.hide_receipt else R.string.show_receipt )
                }

                txtShowReceipt.text = txtShowReceipt.context.getString(if(showReceipt) R.string.hide_receipt else R.string.show_receipt )
            }
        }
    }

    class ItemDiffCallback : DiffUtil.ItemCallback<PaymentProgress>() {
        override fun areItemsTheSame(oldItem: PaymentProgress, newItem: PaymentProgress): Boolean {
            return oldItem.toString() == newItem.toString()
        }

        override fun areContentsTheSame(oldItem: PaymentProgress, newItem: PaymentProgress): Boolean {
            return oldItem.toString() == newItem.toString()
        }
    }
}
