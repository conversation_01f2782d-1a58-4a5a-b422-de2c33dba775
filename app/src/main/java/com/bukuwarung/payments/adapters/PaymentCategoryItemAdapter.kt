package com.bukuwarung.payments.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.databinding.ItemPaymentCategoryItemBinding
import com.bukuwarung.payments.data.model.PaymentCategoryItem
import com.bukuwarung.utils.setSingleClickListener
import com.bumptech.glide.Glide


class PaymentCategoryItemAdapter(var callback: Callback) :
    RecyclerView.Adapter<PaymentCategoryItemAdapter.PaymentCategoryItemViewHolder>() {

    private var list = emptyList<PaymentCategoryItem>()
    private var selectedCatId: String? = null

    interface Callback {
        fun onCategorySelected(category: PaymentCategoryItem)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup, viewType: Int
    ): PaymentCategoryItemViewHolder {
        val itemBinding = ItemPaymentCategoryItemBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PaymentCategoryItemViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: PaymentCategoryItemViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<PaymentCategoryItem>, selectedCatId: String?) {
        this.list = list
        this.selectedCatId = selectedCatId
        notifyDataSetChanged()
    }

    inner class PaymentCategoryItemViewHolder(private val binding: ItemPaymentCategoryItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(paymentCategoryItem: PaymentCategoryItem) {
            with(binding) {
                Glide.with(root.context)
                    .load(paymentCategoryItem.logoAddress)
                    .into(ivPaymentCategory)
                tvCategoryName.text = paymentCategoryItem.name
                itemView.setSingleClickListener {
                    rbPaymentCategory.isSelected = true
                    callback.onCategorySelected(list[adapterPosition])
                }
                rbPaymentCategory.isChecked =
                    selectedCatId == paymentCategoryItem.paymentCategoryId
            }
        }
    }

}