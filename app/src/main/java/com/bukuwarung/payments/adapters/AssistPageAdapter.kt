package com.bukuwarung.payments.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.databinding.LayoutAssistItemBinding
import com.bukuwarung.utils.setSingleClickListener

class AssistPageAdapter(private val list: List<String>, private val clickAction: (Int) -> Unit) :
    RecyclerView.Adapter<AssistPageAdapter.AssistViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AssistViewHolder {
        val itemBinding =
            LayoutAssistItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return AssistViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: AssistViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    override fun getItemCount(): Int = list.size

    inner class AssistViewHolder(
        private val binding: LayoutAssistItemBinding,
        private val clickAction: (Int) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: String, position: Int) {
            binding.tvProblem.text = item
            binding.tvProblem.setSingleClickListener {
                clickAction(position)
            }
        }
    }
}