package com.bukuwarung.payments.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.databinding.ItemBillerBinding
import com.bukuwarung.payments.data.model.Biller
import com.bukuwarung.utils.getColorCompat
import com.bumptech.glide.Glide


class BillerAdapter(private val clickAction: (Biller?) -> Unit) : RecyclerView.Adapter<BillerAdapter.BillerViewHolder>() {

     var list = emptyList<Biller>()
    var selectedBiller = ""
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BillerViewHolder {
        val itemBinding = ItemBillerBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BillerViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>iewHolder, position: Int) {
        holder.bind(list[position], selectedBiller)
    }

    override fun getItemCount(): Int = list.size

    fun setBillerName(billerName: String?) {
        selectedBiller = billerName ?: ""
        notifyDataSetChanged()
    }

    fun setData(list: List<Biller>) {
        this.list = list
        notifyDataSetChanged()
    }

    class BillerViewHolder(private val binding: ItemBillerBinding, private val clickAction: (Biller?) -> Unit) : RecyclerView.ViewHolder(binding.root) {
        fun bind(biller: Biller, selectedBiller: String) {
            val context = binding.root.context
            Glide.with(context)
                    .load(biller.icon)
                    .placeholder(R.drawable.ic_bank)
                    .error(R.drawable.ic_bank)
                    .into(binding.billerImg)
            binding.root.setOnClickListener {
                clickAction(biller)
            }
            if (selectedBiller == biller.code) {
                binding.cardView.strokeColor = context.getColorCompat(R.color.colorPrimary)
                binding.bgLayout.setBackgroundColor(context.getColorCompat(R.color.alice_blue))
            } else {
                binding.cardView.strokeColor = context.getColorCompat(android.R.color.transparent)
                binding.bgLayout.setBackgroundColor(context.getColorCompat(R.color.white))
            }
        }
    }
}
