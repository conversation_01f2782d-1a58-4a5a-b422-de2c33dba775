package com.bukuwarung.payments.adapters

import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.ItemPpobProductBinding
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.utils.*
import com.bukuwarung.utils.Utilities.makeSectionOfTextBold


class PpobProductAdapter(private val clickAction: (PpobProduct) -> Unit, private val productType: String) : RecyclerView.Adapter<PpobProductAdapter.PpobProductViewHolder>() {

    private var list = emptyList<PpobProduct>()
    private var searchTerm = ""
    private var enableButton = false
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PpobProductViewHolder {
        val itemBinding = ItemPpobProductBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PpobProductViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: PpobProductViewHolder, position: Int) {
        holder.bind(list[position], enableButton)
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<PpobProduct>, searchTerm: String = "") {
        this.list = list.filter { it.productInfo?.isFreeForm.isFalseOrNull }
        this.searchTerm = searchTerm
        notifyDataSetChanged()
    }

    fun enableButtonToggle(enable: Boolean) {
        enableButton = enable
        notifyDataSetChanged()
    }

    inner class PpobProductViewHolder(private val binding: ItemPpobProductBinding, private val clickAction: (PpobProduct) -> Unit) : RecyclerView.ViewHolder(binding.root) {
        fun bind(product: PpobProduct, enableButton: Boolean) {
            with(binding) {
                val context = root.context
                nameTxt.text = if (productType == PpobConst.CATEGORY_PAKET_DATA) {
                    makeSectionOfTextBold(product.name
                            ?: AppConst.EMPTY_STRING, searchTerm, object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            //if click requires
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            super.updateDrawState(ds)
                            ds.isUnderlineText = false
                            ds.color = context.getColorCompat(R.color.blue_60)
                        }
                    })
                } else {
                    product.name
                }
                priceTxt.text = Utility.formatAmount(product.amount)
                chooseBtn.isEnabled = if (productType != PpobConst.CATEGORY_EWALLET) enableButton else true
                val isPopular = product.productInfo?.isPopular?.toBoolean() ?: false
                popularTxt.visibility = isPopular.asVisibility()
                rootCard.setBackgroundColor(context.getColorCompat(if (isPopular) R.color.color_info_window_background else R.color.white))
                if (product.active == true) {
                    disturbanceTxt.visibility = View.GONE
                    overlayDisabled.visibility = View.GONE
                    chooseBtn.visibility = View.VISIBLE
                    chooseBtn.setOnClickListener {
                        clickAction(product)
                    }
                } else {
                    disturbanceTxt.visibility = View.VISIBLE
                    overlayDisabled.visibility = View.VISIBLE
                    chooseBtn.visibility = View.INVISIBLE
                }
                product.discount?.let {
                   ivPromo.showView()
                    tvOriginalPrice.showView()
                    tvOriginalPrice.text = Utility.formatAmount(it.original)
                }?: kotlin.run {
                    ivPromo.hideView()
                    tvOriginalPrice.hideView()
                }
            }
        }
    }
}
