package com.bukuwarung.payments.bottomsheet

import android.os.Bundle
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.BottomSheetAddUsedBankBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.Utilities.makeSectionOfTextClickable
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.setSingleClickListener

class AddUsedBankBottomSheet : BaseBottomSheetDialogFragment() {
    private var bottomSheetAddUsedBankBinding: BottomSheetAddUsedBankBinding? = null

    private val binding get() = bottomSheetAddUsedBankBinding!!

    companion object {
        private const val ERROR_MESSAGE = "ERROR_MESSAGE"
        fun newInstance(message: String? = null): AddUsedBankBottomSheet = AddUsedBankBottomSheet().apply {
            val bundle = Bundle()
            bundle.putString(ERROR_MESSAGE, message)
            arguments = bundle
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        bottomSheetAddUsedBankBinding = BottomSheetAddUsedBankBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            closeDialog.setSingleClickListener {
                dialog?.dismiss()
            }
            btClose.setSingleClickListener {
                dialog?.dismiss()
            }
            subtitleTxt.text = makeSectionOfTextClickable((arguments?.getString(ERROR_MESSAGE)
                    ?: getString(R.string.bank_account_used_subtitle)) + " " + getString(R.string.bold_text), getString(R.string.bold_text), object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val intent = WebviewActivity.createIntent(context, AppConst.FAQ_USED_ACCOUNT_BW_URL, "")
                    startActivity(intent)
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                    ds.color = subtitleTxt.context.getColorCompat(R.color.blue_60)
                }
            })
            subtitleTxt.movementMethod = LinkMovementMethod.getInstance()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        bottomSheetAddUsedBankBinding = null
    }
}
