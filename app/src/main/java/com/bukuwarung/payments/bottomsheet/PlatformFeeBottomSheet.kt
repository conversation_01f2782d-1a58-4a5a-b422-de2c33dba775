package com.bukuwarung.payments.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.databinding.BottomSheetPlatformFeeBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.setSingleClickListener


class PlatformFeeBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "PlatformFeeBottomSheet"
        fun createInstance() = PlatformFeeBottomSheet()
    }

    interface Callback {
        fun openPlatformFeeAndCashbackInfo() {}
        fun understoodFeeInfo() {}
    }

    private var _binding: BottomSheetPlatformFeeBinding? = null
    private val binding get() = _binding!!

    private var listener: Callback? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        listener = (context as? Callback)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetPlatformFeeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.btnUnderstand.setSingleClickListener {
            listener?.understoodFeeInfo()
            dialog?.dismiss()
        }

        binding.tvLearnMore.setSingleClickListener {
            listener?.openPlatformFeeAndCashbackInfo()
            dialog?.dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
