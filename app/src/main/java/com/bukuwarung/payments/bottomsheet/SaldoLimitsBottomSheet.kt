package com.bukuwarung.payments.bottomsheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.databinding.BottomSheetSaldoLimitsBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.data.model.SaldoResponse
import com.bukuwarung.utils.*


class SaldoLimitsBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "SaldoLimitsBottomSheet"
        const val SALDO_RESPONSE = "saldo_response"

        fun createInstance(saldoResponse: SaldoResponse) = SaldoLimitsBottomSheet().apply {
            arguments = Bundle().apply {
                putParcelable(SALDO_RESPONSE, saldoResponse)
            }
        }
    }

    private var _binding: BottomSheetSaldoLimitsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetSaldoLimitsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val saldoBalance = arguments?.getParcelable<SaldoResponse>(SALDO_RESPONSE)

        with(binding) {
            saldoBalance?.let {
                val remainingDailyLimitInRp = Utility.formatAmount(it.debitDailyLimit.orNil - it.debitDaily.orNil)
                val remainingMonthlyLimitInRp = Utility.formatAmount(it.debitMonthlyLimit.orNil - it.debitMonthly.orNil)
                tvRemainingDailyLimits.text = TextUtils.decorateTextString(
                    getString(R.string.saldo_daily_limits_info, remainingDailyLimitInRp),
                    hashMapOf(remainingDailyLimitInRp to TextUtils.TextDecorations(bold = true))
                )
                tvRemainingMonthlyLimits.text = TextUtils.decorateTextString(
                    getString(R.string.saldo_monthly_limits_info, remainingMonthlyLimitInRp),
                    hashMapOf(remainingDailyLimitInRp to TextUtils.TextDecorations(bold = true))
                )
                tvAdvancedLimitsDailyValue.text =
                    it.advancedLimit?.dailyDebit?.formatAmountShort(requireContext())
                tvAdvancedLimitsMonthlyValue.text =
                    it.advancedLimit?.monthlyDebit?.formatAmountShort(requireContext())
                tvAdvancedLimitsHoldValue.text =
                    it.advancedLimit?.hold?.formatAmountShort(requireContext())

                tvSupremeLimitsDailyValue.text =
                    it.supremeLimit?.dailyDebit?.formatAmountShort(requireContext())
                tvSupremeLimitsMonthlyValue.text =
                    it.supremeLimit?.monthlyDebit?.formatAmountShort(requireContext())
                tvSupremeLimitsHoldValue.text =
                    it.supremeLimit?.hold?.formatAmountShort(requireContext())
            }

            btnUnderstand.setSingleClickListener {
                dismiss()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
