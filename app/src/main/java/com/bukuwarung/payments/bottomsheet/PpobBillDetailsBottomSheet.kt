package com.bukuwarung.payments.bottomsheet

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.PpobBillDetailsBottomSheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.PaymentContactActivity
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.ppob.FavouriteDetail
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.confirmation.view.PpobOrderFormActivity
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.viewmodels.PpobBillDetailsBsViewModel
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject

class PpobBillDetailsBottomSheet : BaseBottomSheetDialogFragment() {
    companion object {
        private const val ORDER_DETAIL = "orderDetail"
        private const val CATEGORY = "category"
        const val TAG = "ppob_bill_detail_bottom_sheet"
        private const val MESSAGE = "message"
        private const val FAVOURITE_DETAIL = "favourite_detail"

        fun createInstance(orderDetail: FinproOrderResponse? = null, category: String) =
            PpobBillDetailsBottomSheet().apply {
                val bundle = Bundle()
                bundle.putParcelable(ORDER_DETAIL, orderDetail as? Parcelable)
                bundle.putString(CATEGORY, category)
                arguments = bundle
            }
    }

    private var _binding: PpobBillDetailsBottomSheetBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var viewModel: PpobBillDetailsBsViewModel

    interface PpobBillDetailsBsListener {
        fun refreshFavouritesTab()
    }

    private var listener: PpobBillDetailsBsListener? = null

    private var orderDetail: FinproOrderResponse? = null
    private val category by lazy { arguments?.getString(CATEGORY) }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        AndroidSupportInjection.inject(this)
        parentFragment?.let { listener = it as? PpobBillDetailsBsListener }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = PpobBillDetailsBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        isCancelable = false
        orderDetail = arguments?.getParcelable(ORDER_DETAIL)
        with(binding) {
            ivClose.setSingleClickListener {
                listener?.refreshFavouritesTab()
                dismiss()
            }
            Glide.with(requireContext())
                .load(orderDetail?.metadata?.logo)
                .error(
                    PpobConst.CATEGORY_DEFAULT_ICON[category] ?: R.drawable.ic_biller_default
                )
                .into(includeBillerInfo.ivPpobUsecase)
            includeBillerInfo.tvCategoryName.setText(
                PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob
            )
            includeBillerInfo.tvNumber.text = orderDetail?.items?.getOrNull(0)?.beneficiary?.accountNumber.orEmpty()
            if (orderDetail?.customerProfile?.isFavorite.isTrue) {
                setFavouriteView()
            } else {
                setNotFavouriteView()
            }
            billDetailView.setView(orderDetail, false, null, null)
            btnNext.setSingleClickListener {
                dismiss()
                startActivity(
                    PpobOrderFormActivity.createIntent(
                        requireActivity(),
                        orderDetail,
                        null,
                        category
                    )
                )
            }
            val isBillAlreadyPaidError = orderDetail?.finproError?.isBillAlreadyPaid().isTrue
            if (orderDetail?.metadata?.warningMessage.isNotNullOrBlank() || isBillAlreadyPaidError) {
                grpErrorMessage.showView()
                if (isBillAlreadyPaidError) {
                    tvErrorMessage.text = orderDetail?.finproError?.message
                    btnNext.isEnabled = false
                } else {
                    tvErrorMessage.text = orderDetail?.metadata?.warningMessage
                }
            }
        }
        setObservers()
    }

    private fun setObservers() {
        viewModel.eventStatus.observe(this) {
            when (it) {
                is PpobEvent.RefreshFavourite -> {
                    Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_LONG
                    ).show()
                    if (it.refreshFavourite) {
                        listener?.refreshFavouritesTab()
                        orderDetail?.customerProfile?.favouriteDetails = null
                        setNotFavouriteView()
                    }
                }
                else -> {}
            }
        }
    }

    private fun setFavouriteView() = with(binding) {
        includeBillerInfo.ivFavourite.setImageResource(R.drawable.ic_favourite_fill)
        includeBillerInfo.ivFavourite.setSingleClickListener {
            PpobUtils.showRemoveFavouriteDialog(requireContext()) {
                viewModel.removeFavourite(
                    orderDetail?.customerProfile?.favouriteDetails?.id.orEmpty(), requireContext()
                )
            }
        }
        binding.billDetailView.setFavouriteData(
            favName = orderDetail?.customerProfile?.favouriteDetails?.alias.orEmpty(),
            isFavourite = true
        )
    }

    private fun setNotFavouriteView() = with(binding) {
        if (PaymentPrefManager.getInstance().getIsShowFasterTrxFavSuggestion()) {
            Utilities.showTooltip(
                requireContext(),
                includeBillerInfo.ivFavourite,
                getString(R.string.faster_trx_using_fav),
                Gravity.BOTTOM
            )
            PaymentPrefManager.getInstance().setIsShowFasterTrxFavSuggestion(false)
        }
        includeBillerInfo.ivFavourite.setImageResource(R.drawable.ic_favourite_grey)
        includeBillerInfo.ivFavourite.setSingleClickListener {
            loadUserContactActivity()
        }
        binding.billDetailView.setFavouriteData(
            favName = orderDetail?.customerProfile?.favouriteDetails?.alias.orEmpty(),
            isFavourite = false
        )
    }

    private fun loadUserContactActivity() {
        startContactActivityForResult.launch(
            PaymentContactActivity.createIntent(
                requireContext(),
                orderDetail?.orderId,
                AnalyticsConst.PPOB_BUY_PAGE
            )
        )
    }

    private val startContactActivityForResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            Toast.makeText(
                context,
                result.data?.getStringExtra(MESSAGE),
                Toast.LENGTH_LONG
            ).show()
            orderDetail?.customerProfile?.favouriteDetails =
                result.data?.getParcelableExtra<FavouriteDetail>(FAVOURITE_DETAIL)
            orderDetail?.customerProfile?.isFavorite = true
            setFavouriteView()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
        _binding = null
    }
}