package com.bukuwarung.payments.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.databinding.BottomSheetLoyaltyDiscountBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.ppob.confirmation.viewmodel.LoyaltyTierDiscountsBSViewModel
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.*
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject


class LoyaltyTierDiscountsBottomSheet : BaseBottomSheetDialogFragment() {

    @Inject
    lateinit var viewModel: LoyaltyTierDiscountsBSViewModel

    companion object {
        const val TAG = "LoyaltyDiscountBottomsheet"
        private const val PAYMENT_TYPE = "payment_type"
        fun createInstance(paymentType: String) = LoyaltyTierDiscountsBottomSheet().apply {
            arguments = Bundle().apply {
                putString(PAYMENT_TYPE, paymentType)
            }
        }
    }

    private val paymentType by lazy { arguments?.getString(PAYMENT_TYPE).orEmpty() }
    private var _binding: BottomSheetLoyaltyDiscountBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        AndroidSupportInjection.inject(this)
        super.onAttach(context)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetLoyaltyDiscountBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding){
            btnUnderstand.setSingleClickListener { dismiss() }
            viewModel.getPaymentLoyaltyDiscounts(paymentType)
            viewModel.observeEvent.observe(viewLifecycleOwner){
                when(it){
                    is LoyaltyTierDiscountsBSViewModel.Event.ShowLoyaltyDiscountsInfo -> {
                        tvTitle.textHTML(it.loyaltyDiscountsInfo)
                        tvTitle.showView()
                        btnUnderstand.showView()
                        bukuErrorView.hideView()
                    }
                    is LoyaltyTierDiscountsBSViewModel.Event.ShowErrorResponse -> {
                        tvTitle.hideView()
                        btnUnderstand.hideView()
                        bukuErrorView.showView()
                        if (it.isServerError) {
                            bukuErrorView.setErrorType(
                                type = BaseErrorView.Companion.ErrorType.SERVER_UNREACHABLE,
                                message = getString(R.string.loading_error_message)
                            )
                            bukuErrorView.addCallback(serverErrorViewCallBack)
                        } else {
                            bukuErrorView.setErrorType(
                                type = BaseErrorView.Companion.ErrorType.CONNECTION_ERROR,
                                message = getString(R.string.no_connection_message),
                                ctaText = getString(R.string.retry)
                            )
                            bukuErrorView.addCallback(internetErrorViewCallBack)
                        }
                    }
                }
            }
        }
    }

    private var serverErrorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            dismiss()
        }

        override fun messageClicked() {}

    }

    private var internetErrorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            viewModel.getPaymentLoyaltyDiscounts(paymentType)
        }

        override fun messageClicked() {}

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
