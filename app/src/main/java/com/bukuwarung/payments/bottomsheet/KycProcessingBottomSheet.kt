package com.bukuwarung.payments.bottomsheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.databinding.KycProcessingBottomSheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment

class KycProcessingBottomSheet : BaseBottomSheetDialogFragment() {
    companion object {
        private const val HAS_JUST_SUBMIT = "hasJustSubmit"
        const val TAG = "kycProcessingBottomSheet"
        fun createInstance(hasJustSubmit: Boolean) = KycProcessingBottomSheet().apply {
            arguments = Bundle().apply {
                putBoolean(HAS_JUST_SUBMIT, hasJustSubmit)
            }
        }
    }

    private lateinit var binding: KycProcessingBottomSheetBinding
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = KycProcessingBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.closeDialog.setOnClickListener {
            dialog?.dismiss()
        }
        if (arguments?.getBoolean(HAS_JUST_SUBMIT) == true) {
            binding.titleTxt.text = getString(R.string.data_sent_message)
            binding.subtitleTxt.text = getString(R.string.verification_time_message)
        } else {
            binding.titleTxt.text = getString(R.string.your_verification_in_progress)
            binding.subtitleTxt.text = getString(R.string.your_verification_in_progress_subtitle)
        }
    }
}
