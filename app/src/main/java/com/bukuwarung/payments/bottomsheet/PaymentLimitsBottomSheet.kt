package com.bukuwarung.payments.bottomsheet

import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.*
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.BottomSheetPaymentLimitsBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.*
import com.google.firebase.crashlytics.FirebaseCrashlytics
import org.json.JSONException
import org.json.JSONObject


class PaymentLimitsBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "PaymentLimitsBottomSheet"
        private const val ERRORS = "errors"
        private const val ENTRY_POINT = "entry_point"
        private const val LIFETIME_LIMIT = "LIFETIME_LIMIT"
        private const val TRANSACTION_AMOUNT_LIMIT = "TRANSACTION_AMOUNT_LIMIT"
        const val TRANSACTION_COUNT_LIMIT = "TRANSACTION_COUNT_LIMIT"

        fun createInstance(errors: String? = null, entryPoint: String? = null) =
            PaymentLimitsBottomSheet().apply {
                val bundle = Bundle()
                bundle.putString(ERRORS, errors)
                bundle.putString(ENTRY_POINT, entryPoint)
                arguments = bundle
            }
    }

    interface Callback {
        fun limitBsDismissCallback()
    }

    private var _binding: BottomSheetPaymentLimitsBinding? = null
    private val binding get() = _binding!!

    private var listener: Callback? = null
    private val errors by lazy { arguments?.getString(ERRORS) }
    private val entryPoint by lazy { arguments?.getString(ENTRY_POINT) }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? Callback }
        if (context is Callback) listener = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetPaymentLimitsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        with(binding) {
            btnVerifyKtp.setSingleClickListener(AppConst.clickDebounceTime, ::openKycWeb)
            btnVerifyKycHalf.setSingleClickListener(AppConst.clickDebounceTime, ::openKycWeb)
            btnVerifyKycFull.setSingleClickListener(AppConst.clickDebounceTime, ::openKycWeb)
            tvAccountVerifyInfo.setSingleClickListener {
                openAboutAccountVerificationWeb()
                dialog?.dismiss()
            }
            btnLater.setOnClickListener {
                listener?.limitBsDismissCallback()
                dialog?.dismiss()
            }
        }

        errors?.let { errors ->
            try {
                val jsonErrors = JSONObject(errors)
                val limitType: String? = when {
                    jsonErrors.has("data") -> (jsonErrors["data"] as JSONObject?)?.get("limit_name") as String?
                    else -> null
                }
                val errorMessage: String? = when {
                    jsonErrors.has("error_message") -> jsonErrors["error_message"] as String?
                    jsonErrors.has("data") -> (jsonErrors["data"] as JSONObject?)?.get("message") as String?
                    else -> getString(R.string.try_later)
                }
                errorMessage?.let {
                    when (limitType) {
                        TRANSACTION_AMOUNT_LIMIT -> {
                            val limitAmount =
                                (jsonErrors["data"] as JSONObject?)?.get("limit_amount") as Int?
                            // Replace limit_amount in errorMessage and make it bold
                            val limitAmountCurrency = Utility.formatAmount(limitAmount?.toDouble())
                            val message = it.replace("\$limit_amount", limitAmountCurrency)
                            binding.tvMessage.text = Utilities.makeSectionOfTextBold(
                                message, limitAmountCurrency, object : ClickableSpan() {
                                    override fun onClick(widget: View) {}

                                    override fun updateDrawState(ds: TextPaint) {
                                        super.updateDrawState(ds)
                                        ds.isUnderlineText = false
                                        ds.typeface = Typeface.DEFAULT_BOLD
                                        ds.color = requireContext().getColorCompat(R.color.black_80)
                                    }
                                })
                        }
                        LIFETIME_LIMIT -> {
                            val limitAmount =
                                (jsonErrors["data"] as JSONObject?)?.get("limit_amount") as Int?
                            binding.tvMessage.text =
                                parseLifetimeLimitError(it, jsonErrors, limitAmount)
                        }
                        TRANSACTION_COUNT_LIMIT -> {
                            val remainingCount =
                                (jsonErrors["data"] as JSONObject?)?.get("remaining_transaction_count") as Int?
                            with(binding) {
                                tvTitle.gravity = Gravity.START or Gravity.CENTER_VERTICAL
                                tvMessage.text = errorMessage
                                tvMessage.visibility = it.isNotEmpty().asVisibility()
                                btnVerifyKtp.hideView()
                                tvAccountVerifyInfo.hideView()
                                tvMessage2.showView()
                                if (remainingCount ?: 0 <= 0) {
                                    tvTitle.text = getString(R.string.verify_premium_account_smile)
                                    btnVerifyKycFull.showView()
                                } else {
                                    tvTitle.text = getString(R.string.verify_premium_account)
                                    btnVerifyKycHalf.showView()
                                    btnLater.showView()
                                }
                            }
                        }
                        else -> {
                            with(binding) {
                                tvTitle.text = getString(R.string.sorry_disturbance)
                                tvAccountVerifyInfo.hideView()
                                btnVerifyKycHalf.hideView()
                                btnLater.hideView()
                                tvMessage.text = errorMessage
                                btnVerifyKtp.text = getString(R.string.back)
                                btnVerifyKtp.setStyleButtonOutlineBlue(context)
                                btnVerifyKtp.setOnClickListener { dialog?.dismiss() }
                            }
                        }
                    }
                }
            } catch (ex: JSONException) {
                FirebaseCrashlytics.getInstance().recordException(ex)
                dismiss()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun openKycWeb() {
        with(context) {
            val url = "${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}?entryPoint=$entryPoint"
            val intent = WebviewActivity.createIntent(
                this, url, AppConst.EMPTY_STRING
            )
            startActivity(intent)
        }
        dialog?.dismiss()
    }

    private fun parseLifetimeLimitError(
        error: String, jsonErrors: JSONObject, limitAmount: Int?
    ): String {
        // Replace limit_amount and remaining_limit in errorMessage and make it bold
        val remainingAmount =
            (jsonErrors["data"] as JSONObject?)?.get("remaining_limit") as Int?
        val remainingLimitAmountCurrency =
            Utility.formatAmount(remainingAmount?.toDouble())
        var message = error.replace("\$remaining_limit", remainingLimitAmountCurrency)
        val startIndexRemainingLimit = message.indexOf(remainingLimitAmountCurrency)
        val endIndexLimitRemainingLimit =
            startIndexRemainingLimit + remainingLimitAmountCurrency.length

        val limitAmountCurrency = Utility.formatAmount(limitAmount?.toDouble())
        message = message.replace("\$limit_amount", limitAmountCurrency)
        val startIndexLimit = message.indexOf(limitAmountCurrency)
        val endIndexLimit = startIndexLimit + limitAmountCurrency.length

        val str = SpannableStringBuilder(message)
        if (startIndexLimit > 0 && endIndexLimit > 0) {
            str.setSpan(
                StyleSpan(Typeface.BOLD), startIndexLimit, endIndexLimit,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        if (startIndexRemainingLimit > 0 && endIndexLimitRemainingLimit > 0) {
            str.setSpan(
                StyleSpan(Typeface.BOLD), startIndexRemainingLimit,
                endIndexLimitRemainingLimit, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            str.setSpan(
                ForegroundColorSpan(requireContext().getColorCompat(R.color.red_60)),
                startIndexRemainingLimit, endIndexLimitRemainingLimit,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        return str.toString()
    }

    private fun openAboutAccountVerificationWeb() {
        with(context) {
            val intent = WebviewActivity.createIntent(
                this,
                RemoteConfigUtils.getPaymentConfigs().aboutKycUrl,
                AppConst.EMPTY_STRING
            )
            startActivity(intent)
        }
    }
}
