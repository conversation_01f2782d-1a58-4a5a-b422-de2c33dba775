package com.bukuwarung.payments.bottomsheet

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.observe
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.database.entity.UrlType
import com.bukuwarung.databinding.BottomSheetBankAccountListBinding
import com.bukuwarung.dialogs.confirmation_dialog.StandardConfirmationDialog
import com.bukuwarung.payments.DeleteBankPromptDialog
import com.bukuwarung.payments.adapters.BankAccountListAdapter
import com.bukuwarung.payments.adapters.RefundBankAccountListAdapter
import com.bukuwarung.payments.banklist.BankAccountListViewModel
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.payments.pin.NewPaymentPinActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.orNil
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject


class BankAccountListBottomSheetFragment : BottomSheetDialogFragment() {

    companion object {
        const val TAG = "bankListBtSheet"
        private const val CUSTOMER_ID = "customerId"
        private const val SELECTED_ACCOUNT_ID = "selectedAccountId"
        private const val ENTRY_POINT = "ENTRY_POINT"
        private const val IS_REFUND = "IS_REFUND"
        private const val REFUND_AMOUNT = "refund_amount"
        private const val PAYMENT_TYPE = "payment_type"
        private const val PROBLEMATIC_BANK_IDS = "problematic_bank_ids"
        private const val SELECTED_BANK_CODE = "selected_bank_code"
        private const val SELECTED_ACCOUNT_NUMBER = "selected_account_number"
        private const val RC_PIN_SELECT = 99
        private const val RC_PIN_DELETE = 98

        fun createBankAccountInstance(
            entryPoint: String, customerId: String? = null, selectedAccountId: String? = null,
            paymentType: Int = PaymentConst.TYPE_PAYMENT_IN,
            problematicBankIds: ArrayList<String>? = null,
            selectedBankCode: String? = null, selectedAccountNumber: String? = null,
        ): BankAccountListBottomSheetFragment {
            val bt = BankAccountListBottomSheetFragment()
            val bundle = Bundle()
            bundle.putString(ENTRY_POINT, entryPoint)
            bundle.putString(CUSTOMER_ID, customerId)
            bundle.putBoolean(IS_REFUND, false)
            bundle.putString(SELECTED_ACCOUNT_ID, selectedAccountId)
            bundle.putInt(PAYMENT_TYPE, paymentType)
            bundle.putStringArrayList(PROBLEMATIC_BANK_IDS, problematicBankIds)
            bundle.putString(SELECTED_BANK_CODE, selectedBankCode)
            bundle.putString(SELECTED_ACCOUNT_NUMBER, selectedAccountNumber)
            bt.arguments = bundle
            return bt
        }

        fun createBankAccountInstance(
            entryPoint:String, isRefund: Boolean = false, refundAmount: Double? = null
        ): BankAccountListBottomSheetFragment {
            val bt = BankAccountListBottomSheetFragment()
            val bundle = Bundle()
            bundle.putString(ENTRY_POINT, entryPoint)
            bundle.putBoolean(IS_REFUND, isRefund)
            bundle.putDouble(REFUND_AMOUNT, refundAmount ?: 0.0)
            bt.arguments = bundle
            return bt
        }
    }

    @Inject
    lateinit var viewModel: BankAccountListViewModel
    private lateinit var mBinding: BottomSheetBankAccountListBinding
    private val customerId by lazy { arguments?.getString(CUSTOMER_ID) }
    private val selectedAccountId by lazy { arguments?.getString(SELECTED_ACCOUNT_ID) }
    private val entryPoint by lazy { arguments?.getString(ENTRY_POINT).orEmpty() }
    private val isRefund by lazy { arguments?.getBoolean(IS_REFUND) }
    private val paymentType by lazy { arguments?.getInt(PAYMENT_TYPE) }
    private val refundAmount by lazy { arguments?.getDouble(REFUND_AMOUNT) }
    private val problematicBankIds by lazy { arguments?.getStringArrayList(PROBLEMATIC_BANK_IDS) }
    private val selectedBankCode by lazy { arguments?.getString(SELECTED_BANK_CODE) }
    private val selectedAccountNumber by lazy { arguments?.getString(SELECTED_ACCOUNT_NUMBER) }
    private lateinit var adapter: BankAccountListAdapter
    private lateinit var refundAdapter: RefundBankAccountListAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialogThemeRound)
    }

    interface BtSheetBankAccountListener {
        fun onBankAccountSelected(bankAccount: BankAccount?)
        fun addNewBankAccount()
    }

    interface RefundBankAccountListener {
        fun onRefundBankAccountSelected(bankAccount: RefundBankAccount, entryPoint: String)
        fun addNewRefundBankAccount(entryPoint: String)
    }

    private var listener: BtSheetBankAccountListener? = null
    private var refundMethodsListener: RefundBankAccountListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        AndroidSupportInjection.inject(this)
        if (context is BtSheetBankAccountListener) {
            listener = context
        }
        if(context is RefundBankAccountListener) {
            refundMethodsListener = context
        }
    }

    private fun setRefundTitle() {
        mBinding.titleTxt.text = getText(R.string.refund_method)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        mBinding = BottomSheetBankAccountListBinding.inflate(inflater, container, false)
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        mBinding.closeImg.setOnClickListener {
            dialog?.dismiss()
        }
        dialog?.setOnShowListener {
            Utilities.showBottomSheet(it, BottomSheetBehavior.STATE_EXPANDED)
        }
        mBinding.refundInfoTxt.visibility = isRefund.asVisibility()
        // NOTE: In case refundAmount is coming, we are allowing user to change before the refund
        // is initiated, so need to hide this warning.
        if (refundAmount.orNil > 0) {
            mBinding.refundInfoTxt.hideView()
        }
        refundAdapter = RefundBankAccountListAdapter(false, true, refundAmount,
            {
                refundMethodsListener?.onRefundBankAccountSelected(it, entryPoint)
                activity?.setResult(Activity.RESULT_OK, Intent().apply {
                    putExtra("bank_name", it.bankName)
                    putExtra("bank_logo", it.bankLogo)
                    putExtra("account_number", it.accountNumber)
                    putExtra("user_name", it.accountHolderName)
                })
                dialog?.dismiss()
            }, {}, { urlType, bankAccount ->
                clickLink(
                    urlType,
                    bankAccount,
                    paymentType = PaymentConst.REFUND
                )
            })
        adapter = BankAccountListAdapter(false, false, problematicBankIds, {
            if (viewModel.isPaymentIn() || viewModel.isForQris()) {
                viewModel.setCurrentSelectedAccount(it)
                startActivityForResult(NewPaymentPinActivity.createIntent(requireActivity(), PinType.PIN_CONFIRM.toString()), RC_PIN_SELECT)
            } else {
                listener?.onBankAccountSelected(it)
                dialog?.dismiss()
            }
        }, {
            DeleteBankPromptDialog.show(
                onPromptClicked = { confirmed ->
                    run {
                        if (confirmed) {
                            if (viewModel.isPaymentIn() || viewModel.isForQris()) {
                                viewModel.setTempDeletedAccount(it)
                                startActivityForResult(NewPaymentPinActivity.createIntent(requireActivity(), PinType.PIN_CONFIRM.toString()),
                                    RC_PIN_DELETE
                                )
                            } else {
                                viewModel.deleteBankAccount(it)
                            }
                        }
                    }
                },
                manager = requireActivity().supportFragmentManager
            )
        }, { urlType, bankAccount ->
            clickLink(
                urlType,
                bankAccount,
                paymentType = PaymentConst.PAYMENT_IN
            )
        }, disableSelectedBank = viewModel.isForQris())
        mBinding.addBankAccountBtn.setOnClickListener {
            if(isRefund == true)
                refundMethodsListener?.addNewRefundBankAccount(entryPoint)
            else
                listener?.addNewBankAccount()
            activity?.setResult(Activity.RESULT_OK)
            dialog?.dismiss()
        }
        mBinding.rvBankAccounts.layoutManager = LinearLayoutManager(context)
        if(isRefund == true)
            mBinding.rvBankAccounts.adapter = refundAdapter
        else
            mBinding.rvBankAccounts.adapter = adapter

        if (entryPoint == AnalyticsConst.PAYMENT_DETAILS || entryPoint == AnalyticsConst.EDC_ORDER_DETAIL)
            setRefundTitle()
        return mBinding.root
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK) return
        when (requestCode) {
            RC_PIN_SELECT -> viewModel.getCurrentSelectedAccount()
            RC_PIN_DELETE -> viewModel.deleteBankAccount()
            else -> {}
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        if(isRefund == true) {
            viewModel.init(PaymentConst.TYPE_PPOB, "", null, null, selectedAccountId)
        } else if (entryPoint == AnalyticsConst.QRIS_TRANSACTION_DETAILS || paymentType == PaymentConst.TYPE_QRIS_INT) {
            viewModel.init(PaymentConst.TYPE_QRIS_INT, entryPoint, null, null, selectedAccountId)
            mBinding.titleTxt.text = getString(R.string.select_account_to_receive_qris)
        } else {
            viewModel.init(
                if (customerId.isNullOrBlank()) PaymentConst.TYPE_PAYMENT_IN else PaymentConst.TYPE_PAYMENT_OUT,
                entryPoint,
                customerId,
                null,
                selectedAccountId,
                selectedBankCode, selectedAccountNumber
            )
        }
        observeData()
    }

    private fun observeData() {
        viewModel.mediatorLiveData.observe(this) {
            adapter.setData(it)
        }
        viewModel.observeEvent.observe(this) {
            when (it) {
                is BankAccountListViewModel.Event.ShowBankList -> {
                    val selectedBank = it.list.firstOrNull { it.isSelected?: AppConst.INT_ZERO > 0 }
                    // Set selected bank in the activity
                    listener?.onBankAccountSelected(selectedBank)
                    adapter.setData(it.list)
                }
                is BankAccountListViewModel.Event.HasOngoingTransaction -> {
                    val dialog = StandardConfirmationDialog(requireContext(), "Error", getString(R.string.delete_bank_error_exist))
                    dialog.show()
                }
                is BankAccountListViewModel.Event.ShowRefundBankList -> refundAdapter.setData(it.list)
                is BankAccountListViewModel.Event.ReturnSelectedAccount -> {
                    listener?.onBankAccountSelected(it.currentSelectedAccount)
                    dialog?.dismiss()
                }
                else -> {}
            }
        }
    }

    private fun clickLink(urlType: UrlType, bankAccount: String? = null, paymentType: String) {
        startActivity(
            PaymentUtils.getBankAccountRedirectionIntent(
                context = requireContext(),
                urlType = urlType,
                bankAccount = bankAccount,
                paymentType = paymentType,
                entryPoint = entryPoint
            )
        )
    }
}
