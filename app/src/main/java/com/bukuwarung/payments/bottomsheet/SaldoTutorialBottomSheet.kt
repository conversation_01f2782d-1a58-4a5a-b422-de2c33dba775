package com.bukuwarung.payments.bottomsheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.bukuwarung.databinding.BottomSheetSaldoTutorialBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.setSingleClickListener


class SaldoTutorialBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "SaldoTutorialBottomSheet"
        fun createInstance() = SaldoTutorialBottomSheet()
    }

    interface SaldoTutorialListener {
        fun tutorialTopupSaldoClicked()
    }

    private var _binding: BottomSheetSaldoTutorialBinding? = null
    private val binding get() = _binding!!

    private var listener: SaldoTutorialListener? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parentFragment?.let { onAttachToParentFragment(it) }
    }

    private fun onAttachToParentFragment(fragment: Fragment) {
        try {
            listener = fragment as SaldoTutorialListener
        } catch (e: ClassCastException) {
            throw ClassCastException("$fragment must implement SaldoTutorialListener")
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _binding = BottomSheetSaldoTutorialBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.btnTopup.setSingleClickListener {
            listener?.tutorialTopupSaldoClicked()
            dialog?.dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
