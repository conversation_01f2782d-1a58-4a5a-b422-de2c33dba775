package com.bukuwarung.payments.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.databinding.BottomSheetKycVerifyWarningBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment

class KycVerifyWarningBottomSheet : BaseBottomSheetDialogFragment() {
    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is KycVerifyListener) {
            listener = context
        }
    }

    interface KycVerifyListener {
        fun kycVerifyCancelClicked()
    }

    private lateinit var binding: BottomSheetKycVerifyWarningBinding
    var listener: KycVerifyListener? = null
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = BottomSheetKycVerifyWarningBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.cancelBtn.setOnClickListener {
            listener?.kycVerifyCancelClicked()
            dialog?.dismiss()
        }
        binding.continueBtn.setOnClickListener {
            dialog?.dismiss()
        }
    }
}
