package com.bukuwarung.payments.bottomsheet

import android.os.Bundle
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.*
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.BottomSheetKycKybBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.*
import com.google.android.material.bottomsheet.BottomSheetBehavior


class KycKybBottomSheet : BaseBottomSheetDialogFragment() {

    enum class UseCase {
        KYC_REQUIRED, KYC_PENDING, KYC_VERIFIED, KYC_REJECTED,
        KYB_REQUIRED, KYB_SUBMITTED, KYB_PENDING, KYB_VERIFIED, KYB_REJECTED
    }

    companion object {
        const val TAG = "KycKybRequiredBottomSheet"
        private const val USE_CASE = "use_case"
        private const val REJECTION_REASON = "rejection_reason"
        private const val ENTRY_POINT = "entry_point"

        fun createInstance(
            useCase: UseCase, rejectionReason: String? = null, entryPoint: String
        ) = KycKybBottomSheet().apply {
            val bundle = Bundle()
            bundle.putSerializable(USE_CASE, useCase)
            bundle.putSerializable(REJECTION_REASON, rejectionReason)
            bundle.putSerializable(ENTRY_POINT, entryPoint)
            arguments = bundle
        }
    }

    private var _binding: BottomSheetKycKybBinding? = null
    private val binding get() = _binding!!
    private val useCase by lazy { arguments?.getSerializable(USE_CASE) as? UseCase }
    private val rejectionReason by lazy { arguments?.getString(REJECTION_REASON) }
    private val entryPoint by lazy { arguments?.getString(ENTRY_POINT) }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetKycKybBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            Utilities.showBottomSheet(it, BottomSheetBehavior.STATE_EXPANDED)
        }

        with(binding) {
            btnVerifyNow.setSingleClickListener(AppConst.clickDebounceTime) {
                openWeb("${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}?from=Payment&entryPoint=$entryPoint")
            }
            when (useCase) {
                UseCase.KYC_REQUIRED -> setKycRequiredView()
                UseCase.KYC_PENDING -> setKycPendingView()
                UseCase.KYC_VERIFIED -> setKycVerifiedView()
                UseCase.KYC_REJECTED -> setKycRejectedView()
                UseCase.KYB_REQUIRED -> setKybRequiredView()
                UseCase.KYB_PENDING -> setKybPendingView()
                UseCase.KYB_SUBMITTED -> setKybSubmittedView()
                UseCase.KYB_VERIFIED -> setKybVerifiedView()
                UseCase.KYB_REJECTED -> setKybRejectedView()
                else -> return
            }
            btnLater.setOnClickListener { dismiss() }
            ivClose.setOnClickListener { dismiss() }
            tvLearnMore.setOnClickListener {
                openWeb(RemoteConfigUtils.getPaymentConfigs().kycTierInfoUrl)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun openWeb(url: String) {
        dismiss()
        with(context) {
            val intent = WebviewActivity.createIntent(this, url, AppConst.EMPTY_STRING)
            startActivity(intent)
        }
    }

    private fun setKycRequiredView() {
        with(binding) {
            tvMessage.hideView()
            btnUnderstand.hideView()
            ivTopBanner.setImageResource(R.drawable.kyc_upgrade_banner_new)
            tvTitle.text = RemoteConfigUtils.getAppText().kycRequiredTitle
            ivFirstMessageIcon.setImageResource(R.drawable.ic_styled_shield)
            tvFirstTitle.text = RemoteConfigUtils.getAppText().kycRequiredInfoTitle1
            tvFirstMessage.text = RemoteConfigUtils.getAppText().kycRequiredInfoMessage1
            ivSecondMessageIcon.setImageResource(R.drawable.ic_coins)
            tvSecondTitle.text = RemoteConfigUtils.getAppText().kycRequiredInfoTitle2
            tvSecondMessage.text = RemoteConfigUtils.getAppText().kycRequiredInfoMessage2
            ivThirdMessageIcon.setImageResource(R.drawable.ic_purse)
            tvThirdTitle.text = RemoteConfigUtils.getAppText().kycRequiredInfoTitle3
            tvThirdMessage.text = RemoteConfigUtils.getAppText().kycRequiredInfoMessage3
            clFourthMessage.hideView()
        }
    }

    private fun setKycPendingView() {
        with(binding) {
            svContent.hideView()
            grCta.hideView()
            ivTopBanner.hideView()
            tvMessage.showView()
            btnUnderstand.showView()
            tvTitle.text = RemoteConfigUtils.getAppText().kycInProgressTitle
            tvTitle.gravity = Gravity.START
            tvMessage.text = RemoteConfigUtils.getAppText().kycInProgressMessage
            btnUnderstand.setOnClickListener { dismiss() }
        }
    }

    private fun setKycVerifiedView() {
        with(binding) {
            tvMessage.hideView()
            btnUnderstand.hideView()
            grCta.hideView()
            ivClose.showView()
            vwCloseBar.hideView()
            ivTopBanner.setImageResource(R.drawable.banner_premium_badge)
            tvTitle.text = RemoteConfigUtils.getAppText().kycVerifiedTitle
            tvTitle.gravity = Gravity.START
            ivFirstMessageIcon.setImageResource(R.drawable.ic_styled_shield)
            tvFirstTitle.text = RemoteConfigUtils.getAppText().kycRequiredInfoTitle1
            tvFirstMessage.text = RemoteConfigUtils.getAppText().kycRequiredInfoMessage1
            ivSecondMessageIcon.setImageResource(R.drawable.ic_coins)
            tvSecondTitle.text = RemoteConfigUtils.getAppText().kycRequiredInfoTitle2
            tvSecondMessage.text = RemoteConfigUtils.getAppText().kycRequiredInfoMessage2
            ivThirdMessageIcon.setImageResource(R.drawable.ic_purse)
            tvThirdTitle.text = RemoteConfigUtils.getAppText().kycRequiredInfoTitle3
            tvThirdMessage.text = RemoteConfigUtils.getAppText().kycRequiredInfoMessage3
            clFourthMessage.hideView()
        }
    }

    private fun setKycRejectedView() {
        with(binding) {
            svContent.hideView()
            grCta.hideView()
            tvMessage.showView()
            btnUnderstand.showView()
            ivTopBanner.setImageResource(R.drawable.ic_account_warning)
            tvTitle.text = RemoteConfigUtils.getAppText().kycRejectedTitle
            tvTitle.gravity = Gravity.START
            tvMessage.text = if (rejectionReason.isNotNullOrEmpty()) {
                "${rejectionReason}. ${RemoteConfigUtils.getAppText().kycRejectedMessage}"
            } else {
                RemoteConfigUtils.getAppText().kycRejectedMessage
            }
            btnUnderstand.text = getString(R.string.repeat_account_verification)
            btnUnderstand.setOnClickListener {
                dismiss()
                openWeb("${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}?entryPoint=$entryPoint")
            }
        }
    }

    private fun setKybRequiredView() {
        with(binding) {
            if (!RemoteConfigUtils.getPaymentConfigs().enablePaymentKyb.isTrue) {
                tvTitle.textHTML(RemoteConfigUtils.getAppText().kycSupremeRequiredTitle)
                tvMessage.apply {
                    text = RemoteConfigUtils.getAppText().kycSupremeRequiredMessage?.let {
                        Utilities.makeSectionOfTextClickable(
                            it,
                            context.getString(R.string.learn_more),
                            object : ClickableSpan() {
                                override fun onClick(widget: View) {
                                    Utilities.launchBrowser(
                                        context,
                                        RemoteConfigUtils.getPaymentConfigs().kycTierInfoUrl
                                    )
                                    dismiss()
                                }

                                override fun updateDrawState(ds: TextPaint) {
                                    super.updateDrawState(ds)
                                    ds.isUnderlineText = false
                                    ds.color = context.getColorCompat(R.color.blue_60)
                                }
                            })
                    }
                    movementMethod = LinkMovementMethod.getInstance()
                    visibility =
                        RemoteConfigUtils.getAppText().kycSupremeRequiredMessage.isNotNullOrBlank()
                            .asVisibility()
                }
                btnUnderstand.showView()
                svContent.hideView()
                tvLearnMore.hideView()
                grCta.hideView()
                btnUnderstand.setOnClickListener { dismiss() }
            } else {
                tvMessage.hideView()
                btnUnderstand.hideView()
                ivTopBanner.setImageResource(R.drawable.kyb_upgrade_banner)
                tvTitle.text = RemoteConfigUtils.getAppText().kybRequiredTitle
                ivFirstMessageIcon.setImageResource(R.drawable.ic_smartphone)
                tvFirstTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle1
                tvFirstMessage.text = RemoteConfigUtils.getAppText().kybRequiredInfoMessage1
                ivSecondMessageIcon.setImageResource(R.drawable.ic_qr)
                tvSecondTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle2
                tvSecondMessage.text =
                    RemoteConfigUtils.getAppText().kybRequiredInfoMessage2
                ivThirdMessageIcon.setImageResource(R.drawable.ic_coins)
                tvThirdTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle3
                tvThirdMessage.text = RemoteConfigUtils.getAppText().kybRequiredInfoMessage3
                ivFourthMessageIcon.setImageResource(R.drawable.ic_purse)
                tvFourthTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle4
                tvFourthMessage.text =
                    RemoteConfigUtils.getAppText().kybRequiredInfoMessage4
            }
        }
    }

    private fun setKybPendingView() {
        with(binding) {
            svContent.hideView()
            grCta.hideView()
            ivTopBanner.hideView()
            tvMessage.showView()
            btnUnderstand.showView()
            tvTitle.text = RemoteConfigUtils.getAppText().kybInProgressTitle
            tvTitle.gravity = Gravity.START
            tvMessage.text = RemoteConfigUtils.getAppText().kybInProgressMessage
            btnUnderstand.setOnClickListener { dismiss() }
        }
    }

    private fun setKybSubmittedView() {
        with(binding) {
            svContent.hideView()
            grCta.hideView()
            vwCloseBar.hideView()
            ivClose.showView()
            tvMessage.showView()
            btnUnderstand.hideView()
            ivTopBanner.setImageResource(R.drawable.ic_processing)
            tvTitle.text = RemoteConfigUtils.getAppText().kybSubmittedTitle
            tvMessage.text = RemoteConfigUtils.getAppText().kybSubmittedMessage
            val padding36dp = resources.getDimension(R.dimen._36dp).toInt()
            tvMessage.setPadding(0, 0, 0, padding36dp)
        }
    }

    private fun setKybVerifiedView() {
        with(binding) {
            tvMessage.hideView()
            btnUnderstand.hideView()
            grCta.hideView()
            ivClose.showView()
            vwCloseBar.hideView()
            ivTopBanner.setImageResource(R.drawable.banner_priority_badge)
            tvTitle.text = RemoteConfigUtils.getAppText().kybVerifiedTitle
            tvTitle.gravity = Gravity.START
            ivFirstMessageIcon.setImageResource(R.drawable.ic_smartphone)
            tvFirstTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle1
            tvFirstMessage.text = RemoteConfigUtils.getAppText().kybRequiredInfoMessage1
            ivSecondMessageIcon.setImageResource(R.drawable.ic_qr)
            tvSecondTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle2
            tvSecondMessage.text = RemoteConfigUtils.getAppText().kybRequiredInfoMessage2
            ivThirdMessageIcon.setImageResource(R.drawable.ic_coins)
            tvThirdTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle3
            tvThirdMessage.text = RemoteConfigUtils.getAppText().kybRequiredInfoMessage3
            ivFourthMessageIcon.setImageResource(R.drawable.ic_purse)
            tvFourthTitle.text = RemoteConfigUtils.getAppText().kybRequiredInfoTitle4
            tvFourthMessage.text = RemoteConfigUtils.getAppText().kybRequiredInfoMessage4
        }
    }

    private fun setKybRejectedView() {
        with(binding) {
            svContent.hideView()
            grCta.hideView()
            tvMessage.showView()
            btnUnderstand.showView()
            ivTopBanner.setImageResource(R.drawable.ic_shop_warning)
            tvTitle.text = RemoteConfigUtils.getAppText().kybRejectedTitle
            tvTitle.gravity = Gravity.START
            tvMessage.text = if (rejectionReason.isNotNullOrEmpty()) {
                "${rejectionReason}. ${RemoteConfigUtils.getAppText().kybRejectedMessage}"
            } else {
                RemoteConfigUtils.getAppText().kybRejectedMessage
            }
            btnUnderstand.text = getString(R.string.repeat_store_verification)
            btnUnderstand.setOnClickListener {
                dismiss()
                openWeb("${RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl}?from=Payment&entryPoint=$entryPoint")
            }
        }
    }
}
