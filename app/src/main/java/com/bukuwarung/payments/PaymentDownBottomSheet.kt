package com.bukuwarung.payments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.isTrue
import kotlinx.android.synthetic.main.payment_down_bottom_sheet.view.btn_payment_down
import kotlinx.android.synthetic.main.payment_down_bottom_sheet.view.iv_close
import kotlinx.android.synthetic.main.payment_down_bottom_sheet.view.iv_ic_payment_down
import kotlinx.android.synthetic.main.payment_down_bottom_sheet.view.tv_payment_down_body
import kotlinx.android.synthetic.main.payment_down_bottom_sheet.view.tv_payment_down_title

class PaymentDownBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        private const val IS_SERVICE_DOWN = "isServiceDown"
        private const val MESSAGE = "message"
        private const val HIDE_CLOSE_BUTTON = "hideCloseButton"
        const val TAG = "PaymentDownBottomSheet"
        fun createInstance(isServiceDown: Boolean, message: String?, hideCloseButton: Boolean = false): PaymentDownBottomSheet {
            val bottomSheet = PaymentDownBottomSheet()
            val bundle = Bundle()
            bundle.putBoolean(IS_SERVICE_DOWN, isServiceDown)
            bundle.putString(MESSAGE, message)
            bundle.putBoolean(HIDE_CLOSE_BUTTON, hideCloseButton)
            bottomSheet.arguments = bundle
            return bottomSheet
        }
    }

    interface PaymentDownBsListener {
        fun onButtonClicked()
    }

    private val isServiceDown by lazy {
        arguments?.getBoolean(IS_SERVICE_DOWN) ?: false
    }
    private val message by lazy {
        arguments?.getString(MESSAGE)
    }

    private val hideCloseButton by lazy { arguments?.getBoolean(HIDE_CLOSE_BUTTON).isTrue }

    private var listener: PaymentDownBsListener? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        val view: View = inflater.inflate(R.layout.payment_down_bottom_sheet, container, false)

        view.iv_close.setOnClickListener {
            dismiss()
        }
        view.iv_close.visibility = hideCloseButton.not().asVisibility()
        if (isServiceDown) {
            view.iv_ic_payment_down.setImageResource(R.drawable.ic_server_busy)
            view.tv_payment_down_title.setText(R.string.disturbance_message)
            view.tv_payment_down_body.text =
                if (message.isNullOrBlank()) getString(R.string.try_later) else message
            view.btn_payment_down.setText(R.string.back)
            view.btn_payment_down.setOnClickListener {
                dismiss()
                listener?.onButtonClicked()
            }
        } else {
            view.btn_payment_down.setOnClickListener {
                dismiss()
                listener?.onButtonClicked()
            }
        }
        return view
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? PaymentDownBsListener }
        if (context is PaymentDownBsListener) listener = context
    }
}
