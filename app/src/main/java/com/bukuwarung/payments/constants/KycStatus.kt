package com.bukuwarung.payments.constants

enum class KycStatus {
    INITIAL, PENDING_VERIFICATION, PENDING_MANUAL_VERIFICATION, MANUALLY_VERIFIED, VERIFIED, REJECTED
}

fun KycStatus?.isVerified(): Boolean = this == KycStatus.MANUALLY_VERIFIED || this == KycStatus.VERIFIED
fun KycStatus?.isPending(): Bo<PERSON>an = this == KycStatus.PENDING_MANUAL_VERIFICATION || this == KycStatus.PENDING_VERIFICATION
