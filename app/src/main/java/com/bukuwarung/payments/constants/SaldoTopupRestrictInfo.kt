package com.bukuwarung.payments.constants

import com.google.gson.annotations.SerializedName

data class SaldoTopupRestrictInfo(
    @SerializedName("start_time") val startTime: String? = DEFAULT_START_TIME,
    @SerializedName("end_time") val endTime: String? = DEFAULT_END_TIME,
    @SerializedName("dialog_description") val dialogDescription: String? = DEFAULT_DIALOG_DESCRIPTION
) {
    companion object {
        private const val DEFAULT_START_TIME = "2025-06-30T18:00:00"
        private const val DEFAULT_END_TIME = "2025-07-31T18:00:00"
        private const val DEFAULT_DIALOG_DESCRIPTION =
            "Mulai 01 Agustus 2025, fitur top up saldo hanya bisa diakses oleh Juragan yang sudah verifikasi toko. <PERSON><PERSON> belum, kamu masih bisa pakai saldo yang tersisa untuk beli Produk Digital."
    }
}
