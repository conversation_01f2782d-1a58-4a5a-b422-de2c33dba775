package com.bukuwarung.payments

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.observe
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.payment.PaymentTabViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.PAYMENT_NEW_USER_BOTTOMSHEET
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.banklist.BankAccountListActivity
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.WhatsAppUtils
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.showView
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetBehavior
import dagger.android.support.AndroidSupportInjection
import kotlinx.android.synthetic.main.payment_tutorial_bottom_sheet.view.*
import kotlinx.android.synthetic.main.select_bank_account_layout.view.*
import javax.inject.Inject


class PaymentTutorialBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "PaymentTutorialBottomSheet"
        private const val IS_FROM_LANDING = "is_from_landing"

        fun createInstance(isFromPaymentLandingPage: Boolean = false) =
            PaymentTutorialBottomSheet().apply {
                arguments = Bundle().apply { putBoolean(IS_FROM_LANDING, isFromPaymentLandingPage) }
            }
    }

    @Inject
    lateinit var viewModel: PaymentTabViewModel

    private var isFromPaymentLandingPage: Boolean = false
    private var callback: Callback? = null

    interface Callback {
        fun showCoachMark(reShowCoachMark: Boolean)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        AndroidSupportInjection.inject(this)
        parentFragment?.let { callback = it as? Callback }
        if (context is Callback) callback = context
    }

    private fun dismissDialogAndCoachmark() {
        callback?.showCoachMark(false)
        dismiss()
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        callback?.showCoachMark(true)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val displayMetrics = requireActivity().resources.displayMetrics
            val height = displayMetrics.heightPixels

            val maxHeight = (height * 0.88).toInt()
            val mBehavior: BottomSheetBehavior<*> = BottomSheetBehavior.from(requireView().parent as View)
            mBehavior.peekHeight = maxHeight
        }
        return dialog
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val view: View = inflater.inflate(R.layout.payment_tutorial_bottom_sheet, container, false)
        val titleText = RemoteConfigUtils.getAppText().paymentTutorialTitle
        val title = view.findViewById<TextView>(R.id.tv_secure_info)
        title?.text = titleText
        isFromPaymentLandingPage = arguments?.getBoolean(IS_FROM_LANDING).isTrue
        viewModel.checkBankAccounts()
        viewModel.selectedBankAccount.observe(this) { bankAccount ->
            setBankView(view, bankAccount?.lastOrNull())
        }
        view.cl_bottom_layout.visibility = (!isFromPaymentLandingPage).asVisibility()
        view.btn_ok.visibility = (isFromPaymentLandingPage).asVisibility()
        view.btn_ok.setOnClickListener {
            dismiss()
            callback?.showCoachMark(true)
        }
        view.check_video.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_TUTORIAL_OPEN, AppAnalytics.PropBuilder().put(AnalyticsConst.TYPE, AnalyticsConst.VIDEO), true, false, false)
            startActivity(WebviewActivity.createIntent(activity, PaymentConst.PAYMENT_VIDEO_TUTORIAL_URL, ""))
            dismissDialogAndCoachmark()
        }
        view.btn_help.setOnClickListener {
            val bundle = Bundle()
            bundle.putString(AnalyticsConst.ENTRY_POINT,AnalyticsConst.PAYMENTS)
            WhatsAppUtils.openWABotWithHelpText(context, getString(R.string.buku_need_help_message),bundle)
            dismissDialogAndCoachmark()
        }
        view.bank_detail_payment_in.label_account_txt.visibility = View.GONE
        return view
    }

    private fun setBankView(view: View, bankAccount: BankAccount?) {
        val bookId = SessionManager.getInstance().businessId
        view.bank_details_layout.showView()
        bankAccount?.let {
            view.bank_detail_payment_in.enterAccountMessage.visibility = View.GONE
            view.bank_detail_payment_in.bank_account_group.visibility = View.VISIBLE
            view.bank_detail_payment_in.button_change_account.visibility = View.VISIBLE
            view.bank_detail_payment_in.button_change_account.setOnClickListener {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_EDIT_USER_BANK, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, if (isFromPaymentLandingPage) PAYMENT_NEW_USER_BOTTOMSHEET else AnalyticsConst.PEMBAYARAN_TUTORIAL), true, false, false)
                val i = BankAccountListActivity.createIntent(requireActivity(), PaymentConst.TYPE_PAYMENT_IN.toString(), bookId, AnalyticsConst.PAYMENT_TUTORIAL_DIRECT_EXISTING, isSelfOnly = "true")
                startActivity(i)
                dismissDialogAndCoachmark()
            }
            Glide.with(requireContext())
                    .load(bankAccount.getBankLogoIfAvailable())
                    .placeholder(R.drawable.ic_bank)
                    .error(R.drawable.ic_bank)
                    .into(view.bank_detail_payment_in.image_bank)
            view.bank_detail_payment_in.txt_bank_title.text = it.bankName
            view.bank_detail_payment_in.txt_account_number.text = it.accountNumber
        } ?:run  {
            view.bank_detail_payment_in.bank_account_group.visibility = View.GONE
            view.bank_detail_payment_in.setOnClickListener {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, if (isFromPaymentLandingPage) PAYMENT_NEW_USER_BOTTOMSHEET else AnalyticsConst.PEMBAYARAN_TUTORIAL), true, false, false)
                val i = AddBankAccountActivity.createIntent(requireActivity(), PaymentConst.TYPE_PAYMENT_IN.toString(), bookId, AnalyticsConst.PAYMENT_TUTORIAL_DIRECT_FIRST, hasBankAccount = "false", showTutorial = "true")
                startActivity(i)
                dismissDialogAndCoachmark()
            }
        }
    }
}