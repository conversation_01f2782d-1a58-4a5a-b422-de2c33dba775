package com.bukuwarung.payments.selectbank

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.data.model.PaymentMethodsResponse
import com.bukuwarung.session.SessionManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SelectPayMethodViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase
) : BaseViewModel() {

    private var failedApiState = -1
    private var paymentMethodRes: PaymentMethodsResponse? = null
    private fun currentViewState(): ViewState = viewState.value!!
    private val _viewState: MutableLiveData<ViewState> = MutableLiveData<ViewState>(ViewState())
    val viewState: LiveData<ViewState> = _viewState

    companion object {
        // constant for last failed api call for retry purpose
        private const val API_PAYMENT_METHODS = 0
    }

    data class ViewState(
        val showLoading: Boolean = false,
    )

    sealed class Event {
        data class ShowLoading(val show: Boolean) : Event()
        data class ShowCloseButton(val show: Boolean) : Event()
        object ClearSearch : Event()

        data class PaymentMethodsData(val paymentMethods: PaymentMethodsResponse) : Event()

        // ApiErrors are 3xx, 4xx and 5xx responses
        data class ApiError(val code: String, val message: String?) : Event()

        // ServerError is when server is unreachable, it comes as ApiErrorResponse
        // Can be due to service being down or no internet
        data class ConnectionError(val message: String?) : Event()
    }

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private var vaBank: Boolean = false

    private var originalBank = emptyList<Bank>()
    val banks = MutableLiveData<List<Bank>>()

    fun getPaymentMethods(paymentType: String, bankCode: String, amount: Double) =
        viewModelScope.launch {
            _viewState.value = currentViewState().copy(showLoading = true)
            withContext(Dispatchers.IO) {
                val res = paymentUseCase.getPaymentMethods(
                    SessionManager.getInstance().businessId, paymentType,
                    bankCode, amount
                )
                when (res) {
                    is ApiSuccessResponse -> {
                        paymentMethodRes = res.body
                        withContext(Dispatchers.Main) {
                            eventStatus.value = Event.PaymentMethodsData(res.body)
                            _viewState.value = currentViewState().copy(showLoading = false)
                        }
                    }
                    is ApiErrorResponse -> {
                        handleErrorResponse(res.code, res.errorMessage, API_PAYMENT_METHODS)
                    }
                    else -> {
                    }
                }
            }
        }

    private suspend fun handleErrorResponse(code: String, message: String = "", failedApi: Int) {
        withContext(Dispatchers.Main) {
            val isApiError =
                message.isNotBlank() && message != AppConst.NO_INTERNET_ERROR_MESSAGE
            if (isApiError) {
                eventStatus.value = Event.ApiError(code, message)
            } else {
                eventStatus.value = Event.ConnectionError(message)
            }
            _viewState.value = currentViewState().copy(showLoading = false)
            failedApiState = failedApi
        }
    }

    fun onCancelClicked() {
        eventStatus.value = Event.ClearSearch
    }

    fun setQuery(query: String) {
        if (query.isEmpty()) banks.value = originalBank
        else banks.value = originalBank.filter {
            query.isBlank() || it.bankName.lowercase().contains(query.lowercase())
        }
        eventStatus.value = Event.ShowCloseButton(query.isNotEmpty())
    }

    fun setVaBank(vaBank: Boolean, type: String?) = viewModelScope.launch {
        <EMAIL> = vaBank
        withContext(Dispatchers.Main) {
            eventStatus.value = Event.ShowLoading(true)
        }
        withContext(Dispatchers.IO) {
            val response =
                if (vaBank) paymentUseCase.getVirtualAccountBanks() else paymentUseCase.getBanks(
                    type
                )
            if (response is ApiSuccessResponse)
                originalBank = response.body
            else if (response is ApiErrorResponse && type == null)
                originalBank = paymentUseCase.getBanksLocal()
        }
        withContext(Dispatchers.Main) {
            eventStatus.value = Event.ShowLoading(false)
            banks.value = originalBank
        }
    }
}

