package com.bukuwarung.payments.core.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.R
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.TYPE_PAYMENT_OUT
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.customer.CustomerUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.data.model.PaymentCategoryItem
import com.bukuwarung.payments.data.model.PaymentTransactionLimits
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class PaymentInputViewModel @Inject constructor(
    val paymentUseCase: PaymentUseCase,
    val businessUseCase: BusinessUseCase,
    val customerUseCase: CustomerUseCase
) : BaseViewModel() {

    var paymentOutCategories: List<PaymentCategoryItem>? = null
    var paymentOutCategory: PaymentCategoryItem? = null
        private set
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    var paymentOutLimits: PaymentTransactionLimits? = null
        private set

    sealed class Event {
        data class ShowCategories(val categories: List<PaymentCategoryItem>) : Event()
        data class SetSelectedCategory(val category: PaymentCategoryItem) : Event()
        data class ShowToast(
            val message: String? = null,
            val stringId: Int = 0,
            val successAddFav: Boolean = false,
            val successRemoveFav: Boolean = false
        ) : Event()
    }

    fun setPaymentCategory(paymentCategory: PaymentCategoryItem) {
        this.paymentOutCategory = paymentCategory
        PaymentPrefManager.getInstance()
            .saveFilledData(category = paymentCategory)
    }

    fun fetchCategories() = viewModelScope.launch {
        if (paymentOutCategories != null) {
            eventStatus.value = Event.ShowCategories(paymentOutCategories!!)
        } else {
            withContext(Dispatchers.IO) {
                val disbursableType = PaymentConst.DisbursementRequest
                when (val result = paymentUseCase.getPaymentCategoryList(disbursableType)) {
                    is ApiSuccessResponse -> {
                        withContext(Dispatchers.Main) {
                            val categoryListWithUniqueInfo = mutableListOf<PaymentCategoryItem>()
                            result.body.forEach { category ->
                                category.categoryList.forEach { item ->
                                    if (categoryListWithUniqueInfo.firstOrNull { it.name == item.name } == null) {
                                        categoryListWithUniqueInfo += item
                                    }
                                }
                            }
                            categoryListWithUniqueInfo.sortBy { it.priority }
                            paymentOutCategories = categoryListWithUniqueInfo
                            eventStatus.value = Event.ShowCategories(categoryListWithUniqueInfo)
                            autoAssignCategory(categoryListWithUniqueInfo)
                        }
                    }
                    is ApiErrorResponse -> {}
                    else -> {}
                }
            }
        }
    }

    private fun autoAssignCategory(categories: List<PaymentCategoryItem>) {
        val key = PaymentUtils.getKeyForSelectedCategoryId(TYPE_PAYMENT_OUT)
        val prevCategoryId = PaymentPrefManager.getInstance().getSelectedPaymentCategoryId(key)
        val prevCategory = categories.firstOrNull { it.paymentCategoryId == prevCategoryId }
        val selectCategory = when {
            prevCategory != null -> prevCategory
            else -> categories[0]
        }
        setPaymentCategory(selectCategory)
        eventStatus.value = Event.SetSelectedCategory(selectCategory)
    }

    fun addFavourite(id: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.addFavourite(
                SessionManager.getInstance().businessId,
                id
            )) {
                is ApiSuccessResponse -> {
                    withContext(Dispatchers.Main) {
                        if (response.body.success.isTrue) {
                            eventStatus.value =
                                Event.ShowToast(
                                    stringId = R.string.success_fav_message, successAddFav = true
                                )
                        } else {
                            eventStatus.value = Event.ShowToast(
                                response.body.message
                            )
                        }
                    }
                }
                is ApiErrorResponse -> {
                    withContext(Dispatchers.Main) {
                        eventStatus.value = Event.ShowToast(response.errorMessage)
                    }
                }
                else -> {}
            }

        }
    }

    fun deleteFavourite(id: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.removeFavourite(
                SessionManager.getInstance().businessId,
                id
            )) {
                is ApiSuccessResponse -> {
                    withContext(Dispatchers.Main) {
                        if (response.body.success.isTrue) {
                            eventStatus.value =
                                Event.ShowToast(
                                    stringId = R.string.remove_fav_message, successRemoveFav = true
                                )
                        } else {
                            eventStatus.value = Event.ShowToast(
                                response.body.message
                            )
                        }
                    }
                }
                is ApiErrorResponse -> {
                    withContext(Dispatchers.Main) {
                        eventStatus.value = Event.ShowToast(response.errorMessage)
                    }
                }
                else -> {}
            }

        }
    }

    fun fetchLimits(customerId: String?) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            if (paymentOutLimits != null) {
                return@withContext
            }
            val result = paymentUseCase.getPaymentOutLimits(
                SessionManager.getInstance().businessId,
                customerId.orEmpty()
            )
            if (result is ApiSuccessResponse) {
                paymentOutLimits = result.body
            }
        }
    }

    fun isBelowThreshold(amount: Long) =
        amount.toDouble() < RemoteConfigUtils.getMinimumPaymentOutAmount()

    fun isAboveThreshold(amount: Long): Boolean {
        val limits = paymentOutLimits
        if (limits?.remainingDailyTrxLimit == null || limits.perTrxLimit == null) return false
        return (amount > limits.remainingDailyTrxLimit.orNil) || (amount > limits.perTrxLimit.orNil) || (limits.whitelistLimits != null && amount > limits.whitelistLimits.remainingTrxAmountLimit.orNil)
    }

    fun checkDisableButton(amount: Long): Boolean {
        return isAboveThreshold(amount) || isBelowThreshold(amount) || RemoteConfigUtils.isPaymentCategoryMandatory() && paymentOutCategory == null
    }

}