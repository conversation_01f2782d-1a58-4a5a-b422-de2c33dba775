package com.bukuwarung.payments.core.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.Gravity
import android.view.WindowManager
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.ADVERTISEMENT
import com.bukuwarung.constants.PaymentConst.SALDO
import com.bukuwarung.databinding.ActivityPaymentConfirmationBinding
import com.bukuwarung.di.ViewModelFactory
import com.bukuwarung.game.viewmodel.GameViewModel
import com.bukuwarung.payments.bottomsheet.LoyaltyTierDiscountsBottomSheet
import com.bukuwarung.payments.bottomsheet.PlatformFeeBottomSheet
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.payments.core.model.BankAccountDetail
import com.bukuwarung.payments.core.viewmodel.PaymentConfirmationViewModel
import com.bukuwarung.payments.data.model.FinproPaymentMethod
import com.bukuwarung.payments.data.model.PaymentCategoryItem
import com.bukuwarung.payments.data.model.PaymentExitIntentData
import com.bukuwarung.payments.data.model.PaymentMethod
import com.bukuwarung.payments.mapper.toFinproPaymentMethod
import com.bukuwarung.payments.pin.NewPaymentPinActivity
import com.bukuwarung.payments.ppob.confirmation.view.PaymentMethodBottomSheet
import com.bukuwarung.payments.ppob.confirmation.viewmodel.PaymentMethodViewModel
import com.bukuwarung.payments.utils.PaymentNotePreference
import com.bukuwarung.payments.widget.ErrorBottomSheet
import com.bukuwarung.payments.widget.PaymentDetailView
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.getDrawableCompat
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.setDrawable
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.setToolbar
import com.bukuwarung.utils.showView
import com.bumptech.glide.Glide
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject

class PaymentConfirmationPageActivity : BaseActivity(), PaymentDetailView.Callback,
    PaymentMethodBottomSheet.PpobBankAccountsBsListener, ErrorBottomSheet.Callback, PlatformFeeBottomSheet.Callback {
    private lateinit var binding: ActivityPaymentConfirmationBinding

    @Inject
    lateinit var gameViewModel: GameViewModel

    @Inject
    lateinit var viewModel: PaymentConfirmationViewModel
    private lateinit var paymentMethodViewModel: PaymentMethodViewModel

    @Inject
    internal lateinit var viewModelFactory: ViewModelFactory<PaymentMethodViewModel>

    @Inject
    lateinit var paymentNotePreference: PaymentNotePreference
    private val amount by lazy { intent?.getLongExtra(AMOUNT, 0L) }
    private val bankAccountDetail by lazy { intent?.getParcelableExtra(BANK_ACCOUNT_DETAIL) as? BankAccountDetail }
    private val paymentCategory by lazy { intent?.getParcelableExtra(PAYMENT_CATEGORY) as? PaymentCategoryItem }
    private val notes by lazy { intent?.getStringExtra(NOTES).orEmpty() }
    private var retryApi: String = ""

    enum class ApiErrorType {
        PAYMENT_METHOD, OVERVIEW, HEALTH, DISBURSEMENT
    }

    companion object {
        private const val AMOUNT = "amount"
        private const val BANK_ACCOUNT_DETAIL = "bank_account_detail"
        private const val PAYMENT_CATEGORY = "payment_category"
        private const val NOTES = "notes"
        private const val discountFee = "{discount_fee}"

        fun createIntent(
            context: Context,
            amount: Long,
            bankAccountDetail: BankAccountDetail,
            paymentCategory: PaymentCategoryItem?,
            notes: String = ""
        ): Intent {
            return Intent(context, PaymentConfirmationPageActivity::class.java).apply {
                putExtra(BANK_ACCOUNT_DETAIL, bankAccountDetail)
                putExtra(AMOUNT, amount)
                putExtra(PAYMENT_CATEGORY, paymentCategory)
                putExtra(NOTES, notes)
            }
        }
    }

    override fun setViewBinding() {
        binding = ActivityPaymentConfirmationBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        gameViewModel.getGameProgressData(PaymentConst.GAME_PAYMENT_OUT_SUBSCRIPTION_MODEL)
        FeaturePrefManager.getInstance().setExitWithoutCompletingPayment(
            true,
            PaymentConst.TYPE_PAYMENT_OUT,
            PaymentExitIntentData(null, bankAccountDetail?.customerId, bankAccountDetail?.accountId)
        )
        paymentMethodViewModel =
            ViewModelProvider(this@PaymentConfirmationPageActivity, viewModelFactory).get(
                PaymentMethodViewModel::class.java
            )
        setToolbar()
        viewModel.init(bankAccountDetail, amount.orNil, paymentCategory)
        viewModel.onNoteChanged(notes)
        binding.etInputNote.setText(notes)
        setInitialView()
        paymentMethodViewModel.getSaldoBalance(
            PaymentConst.TYPE_PAY_OUT,
            bankAccountDetail?.bankDetail?.code.orEmpty(),
            amount?.toDouble().orNil
        )
    }

    private fun setInitialView() {
        binding.pdvView.setView(
            amount?.toDouble(),
            bankAccountDetail,
            this@PaymentConfirmationPageActivity
        )
        with(binding.includePaymentMethod) {
            includeDefaultPaymentMethod.root.showView()
            gpChangePaymentMethod.hideView()
            progressBar.hideView()
            tvChange.setOnClickListener {
                openPaymentMethodBottomSheet()
            }
            includeDefaultPaymentMethod.tvSelect.setOnClickListener {
                openPaymentMethodBottomSheet()
            }
            with(btnCompletePayment) {
                text = getString(R.string.bayar)
                isEnabled = false
                setSingleClickListener {
                    bankAccountDetail?.let { bankAccountDetail ->
                        if (viewModel.getSelectedPaymentMethodCode()
                                .equals(SALDO, true)
                        ) {
                            openSaldoPin()
                        } else {
                            viewModel.createPaymentOutDisbursement(
                                paymentCategory?.paymentCategoryId.orEmpty(),
                                bankAccountDetail
                            )
                        }
                    }
                }
            }
        }
        initNoteAdapter(paymentNotePreference.noteList)
    }

    private fun setToolbar() {
        binding.includeToolBar.setToolbar(
            this@PaymentConfirmationPageActivity,
            getString(R.string.payment_confirmation),
            RemoteConfigUtils.getPaymentConfigs().supportUrls.payments
        ) {
            InputUtils.hideKeyBoardWithCheck(this@PaymentConfirmationPageActivity)
            onBackPressedDispatcher.onBackPressed()
        }
    }


    private fun callPaymentOutOverviewAPI(paymentMethodCode: String) {
        amount?.let {
            viewModel.callPaymentOutOverviewAPI(
                binding.etInputNote.text.toString(),
                it, bankAccountDetail, paymentMethodCode
            )
        }
    }

    override fun subscribeState() {
        paymentMethodViewModel.observeEvent.observe(this) {
            when (it) {
                is PaymentMethodViewModel.Event.PaymentMethodMapper -> {
                    paymentMethodViewModel.finproGetPaymentMethodsV2Response =
                        it.response.toFinproPaymentMethod(
                            context = this@PaymentConfirmationPageActivity,
                            amount = amount?.toDouble().orNil,
                            saldoBalance = it.saldoBalance,
                            selectedPaymentCode = viewModel.selectedPaymentMethod?.code,
                            isBmsRegistered = it.isBmsRegistered,
                            dailySaldoLimit = it.dailySaldoLimit,
                            monthlySaldoLimit = it.monthlySaldoLimit,
                            isSaldoFreezed = it.isSaldoFreezed
                        )
                    paymentMethodViewModel.dailySaldoLimit = it.dailySaldoLimit
                    paymentMethodViewModel.monthlySaldoLimit = it.monthlySaldoLimit
                    val saldoPaymentMethod =
                        paymentMethodViewModel.finproGetPaymentMethodsV2Response?.highlightedPaymentChannels?.channels?.firstOrNull { it.code == SALDO }
                    val isSaldoAvailable = saldoPaymentMethod?.tag?.tagType == ADVERTISEMENT
                    if (isSaldoAvailable) {
                        setSaldoInfo(saldoPaymentMethod)
                    }
                }
                is PaymentMethodViewModel.Event.ApiError -> {
                    retryApi = ApiErrorType.PAYMENT_METHOD.name
                    showErrorBottomSheet(true, it.message)
                }
                is PaymentMethodViewModel.Event.ConnectionError -> {
                    retryApi = ApiErrorType.PAYMENT_METHOD.name
                    showErrorBottomSheet(false, it.message)
                }
                else -> {}
            }
        }

        viewModel.observeEvent.observe(this) {
            when (it) {
                is PaymentConfirmationViewModel.Event.ShowErrorBottomSheet -> {
                    retryApi = it.name
                    showErrorBottomSheet(it.isServiceDown, it.message)
                }
                is PaymentConfirmationViewModel.Event.ShowToast -> {
                    binding.pdvView.updateAddFav(it.successAddFav, it.successRemoveFav)
                    Toast.makeText(
                        this@PaymentConfirmationPageActivity,
                        it.message ?: getString(it.stringId),
                        Toast.LENGTH_LONG
                    ).show()
                }
                is PaymentConfirmationViewModel.Event.OpenWebView -> handlePaymentOutSuccess(
                    it.url, it.paymentTabEnabled
                )
                is PaymentConfirmationViewModel.Event.ShowPaymentStatusScreen -> openPaymentStatus()
                else -> {}
            }
        }

        viewModel.viewState.observe(this) {
            with(binding.includePaymentMethod) {
                it.disbursementLoading?.let {
                    progressBar.visibility = it.asVisibility()
                    btnCompletePayment.isEnabled = (!it)
                    // Show full screen loading
                    binding.includePaymentLoading.root.visibility = it.asVisibility()
                    if (it) {
                        onBackPressedDispatcher.addCallback(
                                this@PaymentConfirmationPageActivity,
                                object : OnBackPressedCallback(true) {
                                    override fun handleOnBackPressed() {
                                        MainActivity.startActivitySingleTopToTab(
                                                this@PaymentConfirmationPageActivity,
                                                TabName.PAYMENT, null
                                        )
                                        finish()
                                    }
                                }
                        )
                    }
                }
            }
        }
        binding.includePaymentLoading.ivClose.setOnClickListener {
            MainActivity.startActivitySingleTopToTab(this, TabName.PAYMENT, null)
            finish()
        }

        viewModel.paymentOutOverviewResponse.observe(this) {
            it?.let { disbursementOverviewResponse ->
                with(binding) {
                    val destinationBankInformation =
                        disbursementOverviewResponse.destinationBankInformation
                    pdvView.onPaymentMethodSelected(disbursementOverviewResponse)
                    showDiscountIfApplicable(disbursementOverviewResponse.discountFeeText.orEmpty())
                    if (destinationBankInformation?.flag == PaymentConst.BANK_DOWN_TIME && destinationBankInformation.message.isNotNullOrBlank()) {
                        setDownTimeBank(destinationBankInformation.message.orEmpty())
                    }
                }
                Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_PAYMENT_SEND_SELECT_PAYMENT_METHOD, "payment_confirmation")
            }
        }
        viewModel.healthStatus.observe(this) { healthState ->
            with(binding) {
                when (healthState?.status) {
                    PaymentConfirmationViewModel.HEALTH_OK -> {
                        baHealthWarning.hideView()
                        includePaymentMethod.btnCompletePayment.isEnabled = true
                    }
                    PaymentConfirmationViewModel.HEALTH_WARNING -> {
                        with(baHealthWarning) {
                            setBackgroundType("information")
                            addText(getString(R.string.payment_outside_operational_msg))
                            showView()
                        }
                        includePaymentMethod.btnCompletePayment.isEnabled = true
                    }
                    PaymentConfirmationViewModel.HEALTH_ERROR -> {
                        with(baHealthWarning) {
                            setBackgroundType("red_with_cross")
                            addText(healthState.message)
                            showView()
                        }
                        includePaymentMethod.btnCompletePayment.isEnabled = false
                    }
                    else -> {}
                }
            }
        }
    }

    override fun addOrRemoveFavourite(addFav: Boolean, bankAccountDetail: BankAccountDetail?) {
        if (addFav) {
            viewModel.addFavourite(bankAccountDetail?.accountDetail?.id.orEmpty())
        } else {
            viewModel.deleteFavourite(bankAccountDetail?.accountDetail?.id.orEmpty())
        }
    }

    override fun openPaymentMethodBottomSheet() {
        val bs = PaymentMethodBottomSheet.createInstance(
            type = PaymentConst.TYPE_PAY_OUT,
            bankCode = bankAccountDetail?.bankDetail?.code,
            amount = amount?.toDouble().orNil,
            selectedPaymentMethodCode = viewModel.selectedPaymentMethod?.code
        )
        bs.show(supportFragmentManager, PaymentMethodBottomSheet.TAG)
    }

    private fun setDownTimeBank(infoText: String) {
        with(binding) {
            baWarning.addText(infoText)
            baWarning.setInfoViewStyle(
                textColor = getColorCompat(R.color.black_60),
                textStyle = R.style.Body3,
                backgroundType = "warning"
            )
            baWarning.showView()
        }
    }

    private fun initNoteAdapter(noteList: List<String>) {
        val recommendationAdapter: ArrayAdapter<String> =
            ArrayAdapter<String>(this, android.R.layout.simple_list_item_1).apply {
                addAll(noteList)
                notifyDataSetChanged()
                filter.filter("")
            }
        binding.etInputNote.apply {
            setAdapter(recommendationAdapter)
            setOnFocusChangeListener { _, isFocused -> // to show the popup even user hasn't type
                // We need to use post to avoid BadTokenException
                this.post {
                    if (isFocused && !<EMAIL>) {
                        try {
                            showDropDown()
                        } catch (ex: WindowManager.BadTokenException) {
                            FirebaseCrashlytics.getInstance().recordException(ex)
                        }
                    }
                }
            }
            addTextChangedListener {
                val text = it.toString()
                if (text.isNotEmpty()) { // to dismiss the popup after user type the 2nd char
                    dismissDropDown()
                }
                viewModel.onNoteChanged(text)
            }
            setOnItemClickListener { _, _, position, _ ->
                val prop = AppAnalytics.PropBuilder().put(
                    AnalyticsConst.ID, recommendationAdapter.getItem(position).orEmpty()
                )
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_FORM_AUTOCOMPLETE, prop)
            }
        }
    }

    override fun changePaymentMethod(
        finproPaymentMethod: FinproPaymentMethod,
        dailySaldoLimit: Double?,
        monthlySaldoLimit: Double?,
        eventProperty: AppAnalytics.PropBuilder?
    ) {
        viewModel.selectedPaymentMethod = PaymentMethod(
            name = finproPaymentMethod.name,
            code = finproPaymentMethod.code,
            logo = finproPaymentMethod.logo,
            saldoBalance = finproPaymentMethod.details?.saldoBalance,
            dailySaldoLimit = dailySaldoLimit,
            monthlySaldoLimit = monthlySaldoLimit
        )
        callPaymentOutOverviewAPI(finproPaymentMethod.code.orEmpty())
        viewModel.selectedPaymentMethod?.let { handlePaymentMethodData(it) }
    }

    private fun showDiscountIfApplicable(discountFeeText: String) {
        val paymentOutDisbursementOverviewResponse = viewModel.paymentOutOverviewResponse.value
        val discountValue =
            paymentOutDisbursementOverviewResponse?.discountFee.orNil + paymentOutDisbursementOverviewResponse?.loyaltyDiscount?.tierDiscount.orNil + paymentOutDisbursementOverviewResponse?.loyaltyDiscount?.subscriptionDiscount.orNil
        with(binding.includePaymentMethod) {
            if (discountValue > 0.0 && discountFeeText.isNotEmpty()) {
                tvCashbackAmount.showView()
                tvCashbackAmount.setDrawable(0)
                clCashBack.showView()
                tvCashbackAmount.text = discountFeeText.replace(
                    discountFee, Utility.formatAmount(discountValue)
                )
            } else {
                clCashBack.hideView()
            }
        }
    }

    override fun changeUseSaldoReward(useSaldoReward: Boolean) {

    }

    override fun openPlatformFeeInfo() {
        PlatformFeeBottomSheet.createInstance()
            .show(supportFragmentManager, PlatformFeeBottomSheet.TAG)
    }

    override fun openLoyaltyTierInfo() {
        LoyaltyTierDiscountsBottomSheet.createInstance(PaymentConst.Feature.PAYMENT_OUT)
            .show(supportFragmentManager, LoyaltyTierDiscountsBottomSheet.TAG)
    }

    private fun redirectToMainActivity(paymentTabEnabled: Boolean) {
        MainActivity.startActivitySingleTopToTab(
            this,
            if (paymentTabEnabled) TabName.PAYMENT else TabName.OTHERS
        )
    }

    private fun handlePaymentOutSuccess(url: String, paymentTabEnabled: Boolean) {
        redirectToMainActivity(paymentTabEnabled)
        startActivity(WebviewActivity.createIntent(this, url, ""))
    }

    private fun openSaldoPin() {
        startPaymentPinActivityForResult.launch(
            NewPaymentPinActivity.createIntent(
                this@PaymentConfirmationPageActivity,
                PinType.PIN_CONFIRM.toString()
            )
        )
    }

    private val startPaymentPinActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.createPaymentOutDisbursement(
                    paymentCategory?.paymentCategoryId.orEmpty(),
                    bankAccountDetail
                )
            }
        }

    private fun openPaymentStatus() {
        finish()
        startActivity(
            PaymentStatusActivity.createIntent(
                this@PaymentConfirmationPageActivity,
                viewModel.disbursementId,
                bankAccountDetail
            )
        )
    }

    private fun handlePaymentMethodData(paymentMethod: PaymentMethod) {
        val saldoPaymentMethod =
            paymentMethodViewModel.finproGetPaymentMethodsV2Response?.highlightedPaymentChannels?.channels?.firstOrNull { it.code == SALDO }
        val isSaldoAvailable = saldoPaymentMethod?.tag?.tagType == ADVERTISEMENT
        with(binding.includePaymentMethod) {
            gpChangePaymentMethod.showView()
            includeDefaultPaymentMethod.root.hideView()
            tvPaymentMethodName.text = paymentMethod.name
            Glide.with(this@PaymentConfirmationPageActivity)
                .load(paymentMethod.logo)
                .placeholder(R.drawable.ic_bank)
                .error(R.drawable.ic_bank)
                .into(ivPayMethodIcon)
            // selected payment method is saldo
            if (paymentMethod.saldoBalance != null && paymentMethod.saldoBalance >= 0.0) {
                tvPaymentAmount.showView()
                includeLayout.clLayout.background =
                    getDrawableCompat(R.drawable.bg_rounded_rectangle_green_5)
                includeLayout.tvMessage.text = getString(R.string.saldo_selected_advertisement)
                includeLayout.tvMessage.gravity = Gravity.CENTER
                includeLayout.tvMessage.setTextColor(getColorCompat(R.color.green_80))
                includeLayout.tvMessage.setDrawable(left = 0)
                includeLayout.btnCheck.hideView()
                tvPaymentAmount.text = " - ${Utility.formatAmount(paymentMethod.saldoBalance)}"
                includeLayout.root.showView()
            } else {
                tvPaymentAmount.text = ""
                tvPaymentAmount.hideView()
                if (isSaldoAvailable) {
                    setSaldoInfo(saldoPaymentMethod)
                } else {
                    includeLayout.root.hideView()
                }
            }
        }
    }

    private fun setSaldoInfo(saldoPaymentMethod: FinproPaymentMethod?) {
        with(binding.includePaymentMethod) {
            includeLayout.root.showView()
            includeLayout.clLayout.setBackgroundColor(getColorCompat(R.color.blue_80))
            includeLayout.tvMessage.setTextAppearance(this@PaymentConfirmationPageActivity, R.style.SubHeading2)
            includeLayout.tvMessage.text = getString(R.string.use_saldo_info)
            includeLayout.tvMessage.setTextColor(getColorCompat(R.color.white))
            includeLayout.tvMessage.setDrawable(left = 0)
            includeLayout.tvMessage.gravity = Gravity.LEFT
            includeLayout.btnCheck.setBackgroundColor(getColorCompat(R.color.white))
            includeLayout.btnCheck.setTextColor(getColorCompat(R.color.blue80))
            includeLayout.btnCheck.showView()
            includeLayout.btnCheck.setSingleClickListener {
                saldoPaymentMethod?.let {
                    changePaymentMethod(
                        it,
                        paymentMethodViewModel.dailySaldoLimit,
                        paymentMethodViewModel.monthlySaldoLimit
                    )
                }
            }
        }
    }

    private fun showErrorBottomSheet(isServiceDown: Boolean, errorMessage: String? = null) {
        with(binding.includePaymentMethod) {
            gpChangePaymentMethod.hideView()
            includeDefaultPaymentMethod.root.showView()
            tvSaldoReward.hideView()
            btnCompletePayment.isEnabled = false
            tvCashbackAmount.hideView()
        }
        binding.pdvView.updateErrorrCase()
        ErrorBottomSheet.createInstance(
            if (isServiceDown) {
                ErrorBottomSheet.Companion.ApiErrorType.API_ERROR
            } else {
                ErrorBottomSheet.Companion.ApiErrorType.CONNECTION_ERROR
            },
            errorMessage?.ifEmpty { getString(R.string.try_again_or_wait) }
        ).show(supportFragmentManager, ErrorBottomSheet.TAG)
    }

    override fun onDismiss() {

    }

    override fun onButtonClicked() {
        when (retryApi) {
            ApiErrorType.OVERVIEW.name -> {
                callPaymentOutOverviewAPI(viewModel.selectedPaymentMethod?.code.orEmpty())
            }
            ApiErrorType.PAYMENT_METHOD.name -> {
                paymentMethodViewModel.getSaldoBalance(
                    PaymentConst.TYPE_PAY_OUT,
                    bankAccountDetail?.bankDetail?.code.orEmpty(),
                    amount?.toDouble().orNil
                )
            }
            ApiErrorType.HEALTH.name -> {
                bankAccountDetail?.let { viewModel.doHealthCheck(it) }
            }
            ApiErrorType.DISBURSEMENT.name -> {
                viewModel.createPaymentOutDisbursement(
                    paymentCategory?.paymentCategoryId.orEmpty(),
                    bankAccountDetail
                )
            }
            else -> {}
        }
    }

    override fun understoodFeeInfo() {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_FEE_INFO_PAGE_CLICK,
            AppAnalytics.PropBuilder().apply {
                put(
                    AnalyticsConst.ENTRY_POINT,
                    AnalyticsConst.PAYMENT_SEND
                )
                put(
                    AnalyticsConst.TYPE,
                    AnalyticsConst.UNDERSTAND
                )
            }
        )
    }

    override fun openPlatformFeeAndCashbackInfo() {
        startActivity(
            WebviewActivity.createIntent(this, PaymentConst.ABOUT_PAYMENT_CHARGING_URL, "")
        )
    }
}