package com.bukuwarung.payments.core.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.customer.CustomerUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.domain.payments.RiskUseCase
import com.bukuwarung.payments.core.model.AccountDetail
import com.bukuwarung.payments.core.model.BankAccountDetail
import com.bukuwarung.payments.core.model.BankDetail
import com.bukuwarung.session.SessionManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

open class PaymentOutViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val riskUseCase: RiskUseCase,
    private val businessUseCase: BusinessUseCase,
) : PaymentViewModel(paymentUseCase, riskUseCase, businessUseCase) {

    private val _bankAccountDetail = MutableLiveData<BankAccountDetail>()
    val bankAccountDetail: LiveData<BankAccountDetail> = _bankAccountDetail

    fun getCustomerBankAccounts(customerId: String?) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val result = paymentUseCase.getCustomerBankAccounts(
                SessionManager.getInstance().businessId,
                customerId.orEmpty()
            )) {
                is ApiSuccessResponse -> {
                    withContext(Dispatchers.Main) {
                        val bankAccount = result.body.getOrNull(0)
                        _bankAccountDetail.value = BankAccountDetail(
                            accountId = SessionManager.getInstance().businessId,
                            accountDetail = AccountDetail(
                                id = bankAccount?.bankAccountId,
                                maskedNumber = bankAccount?.accountNumber,
                                holderName = bankAccount?.accountHolderName
                            ),
                            bankDetail = BankDetail(
                                bankAccount?.bankCode,
                                bankAccount?.bankName,
                                bankAccount?.getBankLogoIfAvailable()
                            ),
                            customerId = bankAccount?.customerId
                        )
                    }
                }
                else -> {}
            }
        }
    }

}