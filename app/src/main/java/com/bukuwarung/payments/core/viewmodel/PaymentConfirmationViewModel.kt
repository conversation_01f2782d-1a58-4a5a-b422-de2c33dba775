package com.bukuwarung.payments.core.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.R
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.checkout.PaymentCheckoutViewModel
import com.bukuwarung.payments.core.model.BankAccountDetail
import com.bukuwarung.payments.core.view.PaymentConfirmationPageActivity
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentNotePreference
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.util.*
import javax.inject.Inject


class PaymentConfirmationViewModel @Inject constructor(
    val paymentUseCase: PaymentUseCase,
    val businessUseCase: BusinessUseCase,
    private val paymentNotePreference: PaymentNotePreference,
    private val featurePrefManager: FeaturePrefManager
) : BaseViewModel() {

    data class ViewState (
        var disbursementLoading: Boolean? = null
    )
    private val _viewState = MutableLiveData(ViewState())
    val viewState: LiveData<ViewState> = _viewState

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private val _paymentOutOverviewResponse = MutableLiveData<DisbursementOverviewResponse?>()
    val paymentOutOverviewResponse: LiveData<DisbursementOverviewResponse?> =
        _paymentOutOverviewResponse
    private val _healthStatus = MutableLiveData<HealthStatus?>()
    val healthStatus: LiveData<HealthStatus?> =
        _healthStatus
    var selectedPaymentMethod: PaymentMethod? = null
    private var note = "-"
    var disbursementId = ""
    private var healthState = 0
    private var healthStateMessage = ""
    private var bankAccountDetail: BankAccountDetail? = null
    private var amount: Long = 0
    private var paymentCategory: PaymentCategoryItem? = null
    private var discount: Double? = null
    private var adminFee: Double? = null
    private var loyaltyDiscount: LoyaltyDiscount? = null
    private var totalTransfer: BigDecimal? = null

    companion object {
        const val HEALTH_OK = 0
        const val HEALTH_WARNING = 1
        const val HEALTH_ERROR = 2
    }

    sealed class Event {
        object ShowPaymentStatusScreen : Event()
        data class OpenWebView(val url: String, val paymentTabEnabled: Boolean) : Event()
        data class ShowErrorBottomSheet(
            val isServiceDown: Boolean = false,
            val message: String? = null,
            val name: String
        ) : Event()

        data class ShowToast(
            val message: String? = null,
            val stringId: Int = 0,
            val successAddFav: Boolean = false,
            val successRemoveFav: Boolean = false
        ) : Event()
    }

    fun init(bankAccountDetail: BankAccountDetail?, amount: Long, paymentCategory: PaymentCategoryItem?) {
        this.bankAccountDetail = bankAccountDetail
        this.amount = amount
        this.paymentCategory = paymentCategory
    }

    fun getSelectedPaymentMethodCode() = selectedPaymentMethod?.code.orEmpty()

    fun callPaymentOutOverviewAPI(
        note: String,
        amount: Long,
        bankAccountDetail: BankAccountDetail?,
        selectedDestinationVa: String
    ) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val request = DisbursementOverviewRequest(
                    amount = amount.toBigDecimal(),
                    description = note,
                    customerBankAccountId = bankAccountDetail?.accountDetail?.id,
                    accountId = SessionManager.getInstance().businessId,
                    customerId = bankAccountDetail?.customerId.orEmpty(),
                    customerName = bankAccountDetail?.accountDetail?.holderName.orEmpty(),
                    vaBankCode = selectedDestinationVa,
                    gameName = SessionManager.getInstance().gameRuleName
                )
                when (val response =
                    paymentUseCase.getPaymentOutOverview(
                        request.accountId,
                        bankAccountDetail?.customerId.orEmpty(),
                        request
                    )) {

                    is ApiSuccessResponse -> {
                        withContext(Dispatchers.Main) {
                            _paymentOutOverviewResponse.value = response.body
                            bankAccountDetail?.let { doHealthCheck(it) }
                        }
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_PAYMENT_SEND_SELECT_PAYMENT_METHOD,
                            AppAnalytics.PropBuilder()
                                .put(
                                    AnalyticsConst.GROSS_FEE,
                                    response.body.fee.orNil - response.body.discountFee.orNil - response.body.loyaltyDiscount?.tierDiscount.orNil - response.body.loyaltyDiscount?.subscriptionDiscount.orNil
                                )
                                .put(
                                    AnalyticsConst.DISCOUNT,
                                    response.body.discountFee.orNil + response.body.loyaltyDiscount?.tierDiscount.orNil + response.body.loyaltyDiscount?.subscriptionDiscount.orNil
                                )
                                .put(AnalyticsConst.PLATFORM_FEE, response.body.fee.orNil)
                        )
                        discount = response.body.discountFee.orNil
                        adminFee = response.body.fee
                        loyaltyDiscount = response.body.loyaltyDiscount
                        totalTransfer = response.body.totalTransfer
                    }
                    is ApiErrorResponse -> {
                        setEventStatus(
                            Event.ShowErrorBottomSheet(
                                response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE,
                                response.errorMessage, PaymentConfirmationPageActivity.ApiErrorType.OVERVIEW.name
                            )
                        )
                    }
                    else -> {}
                }
            }
        }
    }

    fun onNoteChanged(note: String) {
        this.note = note
        debounced(note)
    }

    val debounced: (String) -> Unit = Utilities.debounce(
        AppConst.clickDebounceTime,
        viewModelScope
    ) {
        PaymentPrefManager.getInstance().saveFilledData(
            notes = it,
            bankAccountDetail = bankAccountDetail,
            amount = amount,
            category = paymentCategory
        )
    }

    fun doHealthCheck(bankAccountDetail: BankAccountDetail) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val request = PaymentHealthCheckRequest(
                    moneyOutChannels = listOf(bankAccountDetail.bankDetail?.code.orEmpty())
                )
                when (val response = paymentUseCase.doHealthCheck(request)) {
                    is ApiSuccessResponse -> {
                        withContext(Dispatchers.Main) {
                            healthState = HEALTH_OK
                            iterateProviderHealthResponse(response.body.providers)
                            if (healthState != PaymentCheckoutViewModel.HEALTH_ERROR) {
                                iterateMoneyOutHealthResponse(response.body.moneyOut)
                                if (healthState != PaymentCheckoutViewModel.HEALTH_ERROR) {
                                    iterateMoneyInHealthResponse(response.body.moneyIn?.paymentOut)
                                }
                            }
                            if (healthState != PaymentCheckoutViewModel.HEALTH_ERROR) {
                                val calendar = Calendar.getInstance()
                                val hourNow = calendar.get(Calendar.HOUR_OF_DAY)
                                if (hourNow < 7 || hourNow >= 23)
                                    healthState = HEALTH_WARNING
                            }
                            _healthStatus.value =
                                HealthStatus(status = healthState, message = healthStateMessage)
                        }
                    }
                    is ApiErrorResponse -> {
                        setEventStatus(
                            Event.ShowErrorBottomSheet(
                                response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE,
                                response.errorMessage,
                                PaymentConfirmationPageActivity.ApiErrorType.HEALTH.name
                            )
                        )
                    }
                    else -> {}
                }

            }
        }
    }

    private fun iterateMoneyInHealthResponse(list: List<HealthStatus>?) {
        if (list != null) {
            for (status in list) {
                if (selectedPaymentMethod?.code == status.name && false == status.enabled) {
                    healthState = HEALTH_ERROR
                    healthStateMessage = status.message.orEmpty()
                }
            }
        }
    }

    private fun iterateProviderHealthResponse(list: List<HealthStatus>?) {
        var currentMessage = ""
        if (list != null) {
            for (status in list) {
                if (true == status.enabled) return
                else currentMessage = status.message.orEmpty()
            }
        }
        healthState = HEALTH_ERROR
        healthStateMessage = currentMessage
    }

    private fun iterateMoneyOutHealthResponse(list: List<HealthStatus>?) {
        if (list != null) {
            for (status in list) {
                if (false == status.enabled) {
                    healthState = HEALTH_ERROR
                    healthStateMessage = status.message.orEmpty()
                }
            }
        }
    }

    fun createPaymentOutDisbursement(
        categoryId: String?,
        detail: BankAccountDetail?
    ) {
        if (_viewState.value?.disbursementLoading.isTrue) { return }
        viewModelScope.launch {
            _viewState.value = _viewState.value?.copy(disbursementLoading = true)
            withContext(Dispatchers.IO) {
                val prop = AppAnalytics.PropBuilder()
                prop.put("bank", selectedPaymentMethod?.code)
                prop.put(AnalyticsConst.USE_CASE,paymentCategory?.title)
                prop.put(AnalyticsConst.CATEGORY,paymentCategory?.name)
                prop.put(AnalyticsConst.PLATFORM_FEE, adminFee.orNil - discount.orNil - loyaltyDiscount?.tierDiscount.orNil - loyaltyDiscount?.subscriptionDiscount.orNil)
                prop.put(AnalyticsConst.TRANSACTION_AMOUNT, amount)
                prop.put(AnalyticsConst.TOTAL_AMOUNT, amount.toBigDecimal().plus(adminFee?.toBigDecimal() ?: BigDecimal(0)))
                prop.put(AnalyticsConst.DISCOUNT, discount)
                prop.put(AnalyticsConst.GROSS_FEE, adminFee.orNil)
                if(loyaltyDiscount?.tierDiscount.orNil.isNotZero().isTrue)
                    prop.put(AnalyticsConst.LOYALTY_DISCOUNT, loyaltyDiscount?.tierDiscount.orNil)
                if (loyaltyDiscount?.subscriptionDiscount.orNil.isNotZero().isTrue)
                    prop.put(AnalyticsConst.SUBSCRIPTION_DISCOUNT, loyaltyDiscount?.subscriptionDiscount.orNil)

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_SEND_CREATED, prop)
                val request = DisbursementOverviewRequest(
                    amount = paymentOutOverviewResponse.value?.amount.orNil,
                    description = note,
                    customerBankAccountId = detail?.accountDetail?.id,
                    vaBankCode = selectedPaymentMethod?.code,
                    accountId = SessionManager.getInstance().businessId,
                    customerId = detail?.customerId.orEmpty(),
                    customerName = detail?.bankDetail?.name.orEmpty(),
                    extras = PaymentExtras(
                        accounting = PaymentAccountingExtras(categoryId = categoryId),
                        recordIn = AppConst.CASH
                    ),
                    paymentCategoryId = categoryId,
                    totalTransfer = paymentOutOverviewResponse.value?.totalTransfer,
                    gameName = SessionManager.getInstance().gameRuleName
                )
                when (val response = paymentUseCase.createDisbursement(
                    SessionManager.getInstance().businessId,
                    detail?.customerId.orEmpty(),
                    request
                )) {
                    is ApiSuccessResponse -> {
                        withContext(Dispatchers.Main) {
                            _viewState.value = _viewState.value?.copy(disbursementLoading = false)
                        }
                        paymentNotePreference.saveNote(note)
                        val key =
                            PaymentUtils.getKeyForSelectedCategoryId(PaymentConst.TYPE_PAYMENT_OUT)
                        PaymentPrefManager.getInstance().setSelectedPaymentCategoryId(
                            key,
                            categoryId
                        )
                        FeaturePrefManager.getInstance().setExitWithoutCompletingPayment(
                            false,
                            PaymentConst.TYPE_PAYMENT_OUT,
                            null
                        )
                        PaymentPrefManager.getInstance().clearPayOutFilledData()
                        if (selectedPaymentMethod?.code == PaymentConst.SALDO) {
                            disbursementId = response.body.disbursementId
                            setEventStatus(Event.ShowPaymentStatusScreen)
                        } else {
                            val paymentUrl =
                                response.body.checkoutUrl ?: response.body.paymentInstructionsUrl
                            paymentUrl?.let {
                                setEventStatus(
                                    Event.OpenWebView(
                                        it,
                                        featurePrefManager.paymentTabEnabled()
                                    )
                                )
                            }
                        }
                    }
                    is ApiErrorResponse -> {
                        withContext(Dispatchers.Main) {
                            _viewState.value = _viewState.value?.copy(disbursementLoading = false)
                        }
                        setEventStatus(
                            Event.ShowErrorBottomSheet(
                                response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE,
                                response.errorMessage,
                                PaymentConfirmationPageActivity.ApiErrorType.DISBURSEMENT.name
                            )
                        )
                    }
                    else -> {}
                }
            }
        }
    }

    fun addFavourite(id: String) = viewModelScope.launch(Dispatchers.IO) {
        when (val response =
            paymentUseCase.addFavourite(SessionManager.getInstance().businessId, id)) {
            is ApiSuccessResponse -> {
                if (response.body.success.isTrue) {
                    setEventStatus(
                        Event.ShowToast(
                            stringId = R.string.success_fav_message, successAddFav = true
                        )
                    )
                } else {
                    setEventStatus(
                        Event.ShowToast(
                            response.body.message
                        )
                    )
                }
            }
            is ApiErrorResponse -> {
                setEventStatus(Event.ShowToast(response.errorMessage))
            }
            else -> {}
        }
    }

    fun deleteFavourite(id: String) = viewModelScope.launch(Dispatchers.IO) {
        when (val response =
            paymentUseCase.removeFavourite(SessionManager.getInstance().businessId, id)) {
            is ApiSuccessResponse -> {
                if (response.body.success.isTrue) {
                    setEventStatus(
                        Event.ShowToast(
                            stringId = R.string.remove_fav_message, successRemoveFav = true
                        )
                    )

                } else {
                    setEventStatus(
                        Event.ShowToast(
                            response.body.message
                        )
                    )
                }
            }
            is ApiErrorResponse -> {
                setEventStatus(Event.ShowToast(response.errorMessage))
            }
            else -> {}
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }
}