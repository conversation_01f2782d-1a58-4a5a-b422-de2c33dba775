package com.bukuwarung.payments.core.view

import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.text.InputType
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.databinding.PaymentBankValidationBottomSheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.core.model.BankAccountDetail
import com.bukuwarung.payments.core.model.BankDetail
import com.bukuwarung.payments.core.viewmodel.PaymentBankValidationBSViewModel
import com.bukuwarung.payments.data.model.PaymentExitIntentData
import com.bukuwarung.payments.widget.BankAccountView
import com.bukuwarung.payments.widget.PaymentBankAccountView
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.*
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import com.google.android.material.bottomsheet.BottomSheetBehavior
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject

class PaymentBankValidationBS : BaseBottomSheetDialogFragment() {

    @Inject
    lateinit var viewModel: PaymentBankValidationBSViewModel
    private var _binding: PaymentBankValidationBottomSheetBinding? = null
    private val binding get() = _binding!!
    private val paymentType = PaymentConst.TYPE_PAYMENT_OUT
    private val entryPoint = AnalyticsConst.PEMBAYARAN

    companion object {
        const val TAG = "PaymentBankValidationBS"
        private const val BANK = "bank"
        fun createInstance(selectedBank: Bank) = PaymentBankValidationBS().apply {
            arguments = Bundle().apply {
                putParcelable(BANK, selectedBank)
            }
        }
    }

    private val selectedBank by lazy {
        arguments?.getParcelable(BANK) as? Bank
    }

    interface ICommunicator {
        fun openBanksBottomSheet()
        fun openPaymentInputPage(bankAccountDetail: BankAccountDetail)
    }

    private var listener: ICommunicator? = null

    override fun onAttach(context: Context) {
        AndroidSupportInjection.inject(this)
        super.onAttach(context)
        listener = context as? ICommunicator
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = PaymentBankValidationBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.setOnShowListener {
            Utilities.showBottomSheet(
                it,
                BottomSheetBehavior.STATE_EXPANDED
            )
        }
        viewModel.setSelectedBank(selectedBank)
        setView()
        subscribe()
        FeaturePrefManager.getInstance().setExitWithoutAddBankAccount(true, paymentType, PaymentExitIntentData(null, "", SessionManager.getInstance().businessId))
    }

    private fun setView() = with(binding) {
        ivBack.setSingleClickListener { openBanksBottomSheet() }
        ivCross.setSingleClickListener { openBanksBottomSheet() }
        bankView.setAddBankAccountView(
            PaymentBankAccountView.PaymentOutBankAccountViewState.CHANGE_BANK_ACCOUNT_HIDE_SUBTITLE,
            { openBanksBottomSheet() },
            BankAccountDetail().apply {
                bankDetail = BankDetail(
                    code = selectedBank?.bankCode,
                    name = selectedBank?.bankName,
                    icon = selectedBank?.logo
                )
            }
        )
        bsvSearch.onTextChangeActionListeners(
            {},
            {
                if(binding.bsvSearch.getTextByRemovingSpaces().isNotBlank()) bsvSearch.addSpaceAfterEveryFourCharacter()
                tvErrorMessage.hideView()
                bankAccountView.hideView()
                btnVerify.isEnabled = true
                viewModel.onAccountNumberChanged(binding.bsvSearch.getTextByRemovingSpaces())
            },
            {}
        )
        bsvSearch.setFocus()
        InputUtils.showKeyboard(context)
        bsvSearch.setInputType(InputType.TYPE_CLASS_NUMBER)
        btnVerify.setSingleClickListener { viewModel.validateAndAddCustomerBankAccount() }
    }

    override fun onCancel(dialog: DialogInterface) {
        //on back click we show the previous bottomsheet.
        openBanksBottomSheet()
    }

    private fun openBanksBottomSheet() {
        InputUtils.hideKeyboard(context)
        dismiss()
        listener?.openBanksBottomSheet()
    }

    private fun subscribe() {
        viewModel.eventStatus.observe(this) {
            when (it) {
                is PaymentBankValidationBSViewModel.Event.ShowLoader -> {
                    if (it.isLoading) {
                        binding.btnVerify.showProgress {
                            buttonTextRes = null
                            progressColor = Color.BLACK
                        }
                    } else {
                        binding.btnVerify.hideProgress(R.string.label_verify)
                    }
                }
                is PaymentBankValidationBSViewModel.Event.ShowErrorMessage -> handleErrorApi(
                    it.message, it.isBankAccountNumberEmpty
                )
                is PaymentBankValidationBSViewModel.Event.ShowBankAccountDetail -> showBankAccountDetail(
                    it.bankAccountDetail,
                    it.isSuccess
                )
                else -> {}
            }
        }
    }

    private fun showBankAccountError(bankAccountDetail: BankAccountDetail) = with(binding){
        btnVerify.isEnabled = false
        bankAccountView.showView()
        bankAccountView.setBankView(
            BankAccount(
                bankCode = bankAccountDetail.bankDetail?.code.orEmpty(),
                accountNumber = bankAccountDetail.accountDetail?.maskedNumber.orEmpty()
            ),
            bankStatus = BankAccountView.BankStatus.COMMON,
            paymentType = paymentType,
            entryPoint = entryPoint,
        )
        context?.let {
            tvErrorMessage.text = Utilities.makeSectionOfTextClickable(
                bankAccountDetail.message?.text.orEmpty(),
                bankAccountDetail.message?.cta?.text.orEmpty(),
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        val intent = WebviewActivity.createIntent(
                            it, bankAccountDetail.message?.cta?.redirect.orEmpty(), ""
                        )
                        it.startActivity(intent)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = false
                        ds.color = it.getColorCompat(R.color.red_80)
                    }
                })
            tvErrorMessage.movementMethod = LinkMovementMethod.getInstance()
            tvErrorMessage.showView()
        }
    }
    private fun handleErrorApi(
        message: String?,
        isBankAccountNumberEmpty: Boolean,
    ) = with(binding) {
        btnVerify.isEnabled = false
        if (message == AppConst.NO_INTERNET_ERROR_MESSAGE) {
            grpErrorView.hideView()
            bukuErrorView.showView()
            bukuErrorView.setErrorType(BaseErrorView.Companion.ErrorType.CONNECTION_ERROR)
            bukuErrorView.addCallback(internetErrorViewCallBack)
            InputUtils.hideKeyboard(context)
        } else if (!isBankAccountNumberEmpty) {
            tvErrorMessage.showView()
            tvErrorMessage.text = when {
                !message.isNullOrBlank() -> message
                else -> getString(R.string.bank_account_not_found)
            }
        }
    }

    private var internetErrorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            dismiss()
        }
        override fun messageClicked() {}
    }

    private fun showBankAccountDetail(bankAccountDetail: BankAccountDetail, isSuccess: Boolean) {
        if(!isSuccess){
            showBankAccountError(bankAccountDetail)
        } else {
            dismiss()
            InputUtils.hideKeyboard(context)
            FeaturePrefManager.getInstance().setExitWithoutAddBankAccount(false, paymentType, null)
            listener?.openPaymentInputPage(bankAccountDetail)
        }
    }

}