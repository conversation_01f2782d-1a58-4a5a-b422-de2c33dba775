package com.bukuwarung.payments.core.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.bukuwarung.R
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.core.model.BankAccountDetail
import com.bukuwarung.payments.core.service.SearchBankAccountPagingSource
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class SearchBankAccountItemViewModel @Inject constructor(
    val paymentUseCase: PaymentUseCase,
    val businessUseCase: BusinessUseCase
) : BaseViewModel() {


    private var queryText: String = ""
    private var oldQueryText: String = ""
    private val _pagingStatus = MutableLiveData<PagingStatus>()
    val pagingStatus: LiveData<PagingStatus> = _pagingStatus
    private val _productData = MutableLiveData<PagingData<BankAccountDetail>>()
    val productData: LiveData<PagingData<BankAccountDetail>> get() = _productData

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus

    sealed class Event {
        data class ShowToast(val message: String? = null, val stringId: Int = 0) : Event()
    }

    fun changeQueryText(newQuery: String) {
        queryText = newQuery
        if(queryText == oldQueryText){
            return
        }
        oldQueryText = queryText
        invalidateDataSource()
    }


    fun invalidateDataSource() {
        providePagingSource()
    }

    fun providePagingSource() {
        val pager = Pager(
            config = PagingConfig(
                pageSize = RemoteConfigUtils.getPaymentConfigs().recentsPaginationLimit.orNil,
                prefetchDistance = 2
            ),
            pagingSourceFactory = {
                SearchBankAccountPagingSource(
                    paymentUseCase = paymentUseCase,
                    pagingStatusLiveData = _pagingStatus,
                    searchTerm = queryText
                )
            }
        )

        viewModelScope.launch {
            pager.flow.cachedIn(viewModelScope).collectLatest {
                _productData.value = it
            }
        }
    }

    fun addFavourite(id: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.addFavourite(
                SessionManager.getInstance().businessId,
                id
            )) {
                is ApiSuccessResponse -> {
                    if (response.body.success.isTrue) {
                        setEventStatus(Event.ShowToast(stringId = R.string.success_fav_message))
                    } else {
                        setEventStatus(Event.ShowToast(response.body.message))
                    }
                    invalidateDataSource()
                }
                is ApiErrorResponse -> {
                    setEventStatus(Event.ShowToast(response.errorMessage))
                }
                else -> {}
            }
        }
    }

    fun deleteFavourite(id: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.removeFavourite(
                SessionManager.getInstance().businessId,
                id
            )) {
                is ApiSuccessResponse -> {
                    if (response.body.success.isTrue) {
                        setEventStatus(Event.ShowToast(stringId = R.string.remove_fav_message))
                    } else {
                        setEventStatus(Event.ShowToast(response.body.message))
                    }
                    invalidateDataSource()
                }
                is ApiErrorResponse -> {
                    setEventStatus(Event.ShowToast(response.errorMessage))
                }
                else -> {}
            }

        }
    }

    fun deleteBankAccount(id: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.removeBankAccount(
                SessionManager.getInstance().businessId,
                id
            )) {
                is ApiSuccessResponse -> {
                    if (response.body.success.isTrue) {
                        setEventStatus(Event.ShowToast(stringId = R.string.remove_account_message))
                    } else {
                        setEventStatus(Event.ShowToast(response.body.message))
                    }
                    invalidateDataSource()
                }
                is ApiErrorResponse -> {
                    setEventStatus(Event.ShowToast(response.errorMessage))
                }
                else -> {}
            }

        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(
        Dispatchers.Main) {
        eventStatus.value = event
    }
}