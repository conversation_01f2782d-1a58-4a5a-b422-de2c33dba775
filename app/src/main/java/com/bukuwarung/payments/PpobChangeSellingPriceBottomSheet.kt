package com.bukuwarung.payments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.databinding.PpobChangeSellingPriceBottomSheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.setSingleClickListener

class PpobChangeSellingPriceBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "PpobChangeSellingPriceListener"
        private const val SELLING_PRICE = "selling_price"

        fun createInstance(sellingPrice: String) =
            PpobChangeSellingPriceBottomSheet().apply {
                arguments = Bundle().apply { putString(SELLING_PRICE, sellingPrice) }
            }
    }

    private var listener: PpobChangeSellingPriceListener? = null

    interface PpobChangeSellingPriceListener {
        fun onUpdateSellingPrice(sellingPrice: Long)
    }

    private var _binding: PpobChangeSellingPriceBottomSheetBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = PpobChangeSellingPriceBottomSheetBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val sellingPrice = arguments?.getString(SELLING_PRICE)
        binding.inputNominal.setText(sellingPrice)
        binding.ivClose.setSingleClickListener {
            InputUtils.hideKeyboardFrom(context, binding.inputNominal)
            dismiss()
        }
        binding.btnSave.setSingleClickListener {
            InputUtils.hideKeyboardFrom(context, binding.inputNominal)
            dismiss()
            listener?.onUpdateSellingPrice(binding.inputNominal.getNumberValue())
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if(context is PpobChangeSellingPriceListener)
            listener = context
    }

}