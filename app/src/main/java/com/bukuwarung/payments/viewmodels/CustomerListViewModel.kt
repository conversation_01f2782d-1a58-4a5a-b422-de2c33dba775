package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.*
import com.bukuwarung.activities.phonebook.ContactRepository
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.session.User
import com.bukuwarung.utils.Utility

class CustomerListViewModel(private val customerRepository: CustomerRepository) : ViewModel() {
    data class ViewState(
            val searchQuery: String? = null,
            val isSearchEnabled: Boolean = false
    )

    private val reload = MutableLiveData(true)

    private var existingContactLoaded = false

    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    val customers: MediatorLiveData<List<CustomerEntity>> = MediatorLiveData()

    val existingCst: LiveData<List<CustomerEntity>> = reload.switchMap {
        customerRepository.getCustomersByBusiness(User.getBusinessId())
    }

    val contactsCst: LiveData<List<CustomerEntity>> = ContactRepository.INSTANCE.contactList.map {
        it
                .filter { contact -> contact.name.isNullOrBlank().not() }
                .map { contact ->
                    val cst = CustomerEntity()
                    cst.name = contact.name
                    cst.phone = contact.mobile
                    cst.image = contact.photo
                    cst
                }
    }

    val filteredCustomers: LiveData<List<CustomerEntity>> = viewState.switchMap { viewState ->
        return@switchMap if (!viewState.isSearchEnabled || customers.value == null || viewState.searchQuery.isNullOrEmpty()) {
            reload()
            MutableLiveData()
        } else {
            val customerPhones = mutableListOf<String>()
            val filteredList = mutableListOf<CustomerEntity>()

            customers.value!!.forEach {
                if (!customerPhones.contains(it.phone)) {
                    customerPhones.add(it.phone)
                    filteredList.add(it)
                }
            }

            MutableLiveData(filteredList.toList().filter { it.name.startsWith(viewState.searchQuery, true) })
        }
    }

    init {
        reload()
        customers.addSource(existingCst) {
            addContactToCustomer(it, skipCheck = existingContactLoaded.not())
            if (existingContactLoaded.not()) existingContactLoaded = existingContactLoaded.not()

            try {
                // we'd only want to load contacts after successfully loaded existing customers
                customers.addSource(contactsCst) { csts ->
                    addContactToCustomer(csts)
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }

    private fun addContactToCustomer(csts: List<CustomerEntity>, skipCheck: Boolean = false) {
        val currentData = customers.value?.toMutableList() ?: mutableListOf()

        csts.forEach { cst ->
            if (Utility.isBlank(cst.name).not() && (currentData.contains(cst).not() || skipCheck)) {
                currentData.add(cst)
            }
        }
        customers.value = currentData
    }

    fun loadContacts() {
        ContactRepository.INSTANCE.refreshContactList()
    }

    fun reload() {
        reload.value = true
    }

    private fun currentViewState(): ViewState = viewState.value!!

    fun enableSearch() {
        viewState.value = currentViewState().copy(isSearchEnabled = true)
    }

    fun disableSearch() {
        viewState.value = currentViewState().copy(isSearchEnabled = false)
    }

    fun onSearchQueryChanged(query: CharSequence) {
        viewState.value = currentViewState().copy(searchQuery = query.toString())
    }
}
