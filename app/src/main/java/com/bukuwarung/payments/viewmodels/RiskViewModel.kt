package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.*
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.RiskUseCase
import com.bukuwarung.payments.data.model.BookValidationRequest
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject


class RiskViewModel @Inject constructor(
    private val riskUseCase: RiskUseCase,
    private val businessUseCase: BusinessUseCase,
) : BaseViewModel() {

    companion object {
        // constant for last failed api call for retry purpose
        private const val API_BOOK_VALIDATION = 1
    }

    data class ViewState(
        val showLoading: Boolean = false,
    )

    sealed class Event {
        object BookValidationsSuccess : Event()
        data class BookValidationError(val bookName: String?) : Event()

        // ApiErrors are 3xx, 4xx and 5xx responses
        data class ApiError(val code: String, val message: String?) : Event()

        // ServerError is when server is unreachable, it comes as ApiErrorResponse
        // Can be due to service being down or no internet
        data class ServerError(val message: String?) : Event()
    }

    private var failedApiState = -1
    private var transactionType = PaymentConst.TYPE_PAYMENT_IN.toString()
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private fun currentViewState(): ViewState = viewState.value!!
    private val _viewState: MutableLiveData<ViewState> = MutableLiveData<ViewState>(ViewState())
    val viewState: LiveData<ViewState> = _viewState

    fun init(transactionType: String) {
        this.transactionType = transactionType
    }

    /**
     * Runs 1 validation.
     * This can be used to prioritise handling of validation errors.
     * It should not happen that both errors show at the same time and more important error gets lost
     * in the handling on UI side.
     * Error handling is triggered once all validation responses are received.
     *
     * 1. Checks for valid business name
     */
    fun runValidations(
        validateBookName: Boolean = true
    ) = viewModelScope.launch {
        _viewState.postValue(currentViewState().copy(showLoading = true))
        var bookValidation: ApiResponse<Any>? = null
        val bookName = getBook()?.bookName

        coroutineScope {
            if (validateBookName) {
                bookName?.let {
                    launch {
                        bookValidation =
                            riskUseCase.validateBookName(BookValidationRequest(it))
                    }
                }
            }
        }

        // We give priority to book validation
        bookValidation?.let { handleBookValidation(it, bookName) }
    }

    private fun handleBookValidation(validationResponse: ApiResponse<Any>, bookName: String?) =
        viewModelScope.launch {
            when (validationResponse) {
                is ApiErrorResponse -> {
                    if (validationResponse.statusCode == 422) {
                        val blockedBook =
                            PaymentUtils.parseBlacklistedBookName(validationResponse.errorMessage)
                        eventStatus.postValue(Event.BookValidationError(blockedBook ?: bookName))
                        _viewState.postValue(currentViewState().copy(showLoading = false))
                    } else {
                        handleErrorResponse(
                            validationResponse.code,
                            validationResponse.errorMessage,
                            API_BOOK_VALIDATION
                        )
                    }
                }
                is ApiSuccessResponse, is ApiEmptyResponse -> {
                    eventStatus.postValue(Event.BookValidationsSuccess)
                    _viewState.postValue(currentViewState().copy(showLoading = false))
                }
            }
        }

    private fun handleErrorResponse(code: String, message: String = "", failedApi: Int) {
        val isServerError =
            message.isNotBlank() && message != AppConst.NO_INTERNET_ERROR_MESSAGE
        if (isServerError) {
            eventStatus.postValue(Event.ServerError(message))
        } else {
            eventStatus.postValue(Event.ApiError(code, message))
        }
        _viewState.postValue(currentViewState().copy(showLoading = false))
        failedApiState = failedApi
    }

    private fun getBook() = businessUseCase.getBusinessById(SessionManager.getInstance().businessId)
}
