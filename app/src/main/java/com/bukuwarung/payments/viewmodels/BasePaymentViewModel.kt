package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.domain.payments.PaymentUseCase

open class BasePaymentViewModel(private val paymentUseCase: PaymentUseCase) : BaseViewModel() {
    fun getBankAccountsFromLocalAndServer(bookId: String?, localOnly: Boolean = true): LiveData<List<BankAccount>> = liveData {
        bookId ?: return@liveData
        val local = paymentUseCase.getLocalMerchantBankAccounts(bookId)
        local?.run {
            emit(this)
            if (localOnly) return@liveData
        }
        val result = paymentUseCase.getMerchantBankAccounts(bookId)
        if (result is ApiSuccessResponse) {
            emit(result.body)
        }
    }
}
