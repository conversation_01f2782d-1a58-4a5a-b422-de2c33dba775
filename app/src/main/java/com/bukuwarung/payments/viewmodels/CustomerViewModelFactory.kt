package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.database.repository.CustomerRepository

class CustomerViewModelFactory(
        private val customerRepository: CustomerRepository,
        private val entryPoint: String? = null
) : ViewModelProvider.NewInstanceFactory() {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return if (modelClass == AddContactViewModel::class.java) {
            AddContactViewModel(customerRepository, entryPoint) as T
        } else {
            CustomerListViewModel(customerRepository) as T
        }
    }
}
