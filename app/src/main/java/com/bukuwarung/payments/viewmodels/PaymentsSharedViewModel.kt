package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.bukuwarung.base_android.extensions.postEvent
import com.bukuwarung.base_android.utils.Event
import com.bukuwarung.constants.PaymentConst.TYPE_PAYMENT_IN
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.payments.data.repository.PaymentsRepository
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class PaymentsSharedViewModel(
        private val businessRepository: BusinessRepository,
        private val customerRepository: CustomerRepository,
        private val paymentsRepository: PaymentsRepository,
        private val customerId: String,
        private val paymentType: Int,
        private var bookId: String?
) : BaseViewModel() {
    data class ViewState(
            val accountsLoader: Boolean = false
    )

    private var accountsJob: Job? = null
    private var enablePaymentsJob: Job? = null

    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    private val _bankAccounts: MutableLiveData<List<BankAccount>> = MutableLiveData()

    val bankAccounts: LiveData<List<BankAccount>> = _bankAccounts

    val customer: LiveData<CustomerEntity>? = customerRepository.getObservableCustomerById(customerId)

    val customerLiveEvent: LiveData<Event<CustomerEntity>>? = customer?.switchMap { customer ->
        MutableLiveData<Event<CustomerEntity>>().also { it.postEvent(customer) }
    }

    fun showLoader(show: Boolean) {
        viewState.value = currentViewState().copy(accountsLoader = show)
    }

    private fun currentViewState(): ViewState = viewState.value!!

    fun logNullCustomer() {
        FirebaseCrashlytics.getInstance().log("PaymentsActivity customerEntity null: $customerId")
    }


    fun launchGetBankAccounts(bookId: String) {
        if (accountsJob?.isActive == true) accountsJob?.cancel()
        accountsJob = getBankAccounts(bookId)
    }

    private fun getBankAccounts(bookId: String) = viewModelScope.launch {
        var bookName = "Usaha Saya"
        try {
            val book = businessRepository.getBusinessByIdSync(getBookId())
            bookName = book.bookName.takeIf{!it.isNullOrBlank()} ?: book.businessName
        } catch (ex: Exception) {
            ex.printStackTrace()
        }

        if (bankAccounts.value.isNullOrEmpty()) {
            updateAccountsLoader(true)
        }

        if (bookName.trim().equals("Usaha Saya", ignoreCase = true)) {
            // stop execution if name is Usaha Saya, Activity will redirect to BusinessProfileFormActivity.java
//            updateAccountsLoader(false)
            return@launch
        }

        val response = if (paymentIn())
            paymentsRepository.getMerchantBankAccounts(bookId)
        else
            paymentsRepository.getCustomerBankAccounts(bookId, customerId)
        handleBankAccountResponse(response)
    }

    private fun handleBankAccountResponse(response: ApiResponse<List<BankAccount>>) {
        when (response) {
            is ApiSuccessResponse -> {
                updateAccountsLoader(false)
                _bankAccounts.value = response.body

                // for users who are already use payment feature
                businessRepository.enablePayments(getBookId())
            }
            is ApiErrorResponse -> {
                if (response.statusCode == 403) {
                    launchEnablePaymentsJob(getBookId())
                } else if (response.statusCode == 401){
//                    if (SessionManager.getInstance().isRefreshingTokenFails) {
                    postAlertForExpiration("Sesi Anda telah habis atau tidak dapat diperbarui, silakan login ulang")
//                    }
                } else {
                    postAlert(response.errorMessage)
                }
            }
            else -> {
                postAlert("Unable to process your request at this time, please try later!") // move to string.xml
            }
        }
    }

    private fun launchEnablePaymentsJob(bookId: String) {
        if (enablePaymentsJob?.isActive == true) enablePaymentsJob?.cancel()

        enablePaymentsJob = enableMerchantPayments(bookId)
    }


    private fun enableMerchantPayments(bookId: String) = viewModelScope.launch {
        var bookName = "Usaha Saya"
        try {
            val book = businessRepository.getBusinessByIdSync(getBookId())
            bookName = book.bookName.takeIf{!it.isNullOrBlank()} ?: book.businessName
        } catch (ex: Exception) {
            ex.printStackTrace()
            return@launch
        }
        when (val response = paymentsRepository
                .enableMerchantPayments(bookId, bookName ?: "-")) {
            is ApiSuccessResponse -> {
                launchGetBankAccounts(bookId)
                //save to firestore and local db
                businessRepository.enablePayments(getBookId())
            }
            is ApiErrorResponse -> {
                if (response.statusCode != 401)
                    postAlert(response.errorMessage)
                else
                    postAlertForExpiration("Sesi Anda telah habis atau tidak dapat diperbarui, silakan login ulang")
            }
            else -> {
                postAlert("Unable to process your request at this time, please try later!") // move to string.xml
            }
        }
    }

    private fun updateAccountsLoader(show: Boolean) {
        viewState.value = currentViewState().copy(accountsLoader = show)

    }


    fun getCustomerId(): String {
        return customer?.value?.customerId!!
    }

    fun getBookId(): String {
        return customer?.value?.bookId ?: bookId!!
    }

    fun paymentIn() = paymentType == TYPE_PAYMENT_IN

}

