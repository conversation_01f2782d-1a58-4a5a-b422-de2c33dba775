package com.bukuwarung.payments.viewmodels

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.R
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.FavouriteRequest
import com.bukuwarung.payments.data.model.ppob.FavouriteDetail
import com.bukuwarung.session.SessionManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class PaymentContactViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase
) : BaseViewModel() {

    data class ViewState(val message: String? = "")

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    sealed class Event {
        data class onAddFavourite(val favouriteDetail: FavouriteDetail?, val message: String) : Event()
    }

    private val event = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = event

    fun addFavourite(favouriteRequest: FavouriteRequest, entryPoint: String?, context: Context) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.addFavourite(
                    SessionManager.getInstance().businessId,
                    favouriteRequest
            )) {
                is ApiSuccessResponse -> {
                    event.postValue(Event.onAddFavourite(response.body.favouriteDetail, response.body.message
                            ?: context.getString(R.string.add_favourite_success_msg)))
                }
                is ApiErrorResponse -> {
                    event.postValue(Event.onAddFavourite(null, response.errorMessage))
                }
                else -> {

                }
            }

        }
    }

    private suspend fun setViewState(state: ViewState) = withContext(Dispatchers.Main) {
        viewState.value = state
    }
}