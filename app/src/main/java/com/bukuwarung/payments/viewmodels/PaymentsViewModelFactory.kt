package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.activities.transaction.customer.CustomerTransactionViewModel
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.PaymentFilterDto
import com.bukuwarung.payments.data.repository.PaymentsRepository

class PaymentsViewModelFactory(
        private val customerRepository: CustomerRepository,
        private val businessRepository: BusinessRepository,
        private val paymentsRepository: PaymentsRepository,
        private val finproUseCase: FinproUseCase,
        private val customerId: String?,
        private val bookId: String?,
        private val paymentType: Int?,
        private val month: String?,
        private val paymentTransactionType: String?,
        private val paymentRequestId: String?,
        private val filterDto: PaymentFilterDto? = null, private val billerCode: String?
) : ViewModelProvider.NewInstanceFactory() {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when (modelClass) {
            PaymentHistoryPagerViewModel::class.java -> {
                PaymentHistoryPagerViewModel(finproUseCase, customerRepository, customerId!!, bookId!!) as T
            }
            CustomerTransactionViewModel::class.java -> {
                CustomerTransactionViewModel(paymentsRepository, customerRepository, businessRepository) as T
            }
            else -> {
                PaymentsSharedViewModel(businessRepository, customerRepository, paymentsRepository, customerId!!, paymentType!!, bookId) as T
            }
        }
    }
}
