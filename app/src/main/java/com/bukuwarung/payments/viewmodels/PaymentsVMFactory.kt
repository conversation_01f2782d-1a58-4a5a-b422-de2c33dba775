package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.domain.payments.BankingUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.OrdersUseCase
import com.bukuwarung.payments.history.OrderHistoryViewModel
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class PaymentsVMFactory @Inject constructor(
    private val finproUseCase: FinproUseCase,
    private val ordersUseCase: OrdersUseCase,
    private val bankingUseCase: BankingUseCase
) : ViewModelProvider.NewInstanceFactory() {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when (modelClass) {
            OrderHistoryViewModel::class.java -> {
                OrderHistoryViewModel(finproUseCase, ordersUseCase, bankingUseCase) as T
            }
            else -> {
                throw IllegalArgumentException("Unsupported VM class")
            }
        }
    }
}
