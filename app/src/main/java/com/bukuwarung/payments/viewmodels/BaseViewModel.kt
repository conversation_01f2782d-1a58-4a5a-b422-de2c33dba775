package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.bukuwarung.base_android.extensions.postEvent
import com.bukuwarung.base_android.utils.Event

open class BaseViewModel : ViewModel() {
    private val _alert = MutableLiveData<Event<String>>()
    val alert: LiveData<Event<String>> = _alert

    private val _alertForExpired = MutableLiveData<Event<String>>()
    val alertForExpired: LiveData<Event<String>> = _alertForExpired

    fun postAlert(message: String) {
        _alert.postEvent(message)
    }

    fun postAlertForExpiration(message: String) {
        _alertForExpired.postEvent(message)
    }
}