package com.bukuwarung.payments.viewmodels

import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.ppob.base.viewmodel.PpobViewModel
import com.bukuwarung.session.SessionManager
import javax.inject.Inject

class PpobBillDetailsBsViewModel @Inject constructor(
    val finproUseCase: FinproUseCase,
    val sessionManager: SessionManager,
    val businessUseCase: BusinessUseCase
) : PpobViewModel(finproUseCase, sessionManager, businessUseCase) {
}