package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.*
import com.bukuwarung.base_android.utils.Event
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.PaymentFilterDto

class PaymentHistoryPagerViewModel(
        private val finproUseCase: FinproUseCase,
        customerRepository: CustomerRepository,
        customerId: String,
        bookId: String
) : ViewModel() {
    data class ViewState(
            val monthsLoader: Boolean = true,

            val showEmptyState: Boolean = false,

            var paymentFilterDto: PaymentFilterDto = PaymentFilterDto()
    )

    private val _query = MutableLiveData("")
    val query = _query as LiveData<String>

    private val _selectedPage = MutableLiveData<Int>()
    val selectedPage: LiveData<Int> = _selectedPage

    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    val customer: LiveData<CustomerEntity> = liveData {
        customerRepository.getCustomerById(customerId)
    }

    val months: LiveData<ApiResponse<List<String>>> =
            liveData {
                val response = finproUseCase.getHistoryMonths(bookId, customerId)
                viewState.value = currentViewState().copy(monthsLoader = false)
                emit(response)
            }

    val moveToLastItem: LiveData<Event<Boolean>> = months.switchMap {
        liveData {
            if (it is ApiSuccessResponse && it.body.isEmpty().not()) emit(Event.of(true))
        }
    }

    val emptyState = months.switchMap {
        liveData {
            emit((it is ApiSuccessResponse && it.body.isEmpty()) || (it is ApiErrorResponse && it.statusCode == 403))
        }
    }

    val alert: LiveData<Event<String>> = months.switchMap {
        liveData {
            if (it is ApiErrorResponse && it.statusCode != 403 && it.statusCode != 401) emit(Event.of(it.errorMessage))
        }
    }

    val alertLogin: LiveData<Event<String>> = months.switchMap {
        liveData {
            if (it is ApiErrorResponse && it.statusCode == 401) emit(Event.of(""))
        }
    }


    private fun currentViewState(): ViewState = viewState.value!!

    fun submitFilter(filter: PaymentFilterDto) {
        viewState.value = currentViewState().copy(paymentFilterDto = filter)
    }

    fun search(query: String) {
        _query.postValue(query)
    }

    fun setCurrentPage(page: Int) {
        _selectedPage.value = page
    }

}

