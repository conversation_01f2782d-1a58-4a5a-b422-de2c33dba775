package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base_android.extensions.postEvent
import com.bukuwarung.base_android.utils.Event
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.CustomerRepository
import kotlinx.coroutines.launch

class AddContactViewModel(private val customerRepository: CustomerRepository, private val entryPoint: String?) : ViewModel() {

    data class ViewState(
            val name: String = "",
            val phone: String = "",

            val nameError: Int = R.string.error_enter_contact_name,
            val showNameError: Boolean = false
    )

    private val _navigation = MutableLiveData<Event<NavigationCommand>>()
    val navigation: LiveData<Event<NavigationCommand>> = _navigation

    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    private fun currentViewState(): ViewState = viewState.value!!

    fun onNameTextChanged(name: String) {
        viewState.value = currentViewState().copy(name = name, showNameError = false)
    }

    fun onPhoneTextChanged(phone: String) {
        viewState.value = currentViewState().copy(phone = phone)
    }

    fun saveCustomer() {
        viewModelScope.launch {
            if (currentViewState().name.isEmpty()) {
                viewState.value = currentViewState().copy(showNameError = true)
                return@launch
            }

            val cleansedPhone = if (currentViewState().phone.isNullOrBlank() || currentViewState().phone.startsWith("0").not()) {
                currentViewState().phone
            } else {
                currentViewState().phone.substring(1, currentViewState().phone.length)
            }
            val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.CUSTOMER_PHONE, currentViewState().phone)
            AppAnalytics.trackEvent(if(AnalyticsConst.LAINNYA == entryPoint) "payment_lainnya_save_customer_manual" else AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_SAVE_CUSTOMER_MANUAL, prop)
            val id = customerRepository.saveCustomer(currentViewState().name, cleansedPhone)
            _navigation.postEvent(NavigationCommand.CustomerDetails(id, cleansedPhone))
        }

    }

    sealed class NavigationCommand {
        class CustomerDetails(val customerId: String, val phone: String) : NavigationCommand()
    }
}

