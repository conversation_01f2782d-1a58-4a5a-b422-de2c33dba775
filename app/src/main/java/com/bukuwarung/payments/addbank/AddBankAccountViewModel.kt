package com.bukuwarung.payments.addbank

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.*
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.BankValidationRequest
import com.bukuwarung.payments.data.model.FinproRefunds
import com.bukuwarung.payments.data.model.PaymentExitIntentData
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.isFalse
import com.bukuwarung.utils.isTrue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject

class AddBankAccountViewModel @Inject constructor(
        private val paymentUseCase: PaymentUseCase,
        private val businessUseCase: BusinessUseCase,
        private val finProUseCase: FinproUseCase,
        sessionManager: SessionManager
) : BaseViewModel() {

    sealed class Event {
        data class SetResultSuccess(val bankAccount: BankAccount) : Event()
        data class AddRefundBankSuccess(val refundBankAccount: RefundBankAccount) : Event()
        data class ShowErrorMessage(val message: String?, val code: String = "", val bankDetails: BankValidationRequest? = null) : Event()
        data class ShowBankAccountDetail(val bankAccount: BankAccount) : Event()
        data class ReturnSelectedAccount(val currentSelectedAccount: BankAccount) : Event()
        object StartTimer : Event()
        object StopTimer : Event()
    }

    sealed class ProfileIncompleteEvent {
        object ShowProfileDialog : ProfileIncompleteEvent()
    }

    data class ViewState(
            val bankAccountNumber: String = "",
            val bankVerificationError: String = "",
            val verificationLoader: Boolean = false,
            val addingLoader: Boolean = false,
            val showBankError: Boolean = false,
            val showAccountInputError: Boolean = false,
            val showAccountDetails: Boolean = false,
            val showVerificationError: Boolean = false,
            val isButtonEnabled: Boolean = false,
            val showBlockedError: Boolean = false
    )

    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    val eventStatus = MutableLiveData<Event>()
    val profileIncompleteEvent: MutableLiveData<ProfileIncompleteEvent> = MutableLiveData()
    private var bookId: String = sessionManager.businessId
    private var bookEntity: BookEntity? = null
    private var entryPoint = ""
    private var product: String? = ""
    private var transactionId: String? = ""
    private var currentSelectedAccount: BankAccount? = null
    private var selectedBank: Bank? = null
    var bankAccount: BankAccount?= null

    private fun currentViewState(): ViewState = viewState.value!!
    private var customerId: String? = null
    private var paymentType: Int = 0
    private var settingFirstTime: Boolean = false

    fun init(
        paymentType: Int,
        entryPoint: String,
        cstId: String?,
        bookId: String?,
        hasBankAccount: Boolean,
        settingFirstTime: Boolean,
        product: String?, transactionId: String?
    ) {
        this.entryPoint = entryPoint
        this.paymentType = paymentType
        this.settingFirstTime = settingFirstTime
        this.product = product
        this.transactionId = transactionId
        customerId = cstId
        if (bookId != null) this.bookId = bookId
        if (!hasBankAccount && entryPoint != AnalyticsConst.PUSH_NOTIF) {
            FeaturePrefManager.getInstance().setExitWithoutAddBankAccount(true, paymentType, PaymentExitIntentData(null, customerId, bookId))
        }
        checkProfileCompletion()
    }

    fun checkProfileCompletion() {
        bookEntity = businessUseCase.getBusinessById(this.bookId)
        bookEntity?.run {
            if (!this.hasCompletedProfileWithoutOwnerName()) {
                profileIncompleteEvent.value = ProfileIncompleteEvent.ShowProfileDialog
            }
        }
    }

    fun setSelectedBank(bank: Bank) {
        selectedBank = bank
        viewState.value = currentViewState().copy(showBankError = false, showBlockedError = false)
    }

    private suspend fun canProceed(): Boolean {
        return when {
            selectedBank == null -> {
                setViewState(currentViewState().copy(showBankError = true, showBlockedError = false))
                false
            }
            currentViewState().bankAccountNumber.isEmpty() -> {
                setViewState(currentViewState().copy(showAccountInputError = true, showBlockedError = false))
                false
            }
            else -> true
        }
    }

    fun addBankAccount(setQrisBank: Boolean) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            if (canProceed().not()) return@withContext
            viewModelScope.launch {
                // show adding loader
                setViewState(currentViewState().copy(addingLoader = true, verificationLoader = false))

                // add bank account
                val response = if (isPaymentIn() || isForQris()) paymentUseCase.addMerchantBankAccount(
                        bookId,
                        buildBankAccount(setQrisBank)
                )
                else
                    paymentUseCase.addCustomerBankAccounts(
                            bookId,
                            customerId!!,
                            buildBankAccount(false)
                    )
                val propBuilder = AppAnalytics.PropBuilder()
                when (response) {
                    is ApiSuccessResponse -> {
                        val bankAccount = response.body
                        if (bankAccount.accountHolderName.isNullOrBlank() || bankAccount.accountNumber.isNullOrBlank()) {
                            setViewState(currentViewState().copy(
                                    verificationLoader = false,
                                    addingLoader = false,
                                    showBankError = false,
                                    showVerificationError = true,
                                    showAccountDetails = false, showBlockedError = false))
                            setEventStatus(Event.ShowErrorMessage(null))
                            propBuilder.put(
                                AnalyticsConst.BANK, response.body.bankCode
                            )
                            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                        } else {
                            setViewState(currentViewState().copy(
                                    verificationLoader = false,
                                    addingLoader = false,
                                    showAccountDetails = true,
                                    showVerificationError = false, showBlockedError = false)
                            )
                            AppAnalytics.trackEvent(if(isPaymentIn() || isForQris()) AnalyticsConst.EVENT_PAYMENT_SAVE_USER_BANK else AnalyticsConst.EVENT_PAYMENT_SAVE_RECIPIENT_BANK,
                                    AppAnalytics.PropBuilder().apply {
                                        put(AnalyticsConst.ENTRY_POINT, entryPoint)
                                        put(
                                            AnalyticsConst.STATUS,
                                            AnalyticsConst.STATUS_SUCCESS.uppercase(Locale.getDefault())
                                        )
                                        put(
                                            if (isPaymentIn() || isForQris()) AnalyticsConst.USER_BANK else if (isPaymentOut()) AnalyticsConst.RECIPIENT_BANK else AnalyticsConst.USER_BANK,
                                            bankAccount.bankCode
                                        )
                                        put(AnalyticsConst.PRODUCT, product)
                                        put(AnalyticsConst.TRANSACTION_ID, transactionId)
                                    }
                            )
                            propBuilder.put(
                                AnalyticsConst.BANK, response.body.bankCode
                            )
                            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_SUCCESS)
                            setEventStatus(Event.SetResultSuccess(response.body))
                            if(isPaymentIn() || isForQris()) FeaturePrefManager.getInstance().setHasBankAccount(true, bookId)
                            FeaturePrefManager.getInstance().setExitWithoutAddBankAccount(false, paymentType, null)
                        }
                    }
                    else -> {
                        val errorCode = if (response is ApiErrorResponse) response.code else ""
                        setViewState(currentViewState().copy(
                                verificationLoader = false,
                                addingLoader = false,
                                showBankError = false,
                                showVerificationError = true,
                                showAccountDetails = false,
                                showBlockedError = errorCode.equals(PpobConst.BLOCKED, ignoreCase = true)))
                        setEventStatus(Event.ShowErrorMessage(if (response is ApiErrorResponse) response.errorMessage else null, errorCode, BankValidationRequest(
                                bankCode = selectedBank?.bankCode!!,
                                accountNumber = currentViewState().bankAccountNumber
                        )))
                        propBuilder.put(
                            AnalyticsConst.BANK, selectedBank?.bankCode
                        )
                        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                    }
                }
                if (isForQris()) {
                    if (settingFirstTime) {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_QRIS_SET_BANK_ACCOUNT, propBuilder
                        )
                    } else {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_QRIS_CHANGE_BANK_ACCOUNT, propBuilder
                        )
                    }
                }
            }
        }
    }

    fun addRefundBankAccount(isChecked: Boolean) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            if (canProceed().not()) return@withContext
            viewModelScope.launch {
                // show adding loader
                setViewState(
                        currentViewState().copy(
                                addingLoader = true,
                                verificationLoader = false, showBlockedError = false
                        )
                )
                //adding refund bank account
                val refundPaymentMethods = listOf(buildRefundBankAccount(isChecked))
                val finproRefunds = FinproRefunds(paymentChannel = "BANK_TRANSFER",refundPaymentMethod = refundPaymentMethods)
                val response = finProUseCase.addRefundBankAccounts(
                    bookId,
                    finproRefunds
                )
                when (response) {
                    is ApiSuccessResponse -> {
                        val bankAccount = response.body.refundPaymentMethod?.first() ?: null
                        if (bankAccount?.accountHolderName.isNullOrBlank() || bankAccount?.accountNumber.isNullOrBlank()) {
                            setViewState(currentViewState().copy(
                                verificationLoader = false,
                                addingLoader = false,
                                showBankError = false,
                                showVerificationError = true,
                                showAccountDetails = false, showBlockedError = false))
                            setEventStatus(Event.ShowErrorMessage(null))
                        } else {
                            setViewState(currentViewState().copy(
                                verificationLoader = false,
                                addingLoader = false,
                                showAccountDetails = true,
                                showVerificationError = false, showBlockedError = false)
                            )
                            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_SAVE_REFUND_BANK,
                                AppAnalytics.PropBuilder().apply {
                                    put(AnalyticsConst.ENTRY_POINT, entryPoint)
                                    put(
                                        AnalyticsConst.STATUS,
                                        AnalyticsConst.STATUS_SUCCESS.uppercase(Locale.getDefault())
                                    )
                                    put(
                                        if (isPaymentIn() || isForQris()) AnalyticsConst.USER_BANK else if (isPaymentOut()) AnalyticsConst.RECIPIENT_BANK else AnalyticsConst.USER_BANK,
                                        bankAccount?.bankCode
                                    )
                                    put(AnalyticsConst.BUSINESS_CATEGORY, bookEntity?.bookType)
                                    put(AnalyticsConst.PRODUCT, product)
                                    put(AnalyticsConst.TRANSACTION_ID, transactionId)
                                }
                            )
                            setEventStatus(Event.AddRefundBankSuccess(bankAccount!!))
                        }
                    }
                    else -> {}
                }
            }
        }
    }

    fun verifyBankAccount(addingBankAccountFor: PaymentConst.BankAccountOwner) = launch {
        withContext(Dispatchers.IO) {
            if (canProceed().not()) return@withContext
            selectedBank?.let { selectedBank ->
                viewModelScope.launch {
                    setEventStatus(Event.StartTimer)
                    setViewState(
                        currentViewState().copy(
                            verificationLoader = true,
                            addingLoader = false,
                            showBlockedError = false
                        )
                    )
                    val response = paymentUseCase.validateBankAccount(
                        bookId,
                        BankValidationRequest(
                            bankCode = selectedBank.bankCode,
                            accountNumber = currentViewState().bankAccountNumber,
                            isPaymentIn = isPaymentIn() || isForQris(),
                            proposedQrisBank = isForQris(),
                            isRefundableBank = isPaymentPpob(),
                            accountOwner = addingBankAccountFor
                        )
                    )
                    val event =
                        if (isPaymentIn() || isForQris()) AnalyticsConst.EVENT_PAYMENT_CHECK_USER_BANK else if (isPaymentOut()) AnalyticsConst.EVENT_PAYMENT_CHECK_RECIPIENT_BANK else AnalyticsConst.EVENT_PAYMENT_CHECK_REFUND_BANK

                    when (response) {
                        is ApiSuccessResponse -> {
                            setEventStatus(Event.StopTimer)
                            bankAccount = response.body
                            bankAccount?.let {
                                if (it.isDisabled.isTrue) {
                                    setViewState(
                                        currentViewState().copy(
                                            verificationLoader = false,
                                            showBankError = false,
                                            showVerificationError = true,
                                            showAccountDetails = false,
                                            showBlockedError = true,
                                            isButtonEnabled = false
                                        )
                                    )
                                    setEventStatus(
                                        Event.ShowErrorMessage(
                                            null,
                                            PpobConst.NOT_SUPPORTED,
                                            BankValidationRequest(
                                                bankCode = selectedBank.bankCode,
                                                accountNumber = currentViewState().bankAccountNumber
                                            )
                                        )
                                    )
                                    logMatchingResultEvent(
                                        autoMatching = (it.matchingStatus != null),
                                        matchingStatus = if (it.matchingStatus.isTrue) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAIL,
                                        matchingScore = it.matchingScore,
                                        owner = addingBankAccountFor.name,
                                        failureReason = AnalyticsConst.ACCOUNT_DISABLED
                                    )
                                } else {
                                    if (it.accountHolderName.isNullOrBlank() || it.accountNumber.isNullOrBlank()) {
                                        setViewState(
                                            currentViewState().copy(
                                                verificationLoader = false,
                                                showBankError = false,
                                                showVerificationError = true,
                                                showAccountDetails = false,
                                                isButtonEnabled = false, showBlockedError = false
                                            )
                                        )
                                        setEventStatus(Event.ShowErrorMessage(null))
                                        AppAnalytics.trackEvent(event,
                                            AppAnalytics.PropBuilder().apply {
                                                put(AnalyticsConst.ENTRY_POINT, entryPoint)
                                                put(
                                                    AnalyticsConst.STATUS,
                                                    AnalyticsConst.STATUS_FAILURE
                                                )
                                                put(AnalyticsConst.PRODUCT, product)
                                                put(AnalyticsConst.TRANSACTION_ID, transactionId)
                                            }
                                        )
                                        logMatchingResultEvent(
                                            autoMatching = (it.matchingStatus != null),
                                            matchingStatus = if (it.matchingStatus.isTrue) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAIL,
                                            matchingScore = it.matchingScore,
                                            owner = addingBankAccountFor.name,
                                            failureReason = AnalyticsConst.MISSING_INFO
                                        )
                                    } else {
                                        setViewState(
                                            currentViewState().copy(
                                                verificationLoader = false,
                                                showAccountDetails = true,
                                                showVerificationError = false,
                                                isButtonEnabled = if (isForQris()) true else !(bankAccount?.matchingStatus.isFalse || bankAccount?.accountAlreadyExists.isTrue),
                                                showBlockedError = false
                                            )
                                        )
                                        currentSelectedAccount = it
                                        setEventStatus(Event.ShowBankAccountDetail(it))
                                        AppAnalytics.trackEvent(event,
                                            AppAnalytics.PropBuilder().apply {
                                                put(AnalyticsConst.ENTRY_POINT, entryPoint)
                                                put(
                                                    AnalyticsConst.STATUS,
                                                    AnalyticsConst.STATUS_SUCCESS.uppercase()
                                                )
                                                put(AnalyticsConst.PRODUCT, product)
                                                put(AnalyticsConst.TRANSACTION_ID, transactionId)
                                            }
                                        )
                                        logMatchingResultEvent(
                                            autoMatching = (it.matchingStatus != null),
                                            matchingStatus = if (it.matchingStatus.isTrue) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAIL,
                                            matchingScore = it.matchingScore,
                                            owner = addingBankAccountFor.name,
                                            failureReason = null
                                        )
                                    }
                                }
                            }
                        }
                        else -> {
                            setEventStatus(Event.StopTimer)
                            val errorCode = if (response is ApiErrorResponse) response.code else ""
                            setViewState(
                                currentViewState().copy(
                                    verificationLoader = false,
                                    showBankError = false,
                                    showVerificationError = (!errorCode.equals(
                                        PpobConst.BLOCKED,
                                        ignoreCase = true
                                    ) && !errorCode.equals(PpobConst.IN_ACTIVE, ignoreCase = true)),
                                    showAccountDetails = false,
                                    showBlockedError = errorCode.equals(
                                        PpobConst.BLOCKED,
                                        ignoreCase = true
                                    ),
                                    isButtonEnabled = false
                                )
                            )
                            setEventStatus(
                                Event.ShowErrorMessage(
                                    if (response is ApiErrorResponse) response.errorMessage else null,
                                    errorCode,
                                    BankValidationRequest(
                                        bankCode = selectedBank?.bankCode!!,
                                        accountNumber = currentViewState().bankAccountNumber
                                    )
                                )
                            )
                            AppAnalytics.trackEvent(event,
                                AppAnalytics.PropBuilder().apply {
                                    put(AnalyticsConst.ENTRY_POINT, entryPoint)
                                    put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAILURE)
                                    put(AnalyticsConst.PRODUCT, product)
                                    put(AnalyticsConst.TRANSACTION_ID, transactionId)
                                }
                            )
                            logMatchingResultEvent(
                                autoMatching = null,
                                matchingStatus = null,
                                matchingScore = null,
                                owner = addingBankAccountFor.name,
                                failureReason = AnalyticsConst.API_FAILURE
                            )
                        }
                    }
                }
            }
        }
    }

    private fun logMatchingResultEvent(
        autoMatching: Boolean?, matchingStatus: String?, matchingScore: Double?, owner: String,
        failureReason: String?
    ) {
        val entryPointProp =
            if (settingFirstTime) AnalyticsConst.ONBOARDING else AnalyticsConst.POST_ONBOARDING
        val featureProp = if (isForQris()) AnalyticsConst.QRIS else AnalyticsConst.PAYMENT
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_MATCHING_INPUT_BANK_ACCOUNT,
            AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.AUTO_MATCHING, autoMatching)
                put(AnalyticsConst.MATCHING_STATUS, matchingStatus)
                put(AnalyticsConst.MATCHING_SCORE, matchingScore)
                put(AnalyticsConst.ENTRY_POINT, entryPointProp)
                put(AnalyticsConst.FEATURE, featureProp)
                put(AnalyticsConst.OWNER, owner)
                put(AnalyticsConst.FAIL_REASON, failureReason)
            }
        )
    }

    private fun buildBankAccount(setQrisBank: Boolean): BankAccount {
        return BankAccount(
            bankCode = selectedBank?.bankCode!!,
            accountNumber = currentViewState().bankAccountNumber,
            isQrisBank = isForQris() && setQrisBank
        )
    }

    private fun buildRefundBankAccount(isChecked: Boolean): RefundBankAccount {
        val value = System.currentTimeMillis().toString()
        return RefundBankAccount(
            accountId = bookId,
            accountNumber = bankAccount?.accountNumber,
            accountHolderName = bankAccount?.accountHolderName,
            customerId = bankAccount?.customerId,
            bankCode = bankAccount?.bankCode,
            bankName = bankAccount?.bankName ?: "",
            bankAccountId = if(bankAccount?.bankAccountId.toString().isNullOrBlank()) value else bankAccount?.bankAccountId.toString(),
            isSelected = isChecked,
            id = if(bankAccount?.bankAccountId.toString().isNullOrBlank()) value else bankAccount?.bankAccountId.toString()
        )
    }

    fun onAccountNumberChanged(accountNumber: String) {
        viewState.value = currentViewState().copy(
                bankAccountNumber = accountNumber,
                showAccountInputError = false,
                showBankError = false,
                showVerificationError = false, showBlockedError = false)
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    private suspend fun setViewState(vs: ViewState) = withContext(Dispatchers.Main) {
        viewState.value = vs
    }

    fun isPaymentIn(): Boolean = paymentType == PaymentConst.TYPE_PAYMENT_IN

    fun isPaymentOut(): Boolean = paymentType == PaymentConst.TYPE_PAYMENT_OUT

    fun isPaymentPpob(): Boolean = paymentType == PaymentConst.TYPE_PPOB

    fun isForQris(): Boolean = paymentType == PaymentConst.TYPE_QRIS_INT
}
