package com.bukuwarung.payments

import android.content.Context
import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.BottomsheetFilterPaymentBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.InjectorUtils
import com.bukuwarung.payments.viewmodels.PaymentHistoryPagerViewModel
import com.bukuwarung.utils.getClassTag
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.showView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog


class PaymentFilterBottomSheet : BaseBottomSheetDialogFragment() {
    private lateinit var binding: BottomsheetFilterPaymentBinding
    private var filterDto: PaymentFilterDto? = null
    private var inMemoryTypeFilters = arrayListOf<String>()
    private var inMemoryStatusFilters = arrayListOf<String>()
    private var listener: Callback? = null

    private val viewModel: PaymentHistoryPagerViewModel by activityViewModels {
        InjectorUtils.providePaymentsViewModelFactory(requireContext(), customerId = "", bookId = "")
    }

    interface Callback {
        fun onFilterBsDismiss(filterApplied: Boolean, updatedFilterDto: PaymentFilterDto?)
    }

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        binding = BottomsheetFilterPaymentBinding.inflate(layoutInflater, container, false)

        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? Callback }
        if (context is Callback) listener = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.viewState.observe(viewLifecycleOwner, Observer {
            filterDto =
                if (arguments?.getParcelable(FILTERS) as? PaymentFilterDto != null) {
                    arguments?.getParcelable(FILTERS) as? PaymentFilterDto
                } else {
                    it.paymentFilterDto
                }
            // Duplicate applied filters to local list
            setFilterState()
        })

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet = dialog.findViewById<View>(R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }

        }
        isCancelable = false

        binding.apply {
            btnReset.setOnClickListener {
                // to reset the view
                inMemoryTypeFilters = getListOfAllTypes()
                inMemoryStatusFilters = getListOfAllStatus()
                updateButtons()
            }
            btnClose.setOnClickListener {
                dismiss()
                listener?.onFilterBsDismiss(false, null)
            }
            btnConfirmFilter.setOnClickListener {
                // only triggered if user click confirmation button
                val confirmProp = AppAnalytics.PropBuilder().put(AnalyticsConst.ID, AnalyticsConst.FILTER_CONFIRM)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_LOOKUP, confirmProp)

                var updatedFilterDto:PaymentFilterDto? = null
                filterDto?.let { it1 ->
                    val newFilterDto = it1.copy(
                        typeFilters = inMemoryTypeFilters, statusFilters = inMemoryStatusFilters
                    )
                    updatedFilterDto = newFilterDto
                    viewModel.submitFilter(newFilterDto)
                }
                dismiss()
                listener?.onFilterBsDismiss(true, updatedFilterDto)
            }

            // Hide saldo filters if not saldo not enabled
            PaymentPrefManager.getInstance().getSaldoEnabledState().let {
                if (it) {
                    binding.cbTypeSaldoIn.showView()
                    binding.cbTypeSaldoOut.showView()
                    binding.cbTypeSaldoRedemption.showView()
                    binding.typeSaldoInSeparator.showView()
                    binding.typeSaldoOutSeparator.showView()
                    binding.typeSaldoRedemptionSeparator.showView()
                } else {
                    binding.cbTypeSaldoIn.hideView()
                    binding.cbTypeSaldoOut.hideView()
                    binding.cbTypeSaldoRedemption.hideView()
                    binding.typeSaldoInSeparator.hideView()
                    binding.typeSaldoOutSeparator.hideView()
                    binding.typeSaldoRedemptionSeparator.hideView()
                }
            }

            cbTypeAll.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeOut.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeIn.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeSaldoIn.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeSaldoOut.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeSaldoRedemption.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypePulsa.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypePaketData.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeListrik.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeEwallet.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeVoucherGame.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeBpjs.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypePdam.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeMultifinance.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeInternetDanTvCable.setOnCheckedChangeListener(::checkedChangeListener)
            cbTypeVehicleTax.setOnCheckedChangeListener(::checkedChangeListener)

            cbTypeAllStatus.setOnCheckedChangeListener(::checkedChangeListener)
            cbSuccess.setOnCheckedChangeListener(::checkedChangeListener)
            cbWaiting.setOnCheckedChangeListener(::checkedChangeListener)
            cbFailed.setOnCheckedChangeListener(::checkedChangeListener)
            cbExpired.setOnCheckedChangeListener(::checkedChangeListener)
            cbHold.setOnCheckedChangeListener(::checkedChangeListener)
        }
    }

    private fun setFilterState() {
        if (filterDto == null) return
        inMemoryTypeFilters = filterDto!!.typeFilters.toMutableList() as ArrayList
        inMemoryStatusFilters = filterDto!!.statusFilters.toMutableList() as ArrayList
        updateButtons()
    }

    private fun checkedChangeListener(button: CompoundButton, isChecked: Boolean) {
        if (button.isPressed) {
            when (button.id) {
                R.id.cb_type_all -> checkedChangeHandlerType(PaymentFilterDto.TYPE_ALL, isChecked)
                R.id.cb_type_out -> checkedChangeHandlerType(PaymentFilterDto.TYPE_OUT, isChecked)
                R.id.cb_type_in -> checkedChangeHandlerType(PaymentFilterDto.TYPE_IN, isChecked)
                R.id.cb_type_saldo_in -> checkedChangeHandlerType(PaymentFilterDto.TYPE_SALDO_IN, isChecked)
                R.id.cb_type_saldo_out -> checkedChangeHandlerType(PaymentFilterDto.TYPE_SALDO_OUT, isChecked)
                R.id.cb_type_saldo_redemption -> checkedChangeHandlerType(PaymentFilterDto.TYPE_SALDO_REDEMPTION, isChecked)
                R.id.cb_type_pulsa -> checkedChangeHandlerType(PaymentFilterDto.TYPE_PULSA, isChecked)
                R.id.cb_type_paket_data -> checkedChangeHandlerType(PaymentFilterDto.TYPE_PAKET_DATA, isChecked)
                R.id.cb_type_listrik -> checkedChangeHandlerType(PaymentFilterDto.TYPE_LISTRIK, isChecked)
                R.id.cb_type_ewallet -> checkedChangeHandlerType(PaymentFilterDto.TYPE_EWALLET, isChecked)
                R.id.cb_type_voucher_game -> checkedChangeHandlerType(PaymentFilterDto.TYPE_GAMING_VOUCHER, isChecked)
                R.id.cb_type_bpjs -> checkedChangeHandlerType(PaymentFilterDto.TYPE_BPJS, isChecked)
                R.id.cb_type_pdam -> checkedChangeHandlerType(PaymentFilterDto.TYPE_PDAM, isChecked)
                R.id.cb_type_multifinance -> checkedChangeHandlerType(PaymentFilterDto.TYPE_MULTIFINANCE, isChecked)
                R.id.cb_type_internet_dan_tv_cable -> checkedChangeHandlerType(PaymentFilterDto.TYPE_INTERNET_DAN_TV_CABLE, isChecked)
                R.id.cb_type_vehicle_tax -> checkedChangeHandlerType(PaymentFilterDto.TYPE_VEHICLE_TAX, isChecked)

                R.id.cb_type_all_status -> checkedChangeHandlerStatus(PaymentFilterDto.STATUS_ALL, isChecked)
                R.id.cb_success -> checkedChangeHandlerStatus(PaymentFilterDto.STATUS_COMPLETED, isChecked)
                R.id.cb_waiting -> checkedChangeHandlerStatus(PaymentFilterDto.STATUS_PENDING, isChecked)
                R.id.cb_failed -> checkedChangeHandlerStatus(PaymentFilterDto.STATUS_FAILED, isChecked)
                R.id.cb_expired -> checkedChangeHandlerStatus(PaymentFilterDto.STATUS_EXPIRED, isChecked)
                R.id.cb_hold -> checkedChangeHandlerStatus(PaymentFilterDto.STATUS_HOLD, isChecked)
            }
        }
    }

    private fun checkedChangeHandlerType(filterId: String, isChecked: Boolean) {
        filterDto?.let {
            if (isChecked) {
                if (filterId == PaymentFilterDto.TYPE_ALL) {
                    // Add all the types to the filterDto.paymentType
                    inMemoryTypeFilters = getListOfAllTypes()
                } else {
                    // Remove TYPE_ALL if present in the filter list
                    if (inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_ALL)) {
                        inMemoryTypeFilters.remove(PaymentFilterDto.TYPE_ALL)
                    }
                    if (!inMemoryTypeFilters.contains(filterId)) {
                        inMemoryTypeFilters.add(filterId)
                    }
                    // Check if need to select TYPE_ALL again (In case, this was the last unselected child)
                    if (
                        inMemoryTypeFilters.size == getListOfTypesWoAll().size
                        && !inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_ALL)
                    ) {
                        inMemoryTypeFilters.add(PaymentFilterDto.TYPE_ALL)
                    }
                }
            } else {
                if (filterId == PaymentFilterDto.TYPE_ALL) {
                    // Remove all the types to the filterDto.paymentType
                    inMemoryTypeFilters.clear()
                } else {
                    // Remove TYPE_ALL if present in the filter list
                    if (inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_ALL)) {
                        inMemoryTypeFilters.remove(PaymentFilterDto.TYPE_ALL)
                    }
                    inMemoryTypeFilters.remove(filterId)
                }
            }
            updateButtons()
        }
    }

    private fun checkedChangeHandlerStatus(filterId: String, isChecked: Boolean) {
        filterDto?.let {
            if (isChecked) {
                if (filterId == PaymentFilterDto.STATUS_ALL) {
                    // Add all the types to the filterDto.filterType
                    inMemoryStatusFilters = getListOfAllStatus()
                } else {
                    // Remove FILTER_ALL if present in the filter list
                    if (inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_ALL)) {
                        inMemoryStatusFilters.remove(PaymentFilterDto.STATUS_ALL)
                    }
                    if (!inMemoryStatusFilters.contains(filterId)) {
                        inMemoryStatusFilters.add(filterId)
                    }
                    // Check if need to select TYPE_ALL again (In case, this was the last unselected child)
                    if (
                        inMemoryStatusFilters.size == getListOfStatusWoAll().size
                        && !inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_ALL)
                    ) {
                        inMemoryStatusFilters.add(PaymentFilterDto.STATUS_ALL)
                    }
                }
            } else {
                if (filterId == PaymentFilterDto.STATUS_ALL) {
                    // Remove all the types to the filterDto.filterType
                    inMemoryStatusFilters.clear()
                } else {
                    // Remove FILTER_ALL if present in the filter list
                    if (inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_ALL)) {
                        inMemoryStatusFilters.remove(PaymentFilterDto.STATUS_ALL)
                    }
                    inMemoryStatusFilters.remove(filterId)
                }
            }
            updateButtons()
        }
    }

    private fun updateButtons() {
        binding.apply {
            binding.cbTypeAll.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_ALL).isTrue
            binding.cbTypeOut.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_OUT).isTrue
            binding.cbTypeIn.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_IN).isTrue
            binding.cbTypeSaldoIn.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_SALDO_IN).isTrue
            binding.cbTypeSaldoOut.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_SALDO_OUT).isTrue
            binding.cbTypeSaldoRedemption.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_SALDO_REDEMPTION).isTrue
            binding.cbTypePulsa.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_PULSA).isTrue
            binding.cbTypePaketData.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_PAKET_DATA).isTrue
            binding.cbTypeListrik.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_LISTRIK).isTrue
            binding.cbTypeEwallet.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_EWALLET).isTrue
            binding.cbTypeVoucherGame.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_GAMING_VOUCHER).isTrue
            binding.cbTypeBpjs.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_BPJS).isTrue
            binding.cbTypePdam.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_PDAM).isTrue
            binding.cbTypeMultifinance.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_MULTIFINANCE).isTrue
            binding.cbTypeInternetDanTvCable.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_INTERNET_DAN_TV_CABLE).isTrue
            binding.cbTypeVehicleTax.isChecked = inMemoryTypeFilters.contains(PaymentFilterDto.TYPE_VEHICLE_TAX).isTrue

            binding.cbTypeAllStatus.isChecked = inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_ALL).isTrue
            binding.cbSuccess.isChecked = inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_COMPLETED).isTrue
            binding.cbWaiting.isChecked = inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_PENDING).isTrue
            binding.cbFailed.isChecked = inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_FAILED).isTrue
            binding.cbExpired.isChecked = inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_EXPIRED).isTrue
            binding.cbHold.isChecked = inMemoryStatusFilters.contains(PaymentFilterDto.STATUS_HOLD).isTrue

            binding.btnConfirmFilter.isEnabled = (inMemoryTypeFilters.isNotEmpty().isTrue
                    && inMemoryStatusFilters.isNotEmpty().isTrue)
        }
    }

    companion object {
        const val FILTERS="filters"
        fun createInstance(fr: FragmentManager, filterDto: PaymentFilterDto?) = PaymentFilterBottomSheet().apply {
            arguments = bundleOf(FILTERS to  filterDto)
        }.also {
            it.show(fr, getClassTag())
        }
    }
}