package com.bukuwarung.payments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.databinding.PaymentPendingInfoBottomsheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.*

class PaymentPendingInfoBottomsheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val IS_PENDING_TRX_TIME_EXCEED = "is_pending_trx_time_exceed"
        fun createInstance(isPendingTrxTimeExceed: Boolean = false) =
            PaymentPendingInfoBottomsheet().apply {
                this.arguments = Bundle().apply {
                    putBoolean(IS_PENDING_TRX_TIME_EXCEED, isPendingTrxTimeExceed)
                }
            }
    }

    private var _binding: PaymentPendingInfoBottomsheetBinding? = null
    private val binding get() = _binding!!
    private var iCommunicator: ICommunicator? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        iCommunicator = context as? ICommunicator
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = PaymentPendingInfoBottomsheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val isPendingTrxTimeExceed = arguments?.getBoolean(IS_PENDING_TRX_TIME_EXCEED) ?: false
        with(binding) {
            with(btnConfirm) {
                if (isPendingTrxTimeExceed) {
                    setText(getString(R.string.create_help_ticket))
                    setSingleClickListener {
                        iCommunicator?.showAssistScreen()
                        dismiss()
                    }
                } else {
                    setText(getString(R.string.understand))
                    setSingleClickListener {
                        dismiss()
                    }
                }

            }
            tvRefundTime.text = getString(
                R.string.wait_pending_trx_message,
                RemoteConfigUtils.getPaymentPendingTimeInMinutes().toString()
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface ICommunicator {
        fun showAssistScreen()
    }
}