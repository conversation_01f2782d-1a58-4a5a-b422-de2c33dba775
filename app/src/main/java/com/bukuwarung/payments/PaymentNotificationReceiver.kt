package com.bukuwarung.payments

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.managers.local_notification.LocalNotificationData
import com.bukuwarung.managers.local_notification.LocalNotificationIcon
import com.bukuwarung.managers.local_notification.LocalNotificationManager.Companion.showDefaultNotification
import com.bukuwarung.managers.local_notification.LocalNotificationStyle
import com.bukuwarung.payments.checkout.PaymentCheckoutActivity
import com.bukuwarung.preference.FeaturePrefManager
import java.util.*

class PaymentNotificationReceiver : BroadcastReceiver() {

    companion object {
        private const val PAYMENT_TYPE = "paymentType"
        private const val CUSTOMER_ID = "customerId"
        private const val BOOK_ID = "bookId"
        private const val PAYMENT_REQUEST_CODE = 99

        fun createIntent(paymentType: Int, customerId: String?, bookId: String? = null): Intent {
            val i = Intent(Application.getAppContext(), PaymentNotificationReceiver::class.java)
            i.putExtra(PAYMENT_TYPE, paymentType)
            i.putExtra(CUSTOMER_ID, customerId)
            i.putExtra(BOOK_ID, bookId)
            return i
        }
    }

    override fun onReceive(context: Context, intent: Intent) {
        val paymentType = intent.getIntExtra(PAYMENT_TYPE, -1)
        val count = FeaturePrefManager.getInstance().getCountPaymentPushNotification(paymentType)
        if (count > 2) return
        val customerId = intent.getStringExtra(CUSTOMER_ID)
        val bookId = intent.getStringExtra(BOOK_ID)
        val notificationStartTime = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)
        if (notificationStartTime !in 7..20) return
        var localNotificationData: LocalNotificationData? = null
        if (paymentType == PaymentConst.TYPE_PAYMENT_IN) {
            return
        } else if (paymentType == PaymentConst.TYPE_PAYMENT_OUT) {
            localNotificationData = LocalNotificationData(
                context.getString(R.string.payment_out_notification_heading),
                context.getString(R.string.payment_out_notification_message),
                LocalNotificationIcon.DEFAULT
            )
        }
        if (localNotificationData == null) return
        val backIntent = Intent(context, MainActivity::class.java)
        backIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        val pendingIntent =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                PendingIntent.getActivities(
                    context,
                    PAYMENT_REQUEST_CODE,
                    arrayOf(
                        backIntent,
                        PaymentCheckoutActivity.createIntent(
                            context, paymentType.toString(), customerId, AnalyticsConst.PUSH_NOTIF, bookId
                        )
                    ), PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getActivities(
                    context,
                    PAYMENT_REQUEST_CODE,
                    arrayOf(
                        backIntent,
                        PaymentCheckoutActivity.createIntent(
                            context, paymentType.toString(), customerId, AnalyticsConst.PUSH_NOTIF, bookId
                        )
                    ), PendingIntent.FLAG_UPDATE_CURRENT
                )
            }
        showDefaultNotification(
            Application.getAppContext(),
            localNotificationData,
            LocalNotificationStyle.BIG_TEXT,
            pendingIntent
        )
        FeaturePrefManager.getInstance().setCountPaymentPushNotification(paymentType, count + 1)
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_NOTIFICATION_RECEIVED, AppAnalytics.PropBuilder()
                .put(AnalyticsConst.ID, AnalyticsConst.PENDING_PAYMENT_OUT)
        )
    }
}