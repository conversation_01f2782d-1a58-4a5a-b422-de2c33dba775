package com.bukuwarung.payments

import android.os.Bundle
import android.view.View
import androidx.fragment.app.FragmentManager
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BasePromptDialogFragment

class DeleteBankPromptDialog(
        onPromptClicked: ((Boolean) -> Unit)
): BasePromptDialogFragment(
        onPromptClicked
) {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setTitle(requireContext().resources.getString(R.string.delete_bank_prompt_title))
        setContent(requireContext().resources.getString(R.string.delete_bank_prompt_body))
    }

    companion object {
        fun show(
                onPromptClicked: ((Boolean) -> Unit),
                manager: FragmentManager
        ) {
            DeleteBankPromptDialog(onPromptClicked).show(manager, "delete-bank-dialog")
        }
    }

}