package com.bukuwarung.payments.history

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.BankingUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.OrdersUseCase
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.viewmodels.BaseViewModel
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject


class OrderHistoryViewModel @Inject constructor(
    private val finproUseCase: FinproUseCase,
    private val ordersUseCase: OrdersUseCase,
    private val bankingUseCase: BankingUseCase
) : BaseViewModel() {

    companion object {
        fun sortByDescending(orders: List<PaymentHistory>) = orders.sortedByDescending {
            val dateString = DateTimeUtilsKt.getStringFromUtc(
                it.timestamp, DateTimeUtilsKt.DD_MMM_YYYY
            )
            val dateLong = DateTimeUtilsKt.getTimestampFromDateString(
                dateString, DateTimeUtilsKt.DD_MMM_YYYY
            )
            dateLong
        }

        fun addDateHeaders(
            orders: List<PaymentHistory>,
            orderDates: ArrayList<String>,
            shouldAddDateHeaders: Boolean = true
        ): ArrayList<OrderHistoryData> {
            val ordersData = arrayListOf<OrderHistoryData>()
            orders.map {
                val dateString = DateTimeUtilsKt.getStringFromUtc(
                    it.timestamp, DateTimeUtilsKt.DD_MMM_YYYY
                )
                if (!orderDates.contains(dateString) && shouldAddDateHeaders) {
                    orderDates.add(dateString)
                    ordersData.add(
                        OrderHistoryData(
                            orderData = null,
                            formattedDate = dateString,
                            timestamp = it.timestamp,
                            viewType = OrderHistoryAdapter.Companion.ViewType.DATE_HEADER
                        )
                    )
                }
                ordersData.add(
                    OrderHistoryData(
                        orderData = it,
                        formattedDate = null,
                        timestamp = null,
                        viewType = OrderHistoryAdapter.Companion.ViewType.ORDER
                    )
                )
            }
            return ordersData
        }

        fun getExistingOrderDates(
            activeTab: PaymentConst.HISTORY_TABS,
            orderDatesMap: HashMap<PaymentConst.HISTORY_TABS, ArrayList<String>>
        ): ArrayList<String> {
            val existingDates = orderDatesMap[activeTab]
            return if (existingDates == null) {
                val datesArray = arrayListOf<String>()
                orderDatesMap[activeTab] = datesArray
                datesArray
            } else {
                existingDates
            }
        }
    }

    data class ViewState(
        val loading: Boolean = true,
        val errorMessage: String? = null,
        val cashbackLoading: Boolean = true,
        val cashbackError: String? = null,
    )

    sealed class Event {
        object ClearSearch : Event()
        data class LinkedItemsFetched(var adapterPos: Int) : Event()
        data class ApiError(var errorMessage: String?, var adapterPos: Int) : Event()
    }

    val filtersState = hashMapOf(
        PaymentConst.HISTORY_TABS.ALL to getDefaultFilters(PaymentConst.HISTORY_TABS.ALL),
        PaymentConst.HISTORY_TABS.PPOB to getDefaultFilters(PaymentConst.HISTORY_TABS.PPOB),
        PaymentConst.HISTORY_TABS.PEMBAYARAN to getDefaultFilters(PaymentConst.HISTORY_TABS.PEMBAYARAN),
        PaymentConst.HISTORY_TABS.SALDO to getDefaultFilters(PaymentConst.HISTORY_TABS.SALDO),
        PaymentConst.HISTORY_TABS.SALDOBONUS to getDefaultFilters(PaymentConst.HISTORY_TABS.SALDOBONUS),
    )

    private var bukuOrigin: String? = null
    private val _filtersStateLive = MutableLiveData(filtersState)
    val filtersStateLive: LiveData<HashMap<PaymentConst.HISTORY_TABS, PaymentFilterDtoX>> =
        _filtersStateLive

    private val _event = MutableLiveData<Event>()
    val event: LiveData<Event> = _event

    var activeTab: PaymentConst.HISTORY_TABS = PaymentConst.HISTORY_TABS.ALL

    private val orderDatesMap = hashMapOf<PaymentConst.HISTORY_TABS, ArrayList<String>>()

    val linkedOrdersMap = hashMapOf<String, LinkedOrdersData>()

    private val _expiringCashbacks = MutableLiveData<ExpiringCashbackInfo>()
    val expiringCashbacks: LiveData<ExpiringCashbackInfo> = _expiringCashbacks

    private val _pagingStatus = MutableLiveData<PagingStatus>()
    val pagingStatus: LiveData<PagingStatus> = _pagingStatus

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    val config = RemoteConfigUtils.getPaymentConfigs()
    private var customerId: String? = null
    private var bookId: String = EMPTY_STRING
    private var billerCode: String? = null
    private val typeDigitalProductChildren = arrayListOf<String>().apply {
        addAll(
            config.historyFiltersNew?.ppob?.products?.flatMap { it.filters }?.map { it.key }
                .orEmpty()
        )
    }
    private var ordersPagingSource: OrdersPagingSource? = null
    val ordersPagedData: LiveData<PagingData<OrderHistoryData>> get() = _ordersPagedData
    private val _ordersPagedData = MutableLiveData<PagingData<OrderHistoryData>>()

    private var queryParams = OrdersPagingSource.QueryParams(
        accountId = SessionManager.getInstance().businessId
    )

    fun getApplicableFilters(): FilterValues? {
        return when (activeTab) {
            PaymentConst.HISTORY_TABS.ALL -> config.historyFiltersNew?.all
            PaymentConst.HISTORY_TABS.PPOB -> config.historyFiltersNew?.ppob
            PaymentConst.HISTORY_TABS.PEMBAYARAN -> config.historyFiltersNew?.pembayaran
            PaymentConst.HISTORY_TABS.SALDO -> config.historyFiltersNew?.saldo
            PaymentConst.HISTORY_TABS.SALDOBONUS -> config.historyFiltersNew?.cashback
        }
    }

    /**
     * This sets the incoming filters to the activity
     * Here we check if incoming array has "parent" types i.e. DIGITAL_PRODUK or PEMBAYARAN
     * Then we add all the children to the applied filters list.
     */
    fun init(
        bookId: String, preselectedTab: PaymentConst.HISTORY_TABS?,
        types: ArrayList<String>?, status: ArrayList<String>?,
        startDate: Long?, endDate: Long?,
        customerId: String? = null, billerCode: String? = null,
        datePreset: PaymentConst.DATE_PRESET?
    ) {
        preselectedTab?.let { activeTab = it }
        this.bookId = bookId
        this.customerId = customerId
        this.billerCode = billerCode
        types?.let {
            getApplicableFilters()?.products?.flatMap { it.filters }?.map {
                // Here we are changing checked status of children, if 'types' contains parent type
                if (types.contains(PaymentConst.TYPE_DIGITAL_PRODUCT)) {
                    if (!it.isChecked) {
                        it.isChecked = typeDigitalProductChildren.contains(it.key)
                    }
                }
                if (types.contains(PaymentConst.TYPE_PEMBAYARAN)) {
                    if (!it.isChecked) {
                        it.isChecked = PaymentConst.TYPE_PEMBAYARAN_CHILDREN.contains(it.key)
                    }
                }
                types.map { type ->
                    if (!it.isChecked) {
                        it.isChecked = it.key == type
                    }
                }
            }
        }
        status?.let {
            getApplicableFilters()?.status?.flatMap { it.filters }?.map {
                status.map { type ->
                    if (!it.isChecked) {
                        it.isChecked = it.key == type
                    }
                }
            }
        }
        datePreset?.let { preset ->
            getApplicableFilters()?.date?.find { it.presetValue == preset }
                ?.let { it.isChecked = true }
        }
        Utilities.safeLet(startDate, endDate) { start, end ->
            if (start == 0L || end == 0L) return@safeLet
            getApplicableFilters()?.date?.find { it.presetValue == PaymentConst.DATE_PRESET.CUSTOM_RANGE }
                ?.let {
                    it.isChecked = true
                    it.startDate = start
                    it.endDate = end
                }
        }
        applyTypeFilters(false)
        applyStatusFilters(false)
        applyDateFilters(false)
        fetchOrders(activeTab)
    }

    fun onTabSelected(tab: PaymentConst.HISTORY_TABS) {
        activeTab = tab
        queryParams.searchQuery = filtersState[activeTab]?.searchQuery
        applyDateFilters(false)
        invalidatePagingSource(tab)
    }

    fun applyTypeFilters(fetchResults: Boolean = true) {
        // Update appliedTypeFilters from filterValues
        filtersState[activeTab]?.typeFilters?.apply {
            clear()
            getApplicableFilters()?.products?.flatMap { it.filters }?.map {
                addIf(it.key, it.isChecked)
            }
            // If typeFilters are empty and we are on any specific tab, we need to add parent
            // filter types otherwise it'll not filter based on the active tab
            if (isEmpty()) {
                when (activeTab) {
                    PaymentConst.HISTORY_TABS.ALL -> {}
                    PaymentConst.HISTORY_TABS.PPOB -> add(PaymentConst.TYPE_DIGITAL_PRODUCT)
                    PaymentConst.HISTORY_TABS.PEMBAYARAN -> add(PaymentConst.TYPE_PEMBAYARAN)
                    PaymentConst.HISTORY_TABS.SALDO -> add(PaymentConst.TYPE_SALDO_ALL)
                    PaymentConst.HISTORY_TABS.SALDOBONUS -> add(PaymentConst.TYPE_CASHBACK_ALL)
                }
            }
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            logFilterEvent(AnalyticsConst.PRODUCT)
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    fun applyStatusFilters(fetchResults: Boolean = true) {
        // Update appliedTypeFilters from filterValues
        filtersState[activeTab]?.statusFilters?.apply {
            clear()
            getApplicableFilters()?.status?.flatMap { it.filters }?.map {
                addIf(it.key, it.isChecked)
            }
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            logFilterEvent(AnalyticsConst.STATUS)
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    fun applyDateFilters(fetchResults: Boolean = true) {
        setDefaultDateFilter()
        // Update appliedTypeFilters from filterValues
        filtersState[activeTab]?.dateFilters?.apply {
            getApplicableFilters()?.date?.find { it.isChecked }?.let {
                val dates = PaymentUtils.getDates(it)
                presetValue = it.presetValue
                startDate = dates.first
                endDate = dates.second
            }
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            logFilterEvent(AnalyticsConst.DATE)
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    fun updateSearchQuery(searchQuery: String, fetchResults: Boolean = true) {
        filtersState[activeTab]?.searchQuery = searchQuery
        queryParams.searchQuery = searchQuery
        if (fetchResults) {
            invalidatePagingSource(activeTab)
        }
    }

    fun updateEdcFilterSelected(isEdcFilterSelected: Boolean){
        bukuOrigin = if (isEdcFilterSelected) AppConst.BUKUWARUNG_EDC else null
        invalidatePagingSource(activeTab)
    }

    fun applySorting(fetchResults: Boolean = true) {
        // Update appliedTypeFilters from filterValues
        getApplicableFilters()?.sort?.find { it.isChecked }?.let {
            filtersState[activeTab]?.sorting = it.key
        }
        _filtersStateLive.value = filtersState
        if (fetchResults) {
            logFilterEvent(AnalyticsConst.SORT)
            _event.value = Event.ClearSearch
            fetchOrders(activeTab)
        }
    }

    private fun logFilterEvent(typeOfFilter: String) {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_CHOOSE_PAYMENT_HISTORY_FILTER,
            AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.ID, typeOfFilter)
                put(AnalyticsConst.TAB, activeTab.name)
                put(
                    AnalyticsConst.STATUS, filtersState[activeTab]?.statusFilters?.joinToString(
                        prefix = "[", separator = ", ", postfix = "]"
                    )
                )
                put(AnalyticsConst.DATE, filtersState[activeTab]?.dateFilters?.presetValue)
                put(
                    AnalyticsConst.START_DATE,
                    DateTimeUtilsKt.getDateFromLong(
                        filtersState[activeTab]?.dateFilters?.startDate,
                        DateTimeUtilsKt.DD_MMM_YYYY
                    )
                )
                put(
                    AnalyticsConst.END_DATE,
                    DateTimeUtilsKt.getDateFromLong(
                        filtersState[activeTab]?.dateFilters?.endDate,
                        DateTimeUtilsKt.DD_MMM_YYYY
                    )
                )
                put(
                    AnalyticsConst.PRODUCT, filtersState[activeTab]?.typeFilters?.joinToString(
                        prefix = "[", separator = ", ", postfix = "]"
                    )
                )
            }
        )
    }

    private fun setDefaultDateFilter() {
        // We will select today's date filter if nothing is applied
        if (getApplicableFilters()?.date?.find { it.isChecked } == null) {
            getApplicableFilters()?.date?.find { it.presetValue == PaymentConst.DATE_PRESET.TODAY }
                ?.isChecked = true
        }
    }

    fun clearFilters() {
        getApplicableFilters()?.products?.flatMap { it.filters }?.map {
            it.isChecked = false
        }
        getApplicableFilters()?.status?.flatMap { it.filters }?.map {
            it.isChecked = false
        }
        getApplicableFilters()?.date?.map { it.isChecked = false }
        // Applying default
        getApplicableFilters()?.date?.find { it.presetValue == PaymentConst.DATE_PRESET.TODAY }
            ?.let { it.isChecked = true }
        applyTypeFilters(false)
        applyStatusFilters(false)
        applyDateFilters(false)
        _event.value = Event.ClearSearch
        fetchOrders(activeTab)
    }

    private fun getTypeFilters(): ArrayList<String> {
        // If typeFilters contains directly unsupported PARENT types,
        // we add their children here.
        val filtersForApi = arrayListOf<String>()
        filtersState[activeTab]?.typeFilters?.let {
            filtersForApi.addAll(it)
            if (it.contains(PaymentConst.TYPE_SALDO_ALL)) {
                filtersForApi.addAll(PaymentConst.TYPE_SALDO_CHILDREN)
                filtersForApi.remove(PaymentConst.TYPE_SALDO_ALL)
            }
            if (it.contains(PaymentConst.TYPE_CASHBACK_ALL)) {
                filtersForApi.addAll(PaymentConst.TYPE_CASHBACK_CHILDREN)
                filtersForApi.remove(PaymentConst.TYPE_CASHBACK_ALL)
            }
        }
        /**
         * If type=DEFAULT is passed, BE will remove Saldo and Komisi Agen orders from the list
         * We need to show Saldo and Komisi Agen orders as nested orders, so just for "ALL" tab,
         * if we pass type=DEFAULT, Saldo and Komisi Agen orders will be hidden.
         *
         * On all the other tabs, i.e. DIGITAL PRODUK, PEMBAYRAN, etc. these orders are not visible
         * already due to filtering.
         */
        if (filtersForApi.isEmpty()) {
            filtersForApi.add(PaymentConst.TYPE_DEFAULT)
        } else {
            filtersForApi.remove(PaymentConst.TYPE_DEFAULT)
        }
        return filtersForApi
    }

    private fun getStatusFilters(): ArrayList<String> {
        val statusFilters = arrayListOf<String>()
        filtersState[activeTab]?.statusFilters?.let {
            statusFilters.addAll(it)
            if (it.contains(PaymentHistory.STATUS_PAID).isTrue) {
                statusFilters.add(PaymentHistory.STATUS_HOLD)
            }
        }
        return statusFilters
    }

    fun fetchOrders(activeTab: PaymentConst.HISTORY_TABS) = viewModelScope.launch {
        if (ordersPagedData.value == null) {
            providePagingSource(activeTab)
        } else {
            invalidatePagingSource(activeTab)
        }
    }

    private fun invalidatePagingSource(activeTab: PaymentConst.HISTORY_TABS) {
        val typeFilters = getTypeFilters()
        val statusFilters = getStatusFilters()
        val sorting = filtersState[activeTab]?.sorting
        val startDate = filtersState[activeTab]?.dateFilters?.startDate
        val endDate = filtersState[activeTab]?.dateFilters?.endDate

        /*
           NOTE: orderDatesMap has to be cleared as well when invalidating data source
           otherwise it won't add date headers to the data.
           Check the code in addDateHeaders function for more details.
         */
        orderDatesMap[activeTab]?.clear()

        queryParams.apply {
            this.startDate = startDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            }
            this.endDate = endDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            }
            type = typeFilters
            status = statusFilters
            this.sorting = sorting
        }

        providePagingSource(activeTab)
    }

    private fun providePagingSource(activeTab: PaymentConst.HISTORY_TABS) {
        val typeFilters = getTypeFilters()
        val statusFilters = getStatusFilters()
        val sorting = filtersState[activeTab]?.sorting
        val searchQuery = filtersState[activeTab]?.searchQuery
        val startDate = filtersState[activeTab]?.dateFilters?.startDate
        val endDate = filtersState[activeTab]?.dateFilters?.endDate

        queryParams = OrdersPagingSource.QueryParams(
            accountId = SessionManager.getInstance().businessId,
            customerId = customerId,
            startDate = startDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            },
            endDate = endDate?.let {
                DateTimeUtils.getFormattedDateTime(it, DateTimeUtils.YYYY_MM_DD)
            },
            type = typeFilters,
            status = statusFilters,
            billerCode = billerCode,
            sorting = sorting,
            limit = RemoteConfigUtils.getPaymentConfigs().paginationConfig?.limitPerPage.orDefault(10),
            searchQuery = searchQuery,
            bukuOrigin = bukuOrigin
        )

        val pager = Pager(
            config = PagingConfig(
                pageSize = RemoteConfigUtils.getPaymentConfigs().paginationConfig?.limitPerPage.orNil,
                prefetchDistance = 2
            ),
            pagingSourceFactory = {
                OrdersPagingSource(
                    ordersUseCase = ordersUseCase,
                    pagingStatusLiveData = _pagingStatus,
                    activeTab = activeTab,
                    orderDates = orderDatesMap,
                    queryParams = queryParams
                )
            }
        )

        viewModelScope.launch {
            pager.flow.cachedIn(viewModelScope).collectLatest {
                _ordersPagedData.value = it
            }
        }
    }

    fun fetchLinkedOrders(orderId: String, adapterPos: Int) =
        viewModelScope.launch(Dispatchers.IO) {
            when (val response = ordersUseCase.getLinkedOrders(orderId)) {
                is ApiSuccessResponse -> {
                    val linkedOrders = arrayListOf<OrderHistoryData>()
                    response.body.map {
                        linkedOrders.add(
                            OrderHistoryData(
                                orderData = it.apply { isLinkedOrder = true },
                                formattedDate = null,
                                timestamp = null,
                                viewType = OrderHistoryAdapter.Companion.ViewType.ORDER
                            )
                        )
                    }
                    linkedOrdersMap[orderId] = LinkedOrdersData(
                        linkedOrders = linkedOrders,
                        linkedOrdersVisibility = true,
                        linkedOrdersLoading = false
                    )
                    withContext(Dispatchers.Main) {
                        _event.value = Event.LinkedItemsFetched(adapterPos)
                    }
                }
                is ApiErrorResponse -> {
                    withContext(Dispatchers.Main) {
                        _event.value = Event.ApiError(response.errorMessage, adapterPos)
                    }
                }
                else -> {}
            }
        }
}