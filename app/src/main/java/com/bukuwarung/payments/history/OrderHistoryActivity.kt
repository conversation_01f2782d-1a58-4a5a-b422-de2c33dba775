package com.bukuwarung.payments.history

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.widget.Toast
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.commonview.view.BukuTileViewBottomSheet
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.ActivityOrderHistoryBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.payments.PaymentHistoryDetailsActivity
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.ExpiringCashbackInfo
import com.bukuwarung.payments.data.model.PaymentFilterDtoX
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.listeners.PpobProductsListener
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.payments.viewmodels.PaymentsVMFactory
import com.bukuwarung.payments.widget.ErrorBottomSheet
import com.bukuwarung.payments.widget.ExpiringCashbacksView
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.Location
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.ui_component.base.BaseEmptyView
import com.bukuwarung.utils.*
import com.bukuwarung.utils.Utilities.debounce
import com.google.android.material.tabs.TabLayout
import kotlinx.coroutines.launch
import javax.inject.Inject


class OrderHistoryActivity : BaseActivity(),
    OrderListCallback,
    OnboardingWidget.OnboardingWidgetListener, ErrorBottomSheet.Callback,
    ExpiringCashbacksView.Callback, PpobProductsListener, Navigator {

    companion object {
        const val BOOK_ID = "book_id"
        const val ACTIVE_TAB = "active_tab"
        const val CUSTOMER_ID = "customer_id"
        const val PRODUCT_FILTERS = "product_filters"
        const val STATUS_FILTERS = "status_filters"
        const val START_DATE = "start_date"
        const val END_DATE = "end_date"
        const val DATE_PRESET = "date_preset"

        /*
         * Biller code is used to differentiate between Postpaid and Prepaid Listrik
         */
        const val BILLER_CODE = "biller_code"

        @JvmStatic
        fun createIntent(
            context: Context,
            bookId: String,
            activeTab: PaymentConst.HISTORY_TABS? = PaymentConst.HISTORY_TABS.ALL,
            productFilters: ArrayList<String>? = null,
            statusFilters: ArrayList<String>? = null,
            dateFilters: Pair<Long, Long>? = null,
            datePreset: PaymentConst.DATE_PRESET? = null,
            customerId: String? = null, billerCode: String? = null
        ) = Intent(context, OrderHistoryActivity::class.java).apply {
            putExtra(BOOK_ID, bookId)
            putExtra(ACTIVE_TAB, activeTab)
            putExtra(CUSTOMER_ID, customerId)
            putExtra(BILLER_CODE, billerCode)
            putStringArrayListExtra(PRODUCT_FILTERS, productFilters)
            putStringArrayListExtra(STATUS_FILTERS, statusFilters)
            dateFilters?.first?.let { putExtra(START_DATE, it) }
            dateFilters?.second?.let { putExtra(END_DATE, it) }
            putExtra(DATE_PRESET, datePreset)
        }
    }

    private lateinit var binding: ActivityOrderHistoryBinding
    private var paginationAdapter: OrderHistoryPagingAdapter? = null
    private var isFilterApplied = false

    @Inject
    lateinit var viewModelFactory: PaymentsVMFactory
    private val viewModel: OrderHistoryViewModel by viewModels { viewModelFactory }

    @Inject
    lateinit var neuro: Neuro

    private val customerId by lazy { intent?.getStringExtra(CUSTOMER_ID).orEmpty() }
    private val bookId by lazy { intent?.getStringExtra(BOOK_ID).orEmpty() }
    private val billerCode by lazy { intent?.getStringExtra(BILLER_CODE) }
    private var hasLogSearchEvent = false
    private var searchWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

        }

        override fun afterTextChanged(s: Editable?) {
            debouncedSearch(s.toString())
        }
    }
    val debouncedSearch: (String) -> Unit = debounce(
        RemoteConfigUtils.getPaymentConfigs().paginationConfig?.debounceDelay.orDefault(AppConst.clickDebounceTime),
        lifecycleScope,
        ::handleSearchChange
    )

    override fun setViewBinding() {
        binding = ActivityOrderHistoryBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setupToolbar()
        paginationAdapter = OrderHistoryPagingAdapter(this, viewModel.linkedOrdersMap)

        var preselectedTab = intent?.getSerializableExtra(ACTIVE_TAB) as? PaymentConst.HISTORY_TABS
        intent?.getStringExtra(ACTIVE_TAB)?.let {
            preselectedTab = tryToGetValueOrDefault(
                tryBlock = { PaymentConst.HISTORY_TABS.valueOf(it) },
                defaultValue = PaymentConst.HISTORY_TABS.ALL
            )
        }
        var datePreset = intent?.getSerializableExtra(DATE_PRESET) as? PaymentConst.DATE_PRESET
        intent?.getStringExtra(DATE_PRESET)?.let {
            datePreset = tryToGetValueOrDefault(
                tryBlock = { PaymentConst.DATE_PRESET.valueOf(it) },
                defaultValue = null
            )
        }
        val typeFilters = intent?.getStringArrayListExtra(PRODUCT_FILTERS)
        val statusFilters = intent?.getStringArrayListExtra(STATUS_FILTERS)
        val startDate = intent?.getLongExtra(START_DATE, 0L)
        val endDate = intent?.getLongExtra(END_DATE, 0L)
        viewModel.init(
            bookId, preselectedTab,
            typeFilters, statusFilters, startDate, endDate,
            customerId, billerCode, datePreset
        )

        with(binding) {
            sflShimmerView.startShimmer()

            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.filter_history_all))
            )
            tlHistory.addTab(
                tlHistory.newTab()
                    .setText(getString(R.string.filter_history_digital_product))
            )
            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.filter_history_payment))
            )
            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.filter_history_saldo))
            )
            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.filter_saldo_bonus))
            )

            tlHistory.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    InputUtils.hideKeyboard(this@OrderHistoryActivity)
                    isFilterApplied = false
                    bevEmptyState.hideView()
                    val tabKey = getTabKey(tab?.position)
                    viewModel.onTabSelected(tabKey)
                    updatedFilters(viewModel.filtersState)
                    tilSearch.showView()
                    tvFilterProduct.showView()
                    tvSort.hideView()
                    expiringCashbackInfo.hideView()
                    tvEdcFilter.hideView()
                    if (tvEdcFilter.isSelected){
                        tvEdcFilter.isSelected = false
                        viewModel.updateEdcFilterSelected(tvEdcFilter.isSelected)
                    }
                    when (tabKey) {
                        PaymentConst.HISTORY_TABS.ALL -> {
                            tilSearch.editText?.hint = getString(R.string.search_transactions)
                            tvEdcFilter.showView()
                        }
                        PaymentConst.HISTORY_TABS.PPOB -> {
                            tilSearch.editText?.hint = getString(R.string.search_product_name)
                            tvEdcFilter.showView()
                        }
                        PaymentConst.HISTORY_TABS.PEMBAYARAN -> {
                            tilSearch.editText?.hint = getString(R.string.search_customer_name)
                            tvEdcFilter.showView()
                        }
                        PaymentConst.HISTORY_TABS.SALDO -> {
                            tilSearch.editText?.hint = getString(R.string.search_transactions)
                            tvEdcFilter.showView()
                        }
                        PaymentConst.HISTORY_TABS.SALDOBONUS -> {
                            tilSearch.hideView()
                            tvFilterProduct.hideView()
                            tvSort.showView()
                            viewModel.expiringCashbacks.value?.let {
                                setCashbackInfoView(it)
                            }
                        }
                    }
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_CLICK_PAYMENT_HISTORY_TAB,
                        AppAnalytics.PropBuilder().apply {
                            put(AnalyticsConst.ID, tabKey.name)
                        }
                    )
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                }

            })

            preselectedTab?.let {
                val tab = when (it) {
                    PaymentConst.HISTORY_TABS.ALL -> tlHistory.getTabAt(0)
                    PaymentConst.HISTORY_TABS.PPOB -> tlHistory.getTabAt(1)
                    PaymentConst.HISTORY_TABS.PEMBAYARAN -> tlHistory.getTabAt(2)
                    PaymentConst.HISTORY_TABS.SALDO -> tlHistory.getTabAt(3)
                    PaymentConst.HISTORY_TABS.SALDOBONUS -> tlHistory.getTabAt(4)
                }
                Handler(Looper.getMainLooper()).postDelayed({ tab?.select() }, 100)
            }

            tvFilterStatus.setSingleClickListener {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_CLICK_PAYMENT_HISTORY_FILTER,
                    AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ID, AnalyticsConst.STATUS)
                        put(AnalyticsConst.TAB, viewModel.activeTab.name)
                    }
                )
                StatusFilterBottomSheet.createInstance(supportFragmentManager)
            }

            tvFilterDate.setSingleClickListener {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_CLICK_PAYMENT_HISTORY_FILTER,
                    AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ID, AnalyticsConst.DATE)
                        put(AnalyticsConst.TAB, viewModel.activeTab.name)
                    }
                )
                DateFilterBottomSheet.createInstance(supportFragmentManager)
            }

            tvFilterProduct.setSingleClickListener {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_CLICK_PAYMENT_HISTORY_FILTER,
                    AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ID, AnalyticsConst.PRODUCT)
                        put(AnalyticsConst.TAB, viewModel.activeTab.name)
                    }
                )
                ProductFilterBottomSheet.createInstance(supportFragmentManager)
            }

            tvSort.setSingleClickListener {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_CLICK_PAYMENT_HISTORY_FILTER,
                    AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ID, AnalyticsConst.SORT)
                        put(AnalyticsConst.TAB, viewModel.activeTab.name)
                    }
                )
                SortingBottomSheet.createInstance(supportFragmentManager)
            }

            tvEdcFilter.setSingleClickListener {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_CLICK_PAYMENT_HISTORY_FILTER,
                    AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ID, AnalyticsConst.EDC)
                        put(AnalyticsConst.TAB, viewModel.activeTab.name)
                    }
                )
                tvEdcFilter.isSelected = !tvEdcFilter.isSelected
                viewModel.updateEdcFilterSelected(tvEdcFilter.isSelected)
            }

            rvHistory.apply {
                adapter = paginationAdapter
                layoutManager = LinearLayoutManager(this@OrderHistoryActivity)
                addItemDecoration(
                    HeaderItemDecoration(rvHistory, false) { pos: Int ->
                        if (pos == -1) false
                        else paginationAdapter?.isHeaderItem(pos).isTrue
                    }
                )
            }

            tilSearch.editText?.addTextChangedListener(searchWatcher)
            tilSearch.setEndIconOnClickListener {
                clearSearch()
                Toast.makeText(
                    this@OrderHistoryActivity,
                    R.string.filter_removed,
                    Toast.LENGTH_SHORT
                ).show()
            }

            ivHelp.setOnClickListener {
                Utilities.launchBrowser(
                    this@OrderHistoryActivity,
                    RemoteConfigUtils.getPaymentConfigs().supportUrls.payments
                )
            }

            ivClearFilter.setOnClickListener {
                viewModel.clearFilters()
                it.hideView()
                Toast.makeText(
                    this@OrderHistoryActivity,
                    R.string.transaction_history_updated,
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
        showCoachmarks()
    }

    private fun handleSearchChange(searchQuery: String) {
        paginationAdapter?.searchText = searchQuery
        viewModel.updateSearchQuery(searchQuery)
        logSearchEvent(searchQuery)
    }

    private fun showCoachmarks() {
        val tabCMShown = OnboardingPrefManager.getInstance()
            .getHasFinishedForId(OnboardingPrefManager.TUTORIAL_ORDER_HISTORY_TAB)
        val filterCMShown = OnboardingPrefManager.getInstance()
            .getHasFinishedForId(OnboardingPrefManager.TUTORIAL_ORDER_HISTORY_FILTER)
        when {
            !tabCMShown -> showTabsCoachmark()
            !filterCMShown -> showFilterCoachmark()
        }
    }

    private fun showTabsCoachmark() {
        OnboardingWidget.createInstance(
            this, this, OnboardingPrefManager.TUTORIAL_ORDER_HISTORY_TAB,
            binding.tlHistory, null,
            getString(R.string.transaction_history_coachmark_tab),
            getString(R.string.transaction_history_coachmark_tab_detail),
            getString(R.string.next),
            FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
            1, 2,
            sendAnalytics = true,
            sendAnalyticsOnDismiss = true,
            0, stepsLocation = Location.BOTTOM,
            hideCloseIcon = true
        )
    }

    private fun showFilterCoachmark() {
        OnboardingWidget.createInstance(
            this,
            this,
            OnboardingPrefManager.TUTORIAL_ORDER_HISTORY_FILTER,
            binding.clFiltersContainer,
            null,
            getString(R.string.transaction_history_coachmark_filter),
            getString(R.string.transaction_history_coachmark_filter_detail),
            getString(R.string.close_tutorial),
            FocusGravity.CENTER,
            ShapeType.RECTANGLE_FULL,
            2,
            2,
            sendAnalytics = true,
            sendAnalyticsOnDismiss = true,
            0,
            stepsLocation = Location.BOTTOM,
            hideCloseIcon = true
        )
    }

    override fun subscribeState() {
        viewModel.filtersStateLive.observe(this) { filtersMap ->
            updatedFilters(filtersMap)
        }

        viewModel.event.observe(this) {
            when (it) {
                OrderHistoryViewModel.Event.ClearSearch -> clearSearch(false)
                is OrderHistoryViewModel.Event.LinkedItemsFetched -> updateLinkedOrders(it.adapterPos)
                is OrderHistoryViewModel.Event.ApiError -> {
                    handleError(it.errorMessage)
                    updateLinkedOrders(it.adapterPos)
                }
            }
        }

        viewModel.viewState.observe(this) {
            with(binding) {
                if (it.loading) {
                    sflShimmerView.showView()
                    sflShimmerView.startShimmer()
                    rvHistory.hideView()
                    bevEmptyState.hideView()
                } else {
                    sflShimmerView.hideView()
                    rvHistory.showView()
                }
                if (it.errorMessage != null) {
                    handleError(it.errorMessage)
                }
            }
        }

        viewModel.expiringCashbacks.observe(this) {
            setCashbackInfoView(it)
        }

        viewModel.ordersPagedData.observe(this) {
            lifecycleScope.launch{
                if(it != null){ paginationAdapter?.submitData(it) }
            }
        }

        viewModel.pagingStatus.observe(this) { status ->
            when (status) {
                PagingStatus.Loading -> {
                    with(binding) {
                        rvHistory.hideView()
                        bevEmptyState.hideView()
                        sflShimmerView.showView()
                        sflShimmerView.startShimmer()
                    }
                }
                is PagingStatus.Loaded -> {
                    with(binding) {
                        rvHistory.showView()
                        bevEmptyState.hideView()
                        sflShimmerView.hideView()
                        binding.pbLoading.hideView()
                        if (tilSearch.editText?.text?.isNotEmpty().isTrue) {
                            filterResultCount(status.totalItems)
                        } else {
                            filterResultCount(0)
                        }
                    }
                }
                PagingStatus.Empty -> {
                    binding.sflShimmerView.hideView()
                    binding.rvHistory.hideView()
                    setEmptyView()
                }
                PagingStatus.EmptyNextPage -> {
                    // Hide any loading for the next page
                    binding.pbLoading.hideView()
                }
                PagingStatus.LoadingNextPage -> {
                    // Show any loading for the next page
                    binding.pbLoading.showView()
                }
                is PagingStatus.Error -> {
                    binding.sflShimmerView.hideView()
                    handleError(status.errorMessage)
                }
                PagingStatus.NoInternet -> {
                    binding.sflShimmerView.hideView()
                    handleError(AppConst.NO_INTERNET_ERROR_MESSAGE)
                }
            }
        }
    }

    private fun setCashbackInfoView(expiringCashbackInfo: ExpiringCashbackInfo) {
        if (expiringCashbackInfo.amount.orNil > 0) {
            binding.expiringCashbackInfo.setView(expiringCashbackInfo, this)
        } else {
            binding.expiringCashbackInfo.hideView()
        }
    }

    private fun handleError(errorMessage: String?) {
        if (errorMessage.isNotNullOrBlank()) {
            if (errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                ErrorBottomSheet.createInstance(
                    ErrorBottomSheet.Companion.ApiErrorType.API_ERROR,
                    errorMessage?.ifEmpty { getString(R.string.try_again_or_wait) }
                ).show(supportFragmentManager, ErrorBottomSheet.TAG)
            } else {
                ErrorBottomSheet.createInstance(
                    ErrorBottomSheet.Companion.ApiErrorType.CONNECTION_ERROR,
                    errorMessage
                ).show(supportFragmentManager, ErrorBottomSheet.TAG)
            }
        }
    }

    private fun clearSearch(fetchResults: Boolean = true) {
        binding.tilSearch.editText?.apply {
            removeTextChangedListener(searchWatcher)
            setText("")
            paginationAdapter?.searchText = ""
            viewModel.updateSearchQuery("", fetchResults)
            addTextChangedListener(searchWatcher)
        }
    }

    private fun updateLinkedOrders(adapterPos: Int) {
        paginationAdapter?.notifyItemChanged(adapterPos)
    }

    /**
     * Sets existing searched query in the search input but doesn't trigger
     * text watcher to avoid duplicate network calls.
     */
    private fun setSearchQuery(searchQuery: String) {
        binding.tilSearch.editText?.apply {
            removeTextChangedListener(searchWatcher)
            setText(searchQuery)
            setSelection(searchQuery.length)
            paginationAdapter?.searchText = searchQuery
            addTextChangedListener(searchWatcher)
        }
    }

    private fun updatedFilters(filtersMap: HashMap<PaymentConst.HISTORY_TABS, PaymentFilterDtoX>) {
        with(binding) {
            filtersMap[viewModel.activeTab]?.statusFilters?.let { statusFilter ->
                if (statusFilter.size > 0) {
                    if (statusFilter.size == 1) {
                        val selectedFilter =
                            viewModel.getApplicableFilters()?.status?.flatMap { it.filters }
                                ?.find { it.key == statusFilter[0] }
                        selectedFilter?.label?.let {
                            tvFilterStatus.isSelected = true
                            tvFilterStatus.text = it
                        } ?: run {
                            tvFilterStatus.isSelected = false
                            tvFilterStatus.text = getString(R.string.select_status)
                        }
                    } else {
                        tvFilterStatus.isSelected = true
                        tvFilterStatus.text = getString(R.string.multi_status)
                    }
                } else {
                    tvFilterStatus.isSelected = false
                    tvFilterStatus.text = getString(R.string.select_status)
                }
            }
            filtersMap[viewModel.activeTab]?.typeFilters?.let { typeFilters ->
                if (typeFilters.size > 0) {
                    if (typeFilters.size == 1) {
                        val selectedFilter =
                            viewModel.getApplicableFilters()?.products?.flatMap { it.filters }
                                ?.find { it.key == typeFilters[0] }
                        selectedFilter?.label?.let {
                            tvFilterProduct.isSelected = true
                            tvFilterProduct.text = it
                        } ?: run {
                            tvFilterProduct.isSelected = false
                            tvFilterProduct.text = getString(R.string.select_product)
                        }
                    } else {
                        tvFilterProduct.isSelected = true
                        tvFilterProduct.text = getString(R.string.multi_product)
                    }
                } else {
                    tvFilterProduct.isSelected = false
                    tvFilterProduct.text = getString(R.string.select_product)
                }
            }
            filtersMap[viewModel.activeTab]?.dateFilters?.let {
                if (it.startDate != null && it.startDate != 0L && it.endDate != null && it.endDate != 0L) {
                    tvFilterDate.isSelected = true
                    if (it.presetValue == PaymentConst.DATE_PRESET.CUSTOM_RANGE) {
                        Utilities.safeLet(it.startDate, it.endDate) { startDate, endDate ->
                            tvFilterDate.text = getString(
                                R.string.two_dashed_strings,
                                DateTimeUtils.getFormattedDateTime(
                                    startDate,
                                    DateTimeUtils.DD_MMM_YY
                                ),
                                DateTimeUtils.getFormattedDateTime(
                                    endDate,
                                    DateTimeUtils.DD_MMM_YY
                                )
                            )
                        }
                    } else {
                        viewModel.getApplicableFilters()?.date?.find { dateFilter -> it.presetValue == dateFilter.presetValue }
                            ?.let { dateFilter ->
                                tvFilterDate.text = dateFilter.label
                            }
                    }
                } else {
                    tvFilterDate.isSelected = false
                    tvFilterDate.text = getString(R.string.select_date)

                }
            }
            filtersMap[viewModel.activeTab]?.sorting.let {
                val selectedSort = viewModel.getApplicableFilters()?.sort?.find { sortOption ->
                    sortOption.key == it
                }
                selectedSort?.let {
                    tvSort.text = it.label
                    tvSort.isSelected = true
                } ?: run {
                    tvSort.text = getString(R.string.sort)
                    tvSort.isSelected = false
                }
            }
            setSearchQuery(filtersMap[viewModel.activeTab]?.searchQuery.toString())

            isFilterApplied =
                tvFilterDate.isSelected || tvFilterProduct.isSelected || tvFilterStatus.isSelected
            ivClearFilter.visibility = isFilterApplied.asVisibility()
        }
    }

    private fun setupToolbar() {
        with(binding.toolbar) {
            navigationIcon =
                <EMAIL>(R.drawable.ic_arrow_back)
            setNavigationOnClickListener {
                InputUtils.hideKeyBoardWithCheck(this@OrderHistoryActivity)
                finish()
            }
        }
    }

    private fun setEmptyView() {
        with(binding) {
            binding.bevEmptyState.showView()
            when {
                tilSearch.editText?.text?.isNotEmpty().isTrue -> {
                    bevEmptyState.setEmptyIcon(R.drawable.ic_no_search_result)
                    bevEmptyState.setTitle(getString(R.string.no_search_transaction))
                    bevEmptyState.setMessage(getString(R.string.no_search_transaction_description))
                    bevEmptyState.setCtaText(getString(R.string.change_keyword))
                    bevEmptyState.binding.btnEmptyCta.showView()
                    bevEmptyState.addCallback(object : BaseEmptyView.Callback {
                        override fun ctaClicked() {
                            clearSearch()
                        }

                        override fun messageClicked() {
                        }

                    })
                }
                isFilterApplied -> {
                    bevEmptyState.setEmptyIcon(R.drawable.ic_no_search_result)
                    bevEmptyState.setTitle(getString(R.string.no_filter_transaction))
                    bevEmptyState.setMessage(getString(R.string.no_filter_transaction_description))
                    bevEmptyState.setCtaText(getString(R.string.change_keyword))
                    bevEmptyState.binding.btnEmptyCta.showView()
                    bevEmptyState.addCallback(object : BaseEmptyView.Callback {
                        override fun ctaClicked() {
                            viewModel.clearFilters()
                            binding.ivClearFilter.hideView()
                        }

                        override fun messageClicked() {
                        }

                    })
                }
                else -> {
                    bevEmptyState.setEmptyIcon(R.drawable.ic_no_transaction)
                    bevEmptyState.setTitle(getString(R.string.no_transaction))
                    bevEmptyState.setMessage(getString(R.string.no_transaction_description))
                    bevEmptyState.binding.btnEmptyCta.hideView()
                }
            }
        }
    }

    // to prevent search event being logged everytime user type in the search box
    private fun logSearchEvent(query: String) {
        if (query.isNotEmpty() && !hasLogSearchEvent) {
            val propBuilder =
                AppAnalytics.PropBuilder().put(AnalyticsConst.ID, AnalyticsConst.SEARCH)
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_LOOKUP,
                propBuilder
            )
            hasLogSearchEvent = true
        }
    }

    fun getTabKey(position: Int?): PaymentConst.HISTORY_TABS {
        return when (position) {
            0 -> PaymentConst.HISTORY_TABS.ALL
            1 -> PaymentConst.HISTORY_TABS.PPOB
            2 -> PaymentConst.HISTORY_TABS.PEMBAYARAN
            3 -> PaymentConst.HISTORY_TABS.SALDO
            4 -> PaymentConst.HISTORY_TABS.SALDOBONUS
            else -> PaymentConst.HISTORY_TABS.ALL
        }
    }

    private fun logPpobSelectionEvent(fragmentBodyBlock: BodyBlock) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put("ppob_type", fragmentBodyBlock.analytics_name.orEmpty())
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PEMBAYARAN_TAB)
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PPOB_BUY_BUTTON_CLICKED, propBuilder,
            true, true, false
        )
        SurvicateAnalytics.invokeEventTracker(
            fragmentBodyBlock.analytics_name.orEmpty(),
            this
        )
    }

    override fun openOrderDetail(order: PaymentHistory) {
        if (order.detailService?.equals("EDC_ADAPTER", true).isTrue) {
            //do nothing
        } else {
            val orderType = order.type?.let {
                if (it.equals(PaymentHistory.TYPE_CASHBACK_SETTLEMENT, true))
                    PaymentHistory.TYPE_PAYMENT_OUT
                else it
            }
            order.orderId?.let {
                startActivity(
                    PaymentHistoryDetailsActivity.createIntent(
                        context = this@OrderHistoryActivity,
                        customerId = order.customerId,
                        orderId = it,
                        paymentType = orderType,
                        fromMainActivity = false,
                        displayName = order.displayName,
                        entryPoint = AnalyticsConst.PAYMENTS_HISTORY,
                        ledgerAccountId = order.ledgerAccountId
                    )
                )
            }
        }
    }

    override fun filterResultCount(result: Int) {
        binding.tvResultCount.visibility = (result != 0).asVisibility()
        if (result > 0) {
            binding.tvResultCount.text =
                SpannableStringBuilder(getString(R.string.result_count, result)).colorText(
                    result.toString(),
                    getColorCompat(R.color.blue_60),
                    true
                )
        }
    }

    override fun fetchLinkedOrders(orderId: String, adapterPos: Int) {
        viewModel.fetchLinkedOrders(orderId, adapterPos)
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        showCoachmarks()
    }

    override fun onDismiss() {

    }

    override fun onButtonClicked() {
        viewModel.fetchOrders(viewModel.activeTab)
    }

    override fun openCashbackInfo() {
        startActivity(
            WebviewActivity.createIntent(
                this@OrderHistoryActivity,
                RemoteConfigUtils.getPaymentConfigs().saldoBonusUrl,
                getString(R.string.filter_history_saldo_bonus)
            )
        )
    }

    override fun spendCashbacks() {
        // Launch PPOB bottomsheet
        val bukuTileBottomSheet = BukuTileViewBottomSheet.createInstance(
            PpobConst.PPOB, true
        )
        bukuTileBottomSheet.setPpobProductsListener(this)
        bukuTileBottomSheet.show(supportFragmentManager, BukuTileViewBottomSheet.TAG)
    }

    override fun onPpobSelected(fragmentBodyBlock: BodyBlock?) {
        fragmentBodyBlock?.let {
            PpobUtils.handlePpobCategorySelection(
                context = this,
                category = it,
                neuro = neuro,
                navigator = this
            )
            logPpobSelectionEvent(it)
        }
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }
}