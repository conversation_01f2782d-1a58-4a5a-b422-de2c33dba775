package com.bukuwarung.preference;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.util.Log;

import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.bukuwarung.Application;
import com.bukuwarung.constants.PrefrenceConstant;
import com.bukuwarung.utils.Utility;

public class ReferralPrefManager {

    private static ReferralPrefManager prefManager;

    private Context context;
    Editor editor;
    SharedPreferences pref;

    MutableLiveData<String> refCode;

    private ReferralPrefManager(Context context2) {
        this.context = context2;
        this.pref = context2.getSharedPreferences(PrefrenceConstant.APP_REFERRAL_PREF, Context.MODE_PRIVATE);
        this.refCode = new MutableLiveData<>();
        refCode.setValue("");
        this.editor = this.pref.edit();
    }

    public static ReferralPrefManager getInstance() {
        if (prefManager == null) {
            prefManager = new ReferralPrefManager(Application.getAppContext());
        }
        return prefManager;
    }

    public boolean isRegistered() {
        return false;//this.pref.getBoolean("registered",false);
    }

    public void setRegistration(boolean status) {
        this.editor.putBoolean("registered", status);
        this.editor.apply();
    }

    public String getBaseInviteUrl() {
        return this.pref.getString("REF_BASE_URL","https://bukuwarung.com/invite?r=");
    }

    public void setBaseInviteUrl(String url) {
        if (url == null || url.isEmpty()) return;
        this.editor.putString("REF_BASE_URL", url);
        this.editor.apply();
    }

    public String getBaseRewardUrl() {
        return this.pref.getString("REF_REWARD_BASE_URL","https://bukuwarung.com/rewards?ae=");
    }

    public void setBaseRewardUrl(String url) {
        if (url == null || url.isEmpty()) return;
        this.editor.putString("REF_REWARD_BASE_URL", url);
        this.editor.apply();
    }

    public String getCurrentReferralCode() {
        return this.pref.getString("REF_CODE_IN_USE","");
    }

    public void setCurrentReferralCode(String code) {
        this.editor.putString("REF_CODE_IN_USE", code);
        this.editor.apply();
    }

    public String getPaymentReferralInUse() {
        return this.pref.getString("PAYMENT_REF_CODE_IN_USE","");
    }

    public void setPaymentReferralInUse(String code) {
        this.editor.putString("PAYMENT_REF_CODE_IN_USE", code);
        this.editor.apply();
    }

    public LiveData<String> getReferralCode() {
        return refCode;
    }

    public String getTemporaryReferralCode() {
        return this.pref.getString("REF_CODE_TEMP","");
    }

    public void setTemporaryReferralCode(String code) {
        if(Utility.isBlank(code)) {
            return;
        }
        this.editor.putString("REF_CODE_TEMP", code);
        refCode.postValue(code);
        Log.d("DeeplinkManager", code + "postVal");
        this.editor.apply();
    }

    public Boolean getShowSuccessReferralDialog() {
        return this.pref.getBoolean("SHOW_SUCCESS_REFERRAL_DIALOG",true);
    }

    public void setShowSuccessReferralDialog(Boolean showSuccessReferralDialog) {
        this.editor.putBoolean("SHOW_SUCCESS_REFERRAL_DIALOG", showSuccessReferralDialog);
        this.editor.apply();
    }

    public Boolean getShowOnboardingDialog() {
        return this.pref.getBoolean("SHOW_ONBOARDING_DIALOG",true);
    }

    public void setShowOnboardingDialog(Boolean showOnboardingDialog) {
        this.editor.putBoolean("SHOW_ONBOARDING_DIALOG", showOnboardingDialog);
        this.editor.apply();
    }

    public Boolean getSendNewUserToReferral() {
        return this.pref.getBoolean(PrefrenceConstant.SEND_NEW_USER_TO_REFERRAL,true);
    }

    public void setSendNewUserToReferral(Boolean sendNewUserToReferral) {
        this.editor.putBoolean(PrefrenceConstant.SEND_NEW_USER_TO_REFERRAL, sendNewUserToReferral);
        this.editor.apply();
    }

    public void setRefCodeUsed() {
        this.editor.putBoolean("REF_CODE_USED", true);
        this.editor.apply();
    }

    public boolean isRefCodeUsed() {
        return this.pref.getBoolean("REF_CODE_USED", false);
    }

    public void clearTemporaryReferralCode() {
        this.editor.remove("REF_CODE_TEMP");
        this.editor.apply();
    }

    public String getBaseApiUrl() {
        return this.pref.getString("REF_API_BASE_URL","https://gs328a0msd.execute-api.ap-southeast-1.amazonaws.com/eval/refcode");
    }

    public void setBaseApiUrl(String url) {
        if (url == null || url.isEmpty()) return;
        this.editor.putString("REF_API_BASE_URL", url);
        this.editor.apply();
    }

    public String getLeaderboardServerIP() {
        return this.pref.getString("LEADER_BOARD_API","https://4hb3zsrwsl.execute-api.ap-southeast-1.amazonaws.com/dev/");
    }

    public void setLeaderboardServerIP(String url) {
        if (url == null || url.isEmpty()) return;
        this.editor.putString("LEADER_BOARD_API", url);
        this.editor.apply();
    }

    public String getMyReferalCode() {
        return this.pref.getString("MY_REFERRAL_CODE","");
    }

    public void setMyReferalCode(String url) {
        this.editor.putString("MY_REFERRAL_CODE", url);
        this.editor.apply();
    }

    public String getReferralDeeplink() {
        return this.pref.getString("MY_REFERRAL_DEEPLINK","");
    }

    public void setReferralDeeplink(String url) {
        this.editor.putString("MY_REFERRAL_DEEPLINK", url);
        this.editor.apply();
    }

    public boolean enablePaymentReferral() {
        return this.pref.getBoolean("payment_refereal_enabled",false);
    }

    public void enablePaymentReferral(boolean status) {
        this.editor.putBoolean("payment_refereal_enabled", status);
        this.editor.apply();
    }
    public boolean enableReferralReward() {
        return this.pref.getBoolean("ref_reward_enabled",false);
    }

    public void enableReferralReward(boolean status) {
        this.editor.putBoolean("ref_reward_enabled", status);
        this.editor.apply();
    }

    public boolean blockUserReferralFlow() {
        return this.pref.getBoolean("block_user_referral_flow",false);
    }

    public void blockUserReferralFlow(boolean block) {
        this.editor.putBoolean("block_user_referral_flow", block);
        this.editor.apply();
    }

}
