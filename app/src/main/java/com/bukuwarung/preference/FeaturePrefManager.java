package com.bukuwarung.preference;

import static com.bukuwarung.activities.profile.ProfileTabFragment.BANK_ACCOUNT;
import static com.bukuwarung.activities.profile.ProfileTabFragment.BRICK_ACCOUNT;
import static com.bukuwarung.activities.profile.ProfileTabFragment.USER_BUSINESS_PROFILE;
import static com.bukuwarung.activities.profile.ProfileTabFragment.USER_KYC_COMPLETE;
import static com.bukuwarung.activities.profile.ProfileTabFragment.USER_PROFILE;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltySummary;
import com.bukuwarung.constants.PaymentConst;
import com.bukuwarung.constants.PrefrenceConstant;
import com.bukuwarung.payments.data.model.PaymentExitIntentData;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class FeaturePrefManager {

    private static final String IS_NOTES_MISSION_COMPLETE = "IS_NOTES_MISSION_COMPLETE";
    private static final String IS_NOTES_REMOVAL_INTRODUCED = "IS_NOTES_REMOVAL_INTRODUCED";
    private static FeaturePrefManager prefManager;

    Editor editor;
    public SharedPreferences pref;

    private static final String STOCK_TAB_VISIBLE = "STOCK_TAB_VISIBLE";
    private static final String PAYMENT_TAB_ENABLED = "PAYMENT_TAB_ENABLED";
    private static final String STOCK_TAB_ENABLED = "STOCK_TAB_ENABLED_FROM_TRANSACTION";
    private static final String STOCK_TAB_ENABLED_FROM_SETTINGS = "STOCK_TAB_ENABLED_FROM_SETTINGS";
    private static final String AUTO_RECORD_TAB_ENABLED_FROM_SETTINGS = "AUTO_RECORD_TAB_ENABLED_FROM_SETTINGS";
    private static final String ELIGIBLE_FOR_STOCK_TAB = "ELIGIBLE_FOR_STOCK_TAB";
    private static final String HAS_BANK_ACCOUNT = "HAS_BANK_ACCOUNT";
    private static final String STOCK_FIRST_BADGE_DISPLAYED = "stock_first_time_badge";
    private static final String INFORMATION_OPTIONAL_VISIBILITY = "information_optional_visibility";
    private static final String STOCK_FIRST_BADGE_DISPLAY_CONDITIONS = "stock_first_time_badge_conditions";

    private static final String PROFILE_VISIT_COUNT = "profile_visit_count";

    private static final String EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_OUT = "EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_OUT";
    private static final String EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_IN = "EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_IN";
    private static final String EXIT_WITHOUT_COMPLETING_PAYMENT_IN = "EXIT_WITHOUT_COMPLETING_PAYMENT_IN";
    private static final String EXIT_WITHOUT_COMPLETING_PAYMENT_OUT = "EXIT_WITHOUT_COMPLETING_PAYMENT_OUT";
    private static final String PAYMENT_OUT_PUSH_NOTIFICATION_COUNT = "PAYMENT_OUT_PUSH_NOTIFICATION_COUNT";
    private static final String PAYMENT_IN_PUSH_NOTIFICATION_COUNT = "PAYMENT_IN_PUSH_NOTIFICATION_COUNT";
    private static final String PAYMENT_EXIT_INTENT_DATA = "PAYMENT_EXIT_INTENT_DATA";

    private static final String IS_SEARCH_FILTER_CUSTOMER_SEEN = "is_search_filter_customer_seen";
    private static final String IS_DATE_FILTER_CUSTOMER_SEEN = "is_date_filter_customer_seen";
    private static final String IS_SEARCH_FILTER_TRANSACTION_SEEN = "is_search_filter_transaction_seen";
    private static final String IS_DATE_FILTER_TRANSACTION_SEEN = "is_date_filter_transaction_seen";

    private static final String IS_SIMPAN_FOR_BUSINESS_CARD_CLICKED = "is_simpan_for_business_card_clicked";
    public static final int STOCK_SELECTED = 1;
    public static final int STOCK_UN_SELECTED = 2;
    public static final int STOCK_NOT_USED = 3;
    public static final int AUTO_RECORD_SELECTED = 4;
    public static final int AUTO_RECORD_NOT_SELECTED = 5;

    private static final String PRODUCT_COACH_MARK_FOR_NO_SELLING_PRICE = "no_selling_price_coach_mark";
    private static final String RPU_STREAK_SHOWN = "rpu_streak_shown";
    private static final String NEW_BIZZ_CARD_DESIGN = "new_bizz_card_design";
    public static final String USING_DAILY_BUSINESS_RECAP = "USING_DAILY_BUSINESS_RECAP";
    public static final String DAILY_BUSINESS_ACTIVATED_FLAG = "DAILY_BUSINESS_ACTIVATED_FLAG";
    public static final String DAILY_BUSINESS_SETTING_VISIBLE = "DAILY_BUSINESS_SETTING_VISIBLE";
    public static final String FROM_TIME_FOR_AUTO_RECORD = "FROM_TIME_FOR_AUTO_RECORD";
    public static final String AUTORECORD_INTRODUCED = "AUTORECORD_INTRODUCED";
    public static final String DAILY_HIGHLIGHT_EXPLORED = "DAILY_HIGHLIGHT_EXPLORED";
    public static final String AUTO_RECORD_ENABLED = "AUTO_RECORD_ENABLED";
    public static final String AUTO_RECORD_INSTITUTIONS = "AUTO_RECORD_INSTITUTIONS";
    public static final String ALL_AUTO_RECORD_INSTITUTIONS = "ALL_AUTO_RECORD_INSTITUTIONS";
    public static final String BUSINESS_CARD_SAVED_ONCE = "BUSINESS_CARD_SAVED_ONCE";
    public static final String POS_NOTIFICATION_SENT = "pos_notification_sent";
    public static final String IS_HOME_COACHMARK_SEEN = "is_home_coachmark_seen";
    public static final String USAGE_GOAL_TAB = "usage_goal_tab";

    public static final String MANDATORY_CATEGORY_TOOLTIP_SEEN = "mandatory_category_tooltip_seen";
    public static final String BUSINESS_CATEGORY_UPDATED = "business_category_updated";
    public static final String PRODUCT_WELCOME_SCREEN = "product_welcome_screen";
    public static final String BRICK_INTEGRATED = "brick_integrated";
    public static final String LAINNYA_PROFILE_COMPLETION_STATUS = "lainnya_profile_completion_status";
    public static final String BRICK_INTEGRATED_FIRST_TIME = "brick_integrated_first_time";
    public static final String TXN_CATEGORY_REVAMP_TOOLTIP = "TXN_CATEGORY_REVAMP_TOOLTIP";
    public static final String BUSINESS_DASHBOARD_PPOB_PRODUCT = "business_dashboard_ppob_product";
    public static final String BUSINESS_DASHBOARD_PAYMENT = "business_dashboard_payment";
    public static final String LIST_OF_NUMBERS = "list_of_numbers";
    public static final String TICKER_CLOSE_TIME = "ticker_close_time";
    public static final String PREV_DETECT_TIME_KEY = "prev_detect_time_key";


    public FeaturePrefManager(Context context2) {
        this.pref = context2.getSharedPreferences(PrefrenceConstant.FEATURE_PREF, PrefrenceConstant.PRIVATE_MODE);
        this.editor = this.pref.edit();
    }

    public static FeaturePrefManager getInstance() {
        if (prefManager == null) {
            prefManager = new FeaturePrefManager(Application.getAppContext());
        }
        return prefManager;
    }

    public void setRateTargetCustomer(int i) {
        this.editor.putInt("RATE_TARGATE", i);
        this.editor.apply();
    }

    public int getRateTargetCustomer() {
        return this.pref.getInt("RATE_TARGATE", 1);
    }

    public void setRateLaterDate(long ignoredOn) {
        this.editor.putLong("RATE_LATER", ignoredOn);
        this.editor.apply();
    }

    public long getRateLaterDate() {
        return this.pref.getLong("RATE_LATER", -1);
    }

    public void setLastCouponVisibilityCheck(long checkdAt) {
        this.editor.putLong("COUPON_VISIBILITY_CHECKED_AT", checkdAt);
        this.editor.apply();
    }

    public void setPaymentTransactionCount(int count) {
        this.editor.putInt("PAYMENT_TRANSACTION_COUNT", count);
        this.editor.apply();
    }

    public int getPaymentTransactionCount() {
        return this.pref.getInt("PAYMENT_TRANSACTION_COUNT", 0);
    }

    public long getLastCouponVisibilityCheck() {
        return this.pref.getLong("COUPON_VISIBILITY_CHECKED_AT", -1);
    }

    public long getLastMonthlyTargetTime() {
        return this.pref.getLong("MONTH_PU_TARGET_REACHED_AT", 0);
    }

    public void setLastMonthlyTargetTime(long timestamp) {
        this.editor.putLong("MONTH_PU_TARGET_REACHED_AT", timestamp);
        this.editor.apply();
    }

    public int getMonthlyPUTargetProgress() {
        return this.pref.getInt("MONTH_PU_TARGET_PROGRESS", 0);
    }

    public void setMonthlyPUTargetProgress(int count) {
        this.editor.putInt("MONTH_PU_TARGET_PROGRESS", count);
        this.editor.apply();
    }

    public void isCouponVisible(boolean isVisible) {
        this.editor.putBoolean("COUPON_VISIBILITY_RESULT", isVisible);
        this.editor.apply();
    }

    public boolean isCouponVisible() {
        return this.pref.getBoolean("COUPON_VISIBILITY_RESULT", true);
    }

    public void hasCompleteUserProfile(boolean isVisible) {
        this.editor.putBoolean("user_profile_complete", isVisible);
        this.editor.apply();
    }

    public boolean hasCompleteUserProfile() {
        return this.pref.getBoolean("user_profile_complete", false);
    }

    public void hasSeenYearReview(boolean isVisible) {
        this.editor.putBoolean("has_seen_year_review", isVisible);
        this.editor.apply();
    }

    public boolean hasSeenYearReview() {
        return this.pref.getBoolean("has_seen_year_review", false);
    }

    public void hasCompleteBusinessProfile(boolean isVisible) {
        this.editor.putBoolean("user_business_complete", isVisible);
        this.editor.apply();
    }

    public boolean hasCompleteBusinessProfile() {
        return this.pref.getBoolean("user_business_complete", false);
    }

    public boolean hasEnableBukuWatermark() {
        return this.pref.getBoolean("disable_buku_watermark", true);
    }

    public void enableBukuWatermark(boolean isEnable) {
        this.editor.putBoolean("disable_buku_watermark", isEnable);
        this.editor.apply();
    }

    public void hasCompletedKYC(boolean isVisible) {
        this.editor.putBoolean("user_kyc_complete", isVisible);
        this.editor.apply();
    }

    public boolean showLoyaltyIntro() {
        return this.pref.getBoolean("show_loyalty_intro", true);
    }

    public void showLoyaltyIntro(boolean isVisible) {
        this.editor.putBoolean("show_loyalty_intro", isVisible);
        this.editor.apply();
    }


    public boolean getFeatureCompletionById(String id) {
        switch (id) {
            case BANK_ACCOUNT:
                return hasBankAccount(User.getBusinessId());
            case USER_KYC_COMPLETE:
                return hasCompletedKYC();
            case BRICK_ACCOUNT:
                return isBrickIntegrated();
            case USER_BUSINESS_PROFILE:
                return hasCompleteBusinessProfile();
            case USER_PROFILE:
                return hasCompleteUserProfile();
        }
        return this.pref.getBoolean(id, false);
    }

    public boolean hasCompletedKYC() {
//        return true;
        return this.pref.getBoolean("user_kyc_complete", false);
    }

    public void hasCompletedBrickConnection(boolean isVisible) {
        this.editor.putBoolean("brick_connection_complete", isVisible);
        this.editor.apply();
    }

    public boolean hasCompletedBrickConnection() {
        return this.pref.getBoolean("brick_connection_complete", false);
    }

    public boolean useMilitaryTime() {
        return this.pref.getBoolean("USING_MILITARY_TIME", true);
    }

    public void setUseMilitaryTime(boolean val) {
        this.editor.putBoolean("USING_MILITARY_TIME", val);
        this.editor.apply();
    }

    public boolean isDailyHighlightExplored() {
        return this.pref.getBoolean(DAILY_HIGHLIGHT_EXPLORED, true);
    }

    public void setDailyHighlightExplored(boolean val) {
        this.editor.putBoolean(DAILY_HIGHLIGHT_EXPLORED, val);
        this.editor.apply();
    }

    public boolean useDailyBusinessRecap() {
        return this.pref.getBoolean(USING_DAILY_BUSINESS_RECAP, false);
    }

    public void setDailyBusinessRecap(boolean val) {
        this.editor.putBoolean(USING_DAILY_BUSINESS_RECAP, val);
        this.editor.apply();
    }

    public boolean isDailyBusinessActivatedFlag() {
        return this.pref.getBoolean(DAILY_BUSINESS_ACTIVATED_FLAG, false);
    }

    public void setDailyBusinessActivatedFlag(boolean val) {
        this.editor.putBoolean(DAILY_BUSINESS_ACTIVATED_FLAG, val);
        this.editor.apply();
    }

    public boolean isDailyHighlightSettingsVisible() {
        return this.pref.getBoolean(DAILY_BUSINESS_SETTING_VISIBLE, false);
    }

    public void setDailyHighlightSettingsVisible(boolean val) {
        this.editor.putBoolean(DAILY_BUSINESS_SETTING_VISIBLE, val);
        this.editor.apply();
    }

    public void setBackupTimestamp(long lastBackup) {
        this.editor.putLong("BACKUP_TIME", lastBackup);
        this.editor.apply();
    }

    public long getBackupTimestamp() {
        return this.pref.getLong("BACKUP_TIME", System.currentTimeMillis());
    }

    public void isRestoreInProgress(boolean inProgress){
        this.editor.putBoolean("RESTORE_IN_PROGRESS",inProgress);
        this.editor.apply();
    }

    public boolean getDataRestoreProgressStatus(){
        return this.pref.getBoolean("RESTORE_IN_PROGRESS",false);
    }

    public void setFromTimeForAutoRecord(String fromTime) {
        this.editor.putString(FROM_TIME_FOR_AUTO_RECORD, fromTime);
        this.editor.apply();
    }

    public String getFromTimeForAutoRecord() {
        return this.pref.getString(FROM_TIME_FOR_AUTO_RECORD, "");
    }

    public void setAutoRecordIntroduced(boolean isIntroduced) {
        this.editor.putBoolean(AUTORECORD_INTRODUCED, isIntroduced);
        this.editor.apply();
    }

    public boolean isAutoRecordIntroduced() {
        return this.pref.getBoolean(AUTORECORD_INTRODUCED, false);
    }

    public void setCardColor(int color) {
        this.editor.putInt("BIZ_CARD_TEMPLATE", color);
        this.editor.apply();
    }

    public int getCardColor() {
        return this.pref.getInt("BIZ_CARD_TEMPLATE", R.drawable.bcard1);
    }

    public void hasAddedTransaction(boolean i) {
        this.editor.putBoolean("USER_ADDED_TRANS", i);
        this.editor.apply();
    }

    public boolean hasAddedTransaction() {
        return this.pref.getBoolean("USER_ADDED_TRANS", false);
    }

    public void hasUsedReminder(boolean i) {
        this.editor.putBoolean("USER_SET_REMINDER", i);
        this.editor.apply();
    }

    public boolean hasUsedReminder() {
        return this.pref.getBoolean("USER_SET_REMINDER", false);
    }

    public void hasAddedCst(boolean i) {
        this.editor.putBoolean("USER_HAS_ADDED_CST", i);
        this.editor.apply();
    }

    public boolean hasAddedCst() {
        return this.pref.getBoolean("USER_HAS_ADDED_CST", false);
    }

    public void disableTooltips() {
        hasAddedCst(true);
        hasUsedReminder(true);
        hasAddedTransaction(true);
    }

    public void setTooltipTarget(int count) {
        this.editor.putInt("TOOLTIP_TARGET_CST_COUNT", count);
        this.editor.apply();
    }

    public int getTooltipTarget() {
        return this.pref.getInt("TOOLTIP_TARGET_CST_COUNT", 2);
    }

    public void setPosToolTipTarget(int count) {
        this.editor.putInt("POS_TOOLTIP_TARGET_TRX_COUNT", count);
        this.editor.apply();
    }

    public int getPosTooltipTarget() {
        return this.pref.getInt("POS_TOOLTIP_TARGET_TRX_COUNT", 5);
    }

    public int getSelectedFilter() {
        return this.pref.getInt("CST_SELECTED_FILTER", 0);
    }

    public void setSelectedFilter(int count) {
        this.editor.putInt("CST_SELECTED_FILTER", count);
        this.editor.apply();
    }

    public int getCustomerListSortOrder() {
        return this.pref.getInt("CST_SORT_ORDER", 0);
    }

    public void setCustomerListSortOrder(int count) {
        this.editor.putInt("CST_SORT_ORDER", count);
        this.editor.apply();
    }

    public int getSelectedCashFilter() {
        return this.pref.getInt("CASH_SELECTED_FILTER", 0);
    }

    public void setSelectedCashFilter(int count) {
        this.editor.putInt("CASH_SELECTED_FILTER", count);
        this.editor.apply();
    }

    public int getCashListSortOrder() {
        return this.pref.getInt("CASH_SORT_ORDER", 0);
    }

    public void setCashListSortOrder(int count) {
        this.editor.putInt("CASH_SORT_ORDER", count);
        this.editor.apply();
    }

    public boolean isCstTourMode() {
        return this.pref.getBoolean("cst_tour_mode", false);
    }

    public void setCstTourMode(boolean i) {
        this.editor.putBoolean("cst_tour_mode", i);
        this.editor.apply();
    }

    public boolean finishedCstTourMode() {
        return this.pref.getBoolean("finish_tour_mode", false);
    }

    public void setFinishedCstTourMode(boolean i) {
        this.editor.putBoolean("finish_tour_mode", i);
        this.editor.apply();
    }

    public boolean cashTourFinished() {
        return this.pref.getBoolean("finish_cashtour_mode", false);
    }

    public void setCashTourFinished(boolean i) {
        this.editor.putBoolean("finish_cashtour_mode", i);
        this.editor.apply();
    }

    public boolean tourVisible() {
        return this.pref.getBoolean("tour_visible", false);
    }

    public void setTourVisible(boolean i) {
        this.editor.putBoolean("tour_visible", i);
        this.editor.apply();
    }

    public boolean hasSentBusinessInfo() {
        return this.pref.getBoolean("sent_business_info", false);
    }

    public void hasSentBusinessInfo(boolean flag) {
        this.editor.putBoolean("sent_business_info", flag);
        this.editor.apply();
    }

    public void setCashFeatureUseCount(int count) {
        this.editor.putInt("cash_use_count", 1);
        this.editor.apply();
    }

    public int cashFeatureUseCount() {
        return this.pref.getInt("cash_use_count", 1);
    }

    // TODO: duplicate with AppConfigManager.getWhatsappId
    public String getWhatsappId() {
        return this.pref.getString("whatsapp", "6283804550504");
    }

    public void setWhatsappId(String str) {
        this.editor.putString("whatsapp", str);
        this.editor.apply();
    }

    public int getNewInstallTransactionCount() {
        return this.pref.getInt("new_install_transaction_count", 0);
    }

    public void setNewInstallTransactionCount(int count) {
        this.editor.putInt("new_install_transaction_count", count);
        this.editor.apply();
    }

    public int getProductCount() {
        return this.pref.getInt("product_count", 0);
    }

    public void setProductCount(int count) {
        this.editor.putInt("product_count", count);
        this.editor.apply();
    }

    public int getProductTransactionCount() {
        return this.pref.getInt("product_transaction_count", 0);
    }

    public void setProductTransactionCount(int count) {
        this.editor.putInt("product_transaction_count", count);
        this.editor.apply();
    }

    public int getEnableGamifyDialog() {
        return this.pref.getInt("enable_gamify_dialog", 0);
    }

    public void setEnableGamifyDialog(int enableGamifyDialog) {
        this.editor.putInt("enable_gamify_dialog", enableGamifyDialog);
        this.editor.apply();
    }

    public boolean getEnablePaymentPushNotification() {
        return this.pref.getBoolean("enable_payment_push_notification", false);
    }

    public void setEnablePaymentPushNotification(boolean enablePaymentPushNotification) {
        this.editor.putBoolean("enable_payment_push_notification", enablePaymentPushNotification);
        this.editor.apply();
    }

    public boolean getStreaksDialogEnabled() {
        return this.pref.getBoolean("enable_streak_dialog", false);
    }

    public void setStreaksDialogEnabled(boolean flag) {
        this.editor.putBoolean("enable_streak_dialog", flag);
        this.editor.apply();
    }

    public boolean getOnBoardingTutorialDisabled() {
        return this.pref.getBoolean("disable_onBoarding_tutorial", false);
    }

    public void setOnBoardingTutorialDisabled(boolean disableOnBoardTutorial) {
        this.editor.putBoolean("disable_onBoarding_tutorial", disableOnBoardTutorial);
        this.editor.apply();
    }

    public boolean getOnBoardingTutorialDisplayed() {
        return this.pref.getBoolean("displayed_onBoarding_tutorial", false);
    }

    public void setOnBoardingTutorialDisplayed(boolean disableOnBoardTutorial) {
        this.editor.putBoolean("displayed_onBoarding_tutorial", disableOnBoardTutorial);
        this.editor.apply();
    }

    public boolean hasUnlockVoucher() {
        return this.pref.getBoolean("unlock_voucher_code", false);
    }

    public void setUnlockVoucher(boolean flag) {
        this.editor.putBoolean("unlock_voucher_code", flag);
        this.editor.apply();
    }

    public void setStreaksTimestamp(long streaksTimestamp) {
        this.editor.putLong("streaks_time_stamp", streaksTimestamp);
        this.editor.apply();
    }

    public Long getStreaksTimestamp() {
        return this.pref.getLong("streaks_time_stamp", -1);
    }

    public boolean hasStreaksShown() {
        return this.pref.getBoolean("streaks_shown", false);
    }

    public void setStreaksShown(boolean flag) {
        this.editor.putBoolean("streaks_shown", flag);
        this.editor.apply();
    }

    public boolean isRPUEligibleForStreaks() {
        return this.pref.getBoolean(RPU_STREAK_SHOWN, true);
    }

    public void setRPUEligibleForStreaks() {
        this.editor.putBoolean(RPU_STREAK_SHOWN, false);
        this.editor.apply();
    }

    public int getTransactionStage() {
        return this.pref.getInt("transaction_count_stage", 0);
    }

    public void setTransactionStage(int count) {
        this.editor.putInt("transaction_count_stage", count);
        this.editor.apply();
    }

    public long getInstallTimestamp() {
        return this.pref.getLong("install_timestamp", -1);
    }

    public void setInstallTimestamp(long timestamp) {
        this.editor.putLong("install_timestamp", timestamp);
        this.editor.apply();
    }

    public Boolean getShowKomisiAgenDialog(){
        return this.pref.getBoolean("show_komisi_agen_dialog", true);
    }

    public void setShowKomisiAgenDialog(Boolean value){
        this.editor.putBoolean("show_komisi_agen_dialog", value);
        this.editor.apply();
    }

    public String getInstallionMode() {
        return this.pref.getString("install_mode", "-");
    }

    public void setInstallionMode(String mode) {
        this.editor.putString("install_mode", mode);
        this.editor.apply();
    }

    public int getOldTransactionCount() {
        return this.pref.getInt("old_transaction_count", -1);
    }

    public void setOldTransactionCount(int count) {
        this.editor.putInt("old_transaction_count", count);
        this.editor.apply();
    }

    public int getNotificationCount() {
        return this.pref.getInt("notification_count", 0);
    }

    public void setNotificationCount(int count) {
        this.editor.putInt("notification_count", count);
        this.editor.apply();
    }

    public boolean hasUnseenNotification() {
        return this.pref.getBoolean("unseen_notification", false);
    }

    public void setUnseenNotification(boolean flg) {
        this.editor.putBoolean("unseen_notification", flg);
        this.editor.apply();
    }

    public boolean hasRecordedFirstCash() {
        return this.pref.getBoolean("first_cash_transaction_recorded", false);
    }

    public void setHasRecordedFirstCash(boolean flg) {
        this.editor.putBoolean("first_cash_transaction_recorded", flg);
        this.editor.apply();
    }

    public boolean hasRecordedW1Record() {
        return this.pref.getBoolean("w1_created", false);
    }

    public void hasRecordedW1Record(boolean flg) {
        this.editor.putBoolean("w1_created", flg);
        this.editor.apply();
    }

    public boolean hasRecordedM1Record() {
        return this.pref.getBoolean("m1_created", false);
    }

    public void hasRecordedM1Record(boolean flg) {
        this.editor.putBoolean("m1_created", flg);
        this.editor.apply();
    }

    public boolean hasRecordedD1Record() {
        return this.pref.getBoolean("d1_created", false);
    }

    public void hasRecordedD1Record(boolean flg) {
        this.editor.putBoolean("d1_created", flg);
        this.editor.apply();
    }

    public int exitWithoutTransaction() {
        return this.pref.getInt("exit_without_txn", 0);
    }

    public void exitWithoutTransaction(int flg) {
        this.editor.putInt("exit_without_txn", flg);
        this.editor.apply();
    }

    public boolean isExitWithoutAddBankAccount(int paymentType) {
        if(paymentType == PaymentConst.TYPE_PAYMENT_IN)
            return pref.getBoolean(EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_IN, false);
        return pref.getBoolean(EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_OUT, false);
    }

    public boolean isExitWithoutCompletingPayment(int paymentType) {
        if(paymentType == PaymentConst.TYPE_PAYMENT_IN)
            return pref.getBoolean(EXIT_WITHOUT_COMPLETING_PAYMENT_IN, false);
        return pref.getBoolean(EXIT_WITHOUT_COMPLETING_PAYMENT_OUT, false);
    }

    public PaymentExitIntentData getPaymentExitIntentData() {
        String json = pref.getString(PAYMENT_EXIT_INTENT_DATA, null);
        if (json == null || json.isEmpty()) return null;
        try {
            return new Gson().fromJson(json, PaymentExitIntentData.class);
        } catch (Exception e) {
            return null;
        }
    }

    public void setExitWithoutAddBankAccount(boolean flag, int paymentType, PaymentExitIntentData data) {
        if(paymentType == PaymentConst.TYPE_PAYMENT_IN)
            this.editor.putBoolean(EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_IN, flag);
        else if(paymentType == PaymentConst.TYPE_PAYMENT_OUT)
            this.editor.putBoolean(EXIT_WITHOUT_ADD_BANK_ACCOUNT_PAYMENT_OUT, flag);
        else
            return;
        String json = new Gson().toJson(data);
        this.editor.putString(PAYMENT_EXIT_INTENT_DATA, json);
        this.editor.apply();
    }

    public void setExitWithoutCompletingPayment(boolean flag, int paymentType, PaymentExitIntentData data) {
        setExitWithoutCompletingPayment(flag, paymentType);
        String json = new Gson().toJson(data);
        this.editor.putString(PAYMENT_EXIT_INTENT_DATA, json);
        this.editor.apply();
    }

    public void setExitWithoutCompletingPayment(boolean flag, int paymentType) {
        if (paymentType == PaymentConst.TYPE_PAYMENT_IN)
            this.editor.putBoolean(EXIT_WITHOUT_COMPLETING_PAYMENT_IN, flag);
        else
            this.editor.putBoolean(EXIT_WITHOUT_COMPLETING_PAYMENT_OUT, flag);
        this.editor.apply();
    }

    public void setCountPaymentPushNotification(int paymentType,int count){
        if(paymentType == PaymentConst.TYPE_PAYMENT_IN)
            this.editor.putInt(PAYMENT_IN_PUSH_NOTIFICATION_COUNT,count);
        else
            this.editor.putInt(PAYMENT_OUT_PUSH_NOTIFICATION_COUNT,count);
    }

    public int getCountPaymentPushNotification(int paymentType){
        if(paymentType == PaymentConst.TYPE_PAYMENT_IN)
            return this.pref.getInt(PAYMENT_IN_PUSH_NOTIFICATION_COUNT,0);
        else
            return this.pref.getInt(PAYMENT_OUT_PUSH_NOTIFICATION_COUNT,0);
    }

    public boolean hasUploadedContact() {
        return this.pref.getBoolean("uploaded_contact",false);
    }

    public void setUploadedContact(boolean flg) {
        this.editor.putBoolean("uploaded_contact", flg);
        this.editor.apply();
    }

    public boolean cashModuleEnabled() {
        return true; //currently will always set as true
//        return this.pref.getBoolean("cash_module_enabled", true);
    }

    public void setCashModuleEnabled(boolean flg) {
        this.editor.putBoolean("cash_module_enabled", flg);
        this.editor.apply();
    }

    public boolean paymentTabEnabled() {
        return this.pref.getBoolean(PAYMENT_TAB_ENABLED, true);
    }

    public void setPaymentTabEnabled(boolean flag) {
        this.editor.putBoolean(PAYMENT_TAB_ENABLED, flag);
        this.editor.apply();
    }

    public void setLoyaltySummary(LoyaltySummary loyaltySummary) {
        String summaryJson = new Gson().toJson(loyaltySummary);
        editor.putString("loyalty_summary", summaryJson);
        editor.apply();
    }

    public LoyaltySummary getLoyaltySummary()  {
        String summaryJson = this.pref.getString("loyalty_summary", null);
        try {
            return new Gson().fromJson(summaryJson, LoyaltySummary.class);
        } catch (JsonSyntaxException ex) {
            return new LoyaltySummary(0,"","",false);
        }
    }

    public boolean getInformationOptionalVisibility() {
        return this.pref.getBoolean(INFORMATION_OPTIONAL_VISIBILITY, false);
    }

    public void setInformationOptionalVisibility(boolean flag) {
        this.editor.putBoolean(INFORMATION_OPTIONAL_VISIBILITY, flag);
        this.editor.apply();
    }

    public boolean stockTabEnabled() {
        return this.pref.getBoolean(STOCK_TAB_ENABLED, false);
    }

    public void setStockTabEnabled(boolean flag) {
        this.editor.putBoolean(STOCK_TAB_ENABLED, flag);
        this.editor.apply();
    }
    public int stockTabEnabledFromSettings() {
        return this.pref.getInt(STOCK_TAB_ENABLED_FROM_SETTINGS, STOCK_NOT_USED);
    }

    public int autoRecordTabEnabledFromSettings() {
        return this.pref.getInt(AUTO_RECORD_TAB_ENABLED_FROM_SETTINGS, AUTO_RECORD_NOT_SELECTED);
    }

    public void setStockTabEnabledFromSettings(int stockSelectionState) {
        this.editor.putInt(STOCK_TAB_ENABLED_FROM_SETTINGS, stockSelectionState);
        this.editor.apply();
    }

    public void setAutoRecordTabEnabledFromSettings(int autoRecordSelectionState) {
        this.editor.putInt(AUTO_RECORD_TAB_ENABLED_FROM_SETTINGS, autoRecordSelectionState);
        this.editor.apply();
    }
    public boolean stockTabVisible() {
        return this.pref.getBoolean(STOCK_TAB_VISIBLE, false);
    }

    public void stockTabVisible(boolean flag) {
        this.editor.putBoolean(STOCK_TAB_VISIBLE, flag);
        this.editor.apply();
    }

    public boolean hasBankAccount(String bookId) {
        return this.pref.getBoolean(HAS_BANK_ACCOUNT + bookId, false);
    }

    public void setHasBankAccount(boolean flag, String bookId) {
        this.editor.putBoolean(HAS_BANK_ACCOUNT + bookId, flag);
        this.editor.apply();
    }

    public List<TabName> getTabNames() {
        List<TabName> tabNames = new ArrayList<>();
        if(RemoteConfigUtils.TransactionTabConfig.INSTANCE.mergedTab()) {
            if (FeaturePrefManager.getInstance().cashModuleEnabled()) {
                tabNames.add(TabName.TRANSACTION_HOME);
            }
        } else {
            tabNames.add(TabName.CUSTOMER);
            if (FeaturePrefManager.getInstance().cashModuleEnabled()) {
                tabNames.add(TabName.TRANSACTION);
            }
        }
        if ((FeaturePrefManager.getInstance().stockTabEnabled() &&
                FeaturePrefManager.getInstance().stockTabEnabledFromSettings() == FeaturePrefManager.STOCK_NOT_USED)
                ||  (FeaturePrefManager.getInstance().stockTabEnabledFromSettings() == FeaturePrefManager.STOCK_SELECTED)) {
            tabNames.add(TabName.STOCK);
        }
        if (FeaturePrefManager.getInstance().paymentTabEnabled()) {
            tabNames.add(TabName.PAYMENT);
        }
        tabNames.add(TabName.OTHERS);
        return tabNames;
    }

    public List<TabName> getNewTabNames() {
        List<TabName> tabNames = new ArrayList<>();
        tabNames.add(TabName.HOME);

        if(RemoteConfigUtils.TransactionTabConfig.INSTANCE.mergedTab()) {
            if (FeaturePrefManager.getInstance().cashModuleEnabled()) {
                tabNames.add(TabName.TRANSACTION_HOME);
            }
        } else {
            tabNames.add(TabName.CUSTOMER);
            if (FeaturePrefManager.getInstance().paymentTabEnabled()) {
                tabNames.add(TabName.PAYMENT);
            }
            if (FeaturePrefManager.getInstance().cashModuleEnabled()) {
                tabNames.add(TabName.TRANSACTION);
            }
        }
        tabNames.add(TabName.OTHERS);
        return tabNames;
    }



    public int getTabIndex(TabName tabName) {
        int index = getTabNames().indexOf(tabName);
        return Math.max(index, 0);
    }

    public boolean sendSMSOnNewTransactionByDefault() {
        return this.pref.getBoolean("sendSMSOnNewTransactionByDefault", false);
    }

    public void setSendSMSOnNewTransactionByDefault(Boolean isDefault) {
        this.editor.putBoolean("sendSMSOnNewTransactionByDefault", isDefault);
        this.editor.apply();
    }

    public void setHasShownLainnyaMenu(Boolean hasShown){
        this.editor.putBoolean("lainnyaMenuHasShown", hasShown);
        this.editor.apply();
    }

    public boolean getHasShownLainnyaMenu(){
        return this.pref.getBoolean("lainnyaMenuHasShown",false);
    }

    public void setHasShowReceiptAddressToolTip(boolean b){
        this.editor.putBoolean("printer_receipt_address", b);
        this.editor.apply();
    }

    public boolean getHasShowReceiptAddressToolTip(){
        return this.pref.getBoolean("printer_receipt_address", false);
    }

    public boolean hasShownCollectingCalendarAddTutor(){
        return  this.pref.getBoolean("collecting_calendar_add", false);
    }

    public void setHasShownCollectingCalendarAddTutor(boolean b){
        this.editor.putBoolean("collecting_calendar_add", b);
        this.editor.apply();
    }

    public boolean isShowingFilterTutor(){
        return this.pref.getBoolean("filter_tutor_is_showing", false);
    }

    public void setIsShowingFilterTutor(boolean b){
        this.editor.putBoolean("filter_tutor_is_showing", b);
        this.editor.apply();
    }

    public boolean trackTokenRefreshAfterUpdate(){
        return this.pref.getBoolean("fcm_refresh_after_update", false);
    }

    public void setTrackTokenRefreshAfterUpdate(boolean b){
        this.editor.putBoolean("fcm_refresh_after_update", b);
        this.editor.apply();
    }

    public void stockBadgeFirstTimeDisplayed(boolean b){
        this.editor.putBoolean(STOCK_FIRST_BADGE_DISPLAYED, b);
        this.editor.apply();
    }

    public void stockBadgeFirstTimeDisplayConditions(boolean b) {
        this.editor.putBoolean(STOCK_FIRST_BADGE_DISPLAY_CONDITIONS, b);
        this.editor.apply();
    }
    public Boolean isStockBadgeFirstTimeDisplayConditions() {
        return this.pref.getBoolean(STOCK_FIRST_BADGE_DISPLAY_CONDITIONS, false);
    }

    public Boolean isStockBadgeFirstTimeDisplayed() {
        if(!isStockBadgeFirstTimeDisplayConditions()) return true;
        return this.pref.getBoolean(STOCK_FIRST_BADGE_DISPLAYED, true);
    }

    public boolean hasStockEligibilityCheckedAfterUpdate() {
        return this.pref.getBoolean("check_stock_tab_after_update", true);
    }

    public void setStockEligibilityCheckedAfterUpdate(boolean b){
        this.editor.putBoolean("check_stock_tab_after_update", b);
        this.editor.apply();
    }

    public String getCashBalanceValue() {
        return this.pref.getString("balance_summary_value", "0");
    }

    public void setCashBalanceValue(String value) {
        this.editor.putString("balance_summary_value", value);
        this.editor.apply();
    }

    public String getCashInValue() {
        return this.pref.getString("cash_in_value", "0");
    }

    public void setCashInValue(String value) {
        this.editor.putString("cash_in_value", value);
        this.editor.apply();
    }

    public String getCashOutValue() {
        return this.pref.getString("cash_out_value", "0");
    }

    public void setCashOutValue(String value) {
        this.editor.putString("cash_out_value", value);
        this.editor.apply();
    }

    public String getDebitValue() {
        return this.pref.getString("debit_value", "0");
    }

    public void setDebitValue(String value) {
        this.editor.putString("debit_value", value);
        this.editor.apply();
    }

    public String getCreditValue() {
        return this.pref.getString("credit_value", "0");
    }

    public void setCreditValue(String value) {
        this.editor.putString("credit_value", value);
        this.editor.apply();
    }

    public String getCashBalanceStatus() {
        return this.pref.getString("balance_summary_status", "0");
    }

    public void setEdcOrderId(String orderId) {
        this.editor.putString("edc_order_id", orderId);
        this.editor.apply();
    }

    public String getEdcOrderId() {
        return this.pref.getString("edc_order_id", "");
    }

    public void setCashBalanceStatus(String value) {
        this.editor.putString("balance_summary_status", value);
        this.editor.apply();
    }

    public String getUtangSearchKeyword() {
        return this.pref.getString("utang_search_keyword", "");
    }

    public void setUtangSearchKeyword(String value) {
        this.editor.putString("utang_search_keyword", value);
        this.editor.apply();
    }

    public String getTransaksiSearchKeyword() {
        return this.pref.getString("transaksi_search_keyword", "");
    }

    public void setTransaksiSearchKeyword(String value) {
        this.editor.putString("transaksi_search_keyword", value);
        this.editor.apply();
    }

    public boolean isNewUser() {
        return this.pref.getBoolean("is_new_user", false);
    }

    public void setNewUserFlag(boolean b){
        this.editor.putBoolean("is_new_user", b);
        this.editor.apply();
    }

    public void setProfileVisitCount() {
        int count = getProfileVisitCount();
        editor.putInt(PROFILE_VISIT_COUNT, ++count);
        editor.apply();
    }

    public int getProfileVisitCount() {
        return this.pref.getInt(PROFILE_VISIT_COUNT, 0);
    }

    public void setIsSearchFilterCustomerSeen() {
        editor.putBoolean(IS_SEARCH_FILTER_CUSTOMER_SEEN, true);
        editor.apply();
    }

    public void setIsDateFilterCustomerSeen() {
        editor.putBoolean(IS_DATE_FILTER_CUSTOMER_SEEN, true);
        editor.apply();
    }

    public void setIsSearchFilterTransactionSeen() {
        editor.putBoolean(IS_SEARCH_FILTER_TRANSACTION_SEEN, true);
        editor.apply();
    }

    public void setIsDateFilterTransactionSeen() {
        editor.putBoolean(IS_DATE_FILTER_TRANSACTION_SEEN, true);
        editor.apply();
    }

    public boolean isDateFilterCustomerSeen() {
        return pref.getBoolean(IS_DATE_FILTER_CUSTOMER_SEEN, false);
    }

    public boolean isSearchFilterTransactionSeen() {
        return pref.getBoolean(IS_SEARCH_FILTER_TRANSACTION_SEEN, false);
    }

    public boolean isDateFilterTransactionSeen() {
        return pref.getBoolean(IS_DATE_FILTER_TRANSACTION_SEEN, false);
    }

    public boolean isSearchFilterCustomerSeen() {
        return pref.getBoolean(IS_SEARCH_FILTER_CUSTOMER_SEEN, false);
    }

    public void setSimpanForBusinessCardClicked() {
        editor.putBoolean(IS_SIMPAN_FOR_BUSINESS_CARD_CLICKED, true);
        editor.apply();
    }

    public boolean isSimpanForBusinessCardClicked() {
        return pref.getBoolean(IS_SIMPAN_FOR_BUSINESS_CARD_CLICKED, false);
    }

    public boolean isInvoicePowerUser() {
        return pref.getBoolean("is_invoice_power_user", false);
    }

    public void setInvoicePowerUser(boolean puFlag) {
        editor.putBoolean("is_invoice_power_user", puFlag);
        editor.apply();
    }

    public void setInvoiceUseCount(int ignoreCount) {
        editor.putInt("invoice_use_count", ignoreCount);
        editor.apply();
    }

    public int getInvoiceUseCount() {
        return this.pref.getInt("invoice_use_count", 0);
    }

    public boolean productCoachMarkForNoSellingPriceDisplayed() {
        return this.pref.getBoolean(PRODUCT_COACH_MARK_FOR_NO_SELLING_PRICE, false);
    }
    public void setProductCoachMarkForNoSellingPriceDisplayed(boolean state) {
        editor.putBoolean(PRODUCT_COACH_MARK_FOR_NO_SELLING_PRICE, state);
        editor.apply();
    }

    public void addListener(SharedPreferences.OnSharedPreferenceChangeListener listener) {
        prefManager.pref.registerOnSharedPreferenceChangeListener(listener);
    }

    public void removeListener(SharedPreferences.OnSharedPreferenceChangeListener listener) {
        prefManager.pref.unregisterOnSharedPreferenceChangeListener(listener);
    }

    public String getNewBizzCardDesign() {
        return pref.getString(NEW_BIZZ_CARD_DESIGN, "");
    }

    public void setNewBizzCardDesign(String uuid){
        editor.putString(NEW_BIZZ_CARD_DESIGN, uuid);
        editor.apply();
    }

    public void setPosNotificationShown() {
        editor.putBoolean(POS_NOTIFICATION_SENT, true);
        editor.apply();
    }

    public boolean isPosNotificationSent() {
        return pref.getBoolean(POS_NOTIFICATION_SENT, false);
    }

    public boolean isBusinessCardSavedOnce() {
        return this.pref.getBoolean(BUSINESS_CARD_SAVED_ONCE, false);
    }

    public void setBusinessCardSavedOnce(boolean val) {
        this.editor.putBoolean(BUSINESS_CARD_SAVED_ONCE, val);
        this.editor.apply();
    }

    public void setHomeCoachMarkSeen() {
        this.editor.putBoolean(IS_HOME_COACHMARK_SEEN, true);
    }

    public boolean isHomeCoachmarkSeen() {
        return this.pref.getBoolean(IS_HOME_COACHMARK_SEEN, false);
    }

    public void setUsageGoalScreen(int tabId) {
        this.editor.putInt(USAGE_GOAL_TAB, tabId);
    }

    public int getUsageGoalScreen() {
        return this.pref.getInt(USAGE_GOAL_TAB, TabName.TRANSACTION.ordinal());
    }

    public boolean isHasShownCatalogTooltip() {
        return this.pref.getBoolean("catalog_tooltip", false);
    }

    public void setHasShownCatalogTooltip(boolean hasShownCatalogTooltip) {
        this.editor.putBoolean("catalog_tooltip", hasShownCatalogTooltip);
        this.editor.apply();
    }


    public boolean isAutoRecordEnabled() {
        return this.pref.getBoolean(AUTO_RECORD_ENABLED, false);
    }

    public void setAutoRecordEnabled(boolean val) {
        this.editor.putBoolean(AUTO_RECORD_ENABLED, val);
        this.editor.apply();
    }

    public boolean isCompletedNotesMission() {
        return this.pref.getBoolean(IS_NOTES_MISSION_COMPLETE, false);
    }

    public void hasCompletedNotesMission(boolean isComplete) {
        this.editor.putBoolean(IS_NOTES_MISSION_COMPLETE, isComplete);
        this.editor.apply();
    }

    public boolean isNoteRemovalIntroduced() {
        return this.pref.getBoolean(IS_NOTES_REMOVAL_INTRODUCED, false);
    }

    public void setNoteRemovedIntroduced(boolean isComplete) {
        this.editor.putBoolean(IS_NOTES_REMOVAL_INTRODUCED, isComplete);
        this.editor.apply();
    }

    public boolean isTooltipForMandatoryTransactionCategorySeen() {
        return this.pref.getBoolean(MANDATORY_CATEGORY_TOOLTIP_SEEN, false);
    }

    public void setTooltipForMandatoryTransactionCategorySeen() {
        this.editor.putBoolean(MANDATORY_CATEGORY_TOOLTIP_SEEN, true);
        this.editor.apply();
    }

    public void setBookCategoriesUpdated(boolean isUpdated) {
        this.editor.putBoolean(BUSINESS_CATEGORY_UPDATED, isUpdated);
        this.editor.apply();
    }

    public boolean areBookCategoriesUpdated() {
        return this.pref.getBoolean(BUSINESS_CATEGORY_UPDATED, false);
    }

    public boolean isProductDashboardWelcomeScreenSeen() {
        return this.pref.getBoolean(PRODUCT_WELCOME_SCREEN, false);
    }

    public void setProductDashboardWelcomeScreenSeen() {
        this.editor.putBoolean(PRODUCT_WELCOME_SCREEN, true);
    }

    public boolean isBrickIntegrated() {
        return this.pref.getBoolean(BRICK_INTEGRATED, false);

    }
    public void setBrickIntegrated(boolean isBrickIntegrated) {
        this.editor.putBoolean(BRICK_INTEGRATED, isBrickIntegrated);
        this.editor.apply();
    }

    public boolean isLainnyaProfileCompleted() {
        return this.pref.getBoolean(LAINNYA_PROFILE_COMPLETION_STATUS, false);
    }

    public void setLainnyaProfileCompleted(boolean isLainnyaProfileCompleted) {
        this.editor.putBoolean(LAINNYA_PROFILE_COMPLETION_STATUS, isLainnyaProfileCompleted);
        this.editor.apply();
    }


    public boolean isBrickIntegratedAtleastOnce() {
        return this.pref.getBoolean(BRICK_INTEGRATED_FIRST_TIME, false);

    }

    public void setBrickIntegratedAtleastOnce(boolean isBrickIntegrated) {
        this.editor.putBoolean(BRICK_INTEGRATED_FIRST_TIME, isBrickIntegrated);
        this.editor.apply();
    }

    public boolean isTxnCategoryMigrationShown() {
        return this.pref.getBoolean(TXN_CATEGORY_REVAMP_TOOLTIP, false);

    }

    public void setTxnCategoryMigrationShown(boolean shown) {
        this.editor.putBoolean(TXN_CATEGORY_REVAMP_TOOLTIP, shown);
        this.editor.apply();
    }

    public void setBusinessDashboardPpobProductShown(boolean shown) {
        this.editor.putBoolean(BUSINESS_DASHBOARD_PPOB_PRODUCT, shown);
        this.editor.apply();
    }

    public Boolean getBusinessDashboardPpobProductShown() {
        return this.pref.getBoolean(BUSINESS_DASHBOARD_PPOB_PRODUCT, true);
    }
    public void setBusinessDashboardPayment(boolean shown) {
        this.editor.putBoolean(BUSINESS_DASHBOARD_PAYMENT, shown);
        this.editor.apply();
    }

    public Boolean getBusinessDashboardPayment() {
        return this.pref.getBoolean(BUSINESS_DASHBOARD_PAYMENT, false);
    }

    public void setUserHasLoggedIn(String key) {
        if (!this.pref.getStringSet(LIST_OF_NUMBERS, new HashSet<>()).contains(key)) {
            Set<String> stringSet = new HashSet<>(this.pref.getStringSet(LIST_OF_NUMBERS, new HashSet<>()));
            stringSet.add(key);
            this.editor.putStringSet(LIST_OF_NUMBERS, stringSet);
            this.editor.apply();
        }
    }

    public Boolean getHasUserLoggedIn(String key) {
        return this.pref.getStringSet(LIST_OF_NUMBERS, new HashSet<>()).contains(key);
    }

    public Set<String> getLoggedInUserNumbers() {
        return this.pref.getStringSet(LIST_OF_NUMBERS, new HashSet<>());
    }

    public void setLoggedInUserNumbers(Set<String> stringSet) {
        this.editor.putStringSet(LIST_OF_NUMBERS, stringSet);
        this.editor.apply();
    }

    public void setTickerCloseTime() {
        this.editor.putLong(TICKER_CLOSE_TIME, System.currentTimeMillis());
        this.editor.apply();
    }

    public long getTickerCloseTime() {
        return this.pref.getLong(TICKER_CLOSE_TIME, 0L);
    }

    public void setPrevDetectTimeKey(long timestamp) {
        this.editor.putLong(PREV_DETECT_TIME_KEY, timestamp);
        this.editor.apply();
    }

    public long getPrevDetectTimeKey() {
        return this.pref.getLong(PREV_DETECT_TIME_KEY,0);
    }
}
