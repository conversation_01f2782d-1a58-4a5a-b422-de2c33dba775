package com.bukuwarung.preference

import android.content.Context
import android.content.SharedPreferences
import com.bukuwarung.Application
import com.bukuwarung.constants.PrefrenceConstant
import javax.inject.Singleton


private var prefManager: OnboardingPrefManager? = null

private lateinit var editor: SharedPreferences.Editor
private lateinit var pref: SharedPreferences

@Singleton
class OnboardingPrefManager(val context: Context) {
    companion object {
        const val TOUR_STOCK_TAB_ADD_BTN = "TOUR_STOCK_TAB_ADD_BTN"
        const val TOUR_CASH_TAB_ADD_BTN = "TOUR_CASH_TAB_ADD_BTN"
        const val TOUR_AUTORECORD_TXN_BTN = "TOUR_AUTORECORD_TXN_BTN"
        const val TOUR_CASH_TAB_ADD_BTN_STEP2 = "TOUR_CASH_TAB_ADD_BTN_STEP2"
        const val TOUR_CASH_TAB_ADD_BTN_STEP3 = "TOUR_CASH_TAB_ADD_BTN_STEP3"
        const val TUTOR_SAVE_TRX = "TUTOR_SAVE_TRX"
        const val TUTOR_SAVE_UTANG_PIUTANG = "TUTOR_SAVE_UTANG_PIUTANG"
        const val TUTOR_CUSTOMER_TAB = "TUTOR_CUSTOMER_TAB"
        const val TUTOR_ADD_CUSTOMER = "TUTOR_ADD_CUSTOMER"
        const val TOUR_CUSTOMER_TAB_ADD_BTN = "TOUR_CUSTOMER_TAB_ADD_BTN"
        const val TOUR_PULSA_COMPLETION = "TOUR_PULSA_COMPLETION"
        const val TUTORIAL_CASH_MODAL = "TUTORIAL_CASH_MODAL"
        const val TUTORIAL_CONTACT_PERMISSION = "TUTORIAL_CONTACT_PERMISSION"

        const val TUTORIAL_DOWNLOAD_REPORT_DEBT = "TUTORIAL_DOWNLOAD_REPORT_DEBT"
        const val TUTORIAL_DOWNLOAD_REPORT_CASH = "TUTORIAL_DOWNLOAD_REPORT_CASH"
        const val TUTORIAL_SEARCH_DEBT = "TUTORIAL_SEARCH_DEBT"
        const val TUTORIAL_SEARCH_CASH = "TUTORIAL_SEARCH_CASH"
        const val TUTORIAL_CALENDAR = "TUTORIAL_CALENDAR"
        const val TUTORIAL_PPOB_PULSA_CAPITAL_PRICE = "TUTORIAL_PPOB_PULSA_CAPITAL_PRICE"
        const val TUTORIAL_PPOB_PULSA_SELLING_PRICE = "TUTORIAL_PPOB_PULSA_SELLING_PRICE"
        const val TUTORIAL_MANUAL_REMINDER = "TUTORIAL_MANUAL_REMINDER"
        const val TUTORIAL_PPOB_INTRODUCTION = "TUTORIAL_PPOB_INTRODUCTION"
        const val TUTORIAL_PEMBAYARAN_CTA = "TUTORIAL_PEMBAYARAN_CTA"
        const val TUTORIAL_FAVOURITE_CTA = "TUTORIAL_FAVOURITE_CTA"
        const val TUTORIAL_SALDO_CTA = "TUTOR_SALDO_BENEFITS"
        const val TUTORIAL_ORDER_HISTORY_TAB = "TUTOR_PAYMENT_HISTORY_TAB"
        const val TUTORIAL_ORDER_HISTORY_FILTER = "TUTOR_PAYMENT_HISTORY_FILTER"
        const val TUTORIAL_MANUAL_REMINDER_FOR_PAYMENT_LINK = "TUTORIAL_MANUAL_REMINDER_FOR_PAYMENT_LINK"
        const val TUTORIAL_PAYMENT = "TUTORIAL_PAYMENT"
        const val TUTORIAL_DATE_FILTER = "TUTORIAL_FILTER_CASH"
        const val TUTORIAL_DATE_FILTER_CUSTOMER = "TUTORIAL_FILTER_DEBT"
        const val TUTORIAL_DUE_DATE = "TUTORIAL_DUE_DATE"
        const val TUTORIAL_UTANG_SETTLEMENT = "TUTORIAL_UTANG_SETTLEMENT"
        const val TUTOR_CREATE_PRODUCT = "TUTOR_CREATE_PRODUCT"
        const val TUTOR_PRODUCT_QUANTITY = "TUTOR_PRODUCT_QUANTITY"
        const val TUTOR_PRODUCT_DETAIL = "TUTOR_PRODUCT_DETAIL"
        const val TUTOR_BANK_LIST_LAINNYA = "BANK_LIST_LAINNYA"
        const val HAS_SHOWN_AUTO_RECORD_COACHMARK = "HAS_SHOWN_AUTO_RECORD_COACHMARK"
        const val HAS_SHOWN_LAINNYA_CAROUSEL_ITEMS = "HAS_SHOWN_LAINNYA_CAROUSEL_ITEMS"
        const val TUTOR_PRODUCT_DETAIL_WITH_INVENTORY = "TUTOR_PRODUCT_DETAIL_WITH_INVENTORY"
        const val TUTOR_INPUT_BANK_ACCOUNT = "TUTOR_INPUT_BANK_ACCOUNT"
        const val TUTOR_INPUT_BANK_ACCOUNT_STEP2 = "TUTOR_INPUT_BANK_ACCOUNT_STEP2"
        const val TUTORIAL_LINK_UNPAID_CASH_TO_PAYMENT = "TUTORIAL_LINK_UNPAID_CASH_TO_PAYMENT"

        const val TRX_AMOUNT_DETAIL_TOGGLE = "TRX_AMOUNT_DETAIL_TOGGLE"
        const val PROFILE_COMPLETION_DIALOG = "PROFILE_COMPLETION_DIALOG"

        const val TUTOR_PRODUCT_STOCK_DETAIL = "TUTOR_PRODUCT_STOCK_DETAIL"
        const val TUTOR_PRODUCT_STOCK_FIRST_TIME_USER = "TUTOR_PRODUCT_STOCK_FIRST_TIME_USER"
        const val TUTOR_PRODUCT_STOCK_ZERO_SELLING_PRICE = "TUTOR_PRODUCT_STOCK_ZERO_SELLING_PRICE"

        const val TUTOR_TRANSAKSI_NEW_TAB = "TUTOR_TRANSAKSI_NEW_TAB"
        const val TUTOR_TRANSACTION_TAB = "TUTOR_TRANSACTION_TAB"
        const val TUTOR_DAILY_BUSINESS_UPDATE = "TUTOR_DAILY_BUSINESS_UPDATE"
        const val SEND_APPS_FLYER_ID = "SEND_APPS_FLYER_ID"

        const val POS_TUTOR_PRODUCT_TO_CART_FIRST_TIME_USER = "POS_TUTOR_PRODUCT_TO_CART_FIRST_TIME_USER"
        const val POS_TUTOR_PRODUCT_EDIT_FIRST_TIME_USER = "POS_TUTOR_PRODUCT_EDIT_FIRST_TIME_USER"
        const val POS_TUTOR_PROCEED_TO_CART_FIRST_TIME_USER = "POS_TUTOR_PROCEED_TO_CART_FIRST_TIME_USER"

        const val TUTOR_HOME_PAGE = "TUTOR_HOME_PAGE"

        const val CATEGORY_SELECT_INFO = "tutor_trx_category_first_time"

        fun getInstance(): OnboardingPrefManager {
            if (prefManager == null) {
                prefManager = OnboardingPrefManager(Application.getAppContext())
            }
            return prefManager!!
        }
    }

    init {
        pref = context.getSharedPreferences(PrefrenceConstant.ONBOARDING_PREF, PrefrenceConstant.PRIVATE_MODE)
        editor = pref.edit()
    }


    fun setHasFinishedForId(id: String?) {
        if (id == null) return
        editor.putBoolean(id, true)
        editor.apply()
    }

    fun getHasFinishedForId(id: String): Boolean {
        return pref.getBoolean(id, false)
    }

    fun setHasShownAutoRecordCoachmark() {
        editor.putBoolean(OnboardingPrefManager.HAS_SHOWN_AUTO_RECORD_COACHMARK, true)
        editor.apply()
    }

    fun getHasShownAutoRecordCoachmark() : Boolean {
        return pref.getBoolean(OnboardingPrefManager.HAS_SHOWN_AUTO_RECORD_COACHMARK, false)
    }

    fun getHasShownLainnyaCarouselItems() : Boolean {
        return pref.getBoolean(HAS_SHOWN_LAINNYA_CAROUSEL_ITEMS, false)
    }

    fun setHasShownLainnyaCarouselItems() {
        editor.putBoolean(HAS_SHOWN_LAINNYA_CAROUSEL_ITEMS, true)
        editor.apply()
    }

}
