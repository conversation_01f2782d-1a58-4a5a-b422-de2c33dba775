package com.bukuwarung.preference

import android.content.Context
import android.content.SharedPreferences
import com.bukuwarung.Application
import com.bukuwarung.constants.PrefrenceConstant
import javax.inject.Singleton


private var prefManager: NewFeatureBadgePrefManager? = null

private lateinit var editor: SharedPreferences.Editor
private lateinit var pref: SharedPreferences

@Singleton
class NewFeatureBadgePrefManager(val appContext: Context) {
    companion object {
        const val AUTO_RECORD_FEATURE_BADGE_ID = "AUTO_RECORD_FEATURE_BADGE_ID"

        fun getInstance(): NewFeatureBadgePrefManager {
            if (prefManager == null) {
                prefManager = NewFeatureBadgePrefManager(Application.getAppContext())
            }
            return prefManager!!
        }
    }
    init {
        pref = appContext.getSharedPreferences(PrefrenceConstant.ONBOARDING_PREF, PrefrenceConstant.PRIVATE_MODE)
        editor = pref.edit()
    }


    fun setHasShownNewBadgeForId(id: String?) {
        if (id == null) return
        editor.putBoolean(id, true)
        editor.apply()
    }

    fun getHasShownNewBadgeForId(id: String): Boolean {
        return pref.getBoolean(id, false)
    }
}