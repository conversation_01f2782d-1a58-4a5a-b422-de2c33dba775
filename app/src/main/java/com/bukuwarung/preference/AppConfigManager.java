package com.bukuwarung.preference;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.R;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.PrefrenceConstant;
import com.bukuwarung.dialogs.businessselector.BusinessType;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;

public class AppConfigManager {

    private static AppConfigManager prefManager;
    private static String APP_CONFIG_OTP_WAIT_TIME = "otp_wait_time_in_sec";
    private static String APP_CONFIG_REFERRAL_TITLE = "referral_title";
    private static String APP_CONFIG_REFERRAL_BANNER = "referral_banner";
    private static String APP_CONFIG_REFERRAL_PRIZE_BANNER = "referral_prize_banner";
    private static String APP_CONFIG_BUSINESS_TYPE = "business_type";
    private static String APP_CONFIG_REFERRAL_STEPS = "referral_steps";
    private static String APP_CONFIG_REFERRAL_PRIZES = "referral_prizes";
    private static String APP_CONFIG_REFERRAL_TNCS = "referral_tncs";
    private static String APP_CONFIG_REFERRAL_ACTIVE = "referral_active";
    private static String APP_CONFIG_REFERRAL_SHARING_ACTIVE = "referral_sharing_active";
    private static String APP_CONFIG_COLLECTION_CALENDAR_ACTIVE = "collection_active";
    private static String APP_CONFIG_REFERRAL_MESSAGE = "referral_message";
    private static String APP_CONFIG_REFERRAL_LEADERBOARD_MESSAGE = "referral_leaderboard_message";
    private static String APP_CONFIG_REFERRAL_EARNED_PROFILE_POINTS = "referral_earned_profile";
    private static String APP_CONFIG_REFERRAL_EARNED_CARD_POINTS = "referral_earned_card";
    private static String APP_CONFIG_WA_BOT_MSG = "wa_bot_msg";
    private static String APP_CONFIG_SMS_TEMPLATE_CREDIT = "sms_template_credit";
    private static String APP_CONFIG_SMS_TEMPLATE_DEBIT = "sms_template_debit";
    private static String APP_CONFIG_DEFAULT_CASH = "default_cash_option";
    private static String APP_CONFIG_DEFAULT_TAB_LOGIN = "default_tab_login";
    private static String APP_CONFIG_DEFAULT_TAB_NAME = "default_tab_name";
    private static String APP_CONFIG_HAS_PAYMENT_PIN = "has_payment_pin";
    private static String APP_CONFIG_PAYMENT_DEVICE_ID = "payment_device_id";
    private static String APP_CONFIG_HAS_PENDING_PAYMENT = "has_pending_payment";
    private static String APP_CONFIG_BUKUPAY_ACTIVE = "config_bukupay_active";
    private static String APP_CONFIG_WELCOME_TEXT = "welcome_text";
    private static String APP_CONFIG_PAYMENT_INTERVAL_IN_MIN = "payment_interval";
    private static String APP_CONFIG_PAYMENT_IN_MSG = "payment_in_msg";
    private static String APP_CONFIG_PAYMENT_OUT_MSG = "payment_out_msg";
    private static String APP_NOTA_BARU = "nota_baru";
    private static String APP_BANK_LAYOUT = "bank_layout";
    private static String APP_STOCK_BARU = "stock_baru";
    private static String APP_CONFIG_BUSINESS_CARD_URI = "business_card_uri";
    private static String APP_CONFIG_BUSINESS_DASHBOARD_URI = "business_dashboard_uri";
    private static String APP_CONFIG_TRX_TYPE = "transaction_type";
    private static String KYC_CRITERIA_TRX_COUNT = "kyc_criteria_trx_count";
    private static String KYC_CRITERIA_TYPE = "kyc_criteria_type";
    private static String KYC_CRITERIA_AMOUNT = "kyc_criteria_amount";
    private static String APP_CONFIG_PRODUCTS_COUNT = "products_count";
    private static String APP_CONFIG_POS_TRANSAKSI_INTRO_TOOLTIP_SHOWN = "pos_transaksi_intro_tooltip_shown";
    private static String APP_CONFIG_POS_STOK_INTRO_TOOLTIP_SHOWN = "pos_stok_intro_tooltip_shown";
    private static String APP_CONFIG_POS_STORE_FRONT_PRODUCT_COACHMARK_SHOWN = "pos_store_front_product_coachmark_shown";
    private static String APP_CONFIG_POS_STORE_FRONT_EDIT_COACHMARK_SHOWN = "pos_store_front_edit_coachmark_shown";
    private static String APP_CONFIG_POS_STORE_FRONT_MOVE_TO_CART_COACHMARK_SHOWN = "pos_store_front_move_to_cart_coachmark_shown";
    private static String APP_CONFIG_TRANSAKSI_ONBOARDING_COACHMARK_SHOWN = "transaksi_onboarding_coachmark_shown";
    private static String APP_CONFIG_AUTORECORD_TXN_ONBOARDING_COACHMARK_SHOWN = "autorecord_txn_onboarding_coachmark_shown";
    private static String APP_CONFIG_POS_NON_CASH_PAYMENT_METHOD_NEW_BADGE_SHOWN =
            "pos_non_cash_payment_method_new_badge_shown";
    private static String SHOW_LENDING_BANNER = "show_lending_banner";

    Editor editor;
    public SharedPreferences pref;
    private Context context;

    public AppConfigManager(Context context2) {
        this.context = context2;
        this.pref = context2.getSharedPreferences(PrefrenceConstant.APP_CONFIG_PREF, PrefrenceConstant.PRIVATE_MODE);
        this.editor = this.pref.edit();
    }

    public static AppConfigManager getInstance() {
        if (prefManager == null) {
            prefManager = new AppConfigManager(Application.getAppContext());
        }
        return prefManager;
    }

    public String getReportApi() {
        return this.pref.getString("reportApi", "http://52.220.108.242:9091/");
    }

    public void setReportApi(String apiUrl) {
        this.editor.putString("reportApi", apiUrl);
        this.editor.apply();
    }

    public String getReferralLeaderboardUrl() {
        String url =  this.pref.getString("leaderboard_url", "https://bukuwarung-referral-stag-ddc11.web.app/leaderboard");
        if(Utility.isBlank(url)){
            return "https://bukuwarung-referral-stag-ddc11.web.app/leaderboard";
        }
        return url;
    }

    public void setReferralLeaderboardUrl(String apiUrl) {
        this.editor.putString("leaderboard_url", apiUrl);
        this.editor.apply();
    }


    public String getExcelReportApi() {
        return this.pref.getString("excelReportApi", "https://pwa-api.bukuwarung.com/");
    }

    public void setExcelReportApi(String apiUrl) {
        this.editor.putString("excelReportApi", apiUrl);
        this.editor.apply();
    }

    public String getEnableCstPdf() {
        return this.pref.getString("enableCstPdf", "0");
    }

    public void setEnableCstPdf(String flg) {
        this.editor.putString("enableCstPdf", flg);
        this.editor.apply();
    }

    public String getNotificationIcon() {
        return this.pref.getString("notificationIcon", "");
    }

    public void setNotificationIcon(String flg) {
        this.editor.putString("notificationIcon", flg);
        this.editor.apply();
    }

    public int getEnableVideo() {
        return this.pref.getInt("enableVideo", 1);
    }

    public void setEnableVideo(int flg) {
        this.editor.putInt("enableVideo", flg);
        this.editor.apply();
    }

    public int getinformasiOptionalCount() {
        return this.pref.getInt("informasiOptionalCount", 3);
    }

    public void setinformasiOptionalCount(int count) {
        this.editor.putInt("informasiOptionalCount", count);
        this.editor.apply();
    }

    public int getManualReminderPaymentLinkCount() {
        return this.pref.getInt("manualReminderPaymentLinkCount", 1);
    }

    public void setManualReminderPaymentLinkCount(int count) {
        this.editor.putInt("manualReminderPaymentLinkCount", count);
        this.editor.apply();
    }

    public String getStoreVersion() {
        return this.pref.getString("storeVersion", "");
    }

    public void setStoreVersion(String i) {
        this.editor.putString("storeVersion", i);
        this.editor.apply();
    }

    public String getUpdateInfo() {
        return this.pref.getString("updateInfo", "Please update app to use latest features.");
    }

    public void setUpdateInfo(String i) {
        this.editor.putString("updateInfo", i);
        this.editor.apply();
    }

    public int getUpdateFreq() {
        return this.pref.getInt("updateFreq", 2);
    }

    public void setUpdateFreq(int i) {
        this.editor.putInt("updateFreq", i);
        this.editor.apply();
    }

    public String getReportIdFlag() {
        return this.pref.getString("webReport", "0");
    }

    public void setReportIdFlag(String i) {
        this.editor.putString("webReport", i);
        this.editor.apply();
    }

    public String getSyncApi() {
        return this.pref.getString("syncApi", "http://52.220.108.242:9091/");
    }

    public void setSyncApi(String apiUrl) {
        this.editor.putString("syncApi", apiUrl);
        this.editor.apply();
    }

    public String getFileUploadApi() {
        return this.pref.getString("fileUploadApi", "http://54.254.168.212:9082");
    }

    public void setFileUploadApi(String apiUrl) {
        this.editor.putString("fileUploadApi", apiUrl);
        this.editor.apply();
    }

    public String getFileBucket() {
        return this.pref.getString("fileBucket", "https://bukuwarungac-image-dev.s3.ap-southeast-1.amazonaws.com");
    }

    public void setFileBucket(String apiUrl) {
        this.editor.putString("fileBucket", apiUrl);
        this.editor.apply();
    }

    public int getEnableLiveSync() {
        return this.pref.getInt("liveSync", 0);
    }

    public void setEnableLiveSync(int flg) {
        this.editor.putInt("liveSync", flg);
        this.editor.apply();
    }

    //0: guest feature disabled
    //1: guest feature enabled with prop image slides
    //2: guest feature enabled but no prop image slides
    public int getEnableGuestFeature() {
        return this.pref.getInt("enable_guest_feature", 0);
    }

    public void setEnableGuestFeature(int flg) {
        this.editor.putInt("enable_guest_feature", flg);
        this.editor.apply();
    }

    public int getGuestTxnLimit() {
        return this.pref.getInt("guest_txn_limit", 10);
    }

    public void setGuestTxnLimit(int count) {
        this.editor.putInt("guest_txn_limit", count);
        this.editor.apply();
    }

    public int getPullRefresh() {
        return this.pref.getInt("pull_refresh", 0);
    }

    public void setPullRefresh(int flg) {
        this.editor.putInt("pull_refresh", flg);
        this.editor.apply();
    }

    public int getWhatsappAuth() {
        return this.pref.getInt("whatsapp_auth", 2);
    }

    public void setWhatsappAuth(int flg) {
        this.editor.putInt("whatsapp_auth", flg);
        this.editor.apply();
    }

    public boolean isAppUnderMaintenance() {
        return this.pref.getBoolean("app_under_maintenance", false);
    }

    public void isAppUnderMaintenance(boolean status) {
        if(status!=isAppUnderMaintenance()){
            AppAnalytics.setUserProperty("app_under_maintenance",status);
            AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
            prop.put("in_maintenance", status);
            AppAnalytics.trackEvent("app_maintenance_change", prop);
        }
        this.editor.putBoolean("app_under_maintenance", status);
        this.editor.apply();
    }

    public String getSmsApi() {
        return this.pref.getString("smsApi", "");
    }

    public void setSmsApi(String apiUrl) {
        this.editor.putString("smsApi", apiUrl);
        this.editor.apply();
    }

    public String getTxnApi() {
        return this.pref.getString("txnApi", "");
    }

    public void setTxnApi(String apiUrl) {
        this.editor.putString("txnApi", apiUrl);
        this.editor.apply();
    }

    public String getLunasApi() {
        return this.pref.getString("lunaskan_sms_api", "");
    }

    public void setLunasApi(String apiUrl) {
        this.editor.putString("lunaskan_sms_api", apiUrl);
        this.editor.apply();
    }

    public String getMoApi() {
        return this.pref.getString("moapi", "GRAFCYG32R1FS54ME39HGO8S");
    }

    public void setMoApi(String apiUrl) {
        this.editor.putString("moapi", apiUrl);
        this.editor.apply();
    }

    //
    public String getAmpApi() {
        return this.pref.getString("ampApi", "079ae8d7c27fdb49cbeeae8349501bf9");
    }

    public void setAmpApi(String apiUrl) {
        this.editor.putString("ampApi", apiUrl);
        this.editor.apply();
    }

    //
    public String getAfApi() {
        return this.pref.getString("afApi", "w5qqbe8GwcDGReGgqpRsRW");
    }

    public void setAfApi(String apiUrl) {
        this.editor.putString("afApi", apiUrl);
        this.editor.apply();
    }

    public String getOTPApi() {
        return this.pref.getString("auth_url", BuildConfig.API_BASE_URL_TWO_FA);
    }

    public void setOTPApi(String apiUrl) {
        this.editor.putString("auth_url", apiUrl);
        this.editor.apply();
    }

    public String getStreaksApi() {
        return this.pref.getString("streaks_url", BuildConfig.API_BASE_URL_STREAKS);
    }

    public void setStreaksApi(String apiUrl) {
        this.editor.putString("streaks_url", apiUrl);
        this.editor.apply();
    }

    public boolean useNewLogin() {
        return this.pref.getBoolean("login_new", true);
    }

    public boolean isBureauEnabled() {
        return this.pref.getBoolean("bureau_enabled", true);
    }

    public void isBureauEnabled(boolean flag){
        this.editor.putBoolean("bureau_enabled", flag);
        this.editor.apply();

    }

    public int getTryCount() {
        return this.pref.getInt("retry_count", 1);
    }

    public void setTryCount(int count) {
        this.editor.putInt("retry_count", count);
        this.editor.apply();
    }

    public int getInvalidOtpCount() {
        return this.pref.getInt("otp_count", 1);
    }

    public void setInvalidOtpCount(int count) {
        this.editor.putInt("otp_count", count);
        this.editor.apply();
    }

    public void setUseNewLogin(boolean flg) {
        this.editor.putBoolean("login_new", flg);
        this.editor.apply();
    }

    public boolean useWebView() {
        return this.pref.getBoolean("use_web_view", true);
    }

    public void useWebView(boolean flg) {
        this.editor.putBoolean("use_web_view", flg);
        this.editor.apply();
    }

    public boolean useServerCategory() {
        return this.pref.getBoolean("use_server_category", true);
    }

    public void setUseServerCategory(boolean flg) {
        this.editor.putBoolean("use_server_category", flg);
        this.editor.apply();
    }

    public String getEnableReportPdf() {
        return this.pref.getString("enableReportPdf", "1");
    }

    public void setEnableReportPdf(String flg) {
        this.editor.putString("enableReportPdf", flg);
        this.editor.apply();
    }

    public String getWhatsappId() {
        return this.pref.getString("whatsapp", "6283804550504");
    }

    public void setWhatsappId(String str) {
        this.editor.putString("whatsapp", str);
        this.editor.apply();
    }

    public int getOtpWaitTimeInSec() {
        return this.pref.getInt(APP_CONFIG_OTP_WAIT_TIME, 25);
    }

    public void setOtpWaitTimeInSec(int otpWaitTime) {
        this.editor.putInt(APP_CONFIG_OTP_WAIT_TIME, otpWaitTime);
        this.editor.apply();
    }

    public boolean hasBusinessTypes() {
        return this.pref.getString(APP_CONFIG_BUSINESS_TYPE, null) != null;
    }

    public List<BusinessType> getBusinessTypes() {
        try {
            final String json = this.pref.getString(APP_CONFIG_BUSINESS_TYPE, null);
            Gson gson = new Gson();

            if (json == null) {
                return AppConst.defaultBusinessType(context);
            }
            Type type = new TypeToken<List<BusinessType>>() {
            }.getType();
            List<BusinessType> businessTypes = gson.fromJson(json, type);
            return businessTypes;
        } catch (Exception ex) {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("error", ex == null ? "-" : ex.toString());
            AppAnalytics.trackEvent("fetch_business_type_error", propBuilder);

            // delete old local data if any, so it can fetch again on app start
            this.editor.remove(APP_CONFIG_BUSINESS_TYPE);


            return AppConst.defaultBusinessType(context);
        }
    }

    public void setBusinessTypes(List<BusinessType> businessTypes) {
        try {
            Gson gson = new Gson();
            final String json = gson.toJson(businessTypes);
            this.editor.putString(APP_CONFIG_BUSINESS_TYPE, json);
            this.editor.apply();
        } catch (Exception ex) {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("error", ex == null ? "-" : ex.toString());
            AppAnalytics.trackEvent("set_business_type_error", propBuilder);
            ex.printStackTrace();
        }
    }

    public void addNewBusinessType(BusinessType businessType) {
        try {
            String json = this.pref.getString(APP_CONFIG_BUSINESS_TYPE, null);
            Gson gson = new Gson();

            if (json == null) {
                return;
            }
            Type type = new TypeToken<List<BusinessType>>() {
            }.getType();
            List<BusinessType> businessTypes = gson.fromJson(json, type);
            businessTypes.add(businessType);

            json = gson.toJson(businessTypes);
            this.editor.putString(APP_CONFIG_BUSINESS_TYPE, json);
            this.editor.apply();
        } catch (Exception ex) {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("error", ex == null ? "-" : ex.toString());
            AppAnalytics.trackEvent("add_business_type_error", propBuilder);
            ex.printStackTrace();
        }
    }

    public void editBusinessType(BusinessType businessType, String newName) {
        try {
            String json = this.pref.getString(APP_CONFIG_BUSINESS_TYPE, null);
            Gson gson = new Gson();

            if (json == null) {
                return;
            }
            Type type = new TypeToken<List<BusinessType>>() {
            }.getType();
            List<BusinessType> businessTypes = gson.fromJson(json, type);

            final int index = businessTypes.indexOf(businessType);
            businessTypes.get(index).setName(newName);

            json = gson.toJson(businessTypes);
            this.editor.putString(APP_CONFIG_BUSINESS_TYPE, json);
            this.editor.apply();
        } catch (Exception ex) {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("error", ex == null ? "-" : ex.toString());
            AppAnalytics.trackEvent("edit_business_type_error", propBuilder);
            ex.printStackTrace();
        }
    }

    public void deleteBusinessType(BusinessType businessType) {
        try {
            String json = this.pref.getString(APP_CONFIG_BUSINESS_TYPE, null);
            Gson gson = new Gson();

            if (json == null) {
                return;
            }
            Type type = new TypeToken<List<BusinessType>>() {
            }.getType();
            List<BusinessType> businessTypes = gson.fromJson(json, type);
            businessTypes.remove(businessType);

            json = gson.toJson(businessTypes);
            this.editor.putString(APP_CONFIG_BUSINESS_TYPE, json);
            this.editor.apply();
        } catch (Exception ex) {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("error", ex == null ? "-" : ex.toString());
            AppAnalytics.trackEvent("delete_business_type_error", propBuilder);
            ex.printStackTrace();
        }
    }

    public String getReferralTitle() {
        return this.pref.getString(APP_CONFIG_REFERRAL_TITLE,
                "BukuWarung Kejar Untung");
    }

    public void setReferralTitle(String referralTitle) {
        this.editor.putString(APP_CONFIG_REFERRAL_TITLE, referralTitle);
        this.editor.apply();
    }

    public String getReferralBanner() {
        return this.pref.getString(APP_CONFIG_REFERRAL_BANNER,
                "");
    }

    public void setReferralBanner(String referralBanner) {
        this.editor.putString(APP_CONFIG_REFERRAL_BANNER, referralBanner);
        this.editor.apply();
    }

    public boolean allowContactUpload() {
        return this.pref.getBoolean("allow_uploaded_contact", false);
    }

    public void allowContactUpload(boolean flg) {
        this.editor.putBoolean("allow_uploaded_contact", flg);
        this.editor.apply();
    }

    public String getReferralPrizeBanner() {
        return this.pref.getString(APP_CONFIG_REFERRAL_PRIZE_BANNER, "");
    }

    public void setReferralPrizeBanner(String referralPrizeBanner) {
        this.editor.putString(APP_CONFIG_REFERRAL_PRIZE_BANNER, referralPrizeBanner);
        this.editor.apply();
    }

    public List<String> getReferralSteps() {
        try {
            final String json = this.pref.getString(APP_CONFIG_REFERRAL_STEPS, null);
            Gson gson = new Gson();

            if (json == null) {
                return null;
            }
            Type type = new TypeToken<List<String>>() {
            }.getType();
            return gson.fromJson(json, type);
        } catch (Exception ex) {
            return null;
        }
    }

    public void setReferralSteps(List<String> referralSteps) {
        try {
            if (referralSteps == null) return;

            Gson gson = new Gson();
            final String json = gson.toJson(referralSteps);
            this.editor.putString(APP_CONFIG_REFERRAL_STEPS, json);
            this.editor.apply();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<String> getReferralPrizes() {
        try {
            final String json = this.pref.getString(APP_CONFIG_REFERRAL_PRIZES, null);
            Gson gson = new Gson();

            if (json == null) {
                return null;
            }
            Type type = new TypeToken<List<String>>() {
            }.getType();
            return gson.fromJson(json, type);
        } catch (Exception ex) {
            return null;
        }
    }

    public void setReferralPrizes(List<String> referralPrizes) {
        try {
            if (referralPrizes == null) return;

            Gson gson = new Gson();
            final String json = gson.toJson(referralPrizes);
            this.editor.putString(APP_CONFIG_REFERRAL_PRIZES, json);
            this.editor.apply();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<String> getReferralTncs() {
        try {
            final String json = this.pref.getString(APP_CONFIG_REFERRAL_TNCS, null);
            Gson gson = new Gson();

            if (json == null) {
                return null;
            }
            Type type = new TypeToken<List<String>>() {
            }.getType();
            return gson.fromJson(json, type);
        } catch (Exception ex) {
            return null;
        }
    }

    public void setReferralTncs(List<String> referralTncs) {
        try {
            if (referralTncs == null) return;

            Gson gson = new Gson();
            final String json = gson.toJson(referralTncs);
            this.editor.putString(APP_CONFIG_REFERRAL_TNCS, json);
            this.editor.apply();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean useReferral() {
        return this.pref.getBoolean(APP_CONFIG_REFERRAL_ACTIVE, false);
    }

    public void setUseReferral(boolean flag) {
        this.editor.putBoolean(APP_CONFIG_REFERRAL_ACTIVE, flag);
        this.editor.apply();
    }

    public boolean useReferralSharing() {
        return this.pref.getBoolean(APP_CONFIG_REFERRAL_SHARING_ACTIVE, true);
    }

    public void setUseReferralSharing(boolean flag) {
        this.editor.putBoolean(APP_CONFIG_REFERRAL_SHARING_ACTIVE, flag);
        this.editor.apply();
    }

    public int getDefaultTabLogin() {
        if (SessionManager.getInstance().isLoggedIn() && getDefaultTabTarget() != 10 && !SessionManager.getInstance().isGuestUser()) {
            try {
                //get user id last digit to check if target for transaksi tab setting
                int lastDigit = Integer.parseInt(User.getUserId().charAt(User.getUserId().length() - 1) + "");
                //if target rollout is 50% all user ids with last digit < 5 will see transaksi tab as default tab
                //assumption: phone number last digit distribution is equal
                return lastDigit < getDefaultTabTarget() ? this.pref.getInt(APP_CONFIG_DEFAULT_TAB_LOGIN, 0) : 0;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return this.pref.getInt(APP_CONFIG_DEFAULT_TAB_LOGIN, 0);
    }

    public void setDefaultTabLogin(int defaultTabLogin) {
        this.editor.putInt(APP_CONFIG_DEFAULT_TAB_LOGIN, defaultTabLogin);
        this.editor.apply();
    }

    public String getDefaultTabName() {
        if (SessionManager.getInstance().isLoggedIn() && getDefaultTabTarget() != 10 && !SessionManager.getInstance().isGuestUser()) {
            try {
                //get user id last digit to check if target for transaksi tab setting
                int lastDigit = Integer.parseInt(User.getUserId().charAt(User.getUserId().length() - 1) + "");
                //if target rollout is 50% all user ids with last digit < 5 will see transaksi tab as default tab
                //assumption: phone number last digit distribution is equal
                return lastDigit < getDefaultTabTarget() ? pref.getString(APP_CONFIG_DEFAULT_TAB_NAME, TabName.CUSTOMER.name()) : TabName.CUSTOMER.name();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return pref.getString(APP_CONFIG_DEFAULT_TAB_NAME, TabName.CUSTOMER.name());
    }

    public void setDefaultTabName(String defaultTabName) {
        this.editor.putString(APP_CONFIG_DEFAULT_TAB_NAME, defaultTabName);
        this.editor.apply();
    }

    public int getDefaultTabTarget() {
        return this.pref.getInt("DEFAULT_TAB_PERCENTAGE", 10);
    }

    public void setDefaultTabTarget(int defaultTabLogin) {
        this.editor.putInt("DEFAULT_TAB_PERCENTAGE", defaultTabLogin);
        this.editor.apply();
    }

    public int getProfileSetupTarget() {
        return this.pref.getInt("PROFILE_SETUP_TARGET", 10);
    }

    public void setProfileSetupTarget(int defaultTabLogin) {
        this.editor.putInt("PROFILE_SETUP_TARGET", defaultTabLogin);
        this.editor.apply();
    }


    public boolean useCollectionCalendar() {
        return this.pref.getBoolean(APP_CONFIG_COLLECTION_CALENDAR_ACTIVE, false);
    }

    public void setUseCollectionCalendar(boolean flag) {
        this.editor.putBoolean(APP_CONFIG_COLLECTION_CALENDAR_ACTIVE, flag);
        this.editor.apply();
    }

    public boolean eligibleForProfileSetup() {
        return this.pref.getBoolean("requiresProfileSetup", false);
    }

    public void setEligibleForProfileSetup(boolean flag) {
        this.editor.putBoolean("requiresProfileSetup", flag);
        this.editor.apply();
    }

    public boolean isFirstSessionAfterInstall() {
        return this.pref.getBoolean("first_session_after_install", false);
    }

    public void setFirstSessionAfterInstall(boolean flag) {
        this.editor.putBoolean("first_session_after_install", flag);
        this.editor.apply();
    }

    public String getReferralMessage() {
        return this.pref.getString(APP_CONFIG_REFERRAL_MESSAGE,
                context.getResources().getString(R.string.referral_share_message));
    }

    public String getPaymentReferralMessage() {
        return this.pref.getString("payment_referral_msg",
                context.getResources().getString(R.string.payment_ref_message));
    }

    public void setPaymentReferralMessage(String referralMessage) {
        this.editor.putString("payment_referral_msg", referralMessage);
        this.editor.apply();
    }

    public void setReferralMessage(String referralMessage) {
        this.editor.putString(APP_CONFIG_REFERRAL_MESSAGE, referralMessage);
        this.editor.apply();
    }

    public String getReferralLeaderboardMessage() {
        return this.pref.getString(APP_CONFIG_REFERRAL_LEADERBOARD_MESSAGE,
                context.getResources().getString(R.string.referral_leaderboard_share_message));
    }

    public void setReferralLeaderboardMessage(String referralMessage) {
        this.editor.putString(APP_CONFIG_REFERRAL_LEADERBOARD_MESSAGE, referralMessage);
        this.editor.apply();
    }

    public boolean isSyncEnabled() {
        return this.pref.getBoolean("is_sync_enabled", true);
    }

    public void isSyncEnabled(boolean flag) {
        this.editor.putBoolean("is_sync_enabled", flag);
        this.editor.apply();
    }



    public boolean referralHasEarnedProfile() {
        return this.pref.getBoolean(APP_CONFIG_REFERRAL_EARNED_PROFILE_POINTS, false);
    }

    public void setReferralHasEarnedProfile(boolean flag) {
        this.editor.putBoolean(APP_CONFIG_REFERRAL_EARNED_PROFILE_POINTS, flag);
        this.editor.apply();
    }

    public String getWaBotMsg() {
        return this.pref.getString(APP_CONFIG_WA_BOT_MSG,
                context.getResources().getString(R.string.wa_help_text_general));
    }


    public void setWaBotMsg(String waBotMsg) {
        this.editor.putString(APP_CONFIG_WA_BOT_MSG, waBotMsg);
        this.editor.apply();
    }

    public boolean referralHasEarnedCard() {
        return this.pref.getBoolean(APP_CONFIG_REFERRAL_EARNED_CARD_POINTS, false);
    }

    public void setReferralHasEarnedCard(boolean flag) {
        this.editor.putBoolean(APP_CONFIG_REFERRAL_EARNED_CARD_POINTS, flag);
        this.editor.apply();
    }

    public boolean enableNewAmplitudeProject() {
        return this.pref.getBoolean("enabled_new_amplitude_project", true);
    }

    public void setEnableNewAmplitudeProject(boolean flag) {
        this.editor.putBoolean("enabled_new_amplitude_project", flag);
        this.editor.apply();
    }

    public boolean isUsingNewAmpProject() {
        return this.pref.getBoolean("using_new_amp_project", false);
    }

    public void setUsingNewAmpProject(boolean flag) {
        this.editor.putBoolean("using_new_amp_project", flag);
        this.editor.apply();
    }

    public void setSmsTemplateCredit(String template) {
        this.editor.putString(APP_CONFIG_SMS_TEMPLATE_CREDIT, template);
        this.editor.apply();
    }

    public String getSmsTemplateCredit() {
        return this.pref.getString(APP_CONFIG_SMS_TEMPLATE_CREDIT, Application.getAppContext().getString(R.string.credit_sms_template_new));
    }

    public void setSmsTemplateDebit(String template) {
        this.editor.putString(APP_CONFIG_SMS_TEMPLATE_DEBIT, template);
        this.editor.apply();
    }

    public String getSmsTemplateDebit() {
        return this.pref.getString(APP_CONFIG_SMS_TEMPLATE_DEBIT, Application.getAppContext().getString(R.string.debit_sms_template_new));
    }

    public void setAppConfigDefaultCash(int defaultCash) {
        this.editor.putInt(APP_CONFIG_DEFAULT_CASH, defaultCash);
        this.editor.apply();
    }

    public int getDefaultCash() {
        return this.pref.getInt(APP_CONFIG_DEFAULT_CASH, -1);
    }

    public int getReferralTxnVal() {
        return this.pref.getInt("point_txn_min_value", 10000);
    }

    public void setReferralTxnVal(int referralTransactionVal) {
        this.editor.putInt("point_txn_min_value", referralTransactionVal);
        this.editor.apply();
    }

    public int getLeaderBoardSize() {
        return this.pref.getInt("leaderboard_size", 50);
    }

    public void setLeaderBoardSize(int leaderboardSize) {
        this.editor.putInt("leaderboard_size", leaderboardSize);
        this.editor.apply();
    }

    public boolean hasPaymentPin() {
        return this.pref.getBoolean(APP_CONFIG_HAS_PAYMENT_PIN, false);
    }

    public void setHasPaymentPin(boolean flag) {
        this.editor.putBoolean(APP_CONFIG_HAS_PAYMENT_PIN, flag);
        this.editor.apply();
    }

    public String getPaymentDeviceId() {
        return this.pref.getString(APP_CONFIG_PAYMENT_DEVICE_ID, null);
    }

    public void setPaymentDeviceId(String deviceId) {
        this.editor.putString(APP_CONFIG_PAYMENT_DEVICE_ID, deviceId);
        this.editor.apply();
    }

    public boolean hasPendingPayment() {
        return this.pref.getBoolean(APP_CONFIG_HAS_PENDING_PAYMENT, false);
    }

    public void sethasPendingPayment(boolean value) {
        this.editor.putBoolean(APP_CONFIG_HAS_PENDING_PAYMENT, value);
        this.editor.apply();
    }

    public boolean bukuPayLogoActive() {
        return this.pref.getBoolean(APP_CONFIG_BUKUPAY_ACTIVE, false);
    }

    public void setBukuPayLogoActive(boolean value) {
        this.editor.putBoolean(APP_CONFIG_BUKUPAY_ACTIVE, value);
        this.editor.apply();
    }

    public String getWelcomeText() {
        return this.pref.getString(APP_CONFIG_WELCOME_TEXT, null);
    }

    public void setWelcomeText(String value) {
        this.editor.putString(APP_CONFIG_WELCOME_TEXT, value);
        this.editor.apply();
    }

    public int getPaymentIntervalInMin() {
        return this.pref.getInt(APP_CONFIG_PAYMENT_INTERVAL_IN_MIN, 15);
    }

    public void setPaymentIntervalInMin(int value) {
        this.editor.putInt(APP_CONFIG_PAYMENT_INTERVAL_IN_MIN, value);
        this.editor.apply();
    }

    public String getPaymentInMsg() {
        return this.pref.getString(APP_CONFIG_PAYMENT_IN_MSG, "Pembayaran Tagihan menggunakan Pembayaran Digital BukuWarung");
    }

    public void setPaymentInMsg(String value) {
        this.editor.putString(APP_CONFIG_PAYMENT_IN_MSG, value);
        this.editor.apply();
    }

    public String getPaymentOutMsg() {
        return this.pref.getString(APP_CONFIG_PAYMENT_OUT_MSG, "Pembayaran ke Pelanggan menggunakan Pembayaran Digital BukuWarung");
    }

    public void setPaymentOutMsg(String value) {
        this.editor.putString(APP_CONFIG_PAYMENT_OUT_MSG, value);
        this.editor.apply();
    }

    public String getUtangUserMsg() {
        return this.pref.getString("utang_user_message", "“Semenjak saya catat dan ingetin utang pake fitur SMS otomatis di BukuWarung, pelanggan jadi bayar tepat waktu.”");
    }

    public void setUtangUserMsg(String value) {
        this.editor.putString("utang_user_message", value);
        this.editor.apply();
    }

    public String getUtangUserImage() {
        return this.pref.getString("utang_user_image", null);
    }

    public void setUtangUserImage(String value) {
        this.editor.putString("utang_user_image", value);
        this.editor.apply();
    }

    public String getUtangUserName() {
        return this.pref.getString("utang_user_name", "Bu Ance, pemilik warung");
    }

    public void setUtangUserName(String value) {
        this.editor.putString("utang_user_name", value);
        this.editor.apply();
    }

    public String getUtangReviewDate() {
        return this.pref.getString("utang_review_date", "Pengguna aktif sejak Januari 2020");
    }

    public void setUtangReviewDate(String value) {
        this.editor.putString("utang_review_date", value);
        this.editor.apply();
    }

    public String getTransaksiUserMsg() {
        return this.pref.getString("transaksi_user_message", "“Sejak pake BukuWarung, catatan keuangan usaha saya makin rapi, jadi makin untung”");
    }

    public void setTransaksiUserMsg(String value) {
        this.editor.putString("transaksi_user_message", value);
        this.editor.apply();
    }

    public String getTransaksiUserName() {
        return this.pref.getString("transaksi_user_name", "Pak Yudhistira, pemilik toko grosir");
    }

    public void setTransaksiUserName(String value) {
        this.editor.putString("transaksi_user_name", value);
        this.editor.apply();
    }

    public String getTransaksiReviewDate() {
        return this.pref.getString("transaksi_review_date", "Pengguna aktif sejak Februari 2020");
    }

    public void setTransaksiReviewDate(String value) {
        this.editor.putString("transaksi_review_date", value);
        this.editor.apply();
    }

    public String getTransaksiUserImage() {
        return this.pref.getString("txn_user_image", null);
    }

    public void setTransaksiUserImage(String value) {
        this.editor.putString("txn_user_image", value);
        this.editor.apply();
    }

    public String getLatestVersion() {
        return this.pref.getString("latest_app_version", "0");
    }

    public void setLatestVersion(String latestVersion) {
        this.editor.putString("latest_app_version", latestVersion);
        this.editor.apply();
    }

    public int getEnableFlexibleUpdate() {
        return this.pref.getInt("use_flexible_update", 1);
    }

    public void setEnableFlexibleUpdate(int useFlexibleUpdate) {
        this.editor.putInt("use_flexible_update", useFlexibleUpdate);
        this.editor.apply();
    }

    public Boolean getEnablePpobBanner() {
        return this.pref.getBoolean("ppob_banner", true);
    }

    public void setPaymentFreeChargeStatus(boolean status) {
        this.editor.putBoolean("payment_free_charge_status", status);
        this.editor.apply();
    }

    public boolean getPaymentFreeChargeStatus() {
        return this.pref.getBoolean("payment_free_charge_status", false);
    }

    public void showCustomPermissionDialog(boolean status) {
        this.editor.putBoolean("show_custom_permission_dialog", status);
        this.editor.apply();
    }

    public boolean showCustomPermissionDialog() {
        return this.pref.getBoolean("show_custom_permission_dialog", true);
    }

    public boolean usePayloadFromApp() {
        return this.pref.getBoolean("use_payload_from_app", true);
    }

    public void usePayloadFromApp(boolean status) {
        this.editor.putBoolean("use_payload_from_app", status);
        this.editor.apply();
    }

    public int getStockTransactionTarget() {
        return this.pref.getInt("stock_tab_trasaction_target", 2);
    }

    public void setStockTransactionTarget(int value) {
        this.editor.putInt("stock_tab_trasaction_target", value);
        this.editor.apply();
    }

    public void setAppNotaBaru() {
        this.editor.putBoolean(APP_NOTA_BARU, true);
        this.editor.apply();
    }

    public void setBankLayoutBaru() {
        this.editor.putBoolean(APP_BANK_LAYOUT, true);
    }

    public boolean isNotaBaru() {
        return this.pref.getBoolean(APP_NOTA_BARU, false);
    }

    public boolean isBankLayoutBaru() {
        return this.pref.getBoolean(APP_BANK_LAYOUT, false);
    }

    public boolean enableTokokoDownload() {
        return this.pref.getBoolean("tokoko_download_enabled",true);
    }

    public void enableTokokoDownload(boolean status) {
        this.editor.putBoolean("tokoko_download_enabled", status);
        this.editor.apply();
    }

    public String getTokokoDownloadLink() {
        return this.pref.getString("tokoko_download_link", "https://play.google.com/store/apps/details?id=com.tokoko.and");
    }

    public void setTokokoDownloadLink(String latestVersion) {
        this.editor.putString("tokoko_download_link", latestVersion);
        this.editor.apply();
    }

    public long getStreakDuration() {
        return this.pref.getLong("streak_duration", ********);
    }

    public void setStreakDuration(long interval) {
        this.editor.putLong("streak_duration", ********);
        this.editor.apply();
    }

    public String getBusinessCardUri() {
        return this.pref.getString(APP_CONFIG_BUSINESS_CARD_URI, "");
    }

    public void setBusinessCardUri(String uri) {
        this.editor.putString(APP_CONFIG_BUSINESS_CARD_URI, uri);
        this.editor.apply();
    }

    public String getBusinessDashboardUri() {
        return this.pref.getString(APP_CONFIG_BUSINESS_DASHBOARD_URI, "");
    }

    public void setBusinessDashboardUri(String uri) {
        this.editor.putString(APP_CONFIG_BUSINESS_DASHBOARD_URI, uri);
        this.editor.apply();
    }
  
    public void setTransactionType(boolean trxType) {
        this.editor.putBoolean(APP_CONFIG_TRX_TYPE, trxType);
        this.editor.apply();
    }

    public boolean getTransactionType() {
        return this.pref.getBoolean(APP_CONFIG_TRX_TYPE, true);
    }

    public void setKycCriteriaType(String trxType) {
        this.editor.putString(KYC_CRITERIA_TYPE, trxType);
        this.editor.apply();
    }

    public String getKycCriteriaType() {
        return this.pref.getString(KYC_CRITERIA_TYPE,"in&out");
    }

    public void setKycCriteriaTrxCount(int trxCount) {
        this.editor.putInt(KYC_CRITERIA_TRX_COUNT, trxCount);
        this.editor.apply();
    }

    public int getKycCriteriaTrxCount() {
        return this.pref.getInt(KYC_CRITERIA_TRX_COUNT,10);
    }

    public void setKycCriteriaAmount(int amount) {
        this.editor.putInt(KYC_CRITERIA_AMOUNT, amount);
        this.editor.apply();
    }

    public int getKycCriteriaAmount() {
        return this.pref.getInt(KYC_CRITERIA_AMOUNT, 1500000);
    }

    public Boolean getPosTransaksiIntroTooltipShown() {
        return this.pref.getBoolean(APP_CONFIG_POS_TRANSAKSI_INTRO_TOOLTIP_SHOWN, false);
    }

    public void setPosTransaksiIntroTooltipShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_POS_TRANSAKSI_INTRO_TOOLTIP_SHOWN, shown);
        this.editor.apply();
    }

    public Boolean getPosStokIntroTooltipShown() {
        return this.pref.getBoolean(APP_CONFIG_POS_STOK_INTRO_TOOLTIP_SHOWN, false);
    }

    public void setPosStokIntroTooltipShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_POS_STOK_INTRO_TOOLTIP_SHOWN, shown);
        this.editor.apply();
    }

    public Boolean getPosStoreFrontProductCoachmarkShown() {
        return this.pref.getBoolean(APP_CONFIG_POS_STORE_FRONT_PRODUCT_COACHMARK_SHOWN, false);
    }

    public void setPosStoreFrontProductCoachmarkShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_POS_STORE_FRONT_PRODUCT_COACHMARK_SHOWN, shown);
        this.editor.apply();
    }

    public Boolean getPosStoreFrontEditCoachmarkShown() {
        return this.pref.getBoolean(APP_CONFIG_POS_STORE_FRONT_EDIT_COACHMARK_SHOWN, false);
    }

    public void setPosStoreFrontEditCoachmarkShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_POS_STORE_FRONT_EDIT_COACHMARK_SHOWN, shown);
        this.editor.apply();
    }

    public Boolean getPosStoreFrontMoveToCartCoachmarkShown() {
        return this.pref.getBoolean(APP_CONFIG_POS_STORE_FRONT_MOVE_TO_CART_COACHMARK_SHOWN, false);
    }

    public void setPosStoreFrontMoveToCartCoachmarkShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_POS_STORE_FRONT_MOVE_TO_CART_COACHMARK_SHOWN, shown);
        this.editor.apply();
    }

    public Boolean getTransaksiOnboardingCoachmarkShown() {
        return this.pref.getBoolean(APP_CONFIG_TRANSAKSI_ONBOARDING_COACHMARK_SHOWN, false);
    }

    public void setTransaksiOnboardingCoachmarkShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_TRANSAKSI_ONBOARDING_COACHMARK_SHOWN, shown);
        this.editor.apply();
    }

    public Boolean getAutoRecordTxnOnboardingCoachmarkShown() {
        return this.pref.getBoolean(APP_CONFIG_AUTORECORD_TXN_ONBOARDING_COACHMARK_SHOWN, false);
    }

    public void setAutoRecordTxnOnboardingCoachmarkShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_AUTORECORD_TXN_ONBOARDING_COACHMARK_SHOWN, shown);
        this.editor.apply();
    }

    public Boolean getPosNonCashPaymentMethodNewBadgeShown() {
        return this.pref.getBoolean(APP_CONFIG_POS_NON_CASH_PAYMENT_METHOD_NEW_BADGE_SHOWN, false);
    }

    public void setPosNonCashPaymentMethodNewBadgeShown(Boolean shown) {
        this.editor.putBoolean(APP_CONFIG_POS_NON_CASH_PAYMENT_METHOD_NEW_BADGE_SHOWN, shown);
        this.editor.apply();
    }

    public void setEntryPointForId(String id, boolean flg) {
        if (id == null)
            return;
        editor.putBoolean(id, flg);
        editor.apply();
    }

    public Boolean getEntryPointForId(String id){
        return pref.getBoolean(id, false);
    }

    public void setShowLendingBanner(int showLendingBanner) {
        this.editor.putInt(SHOW_LENDING_BANNER, showLendingBanner);
        this.editor.apply();
    }

    public int showLendingBanner() {
        return this.pref.getInt(SHOW_LENDING_BANNER, -1);
    }

    public void setStockBaru() {
        this.editor.putBoolean(APP_STOCK_BARU, false);
        this.editor.apply();
    }

    public boolean isStockBaru() {
        return this.pref.getBoolean(APP_STOCK_BARU, true);
    }

}
