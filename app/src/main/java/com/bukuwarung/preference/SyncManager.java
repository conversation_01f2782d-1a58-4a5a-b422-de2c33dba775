package com.bukuwarung.preference;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import com.bukuwarung.Application;
import com.bukuwarung.constants.PrefrenceConstant;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.setup.SetupManager;

public class SyncManager {

    private static SyncManager syncManager;
    private Editor editor;
    private SharedPreferences configPref;

    private SyncManager(Context context) {
        this.configPref = context.getSharedPreferences("SYNC_PREF_MANAGER", PrefrenceConstant.PRIVATE_MODE);
        this.editor = this.configPref.edit();
    }

    public static SyncManager getInstance() {
        if (syncManager == null) {
            syncManager = new SyncManager(Application.getAppContext());
        }
        return syncManager;
    }

    public static SyncManager getInstance(Context ctx) {
        if (syncManager == null) {
            syncManager = new SyncManager(ctx);
        }
        return syncManager;
    }

    public boolean hasRestoredBooks() {
        return this.configPref.getBoolean("books_downloaded",false);
    }

    public void setRestoredBooks(boolean status) {
        this.editor.putBoolean("books_downloaded", status);
        this.editor.apply();
    }

    public boolean hasRestoredCustomer() {
        return this.configPref.getBoolean("customer_downloaded",false);
    }

    public void setRestoredcustomer(boolean status) {
        this.editor.putBoolean("customer_downloaded", status);
        this.editor.apply();
    }

    public boolean hasRestoredTransactions() {
        return this.configPref.getBoolean("transaction_downloaded",false);
    }

    public void setRestoredTransactions(boolean status) {
        this.editor.putBoolean("transaction_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredCustomer() {
        return this.configPref.getBoolean("manual_customer_downloaded",false);
    }

    public void setManualRestoredcustomer(boolean status) {
        this.editor.putBoolean("manual_customer_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredTransactions() {
        return this.configPref.getBoolean("manual_transaction_downloaded",false);
    }

    public void setManualRestoredTransactions(boolean status) {
        this.editor.putBoolean("manual_transaction_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredCashCategory() {
        return this.configPref.getBoolean("manual_cash_category_downloaded",false);
    }

    public void setManualRestoredCashCategory(boolean status) {
        this.editor.putBoolean("manual_cash_category_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredCashTransactions() {
        return this.configPref.getBoolean("manual_cash_transaction_downloaded",false);
    }

    public void setManualRestoredCashTransactions(boolean status) {
        this.editor.putBoolean("manual_cash_transaction_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredProducts() {
        return this.configPref.getBoolean("manual_product_downloaded",false);
    }

    public void setManualRestoredProducts(boolean status) {
        this.editor.putBoolean("manual_product_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredProductHistory() {
        return this.configPref.getBoolean("manual_product_history_downloaded",false);
    }

    public void setManualRestoredProductHistory(boolean status) {
        this.editor.putBoolean("manual_product_history_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredProductCategory() {
        return this.configPref.getBoolean("manual_product_category_downloaded",false);
    }

    public void setManualRestoredProductCategory(boolean status) {
        this.editor.putBoolean("manual_product_category_downloaded", status);
        this.editor.apply();
    }

    public boolean hasManualRestoredBooks() {
        return this.configPref.getBoolean("manual_books_downloaded",false);
    }

    public void setManualRestoredBooks(boolean status) {
        this.editor.putBoolean("manual_books_downloaded", status);
        this.editor.apply();
    }

    public boolean hasRestoredCashTransactions() {
        return this.configPref.getBoolean("cash_transaction_downloaded",false);
    }

    public void setRestoredCashTransactions(boolean status) {
        this.editor.putBoolean("cash_transaction_downloaded", status);
        this.editor.apply();
    }

    public boolean hasRestoredProduct() {
        return this.configPref.getBoolean("product_downloaded",false);
    }

    public void setRestoredProduct(boolean status) {
        this.editor.putBoolean("product_downloaded", status);
        this.editor.apply();
    }

//    public boolean hasRestoredTransactionItem() {
//        return this.configPref.getBoolean("transaction_item_downloaded",false);
//    }

    public void setRestoredTransactionItem(boolean status) {
        this.editor.putBoolean("transaction_item_downloaded", status);
        this.editor.apply();
    }

    public boolean hasRestoredCatgory() {
        return this.configPref.getBoolean("category_downloaded",false);
    }

    public void setRestoredCategory(boolean status) {
        this.editor.putBoolean("category_downloaded", status);
        this.editor.apply();
    }

    public boolean hasDataRestored(){
        return hasRestoredBooks() && hasRestoredCashTransactions() && hasRestoredCatgory() && hasRestoredCatgory() && hasRestoredCustomer() && SetupManager.getInstance().hasRestored();
    }

    public void setHasRestoredProductCategory(boolean status){
        this.editor.putBoolean("product_category",status);
        this.editor.apply();
    }

    public void setHasRestoredProductCategoryCrossRef(boolean status){
        this.editor.putBoolean("product_category_cross_ref",status);
        this.editor.apply();
    }

    public boolean getHasRestoredProductCategory(){
        return this.configPref.getBoolean("product_category", false);
    }

    public boolean getHasRestoredProductCategoryCrossRef(){
        return this.configPref.getBoolean("product_category_cross_ref", false);
    }


    public void setRestoredAll(boolean status) {
        setRestoredBooks(status);
        setRestoredTransactions(status);
        setRestoredcustomer(status);
        setRestoredCategory(status);
        setRestoredCashTransactions(status);
        setRestoredProduct(status);
        setRestoredTransactionItem(status);
    }

    public boolean hasMultipleBusiness() {
        return this.configPref.getBoolean("multiple_books",false);
    }

    public void setMultipleBusiness(boolean status) {
        this.editor.putBoolean("multiple_books", status);
        this.editor.apply();
    }

    public void setBookServerSeq(long bookServerSeq) {
        this.editor.putLong("book_server_seq", bookServerSeq);
        this.editor.apply();
    }

    public long getBookServerSeq() {
        return this.configPref.getLong("book_server_seq", 0);
    }

    public void setExpenseServerSeq(long expenseServerSeq) {
        this.editor.putLong("expense_server_seq", expenseServerSeq);
        this.editor.apply();
    }

    public long getExpenseServerSeq() {
        return this.configPref.getLong("expense_server_seq", 0);
    }

    public void setExpenseCategoryServerSeq(long seq) {
        this.editor.putLong("expense_category_server_seq", seq);
        this.editor.apply();
    }

    public long getExpenseCategoryServerSeq() {
        return this.configPref.getLong("expense_category_server_seq", 0);
    }

    public void setCustomerTxnServerSeq(long seq) {
        this.editor.putLong("customer_txn_server_seq", seq);
        this.editor.apply();
    }

    public long getCustomerTxnServerSeq() {
        return this.configPref.getLong("customer_txn_server_seq", 0);
    }

    public void setCustomerServerSeq(long seq) {
        this.editor.putLong("customer_server_seq", seq);
        this.editor.apply();
    }

    public long getCustomerServerSeq() {
        return this.configPref.getLong("customer_server_seq", 0);
    }
    public void clearAll() {
        editor.clear();
        editor.apply();
    }
}
