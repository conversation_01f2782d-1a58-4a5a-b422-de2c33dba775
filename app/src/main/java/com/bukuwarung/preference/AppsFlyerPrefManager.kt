package com.bukuwarung.preference

import android.content.Context
import android.content.SharedPreferences
import com.bukuwarung.Application
import com.bukuwarung.constants.PrefrenceConstant
import javax.inject.Singleton


private var prefManager: AppsFlyerPrefManager? = null

private lateinit var editor: SharedPreferences.Editor
private lateinit var pref: SharedPreferences

@Singleton
class AppsFlyerPrefManager(val appContext: Context) {
    companion object {
        const val SEND_APPS_FLYER_ID = "SEND_APPS_FLYER_ID"

        fun getInstance(): AppsFlyerPrefManager {
            if (prefManager == null) {
                prefManager = AppsFlyerPrefManager(Application.getAppContext())
            }
            return prefManager!!
        }
    }
    init {
        pref = appContext.getSharedPreferences(PrefrenceConstant.ONBOARDING_PREF, PrefrenceConstant.PRIVATE_MODE)
        editor = pref.edit()
    }


    fun setHasFinishedForId(id: String?) {
        if (id == null) return
        editor.putBoolean(id, true)
        editor.apply()
    }

    fun getHasFinishedForId(id: String): Boolean {
        return pref.getBoolean(id, false)
    }

    fun getPref(): SharedPreferences = pref
}