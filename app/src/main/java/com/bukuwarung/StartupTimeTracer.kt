package com.bukuwarung

import android.app.Activity
import android.app.Application
import android.app.Application.ActivityLifecycleCallbacks
import android.content.Context
import android.os.Bundle
import androidx.core.os.bundleOf
import com.bukuwarung.activities.SplashActivity
import com.bukuwarung.analytics.AppAnalytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.ktx.performance
import com.google.firebase.perf.metrics.Trace
import java.util.concurrent.TimeUnit

class StartupTimeTracer(private val context: Context) : ActivityLifecycleCallbacks {
    val startFromBackgroundRunnable: Runnable
        get() = Runnable {
            isStartedFromBackground = onCreateTime == -1L
        }

    private val maxLatency: Long = TimeUnit.MINUTES.toMillis(1)

    private var appStartTime: Long = -1L

    private var onCreateTime: Long = -1L

    private var trace: Trace? = null

    private var isTooLateToInitUI = false

    private var isStartedFromBackground = false

    private var isRegisteredForLifecycleCallbacks = false

    fun trace() {
        if (!context.initializeFirebase()) return
        trace = Firebase.performance.newTrace("cold_startup_interaction_time").also {
            it.putAttribute("version", BuildConfig.VERSION_NAME)
            it.start()
        }

        appStartTime = System.currentTimeMillis()
        registerActivityLifecycleCallbacks()
    }

    private fun recordStartupTime() {
        val duration = (System.currentTimeMillis() - appStartTime) / 1000.0

        // Performance
        trace?.stop()
        trace = null

        // Analytics
        val event = "cold_startup_interaction_time"
        val params = bundleOf(
            "duration" to duration,
            "version" to BuildConfig.VERSION_NAME,
        )

        val firebaseAnalytics = AppAnalytics.firebaseAnalytics ?: return
        firebaseAnalytics.logEvent(event, params)
    }

    private fun registerActivityLifecycleCallbacks() {
        if (isRegisteredForLifecycleCallbacks) return
        if (context !is Application) return

        context.registerActivityLifecycleCallbacks(this)
        isRegisteredForLifecycleCallbacks = true
    }

    private fun unregisterActivityLifecycleCallbacks() {
        if (!isRegisteredForLifecycleCallbacks) return
        if (context !is Application) return

        context.unregisterActivityLifecycleCallbacks(this)
        isRegisteredForLifecycleCallbacks = false
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        if (isStartedFromBackground || onCreateTime != -1L) return

        onCreateTime = System.currentTimeMillis()
        isTooLateToInitUI = onCreateTime - appStartTime > maxLatency
    }

    override fun onActivityStarted(activity: Activity) {}

    override fun onActivityResumed(activity: Activity) {
        if (isStartedFromBackground || isTooLateToInitUI) {
            unregisterActivityLifecycleCallbacks()
            return
        }
        if (activity is SplashActivity) return

        recordStartupTime()
        unregisterActivityLifecycleCallbacks()
    }

    override fun onActivityPaused(activity: Activity) {}

    override fun onActivityStopped(activity: Activity) {}

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

    override fun onActivityDestroyed(activity: Activity) {}
}
