package com.bukuwarung.controllers.firebase

import android.net.Uri
import android.util.Log
import com.bukuwarung.BuildConfig
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.utils.*
import com.google.firebase.dynamiclinks.DynamicLink
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.google.firebase.dynamiclinks.ShortDynamicLink
import io.reactivex.Single

internal class FirebaseDynamicLinksController {

    fun generateReferralDynamicLink(bookEntity: BookEntity?, referralCode: String?): Single<Uri> {
        try {

            if (referralCode.isNullOrBlank())
                return Single.error(NullPointerException("Short Link is null"))

            Log.v(TAG, "Generating referral Link")
            val referralUri = ReferralLinkBuilder
                    .builder()
                    .setReferralCode(referralCode)
                    .build()

            val playStoreUri = PlaystoreLinkBuilder
                    .builder()
                    .setReferralCode(referralCode)
                    .build()

            return Single.create { singleEmitter ->
                FirebaseDynamicLinks
                        .getInstance()
                        .createDynamicLink()
                        .setLink(referralUri)
                        .setDomainUriPrefix(BuildConfig.DEEPLINK_URL)
                        // Open links with this app on Android
                        .setAndroidParameters(
                                DynamicLink
                                        .AndroidParameters
                                        .Builder()
                                        .setFallbackUrl(playStoreUri)
                                        .build())
                        .buildShortDynamicLink(ShortDynamicLink.Suffix.SHORT)
                        .addOnSuccessListener {
                            it.shortLink ?: singleEmitter.onErrorWithCheck(NullPointerException("Short Link is null"))
                            if(!Utility.isBlank(referralCode)){
                                ReferralPrefManager.getInstance().referralDeeplink = it.shortLink.toString()
                            }
                            singleEmitter.onSuccessWithCheck(it.shortLink!!)
                            Log.v(TAG, "Dynamic Links generated "+it.shortLink)
                        }
                        .addOnFailureListener {
                            singleEmitter.onErrorWithCheck(it)
                        }

            }
        } catch (ex: Exception) {
            Log.e(TAG, "Exception when generating referral link", ex)
            throw ex
        }
    }

    companion object {
        const val TAG = "DynamicLinksController"
    }

    internal class BusinessOwnerNameEmptyException: IllegalStateException() {}

}