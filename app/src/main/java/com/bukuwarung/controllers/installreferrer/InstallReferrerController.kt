package com.bukuwarung.controllers.installreferrer

import android.content.Context
import android.util.Log
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.tutor.prefs.PreferencesManager
import com.bukuwarung.utils.PlaystoreLinkBuilder
import com.bukuwarung.utils.onErrorWithCheck
import com.bukuwarung.utils.onSuccessWithCheck
import io.reactivex.Single
import io.reactivex.SingleEmitter

internal class InstallReferrerController(applicationContext: Context) {

    private var referrerClient: InstallReferrerClient = InstallReferrerClient.newBuilder(applicationContext).build()

    private var prefManager: PreferencesManager = PreferencesManager(applicationContext)

    fun onDestroy() {
        try {
            referrerClient.endConnection()
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
        }
    }

    /**
     * This function will return a Single stream with value referralCode if there's an
     * install referrer code is supplied in the link.
     */
    fun startDetectingInstalls(): Single<Map<String, String>> {
        return if (!prefManager.isFirstInstall) {
            Log.d(TAG, "Not a first install, exiting Install Referrer Controller")
            Single.error(IllegalStateException("This is not an app first install, continue to read deeplink"))
        } else {
            // so that this line won't be called in another app open
            prefManager.setNotFirstInstall()
            Single.create {
                emitter: SingleEmitter<Map<String, String>> ->
                    referrerClient.startConnection(
                            object : InstallReferrerStateListener {

                                override fun onInstallReferrerSetupFinished(responseCode: Int) {
                                    when (responseCode) {
                                        InstallReferrerClient.InstallReferrerResponse.OK -> {
                                            // Connection established.
                                            Log.d(TAG, "Got Install Referrer Response")
                                            val response: ReferrerDetails = referrerClient.installReferrer
                                            val installInfo = response.installReferrer
                                            Log.d(TAG, installInfo)

                                            try {
                                                installInfo ?: emitter.onErrorWithCheck(IllegalStateException("Install Info is null"))

                                                val propBuilder = AppAnalytics.PropBuilder()
                                                propBuilder.put("install_info", installInfo ?: "-")
                                                AppAnalytics.trackEvent("install_referrer_intercept", propBuilder)

                                                val installInfoMap =
                                                        installInfo
                                                                .split("&")
                                                                .associate {
                                                                    val (left, right) = it.split("=")
                                                                    left to right
                                                                }

                                                referrerClient.endConnection()
                                                if (installInfoMap.containsKey(PlaystoreLinkBuilder.REFERRAL_CODE_KEY)) {
                                                    val referralCode: String =
                                                            installInfoMap[PlaystoreLinkBuilder.REFERRAL_CODE_KEY] ?: return emitter.onErrorWithCheck(IllegalStateException("Install Info is null"))
                                                    ReferralPrefManager.getInstance().temporaryReferralCode = referralCode
                                                    emitter.onSuccessWithCheck(installInfoMap)
                                                } else {
                                                    emitter.onErrorWithCheck(IllegalStateException("Install Info doesn't contains Referral Code"))
                                                }
                                            } catch (ex: Exception) {
                                                // Controller fails to parse given install information
                                                Log.e(TAG, "Exception when parsing install info", ex)
                                                val builder = AppAnalytics.PropBuilder()
                                                builder.put("install_info", installInfo ?: "-")
                                                AppAnalytics.trackEvent("install_referrer_error", builder)

                                                emitter.onErrorWithCheck(IllegalStateException("Install Info can't be parsed"))
                                            }
                                        }
                                        InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                                            // API not available on the current Play Store app.
                                            emitter.onErrorWithCheck(IllegalStateException("Install Referrer Not Supported"))
                                        }
                                        InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                                            // Connection couldn't be established.
                                            emitter.onErrorWithCheck(IllegalStateException("Install Referrer Disconnected"))
                                        }
                                    }
                                }

                                override fun onInstallReferrerServiceDisconnected() {
                                    // If connection fails the first time, we'll go straight to deeplink
                                    emitter.onErrorWithCheck(IllegalStateException("Install Referrer Disconnected"))
                                }
                            }
                    )
            }
        }
    }

    companion object {
        const val TAG = "InstalReferrer"
    }

}