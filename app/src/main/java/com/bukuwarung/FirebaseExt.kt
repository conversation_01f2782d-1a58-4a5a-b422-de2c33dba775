package com.bukuwarung

import android.content.Context
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions

fun Context.initializeFirebase(): Boolean {
    val builder = FirebaseOptions.Builder()
    builder.setApiKey(BukuWarungKeys.googleApiKey.orEmpty())
    builder.setApplicationId(BukuWarungKeys.googleAppId.orEmpty())
    builder.setDatabaseUrl(BukuWarungKeys.firebaseDatabaseUrl)
    builder.setGcmSenderId(BukuWarungKeys.gcmDefaultSenderId)
    builder.setStorageBucket(BukuWarungKeys.googleStorageBucket)
    builder.setProjectId(BukuWarungKeys.projectId)

    return try {
        FirebaseApp.initializeApp(this, builder.build())
        FirebaseApp.getInstance()
        true
    } catch (e: Exception) {
        false
    }
}
