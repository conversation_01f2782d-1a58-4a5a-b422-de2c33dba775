package com.bukuwarung

import androidx.lifecycle.lifecycleScope
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.ActivityCrashBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class BukuCrashingActivity: BaseActivity() {

    val binding: ActivityCrashBinding by lazy {
        ActivityCrashBinding.inflate(layoutInflater)
    }

    override fun setViewBinding() {
        setContentView(binding.root)
    }

    override fun setupView() {
        CommonExceptionHandler.getThrowableFromIntent(intent).let {
        }

        binding.bReport.setOnClickListener {
            lifecycleScope.launch {
                binding.bReport.isEnabled = false
                binding.bReport.text = "memulai kembali..."
                delay(1000)
                binding.bReport.text = "dimulai ulang."
                delay(1000)
                finishAffinity()
            }
        }
        binding.bRestartApp.setOnClickListener {
            finishAffinity()
            MainActivity.startActivityAndClearTop(this)
        }
    }

    override fun subscribeState() {

    }

    companion object {
        private const val TAG = "BukuCrashingActivity"
    }
}