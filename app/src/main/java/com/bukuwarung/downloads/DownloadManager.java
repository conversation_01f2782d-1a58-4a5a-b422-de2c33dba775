package com.bukuwarung.downloads;

import android.content.Context;

import com.bukuwarung.constants.AppConst;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskCompletionSource;

import java.io.File;

public final class DownloadManager {
    private final Context context;
    private final String fileName;

    private final boolean f0public;
    private final String url;

    public DownloadManager(Context context2, String str, String str2, boolean z) {
        this.context = context2;
        this.url = str;
        this.fileName = str2;
        this.f0public = z;
    }

    public final Context getContext() {
        return this.context;
    }

    public final String getFileName() {
        return this.fileName;
    }

    public final String getUrl() {
        return this.url;
    }

    public final Task<DownloadResult> download(boolean z) {
        TaskCompletionSource taskCompletionSource = new TaskCompletionSource();
        File file = null;
        if (this.f0public) {
            File publicDownloadsFolder = AppConst.getPublicDownloadsFolder();
            publicDownloadsFolder.mkdirs();
            file = new File(publicDownloadsFolder, this.fileName);
        } else {
            File cachedDocsFolder = AppConst.getCachedDocsFolder();
            cachedDocsFolder.mkdirs();
            file = new File(cachedDocsFolder, this.fileName);
        }
        new Thread(new ApiDownloadThread(this, file, this.fileName, z, taskCompletionSource)).start();
        Task<DownloadResult> task = taskCompletionSource.getTask();
        return task;
    }
}
