package com.bukuwarung.downloads;

import android.content.ContentValues;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;

import androidx.annotation.RequiresApi;
import androidx.core.content.FileProvider;

import com.bukuwarung.BuildConfig;
import com.google.android.gms.tasks.TaskCompletionSource;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;

final class ApiDownloadThread implements Runnable {

    final File targetFile;
    private String fileName;
    final boolean notify;
    final TaskCompletionSource tcs;
    final DownloadManager downloadManager;

    ApiDownloadThread(DownloadManager downloadManager, File targetFile, boolean notify, TaskCompletionSource taskCompletionSource) {
        this.downloadManager = downloadManager;
        this.targetFile = targetFile;
        this.notify = notify;
        this.tcs = taskCompletionSource;
    }

    ApiDownloadThread(DownloadManager downloadManager, File targetFile, String fileName, boolean notify, TaskCompletionSource taskCompletionSource) {
        this.downloadManager = downloadManager;
        this.targetFile = targetFile;
        this.fileName = fileName;
        this.notify = notify;
        this.tcs = taskCompletionSource;
    }

    public final void run() {
        try {
            URL url = new URL(this.downloadManager.getUrl());
            URLConnection connection = url.openConnection();

            Uri uri = null;        // .connect();
//            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
//                uri = downloadFileUsingMediaStore(connection, fileName);
//            } else {
                downloadFile(connection, targetFile);
                uri = FileProvider.getUriForFile(this.downloadManager.getContext(), BuildConfig.fileProviderAuthority, this.targetFile);
           // }
            this.tcs.setResult(new DownloadResult(true, uri, null, null));

        } catch (Exception e) {
            this.tcs.setResult(new DownloadResult(false, null, "No network", e));
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private Uri downloadFileUsingMediaStore(URLConnection connection, String fileName) throws Exception {
        Uri uri = null;
        InputStream inputStream = connection.getInputStream();
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        ContentValues values = new ContentValues();
        values.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName);
        values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS);
        uri = downloadManager.getContext().getContentResolver().insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, values);

        if (uri != null) {
            OutputStream outputStream = downloadManager.getContext().getContentResolver().openOutputStream(uri);
            if (outputStream != null) {
                BufferedOutputStream bos = new BufferedOutputStream(outputStream);
                byte[] buffer = new byte[1024];
                int bytes = bis.read(buffer);
                while (bytes >= 0) {
                    bos.write(buffer, 0, bytes);
                    bos.flush();
                    bytes = bis.read(buffer);
                }
                bos.close();
            }
        }
        bis.close();
        return uri;
    }

    private boolean downloadFile(URLConnection connection, File targetFile) throws Exception {
        BufferedInputStream bufferedInputStream = new BufferedInputStream(connection.getInputStream());
        FileOutputStream fileOutputStream = new FileOutputStream((File) this.targetFile);

        byte[] bArr = new byte[1024];
        int read = bufferedInputStream.read(bArr);
        while (read >= 0) {
            fileOutputStream.write(bArr, 0, read);
            read = bufferedInputStream.read(bArr);

        }
        fileOutputStream.flush();
        fileOutputStream.close();
        bufferedInputStream.close();
        return true;
    }
}
