package com.bukuwarung.collectingcalendar.main.models

import com.bukuwarung.baseui.DefaultRVAdapterModel
import com.bukuwarung.collectingcalendar.main.CollectingCalendarType
import java.util.*

internal open class CollectingDate(
        val user: CollectingDateUser?,
        val amount: Long,
        var date: Date?,
        val type: CollectingDateType = CollectingDateType.DATA,
        val tabType: CollectingCalendarType? = null
): DefaultRVAdapterModel() {

    override fun getId(): Int {
        user ?: return -1
        val combined = user.name+amount
        return combined.hashCode()
    }

}

internal enum class CollectingDateType {
    DATA,
    SEPARATOR
}