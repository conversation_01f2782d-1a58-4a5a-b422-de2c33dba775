package com.bukuwarung.collectingcalendar.main.adapters

import android.view.View
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.collectingcalendar.main.models.CollectingDate
import kotlinx.android.synthetic.main.item_collecting_divider.view.*
import java.text.SimpleDateFormat
import java.util.*

internal class CollectingCalendarSeparatorViewHolder(view: View): DefaultRVViewHolder<CollectingDate>(view) {

    private val calendarText = view.calendarText

    override fun bind(item: CollectingDate) {
        bindTime(item.date)
    }

    private fun bindTime(date: Date?) {
        try {
            val calendar = Calendar.getInstance()
            calendar.time = date

            val dateString = SimpleDateFormat("dd MMMM YYYY", Locale("id", "ID"))
            calendarText.text = dateString.format(calendar.time)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    override fun free() {}


}