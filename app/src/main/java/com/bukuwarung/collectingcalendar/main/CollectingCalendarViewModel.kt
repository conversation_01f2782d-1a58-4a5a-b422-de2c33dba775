package com.bukuwarung.collectingcalendar.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.ViewModel
import com.bukuwarung.collectingcalendar.main.models.CollectingDate
import com.bukuwarung.collectingcalendar.main.models.CollectingDateUser
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.session.User
import com.bukuwarung.utils.afterToday
import com.bukuwarung.utils.beforeToday
import com.bukuwarung.utils.isToday
import com.bukuwarung.utils.parseToZeroHourDate
import kotlin.math.absoluteValue

internal class CollectingCalendarViewModel(
        private val customerRepository: CustomerRepository,
        private val type: CollectingCalendarType
): ViewModel() {

    init {
        getData()
    }

    private lateinit var customerData: LiveData<List<CustomerEntity>>
    private lateinit var customerReminders: LiveData<List<CustomerEntity>>

    lateinit var stateData: MediatorLiveData<CollectingCalendarState>

    private fun getData() {
        stateData = MediatorLiveData()

        customerData =
                customerRepository
                        .getCustomersByBusiness(User.getBusinessId())

        customerReminders =
                customerRepository
                        .getObservableCustomerWithReminderDate(User.getBusinessId())

        stateData.addSource(
                customerData
        ) {
            val currentState = stateData.value ?: CollectingCalendarState()
            stateData.value = currentState.copy(
                    users = it
                            .map { customer ->
                            CollectingDateUser(
                                    id = customer.customerId,
                                    name = customer.name?:"",
                                    amount = customer.balance.toLong(),
                                    hasDueDate = !customer.dueDate.isNullOrBlank()
                            )
                    },
                    currentState = CollectingCalendarStateType.Loaded
            )
        }

        stateData.addSource(
                customerReminders
        ) {
            val currentState = stateData.value ?: CollectingCalendarState()

            val filteredCustomers = it.filter { customer ->
                val actualDate = customer.dueDate.parseToZeroHourDate()
                when (type) {
                    CollectingCalendarType.PastCalendar -> {
                        customer.balance < 0 && actualDate.beforeToday()
                    }
                    CollectingCalendarType.PresentCalendar -> {
                        customer.balance < 0 && actualDate.isToday()
                    }
                    CollectingCalendarType.FutureCalendar -> {
                        customer.balance < 0 && actualDate.afterToday()
                    }
                }
            }

            stateData.value = currentState.copy(
                    items = filteredCustomers.map { customer ->
                        CollectingDate(
                                user = CollectingDateUser(
                                        id = customer.customerId,
                                        name = customer.name,
                                        amount = customer.balance.toLong().absoluteValue,
                                        hasDueDate = !customer.dueDate.isNullOrBlank()
                                ),
                                date = customer.dueDate.parseToZeroHourDate()?.time,
                                amount = customer.balance.toLong(),
                                tabType = type
                        )
                    },
                    currentState = CollectingCalendarStateType.Loaded
            )
        }

    }

}

internal data class CollectingCalendarState(
        val users: List<CollectingDateUser> = listOf(),
        val items: List<CollectingDate> = listOf(),
        val currentState: CollectingCalendarStateType = CollectingCalendarStateType.Loading
)

internal sealed class CollectingCalendarStateType {
    object Loading: CollectingCalendarStateType()
    object Loaded: CollectingCalendarStateType()
}