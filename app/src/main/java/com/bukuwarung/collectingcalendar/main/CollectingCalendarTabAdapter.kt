package com.bukuwarung.collectingcalendar.main

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import com.bukuwarung.R

class CollectingCalendarTabAdapter(private val applicationContext: Context, fragmentManager: FragmentManager)
    : FragmentStatePagerAdapter(fragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {

    override fun getItem(position: Int): Fragment {
        return when(position) {
            POSITION_FIRST -> {
                CollectingCalendarFragment.getFragmentByType(CollectingCalendarType.PastCalendar)
            }
            POSITION_SECOND -> {
                CollectingCalendarFragment.getFragmentByType(CollectingCalendarType.PresentCalendar)
            }
            POSITION_THIRD -> {
                CollectingCalendarFragment.getFragmentByType(CollectingCalendarType.FutureCalendar)
            }
            else -> {
                CollectingCalendarFragment.getFragmentByType(CollectingCalendarType.FutureCalendar)
            }
        }
    }

    override fun getCount(): Int = TAB_COUNT

    override fun getPageTitle(position: Int): CharSequence? {
        return when(position) {
            POSITION_FIRST -> {
                applicationContext.resources.getString(R.string.collecting_calendar_title_past)
            }
            POSITION_SECOND -> {
                applicationContext.resources.getString(R.string.collecting_calendar_title_present)
            }
            POSITION_THIRD -> {
                applicationContext.resources.getString(R.string.collecting_calendar_title_future)
            }
            else -> {
                applicationContext.resources.getString(R.string.collecting_calendar_title_future)
            }
        }
    }

    companion object {

        const val POSITION_FIRST = 0
        const val POSITION_SECOND = 1
        const val POSITION_THIRD = 2

        const val TAB_COUNT = 3

    }

}