package com.bukuwarung.collectingcalendar.main.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.collectingcalendar.main.models.CollectingDate
import com.bukuwarung.collectingcalendar.main.models.CollectingDateType
import java.util.*

internal class CollectingCalendarAdapter: DefaultRVAdapter<CollectingDate>() {

    override var results: List<CollectingDate?>? = null
        set(value) {
            field = processCollectingDate(value)
            notifyDataSetChanged()
        }

    private fun processCollectingDate(items: List<CollectingDate?>?): List<CollectingDate?>? {
        items ?: return null

        return items
                .map {
                    val newDate = Calendar.getInstance()

                    // we need to convert all hours to 00:00:00 00 to compare
                    newDate.time = it?.date
                    newDate.set(Calendar.HOUR_OF_DAY, 0)
                    newDate.set(Calendar.MINUTE, 0)
                    newDate.set(Calendar.SECOND, 0)
                    newDate.set(Calendar.MILLISECOND, 0)

                    CollectingDate(
                            user = it?.user,
                            type = it?.type ?: CollectingDateType.DATA,
                            amount = it?.amount ?: 0L,
                            date = newDate.time,
                            tabType = it?.tabType
                    )
                }
        .groupBy { it.date }
        .flatMap {
//            val list = mutableListOf<CollectingDate?>(
//                    CollectingDateSeparator(
//                            date = it.value.first()?.date ?: Calendar.getInstance().time // should never be null
//                    )
//            )
            val list = mutableListOf<CollectingDate?>()
            list.addAll(it.value)
            list
        }.sortedBy {
            it?.date
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<CollectingDate> {
        return when (viewType) {
            ITEM_TYPE_DATA -> {
                CollectingCalendarViewHolder(
                        LayoutInflater
                                .from(parent.context)
                                .inflate(
                                        R.layout.item_collecting,
                                        parent,
                                        false
                                )
                )
            }
            ITEM_TYPE_SEPARATOR -> {
                CollectingCalendarSeparatorViewHolder(
                        LayoutInflater
                                .from(parent.context)
                                .inflate(
                                        R.layout.item_collecting_divider,
                                        parent,
                                        false
                                )
                )
            }
            else -> {
                CollectingCalendarViewHolder(
                        LayoutInflater
                                .from(parent.context)
                                .inflate(
                                        R.layout.item_collecting,
                                        parent,
                                        false
                                )
                )
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        val data = results?.get(position) ?: return ITEM_TYPE_DATA
        return when (data.type) {
            CollectingDateType.DATA -> {
                ITEM_TYPE_DATA
            }
            CollectingDateType.SEPARATOR -> {
                ITEM_TYPE_SEPARATOR
            }
        }
    }

    companion object {
        const val ITEM_TYPE_DATA = 0
        const val ITEM_TYPE_SEPARATOR = 1
    }

}