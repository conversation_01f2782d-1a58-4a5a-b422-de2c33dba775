package com.bukuwarung.collectingcalendar.main

import android.os.Bundle
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.savedstate.SavedStateRegistryOwner
import com.bukuwarung.database.repository.CustomerRepository

class CollectingCalendarViewModelFactory(
        private val owner: SavedStateRegistryOwner,
        private val customerRepository: CustomerRepository,
        private val type: CollectingCalendarType,
        defaultArgs: Bundle? = null
) : AbstractSavedStateViewModelFactory(owner, defaultArgs) {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(
            key: String,
            modelClass: Class<T>,
            handle: SavedStateHandle
    ): T {
        return CollectingCalendarViewModel(
                customerRepository,
                type
        ) as T
    }
}