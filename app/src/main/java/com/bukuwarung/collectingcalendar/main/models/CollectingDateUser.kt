package com.bukuwarung.collectingcalendar.main.models

import kotlin.math.absoluteValue

data class CollectingDateUser(
        val id: String,
        val name: String,
        val amount: Long,
        val hasDueDate: Boolean,
        val dueDate: String? = null
)

fun List<CollectingDateUser>.debtAmount(): Long {
    return this.fold(0L, operation = { amt, data ->
        amt + data.amount
    }).absoluteValue
}