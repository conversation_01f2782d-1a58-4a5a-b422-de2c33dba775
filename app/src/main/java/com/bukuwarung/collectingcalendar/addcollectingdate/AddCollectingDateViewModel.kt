package com.bukuwarung.collectingcalendar.addcollectingdate

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.ViewModel
import com.bukuwarung.collectingcalendar.addcollectingdate.models.NewCollectingDate
import com.bukuwarung.collectingcalendar.main.models.CollectingDateUser
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.session.User
import kotlin.math.absoluteValue

internal class AddCollectingDateViewModel(
        private val customerRepository: CustomerRepository,
        private val predefinedUserId: String?
): ViewModel() {

    init {
        getData()
    }

    private lateinit var unremindedCustomer: LiveData<List<CustomerEntity>>
    private lateinit var predefinedCustomer: CustomerEntity

    lateinit var stateData: MediatorLiveData<AddCollectingDateState>

    private fun getData() {

        stateData = MediatorLiveData()

        if (!predefinedUserId.isNullOrBlank()) {
            predefinedCustomer =
                    customerRepository
                            .getCustomerById(predefinedUserId)

            stateData.value = AddCollectingDateState(
                    items = listOf(predefinedCustomer).map {
                        NewCollectingDate(
                                index = 1,
                                user = CollectingDateUser(
                                        id = it.customerId,
                                        name = it.name,
                                        amount = it.balance.toLong().absoluteValue,
                                        hasDueDate = !it.dueDate.isNullOrBlank(),
                                        dueDate = it.dueDate
                                ),
                                amount = it.balance.toLong().absoluteValue,
                                oldCollectingDate = it.dueDate
                        )
                    },
                    type = AddCollectingDateStateType.Loaded
            )
        } else {
            unremindedCustomer =
                    customerRepository
                            .getObservableCustomerWithoutReminderDate(User.getBusinessId())

            stateData.addSource(
                    unremindedCustomer
            ) {
                val currentState = stateData.value ?: AddCollectingDateState()
                stateData.value = currentState.copy(
                        items = it
                                .mapIndexed { index, customer ->
                                    NewCollectingDate(
                                            index = index + 1, // escape 0 based indexing
                                            user = CollectingDateUser(
                                                    id = customer.customerId,
                                                    name = customer.name,
                                                    amount = customer.balance.toLong().absoluteValue,
                                                    hasDueDate = !customer.dueDate.isNullOrBlank(),
                                                    dueDate = customer.dueDate
                                            ),
                                            amount = customer.balance.toLong().absoluteValue,
                                            oldCollectingDate = customer.dueDate
                                    )
                                },
                        type = AddCollectingDateStateType.Loaded
                )

                stateData.removeSource(unremindedCustomer)
            }
        }
    }

}

internal data class AddCollectingDateState(
        val items: List<NewCollectingDate> = listOf(),
        val type: AddCollectingDateStateType = AddCollectingDateStateType.Loading
)

internal sealed class AddCollectingDateStateType {
    object Loading: AddCollectingDateStateType()
    object Loaded: AddCollectingDateStateType()
}