package com.bukuwarung.collectingcalendar.addcollectingdate.models

import com.bukuwarung.baseui.DefaultRVAdapterModel
import com.bukuwarung.collectingcalendar.main.models.CollectingDateUser

data class NewCollectingDate(
        val index: Int,
        val user: CollectingDateUser,
        val amount: Long,
        var oldCollectingDate: String? = null
): DefaultRVAdapterModel() {

    override fun getId(): Int = user.id.hashCode()

}