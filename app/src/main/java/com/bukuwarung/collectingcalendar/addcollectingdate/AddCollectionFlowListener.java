package com.bukuwarung.collectingcalendar.addcollectingdate;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.bukuwarung.R;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.tutor.animation.MaterialFlowListener;
import com.bukuwarung.tutor.prefs.PreferencesManager;
import com.bukuwarung.tutor.shape.Focus;
import com.bukuwarung.tutor.shape.FocusGravity;
import com.bukuwarung.tutor.shape.ShapeType;
import com.bukuwarung.tutor.view.MaterialIntroView;

public class AddCollectionFlowListener implements MaterialFlowListener {

    private Context mContext;
    private Activity activity;

    private static final int MAX_STEP = 1;

    public AddCollectionFlowListener(Context context, Activity activity){
        mContext = context;
        this.activity = activity;
    }

    public void showIntro(View view, String id, String header, String body, FocusGravity focusGravity, int step, ShapeType shape, int delay) {
        new PreferencesManager(mContext).reset(id);
        new MaterialIntroView.Builder(activity)
                .enableDotAnimation(false)
                .setFocusGravity(focusGravity)
                .setFocusType(Focus.MINIMUM)
                .setDelayMillis(delay)
                .setShape(shape).dismissOnTouch(true)
                .enableFadeAnimation(true)
                .performClick(true)
                .setInfoText(body)
                .setHeaderText(header)
                .setTarget(view)
                .setFlowListener(this)
                .setUsageId(id)
                .setIntroStep(step)
                .setMaxSteps(MAX_STEP)
                .setBackText(null)
                .setNextText(mContext.getString(R.string.close_tutorial))
                .show();
    }

    public String getText(int resId){
        return mContext.getString(resId);
    }

    @Override
    public void onNextButtonClicked(String materialIntroViewId, int next) {}

    @Override
    public void onBackButtonClicked(String materialIntroViewId, int next) {}

    @Override
    public void onStartButtonClicked(String materialIntroViewId, int i) {}

    @Override
    public void onCloseButtonClicked(String materialIntroViewId) {}

    @Override
    public void onDismiss() {
        FeaturePrefManager.getInstance().setHasShownCollectingCalendarAddTutor(true);
    }
}
