package com.bukuwarung.collectingcalendar.addcollectingdate.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.collectingcalendar.addcollectingdate.models.NewCollectingDate

internal class AddCollectingDateAdapter(
        private val onSaveCallback: ((customerId: String,
                                      storableDateString: String) -> Unit)
): DefaultRVAdapter<NewCollectingDate>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<NewCollectingDate> {
        return AddCollectingDateViewHolder(
                LayoutInflater
                        .from(parent.context)
                        .inflate(
                                R.layout.collecting_date_picker_item,
                                parent,
                                false
                        ),
                results?.size ?: 0,
                onSaveCallback
        )
    }

}