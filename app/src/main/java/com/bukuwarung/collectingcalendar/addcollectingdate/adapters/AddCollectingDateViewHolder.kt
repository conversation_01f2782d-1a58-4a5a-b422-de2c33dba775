package com.bukuwarung.collectingcalendar.addcollectingdate.adapters

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.collectingcalendar.addcollectingdate.models.NewCollectingDate
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.getDateZeroHour
import kotlinx.android.synthetic.main.collecting_date_picker_item.view.*
import java.util.*
import kotlin.math.absoluteValue

class AddCollectingDateViewHolder(
        private val view: View,
        private val itemCount: Int,
        private val onSaveCallback: ((customerId: String,
                                      storableDateString: String)->Unit)
): DefaultRVViewHolder<NewCollectingDate>(view) {

    private val mainContainer = view.mainContainer
    private val firstLetterTV = view.firstLetter
    private val customerImage = view.photo
    private val nameTV = view.nameText
    private val customerDebtTV = view.customerDebtAmount
    private val customerDebtInfo = view.customerDebtInfo
    private val calendarView = view.calendarView
    private val customerCountTV = view.customerCount

    override fun bind(item: NewCollectingDate) {
        firstLetterTV.text = if (item.user.name.isBlank()) {
            "-"
        } else {
            item.user.name.first().toString()
        }
        customerImage.visibility = View.GONE
        firstLetterTV.visibility = View.VISIBLE
        nameTV.text = item.user.name
        customerDebtTV.text = view.resources.getString(
                R.string.collecting_calendar_debt_amt,
                Utility.formatCurrency(item.amount.absoluteValue.toDouble())
        )
        customerCountTV.text = view.resources.getString(
                R.string.collecting_calendar_user_count,
                item.index, itemCount
        )

        if (item.user.hasDueDate) {
            val predefinedDate = Calendar.getInstance()
            predefinedDate.time = DateTimeUtils.convertToDateYYYYMMDD(item.user.dueDate)

            if (predefinedDate.after(Calendar.getInstance())) {
                customerDebtInfo.visibility = View.VISIBLE
                customerDebtInfo.text = view.resources.getString(
                        R.string.collecting_calendar_debt_duedate,
                        DateTimeUtils.formatCollectingDate(item.user.dueDate)
                )
            }

            calendarView.date = predefinedDate.timeInMillis
        }

        val lp = mainContainer.layoutParams as RecyclerView.LayoutParams

        if (item.index == 1) lp.leftMargin = 36

        if (itemCount == 1) {
            lp.width = ViewGroup.LayoutParams.MATCH_PARENT
            lp.rightMargin = 36
        } else if (item.index == itemCount) {
            lp.rightMargin = 36
        }

        mainContainer.layoutParams = lp

        calendarView.minDate = Calendar.getInstance().timeInMillis - 1000;

        calendarView.setOnDateChangeListener { _, year, month, date ->
            setReminderDate(item.user.id, year, month, date, item)
        }
    }

    private fun setReminderDate(customerId: String, year: Int, month: Int, date: Int, item: NewCollectingDate) {
        val calendar = Calendar.getInstance()
        calendar.set(year, month, date)
        val storableDateString = Utility.getStorableDateString(calendar.getDateZeroHour())

        customerDebtInfo.visibility = View.VISIBLE
        customerDebtInfo.text = view.resources.getString(
                R.string.collecting_calendar_debt_duedate,
                DateTimeUtils.formatCollectingDate(storableDateString)
        )
        item.oldCollectingDate = storableDateString

        val prop = AppAnalytics.PropBuilder()
        prop.put("date", storableDateString)
        prop.put("entryPoint", "calendar")
        AppAnalytics.trackEvent("collecting_date_set_new_collecting_date",prop)

        onSaveCallback(customerId, storableDateString)
    }

    override fun free() {}


}