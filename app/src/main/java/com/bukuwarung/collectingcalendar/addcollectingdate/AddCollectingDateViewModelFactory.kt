package com.bukuwarung.collectingcalendar.addcollectingdate

import android.os.Bundle
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.savedstate.SavedStateRegistryOwner
import com.bukuwarung.database.repository.CustomerRepository

class AddCollectingDateViewModelFactory(
        private val owner: SavedStateRegistryOwner,
        private val customerRepository: CustomerRepository,
        private val predefinedUserId: String?,
        defaultArgs: Bundle? = null
) : AbstractSavedStateViewModelFactory(owner, defaultArgs) {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel?> create(
            key: String,
            modelClass: Class<T>,
            handle: SavedStateHandle
    ): T {
        return AddCollectingDateViewModel(
                customerRepository,
                predefinedUserId
        ) as T
    }
}