package com.bukuwarung.dialogs

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.hideView
import com.zoho.salesiqembed.ZohoSalesIQ
import kotlinx.android.synthetic.main.dialog_help.*


class HelpDialog(context: Context) : BaseDialog(context, BaseDialogType.POPUP) {

    override fun getResId(): Int = R.layout.dialog_help
    val phone: String by lazy { RemoteConfigUtils.getCustomerCarePhoneNumber() }
    private var bookEntity: BookEntity? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        bookEntity =
            BusinessRepository.getInstance(context)?.getBusinessByIdSync(User.getBusinessId())
        setUseFullWidth(false)
        setCancellable(true)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        setUpView()
    }

    private fun setUpView() {
        cv_help_chat.setOnClickListener {
            if (RemoteConfigUtils.showZohodeskHelp()) {
                ZohoSalesIQ.Chat.show()
                ZohoSalesIQ.Visitor.setContactNumber(bookEntity?.businessPhone)
                bookEntity?.businessEmail?.let {
                    ZohoSalesIQ.Visitor.setEmail(it)
                } ?: kotlin.run {
                    ZohoSalesIQ.Visitor.setEmail(bookEntity?.businessPhone.plus("@merchant.com"))
                }
                ZohoSalesIQ.Visitor.addInfo(
                    "Merchant Tier",
                    SessionManager.getInstance().loyaltyTierName
                )
                bookEntity?.businessOwnerName?.let {
                    ZohoSalesIQ.Visitor.setName(it)
                } ?: kotlin.run {
                    ZohoSalesIQ.Visitor.setName("No Name")
                }
            } else {
                context.startActivity(WebviewActivity.createIntent(context,"https://v2.zopim.com/widget/popout.html?key=j42RhpyMIqP2ZYEYUZGhHKuodHW13WiO", "Help Center"))
            }
            dismiss()
        }

        if (RemoteConfigUtils.showTelephoneHelp()) {
            cv_help_call.setOnClickListener {
                val intent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:$phone"))
                context.startActivity(intent)
                dismiss()
            }
        } else {
            cv_help_call.hideView()
        }

        iv_help_close.setOnClickListener {
            dismiss()
        }

    }
}