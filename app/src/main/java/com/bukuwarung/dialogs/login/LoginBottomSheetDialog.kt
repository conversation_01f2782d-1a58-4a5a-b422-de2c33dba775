package com.bukuwarung.dialogs.login

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.onboarding.LoginViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.data.repository.AuthRemoteDataSource
import com.bukuwarung.data.repository.AuthRepository
import com.bukuwarung.data.repository.LoginRemoteDataSource
import com.bukuwarung.data.repository.LoginRepository
import com.bukuwarung.data.restclient.ApiResponseCallAdapterFactory
import com.bukuwarung.data.restclient.HeadersInterceptor
import com.bukuwarung.data.restclient.NullOnEmptyConverterFactory
import com.bukuwarung.data.restclient.OtpInterceptor
import com.bukuwarung.databinding.BottomsheetLoginBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.domain.auth.AuthUseCase
import com.bukuwarung.enums.NotificationChannel
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.afterTextChanged
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit


class LoginBottomSheetDialog : BaseBottomSheetDialogFragment() {
    companion object {
        const val TAG = "loginSheet"
        private const val ENTRY_POINT = "ENTRY_POINT"
        fun newInstance(entryPoint: String = "", listener: LoginSheetListener? = null): LoginBottomSheetDialog {
            val dialog = LoginBottomSheetDialog()
            dialog.listener = listener
            val bundle = Bundle()
            bundle.putString(ENTRY_POINT, entryPoint)
            dialog.arguments = bundle
            return dialog
        }
    }
    private var listener: LoginSheetListener? = null
    private val entryPoint by lazy {
        arguments?.getString(ENTRY_POINT) ?: ""
    }
    interface LoginSheetListener {
        fun goToVerifyOtp(phone: String, countryCode: String, method: String)
    }

    var images = intArrayOf(R.drawable.login_prop1, R.drawable.login_prop2, R.drawable.login_prop3, R.drawable.login_prop4)

    private lateinit var binding: BottomsheetLoginBinding
    private lateinit var viewModel: LoginViewModel

    private var mViewPagerAdapter: ImageViewPagerAdapter? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = BottomsheetLoginBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDismiss(dialog: DialogInterface) {
        //TODO: keyboard issue
//        InputUtils.hideKeyboard(context)
        super.onDismiss(dialog)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet = dialog.findViewById<View>(R.id.design_bottom_sheet)
            bottomSheet?.let { sheet ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                sheet.parent.parent.requestLayout()
            }
        }
        getActivity()?.getWindow()?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)

        val authRepository = AuthRepository(provideAuthRemoteDataSource())
        val loginRepository = LoginRepository(provideLoginRemoteDataSource())
        val authUseCase = AuthUseCase(authRepository, loginRepository)
        viewModel = LoginViewModel(authUseCase)
        setupView()
        view.findViewById<View>(R.id.close_img).setOnClickListener {
            dialog?.dismiss()
        }

        mViewPagerAdapter = ImageViewPagerAdapter(this.requireContext(), images)
        // 2 -> hide prop images
        if(AppConfigManager.getInstance().enableGuestFeature == 2){
            binding.bannerImg.visibility = View.GONE
        }
        binding.bannerImg.setAdapter(mViewPagerAdapter)
        binding.bannerImg.setClipToPadding(false);
        binding.bannerImg.setPadding(100,0,100,0);
        binding.bannerImg.setPageMargin(24);
        binding.bannerImg.setCurrentItem(1);
        if(!AuthHelper.isValidSessionOperation()){
            binding.title.text = getString(R.string.ask_to_register_title_after_trial)
            binding.txtSubtitle.text = getString(R.string.ask_to_register_subtitle_after_trial)
        }

        subscribeState()
    }

    private fun setupView() {
        viewModel.onEventReceived(LoginViewModel.Event.OnCreateView)

        binding.phoneET.afterTextChanged {
            val enable = it.isNotBlank() && it.length > 5
            binding.btnWa.isEnabled = enable
            binding.btnSms.isEnabled = enable
        }
        binding.countryPicker.setCountryForPhoneCode(62)
        binding.countryPicker.setOnCountryChangeListener {
            viewModel.onEventReceived(LoginViewModel.Event.OnCountryPicked(binding.countryPicker.selectedCountryCodeWithPlus))
        }
        binding.loadingPanel.visibility = View.GONE
        binding.inputLayout.visibility = View.VISIBLE
        binding.btnSms.setOnClickListener {
            checkBeforeRequestOtp(NotificationChannel.SMS.value)
        }
        binding.btnWa.setOnClickListener {
            checkBeforeRequestOtp(NotificationChannel.WA.value)
        }
    }

    private fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is LoginViewModel.State.Error -> handleError(it.code, it.message, it.phone, it.method)
                is LoginViewModel.State.GoToVerifyOtp ->
                    goToVerifyOtp(it.phone, it.countryCode, it.method)

                else -> {}
            }
        }
    }

    public fun keyboardState(isopen:Boolean){
        if(isopen){
//            binding.phoneET.requestFocus();
//            InputUtils.showKeyboard(activity)
        }
    }

    private fun checkBeforeRequestOtp(method: String) {
        val phone = Utility.cleanPhonenumber(binding.phoneET.text.toString())
        try {
            if (!Utility.hasInternet()) {
                binding.warningText.text = getString(R.string.no_internet_error)
                binding.warningText.visibility = View.VISIBLE
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_FAIL)
                propBuilder.put(AnalyticsConst.DETAIL, AnalyticsConst.DETAIL_NO_NETWORK)
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, entryPoint)
                propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
                return
            }
            if (Utility.loginExceeded()) {
                binding.warningText.text = getString(R.string.login_exceeded_error)
                binding.warningText.visibility = View.VISIBLE
                return
            }
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_START)
            propBuilder.put(AnalyticsConst.DETAIL, phone)
            propBuilder.put(AnalyticsConst.OTP_METHOD, method)
            propBuilder.put(AnalyticsConst.ENTRY_POINT2, entryPoint)
            propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
        } catch (e: Exception) {
            binding.warningText.text = getString(R.string.general_device_error)
            binding.warningText.visibility = View.VISIBLE
        }
        binding.warningText.visibility = View.GONE
        binding.warningText.text = null
        binding.loadingPanel.visibility = View.VISIBLE
        InputUtils.hideKeyboardFrom(context, binding.phoneET)
        binding.inputLayout.visibility = View.GONE
        viewModel.onEventReceived(LoginViewModel.Event.OnRequestOTP(phone, method))
        val prop = AppAnalytics.PropBuilder()
    }

    private fun goToVerifyOtp(phone: String, countryCode: String, method: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_SENT_OTP)
        propBuilder.put(AnalyticsConst.DETAIL, phone)
        propBuilder.put(AnalyticsConst.OTP_METHOD, method)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, entryPoint)
        propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
        listener?.goToVerifyOtp(phone, countryCode, method)
        dismiss()
    }

    fun fillPhoneNoCountryCode(str: String?) {
        if (str.isNullOrBlank()) return
        try {
            var purifyMobileNumber = Utility.cleanPhonenumber(str)
            purifyMobileNumber = InputUtils.removedCountryCodeFromMobileNo(purifyMobileNumber)
            val findCountryCodeFromMobileNo = InputUtils.findCountryCodeFromMobileNo(str)
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.COUNTRY_CD, findCountryCodeFromMobileNo)
            prop.put(AnalyticsConst.NUMBER, purifyMobileNumber)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_PHONE_NUMBER_AUTO, prop)
            if (!findCountryCodeFromMobileNo.isNullOrBlank()) {
                binding.countryPicker.setCountryForPhoneCode(findCountryCodeFromMobileNo.substring(1).toInt())
            }
            if (!purifyMobileNumber.isNullOrBlank()) {
                binding.phoneET.setText(purifyMobileNumber)
                binding.phoneET.setSelection(purifyMobileNumber.length)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PHONE_SELECTED_FROM_HINT)
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    private fun handleError(code: Int, message: String, phone: String, method: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, message)
        propBuilder.put(AnalyticsConst.DETAIL, phone)
        propBuilder.put(AnalyticsConst.OTP_METHOD, method)
        propBuilder.put(AnalyticsConst.LOGIN_PASSWORD, RemoteConfigUtils.getAuthVariant() != 0)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_ASK_OTP, propBuilder)
        if (code > 500) {
            // server error
            binding.warningText.text = getString(R.string.otp_error_server)
        } else {
            // unknown error
            binding.warningText.text = getString(R.string.general_device_error)
        }

        binding.warningText.visibility = View.VISIBLE
        binding.loadingPanel.visibility = View.GONE
        binding.inputLayout.visibility = View.VISIBLE
    }

    private fun provideAuthRemoteDataSource(): AuthRemoteDataSource {
        val builder: OkHttpClient.Builder = okhttpClientBuilder()
        builder.addInterceptor(OtpInterceptor())
        val okHttpClient = builder.build()
        val retrofit = Retrofit.Builder().baseUrl(AppConfigManager.getInstance().getOTPApi())
                .addCallAdapterFactory(ApiResponseCallAdapterFactory())
                .addConverterFactory(NullOnEmptyConverterFactory())
                .addConverterFactory(GsonConverterFactory.create(provideGson()))
                .client(okHttpClient)
                .build()
        return retrofit.create(AuthRemoteDataSource::class.java)
    }

    private fun provideLoginRemoteDataSource(): LoginRemoteDataSource {
        val builder: OkHttpClient.Builder = okhttpClientBuilder()
        val okHttpClient = builder.build()
        val retrofit = Retrofit.Builder().baseUrl(AppConfigManager.getInstance().getOTPApi())
                .addCallAdapterFactory(ApiResponseCallAdapterFactory())
                .addConverterFactory(NullOnEmptyConverterFactory())
                .addConverterFactory(GsonConverterFactory.create(provideGson()))
                .client(okHttpClient)
                .build()
        return retrofit.create(LoginRemoteDataSource::class.java)
    }

    private fun provideGson() = GsonBuilder().setLenient().create()

    private fun okhttpClientBuilder(): OkHttpClient.Builder {
        return OkHttpClient.Builder()
                .addInterceptor(HeadersInterceptor())
                .addInterceptor(provideLoggingInterceptor())
                .callTimeout(1, TimeUnit.MINUTES)
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
    }

    private fun provideLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        }
    }
}
