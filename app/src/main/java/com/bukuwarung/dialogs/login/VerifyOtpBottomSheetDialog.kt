package com.bukuwarung.dialogs.login

import android.content.ActivityNotFoundException
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.provider.Settings
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.lifecycle.observe
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.VerifyOtpActivity
import com.bukuwarung.activities.onboarding.VerifyOtpViewModel
import com.bukuwarung.activities.onboarding.helper.AutoDetectOTP
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.repository.AuthRemoteDataSource
import com.bukuwarung.data.repository.AuthRepository
import com.bukuwarung.data.repository.LoginRemoteDataSource
import com.bukuwarung.data.repository.LoginRepository
import com.bukuwarung.data.restclient.ApiResponseCallAdapterFactory
import com.bukuwarung.data.restclient.HeadersInterceptor
import com.bukuwarung.data.restclient.NullOnEmptyConverterFactory
import com.bukuwarung.data.restclient.OtpInterceptor
import com.bukuwarung.database.repository.FirebaseRepository
import com.bukuwarung.databinding.BottomsheetVerifyOtpBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.domain.auth.AuthUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.data.repository.PaymentsRepository
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.*
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.firestore.DocumentSnapshot
import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import java.util.regex.Matcher
import java.util.regex.Pattern

class VerifyOtpBottomSheetDialog: BaseBottomSheetDialogFragment() {
    companion object {
        const val TAG = "otpSheet"
        private const val ENTRY_POINT = "ENTRY_POINT"
        private const val PHONE = "PHONE"
        private const val COUNTRY_CODE = "COUNTRY_CODE"
        fun newInstance(phone: String = "", countryCode: String, entryPoint: String = "", listener: OtpSheetListener? = null): VerifyOtpBottomSheetDialog {
            val dialog = VerifyOtpBottomSheetDialog()
            dialog.listener = listener
            val bundle = Bundle()
            bundle.putString(ENTRY_POINT, entryPoint)
            bundle.putString(PHONE, phone)
            bundle.putString(COUNTRY_CODE, countryCode)
            dialog.arguments = bundle
            return dialog
        }
    }

    private var listener: OtpSheetListener? = null
    private val entryPoint by lazy {
        arguments?.getString(ENTRY_POINT) ?: ""
    }
    private val phone by lazy {
        arguments?.getString(PHONE) ?: ""
    }
    private val countryCode by lazy {
        arguments?.getString(COUNTRY_CODE) ?: ""
    }

    interface OtpSheetListener {
        fun callLoginBottomsheet(openKeyboard:Boolean, entryPoint: String = "")
        fun onFinishOtp()
    }

    private lateinit var binding: BottomsheetVerifyOtpBinding
    private lateinit var viewModel: VerifyOtpViewModel
    private var hasObserveFirestore = false
    private var autoVerify = false
    private var cTimer: CountDownTimer? = null
    private var autoDetectOTP: AutoDetectOTP? = null
    private var autoDetect = false

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = BottomsheetVerifyOtpBinding.inflate(layoutInflater, container, false)
        getActivity()?.getWindow()?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)

        return binding.root
    }

    override fun onDismiss(dialog: DialogInterface) {
        //TODO: keyboard issue
//        InputUtils.hideKeyboard(context)
        super.onDismiss(dialog)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet = dialog.findViewById<View>(R.id.design_bottom_sheet)
            bottomSheet?.let { sheet ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                sheet.parent.parent.requestLayout()
            }
        }
        val authRepository = AuthRepository(provideAuthRemoteDataSource())
        val loginRepository = LoginRepository(provideLoginRemoteDataSource())
        val authUseCase = AuthUseCase(authRepository, loginRepository)
        val firebaseRepository = FirebaseRepository.getInstance(context)
        viewModel = VerifyOtpViewModel(authUseCase, firebaseRepository)
        setupView()
        subscribeState()
    }

    private fun setupView() {
        lifecycle.addObserver(viewModel)
        try {
            setAutoDetectOTP()

            binding.closeImg.setOnClickListener { callLoginBottomsheet() }
            binding.txtTryAgain.setOnClickListener { tryOTPAgain() }
            binding.btnManualVerification.setOnClickListener { requestWhatsappCode() }
            viewModel.onEventReceived(VerifyOtpViewModel.Event.OnCreateView(countryCode, phone))
            showNoOtpWarning()
            binding.inputOtp.afterTextChanged {
                if (binding.inputOtp.text.length == VerifyOtpActivity.OTP_LENGTH) {
                    verifyOtp(binding.inputOtp.text.toString())
                } else {
                    binding.otpErrorText.visibility = View.GONE
                }
            }
            val phoneNum = "+${countryCode?.replace("+","")}-$phone"

            val formattedMsg = getString(R.string.please_enter_otp, phoneNum)
            val stringBuilder = SpannableStringBuilder(formattedMsg)
            val boldStyle = StyleSpan(Typeface.BOLD)

            //bold WA atau SMS
            stringBuilder.setSpan(boldStyle, 32, 44, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
            binding.phoneNumber.text = stringBuilder
        } catch (e: Exception) {
            e.recordException()
        }
    }
    private fun setAutoDetectOTP() {
        try {
            autoDetectOTP = AutoDetectOTP(context)
            autoDetectOTP?.startSmsRetriver(object : AutoDetectOTP.SmsCallback {
                override fun connectionfailed() {
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_AUTO_DETECT_OTP, AnalyticsConst.STATUS_CONNECTION_FAIL, "")
                }

                override fun connectionSuccess(aVoid: Void?) {
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_AUTO_DETECT_OTP, AnalyticsConst.STATUS_CONNECTED, AutoDetectOTP.getHashCode(context))
                }

                override fun smsCallback(sms: String) {
                    try {
                        if (sms.contains("adalah") || sms.contains("BukuWarung")) {
                            val pattern: Pattern = Pattern.compile("(\\d{4})")
                            val matcher: Matcher = pattern.matcher(sms)
                            var otp = ""
                            if (matcher.find()) {
                                otp = matcher.group(0) // 4 digit number
                            }
//                            val otp = sms.substring(0, 5).trim { it <= ' ' }
                            AppAnalytics.trackEvent(AnalyticsConst.EVENT_AUTO_DETECT_OTP, AnalyticsConst.STATUS_SUCCESS, "")
                            binding.inputOtp.setText(otp)
                            autoDetect = true
                        }
                    } catch (e: Exception) {
                        e.recordException()
                    }
                }
            })
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is VerifyOtpViewModel.State.Error -> handleError(it.code, it.message)
                is VerifyOtpViewModel.State.ShowLoading -> showVerifying(it.isLoading)
                VerifyOtpViewModel.State.WrongOtp -> handleWrongOtp()
                VerifyOtpViewModel.State.FirebaseAuthError -> handleFirebaseAuthError()
                VerifyOtpViewModel.State.StartAfterVerifyOtp -> handleStartAfterVerifyOtp()
                VerifyOtpViewModel.State.SuccessSignInFirebase -> handleOnFirebaseSignIn()
                VerifyOtpViewModel.State.ProceedWithLogin -> {
                    MainActivity().sendAppsFlyerId()
                    listener?.onFinishOtp()
                    dismiss()
                }
                else -> {}
            }
        }
    }

    private fun trackRegistrationVerifyOtpAnalytics(status: String, detail: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.DETAIL, detail)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, entryPoint)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REGISTRATION_VERIFY_OTP, propBuilder)
    }
    private fun handleOnFirebaseSignIn() {
        if (autoVerify) {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_WHATSAPP_AUTO_LOGIN, AnalyticsConst.STATUS_COMPLETE, phone)
            trackRegistrationVerifyOtpAnalytics(AnalyticsConst.STATUS_SUCCESS, AnalyticsConst.DETAIL_WA_SUCCESS)
        } else {
            trackRegistrationVerifyOtpAnalytics(AnalyticsConst.STATUS_SUCCESS, AnalyticsConst.DETAIL_OTP_CORRECT)
        }
    }

    private fun handleStartAfterVerifyOtp() {
        trackRegistrationVerifyOtpAnalytics(AnalyticsConst.STATUS_RECEIVED_TOKEN, AnalyticsConst.DETAIL_ENTERED_OTP)
    }

    private fun handleFirebaseAuthError() {
        // If sign in fails, display a message to the user.
        onTimerFinish()
        binding.otpErrorText.visibility = View.VISIBLE
        binding.tryAgainContainer.visibility = View.GONE
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_INVALID_OTP, AnalyticsConst.STATUS_F_AUTH, phone)
        showVerifying(false)
    }

    private fun handleError(code: Int, message: String) {
        trackRegistrationVerifyOtpAnalytics(AnalyticsConst.STATUS_FAIL, AnalyticsConst.DETAIL_INVALID_OTP)
        if (code >= 500) {
            // server error
            binding.otpErrorText.text = getString(R.string.otp_error_server)
        } else {
            val stringBuilder = SpannableStringBuilder(getString(R.string.otp_error_wrong))
            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(view: View) {
                    tryOTPAgain()
                }
            }
            //bold WA atau SMS
            stringBuilder.setSpan(clickableSpan, 16, 25, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
            // OTP code error
            binding.otpErrorText.text = stringBuilder
            binding.otpErrorText.movementMethod = LinkMovementMethod.getInstance()
        }
        onTimerFinish()
        binding.tryAgainContainer.visibility = View.GONE
        binding.otpErrorText.visibility = View.VISIBLE
    }

    private fun handleWrongOtp() {
        trackRegistrationVerifyOtpAnalytics(AnalyticsConst.STATUS_FAIL, AnalyticsConst.DETAIL_INVALID_OTP)
        val message = getString(R.string.otp_error_wrong)
        val stringBuilder = SpannableStringBuilder(message)
        val clickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                tryOTPAgain()
            }
        }
        //bold WA atau SMS
        stringBuilder.setSpan(clickableSpan, 16, 25, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        // OTP code error
        binding.otpErrorText.text = stringBuilder
        binding.otpErrorText.movementMethod = LinkMovementMethod.getInstance()
        onTimerFinish()
        binding.tryAgainContainer.visibility = View.GONE
        binding.otpErrorText.visibility = View.VISIBLE
    }

    private fun tryOTPAgain() {
        trackRegistrationVerifyOtpAnalytics(AnalyticsConst.STATUS_CANCELLED, AnalyticsConst.DETAIL_CLICK_RETRY)
        callLoginBottomsheet()
    }
    
    private fun callLoginBottomsheet() {
        listener?.callLoginBottomsheet(true, entryPoint)
        dismiss()
    }

    override fun onDestroy() {
        super.onDestroy()
        cancelTimer()
    }

    private fun showNoOtpWarning() {
        binding.textCounter.visibility = View.VISIBLE
        cTimer = object : CountDownTimer(AppConst.OTP_WAIT_TIME * AppConst.ONE_SECOND, AppConst.ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                val counter = getString(R.string.retry_in) + " " + millisecondsToTime(millisUntilFinished)
                binding.textCounter.text = counter
            }

            override fun onFinish() {
                onTimerFinish()
            }
        }
        cTimer?.start()
    }

    private fun onTimerFinish() {
        cancelTimer()
        // we don't want to show both at the same time
        if (binding.otpErrorText.visibility == View.VISIBLE) {
            binding.tryAgainContainer.visibility = View.GONE
        } else {
            binding.tryAgainContainer.visibility = View.VISIBLE
        }
        binding.textCounter.visibility = View.GONE
//        binding.btnManualVerification.visibility = View.VISIBLE
    }

    //cancel timer
    private fun cancelTimer() {
        cTimer?.cancel()
    }

    private fun millisecondsToTime(milliseconds: Long): String {
        val minutes = milliseconds / 1000 / 60
        val seconds = milliseconds / 1000 % 60
        val secondsStr = seconds.toString()
        val secs = if (secondsStr.length >= 2) secondsStr.substring(0, 2)
        else "0$secondsStr"
        return "$minutes:$secs"
    }

    fun verifyOtp(otp: String) {
        binding.otpErrorText.visibility = View.GONE
        binding.otpErrorText.text = null
        if (!Utility.hasInternet()) {
            onTimerFinish()
            binding.tryAgainContainer.visibility = View.GONE
            binding.otpErrorText.visibility = View.VISIBLE
            val stringBuilder = SpannableStringBuilder()
            stringBuilder.append(getString(R.string.otp_no_internet))
            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(view: View) {
                    try {
                        verifyOtp(binding.inputOtp.text.toString())
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                }
            }
            stringBuilder.setSpan(clickableSpan, 75, stringBuilder.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
            // OTP code error
            binding.otpErrorText.text = stringBuilder
            binding.otpErrorText.movementMethod = LinkMovementMethod.getInstance()
            return
        }
        if (Utility.invalidOTPExceeded()) {
            onTimerFinish()
            binding.tryAgainContainer.visibility = View.GONE
            binding.otpErrorText.visibility = View.VISIBLE
            binding.otpErrorText.text = getString(R.string.otp_too_many)
            return
        }
        trackRegistrationVerifyOtpAnalytics(AnalyticsConst.STATUS_START, AnalyticsConst.DETAIL_ENTERED_OTP)
        val androidId: String? = try {
            Settings.Secure.getString(context?.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            e.recordException()
            null
        }
        InputUtils.hideKeyboard(context)
        viewModel.onEventReceived(VerifyOtpViewModel.Event.OnVerifyOtp(
                phone, countryCode.replace("+", ""), otp, androidId, "", "", "", false, autoVerify))
    }

    private fun showVerifying(show: Boolean) {
        if (show) {
            binding.loadingPanel.visibility = View.VISIBLE
            binding.bottomContainer.visibility = View.GONE
        } else {
            binding.loadingPanel.visibility = View.GONE
            binding.bottomContainer.visibility = View.VISIBLE
        }
    }

    private fun openWaBotHelp() {
        WhatsAppUtils.openWABotWithHelpText(context,getString(R.string.wa_help_text_general),Bundle())
    }

    private fun requestWhatsappCode() {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REQUEST_WHATSAPP_VERIFY, AnalyticsConst.STATUS_WA_LINK, phone)
        viewModel.onEventReceived(VerifyOtpViewModel.Event.DeleteFirestoreData(phone))
        if (!hasObserveFirestore) {
            viewModel.listenToFirestoreWaAuth().observe(this) { list ->
                for (dc in list) {
                    val doc: DocumentSnapshot = dc.document
                    var token: String? = null
                    var reqPhone: String? = null
                    try {
                        token = doc["token"] as String?
                        reqPhone = doc["phone"] as String?
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                    if (reqPhone != null && !token.isNullOrBlank() && reqPhone == phone) {
                        autoVerify = true
                        viewModel.onEventReceived(VerifyOtpViewModel.Event.AfterOtpVerify(token, phone, autoVerify))
                    }
                }
            }
            hasObserveFirestore = true
        }
        try {
            val sb = StringBuilder()
            sb.append(getString(R.string.please_verify, phone))
            try {
                val phoneNumberWithCountryCode = AppConst.WA_BW_NUMBER
                val url = String.format(AppConst.WA_INTENT_URL,
                        phoneNumberWithCountryCode, sb.toString())
                val packageName = ShareUtils.getWhatsAppPackageName(context, url, false)
                if (packageName == null || !packageName.equals("BOTH", ignoreCase = true)) {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    if (packageName != null) intent.setPackage(packageName)
                    startActivity(intent)
                } else {
                    // will be handled in dialog picker
                }
            } catch (e: ActivityNotFoundException) {
                NotificationUtils.alertToast(getString(R.string.whatsapp_not_installed))
            }
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_REQUEST_WHATSAPP_LOGIN)
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun provideAuthRemoteDataSource(): AuthRemoteDataSource {
        val builder: OkHttpClient.Builder = okhttpClientBuilder()
        builder.addInterceptor(OtpInterceptor())
        val okHttpClient = builder.build()
        val retrofit = Retrofit.Builder().baseUrl(AppConfigManager.getInstance().getOTPApi())
                .addCallAdapterFactory(ApiResponseCallAdapterFactory())
                .addConverterFactory(NullOnEmptyConverterFactory())
                .addConverterFactory(GsonConverterFactory.create(provideGson()))
                .client(okHttpClient)
                .build()
        return retrofit.create(AuthRemoteDataSource::class.java)
    }

    private fun provideLoginRemoteDataSource(): LoginRemoteDataSource {
        val builder: OkHttpClient.Builder = okhttpClientBuilder()
        val okHttpClient = builder.build()
        val retrofit = Retrofit.Builder().baseUrl(AppConfigManager.getInstance().getOTPApi())
                .addCallAdapterFactory(ApiResponseCallAdapterFactory())
                .addConverterFactory(NullOnEmptyConverterFactory())
                .addConverterFactory(GsonConverterFactory.create(provideGson()))
                .client(okHttpClient)
                .build()
        return retrofit.create(LoginRemoteDataSource::class.java)
    }

    private fun provideGson() = GsonBuilder().setLenient().create()

    private fun okhttpClientBuilder(): OkHttpClient.Builder {
        return OkHttpClient.Builder()
                .addInterceptor(HeadersInterceptor())
                .addInterceptor(provideLoggingInterceptor())
                .callTimeout(1, TimeUnit.MINUTES)
                .connectTimeout(1, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
    }

    private fun provideLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        }
    }
}
