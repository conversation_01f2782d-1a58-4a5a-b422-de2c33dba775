package com.bukuwarung.commonview.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bukuwarung.activities.BaseAndroidViewModel
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.wrapper.EventWrapper

class BukuTileViewModel: BaseViewModel() {

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state


    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    sealed class State {

    }

    sealed class Event {
    }

    fun onEventReceived(event: Event) {
        when (event) {

            else -> {}
        }
    }
}