package com.bukuwarung.commonview.adapter

import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.BuildConfig
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.homepage.data.BukuTileViewBottomSheetCoachMark
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.commonview.view.BukuTileView
import com.bukuwarung.databinding.PpobBottomSheetItemBinding
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil

class BukuTileBottomSheetItemAdapter(
    private val list: List<FragmentBodyBlock>,
    private val category: String,
    private val clickAction: (bodyBlock: BodyBlock?) -> Unit,
    private val showOnBoarding: (BukuTileViewBottomSheetCoachMark) -> Unit
) : RecyclerView.Adapter<BukuTileBottomSheetItemAdapter.BukuTileBottomSheetItemViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BukuTileBottomSheetItemViewHolder {
        val itemBinding =
            PpobBottomSheetItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BukuTileBottomSheetItemViewHolder(itemBinding, category, clickAction, showOnBoarding)
    }

    override fun onBindViewHolder(holder: BukuTileBottomSheetItemViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    override fun getItemCount(): Int = list.size

    class BukuTileBottomSheetItemViewHolder(
        private val binding: PpobBottomSheetItemBinding,
        private val category: String,
        private val clickAction: (bodyBlock: BodyBlock?) -> Unit,
        private val showOnBoarding: (BukuTileViewBottomSheetCoachMark) -> Unit,
    ) : RecyclerView.ViewHolder(binding.root) {

        private var bukuTileAdapter: BukuTileBottomSheetAdapter? = null
        private val context = binding.rvPpob.context

        fun bind(
            fragmentBodyBlockItem: FragmentBodyBlock,
            position: Int
        ) {
            with(binding) {
                tvPpobTitle.visibility =
                    fragmentBodyBlockItem.title.isNotNullOrBlank().asVisibility()
                tvPpobTitle.text = fragmentBodyBlockItem.title
                tvPpobSubTitle.visibility =
                    fragmentBodyBlockItem.subtitle.isNotNullOrBlank().asVisibility()
                tvPpobSubTitle.text = fragmentBodyBlockItem.subtitle
                rvPpob.visibility = fragmentBodyBlockItem.data?.isNotEmpty().asVisibility()
                val reminderFilteredList = if (PaymentPrefManager.getInstance().shouldShowRemindersOptions()) {
                    fragmentBodyBlockItem.data.orEmpty()
                } else {
                    fragmentBodyBlockItem.data?.filter{ !it?.analytics_name?.contains(BukuTileView.REMINDER, true).isTrue }.orEmpty()
                }
                val versionFilteredList = reminderFilteredList.filter{ it?.start_version.orNil <= BuildConfig.VERSION_CODE && (it?.end_version.orNil >= BuildConfig.VERSION_CODE || it?.end_version.orNil == -1) }
                rvPpob.apply {
                    bukuTileAdapter =
                        BukuTileBottomSheetAdapter(
                            category,
                            ArrayList(versionFilteredList),
                        ) { fragmentBody, _ ->
                            clickAction(fragmentBody)
                        }
                    layoutManager = GridLayoutManager(context, getNumberOfColumns())
                    adapter = bukuTileAdapter
                }
                rvPpob.isNestedScrollingEnabled = false
                fragmentBodyBlockItem.coachmarkInfo?.let {
                    showOnBoarding(BukuTileViewBottomSheetCoachMark(vwCoachmark, it, position))
                }

            }
        }

        private fun getNumberOfColumns(): Int {
            val displayMetrics: DisplayMetrics = context.resources.displayMetrics
            val dpWidth: Float = displayMetrics.widthPixels / displayMetrics.density
            return (dpWidth / 80).toInt()
        }
    }
}
