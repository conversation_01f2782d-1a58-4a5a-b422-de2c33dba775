package com.bukuwarung.commonview.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.databinding.ItemBukuTileSeeMoreBinding
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.android.synthetic.main.item_buku_tile.view.*

class BukuTileViewAdapter(private var numberOfRows: Int, private var isSeeMoreSection: Boolean = false, private var bukuTileContent: List<BodyBlock?>,
      private val category: String , val getOnClickData: (BodyBlock?, String, Int) -> Unit): RecyclerView.Adapter<RecyclerView.ViewHolder>()  {

    companion object {
        const val TYPE_ITEM = 0
        const val TYPE_SEE_MORE = 1
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when(viewType) {
            TYPE_ITEM -> {
                BukuTileViewHolder(parent).listen { position, type ->
                    if (position >= 0 && position < bukuTileContent.size) {
                        getOnClickData(bukuTileContent[position], category, position)
                    } else {
                        FirebaseCrashlytics.getInstance().log(
                            "BukuTileItemClick:category = $category,  position = $position, tileContent = $bukuTileContent"
                        )
                    }
                }
            }
            else -> {
                BukuSeeMoreViewHolder(ItemBukuTileSeeMoreBinding.inflate(
                    layoutInflater,
                    parent,
                    false
                )).listen { position, type ->
                    getOnClickData(bukuTileContent[position - 1], category, position)
                }
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (isSeeMoreSection && position == numberOfRows - 1) {
            TYPE_SEE_MORE
        } else {
            TYPE_ITEM
        }
    }

    override fun getItemCount(): Int {
        return if (isSeeMoreSection) {
            numberOfRows
        } else {
            bukuTileContent.size
        }
    }

    fun refreshData(newContents: List<BodyBlock?>) {
        bukuTileContent = newContents
        notifyDataSetChanged()
    }

    inner class BukuSeeMoreViewHolder(private val binding: ItemBukuTileSeeMoreBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            if (category == "ppob") {
                binding.othersImg.setImageResource(R.drawable.ic_other_ppob)
            } else {
                binding.othersImg.setImageResource(R.drawable.ic_see_more_grey)
            }
        }
    }

    inner class BukuTileViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {

        private val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()

        val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }

        constructor(parent: ViewGroup) : this(
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_buku_tile,
                parent, false
            )
        )

        fun bind(fragmentBody: BodyBlock?) {
            with(itemView) {
                Glide.with(context).load(fragmentBody?.icon).placeholder(shimmerDrawable)
                    .centerInside().into(iv_tile_image)
                tv_tile_name.text = fragmentBody?.display_name

                val shouldShowPromoTags = RemoteConfigUtils.NewHomePage.shouldShowPromoTags()
                val endTime =
                    DateTimeUtilsKt.getDaysDifference(fragmentBody?.is_new_end_time, true)
                        .times(24).times(60).times(60).times(1000)
                if (shouldShowPromoTags) {
                    if (fragmentBody?.is_promo!! || (fragmentBody?.is_new!! && fragmentBody?.is_promo)) {
                        tv_new.hideView()
                        tv_promo.showView()
                    } else {
                        tv_promo.hideView()
                    }

                    if (!fragmentBody?.is_promo && fragmentBody.is_new!! && (endTime.orNil > 0L)) {
                        tv_new.showView()
                    } else {
                        tv_new.hideView()
                    }
                }
                else {
                    tv_promo.hideView()
                    if (fragmentBody?.is_new!! && (endTime.orNil > 0L)) {
                        tv_new.showView()
                    } else {
                        tv_new.hideView()
                    }
                }
                if (fragmentBody.coming_soon) {
                    tv_coming_soon.showView()
                    cv_tile_image.setCardBackgroundColor(ContextCompat.getColor(
                        context,
                        R.color.coming_soon
                    ))
                } else {
                    tv_coming_soon.hideView()
                }
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        return if (isSeeMoreSection) {
            if (position == numberOfRows - 1) {
                (holder as BukuTileViewAdapter.BukuSeeMoreViewHolder).bind()
            } else {
                (holder as BukuTileViewAdapter.BukuTileViewHolder).bind(bukuTileContent[position])
            }
        } else {
                 (holder as BukuTileViewAdapter.BukuTileViewHolder).bind(bukuTileContent[position])
        }
    }


}