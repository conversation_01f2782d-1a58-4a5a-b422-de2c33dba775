package com.bukuwarung.commonview.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isFalseOrNull
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.listen
import com.bukuwarung.utils.showView
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable
import kotlinx.android.synthetic.main.item_buku_tile.view.*

class BukuTileBottomSheetAdapter(private val category: String, private var bukuTileContent: ArrayList<BodyBlock?>, val getOnClickData: (BodyBlock?, String) -> Unit): RecyclerView.Adapter<BukuTileBottomSheetAdapter.BukuTileViewHolder>()  {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BukuTileBottomSheetAdapter.BukuTileViewHolder {
        return BukuTileViewHolder(parent).listen { position, type ->
            getOnClickData(bukuTileContent[position], category)
        }
    }

    override fun getItemCount(): Int {
        return bukuTileContent.size
    }

    inner class BukuTileViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {

        private val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()

        val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }

        constructor(parent: ViewGroup) : this(
            LayoutInflater.from(parent.context).inflate(
                R.layout.item_buku_tile,
                parent, false
            )
        )

        fun bind(fragmentBody: BodyBlock?) {
            with(itemView) {
                Glide.with(context).load(fragmentBody?.icon).placeholder(shimmerDrawable)
                    .centerInside().into(iv_tile_image)
                tv_tile_name.text = fragmentBody?.display_name


                val shouldShowPromoTags = RemoteConfigUtils.NewHomePage.shouldShowPromoTags()

                if (shouldShowPromoTags) {
                    if (fragmentBody?.is_new.isTrue && fragmentBody?.is_promo.isTrue) {
                        tv_new.hideView()
                        tv_promo.showView()
                    } else {
                        tv_promo.hideView()
                    }

                    if (fragmentBody?.is_promo.isFalseOrNull && fragmentBody?.is_new.isTrue) {
                        tv_new.showView()
                    } else {
                        tv_new.hideView()
                    }
                }
                else {
                    tv_promo.hideView()
                    if (fragmentBody?.is_new.isTrue) {
                        tv_new.showView()
                    } else {
                        tv_new.hideView()
                    }
                }

                if (fragmentBody?.coming_soon.isTrue) {
                    tv_coming_soon.showView()
                    cv_tile_image.setCardBackgroundColor(
                        ContextCompat.getColor(
                        context,
                        R.color.coming_soon
                    ))
                } else {
                    tv_coming_soon.hideView()
                }
            }
        }
    }

    override fun onBindViewHolder(holder: BukuTileViewHolder, position: Int) {
        holder.bind(bukuTileContent[position])
    }

}