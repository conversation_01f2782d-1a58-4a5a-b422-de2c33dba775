package com.bukuwarung.referral.model

import com.google.gson.annotations.SerializedName

data class ReferralDataResponsePayload(
    @SerializedName("referredAt")
    val referredAt: String="",
    @SerializedName("referredByCode")
    val referredByCode: String="",
    @SerializedName("referredByUserId")
    val referredByUserId: String="",
    @SerializedName("userId")
    val userId: String="",
    @SerializedName("userReferralCode")
    val userReferralCode: String="",
    @SerializedName("userReferralDeeplink")
    val userReferralDeeplink: String="",
    @SerializedName("error")
    val error: Boolean=false,
    @SerializedName("errorTypeCode")
    val errorTypeCode: Int
)
