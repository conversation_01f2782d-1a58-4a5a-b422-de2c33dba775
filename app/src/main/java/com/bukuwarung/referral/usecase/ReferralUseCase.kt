package com.bukuwarung.referral.usecase

import com.bukuwarung.referral.model.ReferralDataResponsePayload
import com.bukuwarung.referral.model.ReferralDataPutRequest
import com.bukuwarung.referral.model.ReferralDataPostRequestPayload
import com.bukuwarung.referral.repository.ReferralRepository
import javax.inject.Inject

class ReferralUseCase @Inject constructor(
    private val referralRepository: ReferralRepository){

    suspend fun getReferralData():ReferralDataResponsePayload? = referralRepository.getReferralData()

    suspend fun putReferralData(senderRefData: ReferralDataPutRequest): ReferralDataResponsePayload?= referralRepository.putReferralData(senderRefData)

    suspend fun postReferralData(newUserData: ReferralDataPostRequestPayload?): ReferralDataResponsePayload? = referralRepository.postReferralData(newUserData)
}