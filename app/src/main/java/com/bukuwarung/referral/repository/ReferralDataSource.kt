package com.bukuwarung.referral.repository

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.referral.model.*
import io.reactivex.Observable
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT

interface ReferralDataSource {

    @GET("loyalty/api/v1/referral")
    suspend fun getReferralData(): ApiResponse<ReferralDataResponse>

    @PUT("loyalty/api/v1/referral")
    suspend fun putReferralData(@Body params: ReferralDataPutRequest): ApiResponse<ReferralDataResponse>

    @POST("ac/api/v2/app/upgrade")
    suspend fun postUpgradeReferralData(@Body params: ReferralDataPostUpgradeRequestPayload?): ApiResponse<ReferralUpdateResponse>

    @POST("loyalty/api/v1/referral")
    suspend fun postReferralData(@Body params: ReferralDataPostRequestPayload?): ApiResponse<ReferralDataResponse>
}