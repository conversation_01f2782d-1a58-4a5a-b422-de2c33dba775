package com.bukuwarung.referral.repository

import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.referral.model.ReferralDataResponsePayload
import com.bukuwarung.referral.model.ReferralDataPutRequest
import com.bukuwarung.referral.model.ReferralDataPostRequestPayload
import com.bukuwarung.utils.recordException
import javax.inject.Inject


class ReferralRepository @Inject constructor(private val referralDataSource: ReferralDataSource) {

    suspend fun getReferralData(): ReferralDataResponsePayload? {
        return try {
            when (val response = referralDataSource.getReferralData()) {
                is ApiSuccessResponse -> response.body.data
                is ApiEmptyResponse -> null
                is ApiErrorResponse -> null
            }
        } catch (ex: Exception) {
            ex.recordException()
            return null
        }
    }

    suspend fun putReferralData(senderRefData: ReferralDataPutRequest): ReferralDataResponsePayload? {
        return try{
            when(val response = referralDataSource.putReferralData(senderRefData)){
                is ApiSuccessResponse -> response.body.data
                is ApiEmptyResponse -> null
                is ApiErrorResponse -> ReferralDataResponsePayload(error = true, errorTypeCode = response.errorTypeCode)
            }
        }
        catch (ex: Exception){
            ex.recordException()
            return null
        }

    }

    suspend fun postReferralData(newUserData: ReferralDataPostRequestPayload?): ReferralDataResponsePayload? {
        return try {
            when (val response = referralDataSource.postReferralData(newUserData)) {
                is ApiSuccessResponse -> response.body.data
                is ApiEmptyResponse -> null
                is ApiErrorResponse -> null
            }
        } catch (ex: Exception) {
            ex.recordException()
            return null;
        }
    }

}