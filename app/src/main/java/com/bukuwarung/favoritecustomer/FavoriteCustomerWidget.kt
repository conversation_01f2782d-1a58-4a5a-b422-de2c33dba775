package com.bukuwarung.favoritecustomer

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.databinding.FavoriteCustomerWidgetBinding
import com.bukuwarung.utils.dp
import com.bukuwarung.utils.getColorCompat
import com.google.android.material.chip.Chip

class FavoriteCustomerWidget @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private val binding = FavoriteCustomerWidgetBinding.inflate(LayoutInflater.from(context), this, true)

    interface FavoriteCustomerCallback {
        fun onFavoriteCustomerSelected(contact: Contact?)
    }

    private var favoriteCustomerCallback: FavoriteCustomerCallback? = null
    private var contacts = listOf<Contact>()

    fun showFavoriteCustomers(customers_: List<Contact>, favoriteCustomerCallback_: FavoriteCustomerCallback) {
        favoriteCustomerCallback = favoriteCustomerCallback_
        contacts = customers_

        binding.chipGroup.removeAllViews()
        contacts.map {
            Chip(context).apply {
                text = it.name
                maxWidth = 116.dp
                ellipsize = TextUtils.TruncateAt.END
                maxLines = 1

                setChipBackgroundColorResource(R.color.blue_background)
                setTextColor(context.getColorCompat(R.color.blue_60))
                setOnClickListener { _ -> favoriteCustomerCallback?.onFavoriteCustomerSelected(it) }
            }
        }.forEach { binding.chipGroup.addView(it) }
    }
}