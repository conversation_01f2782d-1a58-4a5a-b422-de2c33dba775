package com.bukuwarung.favoritecustomer

import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.recordException

class FavoriteCustomerUseCase(private val transactionRepository: TransactionRepository, private val sessionManager: SessionManager) {
    /**
     * The order of suggestion will be based on the frequency of Utang [Highest frequency first and so on]
     */

    fun getFavoriteContacts(limit: Int): List<Contact> {
        return try {
            transactionRepository.getMostTransactingCustomers(sessionManager.businessId, limit)
        } catch (ex: Exception) {
            ex.recordException()
            listOf<CustomerEntity>()
        }.map { Contact(it.name, it.phone, it.image, it.customerId) }
    }
}