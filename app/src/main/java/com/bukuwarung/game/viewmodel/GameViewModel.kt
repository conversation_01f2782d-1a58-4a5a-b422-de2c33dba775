package com.bukuwarung.game.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.game.model.GameData
import com.bukuwarung.game.model.LeaderboardWhitelistedResponse
import com.bukuwarung.game.usecase.GameUseCase
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.isTrue
import kotlinx.coroutines.launch
import javax.inject.Inject

class GameViewModel @Inject constructor(private val gameUseCase: GameUseCase) : BaseViewModel() {


    private var _gameData = MutableLiveData<GameData?>()
    val gameData: LiveData<GameData?> = _gameData

    private val _userWhitelistedForLeaderboard = MutableLiveData<LeaderboardWhitelistedResponse?>()
    val userWhitelistedForLeaderboard: LiveData<LeaderboardWhitelistedResponse?>
        get() = _userWhitelistedForLeaderboard

    private var _gameErrorMessage = MutableLiveData<String>()
    val gameErrorMessage: LiveData<String> = _gameErrorMessage

    fun getGameProgressData(gameRuleName: String) =
        viewModelScope.launch {
            when (val response = gameUseCase.getGameProgressData(gameRuleName)) {
                is ApiSuccessResponse -> {
                    _gameData.let {
                        _gameData.value = response.body?.data
                    }
                    val gameProgress = response.body?.data?.gameProgress?.getOrNull(0)
                    SessionManager.getInstance().gameRuleName =
                        if (gameProgress?.isSubscriptionActive.isTrue && gameProgress?.status.equals(PaymentConst.GAME_ACTIVE)) {
                            response.body?.data?.game?.gameRuleName
                        } else null
                }
                is ApiErrorResponse -> {
                    _gameErrorMessage.apply {
                        value = response.errorMessage
                    }
                }

                else -> {}
            }
        }

    fun checkForWhitelisting(campaignName: String) = viewModelScope.launch {
        //checkWhitelist should be sent as true. In case of null for this value backend is sending top 10 users in the leaderboard.
        when (val response = gameUseCase.userWhitelistedForLeaderboard(campaignName, true)) {
            is ApiSuccessResponse -> {
                _userWhitelistedForLeaderboard.value = response.body
            }
            else -> {
                _userWhitelistedForLeaderboard.value = LeaderboardWhitelistedResponse(false, null)
            }
        }
    }
}