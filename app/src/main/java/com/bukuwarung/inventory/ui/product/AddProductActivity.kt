package com.bukuwarung.inventory.ui.product

import android.os.Bundle
import android.widget.ImageView
import androidx.fragment.app.FragmentActivity
import com.bukuwarung.R
import com.bukuwarung.utils.InputUtils
import dagger.android.AndroidInjection
import kotlinx.android.synthetic.main.add_product_activity.*

class AddProductActivity : FragmentActivity() {

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        AndroidInjection.inject(this);
        setContentView(R.layout.add_product_activity)

        iv_close.setOnClickListener {
            InputUtils.hideKeyboard(this)
            onBackPressed()
        }
        findViewById<ImageView>(R.id.back).setOnClickListener {
            InputUtils.hideKeyboard(this)
            onBackPressed()
        }

        supportFragmentManager.beginTransaction().add(R.id.container,
                AddProductFragment.instance(intent.extras?.getString(BOOK_ID) ?: "")).commit()

    }

    companion object {
        const val BOOK_ID = "book_id"
        @JvmStatic
        val PRODUCT_ADDED_SUCCESS = 100

    }

}