package com.bukuwarung.inventory.ui

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.productcategory.view.DynamicProductUIModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.databinding.EmptyItemBinding
import com.bukuwarung.databinding.InventoryProductItemBinding
import com.bukuwarung.databinding.ItemProductCatalogBannerBinding
import com.bukuwarung.inventory.getDisplayPrice
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import java.util.*

class InventoryProductAdapter(
    private val clickEvent : (ClickEvent) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val data = mutableListOf<DynamicProductUIModel>()
    private var shouldShowCatalogBanner = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            DynamicProductUIModel.PRODUCT -> ProductViewHolder(InventoryProductItemBinding.inflate(layoutInflater))
            DynamicProductUIModel.INFO -> CatalogBannerViewHolder(ItemProductCatalogBannerBinding.inflate(layoutInflater))
            else -> EmptyViewHolder(EmptyItemBinding.inflate(layoutInflater))
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
       when(getItemViewType(position)) {
           DynamicProductUIModel.PRODUCT -> (holder as ProductViewHolder).bind(data[position].productEntity, clickEvent)
           DynamicProductUIModel.INFO -> (holder as CatalogBannerViewHolder).bind(clickEvent)
           EMPTY_ITEM -> {}
       }
    }

    override fun getItemCount(): Int {
        return data.size + 1
    }

    override fun getItemViewType(position: Int): Int {
        return if (position > data.size - 1)  EMPTY_ITEM else data[position].type
    }

    class ProductViewHolder(val binding: InventoryProductItemBinding) : RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n")
        fun bind(product: ProductEntity?, clickEvent : (ClickEvent) -> Unit) {
            product ?: return

            binding.apply {
                try {
                    tvProductName.text = product.name.capitalize()
                    if (product.name.isNotEmpty()) {
                        tvInitial.text = product.name[0].toString().uppercase(Locale.getDefault())
                    }
                    val sellingAmount = product.getDisplayPrice().toDouble()
                    sellingPriceTxt.text = Utility.formatCurrency(sellingAmount)
                    tvStock.apply {
                        text = this.context.getString(R.string.stock_placeholder,
                            Utility.getRoundedOffPrice(product.stock))
                        val color = if (product.stock > product.minimumStock) {
                            R.color.black_80
                        } else {
                            R.color.out_red
                        }

                        setTextColor(this.context.getColorCompat(color))
//                        tvMeasurement.setTextColor(this.context.getColorCompat(color))
                    }
                    if(product.favourite) {
                        binding.labelFavourite.showView()
                    } else {
                        binding.labelFavourite.hideView()
                    }
                    if (product.trackInventory == AppConst.INVENTORY_TRACKING_DISABLED) {
                        tvStock.visibility = View.INVISIBLE
//                        tvMeasurement.visibility = View.INVISIBLE
                        btnManageStock.visibility = View.INVISIBLE
                    } else {
                        tvStock.visibility = View.VISIBLE
                        tvMeasurement.visibility = View.VISIBLE
                        tvSubtext.visibility = View.GONE
                        btnManageStock.visibility = View.VISIBLE
                    }
                    tvMeasurement.text = "/" + product.measurementName
                    root.setOnClickListener {
                        clickEvent(ClickEvent.Item(product.productId))
                    }

                    if (product.isImportedFromCatalog && !product.hasUpdatedPrice){
                        btnManageStock.visibility = View.INVISIBLE
                        btnManagePrice.visibility = View.VISIBLE
                    }else{
                        btnManagePrice.visibility = View.GONE
                    }

                    btnManageStock.setOnClickListener { clickEvent(ClickEvent.Edit(product.productId)) }
                    btnManagePrice.setOnClickListener { clickEvent(ClickEvent.Price(product)) }
                } catch (e: Exception) {
                    e.printStackTrace()
                 }
            }
        }

    }
    class EmptyViewHolder(val binding: EmptyItemBinding) : RecyclerView.ViewHolder(binding.root) {

    }
    class CatalogBannerViewHolder(val binding: ItemProductCatalogBannerBinding) : RecyclerView.ViewHolder(binding.root){
        fun bind(callback: (ClickEvent) -> Unit){
            binding.btnCatalog.setOnClickListener { callback(ClickEvent.Banner) }
        }
    }

    fun updateData(newProductList: List<ProductEntity>) {
        data.apply {
            clear()
            if (shouldShowCatalogBanner) add(DynamicProductUIModel(DynamicProductUIModel.INFO, null))
            addAll(newProductList.map { DynamicProductUIModel(DynamicProductUIModel.PRODUCT, it) })
        }

        notifyDataSetChanged()

    }

    fun setCatalogBannerVisibility(shouldBeShown: Boolean){
        if (shouldShowCatalogBanner == shouldBeShown) return

        shouldShowCatalogBanner = shouldBeShown

        if (shouldShowCatalogBanner) {
            data.add(0, DynamicProductUIModel(DynamicProductUIModel.INFO, null))
        }else{
            data.removeIf { it.type == DynamicProductUIModel.INFO }
        }

        notifyDataSetChanged()
    }

    sealed class ClickEvent{
        data class Item(val productId: String) : ClickEvent()
        data class Edit(val productId: String) : ClickEvent()
        data class Price(val product: ProductEntity) : ClickEvent()
        object Banner : ClickEvent()
    }

    companion object{
        private const val EMPTY_ITEM = 10
    }

}