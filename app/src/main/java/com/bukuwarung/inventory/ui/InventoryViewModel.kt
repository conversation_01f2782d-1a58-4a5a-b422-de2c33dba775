package com.bukuwarung.inventory.ui

import androidx.lifecycle.*
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.catalogproduct.viewmodel.ProductCatalogUseCase
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.recordException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject

class InventoryViewModel @Inject constructor(
    private val productInventory: ProductInventory,
    private val backgroundDispatcher: CoroutineDispatcher,
    private val businessRepository: BusinessRepository,
    private val catalogUseCase: ProductCatalogUseCase,
    private val sessionManager: SessionManager,
    private val productCategoryUseCase: ProductCategoryUseCase
) : BaseViewModel() {
    private val inventoryStatus = MutableLiveData<InventoryEvent>()
    val observeInventory: LiveData<InventoryEvent> = inventoryStatus

    private var productList: List<ProductEntity> = listOf()
    private var runningOutProductList: List<ProductEntity> = listOf()
    private var showOnlyRunningOutStock = false

    private val filteredList = MutableLiveData<ProductFilterEvent>()
    val observeFilteredList: LiveData<ProductFilterEvent> = filteredList

    private var isDataAvailable = false
    private var toolTipDisplayed = false
    private var productSort: ProductSortEvent = ProductSortEvent.AscendingName

    private val categoryIdLiveData = MutableLiveData<String?>()
    val productCategoryObservable = productCategoryUseCase.getObservableCategories()

    private val categorizedProduct = categoryIdLiveData.switchMap { categoryId ->
        productCategoryUseCase.getCategoryWithProductsObservable(categoryId)
    }

    private val allProducts = productInventory.getAllProducts()
    val productListStatus = categorizedProduct.switchMap { categoryWithProducts ->
        isDataAvailable = true
        checkProductCatalogEligibility()

       if (categoryWithProducts == null){
           allProducts.map {
                productList = it
                inventoryStatus.value = InventoryEvent.EnableExtraFeature(it.size)
                ProductStatusEvent.UpdateProductList(sortData(it), !showOnlyRunningOutStock, productList.isEmpty(), false)
            }
        }else{
            val categorizedId = categoryWithProducts.products.map { it.productId }
           allProducts.map {products->
               productList = products.filter { categorizedId.contains(it.productId) }
               inventoryStatus.value = InventoryEvent.EnableExtraFeature(productList.size)
               ProductStatusEvent.UpdateProductList(sortData(productList), !showOnlyRunningOutStock, productList.isEmpty(), false)
            }
        }
    }

    private val runningOutProducts = productInventory.getAllRunningOutProducts()
    val runningOutProductListStatus = categorizedProduct.switchMap { categoryWithProducts ->
        if (categoryWithProducts == null){
            runningOutProducts.map {
                runningOutProductList = it
                ProductStatusEvent.UpdateLowProductList(sortData(it), !showOnlyRunningOutStock, runningOutProductList.isEmpty(), false)
            }
        }else{
            val categorizedId = categoryWithProducts.products.map { it.productId }
            runningOutProducts.map { products->
                runningOutProductList =  products.filter { categorizedId.contains(it.productId) }
                ProductStatusEvent.UpdateLowProductList(sortData(runningOutProductList), !showOnlyRunningOutStock, runningOutProductList.isEmpty(), false)
            }
        }
    }

    fun searchQueryTextChange(query: String?) {
        if (query != null && query.isNotEmpty()) {
            if(!showOnlyRunningOutStock) {
                val list = filterList(productList, query)
                filteredList.value = ProductFilterEvent.UpdateProductList(list, list.isEmpty())
            } else {
                val list = filterList(runningOutProductList, query)
                filteredList.value = ProductFilterEvent.UpdateProductList(list, list.isEmpty())
            }
        } else {
            if(!showOnlyRunningOutStock) {
                filteredList.value = ProductFilterEvent.UpdateProductList(productList, productList.isEmpty())
            } else {
                filteredList.value = ProductFilterEvent.UpdateProductList(runningOutProductList, productList.isEmpty())
            }
        }
    }

    fun onProductClicked(id: String) {
        val productEntity = productList.find { it.productId == id }
        if (productEntity != null) {
            inventoryStatus.value = InventoryEvent.OpenUpdateProductScreen(
                id, productEntity.stock,
                productEntity.stock <= productEntity.minimumStock,
            productEntity.trackInventory)
        }
    }

    fun onEditStockClicked(id: String) {
        val productEntity = productList.find { it.productId == id }
        if (productEntity != null) {
            inventoryStatus.value = InventoryEvent.OpenUpdateProductStock(id)
        }
    }

    fun onManagePriceClicked(id: String){
        val productEntity = productList.find { it.productId == id } ?: return

        // will affect for products imported from catalog
        // if user has updated any price of this product
        // button "Atur Harga" will be hidden in [InventoryProductAdapter.ProductViewHolder]
        // {@link package com.bukuwarung.inventory.ui.Inventory.ProductAdapter.class#member label}
        productEntity.hasUpdatedPrice = true
        productInventory.updateProduct(productEntity)
        inventoryStatus.value = InventoryEvent.OpenPriceEdit(id, productEntity.unitPrice)
    }

    private fun filterList(products: List<ProductEntity>, query: String) =
            products.filter {
                it.name.contains(query, true)
            }

    fun onAddProductClicked() {
        inventoryStatus.value = InventoryEvent.OpenAddProductScreen
    }

    fun init(showOnlyRunningOutStock: Boolean = false) {
        this.showOnlyRunningOutStock = showOnlyRunningOutStock
        categoryIdLiveData.value = null
        toolTipDisplayed = OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTOR_PRODUCT_STOCK_FIRST_TIME_USER)
    }

    fun sortProduct(sortEvent: ProductSortEvent) {
        productSort = sortEvent
        categoryIdLiveData.value = categoryIdLiveData.value
    }

    fun getCurrentSortOption() = productSort

    fun isDataAvailableReturnSize(): Int? {
        if(isDataAvailable) {
            return productList.size
        }
        return null
    }

    fun onUiFocused() {
        if(!showOnlyRunningOutStock && productList.isEmpty()) {
            inventoryStatus.value = InventoryEvent.ShowEmptyUI(allProducts = true, showToolTip = !toolTipDisplayed)
        } else if(productList.isEmpty()) {
            inventoryStatus.value = InventoryEvent.ShowEmptyUI(allProducts = false, showToolTip = false)
        }

        checkProductCatalogEligibility()
    }
    fun tootTipDismissed(id: String) {
        toolTipDisplayed = true
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
    }
    fun tootTipActionButtonClicked(id: String) {
        toolTipDisplayed = true
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
        onAddProductClicked()
    }

    fun createNewProductCategory(categoryName: String) = viewModelScope.launch {
        if (categoryName.isNullOrBlank()) return@launch
        try {
            productCategoryUseCase.createNewCategory(categoryName)
        }catch (ex: Exception){
            ex.recordException()
            inventoryStatus.value = InventoryEvent.OnCategoryNameAlreadyExist
        }
    }

    fun updateProductCategoryId(categoryId: String?){
        productSort = ProductSortEvent.AscendingName
        categoryIdLiveData.value = categoryId
    }

    private fun sortData(list: List<ProductEntity>): List<ProductEntity> {
        val favouriteProducts = mutableListOf<ProductEntity>()
        val nonFavouriteProducts = mutableListOf<ProductEntity>()
        for(posItem in list) {
            if (posItem.favourite) {
                favouriteProducts.add(posItem)
            } else {
                nonFavouriteProducts.add(posItem)
            }
        }
        return when (productSort) {
            ProductSortEvent.AscendingName -> {
                val sortedfavouriteProducts = favouriteProducts.sortedWith(compareBy(String.CASE_INSENSITIVE_ORDER) { product -> product.name } )
                val sortedNonFavouriteProducts = nonFavouriteProducts.sortedWith(compareBy(String.CASE_INSENSITIVE_ORDER) { product -> product.name } )
                val productToPopulate = mutableListOf<ProductEntity>()
                productToPopulate.addAll(sortedfavouriteProducts)
                productToPopulate.addAll(sortedNonFavouriteProducts)
                productToPopulate
            }
            ProductSortEvent.DescendingName -> {
                val sortedfavouriteProducts = favouriteProducts.sortedWith(compareByDescending(String.CASE_INSENSITIVE_ORDER) { product -> product.name } )
                val sortedNonFavouriteProducts = nonFavouriteProducts.sortedWith(compareByDescending(String.CASE_INSENSITIVE_ORDER) { product -> product.name } )
                val productToPopulate = mutableListOf<ProductEntity>()
                productToPopulate.addAll(sortedfavouriteProducts)
                productToPopulate.addAll(sortedNonFavouriteProducts)
                productToPopulate
            }
            ProductSortEvent.MostStock -> {
                val productToPopulate = mutableListOf<ProductEntity>()
                productToPopulate.addAll(favouriteProducts)
                productToPopulate.addAll(nonFavouriteProducts)
                productToPopulate.sortedByDescending { product -> product.stock }
            }
            ProductSortEvent.LeastStock -> {
                val productToPopulate = mutableListOf<ProductEntity>()
                productToPopulate.addAll(favouriteProducts)
                productToPopulate.addAll(nonFavouriteProducts)
                productToPopulate.sortedBy { product -> product.stock }
            }
        }
    }

    fun getCatalogCategories() = catalogUseCase.getCatalogCategories()

     private fun checkProductCatalogEligibility() = viewModelScope.launch{
        val eligibleBizzIds = RemoteConfigUtils.ProductCatalog.getEligibleBusinessTypes()

        val myBusinessType = businessRepository.getBusinessByIdSync(sessionManager.businessId).bookType
        val isMyBusinessEligible = myBusinessType in eligibleBizzIds

        inventoryStatus.postValue(InventoryEvent.CatalogFeatureEligibility(isMyBusinessEligible))
    }

    sealed class InventoryEvent {
        object StartLoading : InventoryEvent()
        object StopLoading : InventoryEvent()
        data class EnableExtraFeature(val productCount: Int) : InventoryEvent()
        data class ShowError(val error: String) : InventoryEvent()
        data class OpenUpdateProductScreen(
            val id: String,
            val totalStock: Double,
            val criticalStock: Boolean,
             val trackInventory: Int
        ) : InventoryEvent()
        data class OpenUpdateProductStock(val id: String) : InventoryEvent()
        object OpenAddProductScreen: InventoryEvent()
        data class ShowEmptyUI(val allProducts: Boolean, val showToolTip: Boolean): InventoryEvent()
        object ShowNoRecordFound: InventoryEvent()
        data class OpenPriceEdit(val id: String, val unitPrice: Double) : InventoryEvent()
        data class CatalogFeatureEligibility(val isEligible: Boolean) : InventoryEvent()
        object OnCategoryNameAlreadyExist : InventoryEvent()
    }

    sealed class ProductStatusEvent {
        data class UpdateProductList(val productList: List<ProductEntity>, val allProducts: Boolean, val showEmptyScreen: Boolean, val showToolTip: Boolean) : ProductStatusEvent()
        data class UpdateLowProductList(val productList: List<ProductEntity>, val allProducts: Boolean, val showEmptyScreen: Boolean, val showToolTip: Boolean) : ProductStatusEvent()
    }

    sealed class ProductFilterEvent {
        data class UpdateProductList(val productList: List<ProductEntity>, val showNoRecordFound: Boolean) : ProductFilterEvent()
        data class UpdateLowProductList(val productList: List<ProductEntity>,  val showNoRecordFound: Boolean) : ProductFilterEvent()
    }

    sealed class ProductSortEvent {
        object AscendingName : ProductSortEvent()
        object DescendingName : ProductSortEvent()
        object MostStock : ProductSortEvent()
        object LeastStock : ProductSortEvent()
    }
}