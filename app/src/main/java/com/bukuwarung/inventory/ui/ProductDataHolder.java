package com.bukuwarung.inventory.ui;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.ProductEntity;

public class ProductDataHolder extends DataHolder {

    private final ProductEntity productEntity;

    public ProductDataHolder(ProductEntity productEntity) {
        this.productEntity = productEntity;
        setTag(Tag.PRODUCT_ITEM_VIEW);
    }

    public final ProductEntity getProductEntity() {
        return this.productEntity;
    }

    public String getName() {
        return this.productEntity.name;
    }
}