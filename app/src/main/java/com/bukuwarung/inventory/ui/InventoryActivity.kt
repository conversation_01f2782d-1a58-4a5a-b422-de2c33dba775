package com.bukuwarung.inventory.ui

import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.ActivityStockBinding
import com.bukuwarung.inventory.ui.InventoryHomeFragment.Companion.instance
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.toBoolean

class InventoryActivity: BaseActivity() {

    lateinit var binding: ActivityStockBinding

    lateinit var stockFragment: InventoryHomeFragment

    var from:String? = null

    override fun setViewBinding() {
        val view = ActivityStockBinding.inflate(layoutInflater)
        setContentView(view.root)
    }

    override fun setupView() {

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

        val shouldOpenProductCatalog = intent.getStringExtra(InventoryHomeFragment.OPEN_CATALOG_BOTTOM_SHEET).toBoolean()
        val tab = intent.getIntExtra("tab", 0)

        stockFragment = instance(position = tab, from = from, openCatalog = shouldOpenProductCatalog) {
            super.onBackPressed()
            Unit
        }

        val transaction: FragmentTransaction?
        transaction = supportFragmentManager?.beginTransaction()

        transaction?.add(R.id.frame_stock, stockFragment)?.commit()
    }

    override fun subscribeState() {
    }


}