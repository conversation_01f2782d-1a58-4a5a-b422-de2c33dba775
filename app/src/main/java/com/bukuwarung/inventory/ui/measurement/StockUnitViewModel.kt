package com.bukuwarung.inventory.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.inventory.usecases.StockUnit
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class StockUnitViewModel @Inject constructor(val stockUnit: StockUnit, val backgroundDispatcher: CoroutineDispatcher) : BaseViewModel() {
    private val unit = MutableLiveData<StockUnitEvents>()
    val observerUnits: LiveData<StockUnitEvents> = unit
    private var listOfUnits: List<MeasurementEntity> = listOf()
    private var bookId = ""
    private var currentSelected: String = ""
    private var currentSelectedMeasurementId: String = ""

    fun init(bookId: String, currentMeasurementId: String?) {
        this.bookId = bookId
        this.currentSelectedMeasurementId = currentMeasurementId ?: ""
        unit.value = StockUnitEvents.ShowLoading
        viewModelScope.launch {
            withContext(backgroundDispatcher) {
                listOfUnits = stockUnit.getAllMeasurementUnit(bookId)
                if (currentMeasurementId?.isNotEmpty() == true) {
                    listOfUnits = mapListToAssignDefault(listOfUnits)
                } else {
                    currentSelectedMeasurementId = listOfUnits.find { it.isDefault == 1 }?.measurementId
                            ?: ""
                }
                withContext(Dispatchers.Main) {
                    unit.value = StockUnitEvents.HideLoading
                    unit.value = StockUnitEvents.ShowUnit(listOfUnits)
                }
            }
        }
    }

    fun unitClicked(id: String) {
        if (id.isEmpty()) {
            unit.value = StockUnitEvents.ShowAddUnit
            return
        }
        listOfUnits.find { it.measurementId == currentSelectedMeasurementId }?.isDefault = 0
        val selected = listOfUnits.find { it.measurementId == id }
        selected?.isDefault = 1
        currentSelected = selected?.measurementName ?: ""
        currentSelectedMeasurementId = id
        unit.value = StockUnitEvents.ShowUnit(listOfUnits)
        close()
    }

    private fun mapListToAssignDefault(listOfUnit: List<MeasurementEntity>) = listOfUnits.map {
        if (it.measurementId == currentSelectedMeasurementId) {
            currentSelected = it.measurementName
            it.isDefault = 1
        } else {
            it.isDefault = 0
        }
        it
    }

    fun close() {
        unit.value = StockUnitEvents.CloseBottomSheet(currentSelectedMeasurementId, currentSelected)
    }


    sealed class StockUnitEvents {
        object ShowLoading : StockUnitEvents()
        object HideLoading : StockUnitEvents()
        data class ShowUnit(val units: List<MeasurementEntity>) : StockUnitEvents()
        data class CloseBottomSheet(val unitId: String, val unitName: String) : StockUnitEvents()
        object ShowAddUnit : StockUnitEvents()

    }

}