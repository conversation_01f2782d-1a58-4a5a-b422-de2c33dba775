package com.bukuwarung.inventory.ui.measurement

import android.content.Context
import android.content.DialogInterface
import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.inventory.detail.EditStockActivity
import com.bukuwarung.databinding.BottomSheetProductUnitSelectBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.inventory.ui.StockUnitViewModel
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject

class StockUnitBottomSheet : BaseBottomSheetDialogFragment() {

    @Inject
    lateinit var viewModel: StockUnitViewModel
    lateinit var adapter: StockUnitAdapter
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val binding = BottomSheetProductUnitSelectBinding.inflate(inflater, container, false)
        binding.stockUnitRecyclerView.layoutManager = LinearLayoutManager(activity)
        adapter = StockUnitAdapter(arrayListOf()) { id ->
            viewModel.unitClicked(id)
        }
        binding.stockUnitRecyclerView.adapter = adapter
        binding.closeDialog.setOnClickListener {
           dismiss()
        }
        observer()
        viewModel.init(arguments?.getString(BOOK_ID)
                ?: "", arguments?.getString(CURRENT_MEASUREMENT))

        binding.addUnitLayout.addUnitParent.setOnClickListener {
            viewModel.unitClicked("")
        }
        return binding.root
    }

    private fun observer() {
        viewModel.observerUnits.observe(this, Observer {
            when (it) {
                is StockUnitViewModel.StockUnitEvents.ShowLoading -> {
                }
                is StockUnitViewModel.StockUnitEvents.HideLoading -> {
                }
                is StockUnitViewModel.StockUnitEvents.ShowUnit -> {
                    adapter.updateData(it.units)
                }
                is StockUnitViewModel.StockUnitEvents.CloseBottomSheet -> {
                    if (parentFragment is StockUnitSelection) {
                        (parentFragment as StockUnitSelection).onMeasurementSelected(it.unitId, it.unitName)
                    } else if (activity is StockUnitSelection) {
                        (activity as StockUnitSelection).onMeasurementSelected(it.unitId, it.unitName)
                    }
                    dismiss()
                }
                StockUnitViewModel.StockUnitEvents.ShowAddUnit -> {
                    // open dialog
                    if (parentFragment is StockUnitSelection) {
                        (parentFragment as StockUnitSelection).addNewMeasurement()
                        dismiss()
                    } else if (activity is StockUnitSelection) {
                        (activity as StockUnitSelection).addNewMeasurement()
                        dismiss()
                    }
                }
            }
        })
    }

    companion object {
        private const val BOOK_ID = "book_id"
        private const val CURRENT_MEASUREMENT = "measurement"
        fun instance(bookId: String, measurementId: String?): StockUnitBottomSheet {
            val fragment = StockUnitBottomSheet()
            val bundle = Bundle()
            bundle.putString(CURRENT_MEASUREMENT, measurementId)
            bundle.putString(BOOK_ID, bookId)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        dismiss()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        AndroidSupportInjection.inject(this)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {

            val dialog = it as BottomSheetDialog
            val bottomSheet = dialog.findViewById<View>(R.id.design_bottom_sheet)
            bottomSheet?.let { sheet ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 200
            }

        }

    }

    interface StockUnitSelection {
        fun onMeasurementSelected(unitId: String, unitName: String)
        fun addNewMeasurement()
    }
}