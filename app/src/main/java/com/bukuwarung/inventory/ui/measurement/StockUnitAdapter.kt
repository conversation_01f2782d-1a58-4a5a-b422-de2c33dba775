package com.bukuwarung.inventory.ui.measurement

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatRadioButton
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.database.entity.MeasurementEntity

class StockUnitAdapter(private val unitList: ArrayList<MeasurementEntity>, val click: (id: String) -> Unit) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val VIEW_TYPE_UNIT = 0
    private val VIEW_TYPE_ADD_UNIT = 1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_UNIT -> StockUnitViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.stock_unit_item, null), click)

            else -> AddStockUnitViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.add_stock_unit_layout, null), click)

        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            VIEW_TYPE_UNIT -> (holder as StockUnitViewHolder).bind(unitList[position])
            VIEW_TYPE_ADD_UNIT -> (holder as AddStockUnitViewHolder).bind()
        }
    }

    override fun getItemCount(): Int {
        return unitList.size
    }

    override fun getItemViewType(position: Int): Int {
        if (position > unitList.size - 1) return VIEW_TYPE_ADD_UNIT
        return VIEW_TYPE_UNIT
    }


    class StockUnitViewHolder(val view: View, val click: (id: String) -> Unit) : RecyclerView.ViewHolder(view) {
        @SuppressLint("SetTextI18n")
        fun bind(unit: MeasurementEntity) {
            val unitRadioButton = view.findViewById<AppCompatRadioButton>(R.id.unit_radio_btn)
            unitRadioButton.text = unit.measurementName
            unitRadioButton.isChecked = unit.isDefault == 1
            view.setOnClickListener {
                click(unit.measurementId)
            }
            unitRadioButton.setOnClickListener {
                click(unit.measurementId)
            }
        }
    }


    class AddStockUnitViewHolder(val view: View, val click: (id: String) -> Unit) : RecyclerView.ViewHolder(view) {
        @SuppressLint("SetTextI18n")
        fun bind() {
            view.findViewById<View>(R.id.add_unit_parent).setOnClickListener {
                click("")
            }
        }
    }

    fun updateData(newUnitList: List<MeasurementEntity>) {
        unitList.clear()
        unitList.addAll(newUnitList)
        notifyDataSetChanged()

    }

}