package com.bukuwarung.inventory.ui

import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.*
import android.view.animation.AnimationUtils
import androidx.appcompat.widget.PopupMenu
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.catalogproduct.view.CatalogFilterBottomSheet
import com.bukuwarung.activities.catalogproduct.view.CatalogProductActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.inventory.detail.EditStockActivity
import com.bukuwarung.activities.inventory.detail.InventoryHistoryDetailActivity
import com.bukuwarung.activities.inventory.detail.ManageStockBottomSheetFragment
import com.bukuwarung.activities.productcategory.view.CategoryAction
import com.bukuwarung.activities.productcategory.view.CategoryAssociatorActivity
import com.bukuwarung.activities.productcategory.view.NewProductCategoryDialog
import com.bukuwarung.activities.productcategory.viewmodel.ProductCategoryViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.analytics.moe.trackEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.databinding.FragmentInventoryBinding
import com.bukuwarung.di.ViewModelFactory
import com.bukuwarung.inventory.ui.product.AddProductActivity
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.*
import com.google.android.material.snackbar.Snackbar
import dagger.android.support.AndroidSupportInjection
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

class InventoryFragment : BaseFragment(), OnboardingWidget.OnboardingWidgetListener {

    @Inject
    lateinit var viewModel: InventoryViewModel
    private lateinit var adapter: InventoryProductAdapter
    private lateinit var binding: FragmentInventoryBinding

    private var onBoardingWidget: OnboardingWidget? = null
    private var productEntity: ProductEntity? = null
    private var isProductCatalogEnabled = false
    private var showAdditionalCategoryAction = true
    private var categoryCreationDialog : NewProductCategoryDialog? = null
    //private var toolTipDisplayed: Boolean? = null

    @Inject
    lateinit var categoryVmFactory: ViewModelFactory<ProductCategoryViewModel>
    private lateinit var categoryViewModel: ProductCategoryViewModel

    /**
     * Remote config
     */
    private val isStockToggleEnabled = RemoteConfigUtils.isStockToggleEnabled()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    var showOnlyRunningOutStock = false
    private var normalBackPressedAction: () -> Unit = { requireActivity().finish() }
    private var productCategory : ProductCategoryEntity? = null

    companion object {

        /**
         *  the default instance will show all product
         */
        private val OUT_OF_STOCK = "out_of_stock"
        private val FROM = "from"
        fun createInstance(onlyOutStock: Boolean = false, from: String?, openCatalog : Boolean = false, normalBackPressedAction: () -> Unit) =
            InventoryFragment().apply {
                val bundle = Bundle()
                bundle.putBoolean(OUT_OF_STOCK, onlyOutStock)
                bundle.putString(FROM, from)
                bundle.putBoolean(InventoryHomeFragment.OPEN_CATALOG_BOTTOM_SHEET, openCatalog)
                arguments = bundle
                this.normalBackPressedAction = normalBackPressedAction
            }
    }

    fun onBackPressed() {
        if (onBoardingWidget != null && onBoardingWidget!!.isShown) {
            onBoardingWidget!!.dismiss(
                isFromButton = false,
                isFromCloseButton = false,
                isFromOutside = true
            )
        } else if (activity != null) {
            normalBackPressedAction()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentInventoryBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        showOnlyRunningOutStock = arguments?.getBoolean(OUT_OF_STOCK) ?: false
        viewModel.init(showOnlyRunningOutStock)
        binding.transactionRecyclerView.layoutManager = LinearLayoutManager(activity)
        adapter = InventoryProductAdapter { clickEvent ->
            when (clickEvent) {
                is InventoryProductAdapter.ClickEvent.Edit -> viewModel.onEditStockClicked(
                    clickEvent.productId
                )
                is InventoryProductAdapter.ClickEvent.Item -> viewModel.onProductClicked(clickEvent.productId)
                is InventoryProductAdapter.ClickEvent.Price -> editPrice(clickEvent.product)
                InventoryProductAdapter.ClickEvent.Banner -> openCatalogBottomSheet()
                else -> {}
            }
        }

        binding.transactionRecyclerView.adapter = adapter
        binding.addProductBtn.setOnClickListener {
            viewModel.onAddProductClicked()
        }
        return binding.root
    }

    private fun editPrice(product: ProductEntity) {
        val editSource = if (product.unitPrice >= 0) AnalyticsConst.NON_0_PRODUCT else AnalyticsConst._0PRODUCT
        val evenProp = AppAnalytics.PropBuilder().put(AnalyticsConst.EDIT_PRODUCT_PRICE_SOURCE, editSource)
        AppAnalytics.trackEvent(AnalyticsConst.INVENTORY_EDIT_PRICE_CLICK, evenProp)
        viewModel.onManagePriceClicked(product.productId)
    }


    override fun setupView(view: View) {
       categoryViewModel = ViewModelProvider(this, categoryVmFactory).get(ProductCategoryViewModel::class.java)

        binding.btnAssociateCategory.setOnClickListener {
            trackManageCategoryClickEvent(productCategory?.name)
            CategoryAssociatorActivity.open(requireContext(), productCategory?.id ?: "")
        }

        binding.filterView.apply {
            setSearchQueryListener(requireActivity()) { query ->
                viewModel.searchQueryTextChange(query.toString())
            }

            setCategoryListener { categoryDataHolder, showCategoryAction ->
                categoryDataHolder ?: return@setCategoryListener

                when(categoryDataHolder.action){
                    CategoryAction.ADD -> {
                        openCategoryCreationDialog()
                    }
                    CategoryAction.ALL -> viewModel.updateProductCategoryId(null)
                    CategoryAction.NORMAL -> {
                        productCategory = categoryDataHolder.category
                        viewModel.updateProductCategoryId(categoryDataHolder.category?.id)
                    }
                    else -> {}
                }

                showAdditionalCategoryAction = showCategoryAction
                binding.llAssociateCategory.visibility = showAdditionalCategoryAction.asVisibility()
                adapter.setCatalogBannerVisibility(!showAdditionalCategoryAction && isProductCatalogEnabled)
            }

            setSecondaryButton(R.drawable.ic_icon_sort_new_28){ anchorView ->
                showSortMenu(anchorView)
            }
        }

        // open catalog bottomsheet from PN
        arguments?.let { args ->
            if (args.getBoolean(InventoryHomeFragment.OPEN_CATALOG_BOTTOM_SHEET)){
                openCatalogBottomSheet()
            }
        }
    }

    private fun trackManageCategoryClickEvent(categoryName: String?) {
        trackEvent(AnalyticsConst.CLICK_MANAGE_PRODUCT_CATEGORY){
            addProperty(AnalyticsConst.PRODUCT_CATEGORY_NAME to categoryName)
            addEntryPointProperty(AnalyticsConst.INVENTORY_TAB)
        }
    }

    private fun openCategoryCreationDialog() {
        trackEvent(AnalyticsConst.CREATE_NEW_PRODUCT_CATEGORY){
            addProperty(AnalyticsConst.ENTRY_POINT2 to AnalyticsConst.PRODUCT_CATEGORY_SECTION)
        }


        categoryCreationDialog = NewProductCategoryDialog(requireContext(), AnalyticsConst.INVENTORY_TAB) { categoryName ->
            viewModel.createNewProductCategory(categoryName)
        }.also {
            it.setQueryChangeCallback(lifecycleScope){ query ->
                categoryViewModel.onEventReceipt(ProductCategoryViewModel.Event.RequestCategorySuggestion(query))
            }
        }

        categoryCreationDialog?.show()
    }

    private fun openCatalogBottomSheet() {
        AppAnalytics.trackEvent(AnalyticsConst.ProductCatalog.IMPORT_CATALOG_CLICK)

        CatalogFilterBottomSheet(requireContext(), viewModel.getCatalogCategories()) {
            val catalogIntent = CatalogProductActivity.createIntent(requireContext(), it.name)
            startActivity(catalogIntent)
            Log.d("Catalog Category", it.name)
        }.show()
    }

    override fun subscribeState() {
        if (showOnlyRunningOutStock) {
            viewModel.runningOutProductListStatus.observe(viewLifecycleOwner) {
                when (it) {
                    is InventoryViewModel.ProductStatusEvent.UpdateLowProductList -> {
                        updateProductList(it.productList)
                        if (it.showEmptyScreen) {
                            showEmptyScreen(it.allProducts, it.showToolTip)
                        }
                    }
                    else -> {}
                }
            }
        }

        /**
         *  need to observe this livedata to preserve the sort button state
         */
        viewModel.productListStatus.observe(viewLifecycleOwner, Observer {
            when (it) {
                is InventoryViewModel.ProductStatusEvent.UpdateProductList -> {
                    if (showOnlyRunningOutStock) return@Observer
                    updateProductList(it.productList)
                    if (it.showEmptyScreen) {
                        showEmptyScreen(it.allProducts, it.showToolTip)
                    }
                    trackPageChangeEvent()
                }
                else -> {}
            }
        })

        viewModel.observeInventory.observe(viewLifecycleOwner, Observer {
            when (it) {
                is InventoryViewModel.InventoryEvent.StartLoading -> {
                    // show loading
                }
                is InventoryViewModel.InventoryEvent.StopLoading -> {
                    // stop loading
                }
                is InventoryViewModel.InventoryEvent.ShowError -> {
                    // show error
                }
                is InventoryViewModel.InventoryEvent.OpenUpdateProductScreen -> {
                    trackClickEvent(it.totalStock, it.criticalStock)
                    if (isStockToggleEnabled && it.trackInventory == AppConst.INVENTORY_TRACKING_DISABLED) {
                        val intent =
                            activity?.let { context ->
                                EditStockActivity.getNewIntent(
                                    context,
                                    it.id,
                                    "",
                                    isInventoryEditFlow = true
                                )
                            }
                        activity?.startActivity(intent)
                    } else {
                        val intent = Intent(activity, InventoryHistoryDetailActivity::class.java)
                        intent.putExtra(InventoryHistoryDetailActivity.PRODUCT_ID_PARAM, it.id)
                        activity?.startActivity(intent)
                    }
                }
                is InventoryViewModel.InventoryEvent.OpenUpdateProductStock -> {
                    // open edit stock bottom sheet
                    goToManageBottomSheet(it.id)
                }
                 InventoryViewModel.InventoryEvent.OpenAddProductScreen -> {
                    val propBuilder = AppAnalytics.PropBuilder()
                    propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.INVENTORY)

                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_ADD_PRODUCT, propBuilder)
                    SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_INVENTORY_ADD_PRODUCT, requireActivity())

                    if (isStockToggleEnabled) {
                        val intent = EditStockActivity.getNewIntent(requireContext(), "", User.getBusinessId())
                        activity?.startActivityForResult(
                            intent,
                            EditStockActivity.PRODUCT_ADDED_SUCCESS
                        )
                    } else {
                        val intent = Intent(activity, AddProductActivity::class.java)
                        intent.putExtra(AddProductActivity.BOOK_ID, User.getBusinessId())
                        activity?.startActivityForResult(
                            intent,
                            AddProductActivity.PRODUCT_ADDED_SUCCESS
                        )
                    }
                }
                is InventoryViewModel.InventoryEvent.EnableExtraFeature -> {
                    binding.filterView.showSecondaryButton(true)
                }
                is InventoryViewModel.InventoryEvent.ShowEmptyUI -> {
                    showEmptyScreen(it.allProducts, it.showToolTip)
                }
                InventoryViewModel.InventoryEvent.ShowNoRecordFound -> {
                    showRecordNotFound(false)
                }
                is InventoryViewModel.InventoryEvent.OpenPriceEdit -> {
                    val editSource = if (it.unitPrice >= 0) AnalyticsConst.NON_0_PRODUCT else AnalyticsConst._0PRODUCT
                    val intent = EditStockActivity.getNewIntent(requireContext(), it.id, "", editSource = editSource)
                    activity?.startActivityForResult(
                        intent,
                        EditStockActivity.PRODUCT_ADDED_SUCCESS
                    )
                }
                is InventoryViewModel.InventoryEvent.CatalogFeatureEligibility -> {
                    isProductCatalogEnabled = it.isEligible
                    adapter.setCatalogBannerVisibility(!showAdditionalCategoryAction && isProductCatalogEnabled)
                }
                InventoryViewModel.InventoryEvent.OnCategoryNameAlreadyExist -> {
                    Snackbar.make(
                        binding.root,
                        getString(R.string.category_name_already_exists),
                        Snackbar.LENGTH_SHORT
                    ).apply {
                        setTextColor(requireContext().getColorCompat(R.color.red_60))
                        setBackgroundTint(requireContext().getColorCompat(R.color.red_5))
                        animationMode = Snackbar.ANIMATION_MODE_FADE
                    }.show()
                }

                else -> {}
            }
        })

        viewModel.observeFilteredList.observe(viewLifecycleOwner, Observer {
            when (it) {
                is InventoryViewModel.ProductFilterEvent.UpdateProductList -> {
                    updateProductList(it.productList)
                    showRecordNotFound(!it.showNoRecordFound)
                }
                is InventoryViewModel.ProductFilterEvent.UpdateLowProductList -> {
                }
                else -> {}
            }
        })

        viewModel.productCategoryObservable.observe(viewLifecycleOwner){ productCategories ->
            binding.filterView.setCategories(productCategories, setCreateCategoryEnabled = true)
        }

        subscribeSingleLiveEvent(categoryViewModel.state){ state ->
            when(state){
                is ProductCategoryViewModel.State.SetCategorySuggestion -> categoryCreationDialog?.submitSuggestion(state.suggestions)
                else -> {}
            }
        }

        binding.filterView.addEventEntryPoint (AnalyticsConst.INVENTORY_TAB)
    }

    private fun goToManageBottomSheet(productId: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.STOCK_ITEM_LIST)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_INVENTORY_MANAGE_STOCK, propBuilder)

        val bottomSheetFragment = ManageStockBottomSheetFragment()
        val bundle = Bundle()
        bundle.putString("product_id", productId)
        bundle.putString("entry_point", AnalyticsConst.STOCK_ITEM_LIST)
        bottomSheetFragment.arguments = bundle
        activity?.supportFragmentManager?.let {
            bottomSheetFragment.show(
                it,
                bottomSheetFragment.tag
            )
        }
    }

    private fun updateProductList(productList: List<ProductEntity>) {
        if (productList.isNotEmpty()) {
            productEntity = productList[0]
            showProductView()
        }
        adapter.updateData(productList)
    }

    private fun showEmptyScreen(showEmptyScreenForAllProducts: Boolean, showToolTip: Boolean) {

        binding.apply {
            if (showEmptyScreenForAllProducts) {
                emptyScreenLayout.root.visibility = View.VISIBLE
                transactionRecyclerView.visibility = View.GONE
                noRecordFoundTxt.visibility = View.GONE
                notFoundContainer.root.visibility = View.GONE
                if (showToolTip) {
                    showFirstTimeToolTip()
                }
            } else {
                notFoundContainer.root.visibility = View.VISIBLE
                emptyScreenLayout.root.visibility = View.GONE
                noRecordFoundTxt.visibility = View.GONE
                tutorarrow.visibility = View.GONE
                transactionRecyclerView.visibility = View.GONE

            }
        
        }


    }

    private fun showProductView() {
        binding.apply {
            transactionRecyclerView.visibility = View.VISIBLE
            noRecordFoundTxt.visibility = View.GONE
            emptyScreenLayout.root.visibility = View.GONE
            notFoundContainer.root.visibility = View.GONE
            tutorarrow.visibility = View.GONE
        }
    }

    private fun showRecordNotFound(found: Boolean) {
        binding.apply {
            if (found) {
                noRecordFoundTxt.visibility = View.GONE
                emptyScreenLayout.root.visibility = View.GONE
                notFoundContainer.root.visibility = View.GONE
                tutorarrow.visibility = View.GONE
                transactionRecyclerView.visibility = View.VISIBLE
            } else {
                noRecordFoundTxt.visibility = View.VISIBLE
                emptyScreenLayout.root.visibility = View.GONE
                notFoundContainer.root.visibility = View.GONE
                tutorarrow.visibility = View.GONE
                transactionRecyclerView.visibility = View.GONE
            }
        }
    }


    private fun showFirstTimeToolTip() {
        lifecycleScope.launch {
            delay(250L)
            binding.addProductBtn.let { viewHolder ->
                onBoardingWidget = OnboardingWidget.createInstance(
                    requireActivity(),
                    this@InventoryFragment,
                    OnboardingPrefManager.TUTOR_PRODUCT_STOCK_FIRST_TIME_USER,
                    viewHolder,
                    R.drawable.onboarding_smile,
                    getString(R.string.new_feature),
                    getString(R.string.stock_onboarding_desc),
                    getString(R.string.next),
                    FocusGravity.CENTER,
                    ShapeType.ROUND_RECT,
                    1,
                    0
                )
                val mAnimation = AnimationUtils.loadAnimation(context, R.anim.swinging)
                binding.tutorarrow.startAnimation(mAnimation)
                binding.tutorarrow.visibility = View.VISIBLE
            }
        }
    }


    override fun onResume() {
        super.onResume()
        trackPageChangeEvent()
        /**
         * only show coachmark on first tab
         */
        if (!showOnlyRunningOutStock) {
            lifecycleScope.launch {
                delay(250L)

                binding.transactionRecyclerView.post {
                    productEntity ?: return@post
                    if (!OnboardingPrefManager.getInstance()
                            .getHasFinishedForId(OnboardingPrefManager.TUTOR_PRODUCT_STOCK_DETAIL)
                    ) {
                        binding.transactionRecyclerView.post {
                            binding.transactionRecyclerView.findViewHolderForLayoutPosition(0)
                                ?.let { viewHolder ->
                                    onBoardingWidget = OnboardingWidget.createInstance(
                                        requireActivity(),
                                        this@InventoryFragment,
                                        OnboardingPrefManager.TUTOR_PRODUCT_STOCK_DETAIL,
                                        viewHolder.itemView,
                                        R.drawable.onboarding_smile,
                                        "",
                                        getString(R.string.onboarding_product_stock_detail),
                                        getString(R.string.next),
                                        FocusGravity.CENTER,
                                        ShapeType.RECTANGLE_FULL,
                                        1,
                                        2
                                    )
                                }
                        }
                    }
                }
            }
        }
        viewModel.onUiFocused()
    }

    private fun showSortMenu(anchorView: View) {

        fun highlightMenu(menuItem: MenuItem) {
            val span = SpannableString(menuItem.title)
            span.setSpan(
                ForegroundColorSpan(requireContext().getColorCompat(R.color.colorPrimaryDark)),
                0,
                span.length,
                0
            )
            menuItem.title = span
        }

        PopupMenu(requireContext(), anchorView).apply {
            menuInflater.inflate(R.menu.product_sort_menu, menu)
            when (viewModel.getCurrentSortOption()) {
                InventoryViewModel.ProductSortEvent.AscendingName -> highlightMenu(menu.getItem(0))
                InventoryViewModel.ProductSortEvent.DescendingName -> highlightMenu(menu.getItem(1))
                InventoryViewModel.ProductSortEvent.MostStock -> highlightMenu(menu.getItem(2))
                InventoryViewModel.ProductSortEvent.LeastStock -> highlightMenu(menu.getItem(3))
                else -> {}
            }


            setOnMenuItemClickListener {
                when (it.itemId) {
                    R.id.sort_ascending -> viewModel.sortProduct(InventoryViewModel.ProductSortEvent.AscendingName)
                    R.id.sort_descending -> viewModel.sortProduct(InventoryViewModel.ProductSortEvent.DescendingName)
                    R.id.sort_most_stock -> viewModel.sortProduct(InventoryViewModel.ProductSortEvent.MostStock)
                    R.id.sort_least_stock -> viewModel.sortProduct(InventoryViewModel.ProductSortEvent.LeastStock)
                    else -> {
                    }
                }

                true
            }
        }.show()
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        if (id == null) return
        when (id) {
            OnboardingPrefManager.TUTOR_PRODUCT_STOCK_DETAIL -> id.let {
                OnboardingPrefManager.getInstance().setHasFinishedForId(id)
                if (isProductCatalogEnabled) showCatalogToolTip()
            }
            OnboardingPrefManager.TUTOR_PRODUCT_STOCK_FIRST_TIME_USER -> {
                if (!isFromOutside) {
                    viewModel.tootTipDismissed(id)
                }
                binding.tutorarrow.clearAnimation()
                binding.tutorarrow.visibility = View.GONE
            }
            else -> {}
        }
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        when (id) {
            OnboardingPrefManager.TUTOR_PRODUCT_STOCK_DETAIL -> productEntity?.let {
                viewModel.onProductClicked(
                    it.productId
                )
            }
            OnboardingPrefManager.TUTOR_PRODUCT_STOCK_FIRST_TIME_USER -> {
                binding.tutorarrow.clearAnimation()
                binding.tutorarrow.visibility = View.GONE
                viewModel.tootTipActionButtonClicked(id)
            }
            else -> {}
        }
    }

    private fun trackPageChangeEvent() {
        if (MainActivity.isStockClicked) {
            val productCount = viewModel.isDataAvailableReturnSize()
            if (productCount != null) {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put("total_product", productCount)
                AppAnalytics.trackEvent(AnalyticsConst.INVENTORY_VISIT_EVENT, propBuilder)
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.INVENTORY_VISIT_EVENT, requireActivity())
                MainActivity.isStockClicked = false
            }
        }

    }

    private fun trackClickEvent(totalStock: Double, criticalStock: Boolean) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.REMAINING_STOCK, totalStock)
        propBuilder.put(AnalyticsConst.CRITICAL_STOCK, criticalStock)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SELECT_PRODUCT, propBuilder)
    }

    private fun showCatalogToolTip() {
        if (FeaturePrefManager.getInstance().isHasShownCatalogTooltip) return

        lifecycleScope.launch {
            delay(250)
            FeaturePrefManager.getInstance().isHasShownCatalogTooltip = true
        }
    }
}