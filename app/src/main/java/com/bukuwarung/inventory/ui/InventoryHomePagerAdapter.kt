package com.bukuwarung.inventory.ui

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter

class InventoryHomePagerAdapter(private val fragmentList: List<Pair<Fragment, String>>,
                                fragmentManager: FragmentManager,
                                behaviour: Int = BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT)
    : FragmentStatePagerAdapter(fragmentManager, behaviour) {
    override fun getCount(): Int = fragmentList.size

    override fun getItem(position: Int) = fragmentList[position].first

    override fun getPageTitle(position: Int): CharSequence? {
        return fragmentList[position].second
    }
}


