package com.bukuwarung.inventory.di

import com.bukuwarung.inventory.ui.InventoryActivity
import com.bukuwarung.inventory.ui.InventoryFragment
import com.bukuwarung.inventory.ui.InventoryHomeFragment
import com.bukuwarung.inventory.ui.measurement.StockUnitBottomSheet
import com.bukuwarung.inventory.ui.product.AddProductActivity
import com.bukuwarung.inventory.ui.product.AddProductFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

@Module
abstract class InventoryFragmentModule {
    @ContributesAndroidInjector
    abstract fun contributeInventoryFragment(): InventoryFragment

    @ContributesAndroidInjector
    abstract fun contributeInventoryHomeFragment(): InventoryHomeFragment

    @ContributesAndroidInjector
    abstract fun contributeAddProductActivity(): AddProductActivity

    @ContributesAndroidInjector
    abstract fun contributeAddProductFragment(): AddProductFragment

    @ContributesAndroidInjector
    abstract fun contributeStockUnitFragment(): StockUnitBottomSheet

    @ContributesAndroidInjector
    abstract fun contributeInventoryActivity(): InventoryActivity
}