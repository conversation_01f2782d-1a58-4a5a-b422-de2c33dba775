package com.bukuwarung.inventory.di

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.activities.catalogproduct.viewmodel.ProductCatalogUseCase
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.inventory.ui.InventoryViewModel
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.session.SessionManager
import kotlinx.coroutines.Dispatchers

/* Factory for creating FeatureViewModel instances */
class InventoryViewModelFactory(private val productInventory: ProductInventory, private val businessRepository: BusinessRepository, private val catalogUseCase: ProductCatalogUseCase, private val sessionManager: SessionManager, private val productCategoryUseCase: ProductCategoryUseCase) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return InventoryViewModel(productInventory, Dispatchers.IO, businessRepository, catalogUseCase, sessionManager, productCategoryUseCase) as T
    }
}

