package com.bukuwarung.inventory.datastore

import androidx.lifecycle.LiveData
import com.bukuwarung.database.dao.ProductDao
import com.bukuwarung.database.entity.ProductEntity
import javax.inject.Inject

class ProductDataStore @Inject constructor(val dao: ProductDao) : ProductLocalDataStore {
    override fun getAllProducts(bookId: String): LiveData<List<ProductEntity>> {
        return dao.getLiveProductByBusiness(bookId)
    }

    override fun getRunningOutProducts(bookId: String): LiveData<List<ProductEntity>> {
        return dao.getLiveRunningOutProductByBusiness(bookId)
    }

    override fun getRunningOutProductsList(bookId: String): List<ProductEntity> {
        return dao.getRunningOutProductListByBusiness(bookId)
    }

    override fun getProductsInStock(bookId: String): List<ProductEntity> {
        return dao.getProductListInStockByBusiness(bookId)
    }

    override fun addProduct(productEntity: ProductEntity) {
        dao.insert(productEntity)
    }

    override fun updateProduct(productEntity: ProductEntity) {
        dao.updateProduct(productEntity)
    }

    override fun getProductCount(bookId: String): Int {
        return dao.getProductCount(bookId)
    }

    override fun addProducts(productEntities: List<ProductEntity>) {
        dao.insertAllIgnore(productEntities)
    }

    override fun getAllProductNames(bookId: String): List<String> {
        return dao.getAllProductNames(bookId)
    }
}

interface ProductLocalDataStore {
    fun getAllProducts(bookId: String): LiveData<List<ProductEntity>>
    fun getRunningOutProducts(bookId: String): LiveData<List<ProductEntity>>
    fun getRunningOutProductsList(bookId: String): List<ProductEntity>
    fun getProductsInStock(bookId: String): List<ProductEntity>
    fun getProductCount(bookId: String): Int
    fun addProduct(productEntity: ProductEntity)
    fun addProducts(productEntities: List<ProductEntity>)
    fun updateProduct(productEntity: ProductEntity)
    fun getAllProductNames(bookId: String) : List<String>
}