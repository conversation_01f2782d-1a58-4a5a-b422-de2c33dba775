package com.bukuwarung.inventory.datastore

import com.bukuwarung.database.dao.InventoryDao
import com.bukuwarung.database.entity.MeasurementEntity
import javax.inject.Inject

class StockUnitDataStore @Inject constructor(val inventoryDao: InventoryDao) : StockUnitLocalDataStore {
    override suspend fun getAllMeasurementUnit(bookId: String): List<MeasurementEntity> = inventoryDao .getAllMeasurements()

    override suspend fun addMeasurementUnit(measurementEntity: MeasurementEntity)  {
        val measurementName = measurementEntity.measurementName.toLowerCase()
        if (inventoryDao.findMeasurementCountByName(measurementName) == 0)
            inventoryDao.insert(measurementEntity)
    }

    override suspend fun addMeasurementUnit(measurementEntities: List<MeasurementEntity>) {
        inventoryDao.insert(measurementEntities)
    }

    override suspend fun getDefaultMeasurement(): MeasurementEntity? {
        return inventoryDao.defaultMeasurement
    }
}

interface StockUnitLocalDataStore {
    suspend fun getAllMeasurementUnit(bookId: String): List<MeasurementEntity>
    suspend fun addMeasurementUnit(measurementEntity: MeasurementEntity)
    suspend fun addMeasurementUnit(measurementEntity: List<MeasurementEntity>)
    suspend fun getDefaultMeasurement(): MeasurementEntity?
}
