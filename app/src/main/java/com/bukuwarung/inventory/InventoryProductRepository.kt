package com.bukuwarung.inventory

import androidx.lifecycle.LiveData
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.inventory.datastore.ProductLocalDataStore
import javax.inject.Inject

class InventoryProductProductRepository @Inject constructor(val dataStore: ProductLocalDataStore) : InventoryProductRepo {


    override fun getAllProducts(bookId: String): LiveData<List<ProductEntity>> {
        return dataStore.getAllProducts(bookId)
    }

    override fun getAllRunningOutProducts(bookId: String): LiveData<List<ProductEntity>> {
        return dataStore.getRunningOutProducts(bookId)
    }

    override fun getAllRunningOutProductsList(bookId: String): List<ProductEntity> {
        return dataStore.getRunningOutProductsList(bookId)
    }

    override fun getProductsInStock(bookId: String): List<ProductEntity> {
        return dataStore.getProductsInStock(bookId)
    }

    override fun addProduct(productInventoryDto: ProductEntity) {
        dataStore.addProduct(productInventoryDto)
    }

    override fun updateProduct(productInventoryDto: ProductEntity) {
        dataStore.updateProduct(productInventoryDto)
    }

    override fun getProductCount(bookId: String): Int {
        return dataStore.getProductCount(bookId)
    }

    override fun addProducts(productEntities: List<ProductEntity>) {
        dataStore.addProducts(productEntities)
    }

    override fun getAllProductNames(bookId: String): List<String> {
        return dataStore.getAllProductNames(bookId)
    }
}

interface InventoryProductRepo {
    fun getAllProducts(bookId: String): LiveData<List<ProductEntity>>
    fun getAllRunningOutProducts(bookId: String): LiveData<List<ProductEntity>>
    fun getAllRunningOutProductsList(bookId: String): List<ProductEntity>
    fun getProductsInStock(bookId: String): List<ProductEntity>
    fun getProductCount(bookId: String) : Int
    fun addProduct(productInventoryDto: ProductEntity)
    fun addProducts(productEntities: List<ProductEntity>)
    fun updateProduct(productInventoryDto: ProductEntity)
    fun getAllProductNames(bookId: String) : List<String>
}
