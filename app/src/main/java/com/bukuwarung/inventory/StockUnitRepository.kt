package com.bukuwarung.inventory

import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.inventory.datastore.StockUnitLocalDataStore
import javax.inject.Inject

class StockUnitRepository @Inject constructor(val stockUnitLocalDataStore: StockUnitLocalDataStore): StockUnitRepo {
    override suspend fun getAllMeasurementUnit(bookId: String): List<MeasurementEntity> {
       return stockUnitLocalDataStore.getAllMeasurementUnit(bookId)
    }

    override suspend fun addMeasurementUnit(measurementEntity: MeasurementEntity) {
         stockUnitLocalDataStore.addMeasurementUnit(measurementEntity)
    }

    override suspend fun addMeasurementUnit(measurementEntity: List<MeasurementEntity>) {
         stockUnitLocalDataStore.addMeasurementUnit(measurementEntity)
    }

    override fun isDefaultUnitAdded(bookId: String): Boolean  = false
    override suspend fun getDefaultMeasurement(): MeasurementEntity? {
        return stockUnitLocalDataStore.getDefaultMeasurement()
    }

}

interface StockUnitRepo {
    suspend fun getAllMeasurementUnit(bookId: String): List<MeasurementEntity>
    suspend fun addMeasurementUnit(measurementEntity: MeasurementEntity)
    suspend fun addMeasurementUnit(measurementEntity: List<MeasurementEntity>)
    fun isDefaultUnitAdded(bookId: String): Boolean
    suspend fun getDefaultMeasurement(): MeasurementEntity?


}