package com.bukuwarung.inventory.usecases

import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.inventory.StockUnitRepo
import javax.inject.Inject

class StockUnit @Inject constructor(val stockUnitRepo: StockUnitRepo) {
    private val UNIT_NAME_LENGTH = 8
    suspend fun getAllMeasurementUnit(bookId: String): List<MeasurementEntity> {
        if (!stockUnitRepo.isDefaultUnitAdded(bookId)) {
            stockUnitRepo.addMeasurementUnit(getStaticUnitList(bookId))
        }
        return stockUnitRepo.getAllMeasurementUnit(bookId)
    }

    suspend fun addMeasurementUnit(measurementEntity: MeasurementEntity) {
        if (measurementEntity.measurementName.length <= UNIT_NAME_LENGTH) {
            stockUnitRepo.addMeasurementUnit(measurementEntity)
        } /*else {
            throw Exception("Unit name should be less than 8")
        }*/

    }

    suspend fun getDefaultMeasurement(bookId: String): MeasurementEntity? {
        if (!stockUnitRepo.isDefaultUnitAdded(bookId)) {
            stockUnitRepo.addMeasurementUnit(getStaticUnitList(bookId))
        }
        return stockUnitRepo.getDefaultMeasurement()
    }

    private fun getStaticUnitList(bookId: String) = arrayListOf<MeasurementEntity> (
            MeasurementEntity("1", bookId, 0, "Botol"),
            MeasurementEntity("2", bookId, 0, "Bungkus"),
            MeasurementEntity("3", bookId, 0, "Dus"),
            MeasurementEntity("6", bookId, 0, "Karung"),
            MeasurementEntity("7", bookId, 0, "Kaleng"),
            MeasurementEntity("4", bookId, 0, "Kg"),
            MeasurementEntity("5", bookId, 1, "Pcs"),
            MeasurementEntity("8", bookId, 0, "Lembar"),
            MeasurementEntity("9", bookId, 0, "Liter"),
            MeasurementEntity("10", bookId, 0, "Pasang"))
}