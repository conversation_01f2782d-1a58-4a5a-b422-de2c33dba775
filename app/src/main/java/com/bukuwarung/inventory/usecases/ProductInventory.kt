package com.bukuwarung.inventory.usecases

import androidx.lifecycle.LiveData
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.dto.FrequentProductDto
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.datasync.AppExpenseTransSyncManager
import com.bukuwarung.inventory.InventoryProductRepo
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import javax.inject.Inject

class ProductInventory @Inject constructor(
    private val inventoryProductRepo: InventoryProductRepo,
    private val productRepository: ProductRepository,
    val sessionManager: SessionManager) {

    /**
     * Remote config
     */
    private val isStockToggleEnabled = RemoteConfigUtils.isStockToggleEnabled()

    fun addProductToInventory(product: ProductEntity?, source: String, categories : List<String> = emptyList()) {
        if (product?.name == null) return
        product.code = Utility.generateProductCode(product.name)
        val propBuilder = AppAnalytics.PropBuilder()

        if (isStockToggleEnabled) {
            var useStock = ""
            when (product.trackInventory) {
                AppConst.INVENTORY_TRACKING_ENABLED -> {
                    useStock = AnalyticsConst.YES
                }
                AppConst.INVENTORY_TRACKING_DISABLED -> {
                    useStock = AnalyticsConst.NO
                }
            }
            propBuilder.put(AnalyticsConst.USE_STOCK, useStock)
        }

        propBuilder.put(AnalyticsConst.STOCK, product.stock)
        propBuilder.put(AnalyticsConst.MINIMUM_STOCK, product.minimumStock)
        propBuilder.put(AnalyticsConst.MEASUREMENT_UNIT, product.measurementName)
        propBuilder.put(AnalyticsConst.SELLING_PRICE, product.unitPrice>0)
        propBuilder.put(AnalyticsConst.PRODUCT_SAVE_SOURCE, source)
        propBuilder.put(AnalyticsConst.IS_FIRST_PRODUCT, FeaturePrefManager.getInstance().productCount == 1)
        propBuilder.put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(product.stock))
        propBuilder.put(AnalyticsConst.BUYING_PRICE, (product.buyingPrice != 0.0).toString())

        propBuilder.put(AnalyticsConst.PRODUCT_CATEGORY_FILLED, categories.isNotEmpty())
        propBuilder.put(AnalyticsConst.NEW_PRODUCT_CATEGORY, false)
        propBuilder.put(AnalyticsConst.PRODUCT_CATEGORY_NAME, categories.joinToString())
        propBuilder.put(AnalyticsConst.FAVORITE_MARKED, product.favourite)

        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SAVE_PRODUCT, propBuilder)
        //product count is needed for next product code
        FeaturePrefManager.getInstance().productCount = FeaturePrefManager.getInstance().productCount + 1;

        //TODO: store product to firestore, this flow is alreadu in productRepository but inventory is using separate repo that's why need to add here
        AppExpenseTransSyncManager.getInstance().updateUserProducts(product)
        inventoryProductRepo.addProduct(product)
    }

    fun addProductsToInventory(productEntities: List<ProductEntity>){
        // TODO EVENT
        // TODO Firebase
        inventoryProductRepo.addProducts(productEntities)
    }

    fun updateProductToInventory(product: ProductEntity) {
        inventoryProductRepo.updateProduct(product)
    }

    fun getAllProducts(): LiveData<List<ProductEntity>> {
        return inventoryProductRepo.getAllProducts(sessionManager.businessId)
    }

    fun getAllRunningOutProducts(): LiveData<List<ProductEntity>> =
            inventoryProductRepo.getAllRunningOutProducts(sessionManager.businessId)

    fun getProductCount(bookId: String): Int = inventoryProductRepo.getProductCount(bookId)

    fun getAllProductNames(bookId: String) : List<String> = inventoryProductRepo.getAllProductNames(bookId)

    fun updateProduct(product: ProductEntity){
        productRepository.updateProduct(product)
    }

    fun getBestsellingProduct(startDate: String, endDate: String): List<FrequentProductDto?>? {
        return if(productRepository.getFrequentProductNamesInDateRange(sessionManager.businessId,startDate,endDate).size>0)
            productRepository.getFrequentProductNamesInDateRange(sessionManager.businessId,startDate,endDate)
            else null
    }

    fun getBestSellingCategory(startDate: String, endDate: String): ProductCategoryEntity? {
        return productRepository.getBestSellingCategory(sessionManager.businessId,startDate,endDate)
    }

    fun getTotalBuyingPriceForAllCategoriesWithProduct(startDate: String, endDate: String): Double {
        return productRepository.getTotalBuyingPriceForAllCategoriesWithProduct(sessionManager.businessId,startDate,endDate)
    }

    fun getTotalSellingPriceForAllCategoriesWithProduct(startDate: String, endDate: String): Double {
        return productRepository.getTotalSellingPriceForAllCategoriesWithProduct(sessionManager.businessId,startDate,endDate)
    }

    fun getAllRunningOutProductsList() : List<ProductEntity> =
        inventoryProductRepo.getAllRunningOutProductsList(sessionManager.businessId)

    fun getProductsInStock() : List<ProductEntity> =
        inventoryProductRepo.getProductsInStock(sessionManager.businessId)

    fun getTotalTransactionsWithProducts(startDate: String, endDate: String): Int {
        return productRepository.getTotalTransactionsWithProducts(sessionManager.businessId,startDate,endDate)
    }
}