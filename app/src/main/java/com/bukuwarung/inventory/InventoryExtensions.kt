package com.bukuwarung.inventory

import com.bukuwarung.database.entity.MeasurementEntity
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.utils.Utility
import kotlin.math.ceil
import kotlin.math.floor

fun ProductEntity.generateId() : String = Utility.uuid()
fun MeasurementEntity.generateId() : String = Utility.uuid()
fun ProductEntity.generateProductCode() : String = Utility.generateProductCode(this.name)
fun ProductEntity.getDisplayPrice() : String {
    return removePrecisionIfZero(unitPrice)
}

fun removePrecisionIfZero(unitPrice: Double): String {
  return Utility.removePrecisionIfZero(unitPrice)
}
