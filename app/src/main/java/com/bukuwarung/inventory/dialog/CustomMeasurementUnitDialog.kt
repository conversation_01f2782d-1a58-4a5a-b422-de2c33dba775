package com.bukuwarung.inventory.dialog

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseDialog
import kotlinx.android.synthetic.main.dialog_edit_product.btnCancel
import kotlinx.android.synthetic.main.dialog_edit_product.btnSave
import kotlinx.android.synthetic.main.dialog_measurement_unit.*

class CustomMeasurementUnitDialog(
         context: Context,  val addMeasurementDialogInterface:  AddMeasurementInterface
) : BaseDialog(context) {
    override fun getResId(): Int = R.layout.dialog_measurement_unit

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)

        //90% of screen width
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)

        setupView()
    }

    private fun setupView() {
        btnCancel.setOnClickListener {
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.ADD_PRODUCT_SCREEN)
            propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CANCEL_CREATE_NEW)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_SET_PRODUCT_UNIT, propBuilder)
            dismiss() }
        btnSave.setOnClickListener {
            val newName = et_unit_name.text.toString()
            if (newName.isNotEmpty()) {
                addMeasurementDialogInterface.addMeasurement(newName)
            }
        }
    }

    interface AddMeasurementInterface {
        fun addMeasurement(measurement: String)
        fun dismiss()
    }
}