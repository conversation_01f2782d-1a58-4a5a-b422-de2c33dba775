package com.bukuwarung

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.onboarding.WelcomeScreens
import com.bumptech.glide.Glide
import kotlinx.android.synthetic.main.welcome_screen_item.view.*


class WelcomeAdapter(val images: List<Int>, val titles: List<String>, val subTitles: List<String>, val shouldShowNewLoginScreen: Boolean) : RecyclerView.Adapter<WelcomeAdapter.WelcomeViewHolder>() {
    private var list: List<WelcomeScreens> = listOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WelcomeViewHolder {
        return WelcomeViewHolder(parent)
    }


    override fun onBindViewHolder(holder: WelcomeViewHolder, position: Int) {
        holder.bind(position + 1 == images.size, list[position], images[position % images.size], titles[position % titles.size], subTitles[position % subTitles.size])
    }

    fun setItem(list: List<WelcomeScreens>) {
        this.list = list
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = list.size

    inner class WelcomeViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {

        constructor(parent: ViewGroup) : this(
                LayoutInflater.from(parent.context).inflate(
                        R.layout.welcome_screen_item,
                        parent, false
                )
        )

        fun bind(isLast: Boolean, welcomeScreen: WelcomeScreens, imageResId: Int, title: String, subTitle: String) {
            with(itemView) {
                Glide.with(context.applicationContext).load(welcomeScreen.backGroundImage).placeholder(imageResId).centerInside().into(iv_welcome)
                tv_title.text = title
                tv_subtitle.text = subTitle
            }
        }

    }
}