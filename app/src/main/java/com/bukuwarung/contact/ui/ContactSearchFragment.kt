package com.bukuwarung.contact.ui

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import androidx.core.widget.doOnTextChanged
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.R
import com.bukuwarung.activities.phonebook.PhoneContactViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.contact.ContactViewModelFactory
import com.bukuwarung.databinding.ContactSearchTextLayoutBinding
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog.Companion.newInstance
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog.ContactPermissionSheetListener
import com.bukuwarung.utils.*
import javax.inject.Inject

class ContactSearchFragment : BaseFragment(), ContactPermissionSheetListener {
    @Inject
    lateinit var viewModelFactory: ContactViewModelFactory
    lateinit var viewModel: ContactSearchViewModel
    private var rowHolderListViewModel: PhoneContactViewModel? = null
    private var iUserContactFragmentCommunicator: IUserContactFragmentCommunicator? = null
    private var useCase = CustomerSearchUseCase.ACCOUNTING
    private var contactSearchFragmentBinding: ContactSearchTextLayoutBinding? = null
    private val binding get() = contactSearchFragmentBinding!!
    private var orderId = ""


    override fun onAttach(context: Context) {
        super.onAttach(context)
        iUserContactFragmentCommunicator = parentFragment as? IUserContactFragmentCommunicator
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        contactSearchFragmentBinding =
            ContactSearchTextLayoutBinding.inflate(inflater, container, false)
        return binding.root
    }


    fun onClick(view: View?) {
        val viewId = (if (view != null) Integer.valueOf(view.id) else null) ?: return
        if (viewId == R.id.permissionButton) {
            if (PermissonUtil.hasContactPermission()) {
                val phoneContactViewModel = rowHolderListViewModel
                phoneContactViewModel!!.loadContactsInList()
                return
            }
            ActivityCompat.requestPermissions(
                requireActivity(),
                PermissionConst.READ_WRITE_CONTACTS,
                PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION
            )
        }
    }

    private fun observe() {
        viewModel.inputContactNameObserver.observe(viewLifecycleOwner, Observer {
            when (it) {
                is ContactSearchViewModel.OnContactNameInput.UpdateInputName -> {
                    if (parentFragment != null && parentFragment is OnCustomerNameEntered) {
                        (parentFragment as OnCustomerNameEntered).onCustomerNameEntered(it.inputName)
                    } else {
                        activity?.apply {
                            if (this is OnCustomerNameEntered) {
                                this.onCustomerNameEntered(it.inputName)
                            }
                        }
                    }
                }
                else -> {}
            }

        })
        viewModel.contactsObserver.observe(viewLifecycleOwner, Observer {
            when (it) {
                is ContactSearchViewModel.ContactEvent.ShowLoaderOnSearchResultsFragment -> {
                    showOrHideSearch(it.showLoader)
                }
                else -> {}
            }
        })
    }

    private fun showOrHideSearch(showLoader: Boolean) {
        binding.clSearchFragment.visibility = (!showLoader).asVisibility()
        binding.searchInput.requestFocus()
        binding.searchInput.setSelection(binding.searchInput.text.length)
    }

    override fun setupView(view: View) {
        viewModel = if (parentFragment == null) {
            ViewModelProvider(requireActivity(), viewModelFactory).get(ContactSearchViewModel::class.java)
        } else {
            ViewModelProvider(requireParentFragment(), viewModelFactory).get(ContactSearchViewModel::class.java)
        }
        useCase = when (arguments?.getString(USE_CASE)) {
            CustomerSearchUseCase.FAVORITE.name -> CustomerSearchUseCase.FAVORITE
            CustomerSearchUseCase.PAYMENT.name -> CustomerSearchUseCase.PAYMENT
            else -> CustomerSearchUseCase.ACCOUNTING
        }
        orderId = arguments?.getString(ORDER_ID).orEmpty()
        viewModel.init(useCase, orderId)
        with(binding) {
            arguments?.apply {
                viewModel.setShowFavoriteContacts(useCase == CustomerSearchUseCase.FAVORITE)
                if(getString(QUERY) != "") {
                    searchInput.setText(getString(QUERY))
                    searchInput.setSelection(binding.searchInput.text.length)
                }

                val transactionType = getInt(TRANSACTION_TYPE)

                when (transactionType) {
                    AppConst.DEBIT -> {
                        utangContactIcon.setImageResource(R.drawable.ic_utang_contact_icon_red)
                        tvModalMainTitle1.text = getString(R.string.give_money_to)
                    }
                    UserContactFragment.TRANSACTION_TYPE_FAVOURITE -> {
                        utangContactIcon.setImageResource(R.drawable.ic_favourite_blue)
                        tvModalMainTitle1.text = getString(R.string.enter_name)
                        searchInput.setTextColor(requireContext().getColorCompat(R.color.blue_80))
                    }
                    else -> {
                        utangContactIcon.setImageResource(R.drawable.ic_utang_contact_icon_green)
                        tvModalMainTitle1.text = getString(R.string.collect_money_from)
                    }
                }
                searchInput.requestFocus()
            }

            if(orderId.isNotBlank()) {
                searchInput.setupForSearch(coroutineScope = lifecycleScope, delay = 1000, enableImeAction = false) { newQuery ->
                    searchOnTextChange(newQuery)
                }
            } else {
                searchInput.doOnTextChanged { text, _, _, _ ->
                    searchOnTextChange(text.toString())
                }
            }
            if (PermissonUtil.hasContactPermission()) {
                addContactParentLayout.visibility = View.GONE
            }
            addContactParentLayout.setOnClickListener {
                requestContactPermission()
            }
        }
        viewModel.onSearchTextChange(arguments?.getString(QUERY) ?: "")
        InputUtils.showKeyboardFrom(context, binding.searchInput)
        observe()
    }

    private fun searchOnTextChange(text: String){
        var txt = text
        if (txt.isNotEmpty() && txt.length > 17) {
            txt = txt.substring(0, 14) + "..."
        }
        binding.tvError.hideView()
        iUserContactFragmentCommunicator?.onTextChanged(txt)
        viewModel.onSearchTextChange(txt)
    }

    override fun subscribeState() {
    }

    fun setCurrentCustomerName(customerName: String) {
        binding.searchInput.setText(customerName)
    }

    fun showSameNameError(message: String) {
        binding.tvError.showView()
        binding.tvError.text = message
    }

    companion object {
        private val QUERY = "query"
        private val TRANSACTION_TYPE = "transaction_type"
        private const val USE_CASE = "use_case"
        private const val ORDER_ID = "order_id"

        fun getInstance(
            query: String = "", type: Int?,
            customerSearchUseCase: CustomerSearchUseCase = CustomerSearchUseCase.ACCOUNTING,
            orderId: String
        ): ContactSearchFragment {
            val fragment = ContactSearchFragment()
            val bundle = Bundle().apply {
                this.putString(QUERY, query)
                this.putInt(TRANSACTION_TYPE, type ?: 0)
                this.putString(USE_CASE, customerSearchUseCase.name)
                this.putString(ORDER_ID, orderId)
            }
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun allowPermission() {
        if (!PermissonUtil.hasContactPermission()) {
            ActivityCompat.requestPermissions(requireActivity(),
                    PermissionConst.READ_WRITE_CONTACTS, PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION)
        }
    }

    fun permissionAllowed() {
        if (PermissonUtil.hasContactPermission()) {
            viewModel.contactPermissionAllowed()
            binding.addContactParentLayout.hideView()
        }
    }

    fun requestContactPermission() {
        InputUtils.hideKeyboard(activity)
        if (!PermissonUtil.hasContactPermission()) {
            val dialog = newInstance(AnalyticsConst.CONTACT_FRAGMENT)
            dialog.show(childFragmentManager, ContactPermissionBottomSheetDialog.TAG)
        } else {
            if (!PermissonUtil.hasContactPermission()) {
                AppAnalytics.trackEvent("request_contact_permission")
                ActivityCompat.requestPermissions(requireActivity(), PermissionConst.READ_WRITE_CONTACTS, PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION)
            }
        }
    }

    fun getQueryText() = binding.searchInput.text.toString()

    override fun onStop() {
        super.onStop()
        InputUtils.hideKeyboard(activity)
    }

    interface OnInputFocusChange {
        fun onInputFocusChange(isFocused: Boolean)
    }

    interface OnCustomerNameEntered {
        fun onCustomerNameEntered(customerNameEntered: String)
    }

    interface IUserContactFragmentCommunicator{
        fun onTextChanged(text: String)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        contactSearchFragmentBinding = null
    }
}