package com.bukuwarung.contact.ui


import android.os.Bundle
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.ActivityCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.contact.ContactViewModelFactory
import com.bukuwarung.databinding.ContactSearchResultsLayoutBinding
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog
import com.bukuwarung.utils.*
import javax.inject.Inject

class ContactSearchResultsFragment : BaseFragment(),
    ContactPermissionBottomSheetDialog.ContactPermissionSheetListener {

    @Inject
    lateinit var viewModelFactory: ContactViewModelFactory
    lateinit var viewModel: ContactSearchViewModel
    private lateinit var adapter: ContactAdapter
    lateinit var contactSearchFragment: ContactSearchFragment
    private var useCase = CustomerSearchUseCase.ACCOUNTING
    private var contactSearchResultsLayoutBinding: ContactSearchResultsLayoutBinding? = null
    private val binding get() = contactSearchResultsLayoutBinding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        contactSearchResultsLayoutBinding =
            ContactSearchResultsLayoutBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        viewModel = if (parentFragment == null) {
            ViewModelProvider(
                requireActivity(),
                viewModelFactory
            ).get(ContactSearchViewModel::class.java)
        } else {
            ViewModelProvider(
                requireParentFragment(),
                viewModelFactory
            ).get(ContactSearchViewModel::class.java)
        }

        useCase = when (arguments?.getString(USE_CASE)) {
            CustomerSearchUseCase.FAVORITE.name -> CustomerSearchUseCase.FAVORITE
            CustomerSearchUseCase.PAYMENT.name -> CustomerSearchUseCase.PAYMENT
            else -> CustomerSearchUseCase.ACCOUNTING
        }
        viewModel.init(useCase)

        binding.includeAddCustomer.root.setOnClickListener {
            viewModel.addNewContact()
        }

        if (PermissonUtil.hasContactPermission()) {
            binding.clAddContact.hideView()
        }

        binding.clAddContact.setSingleClickListener {
            requestContactPermission()
        }
        initContactRecyclerView()
    }

    override fun subscribeState() {
        viewModel.contactsObserver.observe(viewLifecycleOwner, Observer {
            when (it) {
                is ContactSearchViewModel.ContactEvent.DisplayContacts -> {
                    adapter.setRowHolderList(it.contactRowHolder, it.query)
                    showOrHideAddNewFavText(it.contactRowHolder.isEmpty(), it.query)
                    if (it.refreshCache) {
                        binding.clAddContact.hideView()
                    }
                }
                is ContactSearchViewModel.ContactEvent.ShowLoaderOnSearchResultsFragment -> {
                    showOrHideSearchResults(it.showLoader)
                }
                is ContactSearchViewModel.ContactEvent.UpdateSelectedCustomer -> {
                    activity?.apply {
                        if (this is OnCustomerSelectedCallback) {
                            this.onCustomerSelected(it.contact, it.contactSource)
                        }
                    }
                }
                else -> {}
            }
        })

        viewModel.addContactObserver.observe(viewLifecycleOwner, Observer {
            when (it) {
                ContactSearchViewModel.AddCustomerEvent.HideCustomerWidget -> {
                    binding.includeAddCustomer.root.hideView()
                }
                is ContactSearchViewModel.AddCustomerEvent.ShowCustomerWidget -> {
                    binding.includeAddCustomer.addContactCustomerName.text = it.customerName
                    if (useCase != CustomerSearchUseCase.FAVORITE) binding.includeAddCustomer.root.showView()
                }
                else -> {}
            }
        })
    }

    private fun showOrHideAddNewFavText(flag: Boolean, customerName: String) {
        binding.tvAddNewFavContact.visibility = (flag && customerName.isNotBlank()).asVisibility()
        binding.tvAddNewFavContact.text = Utilities.makeSectionOfTextBold(
            getString( R.string.save_favourite_subscriber, customerName ), customerName,
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    //if click requires
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                    ds.color = requireContext().getColorCompat(R.color.blue_80)
                }
            })
    }

    private fun showOrHideSearchResults(showLoader: Boolean) {
        binding.clSearchResultsFragment.visibility = (!showLoader).asVisibility()
        binding.progressBar.visibility = showLoader.asVisibility()
    }

    private fun initContactRecyclerView() {
        adapter = ContactAdapter(
            arrayListOf(),
            useCase,
            { id, contactSource -> viewModel.onContactSelected(id, contactSource) },
            { viewModel.fetchRecommendations(false, true)})
        binding.rvContactPicker.adapter = adapter
        val layoutManager = LinearLayoutManager(context)
        binding.rvContactPicker.setHasFixedSize(false)
        binding.rvContactPicker.layoutManager = layoutManager
    }

    fun requestContactPermission() {
        InputUtils.hideKeyboard(activity)
        if (!PermissonUtil.hasContactPermission()) {// && !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_CONTACT_PERMISSION) && AppConfigManager.getInstance().showCustomPermissionDialog()) {
            val dialog = ContactPermissionBottomSheetDialog.newInstance(AnalyticsConst.CONTACT_FRAGMENT)
            dialog.show(childFragmentManager, ContactPermissionBottomSheetDialog.TAG)
        }
    }

    companion object {
        private const val USE_CASE = "use_case"
        fun getInstance(customerSearchUseCase: CustomerSearchUseCase = CustomerSearchUseCase.ACCOUNTING): ContactSearchResultsFragment {
            return ContactSearchResultsFragment().apply {
                arguments = Bundle().apply {
                    putString(USE_CASE, customerSearchUseCase.name)
                }
            }
        }
    }

    interface OnCustomerSelectedCallback {
        fun onCustomerSelected(contact: Contact?, contactSource: String)
    }

    override fun allowPermission() {
        if (!PermissonUtil.hasContactPermission()) {
            ActivityCompat.requestPermissions(
                requireActivity(),
                PermissionConst.READ_WRITE_CONTACTS,
                PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        contactSearchResultsLayoutBinding = null
    }

}

enum class CustomerSearchUseCase { FAVORITE, PAYMENT, ACCOUNTING }