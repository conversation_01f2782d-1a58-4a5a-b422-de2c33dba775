package com.bukuwarung.comparator;

import com.bukuwarung.constants.LeaderBoardSort;
import com.bukuwarung.database.entity.referral.UserRank;

import java.util.Comparator;


public class UserRankComparator implements Comparator<UserRank> {
    private final int orderBy;
    public UserRankComparator(int order){
        orderBy = order;
    }

    public final int compare(UserRank userRank, UserRank userRank2) {
        switch(orderBy) {
            case LeaderBoardSort.RANK_ASC:
                return userRank.rank.compareTo(userRank2.rank);
            case LeaderBoardSort.POINT_ASC:
                return userRank.points.compareTo(userRank2.points);
        }
        return userRank.rank.compareTo(userRank2.rank);
    }
}
