package com.bukuwarung.appsflyer

import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.appsFlyer.AppsFlyerUseCase
import com.bukuwarung.model.request.AppsFlyerIdRequest
import com.bukuwarung.session.User
import kotlinx.coroutines.launch
import javax.inject.Inject

class AppsFlyerViewModel @Inject constructor(private var appsFlyerUseCase: AppsFlyerUseCase): BaseViewModel() {
     fun sendAppsFlyerId(appsFlyerId: String, advertisingId: String?) = viewModelScope.launch {
        val request = AppsFlyerIdRequest(userId = User.getUserId(), appsFlyerId = appsFlyerId, advertisingId = advertisingId)
         when(appsFlyerUseCase.sendAppsFlyerId(request)){
             is ApiSuccessResponse -> {}
             is ApiErrorResponse -> {}
             else -> {}
         }

    }
}