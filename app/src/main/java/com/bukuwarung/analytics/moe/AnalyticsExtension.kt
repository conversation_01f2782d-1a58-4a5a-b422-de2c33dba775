package com.bukuwarung.analytics.moe

import com.bukuwarung.analytics.AppAnalytics

fun trackEvent(
    event: String,
    trackMoEngage: Boolean = true,
    trackFirebase: Boolean = true,
    trackAppsFlyer: Boolean = true,
    lambda: (AppAnalytics.PropBuilder.() -> Unit)? = null
) {
    if (lambda != null) {
        val props = AppAnalytics.PropBuilder().apply(lambda)
        AppAnalytics.trackEvent(event, props, trackMoEngage, trackFirebase, trackAppsFlyer)
    } else {
        AppAnalytics.trackEvent(event, trackMoEngage, trackFirebase, trackAppsFlyer)
    }
}

fun test() {
    trackEvent("EVENT_NAME", trackMoEngage = false)

    trackEvent("EVENT_NAME"){
        addProperty("PROP_NAME" to "PROP_VALUE")
    }
}