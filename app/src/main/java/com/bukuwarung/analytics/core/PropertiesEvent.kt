package com.bukuwarung.analytics.core

import android.location.Location
import androidx.annotation.RestrictTo
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

public class PropertiesEvent {

    private val payloadBuilder = PayloadBuilder()

    /**
     * Add an attribute with value of type Object.
     * <br></br> **Note:** This API only accepts the below data-types:<br></br>
     *
     * String
     *
     * Integer
     *
     * Long
     *
     * Double
     *
     * Float
     *
     * Boolean
     *
     * Date
     *
     * GeoLocation
     *
     * JSONArray
     *
     * JSONObject
     *
     * If any other data-type is passed payload would be rejected.
     *
     * @param attributeName Attribute name
     * @param attributeValue Attribute value
     * @return Instance of [PropertiesEvent]
     * @since 10.0.00
     */
    @Suppress("SENSELESS_COMPARISON")
    public fun addAttribute(attributeName: String, attributeValue: Any?): PropertiesEvent {
        if (attributeName == null || attributeValue == null) return this
        if (isAcceptedDataType(attributeValue)) {
            addAttributeInternal(attributeName, attributeValue)
        }
        return this
    }

    /**
     * Add an attribute with value of type date. The input string should in ISO 8601 format.
     *
     * Accepted Format - [yyyy-MM-dd|yyyyMMdd][T(hh:mm[:ss[.sss]]|hhmm[ss[.sss]])]?[Z|[+-]hh:mm]]
     *
     * @param attributeName Attribute name
     * @param attributeValue Attribute value
     * @return Instance of [PropertiesEvent]
     * @since 10.0.00
     */
    @Suppress("SENSELESS_COMPARISON")
    public fun addDateIso(attributeName: String, attributeValue: String): PropertiesEvent {
        if (attributeName == null || attributeValue == null) return this
        payloadBuilder.putAttrISO8601Date(attributeName, attributeValue)
        return this
    }

    /**
     * Add an attribute with value of type date. The input value should be in Epoch(milliseconds)
     *
     * @param attributeName Attribute name
     * @param attributeValue Attribute value
     * @return Instance of [PropertiesEvent]
     * @since 10.0.00
     */
    public fun addDateEpoch(attributeName: String, attributeValue: Long): PropertiesEvent {
        payloadBuilder.putAttrDateEpoch(attributeName, attributeValue)
        return this
    }

    /**
     * Marks an event as non-interactive.
     *
     * @return Instance of [PropertiesEvent]
     * @since 10.0.00
     */
    public fun setNonInteractive(): PropertiesEvent {
        payloadBuilder.setNonInteractive()
        return this
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public fun getPayload(): PayloadBuilder {
        return payloadBuilder
    }

    private fun addAttributeInternal(attributeName: String, attributeValue: Any) {
        try {
            if (attributeName.isNullOrEmpty()) return
                    when (attributeValue) {

                is Date -> {
                    payloadBuilder.putAttrDate(attributeName, attributeValue)
                }
                is Location -> {
                    payloadBuilder.putAttrLocation(attributeName, attributeValue)
                }
                else -> {
                    payloadBuilder.putAttrObject(attributeName, attributeValue)
                }
            }
        } catch (e: Exception) {

        }
    }

    private fun isAcceptedDataType(attributeValue: Any): Boolean {
        return (attributeValue is String
                || attributeValue is Int
                || attributeValue is Long
                || attributeValue is Double
                || attributeValue is Float
                || attributeValue is Boolean
                || attributeValue is Date
                || attributeValue is JSONArray
                || attributeValue is JSONObject
                || attributeValue is Location)
    }
}