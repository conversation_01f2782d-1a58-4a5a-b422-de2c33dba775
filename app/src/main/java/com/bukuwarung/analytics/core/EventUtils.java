package com.bukuwarung.analytics.core;



import org.json.JSONObject;

import java.util.Calendar;

public final class EventUtils {


    private EventUtils() {
        // Utility class cannot be instantiated
    }

    /**
     * Attribute key which holds the value of EVENT_ACTION
     */
    private static final String EVENT_ACTION = "EVENT_ACTION";
    /**
     * Attribute key which holds the value of EVENT_ATTRS
     */
    public static final String EVENT_ATTRS = "EVENT_ATTRS";

    public static final String EVENT_ATTRS_CUST = "EVENT_ATTRS_CUST";
    /**
     * Attribute key which holds the value of EVENT_G_TIME
     */
    public static final String EVENT_G_TIME = "EVENT_G_TIME";
    /**
     * Attribute key which holds the value of EVENT_L_TIME
     */
    public static final String EVENT_L_TIME = "EVENT_L_TIME";

    public static final String EVENT_NON_INTERACTIVE = "N_I_E";

    public static int[] MONTH_NUMBERS = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };

    public static JSONObject getDataPointJson(String action, JSONObject actionAttrs, String epochTime,
                                              String localTime) {
        return getDataPointJson(action, actionAttrs, null, epochTime, localTime);
    }

    public static JSONObject getDataPointJson(String action, JSONObject generalAttrs,
                                              JSONObject customAttrs, String epochTime, String localTime) {
        JSONObject jsonInteraction = new JSONObject();
        try {
            jsonInteraction.put(EVENT_ACTION, action);
            if (null != generalAttrs) {
                jsonInteraction.put(EVENT_ATTRS, generalAttrs.toString());
            }
            if (null != customAttrs && customAttrs.length() != 0) {
                jsonInteraction.put(EVENT_ATTRS_CUST, customAttrs.toString());
            }
            jsonInteraction.put(EVENT_G_TIME, epochTime);
            jsonInteraction.put(EVENT_L_TIME, localTime);
            return jsonInteraction;
        } catch (Exception e) {

        }
        return null;
    }



    public static String getDateDataPointFormat() {
        Calendar c = Calendar.getInstance();

        int hours = c.get(Calendar.HOUR_OF_DAY);
        int mins = c.get(Calendar.MINUTE);
        int secs = c.get(Calendar.SECOND);
        int day = c.get(Calendar.DAY_OF_MONTH);
        int month = MONTH_NUMBERS[c.get(Calendar.MONTH)];
        int year = c.get(Calendar.YEAR);

        StringBuilder dateString = new StringBuilder().append(day)
                .append(":")
                .append(month)
                .append(":")
                .append(year)
                .append(":")
                .append(hours)
                .append(":")
                .append(mins)
                .append(":")
                .append(secs);

        return dateString.toString();
    }
}