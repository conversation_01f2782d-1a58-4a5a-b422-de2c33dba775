package com.bukuwarung.analytics.core;

import android.location.Location;
import android.text.TextUtils;

import androidx.annotation.NonNull;


import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class PayloadBuilder {



    private JSONObject generalAttrs;
    private JSONObject customAttrs;
    private boolean isInteractiveEvent;
    private boolean hasCalledNonInteractiveApi;

    private void notNullCheck(String attrName) throws Exception {
        if (attrName.isEmpty()) throw new Exception("Action name cannot be empty");
    }


    @Deprecated public PayloadBuilder putAttrInt(String attrName, int attrValue) {
        try {
            notNullCheck(attrName);
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrString(String attrName, String attrValue) {
        try {
            notNullCheck(attrName);
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrBoolean(String attrName, boolean attrValue) {
        try {
            notNullCheck(attrName);
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrFloat(String attrName, float attrValue) {
        try {
            notNullCheck(attrName);
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrDouble(String attrName, double attrValue) {
        try {
            notNullCheck(attrName);
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }

    @Deprecated public PayloadBuilder putAttrLong(String attrName, long attrValue) {
        try {
            notNullCheck(attrName);
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrDate(String attrName, Date attrValue) {
        try {
            notNullCheck(attrName);
            JSONArray timeStamp;
            if (customAttrs.has(ATTR_TIMESTAMP)) {
                timeStamp = customAttrs.getJSONArray(ATTR_TIMESTAMP);
            } else {
                timeStamp = new JSONArray();
            }
            JSONObject attr = new JSONObject();
            attr.put(attrName.trim(), attrValue.getTime());
            timeStamp.put(attr);
            customAttrs.put(ATTR_TIMESTAMP, timeStamp);
        } catch (Exception e) {

        }
        return this;
    }

    @Deprecated public PayloadBuilder putAttrDate(String attrName, String dateString,
                                                  String dateFormat) {
        try {
            notNullCheck(attrName);
            DateFormat format = new SimpleDateFormat(dateFormat, Locale.ENGLISH);
            return putAttrDate(attrName.trim(), format.parse(dateString));
        } catch (Exception e) {

        }
        return this;
    }



    @Deprecated public PayloadBuilder putAttrLocation(String attrName, Location attrValue) {
        try {
            notNullCheck(attrName);
            JSONArray location;
            if (customAttrs.has(ATTR_LOCATION)) {
                location = customAttrs.getJSONArray(ATTR_LOCATION);
            } else {
                location = new JSONArray();
            }
            JSONObject attr = new JSONObject();
            attr.put(attrName.trim(), attrValue.getLatitude() + "," + attrValue.getLongitude());
            location.put(attr);
            customAttrs.put(ATTR_LOCATION, location);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrLocation(String attrName, double latitude,
                                                      double longitude) {
        try {
            notNullCheck(attrName);
            JSONArray location;
            if (customAttrs.has(ATTR_LOCATION)) {
                location = customAttrs.getJSONArray(ATTR_LOCATION);
            } else {
                location = new JSONArray();
            }
            JSONObject attr = new JSONObject();
            attr.put(attrName.trim(), latitude + "," + longitude);
            location.put(attr);
            customAttrs.put(ATTR_LOCATION, location);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrDateEpoch(String attrName, long attrValue) {
        try {
            notNullCheck(attrName);
            JSONArray timeStamp;
            if (customAttrs.has(ATTR_TIMESTAMP)) {
                timeStamp = customAttrs.getJSONArray(ATTR_TIMESTAMP);
            } else {
                timeStamp = new JSONArray();
            }
            JSONObject attr = new JSONObject();
            attr.put(attrName.trim(), attrValue);
            timeStamp.put(attr);
            customAttrs.put(ATTR_TIMESTAMP, timeStamp);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrJSONArray(@NonNull String attrName,
                                                       @NonNull JSONArray attrValue) {
        try {
            notNullCheck(attrName);
            if (attrValue == null) return this;
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrJSONObject(@NonNull String attrName,
                                                        @NonNull JSONObject attrValue) {
        try {
            notNullCheck(attrName);
            if (attrValue == null) return this;
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {

        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrObject(@NonNull String attrName,
                                                    @NonNull Object attrValue) {
        try {
            notNullCheck(attrName);
            if (attrValue == null) return this;
            generalAttrs.put(attrName.trim(), attrValue);
        } catch (Exception e) {
        }
        return this;
    }


    @Deprecated public PayloadBuilder putAttrISO8601Date(@NonNull String attrName,
                                                         @NonNull String attrValue) {
        try {
            notNullCheck(attrName);
            if (TextUtils.isEmpty(attrValue)) {
                return this;
            }
            long epochValue = ISO8601Utils.parse(attrValue).getTime();
            putAttrDateEpoch(attrName, epochValue);
        } catch (Exception e) {
        }
        return this;
    }


    @Deprecated public JSONObject build() {
        try {
            JSONObject dataPoint = new JSONObject();
            boolean nothing = true;
            if (generalAttrs.length() > 0) {
                markEventAsNonInteractiveIfRequired();
                dataPoint.put(EventUtils.EVENT_ATTRS, generalAttrs.toString());
                nothing = false;
            }
            if (customAttrs.length() > 0) {
                dataPoint.put(EventUtils.EVENT_ATTRS_CUST, customAttrs.toString());
                nothing = false;
            }
            if (nothing) {
                dataPoint.put(EventUtils.EVENT_ATTRS, new JSONObject().toString());
            }

            dataPoint.put(EventUtils.EVENT_L_TIME, EventUtils.getDateDataPointFormat());
            if (!isInteractiveEvent) {
                dataPoint.put(EventUtils.EVENT_NON_INTERACTIVE, 1);
            }
            return dataPoint;
        } catch (Exception e) {

        }
        return null;
    }

    private void markEventAsNonInteractiveIfRequired() {
        try {
            if (generalAttrs.has(NON_INTERACTIVE_EVENT_ATTRIBUTE) && !hasCalledNonInteractiveApi) {
                Object attributeValue = generalAttrs.get(NON_INTERACTIVE_EVENT_ATTRIBUTE);
                if (attributeValue instanceof Integer) {
                    int isNonInteractive = (Integer) attributeValue;
                    isInteractiveEvent = isNonInteractive != 1;
                } else {

                }
            }
            if (generalAttrs.has(NON_INTERACTIVE_EVENT_ATTRIBUTE)) {
                generalAttrs.remove(NON_INTERACTIVE_EVENT_ATTRIBUTE);
            }
        } catch (JSONException e) {

        }
    }


    @Deprecated public PayloadBuilder setNonInteractive() {
        isInteractiveEvent = false;
        hasCalledNonInteractiveApi = true;
        return this;
    }


    @Deprecated public PayloadBuilder() {
        customAttrs = new JSONObject();
        generalAttrs = new JSONObject();
        isInteractiveEvent = true;
    }

    private static final String ATTR_TIMESTAMP = "timestamp";
    private static final String ATTR_LOCATION = "location";
    private static final String NON_INTERACTIVE_EVENT_ATTRIBUTE = "moe_non_interactive";
}