package com.bukuwarung.analytics

import android.content.Context
import com.bukuwarung.Application
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.recordException
import com.survicate.surveys.Survicate
import com.survicate.surveys.SurvicateAnswer
import com.survicate.surveys.SurvicateEventListener
import com.survicate.surveys.traits.UserTrait
import java.lang.Exception

class SurvicateAnalytics {

    companion object {
        @JvmStatic
        fun initSurvicateAnalytics() {
            Survicate.init(Application.getAppContext())
        }

        @JvmStatic
        fun setUserIdentifier() {
            try {
                Survicate.setUserTrait(UserTrait.UserId(SessionManager.getInstance().userId))
            }catch (e: Exception){
                e.recordException()
            }
        }

        @JvmStatic
        fun setEntry(screenName: String) {
            Survicate.enterScreen(screenName)
        }

        @JvmStatic
        fun exitScreen(screenName: String) {
            Survicate.leaveScreen(screenName)
        }

        @JvmStatic
        fun resetSurvicate() {
            Survicate.reset()
        }

        @JvmStatic
        fun invokeEventTracker(analyticsEvent: String, context: Context) {
            Survicate.invokeEvent(analyticsEvent)
            setSurvicateEventListener(context)
        }

        @JvmStatic
        fun setSurvicateEventListener(context: Context) {
            Survicate.setEventListener(object: SurvicateEventListener() {
                override fun onSurveyDisplayed(surveyId: String) {
//                    Toast.makeText(context, "on survey displayed", Toast.LENGTH_SHORT).show()
                }
                override fun onQuestionAnswered(surveyId: String, questionId: Long, answer: SurvicateAnswer) {
//                    Toast.makeText(context, "on question answered", Toast.LENGTH_SHORT).show()
                }
                override fun onSurveyClosed(surveyId: String) {
//                    Toast.makeText(context, "on survey closed", Toast.LENGTH_SHORT).show()
                }
                override fun onSurveyCompleted(surveyId: String) {
//                    Toast.makeText(context, "on survey completed", Toast.LENGTH_SHORT).show()
                }
            })
        }
    }


}