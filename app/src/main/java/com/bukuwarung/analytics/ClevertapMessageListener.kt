package com.bukuwarung.analytics

import android.app.PendingIntent
import android.content.Intent
import android.os.Handler
import android.os.Looper
import com.appsflyer.AppsFlyerLib
import com.bukuwarung.activities.SplashActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst.IS_FROM_NOTIF
import com.bukuwarung.constants.AppConst.LOGOUT_ACTION
import com.bukuwarung.constants.AppConst.PN_ID
import com.bukuwarung.constants.AppConst.REFRESH_TOKEN
import com.bukuwarung.domain.notification.PostFcmTokenUseCase
import com.bukuwarung.managers.local_notification.LocalNotificationData
import com.bukuwarung.managers.local_notification.LocalNotificationManager
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.setup.CashTransactionDataSync
import com.bukuwarung.setup.CustomerDataSync
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.zoho.livechat.android.ZohoLiveChat
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import javax.inject.Inject


@AndroidEntryPoint
class ClevertapMessageListener: FirebaseMessagingService() {

    companion object {
        const val REQ_CODE_SPLASH = 101
        const val FCM_DEEPLINK_KEY = "deeplink"
        const val FCM_WEB_URL_KEY = "weburl"
        const val GCM_ACTIVITY_NAME = "gcm_activityName"
        const val PAYMENT_HISTORY_DETAILS_ACTIVITY_CLASS_NAME = "com.bukuwarung.payments.PaymentHistoryDetailsActivity"
        const val SILENT_NOTIFICATION_ACTION = "silent_notification_action"
        const val WZRK_DL = "wzrk_dl"
        const val TRUE_STRING = "true"
    }

    var handler: Handler? = null
    @Inject
    lateinit var postFcmTokenUseCase: PostFcmTokenUseCase

    override fun onMessageReceived(message: RemoteMessage) {
        sendPushNotification(message.notification?.title, message.notification?.body, message.toIntent())
    }

    override fun onNewToken(token: String) {
        ZohoLiveChat.Notification.enablePush(token, false)

        try {
            AppsFlyerLib.getInstance()
                .updateServerUninstallToken(application.applicationContext, token)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        runBlocking {
            postFcmTokenUseCase.invoke()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler?.removeCallbacksAndMessages(null)
        handler = null
    }

    /*
    notification from firebase console will handle by system, onMessageReceived will never
    called except when app is in foreground. so we need to handle background task in this function
     */
    override fun handleIntent(intent: Intent?) {
        try {
            intent?.extras?.let {
                if (it.getString(GCM_ACTIVITY_NAME) == PAYMENT_HISTORY_DETAILS_ACTIVITY_CLASS_NAME) {
                    runBlocking(Dispatchers.IO) {
                        //AsyncCashTransactionDataSync().execute()
                        CashTransactionDataSync().execute()
                        //AsyncCustomerDataSync().execute()
                        CustomerDataSync().execute()
                    }
                }

                if (it.containsKey(PN_ID)) {
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_NOTIFICATION_RECEIVED,
                        AppAnalytics.PropBuilder().put(AnalyticsConst.ID, PN_ID)
                    )
                }

                if (it.getString(SILENT_NOTIFICATION_ACTION) == LOGOUT_ACTION ||
                    it.getString(WZRK_DL)?.contains("$SILENT_NOTIFICATION_ACTION=$LOGOUT_ACTION") == true
                ) {
                    try {
                        SessionManager.getInstance().forceLogout()
                    } catch (ex: Exception) {
                        FirebaseCrashlytics.getInstance().recordException(ex)
                    }
                    return
                }

                if (it.getString(SILENT_NOTIFICATION_ACTION) == REFRESH_TOKEN ||
                    it.getString(WZRK_DL)
                        ?.contains("$SILENT_NOTIFICATION_ACTION=$REFRESH_TOKEN") == true
                ) {
                    try {
                        handler = Handler(Looper.getMainLooper())
                        handler?.post {
                            AuthHelper.newSession()
                        }
                    } catch (ex: Exception) {
                        FirebaseCrashlytics.getInstance().recordException(ex)
                    }
                    return
                }
            }
            intent?.putExtra(IS_FROM_NOTIF, TRUE_STRING)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
        super.handleIntent(intent)
    }

    private fun sendPushNotification(
        title: String?,
        description: String?,
        remoteIntent: Intent
    ) {
        val notificationIntent = Intent(applicationContext, SplashActivity::class.java)
        remoteIntent.extras?.let { notificationIntent.putExtras(it) }
        notificationIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        val pendingIntent = PendingIntent.getActivity(
            applicationContext,
            REQ_CODE_SPLASH,
            notificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        LocalNotificationManager.showDefaultNotification(
            context = application.applicationContext,
            localNotificationData = LocalNotificationData(
                title = title,
                message = description
            ),
            intentToFire = pendingIntent
        )
    }
}