package com.bukuwarung.analytics;

import android.util.Log;

import com.amplitude.api.Amplitude;
import com.appsflyer.AppsFlyerConversionListener;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.Utility;

import java.util.Map;

class ConversionListerner implements AppsFlyerConversionListener {

    public ConversionListerner(){

    }

    @Override
    public void onConversionDataSuccess(Map<String, Object> map) {
        try {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            for (String attrName : map.keySet()) {
                propBuilder.put(attrName,map.get(attrName));
                if(Utility.isEqual(AnalyticsConst.USER_PROP_ADVERTISING_ID,attrName) && map.get(attrName)!=null){
                    SessionManager.getInstance().setAdvertisingId(String.valueOf(map.get(attrName)));
                }
            }
            AppAnalytics.setUserProperties(propBuilder.buildAmp());
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void onConversionDataFail(String errorMessage) {
        try{
            Log.d("LOG_TAG", "error getting conversion data: " + errorMessage);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void onAppOpenAttribution(Map<String, String> conversionData) {
        try{
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    @Override
    public void onAttributionFailure(String errorMessage) {
        try {
            Log.d("LOG_TAG", "error onAttributionFailure : " + errorMessage);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
