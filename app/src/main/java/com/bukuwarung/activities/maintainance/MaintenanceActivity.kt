package com.bukuwarung.activities.maintainance

import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.ActivityMaintainanceBinding
import com.bukuwarung.session.AppMaintenanceData
import com.bukuwarung.utils.RemoteConfigUtils.getAppMaintenanceData
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken

class MaintenanceActivity : BaseActivity() {
    private lateinit var binding: ActivityMaintainanceBinding

    override fun setViewBinding() {
        binding = ActivityMaintainanceBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)
        val appMaintenance = getAppMaintenanceData()
        val gson = GsonBuilder().create()
        val jsonType = object : TypeToken<AppMaintenanceData?>() {}.type
        val appMaintenanceData: AppMaintenanceData = gson.fromJson(appMaintenance, jsonType)
        binding.maintenanceTitle.text = appMaintenanceData.maintenanceTitle
        binding.maintenanceBody.text = appMaintenanceData.maintenanceBody
        binding.btnReturnHome.setOnClickListener {
          finish()
        }
    }

    override fun subscribeState() {
    }

}