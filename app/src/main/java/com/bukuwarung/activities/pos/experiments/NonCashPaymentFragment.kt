package com.bukuwarung.activities.pos.experiments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.activities.pos.PosPaymentFragment
import com.bukuwarung.activities.pos.adapter.PosPaymentWalletAdapter
import com.bukuwarung.activities.pos.model.PosPaymentWallet
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.pos.viewmodel.PosViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentPosNonCashPaymentBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject

class NonCashPaymentFragment : BaseFragment() {
    private lateinit var binding: FragmentPosNonCashPaymentBinding

    @Inject
    lateinit var viewModelFactory: PosViewModelFactory
    private val viewModel: PosViewModel by activityViewModels { viewModelFactory }

    private var posPaymentWalletAdapter: PosPaymentWalletAdapter? = null

    /**
     * State variables
     * to store/change UI or event state
     */
    private var selectedPaymentWallet = -1
    private var sortedPaymentWallets = emptyList<PosPaymentWallet>()
    private var entryPoint: String = ""
    private var totalAmountToPay: Double = 0.0
    private var totalBuyingPrice: Double = 0.0

    /**
     * Remote config
     */
    private val paymentWallets = RemoteConfigUtils.PosExperiments.getPaymentWallets()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPosNonCashPaymentBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val gson: Gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<PosPaymentWallet?>?>() {}.type
        val paymentWalletsToDisplay: List<PosPaymentWallet> =
            gson.fromJson(paymentWallets, jsonType)

        posPaymentWalletAdapter = PosPaymentWalletAdapter {
            it?.let {
                selectedPaymentWallet = it.order
                binding.btnSave.isEnabled = true
                posPaymentWalletAdapter?.setPaymentWallet(it.order)
            }
        }
        binding.rvPosPaymentWallet.layoutManager = GridLayoutManager(requireContext(), 2)
        binding.rvPosPaymentWallet.adapter = posPaymentWalletAdapter

        sortedPaymentWallets = paymentWalletsToDisplay.sortedBy { it.order }
        posPaymentWalletAdapter?.setData(sortedPaymentWallets)

        binding.btnSave.setOnClickListener {
            totalAmountToPay = viewModel.getSelectedProductTotalPrice()
            totalBuyingPrice = viewModel.getSelectedProductTotalBuyingPrice()

            viewModel.trxType = PosPaymentFragment.EXACT_AMOUNT
            entryPoint = AnalyticsConst.PAY_EXACT_AMOUNT_BUTTON
            binding.tvTrxSuccess.text = RemoteConfigUtils.getTrxSuccessMessage()

            val selectedPaymentMethod = sortedPaymentWallets.single { it.order ==
                    selectedPaymentWallet }
            viewModel.setSelectedNonCashPaymentMethod(selectedPaymentMethod.name)

            viewModel.onEventReceived(
                PosViewModel.Event.OnTransactionSave(
                    totalAmountToPay,
                    0.0,
                    totalBuyingPrice,
                    selectedPaymentMethod.name
                )
            )

        }
    }

    override fun setupView(view: View) {
        // No-op
    }

    override fun subscribeState() {
        // No-op
    }

    companion object {
        fun getInstance() = NonCashPaymentFragment()
    }
}