package com.bukuwarung.activities.pos.experiments.customviews

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView

class PosTabLayoutBadgeCustomView @JvmOverloads constructor(
    context: Context,
    attrSet: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrSet, defStyleAttr) {

    private var view: View? = null

    init {
        view = inflate(context, R.layout.pos_tab_layout_badge_custom_view, this)
    }

    fun setSelectedColor() {
        view?.findViewById<TextView>(R.id.tv_non_cash)?.setTextColor(resources.getColor(R.color
            .blue_80))
    }

    fun setUnselectedColor() {
        view?.findViewById<TextView>(R.id.tv_non_cash)?.setTextColor(resources.getColor(R.color
            .black_40))
    }

    fun hideNewBadgeIfShown() {
        if (AppConfigManager.getInstance().posNonCashPaymentMethodNewBadgeShown == false) {
            view?.findViewById<ImageView>(R.id.iv_new_badge)?.showView()
            AppConfigManager.getInstance().posNonCashPaymentMethodNewBadgeShown = true
        } else {
            view?.findViewById<ImageView>(R.id.iv_new_badge)?.hideView()
        }
    }
}