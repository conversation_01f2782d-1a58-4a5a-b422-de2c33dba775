package com.bukuwarung.activities.pos.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.pos.model.PosPaymentWallet
import com.bukuwarung.databinding.ItemPosPaymentWalletBinding
import com.bumptech.glide.Glide

class PosPaymentWalletAdapter(private val clickAction: (PosPaymentWallet?) -> Unit) :
    RecyclerView.Adapter<PosPaymentWalletAdapter.PosPaymentWalletViewHolder>() {

    private var list = emptyList<PosPaymentWallet>()
    private var selectedPaymentWallet: Int = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PosPaymentWalletViewHolder {
        val itemBinding =
            ItemPosPaymentWalletBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return PosPaymentWalletViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: PosPaymentWalletViewHolder, position: Int) {
        holder.bind(list[position], selectedPaymentWallet)
    }

    override fun getItemCount(): Int = list.size

    fun setPaymentWallet(paymentWallet: Int) {
        selectedPaymentWallet = paymentWallet

        notifyDataSetChanged()
    }

    fun setData(list: List<PosPaymentWallet>) {
        this.list = list
        notifyDataSetChanged()
    }

    class PosPaymentWalletViewHolder(
        private val binding: ItemPosPaymentWalletBinding,
        private val clickAction: (PosPaymentWallet?) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(paymentWallet: PosPaymentWallet, selectedPaymentWallet: Int) {
            val context = binding.root.context
            Glide.with(context)
                .load(paymentWallet.imageUrl)
                .placeholder(R.drawable.ic_bank)
                .error(R.drawable.ic_bank)
                .into(binding.ivWallet)

            binding.root.setOnClickListener {
                clickAction(paymentWallet)
            }
            binding.radioPaymentWallet.setOnClickListener {
                clickAction(paymentWallet)
            }

            if (selectedPaymentWallet == paymentWallet.order) {
                binding.llPaymentWallet.background = ContextCompat.getDrawable(
                    context, R.drawable
                        .pos_rounded_light_blue_border_rectangle
                )
                binding.radioPaymentWallet.isChecked = true
            } else {
                binding.llPaymentWallet.background = ContextCompat.getDrawable(
                    context, R.drawable
                        .pos_round_corner_light_border_rectangle
                )
                binding.radioPaymentWallet.isChecked = false
            }
        }
    }
}
