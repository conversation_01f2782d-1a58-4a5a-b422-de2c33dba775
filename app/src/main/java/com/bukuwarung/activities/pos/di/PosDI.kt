package com.bukuwarung.activities.pos.di

import com.bukuwarung.Application
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.expense.IncomeExpenseTab
import com.bukuwarung.activities.pos.*
import com.bukuwarung.activities.pos.experiments.CashPaymentFragment
import com.bukuwarung.activities.pos.experiments.NonCashPaymentFragment
import com.bukuwarung.activities.pos.experiments.PosPaymentWalletFragment
import com.bukuwarung.activities.pos.viewmodel.PosViewModelFactory
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.database.dao.ProductDao
import com.bukuwarung.domain.cash.CashUseCase
import com.bukuwarung.inventory.usecases.ProductInventory
import dagger.Module
import dagger.Provides
import dagger.android.ContributesAndroidInjector

@Module
class PosModule {
    @Provides
    fun provideViewModelFactory(
        cashUseCase: CashUseCase,
        productInventory: ProductInventory,
        productDao: ProductDao,
        productCategoryUseCase: ProductCategoryUseCase
    ): PosViewModelFactory = PosViewModelFactory(cashUseCase, productInventory, productDao, productCategoryUseCase)

    @Provides
    fun provideCashListViewModelFactory(
        application: Application,
        productInventory: ProductInventory
    ): CashListViewModel = CashListViewModel(application, productInventory)

}

@Module
abstract class PosDI {

    @ContributesAndroidInjector
    abstract fun contributePosStoreFrontFragment(): PosStoreFrontFragment

    @ContributesAndroidInjector
    abstract fun contributePosCartFragment(): PosCartFragment

    @ContributesAndroidInjector
    abstract fun contributePosPaymentFragment(): PosPaymentFragment

    @ContributesAndroidInjector
    abstract fun contributePosPaymentWalletFragment(): PosPaymentWalletFragment

    @ContributesAndroidInjector
    abstract fun contributeCashPaymentFragment(): CashPaymentFragment

    @ContributesAndroidInjector
    abstract fun contributeNonCashPaymentFragment(): NonCashPaymentFragment

    @ContributesAndroidInjector
    abstract fun contributeEditPosStockBottomSheetFragment(): EditPosStockBottomSheetFragment

    @ContributesAndroidInjector
    abstract fun contributePosActivity(): PosActivity

    @ContributesAndroidInjector
    abstract fun contributeCashTab(): IncomeExpenseTab
}