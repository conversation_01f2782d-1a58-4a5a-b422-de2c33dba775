package com.bukuwarung.activities.pos.experiments

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter

class PosPaymentViewPagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> CashPaymentFragment.getInstance()
            else -> NonCashPaymentFragment.getInstance()
        }
    }
}
