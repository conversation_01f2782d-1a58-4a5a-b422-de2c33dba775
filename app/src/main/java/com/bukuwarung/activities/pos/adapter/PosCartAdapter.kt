package com.bukuwarung.activities.pos.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.pos.model.PosProduct
import com.bukuwarung.databinding.LayoutPosProductCartItemBinding
import com.bukuwarung.utils.Utility

class PosCartAdapter(private val callback: (PosProduct) -> Unit) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val posProductList = mutableListOf<PosProduct>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder =
            ProductViewHolder(LayoutPosProductCartItemBinding.inflate(LayoutInflater.from(parent.context)))

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as ProductViewHolder).bind(posProductList[position], callback)
    }

    override fun getItemCount(): Int = posProductList.size

    inner class ProductViewHolder(val binding: LayoutPosProductCartItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(posProduct: PosProduct, callback: (PosProduct) -> Unit) {
            binding.apply {
                root.setOnClickListener { callback(posProduct) }
                tvProductName.text = posProduct.product.name.capitalize()
                tvProductCount.text = Utility.formatCurrency(posProduct.count)
                tvProductPrice.text = Utility.formatCurrency(posProduct.count * posProduct.product.unitPrice)
            }
        }
    }

    fun updateData(newPosProducts: List<PosProduct>) {
        posProductList.clear()
        posProductList.addAll(newPosProducts)
        notifyDataSetChanged()
    }
}