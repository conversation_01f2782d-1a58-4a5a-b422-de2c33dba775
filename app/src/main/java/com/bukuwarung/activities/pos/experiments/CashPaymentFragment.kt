package com.bukuwarung.activities.pos.experiments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import com.bukuwarung.R
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.pos.viewmodel.PosViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentPosCashPaymentBinding
import com.bukuwarung.utils.*
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject

class CashPaymentFragment : BaseFragment() {
    private lateinit var binding: FragmentPosCashPaymentBinding

    @Inject
    lateinit var viewModelFactory: PosViewModelFactory
    private val viewModel: PosViewModel by activityViewModels { viewModelFactory }

    private var totalAmountToPay: Double = 0.0
    private var totalBuyingPrice: Double = 0.0
    private var inputtedAmount: Double = 0.0
    private var change: Double = 0.0

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPosCashPaymentBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        initKeyboard()
        return binding.root
    }

    override fun setupView(view: View) {
        // No-op
    }

    override fun subscribeState() {
        // No-op
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        totalAmountToPay = viewModel.getSelectedProductTotalPrice()
        totalBuyingPrice = viewModel.getSelectedProductTotalBuyingPrice()

        binding.btnReceiveMoney.setOnClickListener {
            viewModel.trxType = PosPaymentWalletFragment.EXACT_AMOUNT
            closeCalculatorKeyboard()
            viewModel.posPaymentCompleteEntryPoint = AnalyticsConst.PAY_EXACT_AMOUNT_BUTTON
            binding.tvTrxSuccess.text = RemoteConfigUtils.getTrxSuccessMessage()
            InputUtils.hideKeyBoardWithCheck(activity)
            viewModel.onEventReceived(
                PosViewModel.Event.OnTransactionSave(
                    totalAmountToPay,
                    0.0,
                    totalBuyingPrice
                )
            )
        }

        binding.sellingPriceEdit.requestFocus()
        setKeyboardSimpan(false)
        binding.sellingPriceEdit.doAfterTextChanged {
            inputtedAmount = Utility.extractAmountFromText(it.toString())
            viewModel.userInputtedCustomAmount = inputtedAmount
            change = totalAmountToPay - inputtedAmount

            val isAmountSufficient = inputtedAmount >= totalAmountToPay
            binding.llChangeToGive.visibility = isAmountSufficient.asVisibility()
            setKeyboardSimpan(isAmountSufficient)

            val changeToGive = "Rp${Utility.formatCurrency(change)}"
            binding.tvChangeToGive.text = changeToGive
        }
    }

    private fun initKeyboard() {
        binding.keyboardView.visibility = View.VISIBLE
        binding.keyboardView.setResultTv(binding.sellingPriceEdit)
        binding.keyboardView.setFromPos()
        binding.keyboardView.cursor = binding.cursor
        binding.keyboardView.setCurrency(binding.currencySymbol)
        binding.keyboardView.setExprTv(binding.textAmountCalc)
        binding.keyboardView.setResultLayout(binding.resultLayout)
        binding.keyboardView.showCursor()

        binding.keyboardView.setOnSubmitListener {
            viewModel.trxType = PosViewModel.CUSTOM_AMOUNT
            closeCalculatorKeyboard()
            viewModel.posPaymentCompleteEntryPoint = AnalyticsConst.SAVE_TRANSACTION_BUTTON
            binding.tvTrxSuccess.text = RemoteConfigUtils.getTrxSuccessMessage()
            InputUtils.hideKeyBoardWithCheck(activity)
            viewModel.onEventReceived(
                PosViewModel.Event.OnTransactionSave(
                    totalAmountToPay,
                    inputtedAmount,
                    totalBuyingPrice
                )
            )
        }
    }

    private fun closeCalculatorKeyboard() {
        if (requireActivity().isAnimEnabled()) binding.keyboardView.clearAnimation()
        binding.keyboardView.visibility = View.GONE
        binding.keyboardView.cursor = binding.cursor
        binding.keyboardView.hideCursor()
    }

    private fun setKeyboardSimpan(isSet: Boolean) {
        if (isSet) {
            binding.keyboardView.submitBtn.isClickable = true
            binding.keyboardView.submitBtn.setBackgroundColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.buku_CTA
                )
            )
        } else {
            binding.keyboardView.submitBtn.isClickable = false
            binding.keyboardView.submitBtn.setBackgroundColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.black_2
                )
            )
        }
    }

    companion object {
        fun getInstance() = CashPaymentFragment()
    }
}