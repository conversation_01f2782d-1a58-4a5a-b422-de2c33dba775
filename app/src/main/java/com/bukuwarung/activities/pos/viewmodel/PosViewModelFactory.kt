package com.bukuwarung.activities.pos.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.database.dao.ProductDao
import com.bukuwarung.domain.cash.CashUseCase
import com.bukuwarung.inventory.usecases.ProductInventory
import javax.inject.Inject


class PosViewModelFactory @Inject constructor(
    private val cashUseCase: CashUseCase,
    private val productInventory: ProductInventory,
    private val productDao: ProductDao,
    private val productCategoryUseCase: ProductCategoryUseCase
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return PosViewModel(cashUseCase, productInventory, productDao, productCategoryUseCase) as T
    }

}