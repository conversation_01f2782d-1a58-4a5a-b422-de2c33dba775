package com.bukuwarung.activities.pos.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.pos.model.PosProduct
import com.bukuwarung.activities.pos.viewmodel.PosClickAction
import com.bukuwarung.activities.pos.viewmodel.PosClickEvent
import com.bukuwarung.databinding.PosProductItemBinding
import com.bukuwarung.inventory.getDisplayPrice
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import java.util.*
import kotlin.collections.ArrayList

class PosProductAdapter(
        private val posProductList: ArrayList<PosProduct>,
        private val clickEvent: (PosClickEvent) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return ProductViewHolder(PosProductItemBinding.inflate(layoutInflater))
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as ProductViewHolder).bind(posProductList[position], clickEvent)
    }

    override fun getItemCount(): Int = posProductList.size

    inner class ProductViewHolder(val binding: PosProductItemBinding) : RecyclerView.ViewHolder(binding.root) {

        @SuppressLint("SetTextI18n")
        fun bind(posProduct: PosProduct, clickEvent: (PosClickEvent) -> Unit) {
            binding.apply {
                try {
                    tvProductName.text = posProduct.product.name.capitalize()
                    if (posProduct.product.name.isNotEmpty()) {
                        tvInitial.text =
                            posProduct.product.name[0].toString().uppercase(Locale.getDefault())
                    }
                    val sellingAmount = posProduct.product.getDisplayPrice().toDouble()
                    sellingPriceTxt.text = Utility.formatCurrency(sellingAmount)
                    tvMeasurement.visibility = View.VISIBLE
                    tvMeasurement.text = "/" + posProduct.product.measurementName
                    tvStockNumber.text = Utility.getRoundedOffPrice(posProduct.count)
                    if(posProduct.product.favourite) {
                        labelFavourite.showView()
                    } else {
                        labelFavourite.hideView()
                    }
                    val stockCountVisibility = (posProduct.count > 0.0).asVisibility()
                    tvUnitName.text = posProductList[position].product.measurementName
                    tvStockNumber.visibility = stockCountVisibility
                    tvUnitName.visibility = stockCountVisibility

                    itemView.setOnClickListener {
                        clickEvent(PosClickEvent(posProduct, PosClickAction.CLICK, clickedPosition = adapterPosition))
                    }

                    llProductCount.setOnClickListener {
                        clickEvent(PosClickEvent(posProduct, PosClickAction.EDIT))
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    fun updateData(newPosProducts: List<PosProduct>) {
        posProductList.clear()
        posProductList.addAll(newPosProducts)
        notifyDataSetChanged()
    }
}