package com.bukuwarung.activities.pos.viewmodel

import androidx.lifecycle.*
import com.bukuwarung.Application
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.activities.pos.model.PosProduct
import com.bukuwarung.activities.pos.viewmodel.PosClickAction.CLICK
import com.bukuwarung.activities.pos.viewmodel.PosClickAction.EDIT
import com.bukuwarung.activities.productcategory.data.ProductCategoryUseCase
import com.bukuwarung.activities.productcategory.data.model.CategoryWithProducts
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base_android.utils.SingleLiveEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.dao.ProductDao
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.domain.cash.CashDto
import com.bukuwarung.domain.cash.CashUseCase
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.wrapper.EventWrapper
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

class PosViewModel @Inject constructor(
    private var cashUseCase: CashUseCase,
    private val productInventory: ProductInventory,
    private val productDao: ProductDao,
    private val productCategoryUseCase: ProductCategoryUseCase
) : BaseViewModel() {
    val productLiveDataMerger: MediatorLiveData<List<PosProduct>> by lazy { MediatorLiveData<List<PosProduct>>() }
    private val posProduct = mutableListOf<PosProduct>()

    private val _selectedProduct: MutableLiveData<List<PosProduct>> by lazy { MutableLiveData<List<PosProduct>>() }
    val selectedProduct: LiveData<List<PosProduct>> = _selectedProduct

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private var query: String = ""

    private var areProductsLoaded = false

    val productCategories = productCategoryUseCase.getObservableCategories()
    private val categoryIdLiveData = MutableLiveData<String?>()
    private val categorizedProduct = Transformations.switchMap(categoryIdLiveData) { categoryId ->
        areProductsLoaded = false
        productCategoryUseCase.getCategoryWithProductsObservable(categoryId)
    }

    private val productsObservable = Transformations.switchMap(categorizedProduct) { categoryWithProducts ->
        if (categoryWithProducts == null) {
            productInventory.getAllProducts()
        } else {
            val categorizedId = categoryWithProducts.products.map { it.productId }
            productInventory.getAllProducts().map { products ->
                products.filter { categorizedId.contains(it.productId) }
            }
        }
    }

    /**
     * txType can have two values "exact_amount" and "custom_amount"
     * denoting the type of transaction.
     * trxType is "exact_amount" when user presses receive money or uses non cash payment wallet
     * trxType is "custom_amount" when user uses a custom amount
     */
    var trxType: String = EXACT_AMOUNT
    var posPaymentCompleteEntryPoint: String = ""
    var userInputtedCustomAmount = 0.0

    /**
     * State variables needed for UI in POS fragments
     */
    private var selectedNonCashPaymentMethod = CASH_PAYMENT_METHOD

    init {
        loadProduct()
    }


    sealed class State {
        data class OnProductLoaded(val products: List<PosProduct>) : State()
        data class OnTransactionSaved(
            val trxId: String?,
            val uniqueProductCount: Int,
            val totalProductQty: Double
        ) : State()
    }

    fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    sealed class Event {
        data class OnProductClicked(val posClickEvent: PosClickEvent) : Event()
        object OnClearSelectedProducts : Event()
        data class OnTransactionSave(
            val totalAmount: Double,
            val customAmount: Double,
            val totalBuyingPrice: Double,
            val paymentMethod: String = CASH_PAYMENT_METHOD
        ) : Event()

        data class OnSearchProducts(val searchQuery: String) : Event()
        data class OnAddNewProduct(val productId: String) : Event()
        data class OnCategoryChanged(val categoryId: String?) : Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnProductClicked -> handleProductClicked(event.posClickEvent)
            Event.OnClearSelectedProducts -> loadProduct(onClearSelectedProducts = true)
            is Event.OnTransactionSave -> saveTransaction(
                event.totalAmount,
                event.customAmount,
                event.totalBuyingPrice,
                event.paymentMethod
            )
            is Event.OnSearchProducts -> searchProducts(event.searchQuery)
            is Event.OnAddNewProduct -> onNewProductAdded(event.productId)
            is Event.OnCategoryChanged -> updateCategoryId(event.categoryId)
        }
    }

    private fun updateCategoryId(categoryId: String?) {
        categoryIdLiveData.value = categoryId
    }

    private fun searchProducts(query: String) {
        this.query = query
        refreshProduct()
    }

    private fun saveTransaction(
        amount: Double, customAmount: Double, buyingPrice: Double,
        paymentMethod: String
    ) {
        viewModelScope.launch {
            val cat = Category(POS_CATEGORY_ID, POS_CATEGORY_NAME_EN, POS_CATEGORY_NAME_ID, 1)

            val transactionDto = arrayListOf<TransactionItemDto>()
            selectedProduct.value?.forEach { it ->
                transactionDto.add(TransactionItemDto().apply {
                    productName = it.product.name
                    productId = it.product.productId
                    quantity = it.count
                    isFavourite = it.product.favourite
                    sellingPrice = it.product.unitPrice
                    measurementUnit = it.product.measurementName
                })
            }

            val dto = CashDto(
                cat,
                null,
                1,
                Utility.getStorableDateString(Date()),
                amount,
                buyingPrice,
                "",
                null,
                transactionDto,
                customAmount,
                paymentMethod
            )

            Utility.trackTransactionCount()

            val trxId = cashUseCase.createCashTransaction(dto, -1, "", AppConst.CREDIT)
            var uniqueProductCount = 0
            val totalProductQty = getSelectedProductTotalQty()

            selectedProduct.value?.let {
                uniqueProductCount = it.size
            }

            setState(State.OnTransactionSaved(trxId, uniqueProductCount, totalProductQty))
        }
    }

    // calling this method will refresh the products to initial amount
    private fun loadProduct(onClearSelectedProducts: Boolean = false) {
        if(onClearSelectedProducts) {
            productLiveDataMerger.removeSource(productsObservable)
        }
        productLiveDataMerger.addSource(productsObservable) { products ->
            if (!areProductsLoaded || onClearSelectedProducts) {
                posProduct.apply {
                    clear()
                    addAll(products.map { PosProduct(it) })
                }
                areProductsLoaded = true
            }
            refreshProduct()
        }
    }

    private fun handleProductClicked(posClickEvent: PosClickEvent) {
        findProduct(posClickEvent.product.product.productId)?.let {
            it.count = when (posClickEvent.posClickAction) {
                CLICK -> it.count + 1
                EDIT -> posClickEvent.newCount
            }
            it.product.favourite  = when (posClickEvent.posClickAction) {
                CLICK -> posClickEvent.product.product.favourite
                EDIT -> posClickEvent.isFavourite
            }
        }
        refreshProduct()
    }

    private fun onNewProductAdded(productId: String) {
        val productEntity = findProductByProductId(productId)
        val product = PosProduct(productEntity, 1.0)
        posProduct.add(product)
        refreshProduct()
    }


    private fun refreshProduct() {
        val favouriteProducts = mutableListOf<PosProduct>()
        val nonFavouriteProducts = mutableListOf<PosProduct>()
        for(posItem in posProduct) {
            if (posItem.product.favourite) {
                favouriteProducts.add(posItem)
            } else {
                nonFavouriteProducts.add(posItem)
            }
        }
        val sortedfavouriteProducts = favouriteProducts.sortedWith(compareBy(String.CASE_INSENSITIVE_ORDER) { it.product.name })
            .filter { it.product.name.contains(query, true) }
        val sortedNonFavouriteProducts = nonFavouriteProducts.sortedWith(compareBy(String.CASE_INSENSITIVE_ORDER) { it.product.name })
            .filter { it.product.name.contains(query, true) }
        val productToPopulate = mutableListOf<PosProduct>()
        productToPopulate.addAll(sortedfavouriteProducts)
        productToPopulate.addAll(sortedNonFavouriteProducts)
        setState(State.OnProductLoaded(productToPopulate))
        populateSelectedProduct()
    }

    private fun populateSelectedProduct() {
        _selectedProduct.value = posProduct.filter { it.count > 0.0 }
    }

    private fun findProduct(productId: String): PosProduct? {
        return posProduct.find { it.product.productId == productId }
    }

    fun getSelectedProductTotalPrice(): Double {
        return selectedProduct.value?.sumByDouble { it.product.unitPrice * it.count } ?: 0.0
    }

    fun getSelectedProductTotalBuyingPrice(): Double {
        return selectedProduct.value?.sumByDouble { it.product.buyingPrice * it.count } ?: 0.0
    }

    fun getSelectedProductTotalQty(): Double {
        return selectedProduct.value?.sumByDouble { it.count } ?: 0.0
    }

    fun getSelectedUniqueProductCount(): Int {
        return selectedProduct.value?.size ?: 0
    }

    fun findProductByProductId(productId: String): ProductEntity {
        return productDao.getProductById(productId)
    }

    fun getSelectedNonCashPaymentMethod(): String {
        return selectedNonCashPaymentMethod
    }

    fun setSelectedNonCashPaymentMethod(selectedNonCashPaymentMethod: String) {
        this.selectedNonCashPaymentMethod = selectedNonCashPaymentMethod
    }

    companion object {
        const val CASH_PAYMENT_METHOD = "CASH"
        const val POS_CATEGORY_ID = "pos"
        const val POS_CATEGORY_NAME_EN = "pos"
        const val POS_CATEGORY_NAME_ID = "Kasir"

        const val EXACT_AMOUNT = "exact_amount"
        const val CUSTOM_AMOUNT = "custom_amount"
    }
}

enum class PosClickAction {
    CLICK, EDIT
}

data class PosClickEvent(
    val product: PosProduct,
    val posClickAction: PosClickAction,
    var newCount: Double = 0.0,
    val clickedPosition: Int = 0,
    var isFavourite: Boolean = false
)