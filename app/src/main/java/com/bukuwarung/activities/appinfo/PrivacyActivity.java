package com.bukuwarung.activities.appinfo;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.bukuwarung.R;

public class PrivacyActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_privacy);

        Toolbar mToolbar= findViewById(R.id.toolbar);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        TextView privacyTv = findViewById(R.id.privacy_url);
        privacyTv.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                Intent browserIntent = new Intent(
                Intent.ACTION_VIEW, Uri.parse("http://www.bukuwarung.com/privacy.html"));
                startActivity(browserIntent);
            }
        });
        TextView isDataSharedTv = findViewById(R.id.isDataSharedTv);
        isDataSharedTv.setText(getString(R.string.is_data_shared));
        TextView usedForRecoveryTv = findViewById(R.id.usedForRecovery);
        usedForRecoveryTv.setText(getString(R.string.used_for_data_recovery));
        TextView usedForSmsTv = findViewById(R.id.usedForSms);
        usedForSmsTv.setText(getString(R.string.use_for_sms_service));
        TextView storeDataTv = findViewById(R.id.storeDataTv);
        storeDataTv.setText(getString(R.string.store_data_question));
        TextView privacyUrlTv = findViewById(R.id.privacy_url);
        privacyUrlTv.setText(getString(R.string.html_privacy));
        TextView weDontShareTv = findViewById(R.id.weDontShareTv);
        weDontShareTv.setText(getString(R.string.we_dont_share_data));
        TextView titleTv = findViewById(R.id.screen_title);
        titleTv.setText(getString(R.string.privacy_policy));
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        this.finish();
    }
}
