package com.bukuwarung.activities.livelinesscheck

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.ActivityLivelinessLandingBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.setSingleClickListener
import com.google.zxing.client.android.Intents.Scan.RESULT

class LivelinessLandingActivity: BaseActivity() {
    private lateinit var binding: ActivityLivelinessLandingBinding
    private val productId by lazy { intent?.getStringExtra(PRODUCT_ID) }
    val ACTIVITY_RESULT = 100


    companion object {
        const val PRODUCT_ID = "productId"
        fun createIntent(origin: Context, productId:String?): Intent {
            val i = Intent(origin, LivelinessLandingActivity::class.java)
            i.putExtra(PRODUCT_ID, productId)
            return i
        }
    }

    override fun setViewBinding() {
        binding = ActivityLivelinessLandingBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        with(binding) {


            btnNext.setSingleClickListener {
                startActivityForResult(
                    CameraLivelinessActivity.createIntent(this@LivelinessLandingActivity, productId),
                    ACTIVITY_RESULT
                )
            }

            includeToolbar.tvTitle.text = getString(R.string.title_liveliness)
            includeToolbar.btnBack.setOnClickListener {
                onBackPressed()
            }
            includeToolbar.tvHelp.setOnClickListener {
                startActivity(
                    WebviewActivity.createIntent(
                        this@LivelinessLandingActivity,
                        RemoteConfigUtils.getPaymentConfigs().supportUrls.liveness,
                        getString(R.string.help_bantuan)
                    )
                )
            }
        }
    }


    override fun subscribeState() {
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == Activity.RESULT_OK) {
            val i = Intent()
            i.putExtra(
                CameraLivelinessActivity.RESULT,
                data?.extras?.getBoolean(CameraLivelinessActivity.RESULT)
            )

            setResult(resultCode, i)
            finish()
        }
    }

}