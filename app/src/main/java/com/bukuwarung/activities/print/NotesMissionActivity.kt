package com.bukuwarung.activities.print

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.invoice.InvoiceSettingActivity
import com.bukuwarung.activities.profile.ProfileTabFragment
import com.bukuwarung.activities.profile.ProfileTabViewModel
import com.bukuwarung.activities.profile.businessprofile.BusinessProfileWebviewActivity
import com.bukuwarung.activities.profile.businessprofile.NgBusinessProfileActivity
import com.bukuwarung.activities.profile.update.dialogs.UserProfileActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.RemoteConfigConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.ActivityNotesMissionBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject


class NotesMissionActivity : BaseActivity(), UserProfileMissionSuccessBottomSheet.Callback {

    private var businessProfileCompletionProgress: Int = 0
    private var successShown: Boolean = false
    private lateinit var notesMissionSteps: List<NotesMissionStep>
    private lateinit var binding: ActivityNotesMissionBinding
    @Inject
    lateinit var viewModel: ProfileTabViewModel
    private var userProfileTemp: UserProfileEntity? = null

    private var from : String? = ""

    companion object{
        val FROM :String = "FROM"

    }


    override fun setViewBinding() {
        binding = ActivityNotesMissionBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {

        from = intent.getStringExtra(FROM)

        viewModel.getUserProfile(User.getUserId()).observe(this, Observer {
            if (it == null) {
                if (SessionManager.getInstance().isGuestUser()
                ) {
                } else {
                    val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                    userProfileTemp = UserProfileEntity(
                        User.getUserId(),
                        if (bookEntity.hasCompletedProfileWithOwnerName()) bookEntity.businessOwnerName else getString(
                            R.string.default_owner_name
                        ),
                        User.getUserId()
                    )
                }
            } else {
                userProfileTemp = it
            }
        })


        binding.backBtn.setOnClickListener {
            finish()
        }

        binding.mbCompleteProfile.setOnClickListener {
            actionHandler(notesMissionSteps.get(0).redirection)
        }

        binding.activateNow.setOnClickListener {
            actionHandler(notesMissionSteps.get(1).redirection)
        }

        notesMissionSteps = getNotesMissionSteps()
        initFirstStep(notesMissionSteps.get(0))
        initSecondStep(notesMissionSteps.get(1))

    }

    private fun initSecondStep(step: NotesMissionStep) {
        binding.txtStatusPending2.setText(step.title)
        binding.txtAdditionalInfo2.setText(step.subText)
    }

    fun initFirstStep(step: NotesMissionStep) {
        binding.txtStatusPending1.setText(step.title)
        binding.txtAdditionalInfo1.setText(step.subText)
    }

    override fun onResume() {
        super.onResume()
        if(FeaturePrefManager.getInstance().getFeatureCompletionById(notesMissionSteps.get(0).featureId)){
            setFirstStepState(true)
        }
        val bookEntity = BusinessRepository.getInstance(this).getBusinessByIdSync(SessionManager.getInstance().businessId)
        businessProfileCompletionProgress=Utility.calculateCompletionPercentage(bookEntity)
        if(businessProfileCompletionProgress == 100) {
            FeaturePrefManager.getInstance().hasCompleteBusinessProfile(true)
        }

        if(FeaturePrefManager.getInstance().getFeatureCompletionById(notesMissionSteps.get(1).featureId)) {
            if (FeaturePrefManager.getInstance()
                    .getFeatureCompletionById(notesMissionSteps.get(0).featureId)
            ) {
                FeaturePrefManager.getInstance().hasCompletedNotesMission(true)
                showRewardState()
                if (!successShown) {
                    var entryPoint = ""
                    if(from == com.bukuwarung.activities.print.FROM.CASH_TRANSACTION_RECEIPT.name)
                        entryPoint = AnalyticsConst.NOTA
                    else if (from == com.bukuwarung.activities.print.FROM.LAINNYA_BANNER.name)
                        entryPoint = AnalyticsConst.LAINNYA_BANNER

                    val propBuilder = AppAnalytics.PropBuilder()
                    propBuilder.put(AnalyticsConst.ENTRY_POINT2,entryPoint)
                    propBuilder.put(AnalyticsConst.MISSION_NAME,AnalyticsConst.USER_BUSINESS_PROFILE_FILL_FOR_NM_REWARD)
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_MISSION_COMPLETED_POPUP, propBuilder)

                    SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_MISSION_COMPLETED_POPUP, this)

                    UserProfileMissionSuccessBottomSheet.createInstance(false, entryPoint)
                        .show(supportFragmentManager, UserProfileMissionSuccessBottomSheet.TAG)
                }
                successShown = true
            }
        }
    }

    override fun subscribeState() {
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
    }


    fun setFirstStepState(isStepComplete : Boolean) {
        if(isStepComplete){
            binding.iconStatusOk.visibility = View.VISIBLE
            binding.iconStatusPending1.visibility = View.INVISIBLE
            binding.txtAdditionalInfo1.visibility = View.GONE
            binding.mbCompleteProfile.visibility = View.GONE
            binding.txtAdditionalInfo2.visibility = View.VISIBLE
            binding.activateNow.visibility = View.VISIBLE
            if(FeaturePrefManager.getInstance().getFeatureCompletionById(notesMissionSteps.get(1).featureId)){
                showRewardState()
            }
        }
    }

    fun showRewardState() {
        binding.txtAdditionalInfo2.visibility = View.GONE
        binding.activateNow.visibility = View.GONE
        binding.iconStatusOk2.visibility = View.VISIBLE
        binding.iconStatusPending2.visibility = View.INVISIBLE
        binding.iconReward.setImageResource(R.drawable.ic_reward_active)
    }

    fun actionHandler(redirection : String){
        var entryPoint = ""
        var section = ""
        var status = ""
        var button = ""
        if(from== com.bukuwarung.activities.print.FROM.CASH_TRANSACTION_RECEIPT.name){ entryPoint = AnalyticsConst.NOTA }
        else if (from == com.bukuwarung.activities.print.FROM.INVOICE_SETTING.name) { entryPoint = AnalyticsConst.NOTA_SETTING}
        else if(from == com.bukuwarung.activities.print.FROM.LAINNYA_BANNER.name) { entryPoint = AnalyticsConst.LAINNYA_BANNER}
        if(!FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(0).featureId)){
            button = AnalyticsConst.USER_PROFILE
            section = AnalyticsConst.FIRST
        }
        else if(!FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(1).featureId))
        {
            button = AnalyticsConst.BUSINESS_PROFILE
            section = AnalyticsConst.SECOND
        }
        if(!FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(0).featureId)
            && !FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(1).featureId)
        ){
            status = AnalyticsConst.INCOMPLETE
        }
        else if(FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(0).featureId)){
            status = AnalyticsConst.FIRST_STEP_COMPLETE
        }
        else if(FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(0).featureId)
            && FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(1).featureId)){
            status = AnalyticsConst.SECOND_STEP_COMPLETE
        }

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.SECTION,section)
        propBuilder.put(AnalyticsConst.BUTTON,button)
        propBuilder.put(AnalyticsConst.MISSION_NAME,AnalyticsConst.USER_BUSINESS_PROFILE_FILL_FOR_NM_REWARD)
        propBuilder.put(AnalyticsConst.STATUS,status)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2,entryPoint)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_MISSION_CLICK, propBuilder)

        when(redirection){
            Redirections.UBAH_PROFILE.redirection -> {
                val intent = Intent(this, UserProfileActivity::class.java)
                intent.putExtra(ProfileTabFragment.USER_PROFILE_TEMP, userProfileTemp)
                intent.putExtra(ProfileTabFragment.FROM_MISSION, true)
                startActivity(intent)
            }
            Redirections.UBAH_INFORMASI_USAHA.redirection -> {
                openBusinessProfileWebView()
            }
        }
    }

    private fun openBusinessProfileWebView() {
        if(RemoteConfigUtils.getBusinessProfileVariant() == RemoteConfigConst.BUSINESS_PROFILE_VARIANT_NEW_WEB) {
            val webViewIntent = BusinessProfileWebviewActivity.createIntent(this,true)
            this.startActivity(webViewIntent)
        }else {
            val webViewIntent = NgBusinessProfileActivity.createIntent(this,User.getBusinessId(),true)
            this.startActivity(webViewIntent)
        }
    }

    override fun seeNote(entryPoint: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, entryPoint)
        propBuilder.put(
            AnalyticsConst.MISSION_NAME,
            AnalyticsConst.USER_BUSINESS_PROFILE_FILL_FOR_NM_REWARD
        )
        AppAnalytics.trackEvent(AnalyticsConst.MISSION_COMPLETED_POPUP_CLICK, propBuilder)
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.MISSION_COMPLETED_POPUP_CLICK, this)
        startActivity(InvoiceSettingActivity.createIntent(this))
    }

}

enum class FROM {
    INVOICE_SETTING,
    CASH_TRANSACTION_RECEIPT,
    LAINNYA_BANNER
}