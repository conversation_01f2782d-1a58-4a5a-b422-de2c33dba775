package com.bukuwarung.activities.print.adapter

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

class PrinterPrefManager(context: Context) {
    private val sp = context.getSharedPreferences("PINTER_PREF", Context.MODE_PRIVATE)
    private val spe = sp.edit()
    private val gson = Gson()

    var defaultDeviceAddress: String
        get() = sp.getString("PRINTER_ADDRESS", "") ?: ""
        set(value) {
            spe.putString("PRINTER_ADDRESS", value).commit()
        }

    var installedPrinters: List<PrinterDataHolder>?
        get() {
            val printers = sp.getString("INSTALLED_PRINTERS", "")
            val listType: Type = object : TypeToken<ArrayList<PrinterItem?>?>() {}.type
            val printerItemList = gson.fromJson<ArrayList<PrinterItem>>(printers, listType)
            return getPrinterHolderItemListFromPrinterItem(printerItemList)
        }
        set(printers) {
            val printerList = getPrinterItemListFromPrinterDataHolder(printers ?: emptyList())
            val printerString = gson.toJson(printerList)
            spe.putString("INSTALLED_PRINTERS", printerString).commit()
        }

    var isFirstConnect: Boolean
        get() = sp.getBoolean("IS_FIRST_CONNECT", true)
        set(value) {
            if (sp.getBoolean("IS_FIRST_CONNECT", true)) {
                spe.putBoolean("IS_FIRST_CONNECT", value).commit()
            }
        }

    // Doing this because gson cannot parse BluetoothDevice object and it may throw Runtime Exception: Unable to invoke no-args constructor for interface android.os.IBinder

    data class PrinterItem(
        var name: String,
        val macAddress: String,
        var isDefault: Boolean = false,
        var isPairing: Boolean = false
    )

    private fun getPrinterItemListFromPrinterDataHolder(printers: List<PrinterDataHolder>?): ArrayList<PrinterItem> {
        val printerItemList = ArrayList<PrinterItem>()
        printers?.forEach {
            printerItemList.add(PrinterItem(name = it.name, macAddress = it.macAddress, isDefault = it.isDefault, isPairing = it.isPairing))
        }

        return printerItemList
    }

    private fun getPrinterHolderItemListFromPrinterItem(printers: List<PrinterItem>?): ArrayList<PrinterDataHolder> {
        val printerItemList = ArrayList<PrinterDataHolder>()
        printers?.forEach {
            printerItemList.add(PrinterDataHolder(name = it.name, macAddress = it.macAddress, isDefault = it.isDefault, isPairing = it.isPairing, device = null).apply {
                this.viewType = BasePrinterDataHolder.INSTALLED_PRINTER
            })
        }
        return printerItemList
    }
}