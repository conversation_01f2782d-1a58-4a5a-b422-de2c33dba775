package com.bukuwarung.activities.print.setup

import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import android.graphics.Color
import android.util.Log
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.print.adapter.BasePrinterDataHolder
import com.bukuwarung.activities.print.adapter.PrinterAdapter
import com.bukuwarung.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.bluetooth_printer.BukuPrinter
import com.bukuwarung.bluetooth_printer.model.BluetoothDeviceDiscoveryCallBack
import com.bukuwarung.bluetooth_printer.utils.BluetoothConnection
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityBluetoothPrinterScanBinding
import com.bukuwarung.databinding.LayoutPrinterSnackbarBinding
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import com.google.android.material.snackbar.Snackbar

class BluetoothPrinterScanActivity : BaseActivity() {
    private lateinit var binding: ActivityBluetoothPrinterScanBinding
    private lateinit var printerAdapter: PrinterAdapter
    private var bluetoothConnection: BluetoothConnection? = null
    private var unrecognisedDeviceFound: Boolean = false

    private val printerCallback: (PrinterDataHolder) -> Unit = { printer ->
        if (printer.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER) {
            printer.device?.let {
                when (it.bondState) {
                    BluetoothDevice.BOND_NONE -> {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_CONNECT_PRINTER,
                            AppAnalytics.PropBuilder()
                                .put(AnalyticsConst.PRINTER_NAME, printer.name)
                                .put(AnalyticsConst.PRINTER_ID, printer.macAddress)
                        )
                        bluetoothConnection?.pair(it)
                    }
                    else -> {}
                }
            }
        }
    }

    override fun setViewBinding() {
        binding = ActivityBluetoothPrinterScanBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.noPrinterView.tvNoPrinter.text = getString(R.string.no_device_available)
    }

    override fun setupView() {
        bluetoothConnection = BluetoothConnection(this)

        binding.ivBack.setOnClickListener {
            finish()
        }

        binding.ivRefresh.setOnClickListener {
            startScan()
        }

        binding.noPrinterView.btnRefreshScanPrinter.setOnClickListener {
            startScan()
        }

        if (!::printerAdapter.isInitialized) {
            printerAdapter = PrinterAdapter(this, printerCallback, null, null)
        }

        binding.rvPrinter.apply {
            layoutManager = LinearLayoutManager(this@BluetoothPrinterScanActivity)
            adapter = printerAdapter
        }
    }

    override fun onStart() {
        super.onStart()
        initScanDeviceCallback()
        bluetoothConnection?.onStart()
        startScan()
    }

    private fun initScanDeviceCallback() {
        bluetoothConnection?.setDiscoveryCallback(object : BluetoothDeviceDiscoveryCallBack {
            override fun onDiscoveryStarted() {
                printerAdapter.setScanningStatus(true)
            }

            override fun onDiscoveryFinished() {
                printerAdapter.setScanningStatus(false)
                if (printerAdapter.isAvailablePrinterEmpty()) {
                    setUpNoDevicesAvailable()
                    triggerSearchCompleteEvent(false, unrecognisedDeviceFound)
                } else {
                    resetLayout(false)
                    triggerSearchCompleteEvent(true, unrecognisedDeviceFound)
                }
                unrecognisedDeviceFound = false
            }

            override fun onDeviceFound(device: BluetoothDevice) {
                if (device.bondState != BluetoothDevice.BOND_BONDED && isPrinterDevice(device)) {
                    binding.scanningPrinterView.layoutScanningPrinter.hideView()
                    printerAdapter.addAvailablePrinter(device)
                } else if (device.bondState != BluetoothDevice.BOND_BONDED && device.bluetoothClass.majorDeviceClass == BluetoothClass.Device.Major.UNCATEGORIZED) {
                    unrecognisedDeviceFound = true
                }
            }

            override fun onDevicePaired(device: BluetoothDevice) {
                // automatically go back to previous activity once the printer is successfully paired
                printerAdapter.addInstalledPrinter(device, null)
                triggerPairingCompleteEvent(true)

                setResult(RESULT_OK)
                finish()
            }

            override fun onDeviceUnpaired(device: BluetoothDevice) {
                val pairedPrinter = BukuPrinter.getPairedPrinter()
                if (pairedPrinter != null && pairedPrinter.address == device.address)
                    BukuPrinter.removeCurrentPrinter()
            }

            override fun onError(message: String) {
                binding.scanningPrinterView.layoutScanningPrinter.hideView()
                showSnackBarForPairingFailed()
                triggerPairingCompleteEvent(false, message)
                Log.d("BluetoothPrinterScanActivity", message)
            }
        })
    }

    private fun startScan() {
        resetLayout(true)
        bluetoothConnection?.startScanning()
        bluetoothConnection?.stopScanning(16000) // Stop scanning for devices after 16 secs
        printerAdapter.setScanningStatus(true)
        binding.scanningPrinterView.layoutScanningPrinter.showView()
        AppAnalytics.trackEvent(AnalyticsConst.SEARCHING_PRINTER_START)
    }

    private fun isPrinterDevice(device: BluetoothDevice?): Boolean {
        device?.let {
            val majorDeviceClass = device.bluetoothClass.majorDeviceClass
            val deviceClass = device.bluetoothClass.deviceClass
            val imagingDeviceCode = BluetoothClass.Device.Major.IMAGING

            return majorDeviceClass == imagingDeviceCode && (deviceClass == imagingDeviceCode || deviceClass == 1664)
        }
        return false
    }

    private fun resetLayout(clearItems: Boolean) {
        binding.rvPrinter.showView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.scanningPrinterView.layoutScanningPrinter.hideView()
        if (clearItems) {
            binding.tvBluetoothTitle.text = getString(R.string.devices_available)
        } else {
            binding.tvBluetoothTitle.text = getString(R.string.my_devices)
        }
    }

    private fun setUpNoDevicesAvailable() {
        binding.noPrinterView.layoutNoPrinter.showView()
        binding.noPrinterView.tvPrinterHint.showView()
        binding.noPrinterView.btnRefreshScanPrinter.showView()
        binding.scanningPrinterView.layoutScanningPrinter.hideView()
        binding.rvPrinter.hideView()
        binding.noPrinterView.btnAddPrinter.hideView()
    }

    override fun onStop() {
        super.onStop()
        bluetoothConnection?.stopScanning(0)
        bluetoothConnection?.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        bluetoothConnection = null
    }

    override fun subscribeState() {}

    private fun showSnackBarForPairingFailed() {
        val snackbar: Snackbar = Snackbar.make(binding.printerSnackbarGuideline, "", Snackbar.LENGTH_SHORT)
        val customSnackBinding = LayoutPrinterSnackbarBinding.inflate(layoutInflater)
        snackbar.view.setBackgroundColor(Color.TRANSPARENT)
        val snackbarLayout: Snackbar.SnackbarLayout = snackbar.view as Snackbar.SnackbarLayout
        snackbarLayout.setPadding(0, 0, 0, 0)

        val snackTV = customSnackBinding.tvSnackbarText
        snackTV.text = getString(R.string.printer_connect_failed)

        val closeIV = customSnackBinding.ivCloseSnackbar
        closeIV.setOnClickListener {
            snackbar.dismiss()
        }
        snackbarLayout.addView(customSnackBinding.root, 0)
        snackbar.show()
    }

    private fun triggerPairingCompleteEvent(isPaired: Boolean, failReason: String = "") {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PRINTER_PAIRING_COMPLETE,
            AppAnalytics.PropBuilder().put(
                AnalyticsConst.PAIRING_STATUS,
                if (isPaired) "success" else "failed"
            ).put(
                AnalyticsConst.FAIL_REASON, failReason
            )
        )
    }

    private fun triggerSearchCompleteEvent(isSuccess: Boolean, unknownDevice: Boolean) {
        AppAnalytics.trackEvent(
            AnalyticsConst.SEARCHING_PRINTER_END,
            if (isSuccess) {
                AppAnalytics.PropBuilder().put(AnalyticsConst.SEARCHING_RESULT, "success")
            } else {
                AppAnalytics.PropBuilder().put(
                    AnalyticsConst.SEARCHING_RESULT, "failed"
                ).put(
                    AnalyticsConst.FAILED_REASON, if (unknownDevice) "unknown_device" else "timeout_cannot_found"
                )
            }
        )
    }
}