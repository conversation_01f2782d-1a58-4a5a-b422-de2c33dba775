package com.bukuwarung.activities.print

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBoldAndLarge
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.setTextOrDefault
import kotlinx.android.synthetic.main.utang_receipt_layout.view.*

class UtangOldReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var bookEntity: BookEntity? = null
    private var customerEntity: CustomerEntity? = null
    private var transactionEntity: TransactionEntity? = null
    private var customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
    private var isExpanded: Boolean = false
    private var isGuestUser: Boolean = false

    init {
        View.inflate(context, R.layout.utang_receipt_layout, this)
    }

    fun setup(initializer: Initializer.() -> Unit) = Initializer(initializer)


    inner class Initializer private constructor() {
        constructor(init: Initializer.() -> Unit) : this() {
            init()
        }

        fun setBook(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun setTransaction(init: Initializer.() -> TransactionEntity?) = apply {
            transactionEntity = init()
            initialize()
        }

        fun setCustomer(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun setSummaryDto(init: Initializer.() -> CustomerTransactionSummaryDto?) = apply {
            customerTransactionSummaryDto = init()
            initialize()
        }

        fun setExpanded(init: Initializer.() -> Boolean) = apply {
            isExpanded = init()
            initialize()
        }

        fun setGuestUser(init: Initializer.() -> Boolean) = apply {
            isGuestUser = init()
            initialize()
        }
    }

    private fun initialize() {
        bookEntity?.let { book ->
            tvWarungName.text = book.businessName.takeIf { it != "Usaha Saya" } ?: "-"
            tvWarungPhone.text = Utility.beautifyPhoneNumber(book.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(book.ownerId).takeIf { it.isNotBlank() } ?: "-"
        }

        transactionEntity?.let { transaction ->
            tvTransactionDate.text = "%s %s".format(Utility.formatReceiptDate(transaction.date), Utility.getReadableTimeString(transaction.updatedAt))
            tv_transaction_type.text = context.getString(if (transaction.amount >= 0) R.string.receiving_label else R.string.giving_label)
            tv_transaction_note.setTextOrDefault(transaction.description)
            tvTransactionNominal.text = Utility.formatAmount(transaction.amount)

        }

        customerEntity?.let { customer ->
            tv_cst_name.text = customer.name
            tv_cst_phone.text = Utility.beautifyPhoneNumber(customer.phone)
        }

        customerTransactionSummaryDto?.let { dto ->
            tv_transaction_total.text = Utility.formatAmount(dto.total.toDouble())
            tv_paid_total.text = Utility.formatAmount(dto.paid.toDouble())
            tv_remaining_total.text = Utility.formatAmount(dto.remaining.toDouble())
        }

        transaction_detail_layout.visibility = isExpanded.asVisibility()
        tvWarungName.visibility = (!isGuestUser).asVisibility()
        tvWarungPhone.visibility = (!isGuestUser).asVisibility()
    }

    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        printableList.add(appendLeftWithNewLine(tvTransactionDate.text))
        printableList.add(appendDashLine())

        if (!isGuestUser) {
            printableList.addAll(
                listOf(
                    appendLeftWithNewLine(tvWarungName.text),
                    appendLeftWithNewLine(tvWarungPhone.text),
                    appendDashLine()
                )
            )
        } else {
            printableList.add(appendExtraLine())
        }

        printableList.addAll(
            listOf(
                appendLeftAndRight(context.getString(R.string.name_label), context.getString(R.string.mobile_phone_label)),
                appendLeftAndRight(tv_cst_name.text, tv_cst_phone.text),
                appendExtraLine(),
                appendLeftWithNewLine(tv_transaction_type.text),
                appendLeftWithNewLineBoldAndLarge(tvTransactionNominal.text),
                appendDashLine(),
                appendLeftWithNewLine(context.getString(R.string.label_note)),
                appendLeftWithNewLine(tv_transaction_note.text),
                appendDashLine()
            )
        )

        if (isExpanded) {
            printableList.addAll(
                listOf(
                    appendLeftAndRight(context.getString(R.string.total_utang), tv_transaction_total.text),
                    appendLeftAndRight(context.getString(R.string.total_sudah_dibayar), tv_paid_total.text),
                    appendLeftAndRight(context.getString(R.string.kurang_bayar), tv_remaining_total.text),
                    appendExtraLine(),
                    appendDashLine()
                )
            )
        } else {
            printableList.add(appendExtraLine())
        }

        printableList.addAll(
            listOf(
                appendExtraLine(),
                appendExtraLine()
            )
        )
        return printableList
    }
}