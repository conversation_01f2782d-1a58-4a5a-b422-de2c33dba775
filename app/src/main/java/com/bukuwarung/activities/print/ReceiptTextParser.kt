package com.bukuwarung.activities.print

import android.content.Context
import com.bukuwarung.R
import com.bukuwarung.activities.customer.transactiondetail.CustomerTransactionDetailState
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBoldAndLarge
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*

class ReceiptTextParser(
    private val context: Context,
    private val currentBook: BookEntity
) {
    private val warungName = currentBook.businessName.takeIf { it != "Usaha Saya" } ?: "-"
    private val warungPhone = currentBook.getFormattedPhoneNumber() ?: currentBook.getFormattedOwnerIdAsPhoneNumber() ?: "-"

    fun parseCustomerReceipt(customerEntity: CustomerEntity, transactionEntity: TransactionEntity, customerTransactionSummaryDto: CustomerTransactionSummaryDto?, isWithDetail: Boolean): ArrayList<BasePrintType> {
        val printablesList = ArrayList<BasePrintType>()
        val date = "%s %s".format(Utility.formatReceiptDate(transactionEntity.date), Utility.getReadableTimeString(transactionEntity.createdAt))

        val customerName = customerEntity.name
        val customerPhone = Utility.beautifyPhoneNumber(customerEntity.phone)
        val trxType = if (transactionEntity.amount.orNil >= 0) {
            /**Menerima*/
            context.getString(R.string.receiving_label)
        } else {
            /**Memberikan*/
            context.getString(R.string.giving_label)
        }
        val notes = transactionEntity.description.takeIf { it.isNullOrBlank().isFalse } ?: "-"
        val nominal = "${Utility.getCurrency()}${Utility.formatCurrency(transactionEntity.amount)}"

        val labelNote = context.getString(R.string.label_note)
        val labelName = context.getString(R.string.name_label)
        val labelPhone = context.getString(R.string.mobile_phone_label)

        printablesList.addAll(
            listOf(
                appendLeftWithNewLine(date),
                appendDashLine(),
                appendExtraLine()
            )
        )

        if (!SessionManager.getInstance().isGuestUser) {
            printablesList.addAll(
                listOf(
                    appendLeftWithNewLineBold(warungName),
                    appendDashLine(),
                    appendExtraLine()
                )
            )
        }

        printablesList.addAll(
            listOf(
                appendLeftAndRight(labelName, labelPhone),
                appendLeftAndRight(customerName, customerPhone),
                appendExtraLine(),
                appendLeftWithNewLine(trxType),
                appendLeftWithNewLineBoldAndLarge(nominal),
                appendExtraLine(),
                appendLeftWithNewLine(labelNote),
                appendLeftWithNewLine(notes),
                appendDashLine(),
                appendExtraLine(),
                appendExtraLine()
            )
        )

        if (isWithDetail) {
            val totalText = context.getString(R.string.total_utang)
            val paidText = context.getString(R.string.total_sudah_dibayar)
            val remainingText = context.getString(R.string.kurang_bayar)

            val total = customerTransactionSummaryDto?.total?.toDouble().asFormattedCurrency()
            val paid = customerTransactionSummaryDto?.paid?.toDouble().asFormattedCurrency()
            val remaining = customerTransactionSummaryDto?.remaining?.toDouble().asFormattedCurrency()

            printablesList.addAll(
                listOf(
                    appendLeftAndRight(totalText, total),
                    appendLeftAndRight(paidText, paid),
                    appendLeftAndRight(remainingText, remaining),
                    appendDashLine(),
                    appendExtraLine(),
                    appendExtraLine()
                )
            )
        }

        return printablesList
    }

    fun parseCustomerPaymentReceipt(state: CustomerTransactionDetailState, customerTransactionSummaryDto: CustomerTransactionSummaryDto?, isWithDetail: Boolean): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()
        val trxEntity = state.transaction ?: return printableList
        val customerEntity = state.customer ?: return printableList

        val tvTransactionDate = Utility.formatReceiptDate(trxEntity.date)
        val transactionTime = Utility.getReadableTimeString(trxEntity.createdAt)

        val customerName = customerEntity.name
        val customerPhone = Utility.beautifyPhoneNumber(customerEntity.phone).takeIf { it.isNotBlank() } ?: "-"
        val nominal = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(trxEntity.amount))

        var invoiceNumber = ""
        val labelFreeVisible = AppConfigManager.getInstance().paymentFreeChargeStatus
        var senderName = ""
        var recipientName = ""
        var recipientNumber = ""
        var note = ""

        // static texts
        val labelNote = context.getString(R.string.label_note)
        val labelName = context.getString(R.string.name_label)
        val labelPhone = context.getString(R.string.mobile_phone_label)
        val labelSuccess = context.getString(R.string.transaction_success_label)
        val labelFree = context.getString(R.string.free_charge_label)
        val labelSender = context.getString(R.string.sender)
        val labelRecipient = context.getString(R.string.penerima)
        val footerText = context.getString(R.string.label_transaction_security_guaranteed_plain)

        val amount = state.transaction.amount ?: 0.0
        if (amount > 0) {
            // payment in
            state.paymentCollection?.let {
                // set transaction ID
                invoiceNumber = it.transactionId ?: "-"
                // set sender name (customer name)
                senderName = "%s - %s".format(it.paymentChannel, customerEntity.name.takeIf { n -> n.isNotEmpty() } ?: "-")
                // set receiver name
                recipientName = "%s - %s".format(
                    it.receiverBank?.bankCode
                        ?: "-", it.receiverBank?.accountHolderName ?: "-"
                )
                // set receiver bank no
                recipientNumber = it.receiverBank?.accountNumber ?: "-"
                // set payment notes
                note = (it.description ?: "").let { str ->
                    if (str.isBlank() || str == "-") {
                        context.getString(R.string.label_payment_in_note_plain)
                    } else {
                        "%s - %s".format(context.getString(R.string.label_payment_in_note_plain), str)
                    }
                }

            }
        } else {
            // payment out
            state.paymentDisbursement?.let {
                // set transaction ID
                invoiceNumber = it.transactionId ?: "-"
                // set sender name (merchant name)
                senderName = "%s - %s".format(
                    it.paymentChannel, currentBook.businessOwnerName
                        ?: "-"
                )
                // set receiver name
                recipientName = "%s - %s".format(
                    it.receiverBank?.bankCode
                        ?: "-", it.receiverBank?.accountHolderName ?: "-"
                )
                // set receiver bank no
                recipientNumber = it.receiverBank?.accountNumber ?: "-"
                // set payment notes
                note = (it.description ?: "").let { str ->
                    if (str.isBlank() || str == "-") {
                        context.getString(R.string.label_payment_in_note_plain)
                    } else {
                        "%s - %s".format(context.getString(R.string.label_payment_in_note_plain), str)
                    }
                }
            }
        }

        printableList.addAll(
            listOf(
                appendLeftAndRight(tvTransactionDate, invoiceNumber),
                appendLeftWithNewLine(transactionTime),
                appendDashLine(),
                appendExtraLine(),
                appendLeftWithNewLineBold(warungName),
                appendLeftWithNewLine(warungPhone),
                appendDashLine(),
                appendLeftAndRight(labelName, labelPhone),
                appendLeftAndRight(customerName, customerPhone),
                appendExtraLine(),
                appendLeftWithNewLine(labelSuccess),
                appendLeftWithNewLineBold(nominal),
                appendExtraLine()
            )
        )

        if (labelFreeVisible) {
            printableList.addAll(
                listOf(
                    appendLeftWithNewLine(labelFree),
                    appendExtraLine()
                )
            )
        }

        printableList.addAll(
            listOf(
                appendExtraLine(),
                appendLeftWithNewLine(labelSender),
                appendLeftWithNewLine(senderName),
                appendExtraLine(),
                appendLeftWithNewLine(labelRecipient),
                appendLeftWithNewLine(recipientName),
                appendLeftWithNewLine(recipientNumber),
                appendExtraLine(),
                appendDashLine(),
                appendLeftWithNewLine(labelNote),
                appendLeftWithNewLine(note),
                appendExtraLine(),
                appendDashLine(),
                appendExtraLine()
            )
        )

        if (isWithDetail) {
            val totalText = context.getString(R.string.total_utang)
            val paidText = context.getString(R.string.total_sudah_dibayar)
            val remainingText = context.getString(R.string.kurang_bayar)

            val total = customerTransactionSummaryDto?.total?.toDouble().asFormattedCurrency()
            val paid = customerTransactionSummaryDto?.paid?.toDouble().asFormattedCurrency()
            val remaining = customerTransactionSummaryDto?.remaining?.toDouble().asFormattedCurrency()

            printableList.addAll(
                listOf(
                    appendLeftAndRight(totalText, total),
                    appendLeftAndRight(paidText, paid),
                    appendLeftAndRight(remainingText, remaining),
                    appendDashLine(),
                    appendExtraLine()
                )
            )
        }

        printableList.addAll(
            listOf(
                appendCenterWithNewLine(footerText),
                appendExtraLine(),
                appendExtraLine()
            )
        )

        return printableList
    }

}