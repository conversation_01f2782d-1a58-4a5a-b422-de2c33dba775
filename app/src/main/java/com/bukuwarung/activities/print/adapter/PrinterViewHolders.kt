package com.bukuwarung.activities.print.adapter

import android.bluetooth.BluetoothDevice
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.databinding.PairedPrinterItemBinding
import com.bukuwarung.databinding.PrinterItemBinding
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView


abstract class BasePrinterDataHolder {
    var viewType: Int = 0

    companion object TAG {
        // keep the order for easier sorting
        const val INSTALLED_PRINTER = 2
        const val AVAILABLE_PRINTER = 4
    }
}

data class PrinterDataHolder(
    var name: String,
    val macAddress: String,
    var device: BluetoothDevice? = null,
    var isDefault: Boolean = false,
    var isPairing: Boolean = false
) : BasePrinterDataHolder()

class AvailablePrinterViewHolder(private val binding: PrinterItemBinding) : RecyclerView.ViewHolder(binding.root) {

    fun bind(printer: PrinterDataHolder, callBack: (PrinterDataHolder) -> Unit) {
        binding.root.apply {
            setOnClickListener {
                callBack(printer)
                binding.tvConnect.hideView()
                binding.progressBarPairPrinter.showView()
            }
            binding.tvPrinterName.text = printer.name
            binding.tvPrinterAddress.text = printer.macAddress
        }
    }
}

class InstalledPrinterViewHolder(private val binding: PairedPrinterItemBinding) : RecyclerView.ViewHolder(binding.root) {

    fun bind(printer: PrinterDataHolder, testCallBack: (PrinterDataHolder) -> Unit, settingCallBack: (PrinterDataHolder) -> Unit) {
        binding.root.apply {
            binding.tvPrinterName.text = printer.name
            binding.tvPrinterAddress.text = printer.macAddress
        }
        binding.tvTestPrinter.setOnClickListener {
            testCallBack(printer)
        }

        binding.ivSetting.setOnClickListener {
            settingCallBack(printer)
        }
    }
}