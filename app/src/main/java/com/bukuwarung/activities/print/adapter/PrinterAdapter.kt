package com.bukuwarung.activities.print.adapter

import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.databinding.PairedPrinterItemBinding
import com.bukuwarung.databinding.PrinterItemBinding

class PrinterAdapter(
    context: Context,
    private val printerCallback: ((PrinterDataHolder) -> Unit)?,
    private val testPrinterCallBack: ((PrinterDataHolder) -> Unit)?,
    private val settingPrinterCallBack: ((PrinterDataHolder) -> Unit)?
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val btAdapter = (context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager).adapter
    private val pref = PrinterPrefManager(context)
    private val data = mutableListOf<BasePrinterDataHolder>()
    private fun printers() = data
        .filter { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER || it.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER }
        .map { it as PrinterDataHolder }

    fun addInstalledPrinter(btDevice: BluetoothDevice, updatedName: String?) {
        val isPrinterPresent = printers().firstOrNull { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER && it.macAddress == btDevice.address }
        if (isPrinterPresent == null) {
            val installedPrinter = PrinterDataHolder(updatedName ?: btDevice.name, btDevice.address, isPairing = false, device = btDevice).apply {
                viewType = BasePrinterDataHolder.INSTALLED_PRINTER
            }
            data.add(installedPrinter)
            saveInstalledPrinters()
        }
    }

    fun showInstalledPrinters() {
        data.clear()
        val installedPrinters = pref.installedPrinters
        installedPrinters?.forEach { printer ->
            printer.viewType = BasePrinterDataHolder.INSTALLED_PRINTER
            printer.device = getDevice(printer.macAddress)
            data.add(printer)
        }
        getInstalledPrinterDeviceList().forEach { btDevice ->
            addInstalledPrinter(btDevice, null)
        }
        notifyDataSetChanged()
    }

    fun getDevice(address: String): BluetoothDevice? {
        if (!btAdapter.isEnabled) btAdapter.enable()
        val pairedDevices: Set<BluetoothDevice> = btAdapter.bondedDevices
        for (d in pairedDevices) if (d.address.equals(address, ignoreCase = true)) return d
        return null
    }

    private fun getInstalledPrinterDeviceList(): ArrayList<BluetoothDevice> {
        val installedPrinterList = ArrayList<BluetoothDevice>()
        if (!btAdapter.isEnabled) btAdapter.enable()
        val pairedDevices: Set<BluetoothDevice> = btAdapter.bondedDevices
        for (d in pairedDevices) {
            if (isPrinterDevice(d)) {
                installedPrinterList.add(d)
            }
        }
        return installedPrinterList
    }

    private fun isPrinterDevice(device: BluetoothDevice?): Boolean {
        if (device?.name.isNullOrBlank() || device?.address.isNullOrBlank()) {
            return false
        }
        device?.let {
            val majorDeviceClass = device.bluetoothClass.majorDeviceClass
            val deviceClass = device.bluetoothClass.deviceClass
            val imagingDeviceCode = BluetoothClass.Device.Major.IMAGING
            val uncategorizedDeviceCode = BluetoothClass.Device.Major.UNCATEGORIZED

            if (majorDeviceClass == imagingDeviceCode && (deviceClass == imagingDeviceCode || deviceClass == 1664)) {
                return true
            }
            // Hack to recognise the bluetooth/thermal printer, sometimes Bluetooth SIG doesn't assign the device class, so banking on name contains Printer
            else if (majorDeviceClass == uncategorizedDeviceCode && deviceClass == uncategorizedDeviceCode && (device.name.contains("print", true) || device.name.contains("pencetak", true))) {
                return true
            } else if (device.name.contains("printer", true) || device.name.contains("pencetak", true)) {
                return true
            }
        }
        return false
    }

    private fun saveInstalledPrinters() {
        val installedPrinterList = printers().filter { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER }
        pref.installedPrinters = installedPrinterList
    }

    fun updatePrinterName(printer: PrinterDataHolder, updatedName: String) {
        getDevice(printer.macAddress)?.let {
            removeInstalledPrinter(printer)
            addInstalledPrinter(it, updatedName)
        }
    }

    fun removeInstalledPrinter(printer: PrinterDataHolder) {
        val basePrinterDataHolder = printer.apply {
            viewType = BasePrinterDataHolder.INSTALLED_PRINTER
        }
        data.remove(basePrinterDataHolder)
        notifyDataSetChanged()
        saveInstalledPrinters()
    }

    fun addAvailablePrinter(btDevice: BluetoothDevice) {
        if (btDevice.name == null || btDevice.address == null) {
            return
        }

        // check if the device is already added
        val existingPrinter = printers().find { it.macAddress == btDevice.address }
        if (existingPrinter != null) return

        val newPrinter = PrinterDataHolder(btDevice.name, btDevice.address, isPairing = false, device = btDevice).apply {
            viewType = BasePrinterDataHolder.AVAILABLE_PRINTER
        }
        data.add(newPrinter)
        notifyDataSetChanged()
    }

    fun setScanningStatus(isScanning: Boolean) {
        if (isScanning) {
            // remove all discovered printer
            printers().filter { it.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER }
                .forEach { data.remove(it) }
        }
    }

    fun isAvailablePrinterEmpty() = printers().none { it.viewType == BasePrinterDataHolder.AVAILABLE_PRINTER }

    fun isInstalledPrinterEmpty() = printers().none { it.viewType == BasePrinterDataHolder.INSTALLED_PRINTER }

    override fun getItemViewType(position: Int): Int = data[position].viewType

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            BasePrinterDataHolder.AVAILABLE_PRINTER -> {
                val binding = PrinterItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                AvailablePrinterViewHolder(binding)
            }
            BasePrinterDataHolder.INSTALLED_PRINTER -> {
                val binding = PairedPrinterItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                InstalledPrinterViewHolder(binding)
            }
            else -> {
                val binding = PrinterItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                AvailablePrinterViewHolder(binding)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val singleData = data[position]) {
            is PrinterDataHolder -> {
                if (holder is InstalledPrinterViewHolder) {
                    if (testPrinterCallBack != null && settingPrinterCallBack != null) {
                        holder.bind(singleData, testPrinterCallBack, settingPrinterCallBack)
                    }
                } else {
                    if (printerCallback != null) {
                        (holder as AvailablePrinterViewHolder).bind(singleData, printerCallback)
                    }
                }
            }
        }
    }

    override fun getItemCount(): Int = data.size
}