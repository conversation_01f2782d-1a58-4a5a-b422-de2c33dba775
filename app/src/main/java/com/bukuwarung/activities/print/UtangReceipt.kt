package com.bukuwarung.activities.print

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLineBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRightBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendRightWithNewLine
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.utils.*
import kotlinx.android.synthetic.main.item_invoice.view.*
import kotlinx.android.synthetic.main.layout_body_invoice.view.*
import kotlinx.android.synthetic.main.layout_footer_invoice.view.*
import kotlinx.android.synthetic.main.layout_header_invoice.view.*
import kotlinx.android.synthetic.main.layout_store_detail.view.*
import kotlinx.android.synthetic.main.layout_utang_invoice.view.*

class UtangReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var bookEntity: BookEntity? = null
    private var invoiceDataBlock: InvoiceDataBlock? = null
    private var customerEntity: CustomerEntity? = null
    private var transactionEntity: TransactionEntity? = null
    private var customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
    private var isExpanded: Boolean = false
    private var isGuestUser: Boolean = false

    init {
        View.inflate(context, R.layout.layout_utang_invoice, this)
    }

    fun setup(initializer: Initializer.() -> Unit) = Initializer(initializer)


    inner class Initializer private constructor() {
        constructor(init: Initializer.() -> Unit) : this() {
            init()
        }

        fun setInvoiceData(init: Initializer.() -> InvoiceDataBlock?) = apply {
            invoiceDataBlock = init()
            initialize()
        }

        fun setBook(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun setTransaction(init: Initializer.() -> TransactionEntity?) = apply {
            transactionEntity = init()
            initialize()
        }

        fun setCustomer(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun setSummaryDto(init: Initializer.() -> CustomerTransactionSummaryDto?) = apply {
            customerTransactionSummaryDto = init()
            initialize()
        }

        fun setExpanded(init: Initializer.() -> Boolean) = apply {
            isExpanded = init()
            initialize()
        }

        fun setGuestUser(init: Initializer.() -> Boolean) = apply {
            isGuestUser = init()
            initialize()
        }
    }

    private fun initialize() {
        bookEntity?.let { book ->
            layout_store_detail.tv_store_name.text =
                book.businessName.takeIf { it != "Usaha Saya" } ?: "-"
            layout_store_detail.tv_store_phone.text =
                Utility.beautifyPhoneNumber(book.businessPhone).takeIf { it.isNotBlank() }
                    ?: Utility.beautifyPhoneNumber(book.ownerId).takeIf { it.isNotBlank() } ?: "-"
            layout_store_detail.tv_store_address.text = book.businessAddress
        }

        with(layout_header_invoice) {
            tv_date.tv_key.text = context.getString(R.string.date)
            tv_note_code.tv_key.text = context.getString(R.string.kode_nota)
            tv_customer_details.tv_key.text = context.getString(R.string.customers)
        }

        with(layout_body_invoice) {
            tv_insufficient_payment.tv_key.text = context.getString(R.string.remain_debt)
            customerTransactionSummaryDto?.let { dto ->
                if (dto.total >= dto.paid) {
                    tv_insufficient_payment.tv_value.text =
                        Utility.formatAmount(dto.total.toDouble() - dto.paid.toDouble())
                } else {
                    tv_insufficient_payment.tv_value.text =
                        "-".plus(Utility.formatAmount(dto.total.toDouble() - dto.paid.toDouble()))
                }
            }
        }

        transactionEntity?.let { transaction ->
            layout_header_invoice.tv_date.tv_value.text = "%s %s".format(Utility.formatReceiptDate(transaction.date), Utility.getReadableTimeString(transaction.updatedAt))
            layout_footer_invoice.tv_notes_txt.setTextOrDefault(transaction.description)
            layout_body_invoice.tv_amount_given.text = Utility.formatAmount(transaction.amount)
            if (transaction.amount >= 0.0) {
                layout_body_invoice.tv_transaction_type_utang.text = context.getString(R.string.debt_paid)
            } else {
                layout_body_invoice.tv_transaction_type_utang.text = context.getString(R.string.debt_given)
            }
        }

        customerEntity?.let { customer ->
            layout_header_invoice.tv_customer_details.tv_value.text =
                customer.name.plus(System.lineSeparator()).plus(Utility.beautifyPhoneNumber(customer.phone))
        }
        handleStoreDetailVisibility(
            invoiceDataBlock?.data?.getOrNull(0)?.storeDetailData?.getOrNull(
                0
            )
        )
        handleHeaderDataVisibility(
            invoiceDataBlock?.data?.getOrNull(0)?.headerData?.getOrNull(
                0
            )
        )
        handleBodyDataVisibility(
            invoiceDataBlock?.data?.getOrNull(0)?.bodyData?.getOrNull(
                0
            )
        )
        handleFooterDataVisibility(
            invoiceDataBlock?.data?.getOrNull(0)?.footerData?.getOrNull(
                0
            )
        )
        with(layout_body_invoice) {
            tv_insufficient_payment.visibility = isExpanded.asVisibility()
        }
//        tvWarungName.visibility = (!isGuestUser).asVisibility()
//        tvWarungPhone.visibility = (!isGuestUser).asVisibility()
    }

    private fun handleFooterDataVisibility(footerDataItem: FooterDataItem?) {
        with(layout_footer_invoice) {
            if (footerDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (footerDataItem?.elements?.getOrNull(0)?.notes?.visibility.isFalseOrNull || transactionEntity?.description.isNullOrBlank()) {
                tv_notes.hideView()
                tv_notes_txt.hideView()
            } else {
                tv_notes.showView()
                tv_notes_txt.showView()
            }
            if (footerDataItem?.elements?.getOrNull(0)?.bukuAd?.visibility.isFalseOrNull) {
                bukuwarung_ad_layout.hideView()
            } else {
                bukuwarung_ad_layout.showView()
            }
        }
    }

    private fun handleBodyDataVisibility(bodyDataItem: BodyDataItem?) {
        with(layout_body_invoice) {
            if (bodyDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.insufficientPayment?.visibility.isFalseOrNull) {
                tv_insufficient_payment.hideView()
            } else {
                tv_insufficient_payment.showView()
            }
        }
    }

    private fun handleHeaderDataVisibility(headerDataItem: HeaderDataItem?) {
        with(layout_header_invoice) {
            if (headerDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (headerDataItem?.elements?.getOrNull(0)?.customer?.visibility.isFalseOrNull) {
                tv_customer_details.hideView()
            } else {
                tv_customer_details.showView()
            }
            if (headerDataItem?.elements?.getOrNull(0)?.noteCode?.visibility.isFalseOrNull) {
                tv_note_code.hideView()
            } else {
                tv_note_code.showView()
            }
        }
    }

    private fun handleStoreDetailVisibility(storeDetailDataItem: StoreDetailDataItem?) {
        with(layout_store_detail) {
            if (storeDetailDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.icon?.visibility.isFalseOrNull) {
                iv_logo.hideView()
            } else {
                iv_logo.showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.address?.visibility.isFalseOrNull || bookEntity?.businessAddress.isNullOrEmpty()) {
                tv_store_address.hideView()
            } else {
                tv_store_address.showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.phone?.visibility.isFalseOrNull) {
                tv_store_phone.hideView()
            } else {
                tv_store_phone.showView()
            }
        }
    }

    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        // TODO: Add Logo for premium

        // Store Details
        val storeDetailDataItem = invoiceDataBlock?.data?.getOrNull(0)?.storeDetailData?.getOrNull(0)
        if (storeDetailDataItem?.visibility.isTrue) {
            printableList.add(appendCenterWithNewLineBold(layout_store_detail.tv_store_name.text))
            if (storeDetailDataItem?.elements?.getOrNull(0)?.address?.visibility.isTrue && bookEntity?.businessAddress.isNotNullOrEmpty()) {
                printableList.add(appendCenterWithNewLine(layout_store_detail.tv_store_address.text))
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.phone?.visibility.isTrue) {
                printableList.add(appendCenterWithNewLine(layout_store_detail.tv_store_phone.text))
            }
            printableList.add(appendExtraLine())
        }

        //Header Details
        val headerDataItem = invoiceDataBlock?.data?.getOrNull(0)?.headerData?.getOrNull(0)
        if (headerDataItem?.visibility.isTrue) {
            printableList.add(appendLeftAndRight(layout_header_invoice.tv_date.tv_key.text, layout_header_invoice.tv_date.tv_value.text))
            if (headerDataItem?.elements?.getOrNull(0)?.noteCode?.visibility.isTrue) {
                printableList.add(appendLeftAndRight(layout_header_invoice.tv_note_code.tv_key.text, layout_header_invoice.tv_note_code.tv_value.text))
            }
            if (headerDataItem?.elements?.getOrNull(0)?.customer?.visibility.isTrue) {
                printableList.add(appendLeftAndRight(layout_header_invoice.tv_customer_details.tv_key.text, customerEntity?.name))
                printableList.add(appendRightWithNewLine(Utility.beautifyPhoneNumber(customerEntity?.phone)))
            }
        }

        printableList.add(appendDashLine())
        printableList.add(appendLeftAndRightBold(tv_transaction_type_utang.text, tv_amount_given.text))
        printableList.add(appendDashLine())

        // Body Details
        val bodyDataItem = invoiceDataBlock?.data?.getOrNull(0)?.bodyData?.getOrNull(0)
        if (bodyDataItem?.visibility.isTrue && isExpanded) {
            if (bodyDataItem?.elements?.getOrNull(0)?.insufficientPayment?.visibility.isTrue) {
                printableList.add(appendLeftAndRight(tv_insufficient_payment.tv_key.text, tv_insufficient_payment.tv_value.text))
            }
            printableList.add(appendExtraLine())
        }

        // Footer Details
        val footerDataItem = invoiceDataBlock?.data?.getOrNull(0)?.footerData?.getOrNull(0)
        if (footerDataItem?.visibility.isTrue) {
            if (footerDataItem?.elements?.getOrNull(0)?.notes?.visibility.isTrue && !layout_footer_invoice.tv_notes_txt.text.isNullOrBlank()) {
                printableList.add(appendLeftWithNewLine(layout_footer_invoice.tv_notes.text))
                printableList.add(appendLeftWithNewLine(layout_footer_invoice.tv_notes_txt.text))
                printableList.add(appendExtraLine())
            }

            if (footerDataItem?.elements?.getOrNull(0)?.bukuAd?.visibility.isTrue) {
                printableList.add(appendCenterWithNewLine(tv_footer_one.text))
                printableList.add(appendCenterWithNewLine(tv_bukuwarung_url.text))
                printableList.add(appendExtraLine())
            }
        }

        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        return printableList
    }
}