package com.bukuwarung.activities.print

import com.google.gson.annotations.SerializedName

data class InvoiceDataBlock(

	@SerializedName("body_block_name")
	val bodyBlockName: String? = null,

	@SerializedName("data")
	val data: List<DataItem>? = null,

	@SerializedName("id")
	val id: String? = null
)

data class NoteCode(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class InsufficientPayment(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class Cashier(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class TableNumber(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class Order(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class TypeOfPayment(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class BodyDataItem(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("elements")
	val elements: List<ElementsItem>? = null,

	@SerializedName("display_name")
	val displayName: String? = null,

	@SerializedName("analytics_name")
	val analyticsName: String? = null,

	@SerializedName("invoice_name")
	val invoiceName: String? = null
)

data class TransactionDate(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class ElementsItem(

	@SerializedName("date")
	val transactiondate: TransactionDate? = null,

	@SerializedName("note_code")
	val noteCode: NoteCode? = null,

	@SerializedName("customer")
	val customer: Customer? = null,

	@SerializedName("address")
	val address: Address? = null,

	@SerializedName("phone")
	val phone: Phone? = null,

	@SerializedName("icon")
	val icon: Icon? = null,

	@SerializedName("title")
	val title: Title? = null,

	@SerializedName("promo")
	val promo: Promo? = null,

	@SerializedName("buku_ad")
	val bukuAd: BukuAd? = null,

	@SerializedName("notes")
	val notes: Notes? = null,

	@SerializedName("social_media")
	val socialMedia: SocialMedia? = null,

	@SerializedName("total_debt")
	val totalDebt: TotalDebt? = null,

	@SerializedName("total_paid")
	val totalPaid: TotalPaid? = null,

	@SerializedName("sub_total")
	val subTotal: InvoiceElementItem? = null,

	@SerializedName("discount")
	val discount: InvoiceElementItem? = null,

	@SerializedName("tax")
	val tax: InvoiceElementItem? = null,

	@SerializedName("total")
	val total: InvoiceElementItem? = null,

	@SerializedName("cash")
	val cash: InvoiceElementItem? = null,

	@SerializedName("return")
	val returnAmout: InvoiceElementItem? = null,

	@SerializedName("amount_given")
	val amountGiven: AmountGiven? = null,

	@SerializedName("insufficient_payment")
	val insufficientPayment: InsufficientPayment? = null,

	@SerializedName("cashier")
    val cashier: Cashier? = null,

	@SerializedName("table_number")
	val tableNumber: TableNumber? = null,

	@SerializedName("order")
	val order: Order? = null,

	@SerializedName("type_of_payment")
	val typeOfPayment: TypeOfPayment? = null
)

data class Title(

	@SerializedName("visibility")
	val visibility: Boolean? = null
)

data class Notes(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class FooterDataItem(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("elements")
	val elements: List<ElementsItem>? = null,

	@SerializedName("display_name")
	val displayName: String? = null,

	@SerializedName("analytics_name")
	val analyticsName: String? = null,

	@SerializedName("invoice_name")
	val invoiceName: String? = null
)

data class TotalPaid(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class InvoiceElementItem(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class Promo(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class TotalDebt(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class Customer(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class Phone(

	@SerializedName("visibility")
	val visibility: Boolean? = null
)

data class TransactionStatusItem(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("display_name")
	val displayName: String? = null,

	@SerializedName("analytics_name")
	val analyticsName: String? = null,

	@SerializedName("invoice_name")
	val invoiceName: String? = null
)

data class Icon(

	@SerializedName("visibility")
	val visibility: Boolean? = null
)

data class DataItem(

	@SerializedName("header_data")
	val headerData: List<HeaderDataItem>? = null,

	@SerializedName("transaction_status")
	val transactionStatus: List<TransactionStatusItem>? = null,

	@SerializedName("store_detail_data")
	val storeDetailData: List<StoreDetailDataItem>? = null,

	@SerializedName("footer_data")
	val footerData: List<FooterDataItem>? = null,

	@SerializedName("body_data")
	val bodyData: List<BodyDataItem>? = null
)

data class HeaderDataItem(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("elements")
	val elements: List<ElementsItem>? = null,

	@SerializedName("display_name")
	val displayName: String? = null,

	@SerializedName("analytics_name")
	val analyticsName: String? = null,

	@SerializedName("invoice_name")
	val invoiceName: String? = null
)

data class Address(

	@SerializedName("visibility")
	val visibility: Boolean? = null
)

data class AmountGiven(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class BukuAd(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class SocialMedia(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("key")
	val key: String? = null
)

data class StoreDetailDataItem(

	@SerializedName("visibility")
	val visibility: Boolean? = null,

	@SerializedName("elements")
	val elements: List<ElementsItem>? = null,

	@SerializedName("display_name")
	val displayName: String? = null,

	@SerializedName("analytics_name")
	val analyticsName: String? = null,

	@SerializedName("invoice_name")
	val invoiceName: String? = null
)
