package com.bukuwarung.activities.print

import android.content.Context
import android.text.Html
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLineBoldAndLarge
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLineBoldAndLarge
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.PaymentReceiptDto
import com.bukuwarung.payments.data.model.ReceiverBank
import com.bukuwarung.utils.*
import kotlinx.android.synthetic.main.utang_payment_receipt_layout.view.*

class PaymentReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var bookEntity: BookEntity? = null
    private var customerEntity: CustomerEntity? = null
    private var receiverBank: ReceiverBank? = null
    private var paymentReceiptDto: PaymentReceiptDto? = null
    private var finproOrderResponse: FinproOrderResponse? = null
    private var userProfileEntity: UserProfileEntity? = null

    init {
        View.inflate(context, R.layout.utang_payment_receipt_layout, this)
    }

    fun setup(initializer: Initializer.() -> Unit) = Initializer(initializer)

    inner class Initializer private constructor() {
        constructor(init: PaymentReceipt.Initializer.() -> Unit) : this() {
            init()
        }

        fun setBook(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun setCustomer(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun setReceiverBank(init: Initializer.() -> ReceiverBank?) = apply {
            receiverBank = init()
            initialize()
        }

        fun setDto(init: Initializer.() -> PaymentReceiptDto?, userProfile: UserProfileEntity?) = apply {
            paymentReceiptDto = init()
            userProfileEntity = userProfile
            initialize()
        }

        fun setFinproOrder(init: Initializer.() -> FinproOrderResponse?) = apply {
            finproOrderResponse = init()
            initialize()
        }
    }

    private fun initialize() {
        bookEntity?.let { book ->
            tvWarungName.text = book.businessName.takeIf { it != "Usaha Saya" } ?: "-"
            tvWarungPhone.text = Utility.beautifyPhoneNumber(book.businessPhone)

        }

        customerEntity?.let { customer ->
            tv_cst_name.setTextOrDefault(customer.name)
            tv_cst_phone.setTextOrDefault(Utility.beautifyPhoneNumber(customer.phone))
        }

        paymentReceiptDto?.let { dto ->
            tvTransactionDate.text = Utility.formatReceiptDateFromHistory(dto.completedStatusDate, true)
            tv_invoice_number.text = dto.transactionId
            tvTransactionNominal.text = Utility.formatAmount(dto.amount)
            tv_free_charge.visibility = dto.paymentFreeChargeStatus.asVisibility()
            if (dto.ppobCategory == PpobConst.CATEGORY_LISTRIK && dto.categoryCode != PpobConst.CATEGORY_PLN_POSTPAID) {
                note_hint.visibility = View.VISIBLE
                layout_payment_catatan.visibility = View.VISIBLE
                tv_number.text = dto.ppobItem?.details?.token
                tv_pdt_name.text = context.getString(R.string.token_code)
            } else if (dto.ppobCategory == PpobConst.CATEGORY_VOUCHER_GAME && dto.ppobItem?.details?.voucherCode.isNotNullOrBlank()) {
                layout_payment_catatan.visibility = View.VISIBLE
                layout_payment_catatan.setBackgroundColor(ContextCompat.getColor(context, R.color.alice_blue))
                tv_number.text = dto.ppobItem?.details?.voucherCode
                tv_pdt_name.text = context.getString(R.string.code_voucher)
                note_hint.showView()
                note_hint.text = context.getString(R.string.voucher_code_info)
            } else {
                layout_payment_catatan.visibility = View.GONE
                note_hint.visibility = View.GONE
            }

            receiverBank?.let { bank ->
                // In case of ayment In, show info in following format
                // [Bank code]-[Business Name]-[merchant bank Name]
                if (dto.isPaymentIn && bookEntity?.businessName.isNotNullOrBlank()) {
                    tv_recipient_name.text = context.getString(
                        R.string.three_dashed_strings,
                        bank.bankCode,
                        bookEntity?.businessName,
                        bank.accountHolderName
                    )
                } else {
                    tv_recipient_name.text = context.getString(
                        R.string.two_dashed_strings,
                        bank.bankCode, bank.accountHolderName
                    )
                }
                tv_recipient_aaccount_number.text = bank.accountNumber
            }

            tv_sender_name.text = if (dto.isPaymentIn) {
                context.getString(
                    R.string.two_dashed_strings,
                    dto.paymentChannel, customerEntity?.name
                )
            } else {
                // In case of Payment Out, show info in the following format
                // [Bank code]-[Business Name]-[Business Owner Name]
                val businessOwnerName = when {
                    userProfileEntity?.userName.isNotNullOrBlank() -> {
                        userProfileEntity?.userName
                    }
                    bookEntity?.businessOwnerName.isNotNullOrBlank() -> {
                        bookEntity?.let {
                            if (it.hasCompletedProfileWithOwnerName()) it.businessOwnerName
                            else context.getString(R.string.defaulBusinessName)
                        } ?: run { context.getString(R.string.defaulBusinessName) }
                    }
                    else -> context.getString(R.string.defaulBusinessName)
                }
                context.getString(
                    R.string.three_dashed_strings,
                    dto.paymentChannel,
                    bookEntity?.businessName,
                    businessOwnerName
                )
            }

            tv_transaction_note.textHTML(
                    dto.note.let {
                        if (it.isBlank() || it == "-") {
                            context.getString(R.string.label_payment_in_note_plain)
                        } else {
                            "%s - %s".format(context.getString(R.string.label_payment_in_note_plain), it)
                        }
                    }
            )
            if (dto.isQrisPaymentIn) {
                tv_transaction_note.textHTML(dto.note)
                gp_customer_details.hideView()
            }
            sender_txt.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            tv_sender_name.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            recipient_txt.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            tv_recipient_name.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            tv_recipient_aaccount_number.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()
            recipient_divider.visibility = (!dto.isPPOB && !dto.isQrisPaymentIn).asVisibility()

        }

        finproOrderResponse?.let { order ->
            val item = order.items?.firstOrNull()
            tv_cst_name.text = "-"
            if (item?.beneficiary?.category == PpobConst.CATEGORY_LISTRIK) {
                tv_cst_phone.text = item.beneficiary.phoneNumber
            } else {
                val mobileNumber = item?.beneficiary?.number ?: ""
                val isMobileNumer = Regex("^(^\\+62|62|062|08|8)(\\d{8,12})\$").matches(mobileNumber)
                tv_cst_phone.text = if (isMobileNumer) mobileNumber else "-"
            }
            if (item?.beneficiary?.category == PpobConst.CATEGORY_BPJS || item?.beneficiary?.code == PpobConst.CATEGORY_PLN_POSTPAID) {
                tv_cst_phone.text = "-"
            }
            if (item?.beneficiary?.category == PpobConst.CATEGORY_TRAIN_TICKET) {
                tv_cst_name.text = context.getString(R.string.code_booking_info)
                tv_cst_phone.hideView()
                tv_cst_name_label.hideView()
                tv_cst_phone_label.hideView()
                tv_transaction_note_message.text = context.getString(R.string.label_payment_detail)
                layout_payment_kode.showView()
                layout_payment_kode.tv_token.text = item.details.token
            }
            tv_transaction_note.text = order.description?.trim() ?: EMPTY_STRING
            tvTransactionDate.text = order.progress?.firstOrNull { it.state == PaymentHistory.STATUS_COMPLETED }?.getFormattedTimestamp()
                ?: "-"
            val serialNumber = item?.details?.serialNumber
            serial_number_txt.visibility = View.VISIBLE
            serial_number_divider.visibility = View.VISIBLE
            serial_number_txt.text = context.getString(R.string.serial_number_formatted, (serialNumber ?: "-"))
        }

        layout_transaction_detail.visibility = View.GONE
        tv_footer.textHTML(RemoteConfigUtils.getAppText().poweredByFooter)
    }

    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        printableList.addAll(
            listOf(
                appendLeftAndRight(Utility.formatReceiptDateFromHistory(paymentReceiptDto?.completedStatusDate, false), tv_invoice_number.text.toString()),
                appendLeftWithNewLine(Utility.formatReceiptTimeromHistory(paymentReceiptDto?.completedStatusDate)),
                appendDashLine(),
                appendLeftWithNewLineBold(tvWarungName.text.toString()),
                appendLeftWithNewLine(tvWarungPhone.text.toString()),
                appendDashLine()
            )
        )

        if (paymentReceiptDto?.ppobCategory == PpobConst.CATEGORY_TRAIN_TICKET) {
            printableList.addAll(
                listOf(
                    appendLeftWithNewLineBold(context.getString(R.string.code_booking_info)),
                    appendLeftWithNewLine(context.getString(R.string.code_booking)),
                    appendLeftWithNewLineBold(paymentReceiptDto?.ppobItem?.details?.token.orEmpty()),
                    appendDashLine(),
                    appendLeftWithNewLine(context.getString(R.string.electricity_token_hint))
                )
            )
        } else {
            printableList.addAll(
                listOf(
                    appendLeftAndRight(context.getString(R.string.name_label), context.getString(R.string.mobile_phone_label)),
                    appendLeftAndRight(tv_cst_name.text.toString(), tv_cst_phone.text.toString()),
                    appendExtraLine()
                )
            )
        }

        printableList.addAll(
            listOf(
                appendLeftWithNewLine(context.getString(R.string.payment_successful)),
                appendLeftWithNewLineBold(tvTransactionNominal.text.toString()),
            )
        )

        // shouldShowAdminFeeDetails
        with(paymentReceiptDto?.agentFeeInfo?.amount ?: 0.0) {
            if (this != 0.0 && paymentReceiptDto?.isPPOB.isFalse) {
                printableList.addAll(
                    listOf(
                        appendLeftAndRight(if (paymentReceiptDto?.isPaymentIn.isTrue) context.getString(R.string.nominal_transaksi) else context.getString(R.string.label_total_payment), context.getString(R.string.service_fee)),
                        appendLeftAndRight(tv_nominal_amount.text.toString(), tv_service_fee.text.toString()),
                        appendDashLine(),
                    )
                )
            }
        }

        // shouldShowPaymentFreeCharge
        with(paymentReceiptDto?.paymentFreeChargeStatus.isTrue) {
            if (this) {
                appendLeftWithNewLine(context.getString(R.string.free_charge_label))
            }
        }

        // showRegularPaymentInfo
        // We do not show recipient and sender for QRIS and PPOB transactions

        with(paymentReceiptDto?.isPPOB.isFalse && paymentReceiptDto?.isQrisPaymentIn.isFalse) {
            if (this) {
                printableList.addAll(
                    listOf(
                        appendLeftWithNewLine(context.getString(R.string.sender)),
                        appendLeftWithNewLine(tv_sender_name.text.toString()),
                        appendExtraLine(),
                        appendLeftWithNewLine(context.getString(R.string.penerima)),
                        appendLeftWithNewLine(tv_recipient_name.text.toString()),
                        appendLeftWithNewLine(tv_recipient_aaccount_number.text.toString()),
                        appendDashLine()
                    )
                )
            } else {
                printableList.add(appendExtraLine())
            }
        }

        printableList.addAll(
            listOf(
                appendLeftWithNewLine(tv_transaction_note_message.text.toString()),
                appendLeftWithNewLine(tv_transaction_note.text.toString()),
            )
        )

        // showListrikInfo
        with(paymentReceiptDto?.ppobItem?.details?.token ?: "") {
            if (this.isNotBlank() && paymentReceiptDto?.isPPOB.isTrue && paymentReceiptDto?.ppobCategory == PpobConst.CATEGORY_LISTRIK) {
                printableList.addAll(
                    listOf(
                        appendDashLine(),
                        appendCenterWithNewLine(context.getString(R.string.token_code)),
                        appendCenterWithNewLineBoldAndLarge(this),
                        appendDashLine(),
                        appendLeftWithNewLine(context.getString(R.string.electricity_token_hint))
                    )
                )
            }
        }

        with(paymentReceiptDto?.ppobItem?.details?.voucherCode ?: "") {
            if (paymentReceiptDto?.ppobCategory == PpobConst.CATEGORY_VOUCHER_GAME && this.isNotNullOrBlank()) {
                printableList.addAll(
                    listOf(
                        appendDashLine(),
                        appendLeftWithNewLine(context.getString(R.string.code_voucher)),
                        appendLeftWithNewLineBold(this),
                        appendLeftWithNewLine(context.getString(R.string.voucher_code_info)),
                        appendDashLine()
                    )
                )
            }
        }

//      showSerialNumberPpob
        with(paymentReceiptDto?.isPPOB.isTrue) {
            if (this) {
                printableList.addAll(
                    listOf(
                        appendLeftWithNewLineBold(serial_number_txt.text.toString()),
                        appendDashLine()
                    )
                )
            }
        }

        printableList.addAll(
            listOf(
                appendCenterWithNewLine(RemoteConfigUtils.getAppText().poweredByFooterPlain),
                appendExtraLine(),
                appendExtraLine()
            )
        )

        return printableList
    }
}