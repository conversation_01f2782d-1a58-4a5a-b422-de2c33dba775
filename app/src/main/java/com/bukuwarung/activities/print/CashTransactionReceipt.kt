package com.bukuwarung.activities.print

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.bukuwarung.R
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLineBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRightBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRightLargeAndBold
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithCenterAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendRightWithNewLine
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.databinding.ItemPosBodyInvoiceBinding
import com.bukuwarung.databinding.LayoutPosInvoiceBinding
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.FinproPayments
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.utils.*
import com.bukuwarung.utils.extensions.getAmountWithCurrency
import com.bukuwarung.utils.extensions.getCustomAmountWithCurrency
import com.bukuwarung.utils.extensions.getFormattedDateForReceipt
import com.bumptech.glide.Glide
import kotlin.math.abs


class CashTransactionReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var bookEntity: BookEntity? = null
    private var transactionEntity: CashTransactionEntity? = null
    private var customerEntity: CustomerEntity? = null
    private var transactionItems = mutableListOf<TransactionItemDto>()
    private var isExpense: Boolean = false
    private var isDetailTransaction: Boolean = false
    private var isInvoiceSetting: Boolean = false
    private var productHeader: TextView? = null
    private var qtyHeader: TextView? = null
    private var priceHeader: TextView? = null
    private var finproOrderResponse: FinproOrderResponse? = null
    private var finproOrderPayment: FinproPayments? = null
    private lateinit var from: FROM
    private var invoiceDataBlock: InvoiceDataBlock? = null

    private var paymentBannerUrl: String = ""
    private var trxStatus: Int = 1
    private var isPosTrx: Boolean = false
    private val binding = LayoutPosInvoiceBinding.inflate(LayoutInflater.from(context), this, true)
    val receiptLayout = binding.headerLayout

    enum class FROM {
        FROM_NOTA,
        FROM_HOMEPAGE_BANNER,
        FROM_NOTA_SETTINGS
    }

    fun setup(initializer: Initializer.() -> Unit) = Initializer(initializer) //check setup usage Naveen

    fun setFinproOrder(response: FinproOrderResponse?) {
        finproOrderResponse = response
        initialize()
    }

    fun setFinproPayments(response: FinproPayments?) {
        finproOrderPayment = response
        initialize()
    }

    private fun initialize() {
        bookEntity?.let { book ->
            with(binding.layoutStoreDetail) {
                tvStoreName.text = book.businessName
                tvStorePhone.text = Utility.beautifyPhoneNumber(book.businessPhone).takeIf { it.isNotBlank() }
                    ?: Utility.beautifyPhoneNumber(book.ownerId).takeIf { it.isNotBlank() } ?: "-"
                tvStoreAddress.text = book.businessAddress
                if (book.businessLogo.isNotNullOrEmpty()) {
                    Glide.with(context).load(book.businessLogo).centerCrop().into(this.ivLogo)
                }
            }

            paymentBannerUrl = RemoteConfigUtils.getPaymentBannerUrl()
            Glide.with(context).load(RemoteConfigUtils.getNotesMissionBannerImageUrl())
                .placeholder(R.color.white)
                .into(binding.imgMissionBanner)


            if (!RemoteConfigUtils.getEnableNotaMission()) {
                binding.bannerContainer.visibility = View.GONE
            }

            binding.imgMissionBanner.setOnClickListener {
                val propBuilder = AppAnalytics.PropBuilder()
                if (from == FROM.FROM_NOTA) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.NOTA)
                } else if (from == FROM.FROM_HOMEPAGE_BANNER) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HOMEPAGE_BANNER)
                } else if (from == FROM.FROM_NOTA_SETTINGS) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.NOTA_SETTING)
                }
                propBuilder.put(AnalyticsConst.BANNER_NAME, AnalyticsConst.USER_BUSINESS_PROFILE_FILL_FOR_NM_REWARD)
                AppAnalytics.trackEvent(AnalyticsConst.BANNER_CLICK, propBuilder)

                val intent = Intent(context, NotesMissionActivity::class.java)
                intent.putExtra(NotesMissionActivity.FROM, com.bukuwarung.activities.print.FROM.CASH_TRANSACTION_RECEIPT.name)
                context.startActivity(intent)
            }

        }

        transactionEntity?.let { trx ->
            with(binding.layoutHeaderPos) {
                tvDate.tvKey.text = context.getString(R.string.date)
                tvNoteCode.tvKey.text = context.getString(R.string.nomor_nota)
                tvCustomerDetails.tvKey.text = context.getString(R.string.customers)
                tvTypeOfPayment.tvKey.text = context?.getString(R.string.type_of_payment)

                tvDate.tvValue.text = trx.getFormattedDateForReceipt()
                tvNoteCode.tvValue.text = Utilities.getFormattedInvoiceId(trx.cashTransactionId)
                isPosTrx = AppConst.POS in trx.cashCategoryId
                when {
                    isPosTrx && trx.paymentMethod == PosViewModel.CASH_PAYMENT_METHOD -> {
                        tvTypeOfPayment.tvValue.text = context.getString(R.string.total_cash)
                    }
                    isPosTrx -> {
                        val nonCashPaymentMethodString = context.getString(R.string.pos_non_cash_payment_wallet)
                        val posPaymentMethod = String.format(nonCashPaymentMethodString, trx.paymentMethod)
                        tvTypeOfPayment.tvValue.text = posPaymentMethod
                    }
                }
            }
            with(binding.layoutTransactionStatus) {
                tvStatusTransaction.setTextColor(context.getColorCompat(if (trx.status != 0) R.color.green_80 else R.color.red_80))
                tvStatusTransaction.text = if (trx.status != 0) context.getString(R.string.paid_label) else context.getString(R.string.not_paid)
            }
            with(binding.layoutPaymentDetail) {
                tvAmountGiven.text = trx.getAmountWithCurrency()
                layoutCashPaid.tvKey.text = context.getString(R.string.total_cash)
                layoutCashPaid.tvValue.text = if (trx.customAmount > 0.0) trx.getCustomAmountWithCurrency() else trx.getAmountWithCurrency()
                layoutReturnPaid.tvKey.text = context.getString(R.string.change_to_give)
                layoutReturnPaid.tvValue.text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(trx.customAmount - trx.amount))
                tvTransactionTypeUtang.text =
                    if (finproOrderPayment?.paymentMethod?.code == PaymentHistory.SALDO_BNPL) context.getString(
                        R.string.label_total_payment
                    )
                    else context.getString(R.string.total)
            }

            binding.bannerContainer.visibility = (!isPosTrx && from != FROM.FROM_NOTA_SETTINGS).asVisibility()
            if (!Utility.isBlank(trx.description)) {
                binding.layoutFooterPos.tvNotesTxt.text = trx.description
            }
            if (transactionItems.isNotEmpty()) {
                productHeader = findViewById(R.id.tv_product_lable)
                qtyHeader = findViewById(R.id.tv_qty_lable)
                priceHeader = findViewById(R.id.tv_price)
                priceHeader?.visibility = (!isExpense).asVisibility()

                binding.productContainer.removeAllViews()
                transactionItems.forEach {
                    val productView = ItemPosBodyInvoiceBinding.inflate(LayoutInflater.from(context), this, false).apply {
                        tvProductPrice.text = Utility.formatCurrency(it.sellingPrice * it.quantity)
                        tvProductPrice.visibility = (!isExpense).asVisibility()

                        tvProductName.text = it.productName
                            ?: resources.getString(R.string.default_placeholder)

                        val productCount = Utility.getRoundedOffPrice(abs(it.quantity))
                        tvProductCount.text = productCount
                        tvProductCountTop.text = productCount
                        tvPricePerUnit.text = "%s / %s".format(Utility.formatCurrency(it.sellingPrice), it.measurementUnit)

                        // give the product items margin
                        normalProductInfoGroup.visibility = (!isExpense).asVisibility()
                        tvProductCountTop.visibility = isExpense.asVisibility()
                        root.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT).apply {
                            setMargins(0, 4.dp, 0, 0)
                        }
                    }
                    binding.productContainer.addView(productView.root)
                }
                binding.layoutPaymentDetail.viewDivider.showView()
            } else {
                binding.layoutPaymentDetail.viewDivider.hideView()
            }
        }
        customerEntity?.let {
            if (it.name.isNotNullOrEmpty()) {
                binding.layoutHeaderPos.tvCustomerDetails.tvValue.text =
                    customerEntity?.name.plus(System.lineSeparator())
                        .plus(Utility.beautifyPhoneNumber(customerEntity?.phone))
            }
        }
        if (isPosTrx) {
            handleStoreDetailVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.storeDetailData?.getOrNull(
                    1
                )
            )
            handleHeaderDataVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.headerData?.getOrNull(
                    1
                )
            )
            handleBodyDataVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.bodyData?.getOrNull(
                    1
                )
            )
            handleFooterDataVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.footerData?.getOrNull(
                    1
                )
            )
            binding.layoutHeaderPos.tvCustomerDetails.root.hideView()
            if (transactionEntity?.paymentMethod == PosViewModel.CASH_PAYMENT_METHOD) {
                binding.layoutPaymentDetail.layoutCashPaid.tvKey.text = context.getString(R.string.total_cash)
            } else {
                binding.layoutPaymentDetail.layoutCashPaid.tvKey.text = context.getString(R.string.non_cash)
                binding.layoutPaymentDetail.layoutReturnPaid.root.hideView()
            }
        } else {
            handleStoreDetailVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.storeDetailData?.getOrNull(
                    2
                )
            )
            handleHeaderDataVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.headerData?.getOrNull(
                    2
                )
            )
            handleBodyDataVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.bodyData?.getOrNull(
                    2
                )
            )
            handleFooterDataVisibility(
                invoiceDataBlock?.data?.getOrNull(0)?.footerData?.getOrNull(
                    2
                )
            )
            binding.layoutPaymentDetail.layoutCashPaid.tvKey.text = context.getString(R.string.total_cash)
            if (transactionEntity?.status == 0) {
                binding.layoutPaymentDetail.layoutCashPaid.root.hideView()
            }
            binding.layoutHeaderPos.tvTypeOfPayment.root.hideView()
            binding.layoutPaymentDetail.layoutReturnPaid.root.hideView()
        }
    }

    inner class Initializer private constructor() {
        constructor(init: Initializer.() -> Unit) : this() {
            init()
        }

        fun setInvoiceData(init: Initializer.() -> InvoiceDataBlock?) = apply {
            invoiceDataBlock = init()
            initialize()
        }

        fun bookEntity(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun cashTransaction(init: Initializer.() -> CashTransactionEntity?) = apply {
            transactionEntity = init()
            initialize()
        }

        fun customerEntity(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun transactionItem(init: Initializer.() -> List<TransactionItemDto>?) = apply {
            init() ?: return@apply
            transactionItems.apply {
                clear()
                addAll(init()!!)
            }

            initialize()
        }

        fun isDetailTransaction(init: Initializer.() -> Boolean) = apply {
            isDetailTransaction = init()
        }

        fun setFrom(setFrom: Initializer.() -> FROM) = apply {
            from = setFrom()
        }

        fun isInvoiceSetting(init: Initializer.() -> Boolean) = apply {
            isInvoiceSetting = init()
        }

        fun trxStatus(init: Initializer.() -> Int) = apply {
            trxStatus = init()
        }

        fun trxType(init: Initializer.() -> Boolean) = apply {
            isExpense = init()
        }

        fun setAsDummy() = apply {
//            initializeDummy()
        }

    }

    private fun handleStoreDetailVisibility(storeDetailDataItem: StoreDetailDataItem?) {
        with(binding.layoutStoreDetail) {
            if (storeDetailDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.icon?.visibility.isFalseOrNull || bookEntity?.businessLogo.isNullOrEmpty()) {
                ivLogo.hideView()
            } else {
                ivLogo.showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.address?.visibility.isFalseOrNull || bookEntity?.businessAddress.isNullOrEmpty()) {
                tvStoreAddress.hideView()
            } else {
                tvStoreAddress.showView()
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.phone?.visibility.isFalseOrNull || tvStorePhone.text.isNullOrEmpty()) {
                tvStorePhone.hideView()
            } else {
                tvStorePhone.showView()
            }
            if (isInvoiceSetting && bookEntity?.businessLogo.isNullOrEmpty()) {
                binding.layoutStoreDetail.ivLogo.setImageResource(R.drawable.ic_logo_placeholder)
                binding.layoutStoreDetail.ivLogo.showView()
            }
        }
    }

    private fun handleHeaderDataVisibility(headerDataItem: HeaderDataItem?) {
        with(binding.layoutHeaderPos) {
            if (headerDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (headerDataItem?.elements?.getOrNull(0)?.customer?.visibility.isFalseOrNull || tvCustomerDetails.tvValue.text.isNullOrEmpty()) {
                tvCustomerDetails.root.hideView()
            } else {
                tvCustomerDetails.root.showView()
            }
            if (headerDataItem?.elements?.getOrNull(0)?.noteCode?.visibility.isFalseOrNull || tvNoteCode.tvValue.text.isNullOrEmpty()) {
                tvNoteCode.root.hideView()
            } else {
                tvNoteCode.root.showView()
            }
            if (headerDataItem?.elements?.getOrNull(0)?.typeOfPayment?.visibility.isFalseOrNull || tvTypeOfPayment.tvValue.text.isNullOrEmpty()) {
                tvTypeOfPayment.root.hideView()
            } else {
                tvTypeOfPayment.root.showView()
            }
        }
    }

    private fun handleBodyDataVisibility(bodyDataItem: BodyDataItem?) {
        with(binding.layoutPaymentDetail) {
            if (bodyDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.subTotal?.visibility.isFalseOrNull || layoutSubtotalPos.tvValue.text.isNullOrEmpty()) {
                layoutSubtotalPos.root.hideView()
            } else {
                layoutSubtotalPos.root.showView()
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.discount?.visibility.isFalseOrNull || layoutDiscountPos.tvValue.text.isNullOrEmpty()) {
                layoutDiscountPos.root.hideView()
            } else {
                layoutDiscountPos.root.showView()
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.tax?.visibility.isFalseOrNull || layoutTaxPos.tvValue.text.isNullOrEmpty()) {
                layoutTaxPos.root.hideView()
            } else {
                layoutTaxPos.root.showView()
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.cash?.visibility.isFalseOrNull || layoutCashPaid.tvValue.text.isNullOrEmpty()) {
                layoutCashPaid.root.hideView()
            } else {
                layoutCashPaid.root.showView()
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.returnAmout?.visibility.isFalseOrNull || transactionEntity?.customAmount == 0.0) {
                layoutReturnPaid.root.hideView()
            } else {
                layoutReturnPaid.root.showView()
            }
        }
    }

    private fun handleFooterDataVisibility(footerDataItem: FooterDataItem?) {
        with(binding.layoutFooterPos) {
            if (footerDataItem?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
            if (footerDataItem?.elements?.getOrNull(0)?.notes?.visibility.isFalseOrNull || transactionEntity?.description.isNullOrBlank()) {
                tvNotes.hideView()
                tvNotesTxt.hideView()
            } else {
                tvNotes.showView()
                tvNotesTxt.showView()
            }
            if (footerDataItem?.elements?.getOrNull(0)?.bukuAd?.visibility.isFalseOrNull) {
                bukuwarungAdLayout.hideView()
            } else {
                bukuwarungAdLayout.showView()
            }
            val category =
                finproOrderResponse?.items?.firstOrNull()?.beneficiary?.category.orEmpty()
            val categoryCode =
                finproOrderResponse?.items?.firstOrNull()?.beneficiary?.code.orEmpty()
            if (category.equals(PpobConst.CATEGORY_LISTRIK) && categoryCode.equals(PpobConst.CATEGORY_PLN_POSTPAID)
                    .not()
            ) {
                layoutPaymentCatatan.rootView.showView()
                tvPdtName.text = context.getString(R.string.token_code)
                tvNumber.text = finproOrderResponse?.items?.firstOrNull()?.details?.token.orEmpty()
                noteHint.showView()
            } else if (category.equals(PpobConst.CATEGORY_VOUCHER_GAME) && finproOrderResponse?.items?.firstOrNull()?.details?.voucherCode.isNotNullOrBlank()) {
                layoutPaymentCatatan.rootView.showView()
                layoutPaymentCatatan.setBackgroundColor(context.getColorCompat(R.color.alice_blue))
                tvPdtName.text = context.getString(R.string.code_voucher)
                tvNumber.text =
                    finproOrderResponse?.items?.firstOrNull()?.details?.voucherCode.orEmpty()
                noteHint.showView()
                noteHint.text = context.getString(R.string.voucher_code_info)
            }
        }
    }

    /**
     * formatted text is used to print the receipt
     * */
    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()
        // TODO: Add Logo for premium

        // Store Details
        val storeDetailDataItem = if (isPosTrx) {
            invoiceDataBlock?.data?.getOrNull(0)?.storeDetailData?.getOrNull(
                1
            )
        } else {
            invoiceDataBlock?.data?.getOrNull(0)?.storeDetailData?.getOrNull(
                2
            )
        }
        if (storeDetailDataItem?.visibility.isTrue) {
            printableList.add(appendCenterWithNewLineBold(binding.layoutStoreDetail.tvStoreName.text))
            if (storeDetailDataItem?.elements?.getOrNull(0)?.address?.visibility.isTrue && bookEntity?.businessAddress.isNotNullOrEmpty()) {
                printableList.add(appendCenterWithNewLine(binding.layoutStoreDetail.tvStoreAddress.text))
            }
            if (storeDetailDataItem?.elements?.getOrNull(0)?.phone?.visibility.isTrue) {
                printableList.add(appendCenterWithNewLine(binding.layoutStoreDetail.tvStorePhone.text))
            }
            printableList.add(appendExtraLine())
        }


        // Header Details
        val headerDataItem = if (isPosTrx) {
            invoiceDataBlock?.data?.getOrNull(0)?.headerData?.getOrNull(
                1
            )
        } else {
            invoiceDataBlock?.data?.getOrNull(0)?.headerData?.getOrNull(
                2
            )
        }
        if (headerDataItem?.visibility.isTrue) {
            printableList.add(appendLeftAndRight(binding.layoutHeaderPos.tvDate.tvKey.text, binding.layoutHeaderPos.tvDate.tvValue.text))
            if (headerDataItem?.elements?.getOrNull(0)?.noteCode?.visibility.isTrue) {
                printableList.add(appendLeftAndRight(binding.layoutHeaderPos.tvNoteCode.tvKey.text, binding.layoutHeaderPos.tvNoteCode.tvValue.text))
            }
        }
        if (headerDataItem?.elements?.getOrNull(0)?.typeOfPayment?.visibility.isTrue) {
            printableList.add(appendLeftAndRight(binding.layoutHeaderPos.tvTypeOfPayment.tvKey.text, binding.layoutHeaderPos.tvTypeOfPayment.tvValue.text))
        }
        if (headerDataItem?.elements?.getOrNull(0)?.customer?.visibility.isTrue) {
            printableList.add(appendLeftAndRight(binding.layoutHeaderPos.tvCustomerDetails.tvKey.text, customerEntity?.name))
            printableList.add(appendRightWithNewLine(Utility.beautifyPhoneNumber(customerEntity?.phone)))
        }

        printableList.add(appendDashLine())
        printableList.add(appendCenterWithNewLine(binding.layoutTransactionStatus.tvStatusTransaction.text))
        printableList.add(appendDashLine())

        // Product Details
        transactionItems.forEach {
            val pricePerUnit = "%s / %s".format(Utility.formatCurrency(it.sellingPrice), it.measurementUnit)
            val quantity = Utility.getRoundedOffPrice(abs(it.quantity))
            if (!isExpense) {
                printableList.addAll(
                    listOf(
                        appendLeftWithNewLine(it.productName),
                        appendLeftWithCenterAndRight(pricePerUnit, quantity, Utility.formatCurrency((it.sellingPrice * it.quantity)))
                    )
                )
            } else {
                printableList.add(appendLeftAndRight(it.productName, quantity))
            }
        }
        printableList.add(appendDashLine())

        // Body/Payment Detail
        val bodyDataItem = if (isPosTrx) {
            invoiceDataBlock?.data?.getOrNull(0)?.bodyData?.getOrNull(
                1
            )
        } else {
            invoiceDataBlock?.data?.getOrNull(0)?.bodyData?.getOrNull(
                2
            )
        }
        if (bodyDataItem?.visibility.isTrue) {
            if (bodyDataItem?.elements?.getOrNull(0)?.subTotal?.visibility.isTrue) {
                printableList.add(appendLeftAndRight(binding.layoutPaymentDetail.layoutSubtotalPos.tvKey.text, binding.layoutPaymentDetail.layoutSubtotalPos.tvValue.text))
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.discount?.visibility.isTrue) {
                printableList.add(appendLeftAndRight(binding.layoutPaymentDetail.layoutDiscountPos.tvKey.text, binding.layoutPaymentDetail.layoutDiscountPos.tvValue.text))
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.tax?.visibility.isTrue) {
                printableList.add(appendLeftAndRight(binding.layoutPaymentDetail.layoutTaxPos.tvKey.text, binding.layoutPaymentDetail.layoutTaxPos.tvValue.text))
            }
            printableList.add(appendLeftAndRightBold(binding.layoutPaymentDetail.tvTransactionTypeUtang.text, binding.layoutPaymentDetail.tvAmountGiven.text))
            printableList.add(appendDashLine())
            if (bodyDataItem?.elements?.getOrNull(0)?.cash?.visibility.isTrue && transactionEntity?.status != 0) {
                printableList.add(appendLeftAndRight(binding.layoutPaymentDetail.layoutCashPaid.tvKey.text, binding.layoutPaymentDetail.layoutCashPaid.tvValue.text))
            }
            if (bodyDataItem?.elements?.getOrNull(0)?.returnAmout?.visibility.isTrue && transactionEntity?.customAmount != 0.0) {
                printableList.add(appendLeftAndRight(binding.layoutPaymentDetail.layoutReturnPaid.tvKey.text, binding.layoutPaymentDetail.layoutReturnPaid.tvValue.text))
            }
        }
        printableList.add(appendExtraLine())

        // Footer Details
        val footerDataItem = if (isPosTrx) {
            invoiceDataBlock?.data?.getOrNull(0)?.footerData?.getOrNull(
                1
            )
        } else {
            invoiceDataBlock?.data?.getOrNull(0)?.footerData?.getOrNull(
                2
            )
        }
        if (footerDataItem?.visibility.isTrue) {
            if (footerDataItem?.elements?.getOrNull(0)?.notes?.visibility.isTrue && !binding.layoutFooterPos.tvNotesTxt.text.isNullOrBlank()) {
                printableList.add(appendLeftWithNewLine(binding.layoutFooterPos.tvNotes.text))
                printableList.add(appendLeftWithNewLine(binding.layoutFooterPos.tvNotesTxt.text))
                printableList.add(appendExtraLine())
            }

            if (footerDataItem?.elements?.getOrNull(0)?.bukuAd?.visibility.isTrue) {
                printableList.add(appendCenterWithNewLine(binding.layoutFooterPos.tvFooterOne.text))
                printableList.add(appendCenterWithNewLine(binding.layoutFooterPos.tvBukuwarungUrl.text))
                printableList.add(appendExtraLine())
            }
        }
        if(binding.layoutFooterPos.tvPdtName.isVisible){
            printableList.add(appendCenterWithNewLine(binding.layoutFooterPos.tvPdtName.text))
            printableList.add(appendLeftWithNewLine(binding.layoutFooterPos.tvNumber.text))
            printableList.add(appendLeftWithNewLine(binding.layoutFooterPos.noteHint.text))
        }
        printableList.add(appendExtraLine())
        printableList.add(appendExtraLine())
        return printableList
    }

    fun listenForAddressChange(editText: EditText) {
        editText.addTextChangedListener {
            binding.layoutStoreDetail.tvStoreAddress.text = it.toString()
        }
    }

    fun updateBusinessLogo(bitmap: Bitmap) {
        Glide.with(context).load(bitmap).centerCrop().into(binding.layoutStoreDetail.ivLogo)
    }

}
