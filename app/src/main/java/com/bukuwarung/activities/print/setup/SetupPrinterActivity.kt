package com.bukuwarung.activities.print.setup

import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Build
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.experiments.CustomWebviewActivity
import com.bukuwarung.activities.print.adapter.PrinterAdapter
import com.bukuwarung.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.bluetooth_printer.BukuPrinter
import com.bukuwarung.bluetooth_printer.model.PairedPrinter
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.ALIGNMENT_CENTER
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.CHARCODE_PC437_ENGLISH
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.EMPHASIZED_MODE_BOLD
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.FONT_SIZE_NORMAL
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.LINE_SPACING_30
import com.bukuwarung.bluetooth_printer.model.basePrinter.BasePrinter.Companion.UNDERLINED_MODE_OFF
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.bluetooth_printer.model.printTypes.TextPrintType
import com.bukuwarung.bluetooth_printer.utils.BluetoothConnection
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.bluetooth_printer.utils.PrinterCallBack
import com.bukuwarung.bluetooth_printer.utils.PrintingHelper
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.databinding.ActivitySetupPrinterBinding
import com.bukuwarung.databinding.LayoutPrinterSnackbarBinding
import com.bukuwarung.dialogs.printer.BluetoothRequestDialog
import com.bukuwarung.utils.PermissonUtil
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import com.google.android.material.snackbar.Snackbar


class SetupPrinterActivity : BaseActivity() {
    private var intentRequestCode = 0
    private var bluetoothConnection: BluetoothConnection? = null
    private lateinit var printerAdapter: PrinterAdapter
    private lateinit var binding: ActivitySetupPrinterBinding

    private val testPrinterCallBack: (PrinterDataHolder) -> Unit = { printer ->
        printTestPrinterOK(printer)
    }

    private val printerSettingCallback: (PrinterDataHolder) -> Unit = { printer ->
        openPrinterSetting(printer)
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_SETTING_PRINTER,
            AppAnalytics.PropBuilder()
                .put(AnalyticsConst.PRINTER_NAME, printer.name)
                .put(AnalyticsConst.PRINTER_ID, printer.macAddress)
        )
    }

    // for analitycs
    private var entryPoint = AnalyticsConst.LAINNYA // default value


    override fun setViewBinding() {
        binding = ActivitySetupPrinterBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        entryPoint = intent.getStringExtra(AnalyticsConst.ENTRY_POINT) ?: AnalyticsConst.LAINNYA
        bluetoothConnection = BluetoothConnection(this)
        intentRequestCode = intent.getIntExtra("requestCode", 0)
        initClickListeners()
    }

    private fun setUpNoPrinterPaired() {
        binding.noPrinterView.layoutNoPrinter.showView()
        binding.rvPrinter.hideView()
        binding.noPrinterView.tvPrinterHint.hideView()
        binding.noPrinterView.btnRefreshScanPrinter.hideView()
        binding.noInternetView.layoutNoInternet.hideView()
        binding.floatingBtnScanPrinter.hideView()
        binding.printerSettingView.editPrinterView.hideView()
        binding.noPrinterView.tvNoPrinter.text = getString(R.string.belum_ada_printer_ditambahkan)
    }

    private fun setUpNoInternet() {
        binding.noInternetView.layoutNoInternet.showView()
        binding.rvPrinter.hideView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.floatingBtnScanPrinter.hideView()
        binding.printerSettingView.editPrinterView.hideView()
    }

    private fun openPrinterSetting(printer: PrinterDataHolder) {
        binding.printerSettingView.editPrinterView.showView()
        binding.printerSettingView.etPrinterName.setText(printer.name)
        binding.printerSettingView.tvPrinterAddress.text = printer.macAddress
        binding.printerSettingView.tvDisconnectPrinter.setOnClickListener {
            printer.device.let { btDevice ->
                if (btDevice == null) {
                    printerAdapter.getDevice(printer.macAddress)?.let { device -> bluetoothConnection?.unPair(device) }
                } else {
                    bluetoothConnection?.unPair(btDevice)
                }
            }
            printerAdapter.removeInstalledPrinter(printer)
            if (BukuPrinter.getPairedPrinter()?.address == printer.macAddress) {
                BukuPrinter.removeCurrentPrinter()
            }
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_DISCONNECT_PRINTER,
                AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.PRINTER_NAME, printer.name)
                    .put(AnalyticsConst.PRINTER_ID, printer.macAddress)
            )
            showInstalledDevicesList()
        }
        binding.printerSettingView.btnSave.isEnabled = false
        binding.printerSettingView.btnSave.setOnClickListener {
            val oldPrinterName = printer.name
            val updatedPrinterName = binding.printerSettingView.etPrinterName.text.toString()
            printerAdapter.updatePrinterName(printer, updatedPrinterName)
            showInstalledDevicesList()
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_SETTING_SAVE_PRINTER,
                AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.OLD_PRINTER_NAME, oldPrinterName)
                    .put(AnalyticsConst.NEW_PRINTER_NAME, updatedPrinterName)
                    .put(AnalyticsConst.PRINTER_ID, printer.macAddress)
            )
        }

        binding.printerSettingView.etPrinterName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.printerSettingView.btnSave.isEnabled = true
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        binding.rvPrinter.hideView()
        binding.tvBluetoothTitle.hideView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.noInternetView.layoutNoInternet.hideView()
        binding.floatingBtnScanPrinter.hideView()
    }

    private fun showInstalledDevicesList() {
        binding.rvPrinter.showView()
        binding.tvBluetoothTitle.showView()
        binding.floatingBtnScanPrinter.showView()
        binding.noPrinterView.layoutNoPrinter.hideView()
        binding.noPrinterView.tvPrinterHint.hideView()
        binding.noPrinterView.btnRefreshScanPrinter.hideView()
        binding.noInternetView.layoutNoInternet.hideView()
        binding.printerSettingView.editPrinterView.hideView()
        printerAdapter.showInstalledPrinters()
        if (printerAdapter.isInstalledPrinterEmpty()) {
            setUpNoPrinterPaired()
        }
    }

    private fun initClickListeners() {
        binding.ivBack.setOnClickListener {
            finish()
        }

        binding.ivInfo.setOnClickListener {
            val intent = CustomWebviewActivity.createIntent(this, infoURL, "Pusat Bantuan Juragan", false, "printer_setting", "PrinterActivity")
            startActivity(intent)
        }

        binding.noPrinterView.btnAddPrinter.setOnClickListener {
            openScanActivity()
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_ADD_PRINTER,
                AppAnalytics.PropBuilder().put(
                    AnalyticsConst.FIRST_CONNECT,
                    PrinterPrefManager(this).isFirstConnect
                )
            )
            PrinterPrefManager(this).isFirstConnect = false
        }

        binding.noInternetView.btnAddPrinter.setOnClickListener {
            openScanActivity()
        }

        binding.floatingBtnScanPrinter.setOnClickListener {
            openScanActivity()
        }
    }

    private fun openScanActivity() {
        val intent = Intent(this, BluetoothPrinterScanActivity::class.java)
        startActivityForResult(intent, intentRequestCode)
    }

    override fun subscribeState() {}

    override fun onStart() {
        super.onStart()
        bluetoothConnection?.onStart()
    }

    override fun onResume() {
        super.onResume()
        checkPermissionAndBluetooth()
    }

    private fun initViews() {
        if (!::printerAdapter.isInitialized) {
            printerAdapter = PrinterAdapter(this, null, testPrinterCallBack, printerSettingCallback)
        }

        binding.rvPrinter.apply {
            layoutManager = LinearLayoutManager(this@SetupPrinterActivity)
            adapter = printerAdapter
        }

        showInstalledDevicesList()

        if (!Utility.hasInternet()) {
            setUpNoInternet()
        }
    }

    private fun checkPermissionAndBluetooth(withDialog: Boolean = true) {
        if (!PermissonUtil.hasLocationPermission() || (bluetoothConnection?.isEnabled == false) || !PermissonUtil.hasBluetoothPermission()) {
            if (withDialog) {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_BT_ENABLE_REQUEST_RETRY,
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
                )
                BluetoothRequestDialog(this, entryPoint) {
                    if (it) {
                        PermissonUtil.requestLocationPermission(this)
                    }
                }.show()
            } else {
                PermissonUtil.requestLocationPermission(this)
            }
        } else {
            initViews()
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            0 -> {
                initViews()
            }
            REQUEST_TO_ENABLE_BT -> {
                if (!::printerAdapter.isInitialized) {
                    printerAdapter = PrinterAdapter(this, null, testPrinterCallBack, printerSettingCallback)
                }
                if (intentRequestCode == RECORD_DETAIL && !printerAdapter.isInstalledPrinterEmpty()) {
                    setResult(RESULT_OK)
                    finish()
                } else {
                    initViews()
                }
            }
            else -> {
                finish()
            }

        }

    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        val firstResult = grantResults.getOrNull(0)
        val secondResult = grantResults.getOrNull(1)
        when (requestCode) {
            PermissionConst.ACCESS_LOCATION -> {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_LOCATION_PERMISSION_REQUEST,
                    AppAnalytics.PropBuilder().put(
                        AnalyticsConst.STATUS,
                        if (firstResult == PackageManager.PERMISSION_GRANTED) AnalyticsConst.STATUS_ALLOW else AnalyticsConst.STATUS_DENY
                    )
                )

                if (firstResult == PackageManager.PERMISSION_GRANTED) {
                    // request to enable bluetooth
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        PermissonUtil.requestBluetoothPermission(this)
                    } else {
                        startActivityForResult(Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE), REQUEST_TO_ENABLE_BT)
                    }
                } else {
                    // permission request denied
                    Toast.makeText(this, getString(R.string.location_permission_denied_message), Toast.LENGTH_SHORT).apply {
                        setGravity(Gravity.CENTER, 0, 0)
                    }.show()
                    finish()
                }
            }
            PermissionConst.BLUETOOTH_PERMISSION -> {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_BT_ENABLE_REQUEST_ALLOWED,
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
                )
                if (firstResult == PackageManager.PERMISSION_GRANTED && secondResult == PackageManager.PERMISSION_GRANTED) {
                    if (!::printerAdapter.isInitialized) {
                        printerAdapter = PrinterAdapter(this, null, testPrinterCallBack, printerSettingCallback)
                    }
                    if (intentRequestCode == RECORD_DETAIL && !printerAdapter.isInstalledPrinterEmpty()) {
                        setResult(RESULT_OK)
                        finish()
                    } else {
                        initViews()
                    }
                } else {
                    // permission request denied
                    Toast.makeText(this, getString(R.string.location_permission_denied_message), Toast.LENGTH_SHORT).apply {
                        setGravity(Gravity.CENTER, 0, 0)
                    }.show()
                    finish()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        bluetoothConnection = null
    }

    override fun onStop() {
        super.onStop()
        bluetoothConnection?.onStop()
    }

    private fun printTestPrinterOK(printer: PrinterDataHolder) {
        val printingHelper = BukuPrinter.printer(PairedPrinter(printer.name, printer.macAddress))
        setPrintingCallback(printingHelper)

        val printable = java.util.ArrayList<BasePrintType>()
        val printables = TextPrintType.Builder()
            .setText("PRINTER OK") //The text you want to print
            .setAlignment(ALIGNMENT_CENTER)
            .setEmphasizedMode(EMPHASIZED_MODE_BOLD) //Bold or normal
            .setFontSize(FONT_SIZE_NORMAL)
            .setUnderlined(UNDERLINED_MODE_OFF) // Underline on/off
            .setCharacterCode(CHARCODE_PC437_ENGLISH) // Character code to support languages
            .setLineSpacing(LINE_SPACING_30)
            .setNewLinesAfter(2) // To provide n lines after sentence
            .build() as TextPrintType

        printable.add(printables)
        printingHelper.print(printable, object : PermissionCallback {
            override fun onPermissionRequired(permissions: Array<String>) {
                checkPermissionAndBluetooth()
            }
        })
    }

    private fun setPrintingCallback(printingHelper: PrintingHelper) {
        printingHelper.printerCallBack = object : PrinterCallBack {
            override fun connectingWithPrinter() {
                Log.d("SetupPrinterActivity", "Connecting with Printer")
            }

            override fun printingOrderSentSuccessfully() {
                showSnackBarForTestPrint(false)
                triggerTestPrintEvent(false)
                printingHelper.printerCallBack = null
                Log.d("SetupPrinterActivity", "Print Order sent successfully")
            }

            override fun connectionFailed(error: String) {
                showSnackBarForTestPrint(true)
                triggerTestPrintEvent(true, "connection_issue")
                printingHelper.printerCallBack = null
                Log.d("SetupPrinterActivity", "Connection Failed $error")
            }

            override fun onError(error: String) {
                showSnackBarForTestPrint(true)
                printingHelper.printerCallBack = null
                triggerTestPrintEvent(true, "printer_issue")
                Log.d("SetupPrinterActivity", "Connection on Error $error")
            }

            override fun onMessage(message: String) {
                Log.d("SetupPrinterActivity", "Connection OnMessage $message")
            }

            override fun disconnected() {
                printingHelper.printerCallBack = null
                Log.d("SetupPrinterActivity", "Connection disconnected")
            }
        }
    }

    companion object {
        private const val REQUEST_TO_ENABLE_BT = 11
        const val RECORD_DETAIL = 2311
        const val infoURL = "https://bukuwarungsupport.zendesk.com/hc/id-id/categories/5136513599631"
    }

    private fun triggerTestPrintEvent(testFailed: Boolean, errorMessage: String = "") {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_TEST_PRINTER_COMPLETED,
            AppAnalytics.PropBuilder()
                .put(AnalyticsConst.TEST_PRINT_STATUS, !testFailed)
                .put(AnalyticsConst.FAIL_REASON, errorMessage)
        )
    }

    private fun showSnackBarForTestPrint(testFailed: Boolean) {
        val snackbar: Snackbar = Snackbar.make(binding.printerSnackbarGuideline, "", Snackbar.LENGTH_SHORT)
        val customSnackBinding = LayoutPrinterSnackbarBinding.inflate(layoutInflater)
        snackbar.view.setBackgroundColor(Color.TRANSPARENT)
        val snackbarLayout: Snackbar.SnackbarLayout = snackbar.view as Snackbar.SnackbarLayout
        snackbarLayout.setPadding(0, 0, 0, 0)

        val snackTV = customSnackBinding.tvSnackbarText
        snackTV.text = if (testFailed) {
            getString(R.string.test_printer_failed)
        } else {
            getString(R.string.testing_printer)
        }
        val closeIV = customSnackBinding.ivCloseSnackbar
        closeIV.setOnClickListener {
            snackbar.dismiss()
        }
        snackbarLayout.addView(customSnackBinding.root, 0)
        snackbar.show()
    }
}