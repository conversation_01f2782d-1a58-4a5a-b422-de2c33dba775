package com.bukuwarung.activities.print

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.bukuwarung.R
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendCenterWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendDashLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendExtraLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithCenterAndRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendLeftWithNewLine
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendRight
import com.bukuwarung.activities.print.BluetoothPrinter.Companion.appendRightWithNewLine
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.databinding.CashReceiptLayoutBinding
import com.bukuwarung.databinding.LayoutProductForReceiptBinding
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.*
import com.bukuwarung.utils.extensions.getAmountWithCurrency
import com.bukuwarung.utils.extensions.getBeautifiedPhoneNumber
import com.bukuwarung.utils.extensions.getCustomAmountWithCurrency
import com.bukuwarung.utils.extensions.getFormattedDateForReceipt
import com.bumptech.glide.Glide
import java.util.*
import kotlin.math.abs


class CashTransactionOldReceipt @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var bookEntity: BookEntity? = null
    private var transactionEntity: CashTransactionEntity? = null
    private var customerEntity: CustomerEntity? = null
    private var transactionItems = mutableListOf<TransactionItemDto>()
    private var useDummy: Boolean = false
    private var isExpense: Boolean = false
    private var isDetailTransaction: Boolean = false
    private var isInvoiceSetting: Boolean = false
    private var productHeader: TextView? = null
    private var qtyHeader: TextView? = null
    private var priceHeader: TextView? = null
    private var finproOrderResponse: FinproOrderResponse? = null
    private lateinit var from: FROM

    private var paymentBannerUrl: String = ""
    private var trxStatus: Int = 1
    private var isPosTrx: Boolean = false
    private var hasCustomAmount: Boolean = false
    private val binding = CashReceiptLayoutBinding.inflate(LayoutInflater.from(context), this, true)
    val receiptLayout = binding.headerLayout

    enum class FROM {
        FROM_NOTA,
        FROM_HOMEPAGE_BANNER,
        FROM_NOTA_SETTINGS
    }

    fun setupOldReceipt(initializer: Initializer.() -> Unit) = Initializer(initializer)

    fun setFinproOrder(response: FinproOrderResponse?) {
        finproOrderResponse = response
        initialize()
    }

    private fun initialize() {

        bookEntity?.let { book ->
            binding.header.tvBusinessName.text = book.businessName
            binding.header.tvBusinessPhone.text = Utility.beautifyPhoneNumber(book.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(book.ownerId).takeIf { it.isNotBlank() } ?: "-"
            binding.header.tvBusinessAddress.apply {
                visibility = (book.businessAddress?.isNotBlank()).asVisibility()
                text = book.businessAddress
            }

            binding.header.ivBusinessLogo.apply {
                visibility = (book.businessLogo?.isNotBlank()).asVisibility()
                Glide.with(context).load(book.businessLogo).centerCrop().into(this)
            }

            paymentBannerUrl = RemoteConfigUtils.getPaymentBannerUrl()
            Glide.with(context).load(RemoteConfigUtils.getNotesMissionBannerImageUrl())
                .placeholder(R.color.white)
                .into(binding.imgMissionBanner)


            if (!RemoteConfigUtils.getEnableNotaMission()) {
                binding.bannerContainer.visibility = View.GONE
            }


            binding.imgMissionBanner.setOnClickListener {
                val propBuilder = AppAnalytics.PropBuilder()
                if (from == FROM.FROM_NOTA) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.NOTA)
                } else if (from == FROM.FROM_HOMEPAGE_BANNER) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HOMEPAGE_BANNER)
                } else if (from == FROM.FROM_NOTA_SETTINGS) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.NOTA_SETTING)
                }
                propBuilder.put(AnalyticsConst.BANNER_NAME, AnalyticsConst.USER_BUSINESS_PROFILE_FILL_FOR_NM_REWARD)
                AppAnalytics.trackEvent(AnalyticsConst.BANNER_CLICK, propBuilder)

                val intent = Intent(context, NotesMissionActivity::class.java)
                intent.putExtra(NotesMissionActivity.FROM, com.bukuwarung.activities.print.FROM.CASH_TRANSACTION_RECEIPT.name)
                context.startActivity(intent)
            }

        }

        transactionEntity?.let { trx ->
            binding.header.tvTrxId.text = Utilities.getFormattedInvoiceId(trx.cashTransactionId)
            binding.header.tvTrxDate.text = trx.getFormattedDateForReceipt()
//            tv_transaction_amount.text = trx.getAmountWithCurrency()
            binding.tvTransactionTotalAmount.text = trx.getAmountWithCurrency()
            binding.header.tvTransactionStatus.text = if (trx.status != 0) context.getString(R.string.paid_label) else context.getString(R.string.not_paid)
            binding.header.tvTransactionStatus.setTextColor(context.getColorCompat(if (trx.status != 0) R.color.green_100 else R.color.red_100))

            isPosTrx = AppConst.POS in trx.cashCategoryId
//            binding.bannerContainer.visibility = (!isPosTrx).asVisibility()
            binding.tvPosTotal.visibility = isPosTrx.asVisibility()
            binding.tvPosTotalAmount.visibility = isPosTrx.asVisibility()

            hasCustomAmount = (isPosTrx && trx.customAmount > 0.0)
            binding.tvPosChange.visibility = hasCustomAmount.asVisibility()
            binding.tvPostChangeAmount.visibility = hasCustomAmount.asVisibility()
            binding.tvPostChangeAmount.text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(trx.customAmount - trx.amount))
            binding.tvPosTotalAmount.text = if (trx.customAmount > 0.0) trx.getCustomAmountWithCurrency() else trx.getAmountWithCurrency()

            when {
                isPosTrx && trx.paymentMethod == PosViewModel.CASH_PAYMENT_METHOD -> {
                    binding.header.tvPosPaymentMethod.text = context.getString(R.string.total_cash)
                }
                isPosTrx -> {
                    val nonCashPaymentMethodString = context.getString(R.string.pos_non_cash_payment_wallet)
                    val posPaymentMethod = String.format(nonCashPaymentMethodString, trx.paymentMethod)
                    binding.header.tvPosPaymentMethod.text = posPaymentMethod
                }
            }

            if (!Utility.isBlank(trx.description)) {
                binding.noteGroup.visibility = trx.description.isNotBlank().asVisibility()
                binding.tvNote.text = trx.description
            }
            val category =
                finproOrderResponse?.items?.firstOrNull()?.beneficiary?.category.orEmpty()
            val categoryCode =
                finproOrderResponse?.items?.firstOrNull()?.beneficiary?.code.orEmpty()
            if (category.equals(PpobConst.CATEGORY_LISTRIK) && categoryCode.equals(PpobConst.CATEGORY_PLN_POSTPAID)
                    .not()
            ) {
                binding.layoutPaymentCatatan.showView()
                binding.tvPdtName.text = context.getString(R.string.token_code)
                binding.tvNumber.text = finproOrderResponse?.items?.firstOrNull()?.details?.token.orEmpty()
                binding.ppobMessage.showView()
            } else if (category.equals(PpobConst.CATEGORY_VOUCHER_GAME) && finproOrderResponse?.items?.firstOrNull()?.details?.voucherCode.isNotNullOrBlank()) {
                binding.layoutPaymentCatatan.rootView.showView()
                binding.layoutPaymentCatatan.setBackgroundColor(context.getColorCompat(R.color.alice_blue))
                binding.tvPdtName.text = context.getString(R.string.code_voucher)
                binding.tvNumber.text =
                    finproOrderResponse?.items?.firstOrNull()?.details?.voucherCode.orEmpty()
                binding.ppobMessage.showView()
                binding.ppobMessage.text = context.getString(R.string.voucher_code_info)
            } else{
                binding.ppobMessage.hideView()
                binding.layoutPaymentCatatan.hideView()
            }
            if (transactionEntity!!.cashCategoryId.lowercase(Locale.getDefault()).contains(context.getString(R.string.pulsa).lowercase(Locale.getDefault())))
                binding.tvNote.visibility = View.GONE

            if (shouldShowServiceInfo()) {
                binding.tvServiceFee.visibility = View.VISIBLE
                binding.tvServiceFeeAmount.visibility = View.VISIBLE
                binding.tvServiceFeeAmount.text = Utility.formatAmount(
                    (transactionEntity?.amount
                        ?: 0.0) - (transactionEntity?.buyingPrice ?: 0.0)
                )
            } else {
                binding.tvServiceFee.visibility = View.GONE
                binding.tvServiceFeeAmount.visibility = View.GONE
            }

            customerEntity?.let { customer ->
                binding.header.customerGroup.visibility = customer.name.isNotNullOrEmpty().asVisibility()
                binding.header.tvCustomerName.text = customer.name
                binding.header.tvCustomerPhone.apply {
                    text = customer.getBeautifiedPhoneNumber()
                    visibility = customer.phone.isNotNullOrEmpty().asVisibility()
                }
            }

            binding.productGroup.visibility = transactionItems.isNotEmpty().asVisibility()
            if (transactionItems.isNotEmpty()) {
                productHeader = findViewById(R.id.tv_product_lable)
                qtyHeader = findViewById(R.id.tv_qty_lable)
                priceHeader = findViewById(R.id.tv_price)
                priceHeader?.visibility = (!isExpense).asVisibility()

                binding.productContainer.removeAllViews()
                transactionItems.forEach {
                    val productView = LayoutProductForReceiptBinding.inflate(LayoutInflater.from(context), this, false).apply {
                        tvProductPrice.text = Utility.formatCurrency(it.sellingPrice * it.quantity)
                        tvProductPrice.visibility = (!isExpense).asVisibility()

                        tvProductName.text = it.productName
                            ?: resources.getString(R.string.default_placeholder)

                        val productCount = Utility.getRoundedOffPrice(abs(it.quantity))
                        tvProductCount.text = productCount
                        tvProductCountTop.text = productCount
                        tvPricePerUnit.text = "%s / %s".format(Utility.formatCurrency(it.sellingPrice), it.measurementUnit)

                        // give the product items margin
                        normalProductInfoGroup.visibility = (!isExpense).asVisibility()
                        tvProductCountTop.visibility = isExpense.asVisibility()
                        root.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT).apply {
                            setMargins(0, 8.dp, 0, 0)
                        }
                    }

                    binding.productContainer.addView(productView.root)
                }
            }

            //TODO load image in banner from remote config
//            binding.bannerContainer
        }
    }

    private fun shouldShowServiceInfo(): Boolean {
        return !PaymentUtils.isPpob(transactionEntity?.cashCategoryId) && transactionEntity?.buyingPrice != 0.0 && transactionEntity?.orderId.isNotNullOrEmpty() && (finproOrderResponse?.agentFeeInfo?.amount != null && (finproOrderResponse?.agentFeeInfo?.amount ?: 0.0) > 0.0)
    }

    private fun initializeDummy() {
        useDummy = true
        transactionItems.add(TransactionItemDto(context.getString(R.string.product_name_dummy), 1.0))
//        binding.bannerContainer.visibility = View.GONE

        binding.header.tvTrxDate.text = "%s %s".format(Utility.formatReceiptDate(Utility.getStorableDateString(Date())), Utility.getReadableTimeString(Date().time))

        binding.header.tvCustomerName.text = bookEntity?.businessOwnerName
        binding.header.tvCustomerPhone.apply {
            text = bookEntity?.businessPhone
            visibility = bookEntity?.businessPhone?.isNotEmpty().asVisibility()
        }
        var status = "Lunas"
        if (transactionEntity?.status == 0) {
            status = "Belum Lunas"
        }
        binding.header.tvTransactionStatus.text = status

//        tv_transaction_amount.text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(transactionEntity?.amount))
        val amnt = transactionEntity?.amount
        var bal = transactionEntity?.buyingPrice
        bal?.let {
            bal = 0.0
        }

        val total = amnt?.minus(bal!!)
        binding.tvTransactionTotalAmount.text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(total))

        binding.noteGroup.visibility = View.VISIBLE

        transactionEntity = CashTransactionDetailActivity.cashTransactionEntity
        transactionItems = mutableListOf()
        if (CashTransactionDetailActivity.cashTransactionItems.isNotEmpty()) {
            transactionItems = CashTransactionDetailActivity.cashTransactionItems as MutableList<TransactionItemDto>
        }
        binding.header.tvTrxId.text = "INV: ABCD1234"
        binding.groupFooter.visibility = View.GONE
    }

    /**
     * only works if receipt is initialize as dummy
     * */
    fun listenForAddressChange(editText: EditText) {
        editText.addTextChangedListener {
            if (useDummy) {
                binding.header.tvBusinessAddress.text = it.toString()
            }
        }

    }

    /**
     * only works if receipt is initialize as dummy
     * */
    fun listenForPhoneChange(editText: EditText) {
        editText.addTextChangedListener {
            if (useDummy) {
                binding.header.tvBusinessPhone.text = it.toString()
            }
        }
    }

    /**
     * only works if receipt is initialize as dummy
     * */
    fun updateBusinessLogo(bitmap: Bitmap) {
        if (useDummy) {
            binding.header.ivBusinessLogo.visibility = View.VISIBLE
            Glide.with(context).load(bitmap).centerCrop().into(binding.header.ivBusinessLogo)
        }
    }

    inner class Initializer private constructor() {
        constructor(init: Initializer.() -> Unit) : this() {
            init()
        }

        fun bookEntity(init: Initializer.() -> BookEntity?) = apply {
            bookEntity = init()
            initialize()
        }

        fun cashTransaction(init: Initializer.() -> CashTransactionEntity?) = apply {
            transactionEntity = init()
            initialize()
        }

        fun customerEntity(init: Initializer.() -> CustomerEntity?) = apply {
            customerEntity = init()
            initialize()
        }

        fun transactionItem(init: Initializer.() -> List<TransactionItemDto>?) = apply {
            init() ?: return@apply
            transactionItems.apply {
                clear()
                addAll(init()!!)
            }

            initialize()
        }

        fun isDetailTransaction(init: Initializer.() -> Boolean) = apply {
            isDetailTransaction = init()
        }

        fun setFrom(setFrom: Initializer.() -> FROM) = apply {
            from = setFrom()
        }

        fun isInvoiceSetting(init: Initializer.() -> Boolean) = apply {
            isInvoiceSetting = init()
        }

        fun trxStatus(init: Initializer.() -> Int) = apply {
            trxStatus = init()
        }

        fun trxType(init: Initializer.() -> Boolean) = apply {
            isExpense = init()
        }

        fun setAsDummy() = apply {
            initializeDummy()
        }

    }

    /**
     * formatted text is used to print the receipt
     * */
    fun getFormattedText(): ArrayList<BasePrintType> {
        val printableList = ArrayList<BasePrintType>()

        printableList.add(appendCenterWithNewLine(binding.header.tvBusinessName.text))

        with(binding.header.tvBusinessPhone.text) {
            if (this.isEmpty()) return@with
            printableList.add(appendCenterWithNewLine(this))
        }

        with(binding.header.tvBusinessAddress.text) {
            if (this.isEmpty()) return@with
            printableList.add(appendCenterWithNewLine(this))
        }

        printableList.addAll(
            listOf(
                appendExtraLine(),
                appendLeftWithNewLine(binding.header.tvTrxDate.text),
                appendLeftAndRight(binding.header.tvTrxId.text, binding.header.tvTransactionStatus.text)
            )
        )

        with(customerEntity) {
            if (this == null || this.name.isNullOrEmpty()) return@with

            printableList.addAll(
                listOf(
                    appendDashLine(),
                    appendLeftAndRight(binding.header.customerLabel.text, this.name)
                )
            )

            if (this.phone?.isEmpty() == true) return@with
            printableList.add(appendRightWithNewLine(Utility.beautifyPhoneNumber(this.phone)))
        }

        // replacement for formatProduct()
        with(transactionItems) {
            if (this.isEmpty()) return@with

            printableList.add(appendDashLine())

            if (!isExpense) {
                printableList.add(appendLeftWithCenterAndRight(productHeader?.text, qtyHeader?.text, priceHeader?.text))
            } else {
                printableList.add(appendLeftAndRight(productHeader?.text, qtyHeader?.text))
            }

            printableList.add(appendExtraLine())

            forEach {
                val pricePerUnit = "%s / %s".format(Utility.formatCurrency(it.sellingPrice), it.measurementUnit)
                val quantity = Utility.getRoundedOffPrice(abs(it.quantity))
                if (!isExpense) {
                    printableList.addAll(
                        listOf(
                            appendLeftWithNewLine(it.productName),
                            appendLeftWithCenterAndRight(pricePerUnit, quantity, Utility.formatCurrency((it.sellingPrice * it.quantity)))
                        )
                    )
                } else {
                    printableList.add(appendLeftAndRight(it.productName, quantity))
                }
                printableList.add(appendExtraLine())
            }
        }

        printableList.addAll(
            listOf(
                appendDashLine()
            )
        )

        // replacement for printServiceInfo()
        printableList.add(appendLeftAndRight(binding.tvTransactionTotalLabel.text, binding.tvTransactionTotalAmount.text))

        if (shouldShowServiceInfo()) {
            printableList.addAll(
                listOf(
                    appendRight(binding.tvServiceFee.text),
                    appendRightWithNewLine(binding.tvServiceFeeAmount.text)
                )
            )
        }
        printableList.add(appendDashLine())

        // replacement for shouldShowPosCharge()
        if (isPosTrx) {

            printableList.add(appendLeftAndRight(binding.tvPosTotal.text, binding.tvPosTotalAmount.text))
            if (hasCustomAmount) {
                printableList.add(appendLeftAndRight(binding.tvPosChange.text, binding.tvPostChangeAmount.text))
            }
            printableList.add(appendDashLine())
        }

        with(transactionEntity?.description) {
            if (this.isNullOrEmpty()) return@with
            printableList.add(appendLeftWithNewLine(binding.noteLabel.text))
            printableList.add(appendLeftWithNewLine(this))
            printableList.add(appendDashLine())
        }
        if(binding.layoutPaymentCatatan.isVisible){
            printableList.add(appendCenterWithNewLine(binding.tvPdtName.text))
            printableList.add(appendLeftWithNewLine(binding.tvNumber.text))
            printableList.add(appendCenterWithNewLine(binding.ppobMessage.text))
            printableList.add(appendDashLine())
        }

        printableList.addAll(
            listOf(
                appendExtraLine(),
                appendCenterWithNewLine(binding.tvFooterOne.text),
                appendCenterWithNewLine(binding.tvBukuwarungUrl.text),
                appendExtraLine(),
                appendExtraLine(),
                appendExtraLine()
            )
        )
        return printableList
    }
}