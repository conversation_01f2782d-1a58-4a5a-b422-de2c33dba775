package com.bukuwarung.activities.print.deeplink.handler

import android.content.Intent
import com.bukuwarung.activities.print.NotesMissionActivity
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.neuro.api.Signal
import javax.inject.Inject

class NotesMissionSignalHandler @Inject constructor() : BukuWarungSignalHandler() {
    override val paths: Set<String> = setOf(
        "/notes-mission",
    )

    override fun handle(signal: Signal) {
        val context = signal.context
        val navigator = signal.navigator

        checkIsLogin(context) {
            val intent = Intent(context, NotesMissionActivity::class.java)
            navigator.navigate(intent)
        }
    }
}