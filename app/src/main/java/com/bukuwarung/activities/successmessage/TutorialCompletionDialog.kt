package com.bukuwarung.activities.successmessage

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.bukuwarung.R
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity
import com.bukuwarung.activities.expense.NewCashTransactionActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.RemoteConfigUtils.isDefaultPengeluaranStock
import com.bukuwarung.utils.RemoteConfigUtils.showMandatoryTransactionCategory
import kotlinx.android.synthetic.main.tutorial_completion_dialog.view.*

class TutorialCompletionDialog : BaseBottomSheetDialogFragment() {
    private val showOldForm: Boolean by lazy { RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm() }
    private val isExitDialogEnabled: Boolean by lazy { RemoteConfigUtils.shouldShowExitDialog() }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        val view: View = inflater.inflate(R.layout.tutorial_completion_dialog, container, false)
        view.recordUtang.setOnClickListener {
            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TUTORIAL)
            propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled)
            propBuilder.put(AnalyticsConst.CUST_FAV_PIN_ENABLED, RemoteConfigUtils.FavoriteCustomer.isEnabled())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_ADD_CUSTOMER_BTN, propBuilder)
            val utangIntent = Intent(context, AddCustomerActivity::class.java)
            utangIntent.putExtra("ShowCustomerTutorial", !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TOUR_CUSTOMER_TAB_ADD_BTN))
            startActivity(utangIntent)
            dialog?.dismiss()
        }
        view.recordTransaksi.setOnClickListener {
            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TUTORIAL)
            propBuilder.put(AnalyticsConst.DEFAULT_CATEGORY_EXPENSES, isDefaultPengeluaranStock())
            propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsTransaction())
            propBuilder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, propBuilder)
            context?.run {
                val intent = NewCashTransactionActivity.createIntent(this, true, !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN))
                startActivity(intent)
            }
            dialog?.dismiss()
        }
        view.closeDialog.setOnClickListener {
            dialog?.dismiss()
        }
        return view

    }

    companion object {
        @JvmStatic
        fun show(manager: FragmentManager) {
            TutorialCompletionDialog().show(manager, "tutorial-completion-dialog")
        }
    }
}