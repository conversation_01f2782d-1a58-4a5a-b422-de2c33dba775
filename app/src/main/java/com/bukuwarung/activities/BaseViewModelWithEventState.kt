package com.bukuwarung.activities

import androidx.annotation.CallSuper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.catalogproduct.viewmodel.CatalogViewModel
import com.bukuwarung.locale.Countries
import com.bukuwarung.locale.Country
import com.bukuwarung.session.SessionManager
import com.bukuwarung.wrapper.EventWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext

abstract class BaseViewModelEventState<S : ViewModelState, E : ViewModelEvent> : ViewModel(), CoroutineScope {
    private val job = Job()
    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Main + job

    private val _state = MutableLiveData<EventWrapper<S>>()
    val state: LiveData<EventWrapper<S>> = _state

    abstract fun onEventReceipt(event: E)

    fun setState(newState: S) {
        _state.value = EventWrapper(newState)
    }

    @CallSuper
    override fun onCleared() {
        super.onCleared()
        job.cancel()
    }

    internal fun getCurrency(): String? {
        var country: Country?
        val str = "Rp"
        val countryList: List<*> = Countries.getCountryList()
        var i = 0
        while (true) {
            if (i >= countryList.size) {
                country = null
                break
            }
            country = countryList[i] as Country?
            if (country!!.code == SessionManager.getInstance().countryCode) {
                break
            }
            i++
        }
        return if (country != null) country.currency else str
    }
}

interface ViewModelState
interface ViewModelEvent
