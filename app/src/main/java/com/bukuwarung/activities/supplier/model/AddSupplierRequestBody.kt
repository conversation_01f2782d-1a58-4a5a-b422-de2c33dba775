package com.bukuwarung.activities.supplier.model

import com.google.gson.annotations.SerializedName

data class AddSupplierRequestBody(
    @SerializedName("storeName")
    private var storeName: String,
    @SerializedName("storeLink")
    private var storeLink: String,
    @SerializedName("phoneNumber")
    private var phoneNumber: String,
    @SerializedName("tokokoVerified")
    private var isTokokoVerified: Boolean,
    @SerializedName("supplierStoreId")
    private var supplierStoreId: String,
    @SerializedName("supplierId")
    private var supplierId: String,
    @SerializedName("countryCode")
    private var countryCode: String
) : java.io.Serializable
