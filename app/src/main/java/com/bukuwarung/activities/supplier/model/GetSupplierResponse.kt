package com.bukuwarung.activities.supplier.model

import com.google.gson.annotations.SerializedName

data class GetSupplierResponse(

	@SerializedName("phoneNumber")
	val phoneNumber: String? = null,

	@SerializedName("totalUnpaid")
	val totalUnpaid: Double? = null,

	@SerializedName("totalPaid")
	val totalPaid: Double? = null,

	@SerializedName("storeName")
	val storeName: String? = null,

	@SerializedName("tokokoVerified")
	val tokokoVerified: Boolean? = null,

	@SerializedName("id")
	val id: String? = null,

	@SerializedName("storeLinks")
	val storeLinks: List<String>? = null,

	@SerializedName("countryCode")
	val countryCode: String? = null
)
