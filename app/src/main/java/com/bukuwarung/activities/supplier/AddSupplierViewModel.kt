package com.bukuwarung.activities.supplier

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.supplier.model.*
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.utils.recordException
import com.bukuwarung.wrapper.EventWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

class AddSupplierViewModel @Inject constructor(private val addSupplierRemoteDataSource: AddSupplierRemoteDataSource) : BaseViewModel() {
    sealed class Event {
        data class AddSupplier(val addSupplierRequestBody: AddSupplierRequestBody) : Event()
        data class EditSupplier(val addSupplierRequestBody: EditSupplierRequestBody, val oldSupplierId: String?) : Event()
        data class VerifyPhoneNumber(val phoneNumber: String) : Event()
        data class GetSupplierDetails(val supplierId: String) : Event()
    }

    sealed class State {
        data class OnSupplierAdded(val result: Boolean?) : State()
        data class OnSupplierEdited(val result: Boolean?) : State()
        data class OnPhoneNumberVerified(val responseBody: VerifyPhoneNumberResponseBody?) : State()
        data class ShowSupplierDetails(val response: GetSupplierResponse?): State()
    }

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private fun setState(newState: State) = viewModelScope.launch { _state.postValue(EventWrapper(newState)) }

    fun submitEvent(event: Event) {
        when (event) {
            is Event.AddSupplier -> {
                viewModelScope.launch(Dispatchers.IO) {
                    val response = addSupplierRemoteCall(event.addSupplierRequestBody)
                    if (response == null) {
                        setState(State.OnSupplierAdded(null))
                    } else {
                        setState(State.OnSupplierAdded(response.result))
                    }
                }
            }
            is Event.EditSupplier -> {
                viewModelScope.launch(Dispatchers.IO) {
                    val response = editSupplierRemoteCall(event.addSupplierRequestBody, event.oldSupplierId)
                    if (response == null) {
                        setState(State.OnSupplierEdited(null))
                    } else {
                        setState(State.OnSupplierEdited(response.result))
                    }
                }
            }
            is Event.VerifyPhoneNumber -> {
                viewModelScope.launch(Dispatchers.IO) {
                     verifyPhoneNumberRemoteCall(event.phoneNumber)
                }
            }
            is Event.GetSupplierDetails -> {
                viewModelScope.launch(Dispatchers.IO) {
                    getSupplierDetails(event.supplierId)
                }
            }
            else -> {}
        }
    }

    private suspend fun addSupplierRemoteCall(addSupplierRequestBody: AddSupplierRequestBody): AddSupplierResponse? {
        return try {
            when (val response = addSupplierRemoteDataSource.addSupplier("android-buku",addSupplierRequestBody)) {
                is ApiSuccessResponse -> response.body
                is ApiEmptyResponse -> null
                is ApiErrorResponse -> null
            }
        } catch (ex: Exception) {
            ex.recordException()
            return null
        }
    }

    private suspend fun editSupplierRemoteCall(addSupplierRequestBody: EditSupplierRequestBody, oldSupplierId: String?): AddSupplierResponse? {
        return try {
            when (val response =
                addSupplierRemoteDataSource.editSupplier("android-buku", oldSupplierId.orEmpty(), addSupplierRequestBody)) {
                is ApiSuccessResponse -> response.body
                is ApiEmptyResponse -> null
                is ApiErrorResponse -> null
            }
        } catch (ex: Exception) {
            ex.recordException()
            return null
        }
    }

    private suspend fun verifyPhoneNumberRemoteCall(phoneNumber: String) = viewModelScope.launch {
         try {
            when (val response = addSupplierRemoteDataSource.verifyPhoneNumber("android-buku",phoneNumber)) {
                is ApiSuccessResponse -> setState(State.OnPhoneNumberVerified(response.body))
                is ApiEmptyResponse -> setState(State.OnPhoneNumberVerified(null))
                is ApiErrorResponse -> setState(State.OnPhoneNumberVerified(null))
                else -> {}
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    private suspend fun getSupplierDetails(supplierId: String) = viewModelScope.launch {
         try {
            when (val response = addSupplierRemoteDataSource.getSupplierDetails("android-buku",supplierId)) {
                is ApiSuccessResponse -> setState(State.ShowSupplierDetails(response.body.data))
               // is ApiErrorResponse ->  ask for error cases
                else -> {}
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }
}
