package com.bukuwarung.activities.supplier

import com.bukuwarung.activities.supplier.model.*
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ResponseWrapper
import retrofit2.http.*

interface AddSupplierRemoteDataSource {
    @POST("/b2b/supplier/api/v1")
    suspend fun addSupplier(
        @Header("X-Request-Source") header: String,
        @Body request: AddSupplierRequestBody
    ): ApiResponse<AddSupplierResponse>

    @PUT("/b2b/supplier/api/v1/{supplierId}")
    suspend fun editSupplier(
        @Header("X-Request-Source") header: String,
        @Path("supplierId") supplierId: String,
        @Body request: EditSupplierRequestBody
    ): ApiResponse<AddSupplierResponse>

    @GET("/tokoko/stores")
    suspend fun verifyPhoneNumber(
        @Header("X-Request-Source") header: String,
        @Query("phone") phoneNumber: String
    ): ApiResponse<VerifyPhoneNumberResponseBody?>

    @GET("/b2b/supplier/api/v1/summary/{supplierId}")
    suspend fun getSupplierDetails(
        @Header("X-Request-Source") header: String,
        @Path("supplierId") supplierId: String
    ): ApiResponse<ResponseWrapper<GetSupplierResponse>>

}