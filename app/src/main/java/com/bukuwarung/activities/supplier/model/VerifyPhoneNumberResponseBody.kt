package com.bukuwarung.activities.supplier.model

import com.google.gson.annotations.SerializedName

data class VerifyPhoneNumberResponseBody(
    @SerializedName("responseStatus") val responseStatus: String?,

    @SerializedName("responseMessages") val responseMessages: ResponseMessageBody?,

    @SerializedName("responseType") val responseType: String?,

    @SerializedName("storeId") val supplierStoreId: String?,

    @SerializedName("storeLink") val storeLink: String?,

    @SerializedName("newStoreLink") val newStoreLink: String?,

    @SerializedName("fullName") val fullName: String?,

    @SerializedName("phone") val phone: String?,

    @SerializedName("city") val city: String?,

    @SerializedName("storeLogo") val storeLogo: String?,

    @SerializedName("storeViews") val storeViews: Int?,

    @SerializedName("storeName") val storeName: String?,

    @SerializedName("address") val address: String?,

    @SerializedName("province") val province: String?,

    @SerializedName("appVersionCode") val appVersionCode: Int?,

    @SerializedName("verified") val verified: Boolean?,

    @SerializedName("userId") val supplierId: String?,

    @SerializedName("kycVerified") val kycVerified: Boolean?
)
