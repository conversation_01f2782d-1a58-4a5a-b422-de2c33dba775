package com.bukuwarung.activities.businessdashboard.view.BusinessDashboardAutoRecordSummary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardMainActivity
import com.bukuwarung.activities.businessdashboard.view.TransactionCategoryBreakUpFragment
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentProductDashboardTransactionsCardBinding
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import com.google.android.material.tabs.TabLayoutMediator
import javax.inject.Inject

class BusinessDashboardTransactionsCardFragment: BaseFragment() {

    private var currentFragment: Int = 0
    lateinit var binding: FragmentProductDashboardTransactionsCardBinding
    private lateinit var viewPager: ViewPager2
    private var isExpanded1: Boolean = false
    private var isExpanded2: Boolean = false
    private var sdate:String = "2022-01-01"
    private var edate:String = "2022-01-31"
    @Inject
    lateinit var vmFactory: BusinessDashboardMainViewModelFactory
    private val viewModel: BusinessDashboardMainViewModel by activityViewModels { vmFactory }

    override fun setupView(view: View) {
        arguments?.let {
            isFirstCard = it.getBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD)
        }
        with(binding) {
            if (isFirstCard) {
                ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                viewPagerLayout.showView()
            } else {
                ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                viewPagerLayout.hideView()
            }
            clHeading.setOnClickListener {
                if (viewPagerLayout.visibility == View.VISIBLE) {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                    viewPagerLayout.hideView()
                } else {
                    val prop = AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.CARD, AnalyticsConst.TRANSACTION_CARD)
                    }
                    ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                    viewPagerLayout.showView()
                }
            }
        }

        sdate = DateTimeUtils.getCurrentMonthStartDate()
        edate = DateTimeUtils.getCurrentMonthEndDate()
    }
    private var normalBackPressedAction: () -> Unit = { requireActivity().finish() }
    private var adapter: TransactionCardBottomSheetViewPager? = null
    private var isFirstCard = false

    override fun subscribeState() {
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
                else -> {}
            }
        })

        viewModel.monthState.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
                else -> {}
            }
        })
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = FragmentProductDashboardTransactionsCardBinding.inflate(inflater, container, false)
        viewPager = binding.inventoryHomeViewpager

        sdate = selectedMonthStartDate()
        edate = selectedMonthEndDate()
        setAdapter()

        binding.inventoryHomeViewpager.isNestedScrollingEnabled = false

        TabLayoutMediator(binding.inventoryHomeTabLayout, binding.inventoryHomeViewpager) { tab, position ->
            currentFragment = position
            when (position) {
                0 -> {
                    if(isExpanded1){
                        binding.btnDetail.text = getString(R.string.show_less)
                    }
                    else binding.btnDetail.text = getString(R.string.lihat_semua)
                    tab.text = context?.getString(R.string.sales_new)
                }
                else -> {
                    if(isExpanded2) {
                        binding.btnDetail.text = getString(R.string.show_less)
                    }
                    else  binding.btnDetail.text = getString(R.string.lihat_semua)
                    tab.text = context?.getString(R.string.expense_label)
                }
            }
        }.attach()

        viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                currentFragment = position
                when (position) {
                    0 -> {
                        if(isExpanded1){
                            binding.btnDetail.text = getString(R.string.show_less)
                        }
                        else binding.btnDetail.text = getString(R.string.lihat_semua)
                    }
                    else -> {
                        if(isExpanded2) {
                            binding.btnDetail.text = getString(R.string.show_less)
                        }
                        else  binding.btnDetail.text = getString(R.string.lihat_semua)
                    }
                }
                super.onPageSelected(position)
            }
        })

        binding.btnDetailLayout.setOnClickListener {
            val currentFragment = viewPager.findCurrentFragment(childFragmentManager)
            (currentFragment as TransactionCategoryBreakUpFragment).btnDetailClick()
        }

        return binding.root
    }

    private fun setAdapter() {
        adapter = TransactionCardBottomSheetViewPager(this, sdate, edate) { isIncome, isExpanded ->
            if (isIncome) {
                if (isExpanded) {
                    binding.btnDetail.text = getString(R.string.show_less)
                    isExpanded1 = true
                } else {
                    binding.btnDetail.text = getString(R.string.lihat_semua)
                    isExpanded1 = false
                }
            } else {
                if (isExpanded) {
                    binding.btnDetail.text = getString(R.string.show_less)
                    isExpanded2 = true
                } else {
                    binding.btnDetail.text = getString(R.string.lihat_semua)
                    isExpanded2 = false
                }
            }


        }
        binding.inventoryHomeViewpager.adapter = adapter
    }

    companion object {
        private const val TAB_POSITION = "tabPosition"

        @JvmStatic
        fun instance(position: Int, isFirstCard: Boolean, normalBackPressedAction: () -> Unit) = BusinessDashboardTransactionsCardFragment().apply {
            arguments = Bundle().apply {
                putInt(TAB_POSITION, position)
                putBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD, isFirstCard)
            }
            this.normalBackPressedAction = normalBackPressedAction
        }
    }

    private fun selectedMonthStartDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthStartDate()
    }

    private fun selectedMonthEndDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthEndDate()
    }

    private fun onMonthChange(startDate:String,endDate:String){
        sdate = startDate
        edate = endDate
        setAdapter()
    }
}