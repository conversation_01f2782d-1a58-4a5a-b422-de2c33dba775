package com.bukuwarung.activities.businessdashboard.model

import com.google.gson.annotations.SerializedName

data class Metadata(

	@SerializedName("amount")
	val amount: Int? = null,

	@SerializedName("admin_fee")
	val adminFee: Any? = null,

	@SerializedName("cashback_amount")
	val cashbackAmount: Int? = null,

	@SerializedName("user_id")
	val userId: String? = null,

	@SerializedName("disbursable_id")
	val disbursableId: String? = null,

	@SerializedName("disbursable_type")
	val disbursableType: String? = null,

	@SerializedName("applicable_transaction_count")
	val applicableTransactionCount: Int? = null,

	@SerializedName("status")
	val status: String? = null
)
