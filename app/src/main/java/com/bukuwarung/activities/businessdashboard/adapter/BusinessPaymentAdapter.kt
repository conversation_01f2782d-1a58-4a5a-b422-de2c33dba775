package com.bukuwarung.activities.businessdashboard.adapter

import android.provider.Settings.Global.getString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.model.CategoriesItem
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import kotlinx.android.synthetic.main.item_business_dashboard_payment_history.view.*

class BusinessPaymentAdapter(val list: List<CategoriesItem>, val isFromBottomSheet: Boolean, val tagString: String): RecyclerView.Adapter<BusinessPaymentAdapter.BusinessPaymentViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BusinessPaymentAdapter.BusinessPaymentViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_business_dashboard_payment_history, parent, false)
        return BusinessPaymentViewHolder(view)
    }

    override fun getItemCount(): Int {
        return if (isFromBottomSheet) {
            list.size
        } else {
            return if (list.size >= 3) 3 else list.size
        }
    }

    inner class BusinessPaymentViewHolder constructor(itemView: View) :
        RecyclerView.ViewHolder(itemView) {
        fun bind(item: CategoriesItem, position: Int) {
            with(itemView) {
                tv_item_title.text = item.name

                item.count?.let {
                    tv_count.text = itemView.context.getString(R.string.total_transactions, it.toString())
                } ?: kotlin.run {
                    tv_count.text = itemView.context.getString(R.string.total_transactions, "0")
                }
                item.amount?.let {
                    tv_amount.text = Utility.formatAmount(it)
                } ?: kotlin.run {
                    tv_amount.text = itemView.context.getString(R.string.currency).plus("0")
                }
                if (item.mainCategory.equals("Keperluan Usaha")) {
                    label_business_personal.background = ContextCompat.getDrawable(
                        context, R.drawable
                            .label_bisnis
                    )
                    label_business_personal.text = "Bisnis"
                    label_business_personal.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.blue_60
                        )
                    )
                } else {
                    label_business_personal.background = ContextCompat.getDrawable(
                        context, R.drawable
                            .label_pribadi
                    )
                    label_business_personal.setTextColor(
                        ContextCompat.getColor(
                            context,
                            R.color.green_80
                        )
                    )
                    label_business_personal.text = "Pribadi"
                }
            }
            if (position == list.size -1 && !isFromBottomSheet) {
                itemView.vw_divider.hideView()
            } else {
                itemView.vw_divider.showView()
            }
        }
    }

    override fun onBindViewHolder(holder: BusinessPaymentViewHolder, position: Int) {
        holder.bind(list[position], position)
    }
}