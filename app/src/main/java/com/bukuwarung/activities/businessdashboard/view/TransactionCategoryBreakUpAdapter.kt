package com.bukuwarung.activities.businessdashboard.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.database.entity.CashCategoryEntity
import com.bukuwarung.databinding.EmptyItemBinding
import com.bukuwarung.databinding.TransactionCategoryBreakUpItemBinding
import com.bukuwarung.utils.Utility
import java.text.DecimalFormat

class TransactionCategoryBreakUpAdapter(
    val context: Context, val isIncome: Boolean = false,val totalTransactions: Int,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private lateinit var colors: java.util.ArrayList<Int>
    private var colorIndex: Int = 0
    var data : List<CashCategoryEntity> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)

        if(data.size>0){
            return TransactionCategoryBreakUpViewHolder(TransactionCategoryBreakUpItemBinding.inflate(layoutInflater))
        }
        else{
            return EmptyViewHolder(EmptyItemBinding.inflate(layoutInflater))
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if(holder.adapterPosition<=colors.size)
        {
            colorIndex = holder.adapterPosition
        }
        else{
            if(colorIndex > colors.size)
            colorIndex = 0
        }
        (holder as TransactionCategoryBreakUpViewHolder).bind(holder.adapterPosition,data[colorIndex],colors.get(position))
        colorIndex = colorIndex+1
    }

    override fun getItemCount(): Int {
        return data.size
    }

    inner class TransactionCategoryBreakUpViewHolder(val binding: TransactionCategoryBreakUpItemBinding) : RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n")
        fun bind(
            position: Int,
            category: CashCategoryEntity,
            color: Int,
        ) {

            category ?: return

            binding.apply {
                try {

                    if(position+1 == data.size) line.visibility = View.INVISIBLE else line.visibility = View.VISIBLE
                    tvCategoryName.text = category.name
                    tvTotalTransactionValue.text = Utility.formatAmount(category.balance)
                    tvNumberOfTransactions.text = context.resources.getString(R.string.no_of_transaksi,category.frequency.toString())
                    var df = DecimalFormat("0.00")
                    tvPercentage.text = df.format((category.frequency.toFloat()/totalTransactions)*100).toString().plus("%")
                    bg.setBackgroundColor(color)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

    }
    class EmptyViewHolder(val binding: EmptyItemBinding) : RecyclerView.ViewHolder(binding.root) {
    }


    fun updateData(categories: List<CashCategoryEntity>, colors: java.util.ArrayList<Int>) {
        this.colors = colors
        data = categories
        notifyDataSetChanged()
    }

}