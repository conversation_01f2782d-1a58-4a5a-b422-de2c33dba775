package com.bukuwarung.activities.businessdashboard.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.model.BusinessDashboardCashbackResponseItem
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardCashbackFragment.Companion.DATA_ITEM
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardCashbackFragment.Companion.TOP_CARD
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.showView
import kotlinx.android.synthetic.main.item_cashback_binding.view.*
import kotlinx.android.synthetic.main.layout_cashback_top_card.view.*

class BusinessDashboardCashbackAdapter(
    val listener: BusinessDashboardCashbackListener? = null,
    private val totalCashbackAmount: Double? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var cashbackList = arrayListOf<BusinessDashboardCashbackResponseItem>()

    class BusinessDashboardCashbackViewHolder constructor(itemView: View) :
        RecyclerView.ViewHolder(itemView) {
        fun bind(
            businessDashboardCashbackResponseItem: BusinessDashboardCashbackResponseItem,
            position: Int,
            listener: BusinessDashboardCashbackListener?
        ) {
            with(itemView) {
                tv_amount_cashback.text =
                    Utility.formatAmount(businessDashboardCashbackResponseItem.amount?.toDouble())
                tv_status.text = businessDashboardCashbackResponseItem.status
                tv_date.text =
                    Utility.formatDateWithTime(businessDashboardCashbackResponseItem.createdDate)
                setOnClickListener {
                    listener?.goToPaymentHistoryDetailActivity(
                        businessDashboardCashbackResponseItem.metadata?.disbursableType,
                        businessDashboardCashbackResponseItem.metadata?.disbursableId,
                        itemView.context
                    )
                }
                if (businessDashboardCashbackResponseItem.status.equals("PENDING")) {
                    tv_inprocess_info.showView()
                    tv_inprocess_info.text = getProcessSaldoText(businessDashboardCashbackResponseItem)
                } else {
                    tv_inprocess_info.hideView()
                }
            }
        }

        private fun getProcessSaldoText(cashbackResponseItem: BusinessDashboardCashbackResponseItem): String {
            return itemView.context.getString(R.string.cashback_saldo, Utility.formatAmount(cashbackResponseItem.amount?.toDouble()), Utility.formatReceiptDate(cashbackResponseItem.createdDate))
        }
    }

    class TotalCashbackViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun bind(totalCashback: Double) {
            with(itemView) {
                tv_amount_total.text = Utility.formatAmount(totalCashback)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TOP_CARD) {
            val topCardView = LayoutInflater.from(parent.context)
                .inflate(R.layout.layout_cashback_top_card, parent, false)
            TotalCashbackViewHolder(topCardView)
        } else {
            val cashbackItemView = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_cashback_binding, parent, false)
            BusinessDashboardCashbackViewHolder(cashbackItemView)
        }
    }

    override fun getItemCount(): Int {
        return cashbackList.size + 1
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) TOP_CARD
        else DATA_ITEM
    }

    fun setData(list: List<BusinessDashboardCashbackResponseItem>) {
        this.cashbackList = list as ArrayList<BusinessDashboardCashbackResponseItem>
        notifyDataSetChanged()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder.itemViewType == TOP_CARD) {
            (holder as TotalCashbackViewHolder).bind(totalCashbackAmount.orNil)
        } else {
            (holder as BusinessDashboardCashbackViewHolder).bind(
                cashbackList[position - 1],
                position - 1,
                listener
            )
        }
    }

    interface BusinessDashboardCashbackListener {
        fun goToPaymentHistoryDetailActivity(
            paymentType: String?,
            orderId: String?,
            context: Context
        )
    }
}