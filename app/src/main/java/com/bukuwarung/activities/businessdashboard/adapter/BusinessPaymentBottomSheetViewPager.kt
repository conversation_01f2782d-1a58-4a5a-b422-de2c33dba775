package com.bukuwarung.activities.businessdashboard.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardPaymentListFragment

class BusinessPaymentBottomSheetViewPager(
    fragment: Fragment,
) : FragmentStateAdapter(fragment) {
    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> BusinessDashboardPaymentListFragment.instance("Tagih", true)
            else -> BusinessDashboardPaymentListFragment.instance("Bayar", true)
        }
    }
}