package com.bukuwarung.activities.businessdashboard.di

import com.bukuwarung.activities.businessdashboard.view.*
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardAutoRecordSummary.*
import dagger.Module
import dagger.android.ContributesAndroidInjector

@Module
abstract class BusinessDashboardDI {

    @ContributesAndroidInjector
    abstract fun contributeProductDashboardWelcomeActivity(): BusinessDashboardWelcomeActivity

    @ContributesAndroidInjector
    abstract fun contributeProductDashboardAggregateFragment(): BusinessDashboardAggregateFragment

    @ContributesAndroidInjector
    abstract fun contributeProductDashboardMainActivity(): BusinessDashboardMainActivity

    @ContributesAndroidInjector
    abstract fun contributeProductDashboardTransaksiFragment(): BusinessDashboardTransaksiFragment

    @ContributesAndroidInjector
    abstract fun contributeProductDashboardModalFragment(): BusinessDashboardModalFragment

    @ContributesAndroidInjector
    abstract fun contributeProductDashboardStockNewFragment(): BusinessDashboardStockFragment

    @ContributesAndroidInjector
    abstract fun contributeProductDashboardUtangFragment(): BusinessDashboardUtangFragment

    @ContributesAndroidInjector
    abstract fun contributeCashProductTransaksiFragment(): CashBusinessTransaksiFragment

    @ContributesAndroidInjector
    abstract fun contributeNoCashProductTransaksiFragment(): NoCashBusinessTransaksiFragment

    @ContributesAndroidInjector
    abstract fun contributeBusinessDashboardInfoFragment(): BusinessDashboardInfoFragment

    @ContributesAndroidInjector
    abstract fun contributeBusinessDashboardTransactionsCardFragment(): BusinessDashboardTransactionsCardFragment

    @ContributesAndroidInjector
    abstract fun contributeTransactionCategoryBreakUpFragment(): TransactionCategoryBreakUpFragment

    @ContributesAndroidInjector
    abstract fun contributesBusinessTransaksiCreditFragment(): BusinessTransaksiCreditFragment

    @ContributesAndroidInjector
    abstract fun contributesBusinessTransaksiDebitFragment(): BusinessTransaksiDebitFragment

    @ContributesAndroidInjector
    abstract fun contributesBusinessDashboardTipsEmptyFragment(): BusinessDashboardTipsEmptyFragment

    @ContributesAndroidInjector
    abstract fun contributesBusinessDashboardCategoryFragment(): BusinessDashboardCategoryFragment

    @ContributesAndroidInjector
    abstract fun contributesBusinessDashboardPpobProducts(): BusinessDashboardPpobProducts

    @ContributesAndroidInjector
    abstract fun contributesBusinessDashboardPaymentSectionFragment(): BusinessDashboardPaymentSectionFragment

    @ContributesAndroidInjector
    abstract fun contributesBusinessDashboardCashbackFragment(): BusinessDashboardCashbackFragment

    @ContributesAndroidInjector
    abstract fun contributesBusinessDashboardPaymentListFragment(): BusinessDashboardPaymentListFragment
}