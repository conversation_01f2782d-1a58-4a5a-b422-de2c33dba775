package com.bukuwarung.activities.businessdashboard.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.domain.payments.BankingUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.los.LosUseCase
import javax.inject.Inject


class BusinessDashboardMainViewModelFactory @Inject constructor(
    private val losUseCase: LosUseCase, private val paymentUseCase: PaymentUseCase, private val bankingUseCase: BankingUseCase, private val finproUseCase: FinproUseCase
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return BusinessDashboardMainViewModel(paymentUseCase, losUseCase, bankingUseCase, finproUseCase) as T
    }

}