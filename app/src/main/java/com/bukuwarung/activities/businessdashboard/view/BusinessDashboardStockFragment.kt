package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModelFactory
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.expense.CashListViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.database.dto.FrequentProductDto
import com.bukuwarung.database.entity.AppConfig
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.databinding.FragmentProductDashboardStockBinding
import com.bukuwarung.inventory.ui.InventoryActivity
import com.bukuwarung.inventory.ui.InventoryHomeFragment
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.User
import com.bukuwarung.ui.component.bottomsheet.BottomSheetDataHolder
import com.bukuwarung.ui.component.bottomsheet.BottomsheetList
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import javax.inject.Inject


class BusinessDashboardStockFragment : BaseFragment() {
    lateinit var binding: FragmentProductDashboardStockBinding

    @Inject
    lateinit var vmFactory: BusinessDashboardMainViewModelFactory
    private val viewModel: BusinessDashboardMainViewModel by activityViewModels { vmFactory }

    @Inject
    lateinit var cashListViewModelFactory: CashListViewModelFactory
    private lateinit var cashListViewModel: CashListViewModel
    private var sdate:String = "2022-01-01"
    private var edate:String = "2022-01-31"
    private var isFirstCard = false

    companion object {
        fun createIntent(isFirstCard: Boolean): BusinessDashboardStockFragment {
            val fragment = BusinessDashboardStockFragment()
            fragment.arguments = Bundle()
            fragment.arguments = Bundle().apply {
                putBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD, isFirstCard)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProductDashboardStockBinding.inflate(layoutInflater, container, false)

        cashListViewModel = activity?.run {
            ViewModelProviders.of(this, cashListViewModelFactory).get(
                CashListViewModel::class.java
            )
        }!!

        return binding.root
    }

    override fun setupView(view: View) {
        arguments?.let {
            isFirstCard = it.getBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD)
        }
        with(binding) {
            if (isFirstCard) {
                ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                clCard.showView()
            } else {
                ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                clCard.hideView()
            }
            clHeading.setOnClickListener {
                AppConfigManager.getInstance().setStockBaru()
                if (clCard.visibility == View.VISIBLE) {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                    clCard.hideView()
                } else {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                    clCard.showView()
                }
            }
        }
        sdate = DateTimeUtils.getCurrentMonthStartDate()
        edate = DateTimeUtils.getCurrentMonthEndDate()

        val frequentProducts = cashListViewModel.productInventory.getBestsellingProduct(sdate, edate)
        if (frequentProducts.isNullOrEmpty()) {
            binding.imgProductSelect.hideView()
        } else {
            binding.imgProductSelect.showView()
        }
        binding.bestSellingProductLayout.setOnClickListener {
            if (frequentProducts.isNullOrEmpty()) {
                return@setOnClickListener
            }
            val dataList = ArrayList<BottomSheetDataHolder>()
            for (dto in frequentProducts!!) {
                val item = BottomSheetDataHolder(
                    dto!!.productId,
                    dto.productName,
                    "",
                    dto.productCount.toString() + " kali terjual",
                    "",
                    1,
                    false,
                    true
                )
                dataList.add(item)
            }
            val sheet = BottomsheetList(dataList, dataList, "Produk paling laris di tokomu", "Ada 10 daftar produk jualan yang paling sering dibeli oleh pelanggan kamu",false, false)
            sheet.show(childFragmentManager, "tag")
        }

        binding.lowStockProductLayout.setOnClickListener {
            val intent = Intent(activity, InventoryActivity::class.java)
            intent.putExtra("tab", 1)
            startActivity(intent)
        }

        binding.availableStockProductLayout.setOnClickListener {
            val intent = Intent(activity, InventoryActivity::class.java)
            intent.putExtra("tab", 0)
            startActivity(intent)
        }

        binding.newLabel.visibility = if (AppConfigManager.getInstance().isStockBaru == true) View.VISIBLE else View.GONE

        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchInventoryData(cashListViewModel, selectedMonthStartDate(),selectedMonthEndDate()))

    }

    override fun subscribeState() {
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
                is BusinessDashboardMainViewModel.State.SetInventoryData -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.SetInventoryData)
                    populateInventoryData(data)
                }
                else -> {}
            }
        })

        viewModel.monthState.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
                else -> {}
            }
        })
    }

    private fun populateInventoryData(data : BusinessDashboardMainViewModel.State.SetInventoryData) {
        binding.tvBestSellingProduct.text = if(data.products!=null) data.products[0]?.productName else getString(R.string.no_items_sold_yet)
        binding.tvBestSellingCategory.text = if(data.bestSellingCategory!=null)  data.bestSellingCategory.name else getString(R.string.no_product_with_category)
        binding.tvTotalTxnWithProduct.text = getString(R.string.no_of_trasaksi,data.totalTransactionsWithProducts.toString())
        binding.tvTotalPenjualan.text = Utility.formatAmount(data.totalPenjualan)
        binding.tvTotalModal.text = Utility.formatAmount(data.totalModal)
        binding.tvTotalKeuntungan.text = Utility.formatAmount(data.totalPenjualan - data.totalModal)
        binding.tvLowStockProduct.text = if(data.runningOutProducts.size>0) getString(R.string.no_of_products,data.runningOutProducts.size.toString()) else getString(R.string.no_stock_yet)
        binding.tvAvailableStockProduct.text = if(data.productsInStock.size>0) getString(R.string.no_of_products,data.productsInStock.size.toString())  else getString(R.string.no_stock_yet)
    }

    private fun selectedMonthStartDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthStartDate()
    }

    private fun selectedMonthEndDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthEndDate()
    }

    private fun onMonthChange(startDate:String,endDate:String){
        sdate = startDate
        edate = endDate
        val frequentProducts = ProductRepository.getInstance(
            context
        ).getFrequentProductByDateRange(User.getBusinessId(), sdate, edate)

        binding.bestSellingProductLayout.isEnabled = frequentProducts.isNotEmpty()

        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchInventoryData(cashListViewModel, sdate,edate))
    }
}