package com.bukuwarung.activities.businessdashboard.model

import com.google.gson.annotations.SerializedName

data class PaymentCategoriesHistoryResponse(

	@field:SerializedName("saving")
	val saving: Double? = null,

	@field:SerializedName("total_transaction")
	val totalTransaction: Int? = null,

	@field:SerializedName("account_id")
	val accountId: String? = null,

	@field:SerializedName("categories")
	val categories: List<CategoriesItem>? = null
)

data class CategoriesItem(

	@field:SerializedName("amount")
	val amount: Double? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("count")
	val count: Int? = null,

	@field:SerializedName("main_category")
	val mainCategory: String? = null,

	@field:SerializedName("id")
	val id: Int? = null,

	@field:SerializedName("type")
	val type: String? = null
)
