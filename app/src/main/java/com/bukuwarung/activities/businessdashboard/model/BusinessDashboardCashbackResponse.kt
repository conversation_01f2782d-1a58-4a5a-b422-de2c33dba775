package com.bukuwarung.activities.businessdashboard.model

import com.google.gson.annotations.SerializedName

data class BusinessDashboardCashbackResponseItem(

    @SerializedName("amount")
    val amount: Int? = null,

    @SerializedName("metadata")
    val metadata: Metadata? = null,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("last_modified_date")
    val lastModifiedDate: String? = null,

    @SerializedName("external_id")
    val externalId: String? = null,

    @SerializedName("book_id")
    val bookId: Any? = null,

    @SerializedName("expired_date")
    val expiredDate: Any? = null,

    @SerializedName("user_id")
    val userId: String? = null,

    @SerializedName("virtual_account")
    val virtualAccount: Any? = null,

    @SerializedName("id")
    val id: String? = null,

    @SerializedName("created_date")
    val createdDate: String? = null,

    @SerializedName("virtual_account_id")
    val virtualAccountId: String? = null,

    @SerializedName("tenant")
    val tenant: String? = null,

    @SerializedName("campaign_id")
    val campaignId: Any? = null,

    @SerializedName("status")
    val status: String? = null
)
