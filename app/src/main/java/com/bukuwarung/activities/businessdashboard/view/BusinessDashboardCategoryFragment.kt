package com.bukuwarung.activities.businessdashboard.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentProductDashboardCategoryBinding

class BusinessDashboardCategoryFragment : BaseFragment() {
    lateinit var binding: FragmentProductDashboardCategoryBinding
    lateinit var viewModel: BusinessDashboardMainViewModel

    companion object {
        private const val PRODUCT_DASHBOARD_CATEGORY_FRAGMENT =
            "product_dashboard_category_fragment"

        fun createIntent(): BusinessDashboardCategoryFragment {
            val fragment = BusinessDashboardCategoryFragment()
            fragment.arguments = Bundle()
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProductDashboardCategoryBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        binding.btnDetail.setOnClickListener {
            startActivity(
                WebviewActivity.createIntent(requireContext(),
                "https://bukuwarungsupport.zendesk.com/hc/id-id/categories/4414029114511--Fitur-Transaksi-Kategori", "Transaksi Kategori"))

            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.BD)
            propBuilder.put(AnalyticsConst.FEATURE, AnalyticsConst.TRANSACTION_CATEGORY)
        }

    }

    override fun subscribeState() {
    }



}