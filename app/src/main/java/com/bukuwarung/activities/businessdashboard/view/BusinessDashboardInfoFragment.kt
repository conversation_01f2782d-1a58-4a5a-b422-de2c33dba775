package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.BD
import com.bukuwarung.databinding.FragmentProductDashboardInfoBinding
import com.bukuwarung.databinding.FragmentProductDashboardUtangBinding
import com.bukuwarung.utils.setSingleClickListener

class BusinessDashboardInfoFragment: BaseFragment() {
    lateinit var binding: FragmentProductDashboardInfoBinding
    lateinit var viewModel: BusinessDashboardMainViewModel

    companion object {
        private const val PRODUCT_DASHBOARD_INFO_FRAGMENT = "product_dashboard_info_fragment"

        fun createIntent(): BusinessDashboardInfoFragment {
            val fragment = BusinessDashboardInfoFragment()
            fragment.arguments = Bundle()
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProductDashboardInfoBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        binding.cvHome.setSingleClickListener {


            BusinessDashboardWelcomeActivity.isFromInfo = true
            startActivity(BusinessDashboardWelcomeActivity.createIntent(requireContext(), 0.0, 0.0))
        }
    }

    override fun subscribeState() {
    }

}