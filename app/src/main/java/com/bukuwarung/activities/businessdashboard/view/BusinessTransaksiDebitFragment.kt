package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.activities.businessdashboard.adapter.BusinessNoCashTransaksiAdapter
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.expense.CashListViewModelFactory
import com.bukuwarung.activities.expense.adapter.model.DailySummary
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentCashProductTransaksiBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isNotNullOrEmpty
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.android.support.AndroidSupportInjection
import java.lang.Exception
import javax.inject.Inject

class BusinessTransaksiDebitFragment: BaseFragment(), BusinessNoCashTransaksiAdapter.BusinessTransaksiListener {

    private lateinit var binding: FragmentCashProductTransaksiBinding
    lateinit var viewModel: BusinessDashboardMainViewModel
    private lateinit var cashAdapter: BusinessNoCashTransaksiAdapter

    @Inject
    lateinit var cashListViewModelFactory: CashListViewModelFactory
    private lateinit var cashListViewModel: CashListViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCashProductTransaksiBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        viewModel = activity?.run {
            ViewModelProvider(this).get(BusinessDashboardMainViewModel::class.java)
        }!!
        cashListViewModel = activity?.run {
            ViewModelProviders.of(this, cashListViewModelFactory).get(
                CashListViewModel::class.java
            )
        }!!
        return binding.root
    }

    override fun setupView(view: View) {
        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchTransaksiRecords(
            cashListViewModel, (activity as BusinessDashboardMainActivity ).getMonthStartDate(),
            (activity as BusinessDashboardMainActivity ).getMonthEndDate()))

        cashAdapter = BusinessNoCashTransaksiAdapter(this)
        binding.productTransaksiUnitRecyclerView.apply {
            adapter = cashAdapter
            layoutManager = LinearLayoutManager(context)
        }
    }

    override fun subscribeState() {
        viewModel.state_bottomsheet_credit.observe(this, androidx.lifecycle.Observer {
            when(it.peekContent()) {
                is BusinessDashboardMainViewModel.State.PopulateBottomSheetPemasukanTransaksiRecords -> updateTransaksiAdapter(
                    (it.peekContent() as BusinessDashboardMainViewModel.State.PopulateBottomSheetPemasukanTransaksiRecords).transaksiRecords)
                else -> {}
            }
        })
    }

    private fun updateTransaksiAdapter(records: List<DataHolder>) {
        // Data of transaksi here

        val categories = RemoteConfigUtils.SelectCategory.getDebitCategoriesNew()
        val gson: Gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<Category?>?>() {}.type
        val categoriesToDisplay: List<com.bukuwarung.activities.expense.data.Category> = gson.fromJson(categories, jsonType)

        val filteredCategoryList: ArrayList<DataHolder> = arrayListOf()

        val cat = HashSet<String>()

        for (category in categoriesToDisplay) {
            cat.add(category.categoryName)
        }

        for (data in records) {
            if (cat.contains(data.name) || data.name!!.contentEquals("Pengeluaran")) {
                filteredCategoryList.add(data)
                cat.remove(data.name)
            }
        }

        for (category in cat) {
                filteredCategoryList.add(
                    DataHolder.CategoryRowHolder(
                        DailySummary(
                            category,
                            "",
                            "",
                            "",
                            0
                        )
                    )
                )
        }
        cashAdapter.refreshView(filteredCategoryList, categoriesToDisplay)
    }

    companion object {
        fun getInstance() = BusinessTransaksiDebitFragment()
    }

    override fun goToDetail(
        id: String,
        isExpense: Boolean,
        status: Int,
        isDetailTransaksi: Boolean,
        isAutoRecordTxn: Boolean
    ) {
        if (context != null && id.isNotNullOrEmpty()) {
            val intent = CashTransactionDetailActivity.getNewIntent(requireContext(), id, false)
            intent.putExtra(CashTransactionDetailActivity.IS_EXPENSE_PARAM, isExpense)
            intent.putExtra(CashTransactionDetailActivity.TRX_STATUS_PARAM, status)
            intent.putExtra(CashTransactionDetailActivity.IS_DETAIL_TRANSACTION, isDetailTransaksi)
            intent.putExtra(
                CashTransactionDetailActivity.IS_AUTO_RECORD_TRANSACTION,
                isAutoRecordTxn
            )
            startActivity(intent)
        }

    }

    override fun goToCategory(id: String, name: String,trxCount: Int) {
        if (id.isNotNullOrEmpty()) {
            try {
                val intent = Intent(context, CategoryTransactionsActivity::class.java)
                intent.putExtra(CategoryTransactionsActivity.CASH_CATEGORY_ID, id)
                intent.putExtra(CategoryTransactionsActivity.START_DATE, (activity as BusinessDashboardMainActivity ).getMonthStartDate())
                intent.putExtra(CategoryTransactionsActivity.END_DATE, (activity as BusinessDashboardMainActivity ).getMonthEndDate())
                AppAnalytics.trackEvent("open_cash_transaction_list", "", "")
                startActivity(intent)
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.CATEGORY_NAME, name)
                propBuilder.put(AnalyticsConst.NO_TRX, trxCount)
                propBuilder.put(AnalyticsConst.TYPE, AnalyticsConst.TRANSAKSI)
                propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.BD)

                AppAnalytics.trackEvent(AnalyticsConst.BD_CATEGORY_CLICK, propBuilder)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}