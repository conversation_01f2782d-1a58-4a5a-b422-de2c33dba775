package com.bukuwarung.activities.businessdashboard.view.BusinessDashboardAutoRecordSummary

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bukuwarung.activities.businessdashboard.view.TransactionCategoryBreakUpFragment

class TransactionCardBottomSheetViewPager(fragment: Fragment,var sdate: String,var edate : String,var function: (isIncome:Boolean,isExpanded : Boolean) -> Unit): FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> TransactionCategoryBreakUpFragment.createInstance(isIncome = true,sdate,edate) { isExpanded ->
                function(true,isExpanded)
            }
            else -> TransactionCategoryBreakUpFragment.createInstance(isIncome = false,sdate,edate) { isExpanded ->
                function(false,isExpanded)
            }
        }
    }
}