package com.bukuwarung.activities.businessdashboard.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bukuwarung.activities.businessdashboard.view.BusinessTransaksiCreditFragment
import com.bukuwarung.activities.businessdashboard.view.BusinessTransaksiDebitFragment

class BusinessTransactionBottomSheetViewPager(fragment: Fragment): FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> BusinessTransaksiCreditFragment.getInstance()
            else -> BusinessTransaksiDebitFragment.getInstance()
        }
    }
}