package com.bukuwarung.activities.businessdashboard.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.dataholder.CashDataHolder
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.constants.Tag
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.recordException
import com.bumptech.glide.Glide
import kotlin.math.abs

class BusinessTransaksiAdapter(val listener: BusinessTransaksiListener? = null) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var cashDataHolderList: List<DataHolder?>? = emptyList()
    private var categoriesToDisplay: List<com.bukuwarung.activities.expense.data.Category>? = emptyList()

    interface BusinessTransaksiListener {
        fun goToDetail(id: String, isExpense: Boolean, status: Int, isDetailTransaksi: <PERSON>ole<PERSON>,isAutoRecordTxn : Boolean)
        fun goToCategory(id: String, name: String)
    }

    class CategorySummaryViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvExpense: TextView = view.findViewById(R.id.tvExpense)
        val name: TextView = view.findViewById(R.id.name)
        val trxCount: TextView = view.findViewById(R.id.trxCount)
        val tvIncome: TextView = view.findViewById(R.id.tvIncome)
        val catImage: ImageView = view.findViewById(R.id.bg)
    }

    class NoResultViewHolder(view: View?) : RecyclerView.ViewHolder(view!!)
    class LastViewHolder(view: View?) : RecyclerView.ViewHolder(view!!)

    private fun bindCatSummaryViewHolder(summaryViewHolder: CategorySummaryViewHolder, dayRowHolder: DataHolder.CategoryRowHolder) {
        val context = summaryViewHolder.itemView.context
        summaryViewHolder.itemView.setOnClickListener {
            if(dayRowHolder.cash.trxCount > 0) {
                listener?.goToCategory(dayRowHolder.cash.id, dayRowHolder.cash.name)
            }
        }
        summaryViewHolder.name.text = dayRowHolder.cash.name
        try {
            val trxCount = dayRowHolder.cash.getTrxCount()
            summaryViewHolder.trxCount.text = context.getString(R.string.cash_tab_trx_count, trxCount)
        } catch (ex: Exception) {
            summaryViewHolder.trxCount.text = context.getString(R.string.cash_tab_trx_count, 0)
            ex.recordException()
        }
        val sbIncome = StringBuilder()
        sbIncome.append(Utility.getCurrency())
        val incomeDouble: Double =
            if (Utility.isBlank(dayRowHolder.cash.income)) 0.0 else dayRowHolder.cash.income.toDouble()
        sbIncome.append(Utility.formatCurrency(abs(incomeDouble)))
        summaryViewHolder.tvIncome.text = sbIncome.toString()

        /*TODO set expense value from expense property*/
        val buyingPrice = dayRowHolder.cash.buyingPrice
        val expenseDouble: Double =
            if (Utility.isBlank(buyingPrice)) 0.0 else buyingPrice.toDouble()
        if (expenseDouble > 0) {
            val strExpense = Utility.getCurrency() + Utility.formatCurrency(expenseDouble)
            summaryViewHolder.tvExpense.text = strExpense
        } else {
            summaryViewHolder.tvExpense.text = "0"
        }

        val image = categoriesToDisplay?.firstOrNull {
            it.categoryName == dayRowHolder.name
        }
        Glide.with(context).load(image?.categoryImage).placeholder(R.drawable.penjualan).into(summaryViewHolder.catImage)
    }


    override fun getItemViewType(i: Int): Int {
        return cashDataHolderList!![i]!!.tag
    }

    override fun getItemId(i: Int): Long {
        val dataHolder = cashDataHolderList!![i]
        return if (dataHolder is CashDataHolder) {
            (dataHolder.getTag().toString() + ":" + dataHolder.cashCategoryEntity.cashTransactionid).hashCode().toLong()
        } else {
            (dataHolder?.tag.toString() + ":" + i).hashCode().toLong()
        }
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, tag: Int): RecyclerView.ViewHolder {
        return when (tag) {
            Tag.VIEW_CAT_SUMMARY -> {
                val categoryItemView = LayoutInflater.from(viewGroup.context).inflate(R.layout.category_summary_item, viewGroup, false)
                CategorySummaryViewHolder(categoryItemView)
            }
            Tag.LAST_ROW -> {
                val inflate = LayoutInflater.from(viewGroup.context).inflate(R.layout.activity_main_view_customer_last, viewGroup, false)
                LastViewHolder(inflate)
            }
            else -> {
                val noresult = LayoutInflater.from(viewGroup.context).inflate(R.layout.no_search_result_view, viewGroup, false)
                NoResultViewHolder(noresult)
            }
        }
    }

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, i: Int) {
        if (cashDataHolderList == null || cashDataHolderList!![i] == null) return
        val itemViewType = viewHolder.itemViewType
        if (itemViewType == Tag.VIEW_CAT_SUMMARY) {
            val dataHolder = cashDataHolderList!![i]
            val summaryViewHolder = viewHolder as CategorySummaryViewHolder
            if (dataHolder != null) {
                bindCatSummaryViewHolder(
                    summaryViewHolder,
                    dataHolder as DataHolder.CategoryRowHolder
                )
            }
        }
    }

    override fun getItemCount(): Int {
        return if (cashDataHolderList == null) {
            0
        } else cashDataHolderList!!.size
    }

    fun refreshView(records: List<DataHolder>, categoriesToDisplay: List<com.bukuwarung.activities.expense.data.Category>) {
        this.cashDataHolderList = records
        this.categoriesToDisplay = categoriesToDisplay
        notifyDataSetChanged()
    }
}