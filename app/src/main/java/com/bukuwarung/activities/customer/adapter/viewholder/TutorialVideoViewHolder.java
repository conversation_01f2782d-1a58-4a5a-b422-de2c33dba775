package com.bukuwarung.activities.customer.adapter.viewholder;

import android.content.ComponentName;
import android.content.Intent;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.customer.CustomerTab;
import com.bukuwarung.activities.customer.adapter.CustomerAdapter;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.utils.ImageUtils;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;


public final class TutorialVideoViewHolder extends RecyclerView.ViewHolder {

    private TextView userName;
    private TextView tvTransInstruction;
    private TextView userMessage;
    private AppCompatImageView userImage;
    private TextView userReviewDate;

    private RelativeLayout askHelp;
    private RelativeLayout customerWalkThruBtn;
    final CustomerAdapter customerAdapter;

    public TutorialVideoViewHolder(final CustomerAdapter customerAdapter, final View view, CustomerTab fragment) {
        super(view);
        this.customerAdapter = customerAdapter;
        this.askHelp = view.findViewById(R.id.customerHelpBtn);
        this.customerWalkThruBtn = view.findViewById(R.id.customerWalkThruBtn);
        this.tvTransInstruction = view.findViewById(R.id.no_trans_header);
        try {
            this.userName = view.findViewById(R.id.userName);
            this.userName.setText(AppConfigManager.getInstance().getUtangUserName());

            this.userImage = view.findViewById(R.id.userImage);
            if (!Utility.isBlank(AppConfigManager.getInstance().getUtangUserImage())) {
                ImageUtils.loadImageCircleCropped(
                    view.getContext(), userImage,
                    AppConfigManager.getInstance().getUtangUserImage(),
                    R.color.white, R.mipmap.default_icon
                );
            } else {
                ImageUtils.loadImageCircleCropped(
                    view.getContext(), userImage,
                    R.drawable.image_profile_1,
                    R.color.white, R.mipmap.default_icon
                );
            }

            this.userMessage = view.findViewById(R.id.social_message);
            this.userMessage.setText(AppConfigManager.getInstance().getUtangUserMsg());

            this.userReviewDate = view.findViewById(R.id.messageDate);
            this.userReviewDate.setText(AppConfigManager.getInstance().getUtangReviewDate());
            if(RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm()){
                tvTransInstruction.setVisibility(View.VISIBLE);
            }else{
                tvTransInstruction.setVisibility(View.INVISIBLE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        this.askHelp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent sendIntent = new Intent("android.intent.action.MAIN");
                    sendIntent.setComponent(new ComponentName("com.whatsapp", "com.whatsapp.Conversation"));
                    sendIntent.setAction(Intent.ACTION_SEND);
                    sendIntent.setType("text/plain");
                    sendIntent.putExtra(Intent.EXTRA_TEXT, "");
                    sendIntent.putExtra("jid", FeaturePrefManager.getInstance().getWhatsappId() + "@s.whatsapp.net");
                    sendIntent.setPackage("com.whatsapp");
                    customerAdapter.getContext().startActivity(sendIntent);
                    AppAnalytics.trackEvent("utang_help_requested");

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        this.customerWalkThruBtn.setOnClickListener(v -> {
            fragment.showOnboardingTutorial();
        });
    }
}