package com.bukuwarung.activities.customer.download;

import android.app.ProgressDialog;
import android.os.Build;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.activities.customer.CustomerTab;
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transactionreport.TransactionReportActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.Utility;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskExecutors;

import java.util.List;

public final class DownloadPdfClickHandler implements OnClickListener {
    final CustomerTab customerTab;

    public DownloadPdfClickHandler(CustomerTab customerTab ) {
        this.customerTab = customerTab;
    }

    public final void onClick(View view) {
        if(!Utility.hasInternet()) {
            new CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    customerTab.getActivity(),
                    customerTab.getActivity().getSupportFragmentManager()
            );
        }
        List customerList = customerTab.customerListViewModel.getCustomerList();
        if (Utility.hasInternet() && !ListUtils.isEmpty(customerList)) {
            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23) {
                this.customerTab.requestPermissions(PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR, PermissionConst.WRITE_EXTERNAL_STORAGE);
            } else{
                downloadReportPdf(customerList);
            }
        }
    }

    private String dateStrFromTextView(TextView tv){
        String inputText = tv.getText().toString();
        return Utility.getStorableDateString(DateTimeUtils.convertToDateDDMMYYYY(inputText));
    }

    public final void downloadReportPdf(List<? extends DataHolder> matchingTransactions) {
        ProgressDialog progressDialog = ComponentUtil.showProgressDialog(customerTab.getActivity(),customerTab.getString(R.string.preparing_customer_pdf));
        List<? extends DataHolder> list = matchingTransactions;
        Task<PrepareBookCustomerListPayLoad.ReportTaskResult> task = new PrepareBookCustomerListPayLoad().getTask(matchingTransactions);
        task.continueWith(TaskExecutors.MAIN_THREAD, new DisplayDownloadedReportPDF(customerTab, progressDialog));

    }
}
