package com.bukuwarung.activities.customer.adapter.viewholder;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.customer.adapter.CustomerAdapter;


public final class CustomerViewHolder extends RecyclerView.ViewHolder {

    private TextView balance;
    private AppCompatImageView photo;
    private TextView nameInitials;
    private TextView address;
    private TextView dueDate;
    private TextView name;
    final CustomerAdapter adapter;
    private TextView balanceState;
    private ImageView whatsappContact;
    private ImageView whatsappPic;

    public CustomerViewHolder(CustomerAdapter customerAdapter, View view) {
        super(view);
        this.adapter = customerAdapter;
        this.name = view.findViewById(R.id.tvName);
        this.address = view.findViewById(R.id.tvAddress);
        this.dueDate = view.findViewById(R.id.tv_reminder);
        this.balance = view.findViewById(R.id.balanceTv);
        this.balanceState = view.findViewById(R.id.balanceState);
        this.nameInitials = view.findViewById(R.id.nameInitials);
        this.photo = view.findViewById(R.id.photo);
    }

    public final TextView getName() {
        return this.name;
    }

    public final TextView getAddress() {
        return this.address;
    }

    public final TextView getBalance() {
        return this.balance;
    }

    public final TextView getBalanceState() {
        return this.balanceState;
    }

    public final TextView getNameInitials() {
        return this.nameInitials;
    }

    public final AppCompatImageView getPhoto() {
        return this.photo;
    }

    public final TextView getDueDate() {
        return this.dueDate;
    }
}