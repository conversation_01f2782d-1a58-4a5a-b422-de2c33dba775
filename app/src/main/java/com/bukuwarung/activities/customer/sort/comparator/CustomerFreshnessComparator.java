package com.bukuwarung.activities.customer.sort.comparator;

import com.bukuwarung.activities.customer.sort.SortOrder;
import com.bukuwarung.database.entity.CustomerEntity;

import java.util.Comparator;

public final class CustomerFreshnessComparator implements Comparator<CustomerEntity> {

    SortOrder sortOrder;
    public CustomerFreshnessComparator(SortOrder order){
        this.sortOrder=order;
    }

    @Override
    public int compare(CustomerEntity customerEntity1, CustomerEntity customerEntity2) {
        long modifiedDate2 = customerEntity2.lastModifiedAt;
        long modifiedDate1 = customerEntity1.lastModifiedAt;
        if(sortOrder == SortOrder.DESC)
            return (Long.compare(modifiedDate2, modifiedDate1));
        else
            return (Long.compare(modifiedDate1, modifiedDate2));
    }
}
