package com.bukuwarung.activities.customer.sort.comparator;

import com.bukuwarung.activities.customer.sort.SortOrder;
import com.bukuwarung.database.entity.CustomerEntity;

import java.util.Comparator;

public final class CustomerNameComparator implements Comparator<CustomerEntity> {

    SortOrder sortOrder;

    public CustomerNameComparator(SortOrder order) {
        this.sortOrder = order;
    }

    @Override
    public int compare(CustomerEntity customerEntity1, CustomerEntity customerEntity2) {
        String name2 = customerEntity2.name;
        String name1 = customerEntity1.name;
        if (name2 != null && name1 != null && !name2.isEmpty() && !name1.isEmpty()) {
            if (sortOrder == SortOrder.ASC)
                return name1.toLowerCase().compareTo(name2.toLowerCase());
            else
                return name2.toLowerCase().compareTo(name1.toLowerCase());
        }
        return -1;
    }
}
