package com.bukuwarung.activities.customer;

import com.bukuwarung.Application;
import com.bukuwarung.activities.addcustomer.CustomerForm;
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.Date;

public final class CustomerSaveFirstTransaction implements Runnable {
    final AddCustomerActivity activity;
    final double amount;
    final String note;
    final ArrayList transactionPictureStrList;

    public CustomerSaveFirstTransaction(AddCustomerActivity addCustomerActivity, double d, String str, ArrayList arrayList) {
        this.activity = addCustomerActivity;
        this.amount = d;
        this.note = str;
        this.transactionPictureStrList = arrayList;
    }

    public final void run() {
        String storableDateString = activity.transactionDate;
        TransactionRepository transactionRepository = TransactionRepository.getInstance(Application.getAppContext());
        String businessId = User.getBusinessId();
        CustomerForm customer = this.activity.getCustomer();
        int smsStatus = customer.getSmsEnabled()?1:0;
        transactionRepository.saveNewTransaction(businessId, customer.getId(), this.amount, storableDateString, this.note,smsStatus);
    }
}
