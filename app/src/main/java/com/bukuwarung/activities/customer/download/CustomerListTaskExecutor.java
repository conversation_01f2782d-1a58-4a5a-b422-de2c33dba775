package com.bukuwarung.activities.customer.download;

import android.net.Uri;

import com.bukuwarung.Application;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.api.ApiRepository;
import com.bukuwarung.api.model.DownloadReortRequest;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ReportUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.TaskCompletionSource;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.List;

import retrofit2.Call;

final class CustomerListTaskExecutor implements Runnable {

    final int filter;
    final List customerList;
    final TaskCompletionSource tcs;
    final PrepareBookCustomerListPayLoad prepareBookTransactionsReportPayLoad;

    CustomerListTaskExecutor(PrepareBookCustomerListPayLoad customerListReportPayload, int filter, List list, TaskCompletionSource taskCompletionSource) {
        this.prepareBookTransactionsReportPayLoad = customerListReportPayload;
        this.filter = filter;
        this.customerList = list;
        this.tcs = taskCompletionSource;
    }

    public final void run() {
        try {
            JsonObject payload = customerListPayLoad(this.filter, this.customerList);
            AppAnalytics.trackEvent("download_customer_list");
            Call postAccountReport = ApiRepository.getReportsApiService().postAccountCustomersPdfRequest(payload);
            Uri callApiAndDownloadPdf = ReportUtils.callReportApi(null, postAccountReport,"BukuWarung_Pelanggan",true);
            if (callApiAndDownloadPdf != null) {
                AppAnalytics.trackEvent("download_customer_list_success");
                this.tcs.setResult(new PrepareBookCustomerListPayLoad.ReportTaskResult(callApiAndDownloadPdf, null));
                return;
            }
        } catch (Exception e) {
            this.tcs.setResult(new PrepareBookCustomerListPayLoad.ReportTaskResult(null, e.getMessage()));
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
//        AppAnalytics.trackEvent("download_customer_list_failure");
    }

    public final JsonObject customerListPayLoad(int filter, List<CustomerEntity> customerList) {

        String phoneWithCountryCode = SessionManager.getInstance().getCountryCode()+""+ User.getUserId();
        BookEntity bookSync = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
        Integer countAllVisibleCustomersSync = CustomerRepository.getInstance(Application.getAppContext()).countAllVisibleCustomersSync(User.getBusinessId());
        JsonObject reportObj = new JsonObject();
        reportObj.addProperty("language", SessionManager.getInstance().getAppLanguage());
        reportObj.addProperty("filter", filter);
        reportObj.addProperty("businessName", bookSync.businessName);
        reportObj.addProperty("businessCaption", bookSync.businessTagLine);
        reportObj.addProperty("businessPhone", phoneWithCountryCode);
        reportObj.addProperty("businessImage", bookSync.businessImage);
        reportObj.addProperty("totalCustomers", (Number) countAllVisibleCustomersSync);
        JsonArray jsonArray = new JsonArray();

        for (CustomerEntity customerEntity : customerList) {
            JsonObject dataObject = new JsonObject();
            dataObject.addProperty("name", customerEntity.name);
            dataObject.addProperty("address", customerEntity.address);
            dataObject.addProperty("phone", customerEntity.phone);
            dataObject.addProperty("country_code", customerEntity.countryCode);
            dataObject.addProperty("balance", (Number) customerEntity.balance);
            jsonArray.add(dataObject);
        }
        reportObj.add("customers", jsonArray);
        return reportObj;
    }
}
