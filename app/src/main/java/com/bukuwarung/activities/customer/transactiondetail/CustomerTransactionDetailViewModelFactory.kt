package com.bukuwarung.activities.customer.transactiondetail

import android.os.Bundle
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.savedstate.SavedStateRegistryOwner
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.TransactionRepository

class CustomerTransactionDetailViewModelFactory(
        private val owner: SavedStateRegistryOwner,
        private val transactionRepository: TransactionRepository,
        private val customerRepository: CustomerRepository,
        private val transactionId: String,
        private val customerId: String,
        defaultArgs: Bundle? = null
) : AbstractSavedStateViewModelFactory(owner, defaultArgs) {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel?> create(
            key: String,
            modelClass: Class<T>,
            handle: SavedStateHandle
    ): T {
        return CustomerTransactionDetailViewModel(
                transactionRepository,
                customerRepository,
                transactionId,
                customerId
        ) as T
    }
}