package com.bukuwarung.activities.customer.download;

import android.app.ProgressDialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;

import com.bukuwarung.activities.customer.CustomerTab;
import com.bukuwarung.utils.NotificationUtils;
import com.google.android.gms.tasks.Continuation;
import com.google.android.gms.tasks.Task;

public final class DisplayDownloadedReportPDF implements Continuation<PrepareBookCustomerListPayLoad.ReportTaskResult, Object> {
    final ProgressDialog progressDialog;
    final CustomerTab customerTab;

    DisplayDownloadedReportPDF(CustomerTab customerTab, ProgressDialog pDialog) {
        this.customerTab = customerTab;
        this.progressDialog = pDialog;
    }

    public Object then(Task<PrepareBookCustomerListPayLoad.ReportTaskResult> task) {
        try {
            this.progressDialog.dismiss();
            PrepareBookCustomerListPayLoad.ReportTaskResult apiResult = task.getResult();
            if(apiResult == null || apiResult.contentUri == null){
                NotificationUtils.alertToast("Report service is not available. Please try later.");
            }else{
                Intent intent = new Intent("android.intent.action.VIEW", apiResult.contentUri);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                customerTab.getContext().startActivity(intent);
            }
        }catch (ActivityNotFoundException ne){
            NotificationUtils.alertToast("PDF viewer not found.");
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
