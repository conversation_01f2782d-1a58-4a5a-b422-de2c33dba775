package com.bukuwarung.activities.customer.sort.comparator;

import com.bukuwarung.activities.customer.sort.SortOrder;
import com.bukuwarung.database.entity.CustomerEntity;

import java.util.Comparator;

public final class CustomerBalanceComparator implements Comparator<CustomerEntity> {

    SortOrder sortOrder;
    public CustomerBalanceComparator(SortOrder order){
        this.sortOrder=order;
    }

    @Override
    public int compare(CustomerEntity customerEntity1, CustomerEntity customerEntity2) {
        double bal2 = Math.abs(customerEntity2.balance);
        double bal1 = Math.abs(customerEntity1.balance);
        if(sortOrder == SortOrder.ASC)
            return Double.compare(bal1,bal2);
        else
            return Double.compare(bal2,bal1);
    }
}
