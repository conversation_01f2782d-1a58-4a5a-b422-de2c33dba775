package com.bukuwarung.activities.customer.transactiondetail

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Html
import android.view.Gravity
import android.view.View
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.bukuwarung.R
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity
import com.bukuwarung.activities.addcustomer.detail.CustomerActivity
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.expense.dialog.DeleteTrxDialog
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.print.*
import com.bukuwarung.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.activities.print.setup.SetupPrinterActivity
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.transaction.customer.add.AddTransactionActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.bluetooth_printer.model.printTypes.BasePrintType
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.constants.AnalyticsConst.EVENT_CLICK_ADD_CUSTOMER_BTN
import com.bukuwarung.constants.AnalyticsConst.INVOICE
import com.bukuwarung.constants.AnalyticsConst.SMS_CHECKBOX_ENABLED
import com.bukuwarung.constants.AnalyticsConst.UTANG
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.database.entity.TransactionEntityType
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.dialogs.printer.OpenSetupPrinterDialog
import com.bukuwarung.dialogs.printer.PrintingDialog
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.*
import com.bukuwarung.utils.RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import kotlinx.android.synthetic.main.activity_customer_transaction_detail.*
import kotlinx.android.synthetic.main.bottom_share_invoice_layout.view.*
import kotlinx.android.synthetic.main.utang_payment_receipt_layout.*
import kotlinx.android.synthetic.main.utang_payment_receipt_layout.view.*
import kotlinx.android.synthetic.main.utang_receipt_layout.*
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvTransactionDate
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvTransactionNominal
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvWarungName
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvWarungPhone
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tv_cst_name
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tv_cst_phone
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tv_transaction_note

class CustomerTransactionDetailActivity : AppActivity() {

    internal lateinit var viewModel: CustomerTransactionDetailViewModel
        private set

    internal lateinit var handler: Handler
    internal var type: Int = 0

    internal var suppliedAppConfigManager: AppConfigManager? = null // for test only

    // these global objects is for printing only
    private var customer: CustomerEntity? = null
    private var transaction: TransactionEntity? = null
    private var customerPayment: CustomerTransactionDetailState? = null
    private var printingDialog: PrintingDialog? = null
    private var customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
    private var transactionNote: String = ""
    private var utangReceipt: UtangReceipt? = null
    private var utangOldReceipt: UtangOldReceipt? = null
    private var showOldForm = false
    private var openUtangForm = false
    private var invoiceData: InvoiceDataBlock? = null
    private var showNewUtangInvoice = false

    private fun initViewModel() {
        val trxId: String = intent.getStringExtra(TRX_ID_PARAM) ?: "-"
        val cstId: String = intent.getStringExtra(CST_ID_PARAM) ?: "-"

        val factory = CustomerTransactionDetailViewModelFactory(
            this,
            TransactionRepository.getInstance(this),
            CustomerRepository.getInstance(this),
            trxId, cstId
        )

        this.viewModel = ViewModelProviders.of(this, factory)
            .get(CustomerTransactionDetailViewModel::class.java)
    }

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)

        initViewModel()
        handler = Handler()

        setupView()
    }

    /**
     * A hack because we havent implemented DI in project.
     */
    internal fun setViewModel(newViewModel: CustomerTransactionDetailViewModel) {
        viewModel.stateData.removeObservers(this)
        this.viewModel = newViewModel
        observeData()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                EDIT_TRX_REQUEST_CODE -> {
                    data?.getStringExtra(TRX_ID_EDIT_PARAM)?.let { newTrxId ->
                        viewModel.setTransactionId(newTrxId)
                    }
                }
                SetupPrinterActivity.RECORD_DETAIL -> startPrinting()
            }
        }
    }

    private fun setupView() {
        setContentView(R.layout.activity_customer_transaction_detail)
        closeBtn.setOnClickListener { finish() }
        showNewUtangInvoice = RemoteConfigUtils.shouldShowNewUtangInvoice()
        showOldForm = canShowOldTransactionForm()
        observeData()

        var invoiceDataBlock: String = RemoteConfigUtils.INVOICE_DATA.getInvoiceDataBlock()
        val type = object  : TypeToken<InvoiceDataBlock>() {}.type
        val gson: Gson = GsonBuilder().create()
        try {
            invoiceData = gson.fromJson(invoiceDataBlock, type)
        } catch (e: JsonSyntaxException) {
            invoiceDataBlock = RemoteConfigUtils.INVOICE_DATA.getInvoiceDataFailsafeBlock()
            invoiceData = gson.fromJson(invoiceDataBlock, type)
        }
    }

    override fun finish() {
        super.finish()
        if (intent.getStringExtra("from").orEmpty() == "home") {
            MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER)
        }
    }

    private fun observeData() {
        viewModel.stateData.observe(this, Observer { state ->
            showBasedOnState(state)
        })
    }

    private fun showBasedOnState(state: CustomerTransactionDetailState) {
        when (state.currentState) {
            CustomerTransactionDetailStateType.Loading -> {
                loadingContainer.visibility = View.VISIBLE
                notFoundContainer.visibility = View.GONE
                mainContainer.visibility = View.GONE
                editBtn.visibility = View.GONE
                deleteBtn.visibility = View.GONE
            }
            CustomerTransactionDetailStateType.NotFound -> {
                loadingContainer.visibility = View.GONE
                notFoundContainer.visibility = View.VISIBLE
                mainContainer.visibility = View.GONE
                editBtn.visibility = View.GONE
                deleteBtn.visibility = View.GONE

                handler.postDelayed({
                    finish()
                }, 1000)
            }
            CustomerTransactionDetailStateType.Loaded -> {
                loadingContainer.visibility = View.GONE
                notFoundContainer.visibility = View.GONE
                mainContainer.visibility = View.VISIBLE
                editBtn.visibility = View.VISIBLE
                deleteBtn.visibility = View.VISIBLE

                customerPayment = state
                customerTransactionSummaryDto = state.customerTransactionSummaryDto

                state.transaction?.let {
                    val transactionType = if (it.amount >= 0) {
                        1
                    } else {
                        -1
                    }

                    type = if (it.amount >= 0) {
                        1
                    } else {
                        -1
                    }

                    editBtn.visibility = (!it.isPaymentTransaction).asVisibility()
                    deleteBtn.visibility = (!it.isPaymentTransaction).asVisibility()
//                    utang_receipt.visibility = (!it.isPaymentTransaction).asVisibility()
//                    utang_payment_receipt.visibility = it.isPaymentTransaction.asVisibility()

                    customer = state.customer
                    transaction = state.transaction

                    if (it.isPaymentTransaction) {
                        loadPaymentData(state)
                    } else {
                        populateReceipt(it)
                    }

                    tv_show_details.visibility = (transaction!!.transactionType == TransactionEntityType.CASH_TRANSACTION).asVisibility()
                    editBtn.visibility = (transaction!!.transactionType != TransactionEntityType.CASH_TRANSACTION && transaction!!.transactionType != TransactionEntityType.PAYMENT).asVisibility()
                    deleteBtn.visibility = (transaction!!.transactionType != TransactionEntityType.CASH_TRANSACTION).asVisibility()
                    editBtn.setOnClickListener { _ -> goToEditPage(it.transactionId) }
                    deleteBtn.setOnClickListener { _ -> showDeleteDialog(it.transactionId) }
                    bottom_container.bt_print_invoice.setOnClickListener { checkPrintRequirement(transactionType) }
                    bottom_container.bt_general_share.setOnClickListener { _ -> shareTrx(it.isPaymentTransaction, useWA = false) }
                    bottom_container.bt_share_whatsapp.setOnClickListener { _ ->

                        shareTrx(it.isPaymentTransaction, useWA = true)
                        val propBuilder = AppAnalytics.PropBuilder().apply {
                            put(ENTRY_POINT2, UTANG)
                            put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsUtang())
                        }
                        AppAnalytics.trackEvent("click_WA_share_receipt", propBuilder)
                    }
                    bottom_container.tv_open_form.setOnClickListener {
                        openUtangForm = true
                        val intent: Intent
                        if (showOldForm) {
                            intent = Intent(this, AddCustomerActivity::class.java)
                        } else {
                            intent = Intent(this, CustomerActivity::class.java)
                            intent.putExtra(CustomerActivity.IS_EDIT_TRANSACTION, false)
                            intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, AppConst.DEBIT)
                        }
                        startActivity(intent)
                        val propBuilder = AppAnalytics.PropBuilder().apply {
                            put(ENTRY_POINT2, INVOICE)
                        }
                        AppAnalytics.trackEvent(EVENT_CLICK_ADD_CUSTOMER_BTN, propBuilder)
                        finish()
                    }
                    tv_show_details.setOnClickListener {
                        val cashTransaction = viewModel.getCashTransactionByCustomerTransactionId(transaction?.transactionId)
                            ?: return@setOnClickListener
                        val cashDetailIntent = CashTransactionDetailActivity.getNewIntent(this, cashTransaction.cashTransactionId)
                        startActivity(cashDetailIntent)
                    }
                }
            }
            else -> {}
        }
    }

    private fun loadPaymentData(state: CustomerTransactionDetailState) {
        when (state.currentPaymentState) {
            CustomerTransactionPaymentDetailStateType.Loading -> {
                payment_info_loader.visibility = View.VISIBLE
//                utang_payment_receipt.visibility = View.GONE
                payment_info_error.visibility = View.GONE
            }
            CustomerTransactionPaymentDetailStateType.Loaded -> {
                payment_info_loader.visibility = View.GONE
//                utang_payment_receipt.visibility = View.VISIBLE
                payment_info_error.visibility = View.GONE

                populatePaymentReceipt(state)
            }
            CustomerTransactionPaymentDetailStateType.Error -> {
                payment_info_loader.visibility = View.GONE
//                utang_payment_receipt.visibility = View.GONE
                payment_info_error.visibility = View.VISIBLE
            }
            else -> {}
        }
    }

    private fun shareTrx(isPayment: Boolean, useWA: Boolean) {
        val viewToShare = if (isPayment) cv_utang_payment_receipt else cv_utang_receipt
        generateAndShareViewImage(this, "com.whatsapp", subMainContainer, "", useWA)
    }

    private fun generateAndShareViewImage(context: Context?, packageNm: String?, receiptLayout: View?, mobile: String?, useWA: Boolean) {
        try {
            val propBuilder = AppAnalytics.PropBuilder().apply {
                put(AnalyticsConst.IS_REMAINING_DEBT_SHOWN, sw_detail.isChecked)
                put(AnalyticsConst.IS_SETTLED_RECEIPT, (transactionNote == "Dilunaskan" || transactionNote == "Lunas"))
                put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CUSTOMER_DETAIL)
                put(AnalyticsConst.AMOUNT, transaction?.amount)
                put(AnalyticsConst.TRANSACTION_ID, transaction?.transactionId)
                put(AnalyticsConst.NOTA_STANDARD_ENABLED, showNewUtangInvoice)
            }



            AppAnalytics.trackEvent(AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT, propBuilder)
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(receiptLayout, false)
            val shareLayoutImage = ShareLayoutImage(getString(R.string.utang_invoice_sharing_text), context, packageNm, mobile, useWA, true)
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun checkPrintRequirement(transactionType: Int) {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PRINT_CUSTOMER_TRANSACTION,
            AppAnalytics.PropBuilder().put(AnalyticsConst.TYPE, if (transactionType == 1) AnalyticsConst.DEBIT else AnalyticsConst.CREDIT)
                .put(AnalyticsConst.IS_SETTLED_RECEIPT, (transactionNote == "Dilunaskan" || transactionNote == "Lunas"))
                .put(AnalyticsConst.TYPE, if (transactionType == 1) AnalyticsConst.DEBIT else AnalyticsConst.CREDIT)
                .put(AnalyticsConst.IS_REMAINING_DEBT_SHOWN, sw_detail.isChecked)
                .put(AnalyticsConst.TRANSACTION_ID, transaction?.transactionId)
                .put(AnalyticsConst.AMOUNT, transaction?.amount)
                .put(AnalyticsConst.NOTA_STANDARD_ENABLED, showNewUtangInvoice)
        )

        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PRINT_CUSTOMER_TRANSACTION, this)

        when (val printers: List<PrinterDataHolder>? = PrinterPrefManager(this).installedPrinters) {
            null -> {
                val intent = Intent(this, SetupPrinterActivity::class.java)
                val isPrinterPaired = PrinterPrefManager(this).defaultDeviceAddress.isNotEmpty()

                if (isPrinterPaired) {
                    startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                } else {
                    val dialog = OpenSetupPrinterDialog(this) {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                            AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CUSTOMER)
                        )

                        startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                    }
                    dialog.show()
                }

            }
            printers -> {
                when {
                    printers.isEmpty() -> {
                        val dialog = OpenSetupPrinterDialog(this) {
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CUSTOMER)
                            )
                            val intent = Intent(this, SetupPrinterActivity::class.java)
                            startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                        }

                        dialog.show()
                    }
                    printers.isNotEmpty() -> {
                        startPrinting()
                    }
                }
            }
        }
    }


    private fun goToEditPage(transactionId: String) {
        val isOldUTangFlow = RemoteConfigUtils.shouldShowOldUtangForm()
        if (isOldUTangFlow) {
            val intent = Intent(this, AddTransactionActivity::class.java)
            intent.putExtra("transactionId", transactionId)
            intent.putExtra("OPERATION_TYPE", "")
            startActivityForResult(intent, EDIT_TRX_REQUEST_CODE)
        } else {
            val intent = Intent(this, CustomerActivity::class.java)
            intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, type)
            intent.putExtra(CustomerActivity.TRANSACTING_USER_ENTITY, customer)
            intent.putExtra(CustomerActivity.IS_EDIT_TRANSACTION, true)
            intent.putExtra(CustomerActivity.TRANSACTION_ID, transactionId)
            intent.putExtra(CustomerActivity.IS_FROM_EDIT_TRANSACTION, true)
            startActivity(intent)
            finish()
        }
    }

    private fun showDeleteDialog(transactionId: String) {
        val dialog = DeleteTrxDialog(this) {
            if (it) {
                deleteCustomerTransaction(transactionId)
                finish()
            }
        }
        dialog.show()
    }

    private fun deleteCustomerTransaction(transactionId: String) {
        val t = Thread(DeleteCustomerTransactionRunnable(this, transactionId))
        t.start()
        try {
            t.join()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun populateReceipt(transactionEntity: TransactionEntity) {
        try {
            switch_layout.visibility = View.VISIBLE

            showAmountDetailToolTip()
            if (showNewUtangInvoice) {
                utangReceipt = UtangReceipt(this).apply {
                    setup {
                        setInvoiceData { invoiceData }
                        setBook { currentBook }
                        setTransaction { transactionEntity }
                        setCustomer {
                            CustomerRepository.getInstance(this@CustomerTransactionDetailActivity)
                                .getCustomerById(transactionEntity.customerId)
                        }
                        setSummaryDto { customerTransactionSummaryDto }
                        setGuestUser { SessionManager.getInstance().isGuestUser }
                    }
                }
            } else {
                utangOldReceipt = UtangOldReceipt(this).apply {
                    setup {
                        setBook { currentBook }
                        setTransaction { transactionEntity }
                        setCustomer {
                            CustomerRepository.getInstance(this@CustomerTransactionDetailActivity)
                                .getCustomerById(transactionEntity.customerId)
                        }
                        setSummaryDto { customerTransactionSummaryDto }
                        setGuestUser { SessionManager.getInstance().isGuestUser }
                    }
                }
            }

            viewModel.isReceiptExpanded.observe(this, Observer {
                if (showNewUtangInvoice) {
                    utangReceipt?.setup {
                        setExpanded { it }
                    }
                } else {
                    utangOldReceipt?.setup {
                        setExpanded { it }
                    }
                }
            })


            sw_detail.setOnCheckedChangeListener { _, b ->
                viewModel.setReceiptState(b)
            }

            subMainContainer.removeAllViews()
            if (showNewUtangInvoice) {
                subMainContainer.addView(utangReceipt)
            } else {
                subMainContainer.addView(utangOldReceipt)
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    private fun populatePaymentReceipt(state: CustomerTransactionDetailState) {
        try {
            switch_layout.visibility = View.VISIBLE
            showAmountDetailToolTip()

            val trxEntity = state.transaction ?: return
            val customerEntity = state.customer ?: return

            val receipt = layoutInflater.inflate(R.layout.utang_payment_receipt_layout, subMainContainer, false)
            /* var detailHeight = 0
             receipt.layout_transaction_detail.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                 override fun onGlobalLayout() {
                     detailHeight = receipt.layout_transaction_detail.height

                     receipt.layout_transaction_detail.viewTreeObserver.removeOnGlobalLayoutListener(this)
                     receipt.layout_transaction_detail.visibility = View.GONE
                 }
             })*/

            receipt.tvWarungName.text = currentBook.businessName.takeIf { it != "Usaha Saya" }
                ?: "-"
            receipt.tvWarungPhone.text = Utility.beautifyPhoneNumber(currentBook.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(currentBook.ownerId).takeIf { it.isNotBlank() }
                        ?: "-"
            receipt.tvTransactionDate.text = "%s %s".format(Utility.formatReceiptDate(trxEntity.date), Utility.getReadableTimeString(trxEntity.createdAt))

            receipt.tv_cst_name.text = customerEntity.name
            receipt.tv_cst_phone.text = Utility.beautifyPhoneNumber(customerEntity.phone)
            receipt.tvTransactionNominal.text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(trxEntity.amount))

            val showFreeCharge = AppConfigManager.getInstance().paymentFreeChargeStatus

            val amount = state.transaction.amount ?: 0.0
            if (amount > 0) {
                // payment in
                state.paymentCollection?.let {
                    // set transaction ID
                    receipt.tv_invoice_number.text = it.transactionId
                    // set free charge
                    receipt.tv_free_charge.visibility = showFreeCharge.asVisibility()
                    // set sender name (customer name)
                    receipt.tv_sender_name.text = "%s - %s".format(
                        it.paymentChannel, customerEntity.name
                            ?: "-"
                    )
                    // set receiver name
                    receipt.tv_recipient_name.text = "${it.receiverBank?.bankCode ?: "-"} - ${it.receiverBank?.accountHolderName ?: "-"}"
                    // set receiver bank no
                    receipt.tv_recipient_aaccount_number.text = it.receiverBank?.accountNumber
                        ?: "-"
                    // set payment notes
                    val desc = (it.description ?: "").let { str ->
                        if (str.isBlank() || str == "-") {
                            getString(R.string.label_payment_in_note_plain)
                        } else {
                            "%s - %s".format(getString(R.string.label_payment_in_note_plain), str)
                        }

                    }
                    receipt.tv_transaction_note.text = desc
                }
            } else {
                // payment out
                state.paymentDisbursement?.let {
                    // set transaction ID
                    receipt.tv_invoice_number.text = it.transactionId
                    // set free charge
                    receipt.tv_free_charge.visibility = showFreeCharge.asVisibility()
                    // set sender name (merchant name)
                    receipt.tv_sender_name.text = "%s - %s".format(
                        it.paymentChannel, currentBook?.businessOwnerName
                            ?: "-"
                    )
                    // set receiver name
                    receipt.tv_recipient_name.text = "${it.receiverBank?.bankCode ?: "-"} - ${it.receiverBank?.accountHolderName ?: "-"}"
                    // set receiver bank no
                    receipt.tv_recipient_aaccount_number.text = it.receiverBank?.accountNumber
                        ?: "-"
                    // set payment notes
                    val desc = (it.description ?: "").let { str ->
                        if (str.isBlank() || str == "-") {
                            getString(R.string.label_payment_in_note_plain)
                        } else {
                            "%s - %s".format(getString(R.string.label_payment_in_note_plain), str)
                        }

                    }
                    receipt.tv_transaction_note.text = desc
                }
            }

            receipt.tv_transaction_total_payment.text = customerTransactionSummaryDto?.total?.toDouble().asFormattedCurrency()
            receipt.tv_paid_total_payment.text = customerTransactionSummaryDto?.paid?.toDouble().asFormattedCurrency()
            receipt.tv_remaining_total_payment.text = customerTransactionSummaryDto?.remaining?.toDouble().asFormattedCurrency()

            viewModel.isReceiptExpanded.observe(this, Observer {
                receipt.layout_transaction_detail.visibility = it.asVisibility()
            })

            sw_detail.setOnCheckedChangeListener { _, b ->
                viewModel.setReceiptState(b)
            }

            receipt.tv_footer.text = Html.fromHtml(getString(R.string.label_transaction_security_guaranteed))


            subMainContainer.removeAllViews()
            subMainContainer.addView(receipt)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }




    private fun startPrinting() {
        transaction ?: return

        // not-null assertion is ok since both objects have checked using elvis operator
        val textToPrint: ArrayList<BasePrintType> = if (transaction!!.isPaymentTransaction.isTrue) {
            customerPayment ?: return
            ReceiptTextParser(this, currentBook).parseCustomerPaymentReceipt(
                customerPayment!!,
                customerTransactionSummaryDto,
                sw_detail.isChecked
            )
        } else {
            ReceiptTextParser(this, currentBook).parseCustomerReceipt(
                customer!!,
                transaction!!,
                customerTransactionSummaryDto,
                sw_detail.isChecked
            )
            customer ?: return
            if (showNewUtangInvoice) {
                utangReceipt?.getFormattedText()
            } else {
                utangOldReceipt?.getFormattedText()
            }
        } ?: return

        val printer = BluetoothPrinter(this, UTANG)
        printingDialog = printer.printingDialog
        printer.printPrintables(textToPrint, object : PermissionCallback {
            override fun onPermissionRequired(permissions: Array<String>) {
                printingDialog?.dismiss()
                permissionLauncher.launch(permissions)
            }
        })
    }

    private val permissionLauncher: ActivityResultLauncher<Array<String>> =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) {
            if (it.containsValue(false)) {
                Toast.makeText(this, R.string.location_permission_denied_message, Toast.LENGTH_SHORT)
                    .show()
            }
        }

    private fun showAmountDetailToolTip() {
        if (switch_layout.isVisible && !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TRX_AMOUNT_DETAIL_TOGGLE)) {
            TooltipBuilder.builder(this)
                .setText(getString(R.string.transaction_amount_detail_toggle))
                .setAnchor(anchor_tooltip)
                .setGravity(Gravity.CENTER)
                .build()
                .show()

            OnboardingPrefManager.getInstance().setHasFinishedForId(OnboardingPrefManager.TRX_AMOUNT_DETAIL_TOGGLE)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        printingDialog?.let {
            it.dismiss()
            printingDialog = null
        }
    }


    companion object {

        const val EDIT_TRX_REQUEST_CODE = 11
        const val TRX_ID_EDIT_PARAM = "TrxIdFromEdit"

        const val TRX_ID_PARAM = "transactionId"
        const val TRX_ID_PARAM_FOR_CASH_DETAIL = "TrxId"
        const val CST_ID_PARAM = "customerId"

        fun getNewIntent(origin: Context, trxId: String, customerId: String): Intent {
            val intent = Intent(origin, CustomerTransactionDetailActivity::class.java)
            intent.putExtra(TRX_ID_PARAM, trxId)
            intent.putExtra(CST_ID_PARAM, customerId)

            return intent
        }

    }

}