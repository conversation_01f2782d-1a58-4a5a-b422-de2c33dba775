package com.bukuwarung.activities.customer;

import android.app.Activity;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.NotificationUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

public class FeatureHelper {

    public static void showAppRater(final Context context,final int itemCount){
        if(canShowRater(itemCount)){
            final Dialog dialog = new Dialog(context);

            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            dialog.setContentView(R.layout.dialog_rate_app);
            Window window = dialog.getWindow();
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
            dialog.setCancelable(true);
            TextView laterTv = dialog.findViewById(R.id.btn_later);
            laterTv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    FeaturePrefManager.getInstance().setRateLaterDate(System.currentTimeMillis());
                    dialog.dismiss();
                    AppAnalytics.trackEvent("rate_us_later");
                }
            });
            TextView cancelTv = dialog.findViewById(R.id.btn_cancel);
            cancelTv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    FeaturePrefManager.getInstance().setRateLaterDate(-1);
                    FeaturePrefManager.getInstance().setRateTargetCustomer(itemCount*2);
                    dialog.dismiss();
                    AppAnalytics.trackEvent("rate_us_cancelled");
                }
            });

            TextView rateTv = dialog.findViewById(R.id.btn_ok);
            rateTv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    try{
                        Uri uri = Uri.parse("market://details?id=" + context.getPackageName());
                        Intent goToMarket = new Intent(Intent.ACTION_VIEW, uri);
                        // To count with Play market backstack, After pressing back button,
                        // to taken back to our application, we need to add following flags to intent.
                        goToMarket.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY |
                                Intent.FLAG_ACTIVITY_NEW_DOCUMENT |
                                Intent.FLAG_ACTIVITY_MULTIPLE_TASK);
                        try {
                            context.startActivity(goToMarket);
                        } catch (ActivityNotFoundException e) {
                            context.startActivity(new Intent(Intent.ACTION_VIEW,
                                    Uri.parse("http://play.google.com/store/apps/details?id=" + context.getPackageName())));
                        }
                        FeaturePrefManager.getInstance().setRateLaterDate(-1);
                        FeaturePrefManager.getInstance().setRateTargetCustomer(itemCount*3);
                        dialog.dismiss();
                        AppAnalytics.trackEvent("rate_us_accepted");
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            });

            dialog.show();
            AppAnalytics.trackEvent("rate_us_shown");
        }
    }

    public static final boolean canShowRater(int currentCount) {

        int targetCustomerCount = FeaturePrefManager.getInstance().getRateTargetCustomer();
        long laterDate = FeaturePrefManager.getInstance().getRateLaterDate();

        if(laterDate<0){
            return currentCount>targetCustomerCount;
        }else{
            long diff_in_millis = Math.abs( System.currentTimeMillis()-laterDate) ;
            long diff_in_days = diff_in_millis / (1000  * 60 * 60 * 24);
            return currentCount>targetCustomerCount && diff_in_days>4;
        }

    }

    public static final void shareApp(Activity activity) {
        try {
            StringBuilder sb = new StringBuilder();
            if(SessionManager.getInstance().getAppLanguage()==1){
                sb.append("Hi,");
                sb.append("\nBukuWarung is a fast, simple and secure application that I use to manage my business and send payment payment reminders.");
                sb.append("\nGet it for free at http://bukuwarung.com/app");
            }else{
                sb.append("Hei,");
                sb.append("\nBukuWarung adalah aplikasi cepat, sederhana dan aman yang saya gunakan untuk mengelola bisnis saya dan mengirim pengingat pembayaran biaya.");
                sb.append("\nDapatkan secara gratis di http://bukuwarung.com/app");
            }

            Intent share = new Intent(Intent.ACTION_SEND);
            share.setType("text/plain");
            share.addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET);
            share.putExtra(Intent.EXTRA_SUBJECT, "Share BukuWarung..");
            share.putExtra(Intent.EXTRA_TEXT, sb.toString());
            AppAnalytics.trackEvent("share_app_clicked");
            activity.startActivity(Intent.createChooser(share, "Choose an App!"));
        } catch (ActivityNotFoundException e) {
            NotificationUtils.alertError("WhatApp Not Installed");
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);AppAnalytics.trackEvent("error_event","detail",e.getMessage());;
        }
    }
}
