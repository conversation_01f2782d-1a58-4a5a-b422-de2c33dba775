package com.bukuwarung.activities.customer.transactiondetail

import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.database.entity.TransactionEntityType
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.session.User

class DeleteCustomerTransactionRunnable(
        val activity: CustomerTransactionDetailActivity,
        val transactionId: String
): Runnable {

    override fun run() {
        val transaction: TransactionEntity = TransactionRepository.getInstance(activity).getTransactionById(transactionId)
        val userId2 = User.getUserId()
        val deviceId2 = User.getDeviceId()
        var actionBy = AnalyticsConst.ACCOUNTING
        val type = transaction.transactionType
        if (type == TransactionEntityType.PAYMENT) {
            actionBy = AnalyticsConst.PAYMENTS
        }
        val prop = PropBuilder()
        prop.put(AnalyticsConst.UPDATE_TYPE, AnalyticsConst.DELETE)
        prop.put(AnalyticsConst.ACTION_BY, actionBy)
        prop.put(AnalyticsConst.TRANSACTION_ID, transactionId)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_TRANSACTION_UPDATE, prop)
        TransactionRepository.getInstance(activity).updateExistingTransaction(userId2, deviceId2,
                transaction.transactionId, transaction.customerId, transaction.amount, transaction.date,
                transaction.description, 1)
    }

}

class DeleteCashCustomerTransactionRunnable(
        val activity: CashTransactionDetailActivity,
        val transactionId: String
): Runnable {

    override fun run() {
        val transaction: TransactionEntity = TransactionRepository.getInstance(activity).getTransactionById(transactionId)
        val userId2 = User.getUserId()
        val deviceId2 = User.getDeviceId()
//        AppAnalytics.trackEvent("customer_transaction_delete")
        TransactionRepository.getInstance(activity).updateExistingTransaction(userId2, deviceId2,
                transaction.transactionId, transaction.customerId, transaction.amount, transaction.date,
                transaction.description, 1)
    }

}

class DeleteCustomerTransactionDetailRunnable(
        val activity: TransactionDetailExpandedActivity,
        val transactionId: String
): Runnable {

    override fun run() {
        val transaction: TransactionEntity = TransactionRepository.getInstance(activity).getTransactionById(transactionId)
        val userId2 = User.getUserId()
        val deviceId2 = User.getDeviceId()
        AppAnalytics.trackEvent("customer_transaction_delete")
        TransactionRepository.getInstance(activity).updateExistingTransaction(userId2, deviceId2,
                transaction.transactionId, transaction.customerId, transaction.amount, transaction.date,
                transaction.description, 1)
    }

}

