package com.bukuwarung.activities.customer.adapter;

import android.content.Context;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.customer.CustomerTab;
import com.bukuwarung.activities.customer.adapter.dataholder.CustomerDataHolder;
import com.bukuwarung.activities.customer.adapter.viewholder.CustomerViewHolder;
import com.bukuwarung.activities.customer.adapter.viewholder.TutorialVideoViewHolder;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.utils.ProfileIconHelper;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.ShareUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public final class CustomerAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context context;
    private final CustomerTab fragment;
    private OnClickListener mOnClickListener = new CustomerItemClickHandler(this);
    public final RecyclerView customerRecyclerView;
    OnItemClickListener onItemClickListener;

    public List<? extends DataHolder> customerDataHolderList;

    public final Context getContext() {
        return this.context;
    }

    public CustomerAdapter(List<? extends DataHolder> customerList, RecyclerView recyclerView, Context context,CustomerTab fragment, OnItemClickListener onItemClickListener) {
        this.customerDataHolderList = customerList;
        this.customerRecyclerView = recyclerView;
        this.context = context;
        this.fragment = fragment;
        this.onItemClickListener = onItemClickListener;
    }

    public final class LastViewHolder extends RecyclerView.ViewHolder {

        public LastViewHolder(final View view) {
            super(view);
        }
    }

    public interface OnItemClickListener {
        void onItemClicked(CustomerEntity entity);
    }

    private final void bindCustomerViewHolder(CustomerViewHolder customerViewHolder, CustomerDataHolder customerDataHolder, int i) {
        CustomerEntity customerEntity = customerDataHolder.getCustomerEntity();
        AppCompatImageView contactPic = customerViewHolder.getPhoto();
        TextView nameInitials = customerViewHolder.getNameInitials();

        TextView dueDate = customerViewHolder.getDueDate();
        String dueDateVal = customerDataHolder.getCustomerEntity().dueDate;

        dueDate.setText(context.getResources().getString(R.string.jatuh_tempo, dueDateVal));

        boolean isUtangDueDateVisible = RemoteConfigUtils.INSTANCE.isUtangDueDateFeatureEnabled();

        if (TextUtils.isEmpty(dueDateVal) || !isUtangDueDateVisible) {
            dueDate.setVisibility(View.GONE);
        } else {
            dueDate.setVisibility(View.VISIBLE);
            DateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            DateFormat outputFormat = new SimpleDateFormat("dd MMM yyyy");

            try {
                Date date = inputFormat.parse(dueDateVal);
                dueDateVal = outputFormat.format(date);
                if (date.after(new Date())) {
                    dueDate.setTextColor(ContextCompat.getColor(context, R.color.black_40));
                } else {
                    dueDate.setTextColor(ContextCompat.getColor(context, R.color.red_60));
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }

            dueDate.setText(context.getResources().getString(R.string.jatuh_tempo, dueDateVal));
        }


        ProfileIconHelper.setProfilePic(context, contactPic, nameInitials, customerEntity.name, customerEntity.image);
        customerViewHolder.getName().setText(customerDataHolder.getCustomerEntity().name);

        PackageManager packageManager = context.getPackageManager();
        boolean isInstalled = ShareUtils.isPackageInstalled("com.whatsapp", packageManager);

        TextView cstBalanceTv = customerViewHolder.getBalance();
        StringBuilder sb = new StringBuilder();
        sb.append(Utility.getCurrency());
        sb.append(" ");
        Double balance = customerDataHolder.getCustomerEntity().balance;
        sb.append(Utility.formatCurrency(Math.abs(balance)));
        cstBalanceTv.setText(sb.toString());


        String balanceDesc = TransactionUtil.getTransactionTypeString(this.context, customerDataHolder.getCustomerEntity().balance);
        if (Utility.isBlank(balanceDesc)) {
            customerViewHolder.getBalanceState().setVisibility(View.GONE);
        } else {
            customerViewHolder.getBalanceState().setVisibility(View.VISIBLE);
            customerViewHolder.getBalanceState().setText(balanceDesc);
        }
        if (Double.compare(customerDataHolder.getCustomerEntity().balance, AppConst.ZERO) >= 0) {
            customerViewHolder.getBalance().setTextColor(this.context.getResources().getColor(R.color.in_green));
        } else if (Double.compare(customerDataHolder.getCustomerEntity().balance, AppConst.ZERO) < 0) {
            customerViewHolder.getBalance().setTextColor(this.context.getResources().getColor(R.color.out_red));
        }

        String cstAddress = customerDataHolder.getCustomerEntity().address;
        if (Utility.isBlank(cstAddress)) {
            customerViewHolder.getAddress().setVisibility(View.GONE);
        } else {
            customerViewHolder.getAddress().setVisibility(View.VISIBLE);
            customerViewHolder.getAddress().setText(customerDataHolder.getCustomerEntity().address);
        }
    }

    public final void setDataHolderList(List<? extends DataHolder> dataHolderList) {
        if (dataHolderList == null) {
            this.customerDataHolderList = new ArrayList();
        } else {
            this.customerDataHolderList = dataHolderList;
        }
        notifyDataSetChanged();
    }

    public int getItemViewType(int i) {
        return ((DataHolder) this.customerDataHolderList.get(i)).getTag();
    }

    void sendMessageToWhatsapp(CustomerEntity entity) {

        if (onItemClickListener != null) {
            onItemClickListener.onItemClicked(entity);
        }
    }

    public long getItemId(int i) {
        DataHolder dataHolder = (DataHolder) this.customerDataHolderList.get(i);
        if (dataHolder instanceof CustomerDataHolder) {
            return (dataHolder.getTag()+":"+((CustomerDataHolder) dataHolder).getCustomerEntity().customerId).hashCode();
        } else {
            return (dataHolder.getTag()+":"+i).hashCode();
        }
    }

    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int tag) {
        if (tag == Tag.CUSTOMER_TAB_CUSTOMER_VIEW) {
            View customerView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.customer_view_item, viewGroup, false);
            customerView.setOnClickListener(this.mOnClickListener);
            return new CustomerViewHolder(this, customerView);
        } else if (tag == Tag.CUSTOMER_TAB_TUTORIAL_VIEW) {
            View tutorialView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.customer_tab_tutorial_item, viewGroup, false);
            return new TutorialVideoViewHolder(this, tutorialView,fragment);
        }else if (tag == Tag.LAST_ROW) {
            View inflate = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.activity_main_view_customer_last, viewGroup, false);
            return new LastViewHolder(inflate);
        }else if (tag == Tag.VIEW_NO_RESULT) {
            View noresult = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.no_search_result_view, viewGroup, false);
            TextView tvHead = noresult.findViewById(R.id.no_trans_header);
            tvHead.setText(R.string.no_result_utang);
            return new NoResultViewHolder(noresult);
        } else {
            View tutorialView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.customer_tab_tutorial_item, viewGroup, false);
            return new TutorialVideoViewHolder(this, tutorialView,fragment);
        }
    }

    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        DataHolder dataHolder = this.customerDataHolderList.get(i);
        int itemViewType = viewHolder.getItemViewType();
        if (itemViewType == Tag.CUSTOMER_TAB_CUSTOMER_VIEW) {
            CustomerViewHolder customerViewHolder = (CustomerViewHolder) viewHolder;
            if (dataHolder != null) {
                bindCustomerViewHolder(customerViewHolder, (CustomerDataHolder) dataHolder, i);
                return;
            }
        }
    }

    public int getItemCount() {
        if (this.customerDataHolderList == null) {
            return 0;
        }
        return this.customerDataHolderList.size();
    }

    public final class NoResultViewHolder extends RecyclerView.ViewHolder {

        public NoResultViewHolder(final View view) {
            super(view);
        }
    }
}
