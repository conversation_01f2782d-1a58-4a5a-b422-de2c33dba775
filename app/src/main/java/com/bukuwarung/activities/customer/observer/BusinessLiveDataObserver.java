package com.bukuwarung.activities.customer.observer;

import android.content.Context;
import android.view.View;

import androidx.lifecycle.Observer;

import com.bukuwarung.activities.customer.CustomerTab;
import com.bukuwarung.database.entity.BookEntity;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;

public final class BusinessLiveDataObserver<T> implements Observer<BookEntity> {

    final ExtendedFloatingActionButton addNewCustomerBtn;
    final CustomerTab customerTab;

    public BusinessLiveDataObserver(CustomerTab customerTab, ExtendedFloatingActionButton addNewCustomerBtn) {
        this.customerTab = customerTab;
        this.addNewCustomerBtn = addNewCustomerBtn;
    }

    public final void onChanged(BookEntity bookEntity) {
        this.customerTab.bookEntity = bookEntity;
        Context context = this.customerTab.getContext();
        customerTab.refreshToolBar(context);
        //this.addNewCustomerBtn.setVisibility(View.VISIBLE);
    }
}
