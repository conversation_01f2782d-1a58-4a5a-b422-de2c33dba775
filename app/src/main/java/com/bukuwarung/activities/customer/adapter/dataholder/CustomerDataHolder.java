package com.bukuwarung.activities.customer.adapter.dataholder;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.CustomerEntity;

public class CustomerDataHolder extends DataHolder {

    private final CustomerEntity customerEntity;

    public CustomerDataHolder(CustomerEntity customerEntity) {
        this.customerEntity = customerEntity;
        setTag(Tag.CUSTOMER_TAB_CUSTOMER_VIEW);
    }

    public final CustomerEntity getCustomerEntity() {
        return this.customerEntity;
    }

    public String getName() {
        return this.customerEntity.name;
    }
}