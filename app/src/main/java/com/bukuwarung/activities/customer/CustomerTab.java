package com.bukuwarung.activities.customer;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.bukuwarung.constants.AnalyticsConst.DEFAULT_FILTER;
import static com.bukuwarung.constants.AnalyticsConst.SMS_CHECKBOX_ENABLED;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bukuwarung.R;
import com.bukuwarung.activities.WebviewActivity;
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity;
import com.bukuwarung.activities.addcustomer.detail.CustomerActivity;
import com.bukuwarung.activities.appinfo.PrivacyActivity;
import com.bukuwarung.activities.customer.adapter.CustomerAdapter;
import com.bukuwarung.activities.customer.download.DownloadPdfClickHandler;
import com.bukuwarung.activities.customer.filter.CustomerListFilter;
import com.bukuwarung.activities.customer.observer.BusinessLiveDataObserver;
import com.bukuwarung.activities.customer.sort.CustomerSortMenuClickHandler;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.onboarding.LoginActivity;
import com.bukuwarung.activities.onboarding.NewLoginActivity;
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet;
import com.bukuwarung.activities.superclasses.AppFragment;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transactionreport.DateFilter;
import com.bukuwarung.activities.transactionreport.TransactionReportActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.collectingcalendar.addcollectingdate.AddCollectingDateActivity;
import com.bukuwarung.collectingcalendar.main.CollectingCalendarActivity;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.CustomerSort;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.constants.RemoteConfigConst;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.datasync.restore.ManualDataRestoreDialog;
import com.bukuwarung.enums.GamifyTarget;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.OnboardingPrefManager;
import com.bukuwarung.session.AuthHelper;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.shape.FocusGravity;
import com.bukuwarung.tutor.shape.Location;
import com.bukuwarung.tutor.shape.ShapeType;
import com.bukuwarung.tutor.view.OnboardingWidget;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.CoroutineHelper;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utilities;
import com.bukuwarung.utils.Utility;
import com.bukuwarung.utils.UtilsKt;
import com.bukuwarung.utils.WhatsAppUtils;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public final class CustomerTab extends AppFragment implements OnClickListener, OnboardingWidget.OnboardingWidgetListener, CustomerAdapter.OnItemClickListener {

    public interface CustomerTabListener {
        void handleNormalBackPressedFromCustomer();
    }

    private CustomerTabListener listener;
    private CustomerAdapter customerAdapter;

    private Toolbar toolbar;
    private TextView toolbarTitle;

    private int customerFilter;

    public BookEntity bookEntity;
    private ImageView clear;
    private boolean isSearchOpen;
    private MaterialCardView summaryCard;

    private TextView customersCountTV;
    private int customerCount;
    private View downloadPdf;
    private AppBarLayout appBarLayout;

    private LinearLayout searchFilterLayout;
    private EditText searchBox;

    private RelativeLayout searchBoxLayout;
    private int sortOrder;
    private View sortMenu;
    private TextView creditTotal;
    private TextView debitTotal;

    private CountDownTimer timer;
    private LinearLayout collectingCalendarIcon;
    private TextView helpIcon;
    private TextView collectingCalendarCount;
    private View arrow;
    LinearLayout arrowArea;


    private SwipeRefreshLayout customerListPTR;
    private RecyclerView customerRecyclerView;
    public CustomerListViewModel customerListViewModel;

    private LinearLayout bottomTabs;

    public ExtendedFloatingActionButton addCustomerBtn;

    public ImageView dataRefreshBtn;

    // new design
    private HorizontalScrollView hsvFilter;
    private ChipGroup cgFilter;
    private Chip chipAll, chipDebit, chipCredit, chipNil, chipAllDue, chipAllOverdue;

    private TextView tvOfflineText;
    private boolean coachmarkShown = false;
    private boolean reportCoachmarkShown = false;
    private boolean searchCoachmarkShown = false;
    private boolean dateCoachMarkShown = false;
    private boolean isAnyTutorialShowing = false;
    private View btnSummary;
    private OnboardingWidget onboardingWidget;
    private long dateFilterVisibilityLimit = 0;
    private long searchVisibilityLimit = 0;
    private View notificationIcon;
    private View notificationHighlighter;
    private boolean firstLoad = false;
    private TextView expense;
    private TextView income;
    private Boolean showOldForm;
    private Boolean mergedTab;
    private boolean afterRestoreState = false;
    private Boolean showHelpIcon = false;
    private String redirectHelpTo = "";
    private boolean isExitDialogEnabled = false;
    private boolean showOldUtang;

    private boolean isFavoriteCustomerPinEnabled = false;
    public CoroutineHelper coroutineHandler = new CoroutineHelper();

    public CustomerTab() {
        this.sortOrder = featureManager.getCustomerListSortOrder();
        this.customerFilter = featureManager.getSelectedFilter();
    }

    public static CustomerListViewModel getCustomerListViewModel(CustomerTab customerTab) {
        return customerTab.customerListViewModel;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        isFavoriteCustomerPinEnabled = RemoteConfigUtils.FavoriteCustomer.INSTANCE.isEnabled();
        boolean isUtangDueDateVisible = RemoteConfigUtils.INSTANCE.isUtangDueDateFeatureEnabled();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof CustomerTabListener) {
            listener = (CustomerTabListener) context;
        }
    }

    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        return layoutInflater.inflate(R.layout.tab_layout_customers, viewGroup, false);
    }

    public void onActivityCreated(Bundle bundle) {
        super.onActivityCreated(bundle);
        FragmentActivity activity = getActivity();
        showOldForm = RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm();
        showOldUtang = RemoteConfigUtils.INSTANCE.shouldShowOldUtangForm();
        mergedTab = RemoteConfigUtils.TransactionTabConfig.INSTANCE.mergedTab();
        showHelpIcon = RemoteConfigUtils.INSTANCE.shouldShowHelpIcon();
        redirectHelpTo = RemoteConfigUtils.INSTANCE.redirectHelpIcon();

        if (activity != null) {
            View view = getView();
            expense = view.findViewById(R.id.text_expense);
            income = view.findViewById(R.id.text_income);
            if(getParentFragment() != null) {
                View parentView = getParentFragment().getView();
                toolbar = parentView.findViewById(R.id.toolbar);
                view.findViewById(R.id.toolbar).setVisibility(GONE);
            } else {
                toolbar = view.findViewById(R.id.toolbar);
            }
            collectingCalendarIcon = toolbar.findViewById(R.id.collecting_calendar_icon);
            collectingCalendarCount = toolbar.findViewById(R.id.collecting_calendar_count);
            helpIcon = toolbar.findViewById(R.id.tv_help_icon);
            notificationIcon = toolbar.findViewById(R.id.notification_icon);
            notificationHighlighter = toolbar.findViewById(R.id.notify_highlighter_exp);

            if(mergedTab) {
                arrow = getView().findViewById(R.id.tutorarrow);
                arrowArea = getView().findViewById(R.id.arrowArea);
                addCustomerBtn = view.findViewById(R.id.addCustomerBtn);
            } else {
                arrow = getView().findViewById(R.id.tutorarrowForNoTabMerge);
                arrowArea = getView().findViewById(R.id.arrowAreaForNoTabMerge);
                addCustomerBtn = view.findViewById(R.id.addCustomerNewFormBtn);
            }
            initToolBar();
            tvOfflineText = getView().findViewById(R.id.tvOffline);
            appBarLayout = getView().findViewById(R.id.app_bar);

            creditTotal = view.findViewById(R.id.creditTotal);
            debitTotal = view.findViewById(R.id.debitTotal);
            summaryCard = view.findViewById(R.id.summaryView);

            bottomTabs = view.findViewById(R.id.type_rg);

            dateFilterVisibilityLimit = RemoteConfigUtils.INSTANCE.getValueOfN();
            searchVisibilityLimit = RemoteConfigUtils.INSTANCE.getValueOfM();

            collectingCalendarIcon.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {

                    try {
                        String countStr = collectingCalendarCount.getText().toString();
                        int countInt = Integer.parseInt(countStr);
                        Intent intent;
                        if (countInt > 0) {
                            intent = AddCollectingDateActivity.Companion.getNewIntent(getActivity());
                        } else {
                            intent = CollectingCalendarActivity.Companion.getNewIntent(getActivity());
                        }
                        startActivity(intent);
                    } catch (Exception ex) {
                        final Intent intent = CollectingCalendarActivity.Companion.getNewIntent(getActivity());
                        startActivity(intent);
                    }
                }
            });
            isExitDialogEnabled = RemoteConfigUtils.INSTANCE.shouldShowExitDialog();
            dataRefreshBtn = view.findViewById(R.id.btn_image_refresh);
            initSearchSortBar(view);
            initCountDownload(view);
            initNewCustomerOp(view);
            initFilterTabRecyclerView();
            initCustomerRecyclerView();
            btnSummary = view.findViewById(R.id.summary_btn);
            btnSummary.setOnClickListener(view1 -> {
                if(Utility.hasInternet()){
                    goToTransactionReport();
                } else {
                    new CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                            requireContext(),
                            requireActivity().getSupportFragmentManager()
                    );
                }
            });
            income.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (!showOldUtang) {
                        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                        propBuilder.put(AnalyticsConst.TRX_TYPE, AnalyticsConst.CREDIT);
                        propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled);
                        if (showOldUtang) {
                            propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);
                        } else {
                            propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.UPDATED_JUNE);
                        }
                        propBuilder.put(AnalyticsConst.CUST_FAV_PIN_ENABLED, isFavoriteCustomerPinEnabled);
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_ADD_CUSTOMER_BTN, propBuilder);
                    }
                    Intent intent = new Intent(requireActivity(), CustomerActivity.class);
                    intent.putExtra(CustomerActivity.IS_EDIT_TRANSACTION, false);
                    intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, AppConst.CREDIT);
                    startActivity(intent);
                }
            });

            expense.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                    propBuilder.put(AnalyticsConst.TRX_TYPE, AnalyticsConst.DEBIT);
                    propBuilder.put(AnalyticsConst.CUST_FAV_PIN_ENABLED, isFavoriteCustomerPinEnabled);
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_ADD_CUSTOMER_BTN, propBuilder);
                    propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled);
                    if (showOldUtang) {
                        propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);
                    } else {
                        propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.UPDATED_JUNE);
                    }
                    Intent intent = new Intent(requireActivity(), CustomerActivity.class);
                    intent.putExtra(CustomerActivity.IS_EDIT_TRANSACTION, false);
                    intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, AppConst.DEBIT);
                    startActivity(intent);
                }
            });
        }

        addCustomerBtn.setVisibility(VISIBLE);
    }

    private void goToTransactionReport() {
        Intent intent = new Intent(getActivity(), TransactionReportActivity.class);
        intent.putExtra("targetId", "0");
        startActivity(intent);
        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
        prop.put("source", "utang");
        prop.put(DEFAULT_FILTER, DateFilter.Companion.getFilterString(requireContext(),RemoteConfigUtils.PdfDefaultFilterExperiment.INSTANCE.getVariant()));
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TRANSACTION_REPORT, prop);
    }

    private void initFilterTabRecyclerView() {
        hsvFilter = getView().findViewById(R.id.hsvChip);
        cgFilter = getView().findViewById(R.id.cgUtangFilter);
        chipAll = getView().findViewById(R.id.chipAll);
        chipCredit = getView().findViewById(R.id.chipCredit);
        chipDebit = getView().findViewById(R.id.chipDebit);
        chipNil = getView().findViewById(R.id.chipNil);
        chipAllDue = getView().findViewById(R.id.chipAllDue);
        chipAllOverdue = getView().findViewById(R.id.chipAllOverDue);

        boolean isUtangDueDateVisible = RemoteConfigUtils.INSTANCE.isUtangDueDateFeatureEnabled();

        if (isUtangDueDateVisible) {
            chipAllOverdue.setVisibility(VISIBLE);
            chipAllDue.setVisibility(VISIBLE);
        } else {
            chipAllOverdue.setVisibility(GONE);
            chipAllDue.setVisibility(GONE);
        }

        cgFilter.setOnCheckedChangeListener((group, checkedId) -> {
            Chip checkedChip = getView().findViewById(checkedId);
            autoScroll(checkedChip);
        });

        chipAll.setOnCheckedChangeListener((buttonView, isChecked) -> {
            chipAll.setTypeface(isChecked ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipAll.setTextColor(ContextCompat.getColor(requireContext(), isChecked ? R.color.white : R.color.black_60));

            if (isChecked) {
                updateSelectedFilter(CustomerListFilter.ALL);
            }
        });
        chipDebit.setOnCheckedChangeListener((buttonView, isChecked) -> {
            chipDebit.setTypeface(isChecked ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipDebit.setTextColor(ContextCompat.getColor(requireContext(), isChecked ? R.color.white : R.color.black_60));
            if (isChecked) {
                updateSelectedFilter(CustomerListFilter.DEBIT);
            }
        });
        chipCredit.setOnCheckedChangeListener((buttonView, isChecked) -> {
            chipCredit.setTypeface(isChecked ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipCredit.setTextColor(ContextCompat.getColor(requireContext(), isChecked ? R.color.white : R.color.black_60));
            if (isChecked) {
                updateSelectedFilter(CustomerListFilter.CREDIT);
            }
        });
        chipNil.setOnCheckedChangeListener((buttonView, isChecked) -> {
            chipNil.setTypeface(isChecked ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipNil.setTextColor(ContextCompat.getColor(requireContext(), isChecked ? R.color.white : R.color.black_60));
            if (isChecked) {
                updateSelectedFilter(CustomerListFilter.NIL);
            }
        });

        chipAllDue.setOnCheckedChangeListener((buttonView, isChecked) -> {
            chipAllDue.setTypeface(isChecked ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipAllDue.setTextColor(ContextCompat.getColor(requireContext(), isChecked ? R.color.white : R.color.black_60));
            if (isChecked) {
                updateSelectedFilter(CustomerListFilter.ALLDUE);
            }
        });

        chipAllOverdue.setOnCheckedChangeListener((buttonView, isChecked) -> {
            chipAllOverdue.setTypeface(isChecked ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipAllOverdue.setTextColor(ContextCompat.getColor(requireContext(), isChecked ? R.color.white : R.color.black_60));
            if (isChecked) {
                updateSelectedFilter(CustomerListFilter.OVERDUE);
            }
        });

        // a little hack
        chipAll.setChecked(true);
        chipAll.setTextColor(ContextCompat.getColor(requireContext(), chipAll.isChecked() ? R.color.white : R.color.black_60));
        chipAll.setTypeface(chipAll.isChecked() ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
    }

    private void initCustomerRecyclerView() {

        this.customerListPTR = getView().findViewById(R.id.ptrCustomer);
        this.customerRecyclerView = getView().findViewById(R.id.customerRecyclerView);
        this.customerListViewModel = ViewModelProviders.of(this).get(CustomerListViewModel.class);
        List arrayList = new ArrayList();
        this.customerAdapter = new CustomerAdapter(arrayList, this.customerRecyclerView, getContext(), this, this);
        this.customerAdapter.setHasStableIds(true);
        this.customerRecyclerView.setHasFixedSize(true);
        this.customerRecyclerView.setAdapter(this.customerAdapter);
        ViewCompat.setNestedScrollingEnabled(customerRecyclerView, true);

        this.customerRecyclerView.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.VERTICAL,
                false));
        LifecycleOwner lifecycleOwner = this;
        this.customerListViewModel.getDataHolderList().observe(lifecycleOwner, new Observer<List<? extends DataHolder>>() {
            @Override
            public void onChanged(List<? extends DataHolder> list) {
                customerAdapter.setDataHolderList(list);
                Collection customerList = customerListViewModel.getCustomerList();
                if (customerList == null || customerList.isEmpty()) {
                    collectingCalendarIcon.setVisibility(GONE);
                    if(showHelpIcon) {
                        helpIcon.setVisibility(VISIBLE);
                    }
                } else {
                    collectingCalendarIcon.setVisibility(VISIBLE);
                    helpIcon.setVisibility(GONE);
                    if (customerList.size() > FeaturePrefManager.getInstance().getTooltipTarget()) {
                        FeaturePrefManager.getInstance().disableTooltips();
                    }
                    try {
                        List<CustomerEntity> notSetDueDateCustomers = new ArrayList<>();
                        for (Object o : customerList) {
                            CustomerEntity customerEntity = (CustomerEntity) o;
                            if (customerEntity.balance < 0
                                    && Utility.isBlank(customerEntity.dueDate)) {
                                notSetDueDateCustomers.add(customerEntity);
                            }
                        }

                        if (notSetDueDateCustomers.isEmpty()) {
                            // set grey background
                            collectingCalendarCount.setBackgroundResource(R.drawable.bg_collection_grey);
                            collectingCalendarCount.setText("" + notSetDueDateCustomers.size());
                        } else {
                            // set red background
                            collectingCalendarCount.setBackgroundResource(R.drawable.bg_collection_red);

                            int total = notSetDueDateCustomers.size() > 99 ? 99 : notSetDueDateCustomers.size();
                            collectingCalendarCount.setText("" + total);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
                handleCountChange(list);
            }
        });
        this.customerListViewModel.getBusinessLiveData().observe(lifecycleOwner, new BusinessLiveDataObserver(this, addCustomerBtn));

        this.customerListViewModel.getUtangCountLiveData().observe(lifecycleOwner, utangcount -> {
            int count = 0;
            if (utangcount != null) count = utangcount;

            if (count >= 1 && customerCount>0) {
                boolean collectingCalendarActive = AppConfigManager.getInstance().useCollectionCalendar();
                if (collectingCalendarActive) {
                    collectingCalendarIcon.setVisibility(VISIBLE);
                    helpIcon.setVisibility(GONE);
                }
            } else {
                collectingCalendarIcon.setVisibility(GONE);
                if (showHelpIcon) {
                    helpIcon.setVisibility(VISIBLE);
                }
            }
        });

        customerListPTR.setColorSchemeResources(R.color.colorPrimary);
        customerListPTR.setOnRefreshListener(() -> {
            refreshCustomerData();
        });

    }

    private void refreshCustomerData() {
        // TODO: REPLACE WITH ACTUAL FUNCTION TO REFRESH DATA
        try {
            //new AsyncCustomerDataSync().execute();
            coroutineHandler.syncCustomerData();
            new Handler().postDelayed(() -> {
                customerListPTR.setRefreshing(false);
            }, 2000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showCustomerTutorialView() {
        try {


            if (coachmarkShown || getActivity() == null || getActivity().getApplicationContext() == null ||
                    OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TOUR_CUSTOMER_TAB_ADD_BTN) ||
                    TransactionRepository.getInstance(getActivity().getApplicationContext()).getUtangTransactionCountWithDeletedRecords() > 0 ||
                    getView() == null) return;

            new Handler().postDelayed(() -> {
                if (getActivity() == null || getActivity().isFinishing()) return;
                showOnboardingTutorial();

            }, 300);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        afterRestoreState = savedInstanceState != null;
    }

    private void showTooltip() {
        if (isAnyTutorialShowing || getActivity() == null || getActivity().getApplicationContext() == null || !CustomerTab.this.isResumed())
            return;

        int count = TransactionRepository.getInstance(getActivity().getApplicationContext()).getUniqueUtangTransactionCountWithDeletedRecords();

        if (dateFilterVisibilityLimit == count) {
            if (!searchCoachmarkShown && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_SEARCH_DEBT)) {
                searchCoachmarkShown = true;
                isAnyTutorialShowing = true;
                onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), CustomerTab.this,
                        OnboardingPrefManager.TUTORIAL_SEARCH_DEBT, searchFilterLayout, R.drawable.onboarding_announce, getString(R.string.new_feature),
                        getString(R.string.onboarding_search_debt), getString(R.string.understand), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                        1, 1, true, false);
            }
        }


        if (searchVisibilityLimit == count) {
            if (!dateCoachMarkShown && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_DATE_FILTER_CUSTOMER)) {
                dateCoachMarkShown = true;
                isAnyTutorialShowing = true;
                Handler handler = new Handler();
                final Runnable r = () -> {
                    if (getActivity() != null && !getActivity().isFinishing())
                        onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), CustomerTab.this,
                                OnboardingPrefManager.TUTORIAL_DATE_FILTER_CUSTOMER, hsvFilter, R.drawable.onboarding_announce, getString(R.string.new_feature),
                                getString(R.string.onboarding_date_customer_tab_filter), getString(R.string.understand), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                                1, 1, true, false, 0, false, false, null,
                                Location.TOP, false);
                };
                handler.postDelayed(r, 200);
            }
        }

        if (count == 3) {
            if (!reportCoachmarkShown && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_DOWNLOAD_REPORT_DEBT)) {
                reportCoachmarkShown = true;
                isAnyTutorialShowing = true;
                Handler handler = new Handler();
                final Runnable r = () -> {
                    if (getActivity() != null && !getActivity().isFinishing())
                        onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), CustomerTab.this,
                                OnboardingPrefManager.TUTORIAL_DOWNLOAD_REPORT_DEBT, btnSummary, R.drawable.onboarding_announce, getString(R.string.new_feature),
                                getString(R.string.onboarding_download_report), getString(R.string.try_feature), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                                1, 1, true, false, 0, false, false, null, Location.TOP, false);
                };
                handler.postDelayed(r, 200);
            }
        }
    }

    private void initNewCustomerOp(View view) {

        dataRefreshBtn.setOnClickListener(v -> {
            ManualDataRestoreDialog manualDataRestoreDialog = new ManualDataRestoreDialog(getActivity(), getActivity(),1);
            manualDataRestoreDialog.show();
        });

        addCustomerBtn.setOnClickListener(v -> {
                    if (!AuthHelper.isValidSessionOperation()) {
                        ((MainActivity) getActivity()).callLoginBottomsheet(false, AnalyticsConst.UTANG);
                        return;
                    }
                    AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                    propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled);
                    if (showOldUtang) {
                        propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);
                    } else {
                        propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.UPDATED_JUNE);
                    }
                    propBuilder.put(AnalyticsConst.CUST_FAV_PIN_ENABLED, isFavoriteCustomerPinEnabled);
                    propBuilder.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsUtang());
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_ADD_CUSTOMER_BTN, propBuilder);
                    Intent intent;
                    if (showOldForm) {
                        intent = new Intent(getContext(), AddCustomerActivity.class);
                    } else {
                        intent = new Intent(requireActivity(), CustomerActivity.class);
                        intent.putExtra(CustomerActivity.IS_EDIT_TRANSACTION, false);
                        intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, AppConst.DEBIT);
                    }
                    startActivity(intent);
                }
        );
    }

    private void initCountDownload(View view) {
        this.downloadPdf = view.findViewById(R.id.downloadCstPdf);
        this.customersCountTV = view.findViewById(R.id.customersCountTv);
        this.customersCountTV.setOnClickListener(this);
        this.downloadPdf.setOnClickListener(new DownloadPdfClickHandler(this));
    }

    private void initSearchSortBar(View view) {
        this.searchBox = view.findViewById(R.id.searchQueryBox);
        this.clear = view.findViewById(R.id.clear);
        this.sortMenu = view.findViewById(R.id.sortMenu);
        this.searchBoxLayout = view.findViewById(R.id.searchLayout);
        this.searchFilterLayout = view.findViewById(R.id.searchFilterLayout);
        this.clear.setOnClickListener(this);
        this.sortMenu.setOnClickListener(this);
        this.searchBox.addTextChangedListener(new SearchBoxTextWatcher(this));
    }

    public void onDestroy() {
        super.onDestroy();
        CountDownTimer countDownTimer = this.timer;
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        this.timer = null;
        coroutineHandler.cancel();
    }

    public void onResume() {
        super.onResume();
        initToolBar();
        if (Utility.hasInternet()) {
            tvOfflineText.setVisibility(GONE);
        } else {
            tvOfflineText.setVisibility(VISIBLE);
        }
        Integer valueOf = Integer.valueOf(this.customerFilter);
        if (!valueOf.equals(Integer.valueOf(featureManager.getSelectedFilter()))) {
            updateSelectedFilter(featureManager.getSelectedFilter());
            this.customerFilter = featureManager.getSelectedFilter();
        }

        try {
            refreshToolBar(getContext());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        showGamifyPopup();
        if (!afterRestoreState) {
            showCustomerTutorialView();
        } else afterRestoreState = false;
        showTooltip();
    }

    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.clear:
                if (getView() != null)
                    InputUtils.hideKeyboardFrom(getActivity(), getView().findFocus());
                isSearchOpen = false;
                this.searchBoxLayout.setVisibility(View.GONE);
                searchFilterLayout.setVisibility(VISIBLE);
               //if(showOldForm) {
                   this.addCustomerBtn.setVisibility(VISIBLE);
              // }
                this.searchBox.setText("");
                this.searchBox.clearFocus();
                this.hsvFilter.setVisibility(VISIBLE);
                appBarLayout.setExpanded(true, true);
//                AppAnalytics.trackEvent("customer_tap_clear_search");
                break;
            case R.id.customersCountTv:
                startSearch();
                break;
            case R.id.sortMenu:
                showSortOrderDialog();
                break;
        }
    }

    private void startSearch() {
        isSearchOpen = true;
        this.searchBoxLayout.setVisibility(VISIBLE);
        this.searchFilterLayout.setVisibility(View.GONE);
        this.addCustomerBtn.setVisibility(View.GONE);
        this.arrowArea.setVisibility(View.GONE);
        this.hsvFilter.setVisibility(GONE);
        this.searchBox.requestFocus();
        appBarLayout.setExpanded(false, true);
        InputUtils.showKeyboard(getActivity());
        AppAnalytics.trackEvent("customer_tap_search", "", "");
    }

    public void onCreateOptionsMenu(Menu menu, MenuInflater menuInflater) {
        menuInflater.inflate(R.menu.main_menu, menu);
        super.onCreateOptionsMenu(menu, menuInflater);
    }

    public boolean onOptionsItemSelected(MenuItem menuItem) {

        switch (menuItem.getItemId()) {
            case R.id.action_logout:
                sessionManager.logOutInstance();
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_LOGOUT_CLICKED);
                Utilities.INSTANCE.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_LOGOUT_CLICKED,
                        "logout_clicked", SessionManager.getInstance().getBukuwarungToken(),
                        SessionManager.getInstance().getUUID());
                sessionManager.setBukuwarungToken(null);
                sessionManager.setUserLoggedInWithPin(false);
                boolean shouldShowNewLoginScreen = RemoteConfigUtils.INSTANCE.shouldShowNewLoginScreen();
                Intent intent = new Intent(getContext(), shouldShowNewLoginScreen ? NewLoginActivity.class : LoginActivity.class);
                startActivity(intent);
                break;
            case R.id.action_share_app:
                FeatureHelper.shareApp(getActivity());
                break;
            case R.id.action_privacy:
                AppAnalytics.trackEvent("open_privacy_screen");
                startActivity(new Intent(getContext(), PrivacyActivity.class));
                break;

            case R.id.action_settings:
                AppAnalytics.trackEvent("open_settings_screen");
                break;
            case R.id.action_support:
                handleWhatsAppUs();
                break;

        }
        return super.onOptionsItemSelected(menuItem);
    }

    private void handleWhatsAppUs() {
        try {
            AppAnalytics.trackEvent("mh_support_requested");
            Intent sendIntent = new Intent("android.intent.action.MAIN");
            sendIntent.setComponent(new ComponentName("com.whatsapp", "com.whatsapp.Conversation"));
            sendIntent.setAction(Intent.ACTION_SEND);
            sendIntent.setType("text/plain");
            sendIntent.putExtra(Intent.EXTRA_TEXT, "");
            sendIntent.putExtra("jid", AppConfigManager.getInstance().getWhatsappId() + "@s.whatsapp.net");
            sendIntent.setPackage("com.whatsapp");
            startActivity(sendIntent);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public final void initToolBar() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            MainActivity mainActivity = (MainActivity) activity;
            mainActivity.setSupportActionBar(this.toolbar);
            ActionBar supportActionBar = mainActivity.getSupportActionBar();
            supportActionBar.setDisplayShowTitleEnabled(false);
            supportActionBar.setDisplayHomeAsUpEnabled(true);
            supportActionBar.setHomeButtonEnabled(true);

            if (FeaturePrefManager.getInstance().isDailyHighlightExplored()) {
                supportActionBar.setHomeAsUpIndicator((int) R.mipmap.ic_menu_white_24dp);
            } else {
                supportActionBar.setHomeAsUpIndicator(R.drawable.notification_hamburger);
            }
            this.toolbarTitle = this.toolbar.findViewById(R.id.title);
            this.toolbar.setNavigationOnClickListener(v -> {
                FragmentActivity activity1 = getActivity();
                if (activity1 != null) {
                    ((MainActivity) activity1).handleSideMenuIconClick();
                }
            });

            helpIcon.setOnClickListener(view -> {
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.UTANG_TAB);
                propBuilder.put(AnalyticsConst.HELP_ICON_REDIRECTED_TO, redirectHelpTo);
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_SUPPORT_REQUESTED, propBuilder);
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CUSTOMER_SUPPORT_REQUESTED,requireActivity());
                if (redirectHelpTo.equals(RemoteConfigConst.REDIRECT_HELP_ICON_CUSTOMER_SUPPORT)) {
                    WhatsAppUtils.openWABotWithoutProp(getActivity());
                } else if(redirectHelpTo.equals(RemoteConfigConst.REDIRECT_HELP_ICON_TUTORIAL)) {
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN);
                    SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN,requireActivity());
                    startActivity(WebviewActivity.Companion.createIntent(getActivity(), AppConst.TUTORIAL_URL,
                            getString(R.string.learn_bukuwarung)));
                }
            });

            if (!firstLoad) {
                firstLoad = true;
                collectingCalendarIcon.setVisibility(GONE);
                return;
            } else {
                refreshToolBar(getContext());
            }
        }
    }

    public final void refreshToolBar(Context ctx) {
        if(mergedTab) {
            notificationIcon.setVisibility(GONE);
            notificationHighlighter.setVisibility(GONE);
            collectingCalendarIcon.setVisibility(VISIBLE);
        }
        BusinessRepository businessRepository = BusinessRepository.getInstance(ctx);
        this.bookEntity = businessRepository.getBusinessByIdSync(User.getBusinessId());
        if (this.bookEntity != null) {
            String selectedBusiness = this.bookEntity.businessName;
            if (!Utility.isBlank(selectedBusiness)) {
                if (!Utility.hasBusinessName(selectedBusiness)) {
                    this.toolbarTitle.setText(getContext().getString(R.string.mybusiness));
                } else {
                    this.toolbarTitle.setText(selectedBusiness);
                }

            } else {
                this.toolbarTitle.setText(getContext().getString(R.string.mybusiness));
                BookEntity bookEntity = this.bookEntity;
                if (!Utility.isBlank(bookEntity.businessOwnerName)) {
                    businessRepository.updateBusinessProfile(User.getUserId(), User.getDeviceId(), User.getBusinessId(), bookEntity.businessName, bookEntity.bookType, bookEntity.bookTypeName, bookEntity.businessOwnerName);
                    this.toolbarTitle.setText(bookEntity.businessOwnerName);
                }
            }
        }
    }

    private void showSortOrderDialog() {
        Context contextWrapper = new ContextThemeWrapper(getContext(), R.style.PopupMenu);
        final PopupMenu sortDialog = new PopupMenu(contextWrapper, this.sortMenu);
        sortDialog.getMenuInflater().inflate(R.menu.customer_sort_menu, sortDialog.getMenu());
        hightlightSortBeforeDialogOpen(sortDialog);
        sortDialog.setOnMenuItemClickListener(new CustomerSortMenuClickHandler(this, sortDialog));
        sortDialog.show();
    }

    private void hightlightSortBeforeDialogOpen(PopupMenu popupMenu) {
        int i = this.sortOrder;
        String captionStr = "";
        if (i == CustomerSort.NAME_ASC) {
            captionStr = getContext().getString(R.string.activity_main_search_menu_name_asc);
            highlightMenu(popupMenu, captionStr, R.id.nameAsc);
        } else if (i == CustomerSort.NAME_DSC) {
            captionStr = getContext().getString(R.string.activity_main_search_menu_name_dsc);
            highlightMenu(popupMenu, captionStr, R.id.nameDsc);
        } else if (i == CustomerSort.LEAST_AMOUNT) {
            captionStr = getContext().getString(R.string.activity_main_search_menu_least_amount);
            highlightMenu(popupMenu, captionStr, R.id.leastAmount);
        } else if (i == CustomerSort.MOST_AMOUNT) {
            captionStr = getContext().getString(R.string.activity_main_search_menu_most_amount);
            highlightMenu(popupMenu, captionStr, R.id.mostAmount);
        } else if (i == CustomerSort.MOST_RECENT) {
            captionStr = getContext().getString(R.string.activity_main_search_menu_most_recent);
            highlightMenu(popupMenu, captionStr, R.id.mostRecent);
        } else if (i == CustomerSort.DUE_DATE) {
            captionStr = getContext().getString(R.string.collection_calendar);
            highlightMenu(popupMenu, captionStr, R.id.dueDate);
        }
    }

    private void highlightMenu(PopupMenu popupMenu, String str, int i) {
        SpannableString spannableString = new SpannableString(str);
        spannableString
                .setSpan(new ForegroundColorSpan(getColor(R.color.colorPrimaryDark)),
                        0, spannableString.length(), 0);
        popupMenu.getMenu().findItem(i).setTitle(spannableString);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NotNull String[] permissions,
                                           @NotNull int[] grantResults) {
        if (requestCode == PermissionConst.WRITE_EXTERNAL_STORAGE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                AppAnalytics.trackEvent("granted_storage_permission");
                this.downloadPdf.performClick();
            }
        }
    }

    private void updateSelectedFilter(int filterType) {
        customerFilter = filterType;
        customerListViewModel.setBalanceFilter(filterType);
        featureManager.setSelectedFilter(filterType);
    }

    public final void handleCountChange(List<? extends DataHolder> cstList) {
        int count = 0;
        String amountInStr = String.format("%s%s", Utility.getCurrency(), Utility.formatCurrency(Math.abs(customerListViewModel.getBookSummaryModel().amountIn)));
        String amountOutStr = String.format("%s%s", Utility.getCurrency(), Utility.formatCurrency(Math.abs(customerListViewModel.getBookSummaryModel().amountOut)));
        creditTotal.setText(amountInStr);
        debitTotal.setText(amountOutStr);
        FeaturePrefManager.getInstance().setCreditValue(amountInStr);
        FeaturePrefManager.getInstance().setDebitValue(amountOutStr);
        if (ListUtils.isEmpty(cstList)) {
            this.customerCount = count;
            return;
        } else {
            for (DataHolder dataHolder : cstList) {
                if (dataHolder.getTag() == Tag.VIEW_CUSTOMER) {
                    count++;
                }
            }
        }
        this.customerCount = count;
        showTooltip();
        String sb = customerCount + " " + getString(R.string.customers);
        this.customersCountTV.setText(sb);

        int cognitiveThreshold = TransactionRepository.getInstance(getActivity().getApplicationContext()).getUniqueUtangTransactionCountWithDeletedRecords();

        if (count > 0) {
            FeaturePrefManager.getInstance().setFinishedCstTourMode(true);
            if (isSearchOpen) {
                this.hsvFilter.setVisibility(GONE);
                this.summaryCard.setVisibility(GONE);
                this.searchFilterLayout.setVisibility(GONE);
            } else {
                this.summaryCard.setVisibility(VISIBLE);
                setFiltersVisibility(cognitiveThreshold);
            }
        } else {
            if (customerFilter == CustomerListFilter.ALL) {
                this.searchFilterLayout.setVisibility(GONE);
                this.hsvFilter.setVisibility(GONE);
            }
            this.summaryCard.setVisibility(GONE);
        }

        if (customerFilter != CustomerListFilter.ALL) {
            summaryCard.setVisibility(VISIBLE);
            setFiltersVisibility(cognitiveThreshold);
        }


            if (customerCount < 2 && !isSearchOpen) {
                Animation mAnimation = AnimationUtils.loadAnimation(getContext(), R.anim.swinging);
                if (UtilsKt.isAnimEnabled(getContext())) arrow.startAnimation(mAnimation);
                arrowArea.setVisibility(VISIBLE);
            } else {
                arrowArea.setVisibility(GONE);
            }
    }

    public void setSortOrder(int selectedOrder) {
        this.sortOrder = selectedOrder;
        customerListViewModel.setSortOrder(selectedOrder);
    }

    private void autoScroll(View view) {
        if (view == null) {
            return;
        }

        Rect sRect = new Rect();
        hsvFilter.getDrawingRect(sRect);

        int left = view.getLeft();
        int right = view.getRight();
        int x = 48;
        Log.d("FILTER_SCROLL VIEW", String.format("%s | %s", left, right));
        Log.d("FILTER_SCROLL sVIEW", String.format("%s | %s", sRect.left, sRect.right));

        if (left <= sRect.left && right <= sRect.right) {
            // left-side of the view is partially visible
            int post = left - sRect.left - x;
            hsvFilter.smoothScrollBy(post, 0);
            Log.d("FILTER_SCROLL L", "scroll to " + post);
        } else if (left >= sRect.left && right >= sRect.right) {
            // right-side of the view is partially visible
            int post = right - sRect.right + x;
            hsvFilter.smoothScrollBy(post, 0);
            Log.d("FILTER_SCROLL R", "scroll to " + post);
        }
    }

    @Override
    public void onOnboardingDismiss(@Nullable String id, @NotNull String body, boolean isFromButton, boolean isFromCloseButton, boolean isFromOutside) {
        isAnyTutorialShowing = false;
        if (id == null) return;
        OnboardingPrefManager.Companion.getInstance().setHasFinishedForId(id);
    }

    @Override
    public void onOnboardingButtonClicked(@Nullable String id, boolean isFromHighlight) {
        isExitDialogEnabled = RemoteConfigUtils.INSTANCE.shouldShowExitDialog();
        if (id == null || getContext() == null) return;
        switch (id) {
            case OnboardingPrefManager.TUTOR_CUSTOMER_TAB:
                Intent customerFormIntent = new Intent(getContext(), CustomerActivity.class);
                customerFormIntent.putExtra(CustomerActivity.EXTRA_SHOW_TUTORIAL, true);
                customerFormIntent.putExtra(CustomerActivity.IS_EDIT_TRANSACTION, false);
                startActivity(customerFormIntent);
                break;
            case OnboardingPrefManager.TOUR_CUSTOMER_TAB_ADD_BTN:
                AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder();
                if (isFromHighlight)
                    builder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TOOLTIP_CREATE);
                else
                    builder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TOOLTIP_CONTINUE);
                builder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled);
                if (showOldUtang) {
                    builder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);
                } else {
                    builder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.UPDATED_JUNE);
                }
                builder.put(AnalyticsConst.CUST_FAV_PIN_ENABLED, isFavoriteCustomerPinEnabled);
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_ADD_CUSTOMER_BTN, builder);
                Intent intent = new Intent(getContext(), AddCustomerActivity.class);
                intent.putExtra("ShowCustomerTutorial", true);
                startActivity(intent);
                break;
            case OnboardingPrefManager.TUTORIAL_DOWNLOAD_REPORT_DEBT:
                goToTransactionReport();
                break;
        }
    }

    public void showOnboardingTutorial() {
       // if(showOldForm){
            showOldFormOnboardingTutorial();
       /* }else{
            showNewFormOnboardingTutorial();
        }*/
    }

    public void showOldFormOnboardingTutorial() {
        if (isAnyTutorialShowing) return;
        try {
            coachmarkShown = true;
            isAnyTutorialShowing = true;
            onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), CustomerTab.this,
                    OnboardingPrefManager.TOUR_CUSTOMER_TAB_ADD_BTN, addCustomerBtn, R.drawable.onboarding_smile, "",
                    getString(R.string.body_add_cst_btn), getString(R.string.next), FocusGravity.CENTER, ShapeType.ROUND_RECT,
                    1, 4, true, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onBackPressed() {
        if (onboardingWidget != null && onboardingWidget.isShown()) {
            onboardingWidget.dismiss(false, false, true);
        } else if (getActivity() != null && listener != null) {
            listener.handleNormalBackPressedFromCustomer();
        } else if (getActivity() != null) {
            getActivity().finish();
        }
    }

    public void onGamifyDismissed(boolean shouldShowTooltip) {
        isAnyTutorialShowing = false;
        if (shouldShowTooltip) showTooltip();
    }

    public void onGamifyShow() {
        isAnyTutorialShowing = true;
    }

    private void showGamifyPopup() {
        ((MainActivity) getActivity()).showGamifyPopup(GamifyTarget.UTANG_TRANSACTION);
    }

    private void setFiltersVisibility(int count) {

        if (!FeaturePrefManager.getInstance().isDateFilterCustomerSeen()) {
            ComponentUtil.setVisible(searchFilterLayout, count >= dateFilterVisibilityLimit);
        }
        if (!FeaturePrefManager.getInstance().isSearchFilterCustomerSeen()) {
            ComponentUtil.setVisible(hsvFilter, count >= searchVisibilityLimit);
        }

        if (count >= dateFilterVisibilityLimit) {
            FeaturePrefManager.getInstance().setIsDateFilterCustomerSeen();
            searchFilterLayout.setVisibility(VISIBLE);
        }

        if (count >= searchVisibilityLimit) {
            FeaturePrefManager.getInstance().setIsSearchFilterCustomerSeen();
            hsvFilter.setVisibility(VISIBLE);
        }

    }

    public void showNewFormOnboardingTutorial() {
        if (isAnyTutorialShowing) return;
        if (showOldForm) return;
        coachmarkShown = true;
        isAnyTutorialShowing = true;
        onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), CustomerTab.this,
                OnboardingPrefManager.TUTOR_CUSTOMER_TAB, bottomTabs, R.drawable.onboarding_smile, "",
                getString(R.string.customer_tab_coachmark), getString(R.string.next), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                1, 2, true, false);
    }

    @Override
    public void onItemClicked(CustomerEntity entity) {
        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
        prop.put(AnalyticsConst.DESTINATION, AnalyticsConst.OPEN_CHATBOX);
        Utility.shareTransaction(this, getContext(), getView(), entity, true, entity.balance.toString(), "");
    }
}

