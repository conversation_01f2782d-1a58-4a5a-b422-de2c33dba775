package com.bukuwarung.activities.customer.sort.comparator

import com.bukuwarung.activities.customer.sort.SortOrder
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.utils.isNotNullOrEmpty


class CustomerDueDateComparator : Comparator<CustomerEntity> {

    companion object {
        var sortOrder: SortOrder = SortOrder.ASC

        @JvmStatic
        fun init(order: SortOrder): CustomerDueDateComparator {
            sortOrder = order

            return CustomerDueDateComparator()
        }
    }

    override fun compare(customerEntity1: CustomerEntity, customerEntity2: CustomerEntity): Int {
        val dueDate2 = customerEntity2.dueDate
        val dueDate1 = customerEntity1.dueDate
        if (dueDate1.isNotNullOrEmpty() && dueDate2.isNotNullOrEmpty()) {
            return if (sortOrder == SortOrder.DESC) {
                dueDate2.compareTo(dueDate1)
            } else {
                dueDate1.compareTo(dueDate2)
            }
        }
        if (dueDate1.isNullOrEmpty() && dueDate2.isNotNullOrEmpty()) {
            return if (sortOrder == SortOrder.DESC) {
                -1
            } else {
                1
            }
        }
        if (dueDate2.isNullOrEmpty() && dueDate1.isNotNullOrEmpty()) {
            return if (sortOrder == SortOrder.DESC) {
                1
            } else {
                -1
            }
        }
        return 0
    }
}