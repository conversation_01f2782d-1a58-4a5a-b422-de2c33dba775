package com.bukuwarung.activities.customer


import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.bukuwarung.R
import com.bukuwarung.activities.expense.IncomeExpenseTab
import com.bukuwarung.activities.superclasses.AppFragment
import com.bukuwarung.baseui.DefaultViewPager
import com.bukuwarung.databinding.TransactionHomePageBinding
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.tutor.view.OnboardingWidget.Companion.createInstance
import com.bukuwarung.utils.RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm
import kotlinx.android.synthetic.main.transaction_home_page.view.*


class TransactionTabHome : AppFragment(), OnboardingWidget.OnboardingWidgetListener {

    private lateinit var binding: TransactionHomePageBinding
    private lateinit var viewPager: DefaultViewPager
    private var adapter: TransactionTabAdapter? = null
    private lateinit var onBoardingWidget: OnboardingWidget


    fun onBackPressed() {
         val currentFragment = adapter?.getItem(viewPager.currentItem)
         if (currentFragment is CustomerTab) {
             currentFragment.onBackPressed()
         } else if(currentFragment is IncomeExpenseTab)
             currentFragment.onBackPressed()
     }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = TransactionHomePageBinding.inflate(inflater, container, false)
        viewPager = binding.inventoryHomeViewpager
        adapter = TransactionTabAdapter(getFragmentList(), childFragmentManager)
        viewPager.adapter = adapter
        val position = arguments?.getInt(TAB_POSITION, 0) ?: 0
        setCurrentTab(position)

        val showOldForm = canShowOldTransactionForm()

        if (!AppConfigManager.getInstance().isFirstSessionAfterInstall && !showOldForm && !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTOR_TRANSAKSI_NEW_TAB)) {
            onBoardingWidget = createInstance(requireActivity(), this,
                    OnboardingPrefManager.TUTOR_TRANSAKSI_NEW_TAB, viewPager.inventory_home_tab_layout, R.drawable.onboarding_announce, getString(R.string.transaction_tabs),
                    getString(R.string.transaction_tab_description), "", FocusGravity.CENTER, ShapeType.ROUND_RECT,
                    1, 1, true)
        }

        return binding.root
    }

    fun getFragment():Fragment {
        return adapter!!.getItem(viewPager.currentItem)
    }

    fun setCurrentTab(i: Int) {
        if (i < adapter?.count ?: 0) {
            viewPager.currentItem = i
        } else {
            viewPager.currentItem = 0
        }
    }

    private fun getFragmentList(): List<Pair<Fragment, String>> {

        return listOf(Pair(IncomeExpenseTab(), resources.getString(R.string.tab_expense_label)), Pair(CustomerTab(), resources.getString(R.string.tab_customers_label)))
    }

    companion object {
        private const val TAB_POSITION = "tabPosition"
        @JvmStatic
        fun instance(position: Int): TransactionTabHome {
            val fragment = TransactionTabHome()
            fragment.arguments = Bundle().apply {
                putInt(TAB_POSITION, position)
            }
            return fragment
        }
    }

    override fun onOnboardingDismiss(id: String?, body: String, isFromButton: Boolean, isFromCloseButton: Boolean, isFromOutside: Boolean) {
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
    }

}