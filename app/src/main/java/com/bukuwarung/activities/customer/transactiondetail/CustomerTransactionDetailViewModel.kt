package com.bukuwarung.activities.customer.transactiondetail

import androidx.lifecycle.*
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.data.restclient.RestClient
import com.bukuwarung.data.session.SessionRemoteDataSource
import com.bukuwarung.data.session.SessionRepository
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.payments.data.model.Disbursement
import com.bukuwarung.payments.data.model.PaymentCollection
import com.bukuwarung.payments.data.repository.PaymentsRemoteDataSource
import com.bukuwarung.payments.data.repository.PaymentsRepository
import com.bukuwarung.session.SessionManager
import kotlinx.coroutines.launch
import kotlin.math.abs

internal open class CustomerTransactionDetailViewModel(
        private val transactionRepository: TransactionRepository,
        private val customerRepository: CustomerRepository,
        private var transactionId: String,
        private var customerId: String
) : ViewModel() {

    init {
        getData()
    }

    private val paymentsRepository = PaymentsRepository(
            RestClient.retrofit().create(PaymentsRemoteDataSource::class.java),
            SessionRepository(RestClient.retrofit().create(SessionRemoteDataSource::class.java))
    )

    private lateinit var transactionData: LiveData<TransactionEntity>
    private lateinit var transactionListData: LiveData<List<TransactionEntity>>

    lateinit var stateData: MediatorLiveData<CustomerTransactionDetailState>

    /*Quick fix for receipt detail*/
    private val _isReceiptExpanded = MutableLiveData<Boolean>(false)
    var isReceiptExpanded: LiveData<Boolean> = _isReceiptExpanded

    fun setReceiptState(isExpanded: Boolean) {
        _isReceiptExpanded.postValue(isExpanded)
    }

    fun setTransactionId(newTransactionId: String) {
        val data = transactionRepository.getTransactionById(newTransactionId)

        stateData.removeSource(transactionData)
        this.transactionId = newTransactionId

        transactionData = transactionRepository
                .getObservableTransactionById(newTransactionId)

        transactionListData = transactionRepository.getTransactionListByCustomerId(customerId)

        stateData.addSource(transactionData) {
            addDataToState(it)
        }

        stateData.addSource(transactionListData) {
            addTransactionDetailToState(it)
        }
    }

    private fun getData(transactionId: String = this.transactionId) {
        stateData = MediatorLiveData()

        transactionData =
                transactionRepository
                        .getObservableTransactionById(transactionId)

        transactionListData = transactionRepository.getTransactionListByCustomerId(customerId)

        stateData.addSource(transactionData) {
            addDataToState(it)
        }

        stateData.addSource(transactionListData) {
            addTransactionDetailToState(it)
        }

    }

    private fun addDataToState(it: TransactionEntity?) {

        val customerEntity = customerRepository.getCustomerById(it?.customerId)
        stateData.value = getCurrentState().copy(
                transaction = it,
                customer = customerEntity,
                currentState = if (it == null) {
                    CustomerTransactionDetailStateType.NotFound
                } else {
                    CustomerTransactionDetailStateType.Loaded
                }
        )

        val isPaymentTransaction = it?.isPaymentTransaction ?: false
        if (isPaymentTransaction)
            viewModelScope.launch { startLoadingPaymentData(it) }
    }

    private fun addTransactionDetailToState(transactionEntity: List<TransactionEntity>) {
        val total = abs(transactionEntity.filter { it.amount <= 0 }.sumBy { it.amount.toInt() })
        val paid = abs(transactionEntity.filter { it.amount > 0 }.sumBy { it.amount.toInt() })
        val remaining = abs((total - paid).takeIf { it >= 0 } ?: 0)

        stateData.value = getCurrentState().copy(
                customerTransactionSummaryDto = CustomerTransactionSummaryDto(total, paid, remaining)
        )
    }

    // TODO overload this functions to retrieve the full transaction by passing paymentDisbursableId
    private suspend fun startLoadingPaymentData(transactionEntity: TransactionEntity?) {
        transactionEntity ?: return

        stateData.value = getCurrentState().copy(
                currentPaymentState = CustomerTransactionPaymentDetailStateType.Loading
        )

        // start loading payment data from API
        if (transactionEntity.amount > 0) {
            // payment in
            when (val response = paymentsRepository.getRequestedPayment(
                    accountId = transactionEntity.bookId,
                    customerId = transactionEntity.customerId,
                    requestId = transactionEntity.paymentDisbursableId
            )) {
                is ApiSuccessResponse -> {
                    // show data
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Loaded,
                            paymentCollection = response.body,
                            paymentDisbursement = null
                    )
                }
                is ApiErrorResponse -> {
                    // show error
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Error
                    )
                }
                else -> {}
            }
        } else {
            // payment out
            when (val response = paymentsRepository.getDisbursement(
                    accountId = transactionEntity.bookId,
                    customerId = transactionEntity.customerId,
                    requestId = transactionEntity.paymentDisbursableId
            )) {
                is ApiSuccessResponse -> {
                    // show data
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Loaded,
                            paymentDisbursement = response.body,
                            paymentCollection = null
                    )
                }
                is ApiErrorResponse -> {
                    // show error
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Error
                    )
                }
                else -> {}
            }
        }
    }

    private suspend fun startLoadingPaymentData(bookId: String, paymentRequestId: String, customerId: String, amount: Double) {

        stateData.value = getCurrentState().copy(
                currentPaymentState = CustomerTransactionPaymentDetailStateType.Loading
        )

        // start loading payment data from API
        if (amount > 0) {
            // payment in
            when (val response = paymentsRepository.getRequestedPayment(
                    accountId = bookId,
                    customerId = customerId,
                    requestId = paymentRequestId
            )) {
                is ApiSuccessResponse -> {
                    // show data
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Loaded,
                            paymentCollection = response.body,
                            paymentDisbursement = null
                    )
                }
                is ApiErrorResponse -> {
                    // show error
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Error
                    )
                }
                else -> {}
            }
        } else {
            // payment out
            when (val response = paymentsRepository.getDisbursement(
                    accountId = bookId,
                    customerId = customerId,
                    requestId = paymentRequestId
            )) {
                is ApiSuccessResponse -> {
                    // show data
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Loaded,
                            paymentDisbursement = response.body,
                            paymentCollection = null
                    )
                }
                is ApiErrorResponse -> {
                    // show error
                    stateData.value = getCurrentState().copy(
                            currentPaymentState = CustomerTransactionPaymentDetailStateType.Error
                    )
                }
                else -> {}
            }
        }
    }

    private fun getCurrentState() = stateData.value ?: CustomerTransactionDetailState()

    fun getCashTransactionByCustomerTransactionId(transactionId: String?): CashTransactionEntity? {
        return transactionRepository.getCashTransactionByCustomerTransactionId(transactionId)
    }

}

data class CustomerTransactionDetailState(
        val transaction: TransactionEntity? = null,
        val customer: CustomerEntity? = null,
        val paymentCollection: PaymentCollection? = null, // nullable
        val paymentDisbursement: Disbursement? = null, // nullable
        val currentState: CustomerTransactionDetailStateType = CustomerTransactionDetailStateType.Loading,
        val currentPaymentState: CustomerTransactionPaymentDetailStateType = CustomerTransactionPaymentDetailStateType.IDLE,
        val customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
)

enum class CustomerTransactionDetailStateType {
    Loading,
    Loaded,
    NotFound
}

enum class CustomerTransactionPaymentDetailStateType {
    IDLE,
    Loading,
    Loaded,
    Error
}