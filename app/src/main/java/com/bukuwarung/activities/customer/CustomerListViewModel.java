package com.bukuwarung.activities.customer;

import android.app.Application;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;

import com.bukuwarung.activities.customer.adapter.dataholder.CustomerDataHolder;
import com.bukuwarung.activities.customer.adapter.dataholder.TutorialVideoDataHolder;
import com.bukuwarung.activities.customer.filter.CustomerListFilter;
import com.bukuwarung.activities.customer.sort.comparator.CustomerBalanceComparator;
import com.bukuwarung.activities.customer.sort.comparator.CustomerDueDateComparator;
import com.bukuwarung.activities.customer.sort.comparator.CustomerFreshnessComparator;
import com.bukuwarung.activities.customer.sort.comparator.CustomerNameComparator;
import com.bukuwarung.activities.customer.sort.SortOrder;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.CustomerSort;
import com.bukuwarung.database.dto.BookSummaryModel;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import okhttp3.internal.Util;

public final class CustomerListViewModel extends AndroidViewModel {

    private LiveData<BookEntity> businessLiveData ;
    private List<CustomerEntity> customerList;
    private BookSummaryModel bookSummaryModel;
    private LiveData<Integer> trxCount;
    private LiveData<Integer> utangCount;

    private MediatorLiveData<List<DataHolder>> liveDataMerger = new MediatorLiveData<>();

    private String searchQuery;
    private int sortOrder;
    FeaturePrefManager featureManager = FeaturePrefManager.getInstance();
    private int balanceFilter;

    public CustomerListViewModel(Application application) {
        super(application);
        this.sortOrder = featureManager.getCustomerListSortOrder();
        this.balanceFilter = featureManager.getSelectedFilter();
        businessLiveData = BusinessRepository.getInstance(application).getBusinessById(User.getBusinessId());
        this.liveDataMerger.addSource(CustomerRepository.getInstance(application).getCustomersByBusiness(User.getBusinessId()), new Observer<List<CustomerEntity>>() {

            @Override
            public void onChanged(List<CustomerEntity> list) {
                customerList = list;
                convertToViewObject();
            }
        });

        trxCount = TransactionRepository.getInstance(application).getTransactionCountLiveData(User.getUserId());
        utangCount = TransactionRepository.getInstance(application).getUtangCountLiveData(User.getUserId());
    }

    public final LiveData<List<DataHolder>> getDataHolderList() {
        return this.liveDataMerger;
    }

    public final LiveData<BookEntity> getBusinessLiveData() {
        return this.businessLiveData;
    }

    public final LiveData<Integer> getUtangCountLiveData() {
        return this.utangCount;
    }

    public final List<CustomerEntity> getCustomerList() {
        return this.customerList;
    }

    public final BookSummaryModel getBookSummaryModel() {
        return this.bookSummaryModel;
    }

    public final void setSearchQuery(String str) {
        this.searchQuery = str;
        convertToViewObject();
    }

    public final void setSortOrder(int i) {
        this.sortOrder = i;
        convertToViewObject();
    }

    public final void setBalanceFilter(int i) {
        this.balanceFilter = i;
        convertToViewObject();
    }

    public void convertToViewObject() {
        ArrayList<CustomerEntity> custimerListForOps = new ArrayList<>();
        BookSummaryModel summaryModel = new BookSummaryModel();
        if (this.customerList != null) {
            custimerListForOps.addAll(this.customerList);
        }
        ArrayList<CustomerEntity> filterCustomerList = applySelectedFilter(custimerListForOps);
        if (Utility.isBlank(this.searchQuery)) {
            custimerListForOps = filterCustomerList;
        }
        applySelectedSortOrder(custimerListForOps);

        ArrayList<CustomerEntity> customersMatchingSearch = applySearchStr(custimerListForOps, this.searchQuery);

        ArrayList<DataHolder> convertedList = new ArrayList<>();

        if (!ListUtils.isEmpty(customersMatchingSearch)) {
            for (CustomerEntity customerDataHolder : customersMatchingSearch) {
                convertedList.add(new CustomerDataHolder(customerDataHolder));
                if(customerDataHolder.balance<0){
                    summaryModel.amountOut+=customerDataHolder.balance;
                }else{
                    summaryModel.amountIn+=customerDataHolder.balance;
                }
            }
            convertedList.add(new DataHolder.LastRowHolder());
        } else {
            if(CustomerListFilter.ALL == balanceFilter && /*TODO query is blank*/ Utility.isBlank(this.searchQuery)) {
                convertedList.add(new TutorialVideoDataHolder());
            }else{
                convertedList.add(new DataHolder.NoResultRowHolder());
            }
        }
        this.bookSummaryModel = summaryModel;
        this.liveDataMerger.setValue(convertedList);
    }

    private void applySelectedSortOrder(List<? extends CustomerEntity> list) {
        if (!ListUtils.isEmpty(list)) {
            if (this.sortOrder == CustomerSort.MOST_RECENT) {
                Collections.sort(list, new CustomerFreshnessComparator(SortOrder.DESC));
            } else if (this.sortOrder == CustomerSort.LEAST_AMOUNT) {
                Collections.sort(list, new CustomerBalanceComparator(SortOrder.ASC));
            } else if (this.sortOrder == CustomerSort.MOST_AMOUNT) {
                Collections.sort(list, new CustomerBalanceComparator(SortOrder.DESC));
            } else if (this.sortOrder == CustomerSort.NAME_ASC) {
                Collections.sort(list, new CustomerNameComparator(SortOrder.ASC));
            } else if (this.sortOrder == CustomerSort.NAME_DSC) {
                Collections.sort(list, new CustomerNameComparator(SortOrder.DESC));
            } else if (this.sortOrder == CustomerSort.DUE_DATE) {
                Collections.sort(list, CustomerDueDateComparator.init(SortOrder.ASC));
            }
        }
    }

    private ArrayList<CustomerEntity> applySearchStr(ArrayList<CustomerEntity> customerList, String searchStr) {
        if(customerList == null || customerList.isEmpty() || Utility.isBlank(searchStr)){
            if(!Utility.isBlank(FeaturePrefManager.getInstance().getUtangSearchKeyword())) {
                String searchedKeyword = FeaturePrefManager.getInstance().getUtangSearchKeyword();
                AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
                prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.UTANG_HOME_PAGE);
                prop.put(AnalyticsConst.SEARCH_KEYWORD, searchedKeyword);
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SEARCH_PERFORMED, prop);
                FeaturePrefManager.getInstance().setUtangSearchKeyword("");
            }
            return customerList;
        }
        FeaturePrefManager.getInstance().setUtangSearchKeyword(searchStr);
        ArrayList<CustomerEntity> matchingCustomers = new ArrayList<>();
        for(CustomerEntity customerEntity:customerList){
            if (isMatchingSearchStr(customerEntity.name,searchStr)
                    || isMatchingSearchStr(customerEntity.phone,searchStr)
                        || isMatchingSearchStr(customerEntity.address,searchStr)
                            || isMatchingSearchStr(String.valueOf(customerEntity.balance), searchStr)) {
                matchingCustomers.add(customerEntity);
            }
        }
        return matchingCustomers;
    }

    private boolean isMatchingSearchStr(String targetField, String searchStr){
        return !Utility.isBlank(targetField) && targetField.toLowerCase().contains(searchStr.toLowerCase());
    }

    private ArrayList<CustomerEntity> applySelectedFilter(ArrayList<CustomerEntity> cstList) {
        ArrayList<CustomerEntity> filteredList = new ArrayList<>();
        if (cstList == null) {
            return filteredList;
        }

        if (this.balanceFilter == CustomerListFilter.ALL) {
            return cstList;
        }
        for(CustomerEntity customerEntity:cstList){
            if (isMatchingSelectedFilter(customerEntity)) {
                filteredList.add(customerEntity);
            }
        }

        return filteredList;
    }

    private boolean isMatchingSelectedFilter(CustomerEntity customerEntity){
        String dueDate = customerEntity.dueDate;
        double balance = customerEntity.balance;

        if (this.balanceFilter == CustomerListFilter.DEBIT) {
            return Double.compare(balance, 0d) < 0;
        } else if (this.balanceFilter == CustomerListFilter.CREDIT) {
            return Double.compare(balance, 0d) > 0;
        } else if (this.balanceFilter == CustomerListFilter.NIL) {
            return customerEntity.balance == 0;
        } else if (this.balanceFilter == CustomerListFilter.OVERDUE ) {
            return dueDate != null && DateTimeUtils.convertToDateYYYYMMDD(dueDate).before(DateTimeUtils.convertToDateYYYYMMDD(DateTimeUtils.getCurrentDate()));
        } else if (this.balanceFilter == CustomerListFilter.ALLDUE ) {
            return dueDate!= null && dueDate.equals(DateTimeUtils.getCurrentDate());
        }
        return true;
    }
}
