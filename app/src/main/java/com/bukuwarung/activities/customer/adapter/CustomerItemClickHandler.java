package com.bukuwarung.activities.customer.adapter;

import android.content.Intent;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.activities.customer.adapter.dataholder.CustomerDataHolder;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.session.AuthHelper;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

final class CustomerItemClickHandler implements OnClickListener {

    final CustomerAdapter adapter;

    CustomerItemClickHandler(CustomerAdapter customerAdapter) {
        this.adapter = customerAdapter;
    }

    public final void onClick(View view) {

        int childAdapterPosition = this.adapter.customerRecyclerView.getChildAdapterPosition(view);

        try {
            DataHolder dataHolder =  this.adapter.customerDataHolderList.get(childAdapterPosition);
            if (dataHolder instanceof CustomerDataHolder) {
                //check session before payments related operations
                //it will avoid
                AuthHelper.refreshUserSession();

                Intent intent = new Intent(this.adapter.getContext(), new CustomerTransactionActivity().getClass());
                intent.putExtra("customerId", ((CustomerDataHolder) dataHolder).getCustomerEntity().customerId);
                this.adapter.getContext().startActivity(intent);
//                AppAnalytics.trackEvent("open_customer_transaction_list");
            }

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
