package com.bukuwarung.activities.customer.adapter.filter_tab;

import android.graphics.Typeface;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;

public final class FilterTabViewHolder extends RecyclerView.ViewHolder {

    private LinearLayout container;
    private TextView titleTv;
    final FilterTabAdapter adapter;
    private final FilterTabAdapter.OnFilterClickListener onFilterClickListener;

    public FilterTabViewHolder(FilterTabAdapter filterTabAdapter,
                               View view, FilterTabAdapter.OnFilterClickListener onFilterClickListener) {
        super(view);
        this.adapter = filterTabAdapter;
        this.container = view.findViewById(R.id.filterContainer);
        this.titleTv = view.findViewById(R.id.filterText);
        this.onFilterClickListener = onFilterClickListener;
    }

    public final LinearLayout getContainer() {
        return this.container;
    }

    public final TextView getTitleTv() {
        return this.titleTv;
    }

    public final void bind(FilterTab tab) {
        setupContainer(tab);
        setupTitle(tab);
        setupOnClickListener(tab);
    }

    private void setupContainer(FilterTab tab) {
        container.setBackground(adapter.getContext().getResources().getDrawable(
                tab.isEnabled() ? R.drawable.rectangle_filter : R.drawable.rectangle_filter_unselected
        ));
    }

    private void setupTitle(FilterTab tab) {
        titleTv.setText(tab.name);
        titleTv.setTextColor(adapter.getContext().getResources().getColor(
                tab.isEnabled() ? R.color.white : tab.originalColor
        ));
        titleTv.setTypeface(tab.isEnabled()? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
    }

    private void setupOnClickListener(FilterTab filterTab) {
        container.setOnClickListener(view -> {
            if (filterTab != null) onFilterClickListener.onFilterClick(filterTab);
        });
    }
}
