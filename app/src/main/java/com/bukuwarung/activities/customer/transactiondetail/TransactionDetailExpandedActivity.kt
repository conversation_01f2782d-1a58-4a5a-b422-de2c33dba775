package com.bukuwarung.activities.customer.transactiondetail

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Html
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.expense.dialog.DeleteTrxDialog
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.print.BluetoothPrinter
import com.bukuwarung.activities.print.ReceiptTextParser
import com.bukuwarung.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.activities.print.setup.SetupPrinterActivity
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.transaction.customer.add.AddTransactionActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.dto.CustomerTransactionSummaryDto
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.database.entity.TransactionItemsEntity
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.dialogs.printer.OpenSetupPrinterDialog
import com.bukuwarung.dialogs.printer.PrintingDialog
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.*
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.android.synthetic.main.activity_customer_transaction_detail.*
import kotlinx.android.synthetic.main.activity_customer_transaction_detail.deleteBtn
import kotlinx.android.synthetic.main.activity_customer_transaction_detail.editBtn
import kotlinx.android.synthetic.main.activity_customer_transaction_detail.mainContainer
import kotlinx.android.synthetic.main.activity_customer_transaction_detail_expanded.*
import kotlinx.android.synthetic.main.utang_payment_receipt_layout.view.*
import kotlinx.android.synthetic.main.utang_receipt_layout.view.*
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvTransactionDate
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvTransactionNominal
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvWarungName
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tvWarungPhone
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tv_cst_name
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tv_cst_phone
import kotlinx.android.synthetic.main.utang_receipt_layout.view.tv_transaction_note
import java.util.*

class TransactionDetailExpandedActivity : AppActivity() {

    internal lateinit var viewModel: CustomerTransactionDetailViewModel
        private set

    internal lateinit var handler: Handler

    // these global objects is for printing only
    private var customer: CustomerEntity? = null
    private var transaction: TransactionEntity? = null
    private var customerPayment: CustomerTransactionDetailState? = null
    private var trxProducts: List<TransactionItemDto>? = null
    private var printingDialog: PrintingDialog? = null
    private var customerTransactionSummaryDto: CustomerTransactionSummaryDto? = null
    private var transactionNote: String = ""

    private fun initViewModel() {
        val trxId: String = intent.getStringExtra(CustomerTransactionDetailActivity.TRX_ID_PARAM) ?: "-"
        val cstId: String = intent.getStringExtra(CustomerTransactionDetailActivity.CST_ID_PARAM) ?: "-"

        val factory = CustomerTransactionDetailViewModelFactory(
            this,
            TransactionRepository.getInstance(this),
            CustomerRepository.getInstance(this),
            trxId, cstId
        )

        this.viewModel = ViewModelProviders.of(this, factory)
            .get(CustomerTransactionDetailViewModel::class.java)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_customer_transaction_detail_expanded)

        initViewModel()
        handler = Handler()
        setInitValues()
    }

    fun setInitValues() {
        observeData()
//        tv_date.text = "%s %s ".format(Utility.formatReceiptDate(transactionEntity.date), Utility.getReadableTimeString(transactionEntity.createdAt))
    }

    private fun observeData() {
        viewModel.stateData.observe(this, Observer { state ->
            showBasedOnState(state)
        })
    }

    private fun showBasedOnState(state: CustomerTransactionDetailState) {
        when (state.currentState) {
            CustomerTransactionDetailStateType.Loading -> {
                mainContainer.visibility = View.GONE
                editBtn.visibility = View.GONE
                deleteBtn.visibility = View.GONE
            }
            CustomerTransactionDetailStateType.NotFound -> {
                mainContainer.visibility = View.GONE
                editBtn.visibility = View.GONE
                deleteBtn.visibility = View.GONE

                handler.postDelayed({
                    finish()
                }, 1000)
            }
            CustomerTransactionDetailStateType.Loaded -> {
                mainContainer.visibility = View.VISIBLE
                //requirement change, edit is not allowed for transaction_type = CASH_TRANSACTION
                editBtn.visibility = View.GONE
                deleteBtn.visibility = View.VISIBLE

                customerPayment = state
                customerTransactionSummaryDto = state.customerTransactionSummaryDto

                state.transaction?.let {
                    val transactionType = if (it.amount >= 0) {
                        1
                    } else {
                        -1
                    }


//                    editBtn.visibility = (!it.isPaymentTransaction).asVisibility()
//                    deleteBtn.visibility = (!it.isPaymentTransaction).asVisibility()
//                    utang_receipt.visibility = (!it.isPaymentTransaction).asVisibility()
//                    utang_payment_receipt.visibility = it.isPaymentTransaction.asVisibility()

                    if (it.isPaymentTransaction) {
                        loadPaymentData(state)
                    } else {
                        populateReceipt(it)
                    }

                    customer = state.customer
                    transaction = state.transaction

                    tv_account_name.text = customer?.name
                    tv_name.text = currentBook.businessName.takeIf { it != "Usaha Saya" }
                    tv_date.text = "%s %s".format(Utility.formatReceiptDate(it.date), Utility.getReadableTimeString(it.createdAt))
                    tv_total_transaction.text = "${Utility.getCurrency()}${Utility.formatCurrency(transaction?.amount)}"

                    tv_phone_number_right.text = customer?.phone
                    tv_phone_number.text = Utility.beautifyPhoneNumber(currentBook.businessPhone).takeIf { it.isNotBlank() }
                        ?: Utility.beautifyPhoneNumber(currentBook.ownerId).takeIf { it.isNotBlank() }
                                ?: "-"

                    val cashTransactionEntity = TransactionRepository.getInstance(this).getCashTransactionByCustomerTransactionId(transaction?.transactionId)

                    iv_lunas.text = "Lunas"

                    if (cashTransactionEntity.status == 0) {
                        iv_lunas.text = "Belum Lunas"
                    }

                    val transactionItems = TransactionRepository.getInstance(this).getTransactionItems(cashTransactionEntity.cashTransactionId)

                    trxProducts = convertTransactionItemsToDto(cashTransactionEntity.cashTransactionId, transactionItems)


                    if (transactionItems.isNotEmpty()) {
                        showItems(trxProducts)
                    } else {
                        tv_head_transaction_items.visibility = View.GONE
                        tv_head_transaction_items_qty.visibility = View.GONE
                        v_below_items.visibility = View.GONE
                    }

                    btn_close.setOnClickListener {
                        finish()
                    }

                    btn_success.setOnClickListener {
                        TransactionRepository.getInstance(Application.getAppContext()).settleUnpaidCashTransaction(
                            User.getBusinessId(),
                            customer!!.customerId, transaction!!.amount, Utility.getStorableDateString(Date()),
                            if (customer!!.balance > 0) getString(R.string.filter_nil) else getString(R.string.paid_off), 1, cashTransactionEntity.cashTransactionId
                        )

                        val intent = Intent(this, MainActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                        startActivity(intent)
                        finish()
                    }

                    editBtn.setOnClickListener { _ -> goToEditPage(it.transactionId) }
                    deleteBtn.setOnClickListener { _ -> showDeleteDialog(it.transactionId) }
                    btn_print.setOnClickListener { _ -> checkPrintRequirement(transactionType) }
                    btn_share.setOnClickListener { _ -> shareTrx(it.isPaymentTransaction) }
                }
            }
            else -> {}
        }
    }

    fun showItems(transactionItems: List<TransactionItemDto>?) {
        tv_head_transaction_items.visibility = View.VISIBLE
        tv_head_transaction_items_qty.visibility = View.VISIBLE
        v_below_items.visibility = View.VISIBLE

        val linearItemLayout = ll_transaction_items

        val linearChildItems = LinearLayout(this)
        linearChildItems.orientation = LinearLayout.VERTICAL

        linearItemLayout.removeAllViews()

        for (i in 0 until transactionItems!!.size) {
            val rl = RelativeLayout(this)
            val textItemName = TextView(this)
            textItemName.gravity = Gravity.LEFT

            val textItemQty = TextView(this)
            textItemQty.gravity = Gravity.RIGHT

            val lp: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            lp.addRule(RelativeLayout.ALIGN_PARENT_LEFT, textItemName.id)

            textItemName.layoutParams = lp

            lp.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, textItemQty.id)
            textItemQty.layoutParams = lp

            textItemName.text = transactionItems[i].productName
            textItemQty.text = transactionItems[i].quantity.toString()

            rl.addView(textItemName)
            rl.addView(textItemQty)

            linearChildItems.addView(rl)
        }

        linearItemLayout.addView(linearChildItems)
    }

    @SuppressLint("SetTextI18n")
    private fun populateReceipt(transactionEntity: TransactionEntity) {
        try {
            val receipt = layoutInflater.inflate(R.layout.utang_receipt_layout, subMainContainer, false)

            receipt.tvWarungName.visibility = (!SessionManager.getInstance().isGuestUser).asVisibility()
            receipt.tvWarungPhone.visibility = (!SessionManager.getInstance().isGuestUser).asVisibility()

            receipt.tvWarungName.text = currentBook.businessName.takeIf { it != "Usaha Saya" }
                ?: "-"
            receipt.tvWarungPhone.text = Utility.beautifyPhoneNumber(currentBook.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(currentBook.ownerId).takeIf { it.isNotBlank() }
                        ?: "-"
            receipt.tvTransactionDate.text = "%s %s".format(Utility.formatReceiptDate(transactionEntity.date), Utility.getReadableTimeString(transactionEntity.createdAt))
            val customerEntity = CustomerRepository.getInstance(this).getCustomerById(transactionEntity.customerId)
            receipt.tv_cst_name.text = customerEntity.name
            receipt.tv_cst_phone.text = Utility.beautifyPhoneNumber(customerEntity.phone)
//            receipt.tv_transaction_type.text = if (transactionEntity.amount.orNil >= 0) {
//                /**Menerima*/
//                getString(R.string.receiving_label)
//            } else {
//                /**Memberikan*/
//                getString(R.string.giving_label)
//            }
            receipt.tv_transaction_note.setTextOrDefault(transactionEntity.description)
            transactionNote = transactionEntity.description
            receipt.tvTransactionNominal.text = "${Utility.getCurrency()}${Utility.formatCurrency(transactionEntity.amount)}"

            receipt.tv_transaction_total.text = customerTransactionSummaryDto?.total?.toDouble().asFormattedCurrency()
            receipt.tv_paid_total.text = customerTransactionSummaryDto?.paid?.toDouble().asFormattedCurrency()
            receipt.tv_remaining_total.text = customerTransactionSummaryDto?.remaining?.toDouble().asFormattedCurrency()

//            receipt.expandWithAnimation(receipt.transaction_detail_layout, detailHeight, viewModel.isReceiptExpanded)
            viewModel.isReceiptExpanded.observe(this, Observer {
                receipt.transaction_detail_layout.visibility = it.asVisibility()
            })


            subMainContainer.removeAllViews()
            subMainContainer.addView(receipt)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    private fun loadPaymentData(state: CustomerTransactionDetailState) {
        when (state.currentPaymentState) {
            CustomerTransactionPaymentDetailStateType.Loading -> {
                payment_info_loader.visibility = View.VISIBLE
//                utang_payment_receipt.visibility = View.GONE
                payment_info_error.visibility = View.GONE
            }
            CustomerTransactionPaymentDetailStateType.Loaded -> {
                payment_info_loader.visibility = View.GONE
//                utang_payment_receipt.visibility = View.VISIBLE
                payment_info_error.visibility = View.GONE

                populatePaymentReceipt(state)

            }
            CustomerTransactionPaymentDetailStateType.Error -> {
                payment_info_loader.visibility = View.GONE
//                utang_payment_receipt.visibility = View.GONE
                payment_info_error.visibility = View.VISIBLE
            }
            else -> {}
        }
    }

    private fun shareTrx(isPayment: Boolean) {
        val viewToShare = mainContainer
        generateAndShareViewImage(this, "com.whatsapp", viewToShare, "", true)
    }

    private fun generateAndShareViewImage(context: Context?, packageNm: String?, receiptLayout: View?, mobile: String?, useWA: Boolean) {
        try {
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(receiptLayout, false)
            val shareLayoutImage = ShareLayoutImage(getString(R.string.utang_invoice_sharing_text), context, packageNm, mobile, useWA, false)
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun checkPrintRequirement(transactionType: Int) {

        when (val printers: List<PrinterDataHolder>? = PrinterPrefManager(this).installedPrinters) {
            null -> {
                val intent = Intent(this, SetupPrinterActivity::class.java)
                val isPrinterPaired = PrinterPrefManager(this).defaultDeviceAddress.isNotEmpty()

                if (isPrinterPaired) {
                    startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                } else {
                    val dialog = OpenSetupPrinterDialog(this) {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                            AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CUSTOMER)
                        )

                        startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                    }
                    dialog.show()
                }

            }
            printers -> {
                when {
                    printers.isEmpty() -> {
                        val dialog = OpenSetupPrinterDialog(this) {
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CUSTOMER)
                            )
                            val intent = Intent(this, SetupPrinterActivity::class.java)
                            startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                        }

                        dialog.show()
                    }
                    printers.isNotEmpty() -> {
                        startPrinting()
                    }
                }
            }
        }
    }


    private fun goToEditPage(transactionId: String) {
        val intent = Intent(this, AddTransactionActivity::class.java)
        intent.putExtra("transactionId", transactionId)
        intent.putExtra("OPERATION_TYPE", "")

        startActivityForResult(intent, CustomerTransactionDetailActivity.EDIT_TRX_REQUEST_CODE)
    }

    private fun showDeleteDialog(transactionId: String) {
        val dialog = DeleteTrxDialog(this) {
            if (it) {
                deleteCustomerTransaction(transactionId)
                finish()
            }
        }
        dialog.show()
    }

    private fun deleteCustomerTransaction(transactionId: String) {
        val t = Thread(DeleteCustomerTransactionDetailRunnable(this, transactionId))
        t.start()
        try {
            t.join()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun populatePaymentReceipt(state: CustomerTransactionDetailState) {
        try {
            val trxEntity = state.transaction ?: return
            val customerEntity = state.customer ?: return

            val receipt = layoutInflater.inflate(R.layout.utang_payment_receipt_layout, subMainContainer, false)

            receipt.tvWarungName.text = currentBook.businessName.takeIf { it != "Usaha Saya" }
                ?: "-"
            receipt.tvWarungPhone.text = Utility.beautifyPhoneNumber(currentBook.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(currentBook.ownerId).takeIf { it.isNotBlank() }
                        ?: "-"
            receipt.tvTransactionDate.text = "%s %s".format(Utility.formatReceiptDate(trxEntity.date), Utility.getReadableTimeString(trxEntity.createdAt))

            receipt.tv_cst_name.text = customerEntity.name
            receipt.tv_cst_phone.text = Utility.beautifyPhoneNumber(customerEntity.phone)
            receipt.tvTransactionNominal.text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(trxEntity.amount))

            val showFreeCharge = AppConfigManager.getInstance().paymentFreeChargeStatus

            val amount = state.transaction.amount ?: 0.0
            if (amount > 0) {
                // payment in
                state.paymentCollection?.let {
                    // set transaction ID
                    receipt.tv_invoice_number.text = it.transactionId
                    // set free charge
                    receipt.tv_free_charge.visibility = showFreeCharge.asVisibility()
                    // set sender name (customer name)
                    receipt.tv_sender_name.text = "%s - %s".format(
                        it.paymentChannel, customerEntity.name
                            ?: "-"
                    )
                    // set receiver name
                    receipt.tv_recipient_name.text = "${it.receiverBank?.bankCode ?: "-"} - ${it.receiverBank?.accountHolderName ?: "-"}"
                    // set receiver bank no
                    receipt.tv_recipient_aaccount_number.text = it.receiverBank?.accountNumber
                        ?: "-"
                    // set payment notes
                    val desc = (it.description ?: "").let { str ->
                        if (str.isBlank() || str == "-") {
                            getString(R.string.label_payment_in_note_plain)
                        } else {
                            "%s - %s".format(getString(R.string.label_payment_in_note_plain), str)
                        }

                    }
                    receipt.tv_transaction_note.text = desc
                }
            } else {
                // payment out
                state.paymentDisbursement?.let {
                    // set transaction ID
                    receipt.tv_invoice_number.text = it.transactionId
                    // set free charge
                    receipt.tv_free_charge.visibility = showFreeCharge.asVisibility()
                    // set sender name (merchant name)
                    receipt.tv_sender_name.text = "%s - %s".format(
                        it.paymentChannel, currentBook?.businessOwnerName
                            ?: "-"
                    )
                    // set receiver name
                    receipt.tv_recipient_name.text = "${it.receiverBank?.bankCode ?: "-"} - ${it.receiverBank?.accountHolderName ?: "-"}"
                    // set receiver bank no
                    receipt.tv_recipient_aaccount_number.text = it.receiverBank?.accountNumber
                        ?: "-"
                    // set payment notes
                    val desc = (it.description ?: "").let { str ->
                        if (str.isBlank() || str == "-") {
                            getString(R.string.label_payment_in_note_plain)
                        } else {
                            "%s - %s".format(getString(R.string.label_payment_in_note_plain), str)
                        }

                    }
                    receipt.tv_transaction_note.text = desc
                }
            }

            receipt.tv_transaction_total_payment.text = customerTransactionSummaryDto?.total?.toDouble().asFormattedCurrency()
            receipt.tv_paid_total_payment.text = customerTransactionSummaryDto?.paid?.toDouble().asFormattedCurrency()
            receipt.tv_remaining_total_payment.text = customerTransactionSummaryDto?.remaining?.toDouble().asFormattedCurrency()

            viewModel.isReceiptExpanded.observe(this, Observer {
                receipt.layout_transaction_detail.visibility = it.asVisibility()
            })


            receipt.tv_footer.text = Html.fromHtml(getString(R.string.label_transaction_security_guaranteed))


            subMainContainer.removeAllViews()
            subMainContainer.addView(receipt)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }



    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                CustomerTransactionDetailActivity.EDIT_TRX_REQUEST_CODE -> {
                    data?.getStringExtra(CustomerTransactionDetailActivity.TRX_ID_EDIT_PARAM)?.let { newTrxId ->
                        viewModel.setTransactionId(newTrxId)
                    }
                }
                SetupPrinterActivity.RECORD_DETAIL -> startPrinting()
            }
        }
    }


    private fun startPrinting() {
        transaction ?: return
        var entryPoint: String = AnalyticsConst.TRANSACTION

        // not-null assertion is ok since both objects have checked using elvis operator
        val textToPrint = if (transaction!!.isPaymentTransaction.isTrue) {
            customerPayment ?: return
            entryPoint = AnalyticsConst.PAYMENT
            ReceiptTextParser(this, currentBook).parseCustomerPaymentReceipt(customerPayment!!, customerTransactionSummaryDto, true)
        } else {
            customer ?: return
            ReceiptTextParser(this, currentBook).parseCustomerReceipt(customer!!, transaction!!, customerTransactionSummaryDto, true)

        }

        val printer = BluetoothPrinter(this, entryPoint)
        printingDialog = printer.printingDialog
        printer.printPrintables(textToPrint, object : PermissionCallback {
            override fun onPermissionRequired(permissions: Array<String>) {
                printingDialog?.dismiss()
                permissionLauncher.launch(permissions)
            }
        })
    }

    private val permissionLauncher: ActivityResultLauncher<Array<String>> =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) {
            if (it.containsValue(false)) {
                Toast.makeText(this, R.string.location_permission_denied_message, Toast.LENGTH_SHORT)
                    .show()
            }
        }

    private fun convertTransactionItemsToDto(
        cashTransactionId: String?,
        transactionItems: List<TransactionItemsEntity>
    ): List<TransactionItemDto>? {
        val ret = ArrayList<TransactionItemDto>()
        try {
            for (transactionItem in transactionItems) {
                val productEntity = ProductRepository.getInstance(this).getProductsById(transactionItem.productId)
                if (productEntity != null) {
                    val productSelection = TransactionItemDto()
                    if (cashTransactionId != null) productSelection.transactionId = cashTransactionId
                    productSelection.productName = productEntity.name
                    productSelection.quantity = transactionItem.quantity
                    ret.add(productSelection)
                }
            }
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
        }
        return ret
    }


    override fun onDestroy() {
        super.onDestroy()
        printingDialog?.let {
            it.dismiss()
            printingDialog = null
        }
    }

    companion object {

        const val EDIT_TRX_REQUEST_CODE = 11
        const val TRX_ID_EDIT_PARAM = "TrxIdFromEdit"

        const val TRX_ID_PARAM = "transactionId"
        const val CST_ID_PARAM = "customerId"

        fun getNewIntent(origin: Context, trxId: String, customerId: String): Intent {
            val intent = Intent(origin, CustomerTransactionDetailActivity::class.java)
            intent.putExtra(TRX_ID_PARAM, trxId)
            intent.putExtra(CST_ID_PARAM, customerId)

            return intent
        }

    }
}