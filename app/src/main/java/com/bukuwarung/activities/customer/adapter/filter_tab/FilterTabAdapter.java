package com.bukuwarung.activities.customer.adapter.filter_tab;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;

import java.util.List;

public final class FilterTabAdapter extends RecyclerView.Adapter<FilterTabViewHolder> {

    private final Context context;
    public List<FilterTab> filterTabs;
    private OnFilterClickListener onFilterClickListener;

    public final Context getContext() {
        return this.context;
    }

    public FilterTabAdapter(List<FilterTab> filterTabs, Context context, OnFilterClickListener onFilterClickListener) {
        this.filterTabs = filterTabs;
        this.context = context;
        this.onFilterClickListener = onFilterClickListener;
    }

    public long getItemId(int i) {
        return this.filterTabs.get(i).getId();
    }

    public FilterTabViewHolder onCreateViewHolder(ViewGroup viewGroup, int tag) {
        return new FilterTabViewHolder(
                this,
                LayoutInflater.from(
                        viewGroup.getContext()).inflate(R.layout.filter_tab_item,
                        viewGroup,
                        false
                ),
                onFilterClickListener
        );
    }

    public void onBindViewHolder(FilterTabViewHolder holder, int i) {
        FilterTab tab = this.filterTabs.get(i);
        holder.bind(tab);
    }

    public int getItemCount() {
        return this.filterTabs.size();
    }

    public interface OnFilterClickListener {
        void onFilterClick(FilterTab filterTab);
    }
}
