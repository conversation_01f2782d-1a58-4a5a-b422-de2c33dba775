package com.bukuwarung.activities.customer.sort;

import android.view.MenuItem;
import android.view.View;
import android.widget.PopupMenu;

import com.bukuwarung.R;
import com.bukuwarung.activities.customer.CustomerTab;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.CustomerSort;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.utils.RemoteConfigUtils;

public class CustomerSortMenuClickHandler implements PopupMenu.OnMenuItemClickListener {
    private CustomerTab customerTab;
    private PopupMenu sortPopupMenu;
    public CustomerSortMenuClickHandler(CustomerTab customerTab,PopupMenu sortPopupMenu){
        this.customerTab = customerTab;
        this.sortPopupMenu = sortPopupMenu;
        MenuItem item = sortPopupMenu.getMenu().getItem(5);

        boolean isUtangDueDateFeatureEnabled = RemoteConfigUtils.INSTANCE.isUtangDueDateFeatureEnabled();
        if (isUtangDueDateFeatureEnabled) {
            item.setVisible(true);
        } else {
            item.setVisible(false);
        }

    }
    @Override
    public boolean onMenuItemClick(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.leastAmount:
                actionMenuItem((PopupMenu) sortPopupMenu, CustomerSort.LEAST_AMOUNT);
//                AppAnalytics.trackEvent("customer_tap_sort","sort","least amount");
                break;
            case R.id.mostAmount:
                actionMenuItem((PopupMenu) sortPopupMenu, CustomerSort.MOST_AMOUNT);
//                AppAnalytics.trackEvent("customer_tap_sort","sort","most amount");
                break;
            case R.id.mostRecent:
                actionMenuItem((PopupMenu) sortPopupMenu, CustomerSort.MOST_RECENT);
//                AppAnalytics.trackEvent("customer_tap_sort","sort","most recent");
                break;
            case R.id.nameAsc:
                actionMenuItem((PopupMenu) sortPopupMenu, CustomerSort.NAME_ASC);
//                AppAnalytics.trackEvent("customer_tap_sort","sort","name ascending");
                break;
            case R.id.nameDsc:
                actionMenuItem((PopupMenu) sortPopupMenu, CustomerSort.NAME_DSC);
//                AppAnalytics.trackEvent("customer_tap_sort","sort","name descending");
                break;
            case R.id.dueDate:
                actionMenuItem((PopupMenu) sortPopupMenu, CustomerSort.DUE_DATE);
                break;
            default:
                actionMenuItem((PopupMenu) sortPopupMenu, CustomerSort.MOST_RECENT);
//                AppAnalytics.trackEvent("customer_tap_sort","sort","most recent");
                break;
        }
        return true;
    }

    public final void actionMenuItem(PopupMenu sortPopupMenu, int selectedSortOrder) {
        FeaturePrefManager.getInstance().setCustomerListSortOrder(selectedSortOrder);
        customerTab.setSortOrder(selectedSortOrder);
        sortPopupMenu.dismiss();
    }
}
