package com.bukuwarung.activities.customer;

import android.text.Editable;
import android.text.TextWatcher;

public class SearchBoxTextWatcher implements TextWatcher {
    CustomerTab customerTab;
    public SearchBoxTextWatcher(CustomerTab tab){
        customerTab = tab;
    }
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        customerTab.getCustomerListViewModel(customerTab).setSearchQuery(String.valueOf(editable));
    }
}
