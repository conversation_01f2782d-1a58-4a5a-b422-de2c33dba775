package com.bukuwarung.activities.customer.adapter.filter_tab;

public class FilterTab {

    final int id;
    final String name;
    private boolean enabled;
    final int originalColor;
    final int type;
    final String analyticsTag;

    public FilterTab(int id, String name, boolean enabled, int originalColor, int type, String analyticsTag) {
        this.id = id;
        this.name = name;
        this.enabled = enabled;
        this.originalColor = originalColor;
        this.type = type;
        this.analyticsTag = analyticsTag;
    }

    public int getId() { return this.id; }
    public String getName() { return this.name; }
    public int getType() { return this.type; }
    public String getAnalyticsTag() { return this.analyticsTag; }
    public boolean isEnabled() { return this.enabled; }
    public int getOriginalColor() { return this.originalColor; }

    public void setEnabled(boolean enabled) { this.enabled = enabled; }
}
