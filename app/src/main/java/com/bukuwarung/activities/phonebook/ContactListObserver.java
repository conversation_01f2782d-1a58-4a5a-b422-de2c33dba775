package com.bukuwarung.activities.phonebook;

import androidx.lifecycle.Observer;

import com.bukuwarung.activities.phonebook.model.RowHolder;

import java.util.List;

public  final class ContactListObserver<T> implements Observer<List<? extends RowHolder>> {
    final PhonebookSearchDialog activity;

    public ContactListObserver(PhonebookSearchDialog phonebookSearchDialog) {
        this.activity = phonebookSearchDialog;
    }

    public final void onChanged(List<? extends RowHolder> list) {
        PhonebookSearchDialog.getAdapter(this.activity).setRowHolderList(list);
    }
}
