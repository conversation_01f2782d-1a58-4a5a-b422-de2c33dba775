package com.bukuwarung.activities.phonebook.viewholder

import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.databinding.ReferralContactItemLayoutBinding
import com.bukuwarung.utils.ProfileIconHelper

class ReferralContactViewHolder(private val binding: ReferralContactItemLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
    fun bind(contact: Contact, callback: (Contact) -> Unit) {
        binding.btnInvite.setOnClickListener { callback(contact) }
        ProfileIconHelper.setProfilePic(binding.root.context, binding.contactPhoto, binding.nameInitials, contact.name, contact.photo)
        binding.name.text = contact.name
        binding.mobile.text = contact.mobile
    }
}