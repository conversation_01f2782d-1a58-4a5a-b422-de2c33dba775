package com.bukuwarung.activities.phonebook;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.bukuwarung.R;
import com.bukuwarung.activities.phonebook.handler.PhonebookContactClickHandler;
import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.activities.phonebook.model.RowHolder;
import com.bukuwarung.activities.phonebook.viewholder.ContactViewHolder;
import com.bukuwarung.activities.phonebook.viewholder.EmptyViewHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.utils.ProfileIconHelper;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.List;


public final class PhonebookAdapter extends Adapter<ViewHolder> {

    private final Context context;
    public final RecyclerView mRecyclerView;
    private final EditText searchInput;
    public List<? extends RowHolder> contactList;
    private Intent oldIntent;
    public Dialog dlg;
    private final PhoneContactViewModel contactListViewModel;
    private PhonebookSearchDialog.OnCustomerSelectedCallback onCustomerSelectedCallback;

    public final Context getContext() {
        return this.context;
    }
    public Activity activity;

    public PhonebookAdapter(Activity activity, Dialog dlg,
            List<? extends RowHolder> list, RecyclerView recyclerView, Context context,
                            PhoneContactViewModel phoneContactViewModel, EditText searchInput,
                            PhonebookSearchDialog.OnCustomerSelectedCallback onCustomerSelectedCallback) {
        this.activity = activity;
        this.contactList = list;
        this.mRecyclerView = recyclerView;
        this.context = context;
        this.contactListViewModel = phoneContactViewModel;
        this.searchInput = searchInput;
        this.dlg = dlg;
        this.onCustomerSelectedCallback = onCustomerSelectedCallback;
    }

    private final void bindContactViewHolder(ContactViewHolder contactViewHolder, RowHolder.ContactRowHolder contactRowHolder) {
        contactViewHolder.getName().setText(contactRowHolder.getContact().getName());
        contactViewHolder.getMobile().setText(Utility.beautifyPhoneNumber(contactRowHolder.getContact().getMobile()));
        contactViewHolder.getContactLayout().setOnClickListener(
                new PhonebookContactClickHandler(this, this.onCustomerSelectedCallback , contactRowHolder, oldIntent));
        Contact contact = contactRowHolder.getContact();
        ProfileIconHelper.setProfilePic(this.context, contactViewHolder.getContactPic(), contactViewHolder.getNameInitials(), contact.getName(), contact.getPhoto());
    }

    public final void setRowHolderList(List<? extends RowHolder> contactList) {
        if (contactList == null) {
            this.contactList = new ArrayList();
        } else {
            this.contactList = contactList;
        }
        CharSequence obj = this.searchInput.getText().toString();
        if (obj == null || obj.length() == 0) {
            List<? extends RowHolder> filterContactList = this.contactList;
            if (contactList != null) {
                ((ArrayList) filterContactList).add(0, new RowHolder.NewPhoneNoRowHolder());
            }
        }
        notifyDataSetChanged();
    }

    public int getItemViewType(int i) {
        return ((RowHolder) this.contactList.get(i)).getTag();
    }

    public long getItemId(int i) {
        RowHolder rowHolder = (RowHolder) this.contactList.get(i);
        if (rowHolder instanceof RowHolder.ContactRowHolder) {
            return (rowHolder.getTag()+":"+((RowHolder.ContactRowHolder)rowHolder).getContact().getMobile()).hashCode();
        } else {
            return (":"+i).hashCode();
        }
    }

    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        boolean isOldUtang = RemoteConfigUtils.INSTANCE.shouldShowOldUtangForm();
        if (i == Tag.VIEW_CONTACT) {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.phonebook_contact_item, viewGroup, false);
            return new ContactViewHolder(view);
        }else if (i == Tag.VIEW_CONTACT_SEPARATOR) {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.phonebook_contact_separator, viewGroup, false);
            if (!isOldUtang) {
                view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.phonebook_contact_separator_new, viewGroup, false);
            }
            return new EmptyViewHolder(view);
        }else if (i == Tag.VIEW_ADDED_CONTACT_SEPARATOR) {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.phonebook_customer_separator, viewGroup, false);
            if (!isOldUtang) {
                view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.phonebook_customer_separator_new, viewGroup, false);
            }
            return new EmptyViewHolder(view);
        } else {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.blank_layout, viewGroup, false);
            return new EmptyViewHolder(view);
        }
    }

    public void onBindViewHolder(ViewHolder viewHolder, int i) {
        RowHolder rowHolder = (RowHolder) this.contactList.get(i);
        int itemViewType = viewHolder.getItemViewType();
        if (itemViewType == Tag.VIEW_CONTACT) {
            ContactViewHolder contactViewHolder = (ContactViewHolder) viewHolder;
            if (rowHolder != null) {
                bindContactViewHolder(contactViewHolder, (RowHolder.ContactRowHolder) rowHolder);
            }
        }
    }

    public int getItemCount() {
        return this.contactList.size();
    }
}
