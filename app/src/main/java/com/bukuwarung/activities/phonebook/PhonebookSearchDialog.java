package com.bukuwarung.activities.phonebook;

import android.app.Dialog;
import android.content.Context;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;
import androidx.lifecycle.ViewModelProviders;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity;
import com.bukuwarung.activities.phonebook.handler.ContactListSearchBoxChangeListener;
import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.OnboardingPrefManager;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.Utility;
import com.google.android.material.button.MaterialButton;

import java.util.ArrayList;
import java.util.List;

import static com.bukuwarung.preference.OnboardingPrefManager.TUTORIAL_CONTACT_PERMISSION;

public final class PhonebookSearchDialog extends Dialog implements OnClickListener, ContactPermissionBottomSheetDialog.ContactPermissionSheetListener {

    public PhonebookAdapter adapter;
    private RelativeLayout newCustomerBtnLayout;
    private ImageView clearSearch;
    private MaterialButton permissionButton;
    public RelativeLayout permissionExplainLayout;
    private EditText searchInput;
    public RecyclerView recyclerView;
    public PhoneContactViewModel rowHolderListViewModel;
    private AddCustomerActivity activity;
    private Context context;
    private OnCustomerSelectedCallback onCustomerSelectedCallback;

    public PhonebookSearchDialog(AddCustomerActivity activity, Context context){
        super(context,R.style.FullScreenDialogTheme);
        this.activity = activity;
        this.context = context;
    }

    public static PhonebookAdapter getAdapter(PhonebookSearchDialog phonebookSearchDialog) {
        return phonebookSearchDialog.adapter;
    }

    public static PhoneContactViewModel getRowHolderListModel(PhonebookSearchDialog phonebookSearchDialog) {
        return phonebookSearchDialog.rowHolderListViewModel;
    }

    public void setOnCustomerSelectedCallback(OnCustomerSelectedCallback onCustomerSelectedCallback) {
        this.onCustomerSelectedCallback = onCustomerSelectedCallback;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        requestWindowFeature(1);
        setContentView(R.layout.activity_phonebook_search);
        Window window = getWindow();
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        getWindow().setGravity(80);
        this.newCustomerBtnLayout = findViewById(R.id.new_contact_layout);
        this.newCustomerBtnLayout.setOnClickListener(this);

        this.permissionExplainLayout = findViewById(R.id.explainContactPermission);
        this.permissionButton = findViewById(R.id.permissionButton);
        this.permissionButton.setOnClickListener(this);

        this.searchInput = findViewById(R.id.search_input);
        initSearchEditText();
        removeFocusFromEditText();
        this.clearSearch = findViewById(R.id.clear);
        clearSearch.setOnClickListener(this);

        this.recyclerView =  findViewById(R.id.contactPickerRV);

        if (PermissonUtil.hasContactPermission()) {
            this.permissionExplainLayout.setVisibility(View.GONE);
        } else {
            this.permissionExplainLayout.setVisibility(View.VISIBLE);
        }
        initContactRecyclerView();
        findViewById(R.id.closeBtn).setOnClickListener(view -> dismiss());
        if(!PermissonUtil.hasContactPermission() && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(TUTORIAL_CONTACT_PERMISSION) && AppConfigManager.getInstance().showCustomPermissionDialog()) {
            ContactPermissionBottomSheetDialog dialog = ContactPermissionBottomSheetDialog.Companion.newInstance(AnalyticsConst.UTANG_FORM);
            dialog.show(dialog.getChildFragmentManager(), ContactPermissionBottomSheetDialog.TAG);
            OnboardingPrefManager.Companion.getInstance().setHasFinishedForId(TUTORIAL_CONTACT_PERMISSION);
        } else {
            activity.requestContactPermissions();
        }
    }

    private void initContactRecyclerView(){
        List emptyList = new ArrayList();
        this.rowHolderListViewModel = ViewModelProviders.of(activity).get(PhoneContactViewModel.class);
        rowHolderListViewModel.loadContactsInList();
        this.adapter = new PhonebookAdapter(activity, this,
                emptyList, this.recyclerView, activity, this.rowHolderListViewModel, this.searchInput, this.onCustomerSelectedCallback);
        this.adapter.setHasStableIds(true);
        this.recyclerView.setAdapter( this.adapter);
        this.recyclerView.setLayoutManager(new LinearLayoutManager(context));
        this.rowHolderListViewModel.getRowHolderList().observe(activity, new ContactListObserver<>(this));
    }

    private void initSearchEditText() {
        EditText editText = this.searchInput;
        editText.addTextChangedListener(new ContactListSearchBoxChangeListener(this));
    }

    private void removeFocusFromEditText() {
        getWindow().setSoftInputMode(3);
    }

    public void setSearchVisibility(boolean isEmpty){
        ComponentUtil.setVisible(this.clearSearch,!isEmpty);
    }

    public void onClick(View view) {

        Integer viewId = view != null ? Integer.valueOf(view.getId()) : null;
        if(viewId == null) return;
        if (viewId.intValue() == R.id.new_contact_layout) {
            openAddCategoryDialog(this, activity);
        } else if (viewId.intValue() == R.id.permissionButton) {
            if (PermissonUtil.hasContactPermission()) {
                PhoneContactViewModel phoneContactViewModel = this.rowHolderListViewModel;
                phoneContactViewModel.loadContactsInList();
                return;
            }
            ActivityCompat.requestPermissions(activity, PermissionConst.READ_WRITE_CONTACTS, PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION);
        } else if (viewId.intValue() == R.id.clear) {
            EditText editText = this.searchInput;
            editText.getText().clear();
            Utility.hideKeyboard(activity);
            ComponentUtil.setVisible(this.clearSearch,false);
        }
    }

    public final void openAddCategoryDialog(final PhonebookSearchDialog parent, final AddCustomerActivity activity) {

        final Context context = parent.context;
        final Dialog dialog = new Dialog(context,android.R.style.Theme_DeviceDefault_Light_Dialog);

        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_new_customer);
        Window window = dialog.getWindow();
        if (window != null) {
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
        }
        dialog.setCancelable(false);
        final EditText customerNm = dialog.findViewById(R.id.customerNm);
        final EditText customerPhone = dialog.findViewById(R.id.customerPhone);
        customerNm.requestFocus();
        dialog.findViewById(R.id.closeDialog).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dialog.dismiss();
                InputUtils.hideKeyboardDlg(activity,customerNm,context);
            }
        });

        dialog.findViewById(R.id.btn_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dialog.dismiss();
                InputUtils.hideKeyboardDlg(activity,customerNm,context);
            }
        });

        dialog.findViewById(R.id.btn_save).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String name = customerNm.getText().toString();
                String phoneNumber = customerPhone.getText().toString();


                    activity.phoneText.setText(name);
                    activity.getCustomer().mobile = Utility.cleanPhonenumber(phoneNumber);
                    activity.getCustomer().name = name;


                    onCustomerSelectedCallback.onCustomerSelected(new Contact(
                            name,
                            phoneNumber,
                            "",
                            null

                    ));
                    dialog.dismiss();
                    dismiss();

            }
        });

        dialog.show();

    }
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        InputUtils.hideKeyBoardWithCheck(activity);
    }

    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        if (i == PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION && VERSION.SDK_INT >= 23) {
            if (PermissonUtil.hasContactPermission()) {
                this.rowHolderListViewModel.loadContactsInList();
                this.adapter.notifyDataSetChanged();
                this.permissionExplainLayout.setVisibility(View.GONE);
                this.recyclerView.setVisibility(View.VISIBLE);
                return;
            }
            Toast.makeText(activity, activity.getString(R.string.alert_deny_contact_permission), Toast.LENGTH_LONG).show();
        }
    }

    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            onBackPressed();
        }
        return true;
    }

    @Override
    public void allowPermission() {
        activity.requestContactPermissions();
    }

    public interface OnCustomerSelectedCallback {

        public void onCustomerSelected(Contact contact);

    }


}
