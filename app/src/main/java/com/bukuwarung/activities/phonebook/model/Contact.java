package com.bukuwarung.activities.phonebook.model;


import com.bukuwarung.database.entity.BankAccount;

import java.util.List;

public final class Contact {
    private String mobile;
    private final String name;
    private final String photo;
    private String customerId;
    private List<BankAccount> banks;

    public Contact(String name, String mobile, String photo,String cstId) {
        this.name = name;
        this.mobile = mobile;
        this.photo = photo;
        this.customerId = cstId;
    }

    public final String getMobile() {
        return this.mobile;
    }

    public final String setMobile(String mobile) {
        return this.mobile = mobile;
    }

    public final String getName() {
        return this.name;
    }

    public final String getPhoto() {
        return this.photo;
    }

    public final String getCustomerId() {
        return this.customerId;
    }
    public final String setCustomerId(String customerId) {
        return this.customerId = customerId;
    }

    public List<BankAccount> getBanks() {
        return banks;
    }

    public void setBanks(List<BankAccount> banks) {
        this.banks = banks;
    }
}
