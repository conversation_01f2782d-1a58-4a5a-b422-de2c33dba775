package com.bukuwarung.activities.phonebook.model;

import com.bukuwarung.activities.phonebook.ContactRepository;
import com.bukuwarung.constants.Tag;

public abstract class RowHolder {

    private int tag;

    public static final class ContactRowHolder extends RowHolder {

        private final Contact contact;
        private String contactSource = "";
        private String favouriteText = "";
        private String favouriteCount = "";

        public ContactRowHolder(Contact contact) {
            this.contact = contact;
            setTag(Tag.VIEW_CONTACT);
        }

        public ContactRowHolder(Contact contact, String contactSource, String favouriteText, String favouriteCount){
            this.contact = contact;
            this.contactSource = contactSource;
            this.favouriteText = favouriteText;
            this.favouriteCount = favouriteCount;
            setTag(Tag.VIEW_CONTACT);
        }

        public final Contact getContact() {
            return this.contact;
        }

        public String getName() {
            return this.contact.getName();
        }

        public String getContactSource() {
            return contactSource;
        }

        public String getFavouriteText() {
            return favouriteText;
        }

        public String getFavouriteCount() {
            return favouriteCount;
        }
    }

    public static final class NewPhoneNoRowHolder extends RowHolder {
        public String getName() {
            return " ";
        }

        public NewPhoneNoRowHolder() {
            setTag(Tag.VIEW_NEW_PHONE);
        }
    }

    public static final class SeparatorHolder extends RowHolder {
        public String getName() {
            return ContactRepository.DUMMY_SEPARATOR;
        }

        public SeparatorHolder() {
            setTag(Tag.VIEW_CONTACT_SEPARATOR);
        }
    }

    public static final class CustomerSeparatorHolder extends RowHolder {
        public String getName() {
            return ContactRepository.DUMMY_SEPARATOR;
        }

        public CustomerSeparatorHolder() {
            setTag(Tag.VIEW_ADDED_CONTACT_SEPARATOR);
        }
    }

    public static final class FavoriteCustomerSeparatorHolder extends RowHolder{
        public  String getName() {return  ContactRepository.DUMMY_FAVORITE_TITLE_SEPARATOR;}

        public FavoriteCustomerSeparatorHolder(){
            setTag(Tag.VIEW_FAVORITE_CONTACT_SEPARATOR);
        }
    }

    public static final class RecommendationCustomerSeparatorHolder extends RowHolder {
        public String getName() {
            return ContactRepository.DUMMY_RECOMMENDATION_TITLE_SEPARATOR;
        }

        public RecommendationCustomerSeparatorHolder() {
            setTag(Tag.VIEW_RECOMMENDATION_CONTACT_SEPARATOR);
        }
    }

    public static final class LoadMoreRecommendationSeparatorHolder extends RowHolder {
        public String getName() {
            return ContactRepository.DUMMY_RECOMMENDATION_BUTTON_SEPARATOR;
        }

        public LoadMoreRecommendationSeparatorHolder() {
            setTag(Tag.BUTTON_LOAD_MORE_RECOMMENDATIONS);
        }
    }

    public final int getTag() {
        return this.tag;
    }

    public final void setTag(int i) {
        this.tag = i;
    }
}
