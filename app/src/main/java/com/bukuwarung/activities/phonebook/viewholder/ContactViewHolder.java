package com.bukuwarung.activities.phonebook.viewholder;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.google.android.material.button.MaterialButton;

public final class ContactViewHolder extends RecyclerView.ViewHolder {
    private MaterialButton add;
    private RelativeLayout contactLayout;
    private AppCompatImageView contactPic;
    private TextView nameInitials;
    private TextView mobile;
    private TextView name;
    private ImageView ivFavourite;

    public ContactViewHolder(View view) {
        super(view);
        this.mobile = view.findViewById(R.id.mobile);
        this.add = view.findViewById(R.id.add);
        this.nameInitials = view.findViewById(R.id.nameInitials);
        this.contactPic = view.findViewById(R.id.contact_photo);
        this.contactLayout = view.findViewById(R.id.contactLayout);
        this.name = view.findViewById(R.id.name);
        this.ivFavourite = view.findViewById((R.id.iv_favourite));
    }

    public TextView getName() {
        return this.name;
    }

    public TextView getMobile() {
        return this.mobile;
    }

    public TextView getNameInitials() {
        return this.nameInitials;
    }

    public AppCompatImageView getContactPic() {
        return this.contactPic;
    }

    public RelativeLayout getContactLayout() {
        return this.contactLayout;
    }

    public ImageView getIvFavourite() { return this.ivFavourite; }
}