package com.bukuwarung.activities.phonebook.handler;

import android.text.Editable;
import android.text.TextWatcher;

import com.bukuwarung.activities.phonebook.PhonebookSearchDialog;
import com.bukuwarung.utils.Utility;

public class ContactListSearchBoxChangeListener implements TextWatcher {
    final PhonebookSearchDialog activity;


    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }


    public ContactListSearchBoxChangeListener(PhonebookSearchDialog phonebookSearchDialog) {
        this.activity = phonebookSearchDialog;
    }

    public void afterTextChanged(Editable editable) {
        try {
            PhonebookSearchDialog.getRowHolderListModel(this.activity).setSearchQuery(String.valueOf(editable));
            activity.setSearchVisibility(Utility.isBlank(String.valueOf(editable)));

        }catch (Exception e){
            e.printStackTrace();
        }

    }
}
