package com.bukuwarung.activities.phonebook;

import android.app.Application;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;

import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.activities.phonebook.model.RowHolder;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.List;

public final class PhoneContactViewModel extends AndroidViewModel {

    public ArrayList<Contact> contactList;
    private MediatorLiveData<List<RowHolder>> liveDataMerger = new MediatorLiveData<>();
    private String searchQuery;

    public PhoneContactViewModel(Application application) {

        super(application);
        this.liveDataMerger.addSource(ContactRepository.INSTANCE.getContactList(), new Observer<ArrayList<Contact>>() {

            @Override
            public final void onChanged(ArrayList<Contact> arrayList) {
            contactList = arrayList;
            refresh();
            }
        });
    }

    public final LiveData<List<RowHolder>> getRowHolderList() {
        return this.liveDataMerger;
    }

    public final void setSearchQuery(String str) {
        this.searchQuery = str;
        refresh();
    }

    public final void loadContactsInList() {
        ContactRepository.INSTANCE.refreshContactList();
    }


    public final void refresh() {

        ArrayList matchingContactList = new ArrayList();
        if(ListUtils.isEmpty(contactList)){
            return;
        }
        for (Object next : contactList) {
            if (matchesSearchFilter((Contact) next)) {
                matchingContactList.add(next);
            }
        }
        ArrayList resultList = new ArrayList();
        contactsToViewHolderList(resultList,matchingContactList);
        this.liveDataMerger.setValue(resultList);
    }

    private final void contactsToViewHolderList(ArrayList<RowHolder> resultList, ArrayList<Contact> contactList) {
        if (!ListUtils.isEmpty(contactList)) {
            int size = contactList.size();
            for (int i = 0; i < size; i++) {
                Contact contact = contactList.get(i);
                if(contact.getCustomerId()!=null && contact.getCustomerId().equals(ContactRepository.DUMMY_SEPARATOR)) {
                    resultList.add(new RowHolder.SeparatorHolder());
                } else if(contact.getCustomerId()!=null && contact.getCustomerId().equals(ContactRepository.DUMMY_TITLE_SEPARATOR)) {
                    resultList.add(new RowHolder.CustomerSeparatorHolder());
                } else {
                    resultList.add(new RowHolder.ContactRowHolder(contact));
                }
            }
        }
    }

    private final boolean matchesSearchFilter(Contact contact) {

        if (Utility.isBlank(searchQuery)) {
            return true;
        }

        String name = contact.getName();
        String mobile = contact.getMobile();

        if(Utility.isBlank(name) && Utility.isBlank(mobile)) {
            return false;
        }else{
            return matchingStr(name, this.searchQuery) || matchingStr(mobile, this.searchQuery);
        }
    }

    private boolean matchingStr(String original,String query){
        if(Utility.isBlank(original))
            return false;
        String originalStr = original.toLowerCase();
        String queryStr = query.toLowerCase();
        return originalStr.contains(queryStr);
    }
}
