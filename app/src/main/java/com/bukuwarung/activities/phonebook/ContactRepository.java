package com.bukuwarung.activities.phonebook;

import android.content.Context;
import android.database.Cursor;
import android.os.AsyncTask;
import android.provider.ContactsContract;

import androidx.lifecycle.MutableLiveData;

import com.bukuwarung.Application;
import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.activities.phonebook.model.ContactDetail;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.comparator.ContactComparator;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


public final class ContactRepository {

    public static final ContactRepository INSTANCE;
    public static String DUMMY_SEPARATOR = "-1";
    public static String DUMMY_TITLE_SEPARATOR = "-2";
    public static String DUMMY_FAVORITE_TITLE_SEPARATOR = "-3";
    public static String DUMMY_RECOMMENDATION_TITLE_SEPARATOR = "-4";
    public static String DUMMY_RECOMMENDATION_BUTTON_SEPARATOR = "-5";

    public static MutableLiveData<ArrayList<Contact>> contactsLiveData = new MutableLiveData<>();

    private static final class LoadContacts extends AsyncTask<Void, Void, ArrayList<Contact>> {

        public ArrayList<Contact> doInBackground(Void... voidArr) {

            if (!PermissonUtil.hasContactPermission()) {
                return null;
            }
            return ContactRepository.INSTANCE.startLoadingContacts();
        }


        public void onPostExecute(ArrayList<Contact> arrayList) {
            if (arrayList != null) {
                ContactRepository.contactsLiveData.setValue(arrayList);
            }
        }
    }

    private static final class SaveContacts extends AsyncTask<ArrayList<ContactDetail>,Void, Boolean > {

        public Boolean doInBackground(ArrayList<ContactDetail>... contactArr) {
            if(FeaturePrefManager.getInstance().hasUploadedContact())
                return true;
            FeaturePrefManager.getInstance().setUploadedContact(true);
            if (!PermissonUtil.hasContactPermission()) {
                return false;
            }
            try{
                List<ContactDetail> contacts = contactArr[0];
                TransactionUtil.saveAllContact(contacts);
            }catch (Exception e){
                e.printStackTrace();
            }
            return true;
        }


        public void onPostExecute(boolean val) {

        }
    }

    static {
        ContactRepository contactRepository = new ContactRepository();
        INSTANCE = contactRepository;
        contactRepository.refreshContactList();
    }

    private ContactRepository() {
    }

    public final MutableLiveData<ArrayList<Contact>> getContactList() {
        return contactsLiveData;
    }

    public final void refreshContactList() {
        new LoadContacts().execute(new Void[0]);
    }

    public final ArrayList<Contact> startLoadingContacts() {
        ArrayList<Contact> contactModelArrayList = new ArrayList<>();
        try {
            Context context = Application.getAppContext();
            List<CustomerEntity> addedCustomer = CustomerRepository.getInstance(context).getCustomersListForContacts(User.getBusinessId());
            Set<String> phoneSet = new HashSet<String>();
            Set<String> phoneSetOriginal = new HashSet<String>();
            if(!ListUtils.isEmpty(addedCustomer)){
                contactModelArrayList.add(new Contact("","","",DUMMY_TITLE_SEPARATOR));
            }
            for(CustomerEntity customerEntity:addedCustomer){
                if(ListUtils.isEmpty(contactModelArrayList)){
                    contactModelArrayList.add(new Contact("","","",DUMMY_TITLE_SEPARATOR));
                }
                Contact contactModel = new Contact(customerEntity.name, customerEntity.phone, customerEntity.image, customerEntity.customerId);
                contactModelArrayList.add(contactModel);
                phoneSet.add(customerEntity.phone);
            }

            contactModelArrayList.add(new Contact("","","",DUMMY_SEPARATOR));
            Cursor phones = context.getContentResolver().query(ContactsContract.CommonDataKinds.Phone.CONTENT_URI, null, null, null, ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC");
            ArrayList<ContactDetail> contactDetailList = new ArrayList<>();
            while (phones != null && phones.moveToNext()) {
                String name = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME));
                String phoneNumber = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER));
                String photoUri = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Photo.PHOTO_URI));
                phoneNumber = Utility.cleanPhonenumber(phoneNumber);
                if(!phoneSet.contains(phoneNumber)) {
                    Contact contactModel = new Contact(name, phoneNumber, photoUri,null);
                    contactModelArrayList.add(contactModel);
                }
                try{
                    if(!phoneSetOriginal.contains(phoneNumber)) {
                        contactDetailList.add(new ContactDetail(phones));
                        phoneSetOriginal.add(phoneNumber);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
            try {
                if (!FeaturePrefManager.getInstance().hasUploadedContact() && AppConfigManager.getInstance().allowContactUpload()) {
//                    AppAnalytics.trackEvent("user_contacts_synched");
//                    new SaveContacts().execute(contactDetailList);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            phones.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return contactModelArrayList;
    }

    private final void sortContactList(List<Contact> list) {
        Collections.sort(list, new ContactComparator());
    }
}
