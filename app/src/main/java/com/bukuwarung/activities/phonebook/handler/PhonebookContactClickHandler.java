package com.bukuwarung.activities.phonebook.handler;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity;
import com.bukuwarung.activities.phonebook.PhonebookAdapter;
import com.bukuwarung.activities.phonebook.PhonebookSearchDialog;
import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.activities.phonebook.model.RowHolder;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.Utility;

import java.util.List;

public final class PhonebookContactClickHandler implements OnClickListener {
    final RowHolder.ContactRowHolder targetRow;
    final PhonebookAdapter phonebookAdapter;
    final Intent oldIntent;
    final PhonebookSearchDialog.OnCustomerSelectedCallback onCustomerSelectedCallback;

    public PhonebookContactClickHandler(PhonebookAdapter phonebookAdapter,
                                        PhonebookSearchDialog.OnCustomerSelectedCallback onCustomerSelectedCallback,
                                        RowHolder.ContactRowHolder contactRowHolder, Intent oldIntent) {
        this.phonebookAdapter = phonebookAdapter;
        this.targetRow = contactRowHolder;
        this.oldIntent = oldIntent;
        this.onCustomerSelectedCallback = onCustomerSelectedCallback;
    }

    private final void alertDuplicateNumber(Activity activity, List<? extends CustomerEntity> list,
                                            String countryCode, String phoneNumber) {
        String message = Utility.getNumberWithCC(countryCode,phoneNumber)+" already exists";
        NotificationUtils.alertToastWithContext(activity,message);
    }

    public final void onClick(View view) {
        final Contact contact = this.targetRow.getContact();
        String countryCode = SessionManager.getInstance().getCountryCode();
        final String phoneNumber = contact.getMobile();

        List<CustomerEntity> customersWithPhone =
                CustomerRepository.getInstance(phonebookAdapter.activity)
                        .getCustomerByNumber(User.getBusinessId(), countryCode.substring(1),
                                Utility.cleanPhonenumber(phoneNumber));

//        if (customersWithPhone != null && !customersWithPhone.isEmpty()) {
//            if (phonebookAdapter.activity != null) {
//                alertDuplicateNumber(phonebookAdapter.activity, customersWithPhone, countryCode, phoneNumber);
//                return;
//            }
//        } else {
            this.onCustomerSelectedCallback.onCustomerSelected(this.targetRow.getContact());
            phonebookAdapter.dlg.dismiss();
//        }
    }
}
