package com.bukuwarung.activities.phonebook.model;


import android.database.Cursor;
import android.provider.ContactsContract;

import com.bukuwarung.session.User;

public class ContactDetail {
    public String mobile;
    public String name;
    public String email;
    public String owner;

    public ContactDetail(Cursor phones){
        this.mobile = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER));
        this.name = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)).trim();
        this.email = phones.getString(phones.getColumnIndex(ContactsContract.CommonDataKinds.Email.DATA));
        this.owner = User.getUserId();
    }
    public ContactDetail(){

    }

    public ContactDetail(String mobile, String name, String email, String owner) {
        this.mobile = mobile;
        this.name = name;
        this.email = email;
        this.owner = owner;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }
}
