package com.bukuwarung.activities.expense

import android.annotation.SuppressLint
import android.app.Activity
import android.app.DatePickerDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.SystemClock
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.view.View.*
import android.view.WindowManager
import android.view.animation.AnimationUtils
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.bulktransaction.data.BulkTransactionData
import com.bukuwarung.activities.bulktransaction.view.BulkTransactionActivity
import com.bukuwarung.activities.categorydetail.tasks.UpdateCashAsyncTask
import com.bukuwarung.activities.expense.adapter.NewCashCategoryAdapter
import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.activities.expense.category.SelectCategory
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity.Companion.TRX_ID_EDIT_PARAM
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity.Companion.getNewIntent
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst.*
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.contact.ui.ContactSearchFragment
import com.bukuwarung.contact.ui.ContactSearchResultsFragment
import com.bukuwarung.contact.ui.UserContactFragment
import com.bukuwarung.contact.ui.UserContactFragment.Companion.TYPE_CREDIT_FROM_TRANSACTION_FLOW
import com.bukuwarung.contact.ui.UserContactFragment.Companion.TYPE_DEBIT_FROM_TRANSACTION_FLOW
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.CashCategoryEntity
import com.bukuwarung.database.entity.CashTransactionEntity
import com.bukuwarung.database.entity.CustomerEntity
import com.bukuwarung.database.entity.TransactionEntityType
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityCashTransactionEntryBinding
import com.bukuwarung.databinding.LayoutProductForReceiptBinding
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.dialogs.categoryselector.CategorySelectorDialog
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog2
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectDialog
import com.bukuwarung.feature.transaction.record.screen.TransactionRecordActivity
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.preference.OnboardingPrefManager.Companion.CATEGORY_SELECT_INFO
import com.bukuwarung.preference.OnboardingPrefManager.Companion.TOUR_CASH_TAB_ADD_BTN_STEP2
import com.bukuwarung.preference.OnboardingPrefManager.Companion.TOUR_CASH_TAB_ADD_BTN_STEP3
import com.bukuwarung.preference.OnboardingPrefManager.Companion.TUTOR_PRODUCT_DETAIL
import com.bukuwarung.preference.OnboardingPrefManager.Companion.TUTOR_PRODUCT_DETAIL_WITH_INVENTORY
import com.bukuwarung.preference.OnboardingPrefManager.Companion.TUTOR_SAVE_TRX
import com.bukuwarung.preference.OnboardingPrefManager.Companion.getInstance
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.tutor.view.OnboardingWidget.Companion.createInstance
import com.bukuwarung.utils.*
import com.bukuwarung.utils.RemoteConfigUtils.NewHomePage.shouldShowNewHomePage
import com.bukuwarung.utils.RemoteConfigUtils.getCategoryUIVariant
import com.bukuwarung.utils.RemoteConfigUtils.isUpaidTransactionSupported
import com.bukuwarung.utils.RemoteConfigUtils.showNewTransactionCategory
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import kotlinx.android.synthetic.main.activity_cash_transaction_entry.*
import kotlinx.android.synthetic.main.activity_cash_transaction_entry.view.*
import java.io.File
import java.util.*
import javax.inject.Inject

class NewCashTransactionActivity : BaseActivity(), OnboardingWidget.OnboardingWidgetListener,
    ContactSearchResultsFragment.OnCustomerSelectedCallback,
    ContactPermissionBottomSheetDialog.ContactPermissionSheetListener,
    ContactSearchFragment.OnCustomerNameEntered {
    private var editedCategory: Category? =null
    lateinit var binding: ActivityCashTransactionEntryBinding
    private var userContactFragment: UserContactFragment? = null
    private var customerNameEntered: String = ""
    private var isExpense = false
    private var transactionType = 0
    private var isFormVisible = false
    private var tempOnboardingVisibility = true
    private var transactionEntity: CashTransactionEntity? = null
    private var isModalFocus = false
    private var lastButtonSaveClicked: Long = 0
    private var customerPhoneNumber: String = ""
    private var type = 0
    private var harga = 0.0
    private var notes = ""
    private var date = ""
    private var credit = 0.0
    private var sales = 0.0
    private var areProductsSelected: Boolean = false
    private var from: String? = null

    // for analytics
    private var sumOfItemTotal = 0.0
    private var totalPenjualan = 0.0
    private var productCount = 0
    private var profilePicUri: Uri? = null
    private var profilePicFile: File? = null
    private var attachmentUri: String? = null
    private var imageSrc: String? = null
    private val takePhoto = "take_photo"
    private val upload = "upload"
    private var showMandatoryTransactionCategory: Boolean = false
    private var isDefaultPengeluaranStock: Boolean = false
    private var isCategorySelected = false

    private var categoryAdapter: NewCashCategoryAdapter? = null
    private var topCategories : List<CashCategoryEntity>?=null
    var categoriesToDisplay: MutableList<com.bukuwarung.activities.expense.data.Category>?=
        mutableListOf()
    var categorySelected: String? = null


    companion object {
        const val TRX_TYPE = "TRX_TYPE"
        const val SHOW_INTRO = "show_intro"
        const val SHOW_BUSINESS_DASHBOARD = "show_business_dashboard"
        const val FROM_DAILY_BUSINESS_UPDATE = "from_dbu"
        private const val PREV_TRX_TYPE = "prev_trx_type"
        const val CATEGORY = "category"
        private const val PRODUCT_LIST_REQUEST_CODE = 97
        private const val RC_SELECT_BANK_ACCOUNT = 98
        private const val SELECT_CATEGORY = 100

        enum class TransactionStatus {
            MODE_FULLY_PAID,
            MODE_FULLY_UNPAID
        }

        @JvmStatic
        fun createIntent(context: Context?): Intent {
            return createIntent(
                context,
                showIntro = false,
                isExpense = false,
                from_daily_business_update = false,
            )
        }

        fun createIntent(
            context: Context?,
            showIntro: Boolean = false,
            isExpense: Boolean,
            from_daily_business_update: Boolean = false
        ): Intent {
            val intent: Intent =
                if (!RemoteConfigUtils.isNewUITrxFormEnabled())
                    Intent(context, NewCashTransactionActivity::class.java)
                else
                    Intent(context, TransactionRecordActivity::class.java)
            intent.putExtra(SHOW_INTRO, showIntro)
            intent.putExtra(FROM_DAILY_BUSINESS_UPDATE, from_daily_business_update)
            intent.putExtra(TRX_TYPE, isExpense)
            return intent
        }
    }

    private var currentMode = TransactionStatus.MODE_FULLY_PAID

    @Inject
    lateinit var viewModel: CashTransactionViewModel

    private var appLanguage = -1
    private var allowShownSuccessTutorial = false
    private var showInformasiOptional = GONE

    private var customerEntity: CustomerEntity? = null
    private var customerContact: Contact? = null

    //product fields
    var cashTransactionId: String? = null
    var status: Int = -1
    var categorySelectedStatus: Int = -1
    var fromDailyBusinessUpdate: Boolean = false
    var customerName: String? = null
    var customerId: String? = null
    var cashCategoryEntity: CashCategoryEntity? = null
    private var onboardingWidget: OnboardingWidget? = null
    var isEdit: Boolean = false
    private var sendSms = false
    private var shouldShowExitDialog = false
    var categories: String? = null
    var trxCategory: String? = null


    private val showTutorial by lazy { intent.getBooleanExtra("show_intro", false) }

    override fun setViewBinding() {
        shouldShowExitDialog = RemoteConfigUtils.shouldShowExitDialog()

        showMandatoryTransactionCategory = RemoteConfigUtils.showMandatoryTransactionCategory()

        isDefaultPengeluaranStock = RemoteConfigUtils.isDefaultPengeluaranStock()

        binding = ActivityCashTransactionEntryBinding.inflate(layoutInflater)

        setContentView(binding.root)

        binding.contactSearchContainerNonEdit.root.setOnClickListener {
            // showContactFragment(currentMode)
            addUserFragment()
        }
        binding.contactSearchContainerNonEdit.searchInput.setOnClickListener {
            addUserFragment()
        }
        binding.paidContactSearchContainerNonEdit.searchInput.setOnClickListener {
            addUserFragment()
        }

        binding.paidContactSearchContainerNonEdit.root.setOnClickListener {
            addUserFragment()
        }
        binding.contactSearchContainerNonEdit.addContactParentLayout.setOnClickListener {
            requestContactPermission()
        }
        binding.paidContactSearchContainerNonEdit.addContactParentLayout.setOnClickListener {
            requestContactPermission()
        }
        if (PermissonUtil.hasContactPermission()) {
            binding.contactSearchContainerNonEdit.addContactParentLayout.visibility = GONE
            binding.paidContactSearchContainerNonEdit.addContactParentLayout.visibility = GONE
        }
        binding.notPaidOffSendCustomerSms.setOnCheckedChangeListener { _, isChecked ->
            sendSms = if (isChecked) {
                binding.notPaidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_checked)
                true
            } else {
                binding.notPaidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_not_checked)
                false
            }
        }

        if (showMandatoryTransactionCategory) {
            binding.categoryEt.hideView()
            // binding.categoryEtNew.showView()
            binding.gpProduct.hideView()
            binding.productHeaderLayout.root.hideView()
            binding.btnSave.isEnabled = false
            binding.rgTrxType.requestFocus()
            hideKeyboard()
        } else {
            binding.categoryEt.showView()
            // binding.categoryEtNew.hideView()
            binding.gpProduct.showView()
            binding.productHeaderLayout.root.hideView()
            binding.btnSave.isEnabled = true
        }

        binding.paidOffSendCustomerSms.setOnCheckedChangeListener { _, isChecked ->
            sendSms = if (isChecked) {
                binding.paidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_checked)
                true
            } else {
                binding.paidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_not_checked)
                false
            }
        }


    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) { it ->
            when (it) {
                is CashTransactionViewModel.State.GetLanguage -> appLanguage = it.language
                is CashTransactionViewModel.State.ShowCategoryDialog -> showCategoryDialog(
                    it.categoryList,
                    it.trxType,
                    it.selectedCategory
                )
                is CashTransactionViewModel.State.UpdateUi -> {
                    (it.trxType !=1).also { isExpense = it }
                    updateUi(it.isExpense)
                }
                is CashTransactionViewModel.State.InitKeyboardView -> initKeyboardView(it.cashTransactionEntity)
                is CashTransactionViewModel.State.SetTransactionEntityData -> setTransactionEntityData(
                    it.cashTransactionEntity,
                    it.selectedCategory
                )
                is CashTransactionViewModel.State.SetCategoryData -> setCategoryData(it.selectedCategory)
                is CashTransactionViewModel.State.SetCategoryByFrequency -> setCategoryByFrequency(it.topfiveCategory!!)
                is CashTransactionViewModel.State.ShowProductDialog -> openProductList(it.existingProducts)
                is CashTransactionViewModel.State.SetSelectedProduct -> setSelectedProduct(
                    it.products,
                    false
                )
                is CashTransactionViewModel.State.OnAddCashFinished -> onAddCashFinished(
                    it.cashTrxId,
                    it.showMessage,
                    it.status
                )
                is CashTransactionViewModel.State.ShowDatePicker -> showTransactionDatePicker(it.transactionDate)
                is CashTransactionViewModel.State.StartMainActivity -> {
                    SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_SAVED_CASH, this)
                    MainActivity.startNewTaskActivity(this)
                    finish()
                }
                CashTransactionViewModel.State.ShowModalTutorial -> {}
                is CashTransactionViewModel.State.ShowProductDetailTutorial -> {
                    val widgetId =
                        if (it.isInventoryEnabled) TUTOR_PRODUCT_DETAIL_WITH_INVENTORY else TUTOR_PRODUCT_DETAIL
                    val anchorId =
                        if (it.isInventoryEnabled) binding.productEt else binding.viewBarangBg
                    val buttonTextId =
                        if (it.isInventoryEnabled) R.string.next else R.string.try_feature
                    val bodyTextId =
                        if (it.isInventoryEnabled) R.string.onboarding_product_subtitle_inventory_enabled else R.string.onboarding_product_subtitle

                    onboardingWidget = OnboardingWidget.createInstance(
                        this,
                        this,
                        widgetId,
                        anchorId,
                        R.drawable.onboarding_announce,
                        getString(R.string.new_feature),
                        getString(bodyTextId),
                        getString(buttonTextId),
                        FocusGravity.CENTER,
                        ShapeType.RECTANGLE_FULL,
                        1,
                        1,
                        true
                    )
                }
                is CashTransactionViewModel.State.SetFieldsState -> {
                    /*  binding.trxDetailGroup.visibility = it.showDetailFields.asVisibility()
                      binding.productEt.visibility = it.showProductField.asVisibility()
                      binding.productFeatureLabel.visibility = (it.showLabel && it.showProductField).asVisibility()*/
                }
                is CashTransactionViewModel.State.ShowSelectedCustomer -> {
                    customerEntity = it.customerEntity
                    customerContact = it.contact

                    val customerName =
                        it.customerEntity?.name.takeIf { name -> name.isNotNullOrBlank() }
                            ?: it.contact?.name ?: ""

                    binding.contactSearchContainerNonEdit.searchInput.setText(customerName)
                    binding.paidContactSearchContainerNonEdit.searchInput.setText(customerName)
                    if (!Utility.isBlank(customerName) && binding.rbPaid.isChecked) {
                        val hideContact = RemoteConfigUtils.hideContact()
                        val count =
                            TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords
                        if (count < 1 && hideContact) {
                            binding.paidContactSearchContainerNonEdit.root.visibility = GONE;
                        } else {
                            binding.paidContactSearchContainerNonEdit.root.visibility = VISIBLE;
                        }
                    }
                }
                CashTransactionViewModel.State.InventoryFeatureEnabled -> {/*setLayoutForInventory()*/
                }
                is CashTransactionViewModel.State.SetExistingTrxStatus -> {
                    binding.rgStatus.check(if (it.isPaid) binding.rbPaid.id else binding.rbUnpaid.id)

                }

                is CashTransactionViewModel.State.SelectedUrlForAttachment -> {
                    attachmentUri = it.firebaseUrl
                    binding.btnSave.isEnabled = true
                }

                is CashTransactionViewModel.State.ShowCategorySelectionOption -> {
                    val intent = Intent(this, SelectCategory::class.java)
                    intent.putExtra(SelectCategory.TRANSACTION_TYPE, it.trxType)
                    intent.putExtra(SelectCategory.TRANSACTION_CATEGORY, it.trxCategory)
                    startActivityForResult(intent, SELECT_CATEGORY)
                }

                CashTransactionViewModel.State.OnDeleteAttachment -> {
                    attachmentUri = null
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (Utility.hasInternet()) {
            binding.tvOffline.visibility = View.GONE
        } else {
            binding.tvOffline.visibility = View.VISIBLE
        }

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

        val prop = PropBuilder()
        var display = "grid"
        if (getCategoryUIVariant() == 2) {
            display = "list"
        }
        prop.put(AnalyticsConst.CATEGORY_PAGE_VIEW_VARIANT, display)
        prop.put(AnalyticsConst.NEW_UX_CATEGORY_SELECTION_VARIANT, RemoteConfigUtils.showNewTransactionCategory())
        prop.put("UI_form", "old_UI_Pre_june2022")
        prop.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsTransaction())
        prop.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, prop)

    }

    private fun onAddCashFinished(cashTrxId: String?, showMessage: Boolean, status: Int) {
        val intent = Intent().apply {
            putExtra(TRX_ID_EDIT_PARAM, cashTrxId)
            putExtra("isExpense", isExpense)
        }
        setResult(Activity.RESULT_OK, intent)

        if (cashTransactionId == null) { // create, not edit
            val txnCount =
                if (SessionManager.getInstance().isGuestUser) TransactionRepository.getInstance(
                    Application.getAppContext()
                ).countCashTransactionsWithDeletedForGuest() else TransactionRepository.getInstance(
                    Application.getAppContext()
                ).countCashTransactionsWithDeleted()
            if (RemoteConfigUtils.shouldShowCashSuccessAnimation()) {
                binding.btnSave.isEnabled = false
                binding.tvTrxSuccess.text = RemoteConfigUtils.getTrxSuccessMessage()
                binding.lavSuccess.showForOnce(binding.successView, 75) {
                    cashTrxId?.let { showTransactionReceipt(it, txnCount, status) }
                }
            } else {
                cashTrxId?.let { showTransactionReceipt(it, txnCount, status) }
            }
        } else {
            finish()
        }
    }

    private fun showTransactionReceipt(cashTrxId: String?, txnCount: Int, status: Int) {
        if (txnCount >= RemoteConfigUtils.getForcedInvoiceVisibilityThreshold() || (txnCount >= RemoteConfigUtils.getInvoiceVisibilityThreshold() && (txnCount - FeaturePrefManager.getInstance().invoiceUseCount) < RemoteConfigUtils.getAllowedIgnoreRecieptCount()) || FeaturePrefManager.getInstance()
                .isInvoicePowerUser()
        ) {
            if (cashTrxId != null) {
                if (!FeaturePrefManager.getInstance().isInvoicePowerUser && FeaturePrefManager.getInstance().invoiceUseCount > RemoteConfigUtils.getInvoicePowerUserTxnCriteria()) {
                    FeaturePrefManager.getInstance().isInvoicePowerUser = true
                }
                val intent = getNewIntent(this, cashTrxId)
                intent.putExtra("isRedirectedFromCashTransaction", true)
                intent.putExtra(
                    CashTransactionDetailActivity.IS_EXPENSE_PARAM,
                    AppConfigManager.getInstance().getTransactionType()
                )
                intent.putExtra(CashTransactionDetailActivity.TRX_STATUS_PARAM, status)
                startActivity(intent)
                finish()
            }
        } else {
            MainActivity.startActivitySingleTopToTab(this, TabName.TRANSACTION)
        }
    }

    private fun openProductList(existingProducts: List<TransactionItemDto>) {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAIL_OPEN)

        val productIntent = ProductListActivity.createIntent(this).apply {
            putExtra(ProductListActivity.EXISTING_PRODUCTS, existingProducts.toTypedArray())
            putExtra(ProductListActivity.IS_EDIT, cashTransactionId != null)
            putExtra(ProductListActivity.IS_EXPENSE, isExpense)
        }

        startActivityForResult(productIntent, PRODUCT_LIST_REQUEST_CODE)
    }

    private fun showCategoryDialog(
        categoryList: MutableList<Category>,
        transactionType: Int,
        selectedCategory: Category?
    ) {
        val categoryDialog = CategorySelectorDialog.getInstance(
            this,
            categoryList,
            transactionType
        ) { category: Category, dialog: SelectableObjectDialog<*> ->
            setSelectedCategory(category)
            binding.noteEt.requestFocus()
            if (binding.noteEt.text != null && binding.noteEt.text.toString().isNotEmpty()) {
                binding.noteEt.setSelection(binding.noteEt.text.length)
            }
            dialog.dismiss()
        }
        if (selectedCategory != null) categoryDialog.selectedName = selectedCategory.name
        categoryDialog.show()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun setupView() {
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)

        val id = intent.getStringExtra("cashTransactionId")
            val trxType = intent.getStringExtra(CategoryTransactionsActivity.TRANS_TYPE_EXTRA)
        val categoryId = intent.getStringExtra(CategoryTransactionsActivity.CASH_CATEGORY_ID)
        viewModel.onEventReceived(
            CashTransactionViewModel.Event.OnCreateView(
                id,
                trxType,
                categoryId
            )
        )
        status = intent.getIntExtra("status", -1)
        fromDailyBusinessUpdate = intent.getBooleanExtra(FROM_DAILY_BUSINESS_UPDATE, false)
        customerName = intent.getStringExtra("customerName")
        customerId = intent.getStringExtra("customerId")
        if (intent.hasExtra("isEdit")) {
            isEdit = intent.getBooleanExtra("isEdit", false)
        }
        cashTransactionId = id
        if (Utility.isBlank(cashTransactionId)) {
            FeaturePrefManager.getInstance().exitWithoutTransaction(CASH_TRANSACTION)
        }
        changeStatusVisibility(true)
        isExpense = AppConfigManager.getInstance().transactionType
            binding.rgTrxType.check(if (isExpense) binding.rbExpense.id else binding.rbSelling.id)


        val bulkTransaksiThreshold = RemoteConfigUtils.getBulkTransaksiThreshold()

        val showAllWhite = RemoteConfigUtils.showAllFieldsWithWhite()

        val count = TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords

        if (showMandatoryTransactionCategory) {
            if (binding.rbExpense.isChecked) {
                if (isDefaultPengeluaranStock) {

//                    categoryAdapter?.setNewCashCategoryName(TransactionCategory.PEMBELIAN_STOCK.category)
//                    viewModel.onCategoryChosen(Category(TransactionCategory.PEMBELIAN_STOCK.category,
//                        TransactionCategory.PEMBELIAN_STOCK.category,TransactionCategory.PEMBELIAN_STOCK.category))

//                    binding.categoryEtNew.setText(TransactionCategory.PEMBELIAN_STOCK.category)
                    enableProductGroup()
                } else {
//                    binding.categoryEtNew.setText("")
                    disableProductGroup()
                }
            } else {
                binding.categoryEtNew.setText(TransactionCategory.PENJUALAN.category)
                categoryAdapter?.setNewCashCategoryName(TransactionCategory.PENJUALAN.category)
                viewModel.onCategoryChosen(Category(TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category))
                enableProductGroup()
            }
            checkForSaveButton()
        }

        if(RemoteConfigUtils.showNewTransactionCategory()==2)
        {
            //newView
            binding.parentCategory.category_et_new.hideView()
            binding.parentCategory.type_divider.hideView()
            binding.parentCategory.ch_roof_category.showView()
            binding.chRoofCategory.editSelectLayout.hideView()
            binding.chRoofCategory.chCategorySelect.showView()

        }else  if(RemoteConfigUtils.showNewTransactionCategory()==1) {
            binding.parentCategory.category_et_new.hideView()
            binding.parentCategory.type_divider.hideView()
            binding.parentCategory.ch_roof_category.showView()
            binding.chRoofCategory.editSelectLayout.showView()
            binding.chRoofCategory.chCategorySelect.hideView()
            //oldView
        }else
        {
            binding.parentCategory.category_et_new.showView()
            binding.parentCategory.type_divider.showView()
            binding.parentCategory.ch_roof_category.hideView()
        }
        binding.gpLunas.showView()

        if (showMandatoryTransactionCategory && !FeaturePrefManager.getInstance().isTooltipForMandatoryTransactionCategorySeen) {
            TooltipBuilder.builder(this)
                .setText(getString(R.string.mandatory_category))
                .setAnchor(binding.vwCategoryNew)
                .build()
                .show()

            FeaturePrefManager.getInstance().setTooltipForMandatoryTransactionCategorySeen()
        }

        try {
            binding.balance.requestFocus()
        } catch (e: Exception) {
            e.printStackTrace()
        }

        binding.nomimalOthers.setBackgroundColor(
            if (showAllWhite) ContextCompat.getColor(this, R.color.white) else
                ContextCompat.getColor(this, R.color.black_5)
        )

        binding.nominalsBackground.setBackgroundColor(
            if (showAllWhite) ContextCompat.getColor(this, R.color.blue_background) else
                ContextCompat.getColor(this, R.color.white)
        )

        binding.currencySymbol.text = Utility.getCurrency()
        binding.currencySymbolModal.text = Utility.getCurrency()
        val storableDateString = Utility.getStorableDateString(Date())
        viewModel.onEventReceived(
            CashTransactionViewModel.Event.OnTransactionDateChanged(
                storableDateString
            )
        )
        binding.tvDate.text = Utility.getReadableDateString(storableDateString)
        date = storableDateString
        binding.balance.doAfterTextChanged { calculateModal() }
        binding.balanceModal.doAfterTextChanged { calculateModal() }
        binding.btnAddProduct.setOnClickListener {
            openProductList()
        }
        binding.btnSave.setSingleClickListener {
            onSaveButtonClicked()
        }
        binding.closeBtn.setOnClickListener {
            onBackPressed()
        }


        binding.rgTrxType.setOnCheckedChangeListener { _, checkedId ->
            isExpense = checkedId == binding.rbExpense.id
            if (showMandatoryTransactionCategory) {
                if (isExpense) {
                    transactionType = -1
                    if (isDefaultPengeluaranStock) {

                        categoryAdapter?.setNewCashCategoryName(TransactionCategory.PEMBELIAN_STOCK.category)
                        viewModel.onCategoryChosen(Category(TransactionCategory.PEMBELIAN_STOCK.category,
                            TransactionCategory.PEMBELIAN_STOCK.category,TransactionCategory.PEMBELIAN_STOCK.category))

//                        binding.categoryEtNew.setText(TransactionCategory.PEMBELIAN_STOCK.category)
                        enableProductGroup()
                    } else {
//                        binding.categoryEtNew.setText("")
                        disableProductGroup()
                    }
                }
                else {
                    transactionType = 1
                    binding.categoryEtNew.setText(TransactionCategory.PENJUALAN.category)
                    categoryAdapter?.setNewCashCategoryName(TransactionCategory.PENJUALAN.category)
                    viewModel.onCategoryChosen(Category(TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category))
                    enableProductGroup()
                }

                checkForSaveButton()
            }
            binding.gpLunas.showView()
            updateUi(isExpense)
        }
        updateUi(isExpense)

        viewModel.onEventReceived(CashTransactionViewModel.Event.OnTrxTypeChange(if (isExpense) DEBIT else CREDIT))

        binding.tvDate.setOnClickListener {
            closeCalculatorKeyboard()
            viewModel.onEventReceived(CashTransactionViewModel.Event.OnDateClicked)
        }

        val areNewCategoriesVisible = RemoteConfigUtils.SelectCategory.areNewCategoriesVisible()

        binding.categoryEt.setOnClickListener {
            closeCalculatorKeyboard()
//            viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategoryLayoutClicked)
            if (areNewCategoriesVisible) {
                viewModel.onEventReceived(
                    CashTransactionViewModel.Event.OnNewCategoryClicked(
                        binding.categoryEt.text.toString()
                    )
                )
            } else {
                viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategoryLayoutClicked)
            }
        }

        binding.chRoofCategory.chCategorySelect.setOnClickListener {
            closeCalculatorKeyboard()
            if (areNewCategoriesVisible) {
                if (showMandatoryTransactionCategory) {
                    viewModel.onEventReceived(
                        CashTransactionViewModel.Event.OnNewCategoryClicked(
                            binding.categoryEtNew.text.toString()
                        )
                    )
                } else {
                    viewModel.onEventReceived(
                        CashTransactionViewModel.Event.OnNewCategoryClicked(
                            binding.categoryEtNew.text.toString()
                        )
                    )

                }

            } else {
                viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategoryLayoutClicked)
            }
        }
        binding.chRoofCategory.tvSelectCategory.setOnClickListener {
            closeCalculatorKeyboard()
            if (areNewCategoriesVisible) {
                if (showMandatoryTransactionCategory) {
                    categorySelected = binding.categoryEtNew.text.toString()
                    viewModel.onEventReceived(
                        CashTransactionViewModel.Event.OnNewCategoryClicked(
                            binding.categoryEtNew.text.toString()
                        )
                    )
                } else {
                    categorySelected = binding.categoryEtNew.text.toString()
                    viewModel.onEventReceived(
                        CashTransactionViewModel.Event.OnNewCategoryClicked(
                            binding.categoryEtNew.text.toString()
                        )
                    )

                }

            } else {
                viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategoryLayoutClicked)
            }
        }
        binding.categoryEtNew.setOnClickListener {
            closeCalculatorKeyboard()
//            viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategoryLayoutClicked)
            if (areNewCategoriesVisible) {
                categorySelected = binding.categoryEtNew.text.toString()
                if (showMandatoryTransactionCategory) {
                    viewModel.onEventReceived(
                        CashTransactionViewModel.Event.OnNewCategoryClicked(
                            binding.categoryEtNew.text.toString()
                        )
                    )
                } else {
                    viewModel.onEventReceived(
                        CashTransactionViewModel.Event.OnNewCategoryClicked(
                            binding.categoryEt.text.toString()
                        )
                    )

                }

            } else {
                viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategoryLayoutClicked)
            }
        }

        if (showTutorial) {
            hideKeyboard()
            if (!getInstance().getHasFinishedForId(TOUR_CASH_TAB_ADD_BTN_STEP2)) {
                if (!isExpense) {
                    onboardingWidget = OnboardingWidget.createInstance(
                        this,
                        this,
                        OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN_STEP2,
                        binding.balanceClickView,
                        R.drawable.onboarding_attention,
                        "",
                        getString(R.string.cash_transaction_intro_amount),
                        "",
                        FocusGravity.CENTER,
                        ShapeType.RECTANGLE_FULL,
                        2,
                        3,
                        true
                    )
                }
            }

        }

        binding.rgStatus.setOnCheckedChangeListener { _, radioId ->
            if (binding.categoryEtNew.text.toString().equals("Pinjaman", false)) {
                Toast.makeText(this, R.string.status_change_warning, Toast.LENGTH_LONG).show()
                binding.rbUnpaid.isChecked = true
                binding.rbPaid.isChecked = false
                return@setOnCheckedChangeListener
            }
            if (binding.categoryEtNew.text.toString().equals("Piutang", false)) {
                Toast.makeText(this, R.string.status_change_warning, Toast.LENGTH_LONG).show()
                binding.rbUnpaid.isChecked = false
                binding.rbPaid.isChecked = true
                return@setOnCheckedChangeListener
            }
//            if (binding.categoryEtNew.text.toString().equals("Pembayaran utang", false)) {
//                Toast.makeText(this, R.string.status_change_warning, Toast.LENGTH_LONG).show()
//                binding.rbUnpaid.isChecked = false
//                binding.rbPaid.isChecked = true
//                return@setOnCheckedChangeListener
//            }
            if (binding.categoryEtNew.text.toString().equals("Pemberian utang", false)
                && radioId == R.id.rb_unpaid) {
                Toast.makeText(this, R.string.status_change_warning, Toast.LENGTH_LONG).show()
                binding.rbUnpaid.isChecked = false
                binding.rbPaid.isChecked = true
                return@setOnCheckedChangeListener
            }


            when (radioId) {
                binding.rbPaid.id -> {
                    if (binding.categoryEtNew.text.toString()
                            .equals(TransactionCategory.PEMBERIAN_UTANG.category, true)
                    ) {
                        return@setOnCheckedChangeListener
                    }
                    showPaidTransaction()
                    binding.noteEt.clearFocus()
                    setSmsCheckBox()
                }
                binding.rbUnpaid.id -> {
                    showUnpaidTransaction()
                    binding.noteEt.clearFocus()
                    if (binding.categoryEtNew.text.toString()
                            .equals(TransactionCategory.PENANBAHAN_MODAL.category, true)
                    ) {
                        categorySelectedStatus = 2
                    }
                    setSmsCheckBox()
                }
            }
        }

        if (FeaturePrefManager.getInstance().informationOptionalVisibility || cashTransactionId != null)
            showInformasiOptional = VISIBLE
        else {
            val count = TransactionRepository.getInstance(Application.getAppContext())
                .countCashTransactionsWithDeletedHavingNotesOrCategory()
            if (count >= AppConfigManager.getInstance().getinformasiOptionalCount()) {
                FeaturePrefManager.getInstance().informationOptionalVisibility = true
                showInformasiOptional = VISIBLE
            }
        }

        binding.tvOptionalInfo.setOnClickListener {
            binding.incCameraView.cvImagePreview.hideView()
            val shouldShowTransksiImage = RemoteConfigUtils.showTransksiImage()

            binding.incCameraView.apply {
                if (shouldShowTransksiImage && cashTransactionId.isNullOrEmpty()) {
                    cvCameraInput.showView()
                } else {
                    cvCameraInput.hideView()
                }
            }
            val hasShownCategoryCoachmark: Boolean = getInstance()
                .getHasFinishedForId(OnboardingPrefManager.CATEGORY_SELECT_INFO)
            if (!hasShownCategoryCoachmark) {
                createInstance(
                    this,
                    this,
                    OnboardingPrefManager.CATEGORY_SELECT_INFO,
                    binding.categoryEt,
                    R.drawable.onboarding_announce,
                    getString(R.string.category_feature),
                    getString(R.string.category_feature_info),
                    getString(R.string.understand),
                    FocusGravity.CENTER,
                    ShapeType.RECTANGLE_FULL,
                    0,
                    0,
                    true,
                    false
                )
            }

            binding.otherFieldsGroup.reverseVisibility()
            if (!showMandatoryTransactionCategory) {
                binding.categoryEt.reverseVisibility()
            }
            changeVisibilityOfInformasiOptional(binding.otherFieldsGroup.visibility)
            val propBuilder = PropBuilder()
            if (binding.otherFieldsGroup.visibility == VISIBLE)
                propBuilder.put(
                    AnalyticsConst.PROPERTY_INFORMATION_OPTIONAL,
                    AnalyticsConst.INFORMATION_OPTIONAL_OPEN_STATE
                )
            else
                propBuilder.put(
                    AnalyticsConst.PROPERTY_INFORMATION_OPTIONAL,
                    AnalyticsConst.INFORMATION_OPTIONAL_CLOSE_STATE
                )

            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INFORMATION_OPTIONAL_CLICK, propBuilder,false,false,false)
        }

        binding.incCameraView.clCameraInput.setOnClickListener {
            if (Utility.hasInternet()) {
                binding.btnSave.isEnabled = false
                showImageSelectorDialog()
            } else {
                CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    this,
                    supportFragmentManager
                )
            }
        }

        binding.incCameraView.btnDelete.setOnClickListener {
            binding.incCameraView.apply {
                viewModel.onEventReceived(
                    CashTransactionViewModel.Event.OnDeleteAttachment(
                        attachmentUri
                    )
                )
                cvCameraInput.showView()
                cvImagePreview.hideView()
            }
        }

        binding.form.setOnTouchListener { _, _ ->
            binding.productScrollview.parent.requestDisallowInterceptTouchEvent(false)
            false
        }

        binding.productScrollview.setOnTouchListener { v, _ ->
            v.parent.requestDisallowInterceptTouchEvent(true)
            false
        }

        val transactionCount = TransactionRepository.getInstance(this)
            .countAllCashTransWithDeleted(User.getBusinessId())

        val isBulkTransaksiEnabled =
            RemoteConfigUtils.shouldShowBulkTransaksi() && (transactionCount >= bulkTransaksiThreshold) && !SessionManager.getInstance().isGuestUser

        val showBulkTransaksi = RemoteConfigUtils.shouldShowBulkTransaksi()

        val bulkTransaksiOnOld = binding.btnBulkTransaksi

        ComponentUtil.setVisible(bulkTransaksiOnOld, isBulkTransaksiEnabled)

        bulkTransaksiOnOld.setOnClickListener {
            val intent = Intent(this, BulkTransactionActivity::class.java)
            var sales = 0.0
            if (isExpense) {
                sales = Utility.extractAmountFromText(binding.balance.text.toString())
                credit = 0.0
                harga = 0.0
            } else {
                sales = 0.0
                credit = Utility.extractAmountFromText(binding.balance.text.toString())
                harga = Utility.extractAmountFromText(binding.balanceModal.text.toString())
            }

            val data = BulkTransactionData(type, date, credit, harga, sales, notes)
            intent.putExtra("bulkTransactionData", data)
            startActivity(intent)
        }
    }

    private fun showImageSelectorDialog() {
        val imageSelectorDialog = ImageSelectorDialog2(this, {
            setImageSourceFromCamera()
        }, {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                type = "image/*"
                addCategory(Intent.CATEGORY_OPENABLE)
            }
            startActivityForResult(
                Intent.createChooser(
                    intent,
                    getString(R.string.select_image_instruction)
                ), PermissionConst.REQ_PICK_IMAGE_PERMISSON
            )
        })
        imageSelectorDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_PROFILE_PIC)
        imageSelectorDialog.show()

        imageSelectorDialog.setOnDismissListener {
            binding.btnSave.isEnabled = true
        }
    }

    private fun setImageSourceFromCamera() {
        try {
            profilePicFile = ImageUtils.createTempFile()
            profilePicUri = ImageUtils.getUriFromFile(profilePicFile)
            val intent = Intent("android.media.action.IMAGE_CAPTURE")
            intent.putExtra("output", profilePicUri)
            intent.addFlags(Intent.FLAG_EXCLUDE_STOPPED_PACKAGES)
            if (Build.VERSION.SDK_INT >= 23 && this.checkSelfPermission(android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    PermissionConst.CAMER_AND_STORAGE,
                    PermissionConst.REQ_TAKE_PICTURE_PERMISSON
                )
            }
            if (intent.resolveActivity(this.packageManager) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun changeVisibilityOfInformasiOptional(visibility: Int) {
        setHeaderMenuAttribute(binding.tvOptionalInfo, visibility)
        setSmsCheckBox()
        if (binding.rbPaid.isChecked)
            showPaidTransaction()
        else
            showUnpaidTransaction()
    }

    private fun openProductList() {
        viewModel.onEventReceived(CashTransactionViewModel.Event.OnProductClicked)
    }

    private fun onSaveButtonClicked() {

//        if (checkForLowMemory(this).lowMemory) {
//            Toast.makeText(this, "Rendah pada memori, tidak dapat menyimpan transaksi", Toast.LENGTH_LONG).show()
//            return
//        }
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600)
            return
        lastButtonSaveClicked = SystemClock.elapsedRealtime()

        InputUtils.hideKeyBoardWithCheck(this)

        val balance = binding.balance.text.toString()
        val balanceModal =
            if (binding.rgTrxType.checkedRadioButtonId == binding.rbSelling.id) binding.balanceModal.text.toString() else ""
        AppConfigManager.getInstance().transactionType =
            binding.rgTrxType.checkedRadioButtonId != binding.rbSelling.id
        val note = binding.noteEt.text.toString()
        val status = if (binding.rbPaid.isChecked) 1 else 0
        transactionType = if (binding.rbExpense.isChecked) -1 else 1
        if (status == 0 || categorySelectedStatus > 0) {
            if (binding.contactSearchContainerNonEdit.searchInput.text.isNullOrEmpty()) {
                GenericConfirmationDialog.create(this) {
                    titleRes = R.string.add_contact_confirmation_title
                    bodyRes = R.string.add_contact_confirmation_body
                    btnLeftRes = R.string.cancel
                    btnRightRes = R.string.add_contact_confirm
                    rightBtnCallback = {
                        if (PermissonUtil.hasContactPermission()) {
                            addUserFragment()
                        } else {
                            requestContactPermission()
                        }
                    }
                }.show()
            } else {
                viewModel.onEventReceived(
                    CashTransactionViewModel.Event.SaveCustomerTransaction(
                        balance,
                        balanceModal,
                        note,
                        status,
                        categorySelectedStatus,
                        sendSms,
                        attachmentUri,
                        imageSrc,
                        fromDailyBusinessUpdate,
                        from,
                        categorySelected,
                        transactionType
                    )
                )
            }
        } else {
            viewModel.onEventReceived(
                CashTransactionViewModel.Event.SaveCustomerTransaction(
                    balance,
                    balanceModal,
                    note,
                    status,
                    categorySelectedStatus,
                    sendSms,
                    attachmentUri,
                    imageSrc,
                    fromDailyBusinessUpdate,
                    from,
                    categorySelected,
                    transactionType
                )
            )
        }

        if (isEdit) {
            var actionBy = AnalyticsConst.ACCOUNTING
            val type = transactionEntity?.transactionType
            if (type == TransactionEntityType.PAYMENT) {
                actionBy = AnalyticsConst.PAYMENTS
            }
        }
    }

    private fun calculateModal() {
        val cleanBalance: String
        val cleanModal: String
        try {
            // before was 22.000,90
            // after should be 22000.90
            var balanceText = binding.balance.text.toString()
            var modalText = binding.balanceModal.text.toString()
            if (Utility.isBlank(balanceText)) balanceText = "0"
            if (Utility.isBlank(modalText)) modalText = "0"
            if (SessionManager.getInstance().countryCode == "+62" || SessionManager.getInstance().countryCode == "62") {
                cleanBalance = balanceText.replace(".", "")
                    .replace(",", ".")
                cleanModal = modalText.replace(".", "")
                    .replace(",", ".")
            } else {
                cleanBalance = balanceText.replace(".", "")
                    .replace(",", "")
                cleanModal = modalText.replace(".", "")
                    .replace(",", "")
            }
            val currentBalance = cleanBalance.toDouble()
            val currentModal = cleanModal.toDouble()
            if (currentModal > currentBalance) {
                // make text Kerugian & color red
                binding.txtModalIndicator.text = resources.getString(R.string.loss)
                binding.txtModalIndicator.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.red_100
                    )
                )
                binding.txtProfitAmount.setTextColor(ContextCompat.getColor(this, R.color.red_100))
//                binding.modalIndicatorContainer.setBackgroundResource(R.drawable.loss_bg)
            } else {
                // make text Keuntungan & color green
                binding.txtModalIndicator.text = resources.getString(R.string.profit)
                binding.txtModalIndicator.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.green_100
                    )
                )
                binding.txtProfitAmount.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.green_100
                    )
                )
//                binding.modalIndicatorContainer.setBackgroundResource(R.drawable.profit_bg)
            }
            val profit = currentBalance - currentModal
            val builder = StringBuilder()
            builder.append(Utility.formatAmount(profit))
            binding.txtProfitAmount.text = builder
        } catch (ex: Exception) {
            binding.txtProfitAmount.text = getString(R.string.default_placeholder)
        }
    }

    private fun initKeyboardView(cashTransactionEntity: CashTransactionEntity?) {
        transactionEntity = cashTransactionEntity
        binding.currencySymbol.text = Utility.getCurrency()
        binding.currencySymbolModal.text = Utility.getCurrency()
        binding.textAmountCalc.text = binding.balance.text
        binding.textAmountCalcModal.text = binding.balanceModal.text
        binding.keyboardView.setResultTv(binding.balance)
        binding.keyboardView.setExprTv(binding.textAmountCalc)
        binding.keyboardView.setResultLayout(findViewById(R.id.exprLayout))
        binding.keyboardView.cursor = binding.cursor
        binding.keyboardView.setCurrency(binding.currencySymbol)
        if (cashTransactionEntity == null) {
            binding.keyboardView.visibility = VISIBLE
            binding.keyboardView.showCursor()
        } else {
            binding.keyboardView.visibility = GONE
            binding.keyboardView.hideCursor()
        }
        if (!Utility.isBlank(binding.balance.text.toString())) {
            binding.keyboardView.updateResult(binding.balance.text.toString())
        }
        if (cashTransactionEntity?.orderId.isNullOrEmpty()) {
            binding.balanceClickView.setOnClickListener { changeCalculatorFocusToTrx() }
            binding.modalClickView.setOnClickListener {
                viewModel.onEventReceived(CashTransactionViewModel.Event.OnBalanceModalClicked)
                changeCalculatorFocusToModal()
                binding.textInfoLayout.visibility = VISIBLE
            }
        }
        binding.noteEt.onFocusChangeListener =
            OnFocusChangeListener { _: View?, hasFocus: Boolean ->
                if (hasFocus) {
                    closeCalculatorKeyboard()
                }
            }

        if (showMandatoryTransactionCategory) {
            hideKeyboard()
        }

        binding.keyboardView.setOnSubmitListener {
            viewModel.onEventReceived(
                CashTransactionViewModel.Event.OnKeyboardSubmit(
                    binding.balance.text.toString(),
                    binding.balanceModal.text.toString()
                )
            )
            totalPenjualan = Utilities.cleanStrNominal(binding.balance.text.toString()).toDouble()
            // for new transaction

            if (isModalFocus)
                binding.textInfoLayout.visibility = GONE

            showFormVisible()

            if (showTutorial) {
                if (allowShownSuccessTutorial) {
                    if (!getInstance().getHasFinishedForId(TUTOR_SAVE_TRX)) {
                        onboardingWidget = OnboardingWidget.createInstance(
                            this,
                            this,
                            TUTOR_SAVE_TRX,
                            binding.saveOnboarding,
                            R.drawable.onboarding_great,
                            getString(R.string.done_exclmark),
                            getString(R.string.onboarding_save_subtitle),
                            "",
                            FocusGravity.CENTER,
                            ShapeType.RECTANGLE_FULL,
                            1,
                            1,
                            true
                        )
                    }
                }
            }
        }
    }

    private fun closeCalculatorKeyboard() {
        if (<EMAIL>()) binding.keyboardView.clearAnimation()
        binding.keyboardView.visibility = GONE
        binding.keyboardView.hideCursor()
    }

    private fun changeCalculatorFocusToTrx() {
        try {
            binding.keyboardView.setResultTv(binding.balance)
            binding.keyboardView.setExprTv(binding.textAmountCalc)
            binding.keyboardView.setResultLayout(findViewById(R.id.exprLayout))
            binding.keyboardView.cursor = binding.cursor
            binding.keyboardView.setCurrency(binding.currencySymbol)
            binding.keyboardView.hideCursor()
            InputUtils.hideKeyBoardWithCheck(this)
            binding.noteEt.clearFocus()
            val moveup = AnimationUtils.loadAnimation(this, R.anim.move_up)
            if (isAnimEnabled()) binding.keyboardView.startAnimation(moveup)
            binding.keyboardView.visibility = VISIBLE
            binding.keyboardView.showCursor()
            binding.currencySymbol.visibility = VISIBLE
            isModalFocus = false
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun changeCalculatorFocusToModal() {
        try {
            binding.keyboardView.hideCursor()
            binding.keyboardView.setResultTv(binding.balanceModal)
            binding.keyboardView.setExprTv(binding.textAmountCalcModal)
            binding.keyboardView.setResultLayout(binding.exprLayoutModal)
            binding.keyboardView.cursor = binding.cursorModal
            binding.keyboardView.setCurrency(binding.currencySymbolModal)
            InputUtils.hideKeyBoardWithCheck(this)
            binding.noteEt.clearFocus()
            val moveup = AnimationUtils.loadAnimation(this, R.anim.move_up)
            if (isAnimEnabled()) binding.keyboardView.startAnimation(moveup)
            binding.keyboardView.visibility = VISIBLE
            binding.keyboardView.showCursor()
            binding.currencySymbolModal.visibility = VISIBLE
            isModalFocus = true
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun setTransactionEntityData(
        cashTransactionEntity: CashTransactionEntity,
        selectedCategory: Category?
    ) {
        binding.balance.text = Utility.formatCurrencyForEditing(cashTransactionEntity.amount)
        if (cashTransactionEntity.buyingPrice > 0) {
            // already have modal
            binding.balanceModal.text =
                Utility.formatCurrencyForEditing(cashTransactionEntity.buyingPrice)
            calculateModal()
        }
        binding.categoryEt.setText(getCategoryName(selectedCategory))
        binding.categoryEtNew.setText(getCategoryName(selectedCategory))
        editedCategory= selectedCategory
        checkForSaveButton()
        binding.tvDate.text = Utility.getReadableDateString(cashTransactionEntity.date)
        date = cashTransactionEntity.date
        binding.noteEt.setText(cashTransactionEntity.description)
        if (cashTransactionEntity.orderId.isNotNullOrEmpty()) {
            binding.ppobGroup.visibility = GONE
            binding.noteEt.isEnabled = false
            binding.noteEt.isFocusable = false
            binding.categoryEt.isEnabled = false
            binding.categoryEt.isFocusable = false
            binding.categoryEtNew.isEnabled = false
            binding.categoryEtNew.isFocusable = false
            binding.tvDate.isEnabled = false
            binding.tvDate.isFocusable = false
            binding.currencySymbol.setTextColor(resources.getColor(R.color.black_40))
            binding.balance.setTextColor(resources.getColor(R.color.black_40))
            binding.currencySymbolModal.setTextColor(resources.getColor(R.color.black_40))
            binding.balanceModal.setTextColor(resources.getColor(R.color.black_40))
        }
        if (isEdit) { var cat: String? = null

            cat = if (showMandatoryTransactionCategory) {
                binding.categoryEtNew.text.toString()
            } else {
                binding.categoryEt.text.toString()
            }
            categoryAdapter?.setNewCashCategoryName(cat)


//            binding.chRoofCategory.categoryChipFirst.text = cat

            if (showMandatoryTransactionCategory) {
                val category = binding.categoryEtNew.text.toString()

                if (category.contains(TransactionCategory.HIBAH.category, true) ||
                    category.contains(TransactionCategory.DONASI.category, true) ||
                    category.toString().contains(TransactionCategory.TABUNGAN.category, true)
                ) {
                    isExpense = false
                    binding.rbUnpaid.isChecked = false
                    binding.rbPaid.isChecked = true
                    binding.gpLunas.hideView()
                    showPaidTransaction()
                } else {
                    binding.gpLunas.showView()
                }

                if (category.isNullOrEmpty() && showMandatoryTransactionCategory) {

                    if (binding.rbExpense.isChecked) {
                        if (isDefaultPengeluaranStock) {
                            categoryAdapter?.setNewCashCategoryName(TransactionCategory.PEMBELIAN_STOCK.category)
                            viewModel.onCategoryChosen(Category(TransactionCategory.PEMBELIAN_STOCK.category,
                                TransactionCategory.PEMBELIAN_STOCK.category,TransactionCategory.PEMBELIAN_STOCK.category))
                            binding.categoryEtNew.setText(TransactionCategory.PEMBELIAN_STOCK.category)
                            enableProductGroup()
                        } else {
                            binding.categoryEtNew.setText("")
                            disableProductGroup()
                        }
                    } else {
                        categoryAdapter?.setNewCashCategoryName(TransactionCategory.PENJUALAN.category)
                        viewModel.onCategoryChosen(Category(TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category))

                        binding.categoryEtNew.setText(TransactionCategory.PENJUALAN.category)
                        enableProductGroup()
                    }
                    checkForSaveButton()
                } else if (category.contains(TransactionCategory.PEMBELIAN.category, true) ||
                    category.contains(TransactionCategory.PENJUALAN.category, true) ||
                    category.contains(TransactionCategory.PENDAPATAN.category, true)
                ) {
                    enableProductGroup()
                } else {
                    disableProductGroup()
                }
            }


            if (cat.equals(TransactionCategory.PINJAMAN.category, true) || cat.equals(
                    TransactionCategory.PIUTANG.category,
                    true
                ) ||
                cat.equals(TransactionCategory.PEMBAYARAN_UTANG.category, true) || cat.equals(
                    TransactionCategory.PEMBERIAN_UTANG.category, true
                )
            ) {
                showPaidTransaction()
            }

            binding.incCameraView.apply {
                root.hideView()
                cvImagePreview.hideView()
                clImagePreview.hideView()
                cvCameraInput.hideView()
//                val imageTransaksi = cashTransactionEntity.attachments
//
//                if (!RemoteConfigUtils.showTransksiImage() || imageTransaksi.isNullOrEmpty()) {
//                    root.hideView()
//                    cvImagePreview.hideView()
//                    cvCameraInput.hideView()
//                }
//
//                imageTransaksi?.let {
//                    Glide.with(this@NewCashTransactionActivity).load(it).into(ivImagePreview)
//                }
            }

        }

    }

    private fun setCategoryData(selectedCategory: Category?) {
        binding.categoryEt.setText(getCategoryName(selectedCategory))
        binding.categoryEtNew.setText(getCategoryName(selectedCategory))
        categoryAdapter?.setNewCashCategoryName(selectedCategory?.category_name_en)
        checkForSaveButton()
    }

    private fun setSelectedProduct(products: List<TransactionItemDto>?, isTabChanged: Boolean) {
        products ?: return
        productCount = products.size

        if (productCount != 0) {
            binding.productEt.hideView()
        }

        var totalProductBuyingPrice = 0.0
        products.forEach {
            totalProductBuyingPrice += (it.buyingPrice * it.quantity)
        }
        binding.balanceModal.text = Utility.formatCurrencyForEditing(totalProductBuyingPrice)

        if (isExpense) {
            binding.balance.text = Utility.formatCurrencyForEditing(totalProductBuyingPrice)
        }

        viewModel.onEventReceived(CashTransactionViewModel.Event.OnProductListSelected(products))
        try {
            //TODO: hariom please check
            if (transactionEntity?.orderId.isNotNullOrBlank()) {
                binding.productEt.visibility = products.isEmpty().asVisibility()
            }
            val totalPrice = if (!isExpense) {
                if (products.isNotEmpty()) {
                    products.sumByDouble { it.sellingPrice * it.quantity }.also {
                        sumOfItemTotal = it
                        totalPenjualan = it
                    }
                } else {
                    Utility.extractAmountFromText(binding.balance.text.toString())
                }
            } else {
                Utility.extractAmountFromText(binding.balance.text.toString())
            }
            binding.balance.text = Utility.formatCurrency(totalPrice)
            binding.textAmountCalc.text = Utility.formatCurrencyForEditing(totalPrice)

            binding.productHeaderLayout.tvPrice.visibility = (!isExpense).asVisibility()

            binding.productContainer.removeAllViews()

            binding.btnAddProduct.apply {
                text = if (products.isEmpty()) {
                    setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.mipmap.ic_plus_white_24dp,
                        0,
                        0,
                        0
                    )
                    getString(R.string.add_product)
                } else {
                    setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
                    getString(R.string.add_manage_product)
                }
            }

            products.forEach { populateProduct(it) }

            if (binding.gpProduct.isVisible && binding.productContainer.childCount > 0) {
                binding.productHeaderLayout.root.showView()
            } else {
                binding.productHeaderLayout.root.hideView()
            }
        } catch (e: Exception) {
            e.recordException()
            getString(R.string.product_sold_label).format(products.size)
        }
    }

    fun populateProduct(product: TransactionItemDto) {
        val productBinding = LayoutProductForReceiptBinding.inflate(layoutInflater).apply {
            tvProductName.text = product.productName
            tvPricePerUnit.apply {
                text = if (isExpense) {
                    ""
                } else {
                    Utility.formatAmount(product.sellingPrice) +
                            getString(R.string.forward_slash) + product.measurementUnit
                }
            }
            tvProductCount.text = Utility.getRoundedOffPrice(product.quantity)
            tvPriceWarning.visibility = (product.sellingPrice <= 0.0 && !isExpense).asVisibility()

            tvProductPrice.apply {
                text = Utility.formatAmount(product.sellingPrice * product.quantity)
                setTextColor(getColorCompat(R.color.mandy).takeIf { product.sellingPrice <= 0.0 }
                    ?: getColorCompat(R.color.black_80))

                visibility = (!isExpense).asVisibility()
            }
        }
        binding.productContainer.addView(productBinding.root)
    }

    fun setSelectedCategory(category: Category?) {
        viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategorySelected(category))
        try {
            binding.categoryEt.setText(getCategoryName(category))
            binding.categoryEtNew.setText(getCategoryName(category))
            checkForSaveButton()
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun onBackPressed() {

        InputUtils.hideKeyBoardWithCheck(this)
        if (userContactFragment != null) {
            removeUserFragment()
        } else if (onboardingWidget != null && onboardingWidget!!.isShown) {
            onboardingWidget!!.dismiss(false)
        } else if (binding.keyboardView.visibility == VISIBLE) {
            binding.keyboardView.visibility = GONE
        } else if (cashTransactionId == null) {
            if (shouldShowExitDialog) {
                if ((binding.balance.text.toString() != "0" && binding.balance.text.toString() != "")
                    || (binding.balanceModal.text.toString() != "" && binding.balanceModal.text.toString() != "0")
                    || binding.noteEt.text.toString() != ""
                    || binding.categoryEt.text.toString() != ""
                    || (binding.contactSearchContainerNonEdit.searchInput.text.toString() != "")
                    || (binding.paidContactSearchContainerNonEdit.searchInput.text.toString() != "")
                    || areProductsSelected
                ) {
                    showExitDialog()
                } else {
                    moveToMainActivity()
                }
            } else {
                moveToMainActivity()
            }
        } else {
            moveToMainActivity()
        }
        showFormVisible()
    }

    private fun moveToMainActivity() {
        if (!intent.hasExtra(SHOW_BUSINESS_DASHBOARD))
        {
            if (shouldShowNewHomePage() && !isEdit) {
                MainActivity.startActivitySingleTopToTab(this, TabName.HOME)
            } else {
                MainActivity.startActivitySingleTopToTab(this, TabName.TRANSACTION)
            }
        }

        finish()
    }

    private fun showExitDialog() {

        val prop = PropBuilder()
        prop.put(AnalyticsConst.FORM_NAME, AnalyticsConst.TRANSAKSI_FORM)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_FORM_EXIT_DIALOG_APPEAR, prop)

        GenericConfirmationDialog.create(this) {
            titleRes = R.string.utang_transaksi_exit_dialog_title
            bodyRes = R.string.utang_transaksi_exit_dialog_body
            btnLeftRes = R.string.utang_transaksi_exit_dialog_yes_btn
            btnRightRes = R.string.utang_transaksi_exit_dialog_no_btn
            rightBtnCallback = {
                val propClosed = PropBuilder()
                propClosed.put(AnalyticsConst.FORM_NAME, AnalyticsConst.TRANSAKSI_FORM)
                propClosed.put(AnalyticsConst.OPTION_SELECTED, AnalyticsConst.CONTINUE)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_FORM_EXIT_DIALOG_CLOSED, propClosed)
            }
            leftBtnCallback = {
                val propClosed = PropBuilder()
                propClosed.put(AnalyticsConst.FORM_NAME, AnalyticsConst.TRANSAKSI_FORM)
                propClosed.put(AnalyticsConst.OPTION_SELECTED, AnalyticsConst.EXIT)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_FORM_EXIT_DIALOG_CLOSED, propClosed)
                if (!RemoteConfigUtils.NewHomePage.shouldShowNewHomePage()) {
                    MainActivity.startActivitySingleTopToTab(
                        this@NewCashTransactionActivity,
                        TabName.TRANSACTION
                    )
                }
                finish()
            }
        }.show()
    }

    private fun updateUi(isExpense: Boolean) {
        if (cashTransactionId != null) {
            binding.toolbarTitle.text = getString(R.string.edit_transaction)
//                setHeaderMenuAttribute(binding.tvOptionalInfo, binding.otherFieldsGroup.visibility)
            binding.cursorModal.visibility = GONE
            binding.cursor.visibility = GONE

            if (!customerId.isNullOrEmpty()) {
                binding.paidContactSearchContainerNonEdit.searchInput.setText(
                    customerName
                        ?: ""
                )
                binding.contactSearchContainerNonEdit.searchInput.setText(customerName ?: "")
                var contact = Contact(customerName, "", "", customerId)
                onExistingCustomer(contact)
            }

            if (binding.rbPaid.isChecked) {
                showPaidTransaction()
            } else if (binding.rbUnpaid.isChecked) {
                showUnpaidTransaction()
            }
            if (!isExpense)
                setModalVisibility(VISIBLE)
            else
                setModalVisibility(GONE)
        } else {
            if (binding.rbPaid.isChecked) {
                showPaidTransaction()
            } else {
                showUnpaidTransaction()
            }
            if (!isFormVisible) {
                binding.otherFieldsGroup.visibility = GONE
                binding.categoryEt.hideView()
                binding.paidContactSearchContainerNonEdit.root.visibility = GONE
                binding.btnSave.visibility = GONE
                binding.otherFieldsDivider.visibility = GONE
            }
            if (tempOnboardingVisibility) {
                tempOnboardingVisibility = false;
                binding.tvOptionalInfo.visibility = VISIBLE
                showFormVisible()
            }
        }
        val colorCondition = if (isExpense) R.color.out_red else R.color.green_100
        binding.balance.setTextColor(getColorCompat(colorCondition))
        binding.currencySymbol.setTextColor(getColorCompat(colorCondition))
        binding.balance.setHintTextColor(getColorCompat(colorCondition))
        binding.balanceModal.setTextColor(getColorCompat(R.color.out_red))
        binding.currencySymbolModal.setTextColor(getColorCompat(R.color.out_red))
        binding.balanceModal.setHintTextColor(getColorCompat(R.color.out_red))
        binding.productEt.hint =
            if (isExpense) getString(R.string.product_purchase_label) else getString(R.string.product_sold_label)

        if (isExpense) {
            binding.txtMainTitle.text = getString(R.string.total_expense_label)
            binding.textInfoLayout.visibility = GONE
            if (cashTransactionId == null) {
                binding.toolbarTitle.text = getString(R.string.transaction_contact_debit_title)
            }
            binding.productEt.text = resources.getString(R.string.product_sold_label_expense)
        } else {
            //binding.rgStatus.rb_paid.isChecked = true
            binding.txtMainTitle.text = getString(R.string.total_selling)
            if (cashTransactionId == null) {
                binding.toolbarTitle.text = getString(R.string.new_selling)
            }
            binding.productEt.text = resources.getString(R.string.product_sold_label)
        }

        if (showMandatoryTransactionCategory) {
            hideKeyboard()
        } else {
            changeCalculatorFocusToTrx()
        }
        setModalVisibility((!isExpense).asVisibility())
        if (!isUpaidTransactionSupported()) {
            val hideContact = RemoteConfigUtils.hideContact()
            val count =
                TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords
            if (count < 1 && hideContact) {
                binding.paidContactSearchContainerNonEdit.root.visibility = GONE
            } else {
                binding.paidContactSearchContainerNonEdit.root.visibility = showInformasiOptional
            }
        }

        viewModel.onEventReceived(CashTransactionViewModel.Event.OnTrxTypeChange(if (isExpense) DEBIT else CREDIT))
    }

    private fun setModalVisibility(visibility: Int) {
        binding.modalGroup.visibility = visibility
        binding.exprLayoutModal.visibility = GONE
        binding.cursorModal.visibility = GONE
    }

    private fun hideKeyboard() {
        binding.keyboardView.visibility = GONE
    }

    private fun showTransactionDatePicker(transactionDate: String?) {
        val instance = Calendar.getInstance()
        var year = instance[Calendar.YEAR]
        var month = instance[Calendar.MONTH]
        var date = instance[Calendar.DATE]
        try {
            if (!transactionDate.isNullOrEmpty()) {
                val currentDate = DateTimeUtils.convertToDateYYYYMMDD(transactionDate)
                instance.time = currentDate
                year = instance[Calendar.YEAR]
                month = instance[Calendar.MONTH]
                date = instance[Calendar.DATE]
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
        val datePickerDialog =
            DatePickerDialog(this, android.R.style.Theme_DeviceDefault_Light_Dialog,
                { _, selectedYear, selectedMonth, selectedDate ->
                    val cal = Calendar.getInstance()
                    cal[selectedYear, selectedMonth] = selectedDate
                    val storableDateString = Utility.getStorableDateString(cal.time)
                    viewModel.onEventReceived(
                        CashTransactionViewModel.Event.OnTransactionDateChanged(
                            storableDateString
                        )
                    )
                    binding.tvDate.text = Utility.getReadableDateString(storableDateString)
                }, year, month, date
            )
        datePickerDialog.setTitle(R.string.date)
        datePickerDialog.show()
        val moveDown = AnimationUtils.loadAnimation(
            this@NewCashTransactionActivity,
            R.anim.move_down
        )

        if (this.isAnimEnabled()) binding.keyboardView.startAnimation(moveDown)
        binding.keyboardView.visibility = GONE
        binding.keyboardView.hideCursor()
    }

    private fun getCategoryName(category: Category?): String? {
        return if (appLanguage == 1) category?.category_name_en else category?.category_name_id
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        if (id == TOUR_CASH_TAB_ADD_BTN_STEP3 || id == TOUR_CASH_TAB_ADD_BTN_STEP2) {
            getInstance().setHasFinishedForId(OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN)
        } else if (id == TUTOR_PRODUCT_DETAIL || id == TUTOR_PRODUCT_DETAIL_WITH_INVENTORY) {
            getInstance().setHasFinishedForId(id)
        } else if (id == TUTOR_SAVE_TRX) {
//            builder.put(AnalyticsConst.TYPE, if (binding.expenseRd.isChecked) AnalyticsConst.EXPENSE else AnalyticsConst.SALES)
        } else if (id == CATEGORY_SELECT_INFO) {
            getInstance().setHasFinishedForId(id)
        }
    }

    private fun setHeaderMenuAttribute(textView: TextView, visibility: Int) {
        val arrow: Int = if (visibility == VISIBLE) {
            viewModel.onEventReceived(CashTransactionViewModel.Event.OptionalMenuStateChange(true))
            InputUtils.hideKeyBoardWithCheck(this)
            R.drawable.ic_chevron_up
        } else {
            viewModel.onEventReceived(CashTransactionViewModel.Event.OptionalMenuStateChange(false))
            InputUtils.hideKeyBoardWithCheck(this)
            R.drawable.ic_chevron_down
        }
        textView.setDrawable(right = arrow)

    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        if (requestCode == PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION && Build.VERSION.SDK_INT >= 23) {
            if (PermissonUtil.hasContactPermission()) {
                userContactFragment?.allowedContactPermission()
                binding.contactSearchContainerNonEdit.addContactParentLayout.visibility = GONE
                binding.paidContactSearchContainerNonEdit.addContactParentLayout.visibility = GONE
            }
        } else if (requestCode == PermissionConst.REQ_TAKE_PICTURE_PERMISSON && Build.VERSION.SDK_INT >= 23) {
            setImageSourceFromCamera()
        }
    }

    override fun onCustomerSelected(contact: Contact?, contactSource: String) {
        Log.d(getClassTag(), contact?.name.toString())
        InputUtils.hideKeyBoardWithCheck(this)
        customerContact = contact
        customerPhoneNumber = contact?.mobile ?: ""
        sendSms = false

        if (contact?.mobile.isNotNullOrEmpty()) {
            customerEntity?.phone = contact?.mobile
            sendSms = false
        }
        binding.contactSearchContainerNonEdit.searchInput.setText(contact?.name ?: "")
        binding.paidContactSearchContainerNonEdit.searchInput.setText(contact?.name ?: "")
        setSmsCheckBox()
        removeUserFragment()
        viewModel.onEventReceived(
            CashTransactionViewModel.Event.OnCustomerSelected(
                contact,
                contactSource
            )
        )
    }

    private fun onExistingCustomer(contact: Contact?) {
        customerContact = contact
        customerPhoneNumber = contact?.mobile ?: ""
        if (contact?.mobile.isNotNullOrEmpty())
            customerEntity?.phone = contact?.mobile
        viewModel.onEventReceived(CashTransactionViewModel.Event.OnCustomerSelected(contact))
        sendSms = false
        setSmsCheckBox()
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        if (id == null) return
        when (id) {
            TUTOR_SAVE_TRX -> onSaveButtonClicked()
            TUTOR_PRODUCT_DETAIL, TUTOR_PRODUCT_DETAIL_WITH_INVENTORY -> openProductList()
            TOUR_CASH_TAB_ADD_BTN_STEP3 -> {
                allowShownSuccessTutorial = true
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK) return
        var uri: Uri? = null
        when (requestCode) {
            PRODUCT_LIST_REQUEST_CODE -> {
                val selectedProduct =
                    data?.getParcelableArrayExtra(ProductListActivity.SELECTED_PRODUCTS)?.map {
                        it as TransactionItemDto
                    } ?: emptyList()

                areProductsSelected = !selectedProduct.isEmpty()

                viewModel.onEventReceived(
                    CashTransactionViewModel.Event.OnProductSelectionComplete(
                        selectedProduct
                    )
                )
            }
            RC_SELECT_BANK_ACCOUNT -> {
            }

            PermissionConst.TAKE_PHOTO -> {
                uri = profilePicUri
                imageSrc = takePhoto
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_USER_PROFILE_PIC_CAMERA)
            }

            PermissionConst.REQ_PICK_IMAGE_PERMISSON -> {
                uri = data?.data
                imageSrc = upload
            }

            SELECT_CATEGORY -> {
            selectCategory(data?.getStringExtra(CATEGORY)!!)
                   }
        }

        uri?.let {
            var bitmap = MediaStore.Images.Media.getBitmap(
                this.contentResolver,
                uri
            )

            bitmap = Utility.fixImageRotation(this, bitmap, profilePicFile, it)
            binding.incCameraView.cvCameraInput.hideView()
            binding.incCameraView.cvImagePreview.showView()
            binding.incCameraView.ivImagePreview.setImageBitmap(bitmap)
            viewModel.onEventReceived(CashTransactionViewModel.Event.OnImageCaptured(bitmap))
            profilePicFile = null
        }
    }

    private fun setCategorySelectedState(paid: Int, categoryStatus: Int) {
        if (paid == 0) {
            binding.rbUnpaid.isChecked = true
            binding.rbPaid.isChecked = false
        } else {
            binding.rbUnpaid.isChecked = false
            binding.rbPaid.isChecked = true
        }
        showUnpaidTransaction()
        categorySelectedStatus = categoryStatus
    }

    private fun saveCategory(category: String?) {
        val categoryEntity = CashRepository.getInstance(this)
            .getCashCategoryById(category)

        cashCategoryEntity?.let {
            UpdateCashAsyncTask()
                .execute(categoryEntity)
        }

    }


    private fun showPaidTransaction() {
        currentMode = TransactionStatus.MODE_FULLY_PAID
        val hideContact = RemoteConfigUtils.hideContact()
        val count = TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords
        if (count < 1 && hideContact) {
            binding.paidContactSearchContainerNonEdit.root.visibility = GONE
        } else {
            binding.paidContactSearchContainerNonEdit.root.visibility =
                binding.otherFieldsGroup.visibility
            if (!isUpaidTransactionSupported()) {
                binding.paidContactSearchContainerNonEdit.root.visibility = GONE
            }
            binding.unpaidContactGroup.visibility = GONE
            binding.contactSearchContainerNonEdit.root.visibility = GONE
        }
    }

    private fun showUnpaidTransaction() {
        currentMode = TransactionStatus.MODE_FULLY_UNPAID
        val hideContact = RemoteConfigUtils.hideContact()
        val count = TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords
        binding.unpaidContactGroup.visibility = VISIBLE
        if (count < 1 && hideContact) {
            binding.contactSearchContainerNonEdit.root.visibility = GONE
            binding.paidContactSearchContainerNonEdit.root.visibility = GONE
        } else {
            binding.contactSearchContainerNonEdit.root.visibility = VISIBLE
            binding.paidContactSearchContainerNonEdit.root.visibility = GONE
        }
    }

    // set layout for the inventory
    private fun setLayoutForInventory() {
        val parent = binding.content.id
        val constraintSet = ConstraintSet().apply {
            clone(binding.content)

            clear(binding.productScrollview.id, ConstraintSet.TOP)
            connect(
                binding.productScrollview.id,
                ConstraintSet.TOP,
                parent,
                ConstraintSet.TOP,
                16.dp
            )

            clear(binding.nominalsBackground.id, ConstraintSet.TOP)
            connect(
                binding.nominalsBackground.id,
                ConstraintSet.TOP,
                binding.otherFieldsDivider.id,
                ConstraintSet.BOTTOM,
                0
            )

            clear(binding.tvOptionalInfo.id, ConstraintSet.TOP)
            connect(
                binding.tvOptionalInfo.id,
                ConstraintSet.TOP,
                binding.productDivider.id,
                ConstraintSet.BOTTOM,
                16.dp
            )

        }

        constraintSet.applyTo(binding.content)

    }

    private fun removeUserFragment() {
        if (userContactFragment != null) {
            supportFragmentManager.beginTransaction().remove(userContactFragment!!).commit()
            userContactFragment = null
        }
    }

    private fun addUserFragment() {
        if (isExpense) {
            userContactFragment = UserContactFragment.getInstance(
                TYPE_DEBIT_FROM_TRANSACTION_FLOW, customerEntity?.name
                    ?: customerNameEntered
            )
        } else {
            userContactFragment = UserContactFragment.getInstance(
                TYPE_CREDIT_FROM_TRANSACTION_FLOW, customerEntity?.name
                    ?: customerNameEntered
            )
        }
        supportFragmentManager.beginTransaction()
            .add(R.id.fragment_container_contact, userContactFragment!!).commit()
    }

    private fun requestContactPermission() {
        InputUtils.hideKeyboard(this)
        if (!PermissonUtil.hasContactPermission()) {// && !OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_CONTACT_PERMISSION) && AppConfigManager.getInstance().showCustomPermissionDialog()) {
            val dialog =
                ContactPermissionBottomSheetDialog.newInstance(AnalyticsConst.TRANSAKSI_FORM)
            dialog.show(supportFragmentManager, ContactPermissionBottomSheetDialog.TAG)
            // OnboardingPrefManager.getInstance().setHasFinishedForId(OnboardingPrefManager.TUTORIAL_CONTACT_PERMISSION)
        } else {
            if (!PermissonUtil.hasContactPermission()) {
                AppAnalytics.trackEvent("request_contact_permission")
                ActivityCompat.requestPermissions(
                    this,
                    PermissionConst.READ_WRITE_CONTACTS,
                    PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION
                )
            }
        }
    }

    override fun allowPermission() {
        if (!PermissonUtil.hasContactPermission()) {
            ActivityCompat.requestPermissions(
                this,
                PermissionConst.READ_WRITE_CONTACTS,
                PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION
            )
        }
    }

    override fun onCustomerNameEntered(customerNameEntered: String) {
        this.customerNameEntered = customerNameEntered
    }

    private fun changeStatusVisibility(show: Boolean) {
        var visibility: Int = GONE
        if (isUpaidTransactionSupported() && show) {
            visibility = VISIBLE
        }
        binding.tvStatusLabel.visibility = visibility
        binding.rgStatus.visibility = visibility
        val hideContact = RemoteConfigUtils.hideContact()
        val count = TransactionRepository.getInstance(this).cashTransactionCountWithDeletedRecords
        if (count < 1 && hideContact) {
            binding.paidContactSearchContainerNonEdit.root.visibility = GONE
        } else {
            if (showInformasiOptional.equals(VISIBLE)) {
                binding.paidContactSearchContainerNonEdit.root.visibility = visibility;
            } else {
                binding.paidContactSearchContainerNonEdit.root.visibility = showInformasiOptional;
            }
        }
    }

    // show the content on tap save/back button
    private fun showFormVisible() {
        if (!isFormVisible && cashTransactionId == null) {
            if (!tempOnboardingVisibility) {
                isFormVisible = true
            }
            binding.tvOptionalInfo.visibility = VISIBLE
            binding.btnSave.visibility = VISIBLE
            binding.otherFieldsDivider.visibility = VISIBLE
            changeVisibilityOfInformasiOptional(showInformasiOptional)
            binding.otherFieldsGroup.visibility = showInformasiOptional
            binding.tvDate.visibility = showInformasiOptional
            if (!showMandatoryTransactionCategory) {
                binding.categoryEt.visibility = showInformasiOptional
            }
            binding.noteEt.visibility = showInformasiOptional
            changeStatusVisibility(true)
            if (!isExpense) {
//                changeCalculatorFocusToModal()
                binding.textInfoLayout.visibility = VISIBLE
            }

            setModalVisibility((!isExpense).asVisibility())
        }
    }

    // Show/Hide sms checkbox.
    private fun setSmsCheckBox() {
        if (customerPhoneNumber.isNotEmpty()) {
            if (binding.rbPaid.isChecked) {
                binding.paidOffSendCustomerSms.visibility = binding.otherFieldsGroup.visibility
                binding.paidOffSendCustomerSms.isChecked = sendSms
                binding.notPaidOffSendCustomerSms.visibility = GONE

                if (sendSms)
                    binding.paidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_checked)
                else
                    binding.paidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_not_checked)

            } else {
                if (isUpaidTransactionSupported()) {
                    binding.notPaidOffSendCustomerSms.visibility = VISIBLE
                }
                binding.notPaidOffSendCustomerSms.isChecked = sendSms
                binding.paidOffSendCustomerSms.visibility = GONE

                if (sendSms)
                    binding.notPaidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_checked)
                else
                    binding.notPaidOffSendCustomerSms.setButtonDrawable(R.drawable.ic_checkbox_not_checked)
            }
        } else {
            binding.notPaidOffSendCustomerSms.visibility = GONE
            binding.paidOffSendCustomerSms.visibility = GONE
            sendSms = false
        }
        if (!RemoteConfigUtils.shouldSendSmsTransaction()) {
            paidOffSendCustomerSms.isChecked = false
            paidOffSendCustomerSms.hideView()
            notPaidOffSendCustomerSms.isChecked = false
            notPaidOffSendCustomerSms.hideView()
            sendSms = false
        }
    }

    private fun checkForSaveButton() {
        if (showMandatoryTransactionCategory) {
            binding.btnSave.isEnabled = binding.categoryEtNew.text.toString().isNotNullOrEmpty()
        }
    }

    private fun enableProductGroup() {
        if (showMandatoryTransactionCategory) {
            binding.gpProduct.showView()
            if (binding.productContainer.childCount > 0) {
                binding.productHeaderLayout.root.showView()
            } else {
                binding.productHeaderLayout.root.hideView()
            }
        } else {
            binding.gpProduct.hideView()
            binding.productHeaderLayout.root.hideView()
        }
    }

    private fun disableProductGroup() {
        binding.gpProduct.hideView()
        binding.productHeaderLayout.root.hideView()
    }

    private fun setCategoryByFrequency(topFiveCategories: List<CashCategoryEntity>)
    {
        topCategories = topFiveCategories.sortedByDescending {
            it.frequency
        }
        setCategoryAdapter(isExpense)
    }

    private fun setCategoryAdapter(isExpense: Boolean)
    {
        var chipSelectedCategory :Category? = null
        var sortedCategory = mutableListOf<com.bukuwarung.activities.expense.data.Category>()
        binding.rgTrxType.check(if (isExpense) binding.rbExpense.id else binding.rbSelling.id)

        if(!isExpense) {

            categories=  RemoteConfigUtils.SelectCategory.getCreditCategoriesNew()
            chipSelectedCategory =
                Category(TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category,TransactionCategory.PENJUALAN.category)

        }else{
            categories = RemoteConfigUtils.SelectCategory.getDebitCategoriesNew()
            chipSelectedCategory= Category(TransactionCategory.PEMBELIAN_STOCK.category
                ,TransactionCategory.PEMBELIAN_STOCK.category,
                TransactionCategory.PEMBELIAN_STOCK.category)


        }


        val gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<com.bukuwarung.activities.expense.data.Category?>?>() {}.type
        categoriesToDisplay = gson.fromJson(categories, jsonType)
        if(!isExpense)
        {
            categoriesToDisplay?.add(0,com.bukuwarung.activities.expense.data.Category("https://i.ibb.co/GJ32gQT/penjualan.webp","Penjualan",1))

        }

        categoryAdapter = NewCashCategoryAdapter {category ->
            selectCategory(category?.categoryName!!)

        }
        if(topCategories!= null)
        {
            topCategories?.forEach {topCategory ->
                categoriesToDisplay?.filter { it.categoryName == topCategory.name }?.forEach { it.frequency = topCategory.frequency}

            }
             sortedCategory=categoriesToDisplay!!.sortedByDescending {
                it.frequency
            } as MutableList<com.bukuwarung.activities.expense.data.Category>
        }
        if (categoriesToDisplay != null) if (editedCategory != null && isEdit) {
           val categoryIndex =
               categoriesToDisplay?.indexOfFirst { it.categoryName == editedCategory?.category_name_en }
           if (categoryIndex != null && categoryIndex != -1 && categoryIndex<4) {
               sortedCategory = categoriesToDisplay!!.subList(0, 5)
               categoryAdapter?.setData(sortedCategory)
               categoryAdapter?.setNewCashCategoryName(categoriesToDisplay?.get(categoryIndex)!!.categoryName)
               viewModel.onCategoryChosen(editedCategory!!)
           } else if(categoryIndex != null && categoryIndex != -1 && categoryIndex >= 5){
               val removeCategory =  categoriesToDisplay?.get(categoryIndex)!!
               categoriesToDisplay?.remove(removeCategory)
               categoriesToDisplay?.add(
                   0,
                   removeCategory
               )
               sortedCategory = categoriesToDisplay!!.subList(0, 6)
               categoryAdapter?.setData(sortedCategory)
               categoryAdapter?.setNewCashCategoryName(removeCategory.categoryName)
               viewModel.onCategoryChosen(editedCategory!!)
           }
           else{
               sortedCategory = sortedCategory.subList(0, 5)
               categoryAdapter?.setData(sortedCategory)
               categoryAdapter?.setNewCashCategoryName(chipSelectedCategory.category_name_en)
               viewModel.onCategoryChosen(chipSelectedCategory)
           }

       }


       else{
            sortedCategory = sortedCategory.subList(0, 5)
            categoryAdapter?.setData(sortedCategory)
            categoryAdapter?.setNewCashCategoryName(chipSelectedCategory.category_name_en)
            viewModel.onCategoryChosen(chipSelectedCategory)
        }
        binding.chRoofCategory.rvSelectCategory.adapter = categoryAdapter
    }

    private  fun selectCategory( data : String)
    {
        val category = data
        binding.categoryEt.setText(category)
        binding.categoryEtNew.setText(category)
        val index =categoriesToDisplay?.indexOfFirst{
            it.categoryName == category
        }
        if (index != null) {
            if (index != -1 && index <= 4) {
                categoryAdapter?.setNewCashCategoryName(category)
            }else{
                val  sortedCategory = categoriesToDisplay?.subList(0, 5)
                sortedCategory?.add(0,com.bukuwarung.activities.expense.data.Category(category!!,category,0,0))
                categoryAdapter?.setData(sortedCategory!!)
                categoryAdapter?.setNewCashCategoryName(category)
            }
        }

        checkForSaveButton()

        category?.let {
            val cat = viewModel.getCategoryById(category)
            viewModel.onEventReceived(CashTransactionViewModel.Event.OnCategorySelected(cat))
            categorySelectedStatus = 0
        }

        if (binding.categoryEt.text.toString()
                .equals(TransactionCategory.PINJAMAN.category, true)
        ) {
            setCategorySelectedState(0, 1)
        }
        if (binding.categoryEt.text.toString()
                .equals(TransactionCategory.PEMBAYARAN_UTANG.category, true)
        ) {
            setCategorySelectedState(1, 2)
        }
        if (binding.categoryEt.text.toString()
                .equals(TransactionCategory.PIUTANG.category, true)
        ) {
            setCategorySelectedState(1, 1)
        }
        if (binding.categoryEt.text.toString()
                .equals(TransactionCategory.PEMBERIAN_UTANG.category, true)
        ) {
            setCategorySelectedState(1, 2)
        }

        if (binding.categoryEtNew.text.toString()
                .equals(TransactionCategory.PIUTANG.category, true)
        ) {
            setCategorySelectedState(1, 1)
        }

        if (binding.categoryEtNew.text.toString()
                .equals(TransactionCategory.PENANBAHAN_MODAL.category, true)
        ) {
            disableProductGroup()
        }
        if (binding.categoryEtNew.text.toString()
                .equals(TransactionCategory.PENDAPATAN_DILUAR_USAHA.category, true)
        ) {
            disableProductGroup()
        }

        if (binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.HIBAH.category, true) ||
            binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.DONASI.category, true) ||
            binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.TABUNGAN.category, true)
        ) {
            isExpense = false
            binding.rbUnpaid.isChecked = false
            binding.rbPaid.isChecked = true
            binding.gpLunas.hideView()
            showPaidTransaction()
        } else {
            binding.gpLunas.showView()
        }

        if (showMandatoryTransactionCategory) {
            if (binding.categoryEtNew.text.toString()
                    .contains(TransactionCategory.PEMBELIAN.category, true) ||
                binding.categoryEtNew.text.toString()
                    .contains(TransactionCategory.PENJUALAN.category, true) ||  binding.categoryEtNew.text.toString()
                    .contains(TransactionCategory.PENDAPATAN_LAIN_LAIN.category, true)||  binding.categoryEtNew.text.toString()
                    .contains(TransactionCategory.PENDAPATAN_JASA_KOMISI.category, true)
            ) {
                enableProductGroup()
            } else {
                disableProductGroup()
            }
        }

        if(binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.GAJI_BONUS_KARYAWAN.category, true)|| binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.GAJI_BONUS_KARYAWAN.category, true) || binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.PENGELUARAN_DILUAR_USAHA.category, true) || binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.PENDAPATAN_DILUAR_USAHA.category, true)||binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.PENAMBAHAN_MODAL.category, true)||binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.PINJAMAN.category, true)||binding.categoryEtNew.text.toString()
                .contains(TransactionCategory.PENAGIHAN_UTANG.category, true))
        {
            setModalVisibility(GONE)
        }else{
            setModalVisibility((!isExpense).asVisibility())
        }


    }
}

