package com.bukuwarung.activities.expense;

import android.util.Log;

import com.bukuwarung.activities.superclasses.DataHolder;

import java.util.Collections;
import java.util.List;

public class StringListProvider {

    private List<DataHolder> list;

    public StringListProvider(List<DataHolder> list) {
        this.list = list;
    }

    public List<DataHolder> getDataHolderList(int page, int pageSize) {

    /*    if (list.size() < pageSize) {
            return list;
        }
        int initialIndex = page * pageSize;
        int finalIndex = initialIndex + pageSize;*/
        try {
            Log.i("Paging", "Page load start = " +page + " end = " + pageSize);
            if(page >= list.size()) return Collections.emptyList();
            if(pageSize >= list.size()) {
                pageSize = list.size();
            }
            return list.subList(page, pageSize);
        } catch (IndexOutOfBoundsException e) {
            return list;
        }
    }
}
