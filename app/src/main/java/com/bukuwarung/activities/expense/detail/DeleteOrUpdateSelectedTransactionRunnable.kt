package com.bukuwarung.activities.expense.detail

import android.content.Context
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User

class DeleteOrUpdateSelectedTransactionRunnable(
    val activity: Context,
    val transactionIds: List<String>,
    val categoryId: String,
    val doDelete: Boolean = false,
    var categoryType: Int = 0,
    val currentCategory: String
): Runnable {

    override fun run() {
        val cashRepository = CashRepository.getInstance(activity)
        val userId = User.getUserId()
        val deviceId = User.getDeviceId()

        try {


            if(cashRepository.getCashCategoryByName(categoryId)!=null){
                var catagoryEntity = cashRepository.getCashCategoryByName(categoryId)
                cashRepository.updateExistingCashTransactions(userId, deviceId, if(doDelete==true) 1 else 0,transactionIds,catagoryEntity.cashCategoryId,currentCategory)
            }else{
                var categoryIdFormatted = categoryId.takeIf { it.contains("::") }
                    ?: "${categoryId}::${SessionManager.getInstance().businessId}"
                var newCategoryId =  cashRepository.saveNewCashCategory(userId,deviceId,User.getBusinessId(),categoryIdFormatted,categoryId,0.0,categoryType,-1)
                cashRepository.updateExistingCashTransactions(userId, deviceId, if(doDelete==true) 1 else 0,transactionIds,newCategoryId,currentCategory)
            }


//        cashRepository.updateExistingCashTransactions(userId, deviceId, if(doDelete==true) 1 else 0,transactionIds,categoryId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}