package com.bukuwarung.activities.expense.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.dataholder.BaseProductDataHolder
import com.bukuwarung.activities.expense.adapter.dataholder.ProductDataHolder
import com.bukuwarung.activities.expense.adapter.viewholder.ProductDividerViewHolder
import com.bukuwarung.activities.expense.adapter.viewholder.ProductViewHolder
import com.bukuwarung.activities.expense.adapter.viewholder.ProductViewHolderEvent
import com.bukuwarung.preference.FeaturePrefManager

class ProductAdapter(private val isExpense: Boolean,
                     private val checkCallback: (ProductDataHolder, ProductViewHolderEvent) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val data = mutableListOf<BaseProductDataHolder>()
    private val isInventoryEnabled = FeaturePrefManager.getInstance().stockTabEnabled()


    fun setData(list: List<BaseProductDataHolder>) {
        data.apply {
            clear()
            addAll(list)
        }
        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int): Int = data[position].tag

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            BaseProductDataHolder.CHECKED, BaseProductDataHolder.UNCHECKED -> {
                if (isExpense)
                    ProductViewHolder(layoutInflater.inflate(R.layout.item_product, parent, false))
                else
                    ProductViewHolder(layoutInflater.inflate(R.layout.item_selling_product, parent, false))
            }
            BaseProductDataHolder.DIVIDER -> ProductDividerViewHolder(layoutInflater.inflate(R.layout.product_divider, parent, false))
            else -> ProductDividerViewHolder(layoutInflater.inflate(R.layout.product_divider, parent, false))
        }
    }

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val singleData = data[position]) {
            is ProductDataHolder -> {
                holder as ProductViewHolder
                holder.bind(singleData, isInventoryEnabled, isExpense, checkCallback)
            }
        }
    }

}