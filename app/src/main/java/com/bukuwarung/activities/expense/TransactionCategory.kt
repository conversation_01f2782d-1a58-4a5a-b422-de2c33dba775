package com.bukuwarung.activities.expense

enum class TransactionCategory(val category: String) {
    PENJUALAN("Penjualan"),
    PINJAMAN("Pinjaman"),
    PEMBAYARAN_UTANG("Pembayaran utang"),
    PIUTANG("Piutang"),
    PEMBERIAN_UTANG("Pemberian utang"),
    HIBAH("hibah"),
    DONASI("Donasi"),
    TABUNGAN("Tabungan"),
    PEMBELIAN("Pembelian"),
    PENDAPATAN("Pendapatan"),
    PENANBAHAN_MODAL("Penambahan Modal"),
    PEMBELIAN_STOCK("Pembelian Stok"),
    PENGELUARAN_DILUAR_USAHA("Pengeluaran Di Luar usaha"),
//    TRANSAKSI_AGEN_PEMBAYARAN("Transaksi Agen Pembayaran"),
    PENDAPATAN_DILUAR_USAHA("Pendapatan Di Luar Usaha"),
    PENDAPATAN_LAIN_LAIN("Pendapatan Lain-Lain"),
    PENDAPATAN_JASA_KOMISI("Pendapatan Jasa/Komisi"),
    PENAMBAHAN_MODAL("Penambahan Modal"),
    PINJAMIN("Pinjamin"),
    PENAGIHAN_UTANG("Penagihan Utang/Cicilan"),
    GAJI_BONUS_KARYAWAN("Gaji/Bonus Karyawan"),


}