package com.bukuwarung.activities.expense.adapter.dataholder

import com.bukuwarung.database.entity.ProductEntity

abstract class BaseProductDataHolder{
    var tag = UNCHECKED

    companion object {
        const val CHECKED = 1
        const val DIVIDER = 2
        const val UNCHECKED = 3
    }
}

data class ProductDataHolder(
        var isChecked: Boolean = false,
        var isEditShow: Boolean = false,
        var quantity: Double = 0.0,
        var productEntity: ProductEntity? = null
) : BaseProductDataHolder()

class ProductDividerDataHolder : BaseProductDataHolder() {
    companion object {
        fun create() = ProductDividerDataHolder().apply { tag = DIVIDER }
    }
}
