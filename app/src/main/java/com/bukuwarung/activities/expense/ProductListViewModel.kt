package com.bukuwarung.activities.expense

import android.os.Parcelable
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.expense.adapter.dataholder.BaseProductDataHolder
import com.bukuwarung.activities.expense.adapter.dataholder.ProductDataHolder
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base_android.utils.SingleLiveEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.dao.ProductDao
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.domain.product.ProductUseCase
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.AppIdGenerator
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.wrapper.EventWrapper
import kotlinx.coroutines.launch
import javax.inject.Inject

class ProductListViewModel @Inject constructor(
        private val productUseCase: ProductUseCase,
        private val productDao: ProductDao, // TODO use repo / usecase instead of DAO
        private val featurePrefManager: FeaturePrefManager = FeaturePrefManager.getInstance()
) : BaseViewModel() {
    val liveDataMerger = SingleLiveEvent<List<BaseProductDataHolder>>()
    private val bookId = User.getBusinessId()
    private val _productEntities = productUseCase.getProductByBookLive(bookId)
    private var _productList = mutableListOf<BaseProductDataHolder>()
    private var previousProducts = listOf<ProductDataHolder>()
    private val selectedProduct = mutableListOf<ProductDataHolder>()

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private var isFromExpense : Boolean = false
    private var from:String? = null


    var noSellingPriceCoachMarkDisplayed = false
    sealed class State {
        object NewProductCreated : State()
        object ProductUpdated : State()
        data class ProductDeletable(val isNotDeletable: Boolean, val dataHolder: ProductDataHolder) : State()
        object ProductDeleted : State()
        data class OnFinalizeProductChoice(val data: Array<TransactionItemDto>) : State()
        object TutorCreateProduct : State()
        object ShowNoSellingPriceCoachMark : State()
        data class ShowEditScreen(val productId: String?) : State()
    }

    sealed class Event {
        data class OnCreateView(val existingProducts: Array<Parcelable>?, val fromEdit: Boolean = false, val isExpense: Boolean = false) : Event()
        data class FilterProduct(val query: String) : Event()
        data class CreateNewProduct(val productName: String) : Event()
        data class UpdateProduct(val dataHolder: ProductDataHolder) : Event()
        data class DeleteProduct(val dataHolder: ProductDataHolder) : Event()
        data class ToggleProductChecked(val dataHolder: ProductDataHolder) : Event()
        data class CheckNewProduct(val productEntity: ProductEntity, val from: String?) : Event()
        data class CheckNewProductId(val productId: String) : Event()
        data class CheckProductIsDeletable(val dataHolder: ProductDataHolder) : Event()
        object FinalizeProductChoice : Event()
        object ShowProductNameTutorial : Event()
        data class ProductSellingPriceTutorialClicked(val dismiss: Boolean) : Event()

    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnCreateView -> handleOnCreateView(event.existingProducts, event.fromEdit, event.isExpense)
            is Event.CreateNewProduct -> createProduct(event.productName)
            is Event.FilterProduct -> filterProduct(event.query)
            is Event.UpdateProduct -> updateProduct(event.dataHolder)
            is Event.DeleteProduct -> deleteProduct(event.dataHolder)
            is Event.ToggleProductChecked -> updateDataHolder(event.dataHolder)
            Event.FinalizeProductChoice -> selectedProductsAsTransaction()
            is Event.CheckProductIsDeletable -> checkProductDeletable(event.dataHolder)
            Event.ShowProductNameTutorial -> if (!OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTOR_CREATE_PRODUCT)) {
                setState(State.TutorCreateProduct)
            }
            is Event.CheckNewProduct -> {
                val dataHolder = ProductDataHolder(isChecked = true, isEditShow = true, quantity = 1.0, productEntity = event.productEntity)
                from = event.from
                selectedProduct.add(dataHolder)
                sortList()
            }
            is Event.ProductSellingPriceTutorialClicked -> {
                noSellingPriceCoachMarkDisplayed = true
                featurePrefManager.setProductCoachMarkForNoSellingPriceDisplayed(true)
                if(!event.dismiss) {
                    // open edit screen
                    if(!_productEntities.value.isNullOrEmpty())
                    setState(State.ShowEditScreen(_productEntities.value!![0].productId))
                }
            }
            is Event.CheckNewProductId -> {
                val product = productDao.getProductById(event.productId)
                val dataHolder = ProductDataHolder(isChecked = true, isEditShow = true, quantity = 1.0, productEntity = product)
                _productList.add(dataHolder)
                sortList()
            }
        }
    }

    private fun handleOnCreateView(existingProducts: Array<Parcelable>?, fromEdit: Boolean, isExpense: Boolean) {
        this.isFromExpense = isExpense
        if (existingProducts != null) {
            noSellingPriceCoachMarkDisplayed = featurePrefManager.productCoachMarkForNoSellingPriceDisplayed()
        }

        liveDataMerger.removeSource(_productEntities)

        liveDataMerger.addSource(_productEntities) {
            val list = it.map { productEntity -> ProductDataHolder(productEntity = productEntity) }.toMutableList()
            if (isFromExpense) {
                _productList = _productList.union(list).filterIsInstance<ProductDataHolder>()
                        .distinctBy { dataHolder ->
                            dataHolder.productEntity?.productId
                        }
                        .toMutableList()
            } else {
                selectedProduct.forEach {
                    val toBeChanged = list.find { pdh -> pdh.productEntity?.productId == it.productEntity?.productId }
                            ?: return@forEach
                    list.remove(toBeChanged)
                    toBeChanged.apply {
                        quantity = it.quantity
                        isChecked = it.isChecked
                        isEditShow = true
                    }

                    list.add(toBeChanged)
                }

                _productList = _productList.union(list).filterIsInstance<ProductDataHolder>()
                    .distinctBy { dataHolder ->
                        dataHolder.productEntity?.productId
                    }
                    .toMutableList()
            }

            existingProducts?.let { array ->
                array.toList().forEach { parcel ->
                    parcel as TransactionItemDto
                    val dataHolder = list.find { pdh -> pdh.productEntity?.productId == parcel.productId }?.apply {
                        isChecked = parcel.quantity > 0
                        quantity = parcel.quantity
                        isEditShow = true
                        tag = BaseProductDataHolder.CHECKED
                    } ?: return@forEach

                    if (fromEdit && dataHolder.productEntity?.trackInventory == 2) _productList.add(dataHolder)
                    updateDataHolder(dataHolder)
                }
            }

            sortList()

            if (!noSellingPriceCoachMarkDisplayed && it.isNotEmpty()) {
                var display = true
                it.forEach { productEntity ->
                    if (productEntity.unitPrice > 0) {
                        display = false
                    }
                }
                if (display) {
                    setState(State.ShowNoSellingPriceCoachMark)
                } else {
                    noSellingPriceCoachMarkDisplayed = true
                    featurePrefManager.setProductCoachMarkForNoSellingPriceDisplayed(true)
                }
            }
        }
    }

    private fun updateDataHolder(dataHolder: ProductDataHolder) {
        val product = findProduct(dataHolder) ?: return

        selectedProduct.apply {
            if (contains(product)) {
                remove(product)
            }
            add(dataHolder)
        }

        setList(product)
    }

    private fun findProduct(dataHolder: ProductDataHolder) =
            _productList.filterIsInstance<ProductDataHolder>()
                    .find { it.productEntity?.productId == dataHolder.productEntity?.productId }

    private fun createProduct(productName: String) = launch {
        val product = ProductEntity(User.getBusinessId(), AppIdGenerator.resourceUUID(), productName).also {
            EntityHelper.fillProductMetadata(it)
        }
        _productList.add(ProductDataHolder(true, false, 1.0, product).apply { tag = BaseProductDataHolder.CHECKED })
        productUseCase.insertProduct(product)

        product.code = Utility.generateProductCode(product.name)

        setState(State.NewProductCreated)
    }

    private fun filterProduct(query: String) {
        if (query.isEmpty()) {
            sortList()
            return
        }

        val tempProductList = _productList.filter {
            try {
                it as ProductDataHolder
                it.productEntity?.name?.contains(query, ignoreCase = true) ?: false
            } catch (ex: Exception) {
                false
            }
        }.toMutableList()

        sortList(tempProductList)
    }

    private fun selectedProductsAsTransaction() {
        val finalList = getCheckedProductAsList().sortedBy { it.sellingPrice }.sortedBy { it.productName }.toTypedArray()
        var quantityType = AnalyticsConst.INTEGER
        for (product in finalList) {
            if (Utility.getQuantityTypeFromTotalStock(product.quantity) == AnalyticsConst.DECIMAL) {
                quantityType = AnalyticsConst.DECIMAL
            }
        }

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.TOTAL_PRODUCT, finalList.size)
        propBuilder.put(AnalyticsConst.QUANTITY_TYPE, quantityType)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_SAVE, propBuilder)
        setState(State.OnFinalizeProductChoice(finalList))
    }

    private fun getCheckedProductAsList(): Array<TransactionItemDto> {
        return _productList.filterIsInstance<ProductDataHolder>()
                .filter { it.tag == BaseProductDataHolder.CHECKED }
                .map {
                    TransactionItemDto().apply {
                        productId = it.productEntity?.productId
                        productName = it.productEntity?.name
                        quantity = it.quantity
                        buyingPrice = it.productEntity?.buyingPrice
                        sellingPrice = it.productEntity?.unitPrice
                        measurementUnit = it.productEntity?.measurementName
                    }
                }
                .toTypedArray()
    }


    private fun sortList(listToSort: MutableList<BaseProductDataHolder> = _productList) {
        /*  val hasDivider = listToSort.find { it.tag == BaseProductDataHolder.DIVIDER } != null
          if (!hasDivider) listToSort.add(ProductDividerDataHolder.create())*/
        val finalList = listToSort
                .sortedWith(
                        compareBy {
                            if (it is ProductDataHolder) {
                                it.productEntity?.name
                            } else {
                                null
                            }
                        }
                )
                .toMutableList()

        liveDataMerger.value = finalList
    }

    private fun setList(product: ProductDataHolder, listToSort: MutableList<BaseProductDataHolder> = _productList) {
        /*  val hasDivider = listToSort.find { it.tag == BaseProductDataHolder.DIVIDER } != null
          if (!hasDivider) listToSort.add(ProductDividerDataHolder.create())*/
        listToSort.remove(product)
        listToSort.add(0, product)

        liveDataMerger.value = listToSort
    }

    private fun updateProduct(dataHolder: ProductDataHolder) = launch {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAILS_EDIT)

        productUseCase.updateProduct(dataHolder.productEntity)
        setState(State.ProductUpdated)
    }


    private fun checkProductDeletable(dataHolder: ProductDataHolder) {
        val isNotDeletable = productUseCase.checkProductUsedForTransaction(dataHolder.productEntity?.productId)
        setState(State.ProductDeletable(isNotDeletable, dataHolder))
    }

    private fun deleteProduct(dataHolder: ProductDataHolder) = launch {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAILS_DELETE)

        val productToDelete = findProduct(dataHolder) ?: return@launch
        val productEntity = productToDelete.productEntity ?: return@launch
        _productList.remove(productToDelete)
        productUseCase.deleteProduct(productEntity)
        setState(State.ProductDeleted)
    }

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }
}