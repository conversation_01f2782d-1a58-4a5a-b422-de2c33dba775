package com.bukuwarung.activities.expense.detail

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import androidx.lifecycle.observe
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.expense.NewCashTransactionActivity
import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.activities.expense.category.CategoryUtil
import com.bukuwarung.activities.expense.dialog.DeleteTrxDialog
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.invoice.InvoiceSettingActivity
import com.bukuwarung.activities.pos.PosActivity
import com.bukuwarung.activities.print.BluetoothPrinter
import com.bukuwarung.activities.print.CashTransactionOldReceipt
import com.bukuwarung.activities.print.CashTransactionReceipt
import com.bukuwarung.activities.print.InvoiceDataBlock
import com.bukuwarung.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.activities.print.setup.SetupPrinterActivity
import com.bukuwarung.activities.profile.update.BusinessProfileFormViewModel
import com.bukuwarung.activities.referral.main_referral.ReferralSharingReceiver
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity
import com.bukuwarung.activities.transaction.customer.ProfileCompletionDialog
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.base_android.extensions.observeEvent
import com.bukuwarung.base_android.extensions.shareText
import com.bukuwarung.base_android.extensions.showAlertDialog
import com.bukuwarung.bluetooth_printer.utils.PermissionCallback
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.NOTA_STANDARD_ENABLED
import com.bukuwarung.constants.AnalyticsConst.SMS_CHECKBOX_ENABLED
import com.bukuwarung.constants.AnalyticsConst.TRANSAKSI
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.*
import com.bukuwarung.constants.FirestoreConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.*
import com.bukuwarung.database.entity.extension.getCustomerForInvoice
import com.bukuwarung.database.repository.*
import com.bukuwarung.datasync.restore.DataRestoreDialog
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.dialogs.printer.OpenSetupPrinterDialog
import com.bukuwarung.dialogs.printer.PrintingDialog
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType
import com.bukuwarung.payments.PaymentHistoryDetailsActivity
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.bottomsheet.BankAccountListBottomSheetFragment
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PaymentCollection
import com.bukuwarung.payments.data.model.PaymentCollectionInfo
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.SALDO_BNPL
import com.bukuwarung.payments.pin.NewPaymentPinActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import dagger.android.AndroidInjection
import kotlinx.android.synthetic.main.activity_cash_transaction_detail.*
import kotlinx.android.synthetic.main.bottom_share_invoice_layout.view.*
import kotlinx.android.synthetic.main.cash_receipt_layout.*
import kotlinx.android.synthetic.main.cash_receipt_layout.view.*
import kotlinx.android.synthetic.main.layout_product_for_receipt.view.*
import kotlinx.android.synthetic.main.transaction_receipt_header.*
import kotlinx.android.synthetic.main.transaction_receipt_layout.*
import java.util.*
import javax.inject.Inject
import kotlin.math.absoluteValue

class CashTransactionDetailActivity : AppActivity(), BankAccountListBottomSheetFragment.BtSheetBankAccountListener, OnboardingWidget.OnboardingWidgetListener {

    internal lateinit var viewModel: CashTransactionDetailViewModel
        private set

    internal lateinit var handler: Handler
    private var handlerPaymentInfo: Handler? = null
    private var infoType = 0

    @Inject
    lateinit var businessProfileFormViewModel: BusinessProfileFormViewModel

    internal var suppliedAppConfigManager: AppConfigManager? = null // for test only

    // these global objects just for printing only
    private var cashEntity: CashTransactionEntity? = null

    //edit flag is requried to refresh income expense tab for reflecting edited values
    private var isEdit: Boolean = false
    private var trxProducts: List<TransactionItemDto>? = null
    private var printingDialog: PrintingDialog? = null
    private var isRedirectedFromCashTransaction: Boolean = false
    private var isExpense: Boolean = true
    private var isDetailTransaction: Boolean = false
    private var isAutoRecordTxn: Boolean = false
    private var trxStatus: Int = 1
    private val showOldForm: Boolean by lazy { RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm() }
    private val isExitDialogEnabled: Boolean by lazy { RemoteConfigUtils.shouldShowExitDialog() }
    private var mobileNumber: String? = null
    private var analyticsType: String = ""
    private var isRedirectedFromPosMode = false
    private var onboardingWidget: OnboardingWidget? = null
    private var invoiceData: InvoiceDataBlock? = null

    private fun initViewModel() {
        val trxId: String = intent.getStringExtra(TRX_ID_PARAM) ?: "-"

        val factory = CashTransactionDetailViewModelFactory(this, CashRepository.getInstance(this), CustomerRepository.getInstance(this), TransactionRepository.getInstance(this), trxId)

        this.viewModel = ViewModelProviders.of(this, factory)
            .get(CashTransactionDetailViewModel::class.java)
    }

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        setContentView(R.layout.activity_cash_transaction_detail)
        AndroidInjection.inject(this)
        initViewModel()
        handler = Handler()

        setupView()
    }

    /**
     * A hack because we havent implemented DI in project.
     */
    internal fun setViewModel(newViewModel: CashTransactionDetailViewModel) {
        viewModel?.stateData?.removeObservers(this)
        this.viewModel = newViewModel
        observeData()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == Activity.RESULT_OK) {
            val bankAccount = data?.getParcelableExtra(AddBankAccountActivity.BANK_ACCOUNT) as? BankAccount
            when (requestCode) {
                EDIT_TRX_REQUEST_CODE -> {
                    data?.getStringExtra(TRX_ID_EDIT_PARAM)?.let { newTrxId ->
                        viewModel?.setTransactionId(newTrxId)
                    }

                    isExpense = data?.getBooleanExtra("isExpense", isExpense) ?: isExpense
                    if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                        cash_receipt.setup {
                            trxType { isExpense }
                        }
                    } else {
                        cash_receipt_old.setupOldReceipt {
                            trxType { isExpense }
                        }
                    }
                }
                SetupPrinterActivity.RECORD_DETAIL -> startPrinting()
                RC_ADD_BANK -> viewModel.createInDisbursement(bankAccount) // after adding bank account, directly create payment
                RC_PIN_SELECT -> viewModel.createInDisbursement() // pin return no data, but there should be temp data in vm already
                RC_PIN_ADD_BANK -> startAddBankAccount(viewModel.bookId)
            }
        } else if (requestCode == RC_PIN_SELECT) {
            viewModel.temporarySelectBankAccount(null) // reset temp data
        }
        if (requestCode == EDIT_TRX_REQUEST_CODE) {
            isEdit = true
        }
    }

    override fun onBackPressed() {
        if (onboardingWidget != null && onboardingWidget!!.isShown) {
            onboardingWidget!!.dismiss(false, false, true)
        } else if (FeaturePrefManager.getInstance().stockTabEnabled() && !FeaturePrefManager.getInstance().stockTabVisible()) {
            FeaturePrefManager.getInstance().stockTabVisible(true)
        }
        when {
            isRedirectedFromPosMode -> {
                trackPosModeOpenEvent()
                openPosMode()
            }
            else -> {
                MainActivity.startActivitySingleTopToTab(this, TabName.TRANSACTION)
            }
        }
    }

    private fun setupView() {
        var invoiceDataBlock: String = RemoteConfigUtils.INVOICE_DATA.getInvoiceDataBlock()
        val type = object : TypeToken<InvoiceDataBlock>() {}.type
        val gson: Gson = GsonBuilder().create()
        try {
            invoiceData = gson.fromJson(invoiceDataBlock, type)
        } catch (e: JsonSyntaxException) {
            invoiceDataBlock = RemoteConfigUtils.INVOICE_DATA.getInvoiceDataFailsafeBlock()
            invoiceData = gson.fromJson(invoiceDataBlock, type)
        }
        if (!RemoteConfigUtils.getEnableNotaMission()) {
            banner_container.visibility = View.GONE
        }

        Glide.with(this).load(RemoteConfigUtils.getNotesMissionBannerImageUrl())
            .placeholder(R.color.white)
            .into(img_mission_banner)

        if (FeaturePrefManager.getInstance().hasEnableBukuWatermark()) {
            bukuwarung_watermark_layout.visibility = View.VISIBLE
        } else {
            bukuwarung_watermark_layout.visibility = View.GONE
        }

        val notesMissionSteps = getNotesMissionSteps()
        val bookEntity = BusinessRepository.getInstance(this).getBusinessByIdSync(SessionManager.getInstance().businessId)
        val businessProfileCompletionProgress = Utility.calculateCompletionPercentage(bookEntity)
        if (businessProfileCompletionProgress == 100) {
            FeaturePrefManager.getInstance().hasCompleteBusinessProfile(true)
        }

        if (FeaturePrefManager.getInstance()
                .getFeatureCompletionById(notesMissionSteps.get(1).featureId)
        ) {
            if (FeaturePrefManager.getInstance()
                    .getFeatureCompletionById(notesMissionSteps.get(0).featureId)
            ) {
                if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                    cash_receipt_old.banner_container.visibility = View.GONE
                } else {
                    cash_receipt.banner_container.visibility = View.GONE
                }
            }
        }

        if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
            cash_receipt.banner_container.setOnClickListener {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.NOTA)
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PROFILE_MISSION_ENTRY_POINT_CLICK,
                    propBuilder
                )
            }
        } else {
            cash_receipt_old.banner_container.setOnClickListener {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.NOTA)
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PROFILE_MISSION_ENTRY_POINT_CLICK,
                    propBuilder
                )
            }
        }

        if (intent.hasExtra(IS_REDIRECTED_FROM_POS_MODE)) {
            isRedirectedFromPosMode = intent.getBooleanExtra(IS_REDIRECTED_FROM_POS_MODE, false)
        }

        viewModel.init()

        closeBtn.setOnClickListener {
            onBackPressed()
        }

        copy_img.setOnClickListener {
            Utility.copyToClipboard(trx_no_txt.text.toString(), this@CashTransactionDetailActivity, getString(R.string.trx_no_copied))
        }

        closebtnCross.setOnClickListener {
            if (isRedirectedFromPosMode) {
                trackPosModeOpenEvent()
                openPosMode()
            } else {
                onBackPressed()
            }
        }
        layout_bottom_container.tv_open_form.text = getString(R.string.new_trx)
        layout_bottom_container.tv_open_form.setOnClickListener {
            if (!isRedirectedFromPosMode) {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.INVOICE)
                propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled)
                propBuilder.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsTransaction())
                propBuilder.put(NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, propBuilder)
            }

            FeaturePrefManager.getInstance().invoiceUseCount = FeaturePrefManager.getInstance().invoiceUseCount + 1

            if (isRedirectedFromPosMode) {
                trackPosModeOpenEvent()
                openPosMode()
                return@setOnClickListener
            }

            val intent =
                NewCashTransactionActivity.createIntent(this, isExpense, AppConfigManager.getInstance().getTransactionType())
            startActivity(intent)
            finish()
        }

        observeData()
        if (intent.hasExtra("isRedirectedFromCashTransaction"))
            isRedirectedFromCashTransaction = intent.getBooleanExtra("isRedirectedFromCashTransaction", false)
        if (isRedirectedFromCashTransaction) {
            toolBar.visibility = View.VISIBLE
            app_bar.visibility = View.GONE
            transaction_summary.visibility = View.GONE
            btn_invoice_preference.visibility = View.GONE
            content_root.setBackgroundColor(getColorCompat(R.color.white))
            // TODO set whole background to white
            if (Utility.hasInternet()) {
                tvOffline.visibility = View.GONE
            } else {
                tvOffline.visibility = View.VISIBLE
            }
        }

        if (intent.hasExtra(IS_EXPENSE_PARAM)) {
            isExpense = intent.getBooleanExtra(IS_EXPENSE_PARAM, false)
        }

        if (intent.hasExtra(TRX_STATUS_PARAM)) {
            trxStatus = intent.getIntExtra(TRX_STATUS_PARAM, 1)
        }

        if (intent.hasExtra(IS_DETAIL_TRANSACTION)) {
            isDetailTransaction = intent.getBooleanExtra(IS_DETAIL_TRANSACTION, false)
        }

        if (intent.hasExtra(IS_AUTO_RECORD_TRANSACTION)) {
            isAutoRecordTxn = intent.getBooleanExtra(IS_AUTO_RECORD_TRANSACTION, false)
        }

        btn_invoice_preference.setOnClickListener {
            val eventProp = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CASH_TRANSACTION)
            AppAnalytics.trackEvent(AnalyticsConst.INVOICE_SETTING_OPEN, eventProp)
            startActivity(InvoiceSettingActivity.createIntent(this))
        }
        btn_create_payment.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_TRANSACTION_TAGIH_INWARDS)
            viewModel.onCreatePaymentButtonClicked()
        }
        reload_btn.setOnClickListener {
            viewModel.onRetry()
        }
    }

    private fun observeData() {
        viewModel.stateData.observe(this, Observer { state ->
            showBasedOnState(state)
        })
        viewModel.alert.observeEvent(this, Observer {
            showAlertDialog(it)
        })

        viewModel.observeEvent.observe(this) {
            when (it) {
                is PaymentEvent.OnInitialCreatePayment -> {
                    if (it.list.isEmpty()) {
                        startAddBankAccount(it.bookId)
                    } else {
                        val bankListBtSheet = BankAccountListBottomSheetFragment.createBankAccountInstance(AnalyticsConst.CASH_TRANSACTION, false)
                        bankListBtSheet.show(supportFragmentManager, "bankListBtSheet")
                    }
                }
                is PaymentEvent.ShowPendingPaymentDetail -> showPaymentDetail(it.url, it.paymentId, it.expiredAt, it.bankName, it.accountNumber, it.accountName, it.orderResponse)
                is PaymentEvent.ShareInvoiceUnpaid -> onShareButtonClicked(it.bookEntity, it.customerName, it.amount, it.url, it.prop, it.useWhatsapp)
                PaymentEvent.ResetPayment -> {
                    unpaid_payment_query_group.visibility = View.VISIBLE
                    payment_others_group.visibility = View.VISIBLE
                    payment_detail_group.visibility = View.GONE
                    error_state_group.visibility = View.GONE
                    loading_state_group.visibility = View.GONE
                }
                PaymentEvent.ShowPaymentTooltip -> {
                    onboardingWidget = OnboardingWidget.createInstance(
                        this, this,
                        OnboardingPrefManager.TUTORIAL_LINK_UNPAID_CASH_TO_PAYMENT, payment_layout, R.drawable.onboarding_announce, getString(R.string.new_feature),
                        getString(R.string.onboarding_connect_unpaid_to_payment), "",
                        FocusGravity.CENTER, ShapeType.RECTANGLE_FULL, 1, 1, sendAnalytics = true, sendAnalyticsOnDismiss = true, delay = 0
                    )
                }
                is PaymentEvent.PaymentInCreated -> {
                    viewModel.init()
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_REQUEST_CREATED, it.prop)
                }
                is PaymentEvent.OnTransactionDeleted -> onTransactionDeleted(it.transactionId)
                is PaymentEvent.SetFinproResponse -> if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                    cash_receipt.setFinproOrder(it.response)
                } else cash_receipt_old.setFinproOrder(it.response)
                is PaymentEvent.SetPaymentInfo -> setPaymentInfoForPaid(it.show, it.isPaid, it.fee)
            }
        }
        viewModel.observeTrxEvent.observe(this) {
            when (it) {
                is TransactionEvent.OnMarkAsPaidFinished -> {
                    val prop = AppAnalytics.PropBuilder()
                    prop.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION_ENTRY_POINT)
                    if (it.transactionId != null)
                        prop.put(AnalyticsConst.TRANSACTION_ID, it.transactionId)
                    if (it.amount != null)
                        prop.put(AnalyticsConst.AMOUNT, it.amount)
                    if (it.orderId != null)
                        prop.put(AnalyticsConst.ORDER_ID, it.orderId)
                    prop.put(AnalyticsConst.MODE, it.mode)
                    // prop.put(AnalyticsConst.REMAINING_UTANG,customerTransactionSummaryDto?.remaining)
                    prop.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsTransaction())
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_SETTLE_CONFIRM, prop)
                    isEdit = true
                    payment_layout.hideView()
                }
            }
        }


    }

    private fun setPaymentInfoForPaid(show: Boolean, isPaid: Boolean, fee: String) {
        if (!show) {
            payment_info.hideView()
            payment_others_group.hideView()
            return
        }
        if (isPaid) {
            payment_info.showView()
            payment_others_group.hideView()
            payment_info.setFeeStatus(fee)
        } else {
            payment_info.hideView()
            payment_others_group.showView()
        }
    }

    override fun onPause() {
        super.onPause()
        handlerPaymentInfo?.removeCallbacksAndMessages(null)
        handlerPaymentInfo = null
    }

    private fun showLoadingPaymentViews() {
        payment_detail_group.visibility = View.GONE
        unpaid_payment_query_group.visibility = View.GONE
        payment_others_group.visibility = View.GONE
        payment_info.visibility = View.GONE
        error_state_group.visibility = View.GONE
        loading_state_group.visibility = View.VISIBLE
        ll_info.visibility = View.GONE
    }

    private fun showErrorPaymentViews() {
        unpaid_payment_query_group.visibility = View.GONE
        payment_detail_group.visibility = View.GONE
        payment_others_group.visibility = View.GONE
        payment_info.visibility = View.GONE
        error_state_group.visibility = View.VISIBLE
        loading_state_group.visibility = View.GONE
        ll_info.visibility = View.GONE
    }

    private fun showPaymentDetail(
        url: String, paymentId: String, expiredAt: String?,
        bankName: String?, accountNumber: String?, accountName: String?,
        orderResponse: FinproOrderResponse
    ) {
        if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
            cash_receipt.setFinproOrder(orderResponse)
        } else {
            cash_receipt_old.setFinproOrder(orderResponse)
        }
        unpaid_payment_query_group.visibility = View.GONE
        payment_detail_group.visibility = View.VISIBLE
        payment_info.visibility = View.GONE
        // hide edit but show delete
        editBtn.visibility = View.GONE
        error_state_group.visibility = View.GONE
        deleteBtn.visibility = View.VISIBLE
        payment_id_txt.text = paymentId
        try {
            val expiryDate = DateTimeUtils.getDateFromPaymentUTC(expiredAt)
            if (expiryDate < Date()) {
                expired_title_txt.text = getString(R.string.active_link_expired)
                share_payment_btn.text = getString(R.string.share_new_link)
            } else {
                val expiryString = DateTimeUtils.getFormattedLocalDateTimeForPayment(expiredAt)
                expired_title_txt.text = getString(R.string.active_link_until, expiryString)
            }
        } catch (e: Exception) {
            expired_title_txt.text = ""
        }
        copy_img2.setOnClickListener {
            Utility.copyToClipboard(payment_id_txt.text.toString(), this, getString(R.string.trx_no_copied))
        }
        payment_account_txt.text = Utility.dashDividedString(bankName, accountNumber)
        bank_account_name_txt.text = accountName
        val adminFee = orderResponse.fee ?: ZERO_DOUBLE
        if (adminFee > 0.0) {
            admin_fee_txt.text = Utility.formatAmount(adminFee)
            admin_fee_txt.visibility = View.VISIBLE
            admin_fee_title_txt.visibility = View.VISIBLE
        } else {
            admin_fee_txt.visibility = View.GONE
            admin_fee_title_txt.visibility = View.GONE
        }
        link_et.setText(url)
        share_payment_btn.setOnClickListener {
            viewModel.shareInvoice()
        }
    }

    private fun startAddBankAccount(bookId: String?) {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION), true, false, false)
        startActivityForResult(AddBankAccountActivity.createIntent(this, PaymentConst.TYPE_PAYMENT_IN.toString(), bookId, "", hasBankAccount = "true", paymentIn = true), RC_ADD_BANK)
    }

    // this function is called wrt to ppob
    private fun uiChangesForReceipt(state: CashTransactionDetailState) {
        if (intent.getBooleanExtra(IS_PAYMENT_DETAIL_PPOB, false)) {
            deleteBtn.hideView() //hiding the delete option in case of ppob
            editBtn.showView()
        } else {
            editBtn.hideView() //hiding the edit option in case of ppob from cash transaction
            deleteBtn.showView()
        }
        note_label.visibility = View.VISIBLE
        tv_sender.visibility = View.VISIBLE
        tv_sender_name.visibility = View.VISIBLE
        payment_line.visibility = View.VISIBLE
        tv_payment_safe_message.visibility = View.VISIBLE
        payment_divider.visibility = View.VISIBLE
        val bookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
        tv_sender_name.text = bookEntity.businessOwnerName
        val ppobCategory = state.ppobItem?.beneficiary?.category
        if (PpobConst.CATEGORY_EWALLET == ppobCategory) {
            layout_payment_catatan.visibility = View.GONE
            tv_note.visibility = View.VISIBLE
        } else {
            layout_payment_catatan.visibility = View.VISIBLE
            tv_note.visibility = View.GONE
        }
        if (PpobConst.CATEGORY_LISTRIK == ppobCategory || PpobConst.CATEGORY_TRAIN_TICKET == ppobCategory) {
            if (PpobConst.CATEGORY_PLN_POSTPAID == state.ppobItem.beneficiary.code) {
                layout_payment_catatan.hideView()
                tv_note.showView()
            } else {
                ppob_message.showView()
                tv_pdt_name.text = if (PpobConst.CATEGORY_TRAIN_TICKET == ppobCategory) {
                    getString(R.string.code_booking)
                } else {
                    getString(R.string.token_code)
                }
                tv_number.text = state.ppobItem.details.token
            }
        } else if (PpobConst.getListOfTypesWithNotes().contains(ppobCategory)) {
            layout_payment_catatan.hideView()
            tv_note.showView()
        } else if (ppobCategory == PpobConst.CATEGORY_VOUCHER_GAME && state.ppobItem?.details?.voucherCode.isNotNullOrBlank()) {
            layout_payment_catatan.showView()
            layout_payment_catatan.setBackgroundColor(ContextCompat.getColor(this, R.color.alice_blue))
            tv_pdt_name.text = getString(R.string.code_voucher)
            tv_number.text = state.ppobItem?.details?.voucherCode ?: ""
            note_group.showView()
        } else {
            tv_pdt_name.text = state.pdtName
            tv_number.text = Utility.beautifyPhoneNumber(state.mobileNumber)
        }
        if (mobileNumber.isNotNullOrEmpty())
            tv_customer_phone.text = Utility.beautifyPhoneNumber(mobileNumber)
        else
            tv_customer_phone.text = state.mobileNumber
        tv_trx_date.text = DateTimeUtils.getFormattedLocalDateTimeForPayment(state.pulsaUpdatedAt)

        layout_payment_details.visibility = View.VISIBLE
        trx_no_title_txt.text =
            if (state.payment?.paymentMethod?.code == SALDO_BNPL) getString(R.string.payment_code)
            else getString(R.string.trx_no)
        trx_no_txt.text = state.paymentId
        bank_txt.text = state.payment?.paymentMethod?.name
        product_name_txt.text = state.pdtName

    }

    private fun showBasedOnState(state: CashTransactionDetailState) {
        when (state.currentState) {
            CashTransactionDetailStateType.Loading -> {
                loadingContainer.visibility = View.VISIBLE
                notFoundContainer.visibility = View.GONE
                mainContainer.visibility = View.GONE
                editBtn.visibility = View.GONE
                deleteBtn.visibility = View.GONE
            }
            CashTransactionDetailStateType.NotFound -> {
                loadingContainer.visibility = View.GONE
                notFoundContainer.visibility = View.VISIBLE
                mainContainer.visibility = View.GONE
                editBtn.visibility = View.GONE
                deleteBtn.visibility = View.GONE

                handler.postDelayed({
                    finish()
                }, 1000)
            }
            CashTransactionDetailStateType.Loaded -> {
                loadingContainer.visibility = View.GONE
                notFoundContainer.visibility = View.GONE
                mainContainer.visibility = View.VISIBLE
                editBtn.visibility = if (isAutoRecordTxn == true) View.GONE else View.VISIBLE
                deleteBtn.visibility = View.VISIBLE
                state.transaction?.let {
                    ll_info.visibility = (it.status == BELUM_LUNAS).asVisibility()
                    top_divider.visibility = (it.status == BELUM_LUNAS).asVisibility()
                }
                state.transaction?.let {
                    cashTransactionEntity = it
                    if(it.restoreTransactionItems == 1){
                        val dataRestoreDialog = DataRestoreDialog(this, this,it.cashTransactionId,FirestoreConst.COLLECTION_TRANSACTION_ITEM_STORE,"cashTransactionId")
                        dataRestoreDialog.show()
                    }
                    val transactionType = if ((it.amount ?: -1.0) >= 0) {
                        1
                    } else {
                        -1
                    }
                    top_divider.visibility = (it.status == BELUM_LUNAS).asVisibility()

                    val imageTransaksi = cashTransactionEntity?.attachments

                    if (!RemoteConfigUtils.showTransksiImage() || imageTransaksi.isNullOrEmpty()) {
                        img_transaksi.hideView()
                        tv_upload_photo.hideView()
                    }

                    imageTransaksi?.let {
                        Glide.with(this).load(it).into(img_transaksi)
                    }

                    if (!isRedirectedFromCashTransaction) {
                        transaction_summary.visibility = (it.buyingPrice ?: 0.0 > 0).asVisibility()
                    }

                    img_transaksi.setOnClickListener {
                        showImageDialog(cashTransactionEntity?.attachments)
                    }


                    setTopViewBasedOnTrxType(transactionType, it)
                    //                    setTextData(it, transactionType)
                    populateReceipt(it)

                    cashEntity = it
                    var customerEntity: CustomerEntity? = null
                    if (it.customerTransactionId.isNotNullOrEmpty()) {
                        var transactionEntity: TransactionEntity = TransactionRepository.getInstance(this).getTransactionById(it.customerTransactionId)
                            ?: return
                        customerEntity = CustomerRepository.getInstance(this).getCustomerById(transactionEntity.customerId)
                    }
                    if(it.restoreTransactionItems == 0) {
                        val transactionItems = TransactionRepository.getInstance(this)
                            .getTransactionItems(it.cashTransactionId)
                        trxProducts =
                            convertTransactionItemsToDto(it.cashTransactionId, transactionItems)

                        trxProducts?.let { cashTransactionItems = trxProducts!! }
                    }
                    if (it.orderId.isNullOrBlank()) {
                        editBtn.visibility =
                            if (isAutoRecordTxn == true) View.GONE else (AppConst.POS !in it.cashCategoryId).asVisibility()
                    }
                    editBtn.setOnClickListener { _ ->

                        goToEditPage(it.cashTransactionId, it.status, customerEntity?.name, customerEntity?.customerId, Utility.getTransactionTypeInt(it.amount), it.cashCategoryId)
                    }
                    deleteBtn.setOnClickListener { _ -> showDeleteDialog(it.cashTransactionId) }
                    layout_bottom_container.bt_print_invoice.setOnClickListener {
                        var bookEntity: BookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
                        if (!OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.PROFILE_COMPLETION_DIALOG) && !Utility.hasBusinessName(bookEntity.businessName)) {
                            val profileCompletionDialog = ProfileCompletionDialog(this, this, {
                                bookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
                                tvWarungName?.text = bookEntity.businessName ?: ""
                                businessProfileFormViewModel.updateBusinessName(bookEntity.businessName)
                                checkPrintRequirement(transactionType)
                            }, AnalyticsConst.PRINT_CASH_TRX_RECEIPT, false)
                            profileCompletionDialog.show()
                            OnboardingPrefManager.getInstance().setHasFinishedForId(OnboardingPrefManager.PROFILE_COMPLETION_DIALOG)
                        } else {
                            checkPrintRequirement(transactionType)
                        }
                        FeaturePrefManager.getInstance().invoiceUseCount = FeaturePrefManager.getInstance().invoiceUseCount + 1
                    }
                    layout_bottom_container.bt_general_share.setOnClickListener {
                        viewModel.shareInvoice()
                    }
                    layout_bottom_container.bt_share_whatsapp.setOnClickListener {
                        val propBuilder = AppAnalytics.PropBuilder().apply {
                            put(AnalyticsConst.ENTRY_POINT2, TRANSAKSI)
                            put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsTransaction())
                        }
                        AppAnalytics.trackEvent("click_WA_share_receipt", propBuilder)
                        viewModel.shareInvoice(true)
                    }
                    btn_success.setOnClickListener {
                        markAsPaid(state)
                    }
                    btn_mark_paid_payment.setOnClickListener {
                        markAsPaid(state)
                    }
                    mark_paid_payment_btn2.setOnClickListener {
                        markAsPaid(state)
                    }

                    //                    btn_print.visibility = (transactionType != -1).asVisibility()

                    if (state.isPpob) {
                        cash_receipt.setFinproPayments(state.payment)
                        uiChangesForReceipt(state)
                        showPpobPaymentCollectionsUi(state)
                    } else {
                        if (state.showPaymentLoading) showLoadingPaymentViews()
                        else if (state.showPaymentError) showErrorPaymentViews()
                        else if (transactionType == 1) {
                            error_state_group.visibility = View.GONE
                            loading_state_group.visibility = View.GONE
                            payment_layout.visibility = (it.status == BELUM_LUNAS && !state.needKycAndNotVerified).asVisibility()
                            if (it.amount >= RemoteConfigUtils.getMinimumPaymentAmount()) {
                                ll_info.visibility = View.GONE
                                if (it.orderId.isNullOrBlank()) {
                                    unpaid_payment_query_group.visibility = View.VISIBLE
                                    if (it.status == BELUM_LUNAS) {
                                        viewModel.checkPaymentTooltip()
                                    }
                                    payment_detail_group.visibility = View.GONE
                                } else {
                                    unpaid_payment_query_group.visibility = View.GONE
                                    payment_detail_group.visibility = View.VISIBLE
                                }
                            } else {
                                payment_layout.visibility = View.GONE
                                ll_info.visibility = (it.status == BELUM_LUNAS).asVisibility()
                            }
                        } else {
                            error_state_group.visibility = View.GONE
                            ll_info.visibility = (it.status == BELUM_LUNAS).asVisibility()
                            payment_layout.visibility = View.GONE
                        }
                    }
                }
            }
        }
    }

    public fun resumeAfterRestore(transactionId: String?) {
        val transactionItems =
            TransactionRepository.getInstance(this).getTransactionItems(transactionId)
        trxProducts = convertTransactionItemsToDto(transactionId, transactionItems)

            TransactionRepository.getInstance(this).updateItemRestoreStatus(transactionId)
        trxProducts?.let { cashTransactionItems = trxProducts!! }
    }

    private fun showImageDialog(attachment: String?) {
        val customDialog = Dialog(this)
        customDialog.setContentView(R.layout.dialog_image_popup)
        customDialog.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val ivClose = customDialog.findViewById<ImageView>(R.id.iv_close)
        val ivPreview = customDialog.findViewById<ImageView>(R.id.iv_preview_image)

        Glide.with(this).load(attachment).into(ivPreview)

        ivClose.setOnClickListener {
            //Do something here
            customDialog.dismiss()
        }
        customDialog.show()
    }

    private fun onShareButtonClicked(bookEntity: BookEntity?, customerName: String?, amount: String?, url: String?, prop: AppAnalytics.PropBuilder?, useWhatsapp: Boolean) {
        prop?.run {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_SEND_REMINDER, prop, false, false, false)
        }
        FeaturePrefManager.getInstance().invoiceUseCount = FeaturePrefManager.getInstance().invoiceUseCount + 1
        if (!OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.PROFILE_COMPLETION_DIALOG) && !Utility.hasBusinessName(bookEntity?.businessName)) {
            val profileCompletionDialog = ProfileCompletionDialog(this, this, {
                tvWarungName?.text = bookEntity?.businessName ?: ""
                businessProfileFormViewModel.updateBusinessName(bookEntity?.businessName ?: "")
                shareTrx(bookEntity, customerName, amount, url, useWhatsapp)
            }, AnalyticsConst.SHARE_CASH_TRX_RECEIPT, false)
            profileCompletionDialog.show()
            OnboardingPrefManager.getInstance().setHasFinishedForId(OnboardingPrefManager.PROFILE_COMPLETION_DIALOG)
        } else {
            shareTrx(bookEntity, customerName, amount, url, useWhatsapp)
        }
        FeaturePrefManager.getInstance().invoiceUseCount = FeaturePrefManager.getInstance().invoiceUseCount + 1
    }

    private fun markAsPaid(state: CashTransactionDetailState) {
        GenericConfirmationDialog.create(this) {
            titleRes { R.string.is_transaction_paid_question_title }
            bodyRes { R.string.is_transaction_paid_question_body }
            btnRightRes { R.string.yes }
            btnLeftRes { R.string.cancel_selectable }
            rightBtnCallback = {
                viewModel.markAsPaid(if ((state.transactingCustomer?.balance ?: 0.0) > 0) getString(R.string.filter_nil) else getString(R.string.paid_off))
            }
        }.show()
    }

    private fun shareTrx(bookEntity: BookEntity?, customerName: String?, amount: String?, url: String?, useWA: Boolean) {
        generateAndShareViewImage(this, "com.whatsapp", if (RemoteConfigUtils.shouldShowNewPosInvoice()) cash_receipt.receiptLayout else cash_receipt_old.receiptLayout, "", useWA, bookEntity, customerName, amount, url)
    }

    private fun generateAndShareViewImage(context: Context?, packageNm: String?, receiptLayout: View?, mobile: String?, useWA: Boolean, bookEntity: BookEntity?, customerName: String?, amount: String?, url: String?) {
        try {
            val extraText = when {
                customerName != null && amount != null && url == null ->
                    getString(R.string.cash_invoice_unpaid_sharing_text, customerName, amount, bookEntity?.businessName)
                customerName != null && amount != null && url != null ->
                    getString(R.string.cash_invoice_unpaid_payment_sharing_text, customerName, amount, bookEntity?.businessName, bookEntity?.businessName, url)
                else -> getString(R.string.cash_invoice_sharing_text, bookEntity?.businessName)
            }
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(receiptLayout, false)
            val shareLayoutImage = ShareLayoutImage(extraText, context, packageNm, mobile, useWA, true)
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)

            val eventProp = AppAnalytics.PropBuilder()

            if (isRedirectedFromPosMode) {
                eventProp.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.POS)
            } else {
                eventProp.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CASH_TRANSACTION)
            }

            eventProp
                .put(AnalyticsConst.IMAGE_FILLED, !Utility.isBlank(bookEntity?.businessLogo))
                .put(AnalyticsConst.LOCATION_FILLED, !Utility.isBlank(bookEntity?.businessAddress))
                .put(AnalyticsConst.PHONE_FILLED, !Utility.isBlank(bookEntity?.businessPhone))
                .put(AnalyticsConst.AMOUNT, cashEntity?.amount)
                .put(AnalyticsConst.BUYING_MODAL, cashEntity?.buyingPrice)
                .put(AnalyticsConst.CASH_TRANSACTION_ID, cashEntity?.cashTransactionId)
                .put(AnalyticsConst.BUSINESS_NAME, bookEntity?.businessName)
                .put(AnalyticsConst.FRESH_RECEIPT, isRedirectedFromCashTransaction.toString())
                .put(AnalyticsConst.PRODUCT_TYPE, analyticsType)
                .put(AnalyticsConst.ORDER_ID, cashEntity?.orderId)
                .put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())

            AppAnalytics.trackEvent(AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT, eventProp)

        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun checkPrintRequirement(transactionType: Int) {
        val bookEntity: BookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
        val evenProp = AppAnalytics.PropBuilder().put(AnalyticsConst.TYPE, if (transactionType == 1) AnalyticsConst.SALES else AnalyticsConst.EXPENSE)
            .put(AnalyticsConst.BUSINESS_NAME, bookEntity.businessName)
            .put(AnalyticsConst.FRESH_RECEIPT, if (isRedirectedFromCashTransaction) "true" else "false")
            .put(AnalyticsConst.IMAGE_FILLED, bookEntity.businessLogo.isNotNullOrBlank())
            .put(AnalyticsConst.LOCATION_FILLED, bookEntity.businessAddress.isNotNullOrBlank())
            .put(AnalyticsConst.PHONE_FILLED, bookEntity.businessPhone.isNotNullOrBlank())
            .put(AnalyticsConst.AMOUNT, cashEntity?.amount)
            .put(AnalyticsConst.BUYING_MODAL, cashEntity?.buyingPrice)
            .put(AnalyticsConst.CASH_TRANSACTION_ID, cashEntity?.cashTransactionId)
            .put(AnalyticsConst.TYPE, analyticsType)
            .put(AnalyticsConst.ORDER_ID, cashEntity?.orderId)
            .put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())

        if (isRedirectedFromPosMode) {
            evenProp.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.POS)
        }

        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRINT_CASH_TRANSACTION, evenProp)

        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PRINT_CASH_TRANSACTION, this)

        when (val printers: List<PrinterDataHolder>? = PrinterPrefManager(this).installedPrinters) {
            null -> {
                val dialog = OpenSetupPrinterDialog(this) {
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                        AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH)
                    )
                    val intent = Intent(this, SetupPrinterActivity::class.java)
                    startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                }

                dialog.show()
            }
            printers -> {
                when {
                    printers.isEmpty() -> {
                        val dialog = OpenSetupPrinterDialog(this) {
                            AppAnalytics.trackEvent(
                                AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH)
                            )
                            val intent = Intent(this, SetupPrinterActivity::class.java)
                            startActivityForResult(intent, SetupPrinterActivity.RECORD_DETAIL)
                        }

                        dialog.show()
                    }
                    printers.isNotEmpty() -> {
                        startPrinting()
                    }
                }
            }
        }
    }

    private fun startPrinting() {
        cashEntity ?: return
        trxProducts ?: return

        // not-null assertion is ok since both objects have checked using elvis operator
        val entryPoint = when {
            isRedirectedFromPosMode -> {
                AnalyticsConst.POS_NOTE
            }
            isRedirectedFromCashTransaction -> {
                AnalyticsConst.TRANSAKSI_NOTE
            }
            else -> {
                AnalyticsConst.DETAIL_TRANSAKSI_NOTE
            }
        }

        val printer = BluetoothPrinter(this, entryPoint)
        printingDialog = printer.printingDialog
        printer.printPrintables(
            if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
                cash_receipt.getFormattedText()
            } else {
                cash_receipt_old.getFormattedText()
            }, object : PermissionCallback {
                override fun onPermissionRequired(permissions: Array<String>) {
                    printingDialog?.dismiss()
                    permissionLauncher.launch(permissions)
                }
            }
        )
    }

    private val permissionLauncher: ActivityResultLauncher<Array<String>> =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) {
            if (it.containsValue(false)) {
                Toast.makeText(this, R.string.location_permission_denied_message, Toast.LENGTH_SHORT)
                    .show()
            }
        }

    private fun goToEditPage(
        transactionId: String,
        status: Int,
        name: String?,
        customerId: String?,
        transactionType: Int,
        cashCategoryId: String?
    ) {
        val intent =
            NewCashTransactionActivity.createIntent(this).apply {
            putExtra("cashTransactionId", transactionId)
            putExtra("status", status)
            putExtra("customerName", name)
            putExtra("customerId", customerId)
            putExtra(CategoryTransactionsActivity.CASH_CATEGORY_ID, cashCategoryId)
            putExtra("isEdit", true)

            putExtra(NewCashTransactionActivity.TRX_TYPE, transactionType == DEBIT)
        }

        startActivityForResult(intent, EDIT_TRX_REQUEST_CODE)
        finish()
    }

    private fun showDeleteDialog(transactionId: String) {
        val dialog = DeleteTrxDialog(this) {
            if (it) {
                viewModel.deleteAndExpirePayment()
            }
        }
        dialog.show()
    }

    private fun onTransactionDeleted(transactionId: String?) {
        viewModel.stateData.removeObservers(this)
        finish()
        transactionId?.run {
            deleteCashTransaction(this)
        }
    }

    private fun deleteCashTransaction(transactionId: String) {
        val t = Thread(DeleteOrUpdateTransactionRunnable(this, transactionId))
        t.start()
        try {
            t.join()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun setTopViewBasedOnTrxType(transactionType: Int, cashTransactionEntity: CashTransactionEntity) {
        if (transactionType > 0) {
            val amt = cashTransactionEntity.amount.absoluteValue
            val buyingPrice = (cashTransactionEntity.buyingPrice ?: 0.0).absoluteValue
            val margin = amt - buyingPrice

            if (margin > 0) {
                bg_margin.background = resources.getDrawable(R.drawable.profit_bg, null)
                val textColor = ContextCompat.getColor(this@CashTransactionDetailActivity, R.color.in_green)
                tv_is_profit.apply {
                    text = getString(R.string.profit)
                    setTextColor(textColor)
                }
                tv_margin_nominal.setTextColor(textColor)
            } else {
                bg_margin.background = resources.getDrawable(R.drawable.loss_bg, null)
                val textColor = ContextCompat.getColor(this@CashTransactionDetailActivity, R.color.out_red)
                tv_is_profit.apply {
                    text = getString(R.string.loss)
                    setTextColor(textColor)
                }
                tv_margin_nominal.setTextColor(textColor)
            }

            tv_base_price_nominal.setTextOrDefault(
                "${Utility.getCurrency()}${Utility.formatCurrency((cashTransactionEntity.buyingPrice ?: 0.0).absoluteValue)}"
            )

            tv_income_nominal.setTextOrDefault(
                "${Utility.getCurrency()}${Utility.formatCurrency(amt)}"
            )

            tv_margin_nominal.setTextOrDefault(
                "${Utility.getCurrency()}${Utility.formatCurrency(margin)}"
            )
        }
        if (cashTransactionEntity.orderId.isNotNullOrEmpty()) {
            mobileNumber = cashTransactionEntity.customerPhoneNumber
            analyticsType = PpobConst.CATEGORY_ANALYTICS_MAP[(cashTransactionEntity?.cashCategoryId?.substringBefore(":") ?: "")] ?: ""
        } else if (cashTransactionEntity.orderId.isNotNullOrEmpty()) {
            editBtn.visibility = View.GONE//hiding the edit button if the cash trx receipt corresponds to that of payment in or out
        }
    }

    private fun setTextData(cashTransactionEntity: CashTransactionEntity, transactionType: Int) {
        val bookEntity = BusinessRepository.getInstance(this).getBusinessByIdSync(User.getBusinessId())
        tvTransactionNominal.text = "%s%s".format(Utility.getCurrency(), Utility.formatCurrency(cashTransactionEntity.amount))
        tvWarungName.visibility = (!SessionManager.getInstance().isGuestUser).asVisibility()
        tvWarungPhone.visibility = (!SessionManager.getInstance().isGuestUser).asVisibility()
        bookEntity?.let { book ->
            // set book name
            tvWarungName.text = book.businessName
            // set book phone quantity
            tvWarungPhone.text = Utility.beautifyPhoneNumber(book.businessPhone).takeIf { it.isNotBlank() }
                ?: Utility.beautifyPhoneNumber(book.ownerId).takeIf { it.isNotBlank() } ?: "-"
        }
        tvTrxType.text = if (transactionType == 1) {
            getString(R.string.income_label)
        } else {
            getString(R.string.expense_label)
        }

        val cashRepository = CashRepository.getInstance(this)
        val category = cashRepository?.getCashCategoryById(cashTransactionEntity.cashCategoryId)
        tvTransactionCategory.text = category?.name ?: getString(R.string.default_placeholder)

        if (Utility.isBlank(cashTransactionEntity?.description))
            catatanLayout.visibility = View.GONE
        tvTransactionNotes.text = cashTransactionEntity?.description.takeIf { it.isNullOrBlank().isFalse }
            ?: "-"
        tvTransactionDate.text = "%s %s".format(Utility.formatReceiptDate(cashTransactionEntity.date), Utility.getReadableTimeString(cashTransactionEntity.createdAt))

        populateProducts(cashTransactionEntity.cashTransactionId)
    }

    private fun setProduct(cashTransactionEntity: CashTransactionEntity, transactionType: Int) {
        cashTransactionEntity?.cashTransactionId?.let {
            getTransactionProducts(it)
        }
    }

    private fun convertTransactionItemsToDto(
        cashTransactionId: String?,
        transactionItems: List<TransactionItemsEntity>
    ): List<TransactionItemDto>? {
        val ret = ArrayList<TransactionItemDto>()
        try {
            for (transactionItem in transactionItems) {

                if (transactionItem.measurementName.isNotNullOrEmpty()) {
                    val productSelection = TransactionItemDto()
                    if (cashTransactionId != null) productSelection.transactionId = cashTransactionId
                    productSelection.productName = transactionItem.name
                    productSelection.quantity = transactionItem.quantity
                    productSelection.sellingPrice = transactionItem.sellingPrice
                    productSelection.measurementUnit = transactionItem.measurementName
                    ret.add(productSelection)
                } else {
                    val productEntity = ProductRepository.getInstance(this).getAllProductById(transactionItem.productId)
                    if (productEntity != null) {
                        val productSelection = TransactionItemDto()
                        if (cashTransactionId != null) productSelection.transactionId = cashTransactionId
                        productSelection.productName = productEntity.name
                        productSelection.quantity = transactionItem.quantity
                        productSelection.sellingPrice = productEntity.unitPrice
                        productSelection.measurementUnit = productEntity.measurementName
                        ret.add(productSelection)
                    }
                }


            }
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
        }
        return ret
    }

    private fun getTransactionProducts(cashTransactionId: String?) {
        try {
            if (cashTransactionId != null) {
                val transactionItems = TransactionRepository.getInstance(this)
                    .getTransactionItems(cashTransactionId)
                val productSelections: List<TransactionItemDto>? = convertTransactionItemsToDto(cashTransactionId, transactionItems)
                productSelections?.let {
                    setSelectedProduct(productSelections)
                }
            }
        } catch (ex: java.lang.Exception) {
//            txt_product.text = resources.getString(R.string.default_placeholder) todo
            ex.printStackTrace()
        }
    }

    fun setSelectedProduct(products: List<TransactionItemDto>) {
        try {
            if (products.isNotEmpty()) {
                val stringBuilder = StringBuilder()
                for (i in products.indices) {
                    if (i > 0) stringBuilder.append("\n")
                    stringBuilder.append(products[i].productName)
                    stringBuilder.append(", " + products[i].quantity)
                    addProductDetail("%s %s".format(products[i].quantity, products[i].productName))
                }
//                txt_product.text = stringBuilder TODO
            } else {
//                txt_product.text = resources.getString(R.string.default_placeholder) TODO
                addProductDetail(resources.getString(R.string.default_placeholder))
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
//            txt_product.text = resources.getString(R.string.default_placeholder) TODO
            addProductDetail(resources.getString(R.string.default_placeholder))
        }
    }

    private fun getCategoryById(id: String, transactionType: Int): Category? {
        var categoryid = ""
        categoryid = if (id.contains("::")) {
            id.split("::").toTypedArray()[0]
        } else {
            id
        }

        val list: List<Category> = if (transactionType == 1) {
            CategoryUtil.getCashInCategories()
        } else {
            CategoryUtil.getCashOutCategories()
        }
        for (category in list) {
            //deletes old wrong data from shared pref
            if (category.name.contains(User.getUserId())) {
                val dummyCategory = Category(
                    category.id,
                    category.category_name_en,
                    category.category_name_id,
                    transactionType,
                    SelectableObjectViewHolderType.SECONDARY_CONTENT
                )

                // delete data from sharedpref & continue
//                AppConfigManager.getInstance().deleteCashCategory(dummyCategory, transactionType)
            }
            if (Utility.areEqual(categoryid, category.id)) {
                return category
            } else if (Utility.areEqual(id, category.id)) {
                return category
            }
        }
        if (categoryid != null && !categoryid.isEmpty()) {
            try {
                val categoryEntity = CashRepository.getInstance(this).getCashCategoryById(id)
                val category = Category(
                    categoryid,
                    categoryEntity.name,
                    categoryEntity.name,
                    transactionType,
                    SelectableObjectViewHolderType.SECONDARY_CONTENT
                )
//                AppConfigManager.getInstance().addNewCashCategory(category, transactionType)
                return category
            } catch (ex: java.lang.Exception) {
                ex.printStackTrace()
            }
        }
        return Category("none", "None", "None", -1)
    }

    @SuppressLint("SetTextI18n")
    private fun populateReceipt(cash: CashTransactionEntity) {
        // all POS_TRANSACTION is not expense transaction
        if (cash.transactionType == TransactionEntityType.POS_TRANSACTION) {
            isExpense = false
        }
        if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
            cash_receipt.setup {
                setFrom { CashTransactionReceipt.FROM.FROM_NOTA }
                setInvoiceData { invoiceData }
                isDetailTransaction { isDetailTransaction }
                trxStatus { trxStatus }
                trxType { isExpense }
                bookEntity { currentBook }
                cashTransaction { cash }
                customerEntity { cash.getCustomerForInvoice() }
                transactionItem {
                    val transactionItems =
                        TransactionRepository.getInstance(this@CashTransactionDetailActivity)
                            .getTransactionItems(cash.cashTransactionId)
                    convertTransactionItemsToDto(cash.cashTransactionId, transactionItems)
                }
            }
            cash_receipt_old.hideView()
        } else {
            cash_receipt_old.setupOldReceipt {
                setFrom { CashTransactionOldReceipt.FROM.FROM_NOTA }
                isDetailTransaction { isDetailTransaction }
                trxStatus { trxStatus }
                trxType { isExpense }
                bookEntity { currentBook }
                cashTransaction { cash }
                customerEntity { cash.getCustomerForInvoice() }
                transactionItem {
                    val transactionItems =
                        TransactionRepository.getInstance(this@CashTransactionDetailActivity)
                            .getTransactionItems(cash.cashTransactionId)
                    convertTransactionItemsToDto(cash.cashTransactionId, transactionItems)
                }
            }
            cash_receipt.hideView()
        }
    }


    private fun addProductDetail(productDetail: String) {
//        val text = layoutInflater.inflate(R.layout.product_detail_item, layoutProductDetails, false) as TextView
//        text.text = productDetail
//
//        layoutProductDetails.addView(text)
    }

    private fun populateProducts(cashTransactionId: String?) {
        fun populateProductDto(productDetails: List<TransactionItemDto>?) {
            llProductList.removeAllViews()

            if (productDetails?.isEmpty().isTrue) {
                productDetailLayout.visibility = View.GONE
                categoryBottomBorder.visibility = View.GONE
                return
            }

            productDetails?.forEach {
                val productView = layoutInflater.inflate(R.layout.layout_product_for_receipt, llProductList, false).apply {
                    this.tv_product_name.text = it.productName
                        ?: resources.getString(R.string.default_placeholder)
                    this.tv_product_count.text = it.quantity.toString()
                }

                llProductList.addView(productView)
            }
        }

        try {
            val transactionItems = TransactionRepository.getInstance(this).getTransactionItems(cashTransactionId)
            val productSelections: List<TransactionItemDto>? = convertTransactionItemsToDto(cashTransactionId, transactionItems)
            populateProductDto(productSelections)
        } catch (ex: Exception) {
            populateProductDto(null)
            ex.printStackTrace()
        }
    }

    private fun openPosMode() {
        startActivity(Intent(this, PosActivity::class.java))
        finish()
    }

    private fun trackPosModeOpenEvent() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.POS_INVOICE)
        AppAnalytics.trackEvent(AnalyticsConst.ENTER_POS_MODE_BUTTON_CLICKED, propBuilder)
    }



    override fun onResume() {
        super.onResume()
        if (RemoteConfigUtils.shouldShowNewPosInvoice()) {
            cash_receipt.setup {
                bookEntity { currentBook }
            }
        } else {
            cash_receipt_old.setupOldReceipt {
                bookEntity { currentBook }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        printingDialog?.let {
            it.dismiss()
            printingDialog = null
        }
    }


    companion object {

        const val EDIT_TRX_REQUEST_CODE = 11
        private const val RC_ADD_BANK = 12
        private const val RC_PIN_SELECT = 13
        private const val RC_PIN_ADD_BANK = 14
        const val TRX_ID_EDIT_PARAM = "TrxIdFromEdit"
        private const val PENDING = "PENDING"
        private const val UNPAID = "UNPAID"
        private const val PAID = "PAID"

        const val TRX_ID_PARAM = "TrxId"
        private const val IS_PAYMENT_DETAIL_PPOB = "is_payment_detail_ppob"
        const val IS_EXPENSE_PARAM = "isExpense"
        const val TRX_STATUS_PARAM = "trxStatus"
        const val IS_DETAIL_TRANSACTION = "detailTransaction"
        const val IS_AUTO_RECORD_TRANSACTION = "isAutoRecordTxn"
        const val IS_REDIRECTED_FROM_POS_MODE = "isRedirectedFromPosMode"

        var cashTransactionEntity: CashTransactionEntity? = null
        var cashTransactionItems: List<TransactionItemDto> = listOf()


        fun getNewIntent(
            origin: Context,
            trxId: String,
            isPaymentDetailPpob: Boolean = false
        ): Intent {
            val intent = Intent(origin, CashTransactionDetailActivity::class.java)
            intent.putExtra(TRX_ID_PARAM, trxId)
            intent.putExtra(IS_PAYMENT_DETAIL_PPOB, isPaymentDetailPpob)
            return intent
        }

    }

    override fun onBankAccountSelected(bankAccount: BankAccount?) {
        val propBuilder = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION)
            .put(AnalyticsConst.USER_BANK, bankAccount?.bankCode)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_SWITCH_USER_BANK, propBuilder, false, false, false)
        viewModel.temporarySelectBankAccount(bankAccount)
    }

    override fun addNewBankAccount() {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_ADD_USER_BANK, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.CASH_TRANSACTION), true, false, false)
        startActivityForResult(NewPaymentPinActivity.createIntent(this, PinType.PIN_CONFIRM.toString()), RC_PIN_ADD_BANK)
    }

    override fun onOnboardingDismiss(id: String?, body: String, isFromButton: Boolean, isFromCloseButton: Boolean, isFromOutside: Boolean) {
        // do nothing
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        // do nothing
    }

    private fun showPpobPaymentCollectionsUi(state: CashTransactionDetailState) {
        when (state.paymentCollection?.paymentCollectionCashTransactionInfo?.status ?: "") {
            PENDING -> showPpobPaymentCollectionsPendingStatusUi()
            UNPAID -> showPpobPaymentCollectionsUnpaidStatusUi(state.paymentCollection, state.transaction?.amount ?: 0.0)
            PAID -> showPpobPaymentCollectionsPaidStatusUi(state.paymentCollection?.paymentCollection)
        }
    }

    private fun showPpobPaymentCollectionsPendingStatusUi() {
        payment_layout.hideView()
        ll_info.hideView()
        editBtn.showView()
        deleteBtn.showView()
    }

    private fun showPpobPaymentCollectionsUnpaidStatusUi(
        paymentCollection: PaymentCollectionInfo? = null,
        amount: Double = 0.0
    ) {
        ll_info.hideView()
        payment_layout.showView()
        if (paymentCollection?.paymentCollection == null) {
            unpaid_payment_query_group.showView()
            payment_detail_group.hideView()
            ask_payment_subtitle_txt.text = getString(R.string.ask_digital_payment_collections)
            btn_create_payment.setSingleClickListener {
                if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PAYMENT_IN)) {
                    PaymentUtils.showKycKybStatusBottomSheet(supportFragmentManager, AnalyticsConst.ACCOUNTING)
                } else {
                    viewModel.onCreatePaymentButtonClicked()
                }
            }

            with((amount >= RemoteConfigUtils.getMinimumPaymentAmount()).asVisibility()) {
                ask_payment_title_txt.visibility = this
                ask_payment_subtitle_txt.visibility = this
                btn_create_payment.visibility = this
            }
            editBtn.showView() // showing edit button before generating link
        } else {
            unpaid_payment_query_group.hideView()
            payment_detail_group.showView()
            payment_id_txt.text = paymentCollection?.paymentCollection?.transactionId ?: ""
            payment_account_txt.text = getString(
                R.string.bank_code_and_account_number,
                paymentCollection?.paymentCollection?.receiverBank?.bankCode ?: "",
                paymentCollection?.paymentCollection?.receiverBank?.accountNumber ?: ""
            )
            bank_account_name_txt.text = paymentCollection?.paymentCollection?.receiverBank?.accountHolderName
            val adminFee = paymentCollection?.paymentCollection?.fee ?: 0.0f
            if (adminFee > 0.0f) {
                admin_fee_txt.text = Utility.formatAmount(adminFee.toDouble())
                admin_fee_txt.showView()
                admin_fee_title_txt.showView()
            } else {
                admin_fee_txt.hideView()
                admin_fee_title_txt.hideView()
            }
            link_et.setText(paymentCollection?.paymentCollection?.invoiceUrl ?: "")
            share_payment_btn.setSingleClickListener {
                val receiverIntent = Intent(this, ReferralSharingReceiver::class.java)
                receiverIntent.putExtra(SharingUtilReceiver.EVENT_NAME, "payment_request_share")
                shareText(paymentCollection?.paymentCollection?.template.getPaymentSharingText(null) ?: "", receiverIntent)
            }
            editBtn.hideView()//hiding edit button after generating the link
            showViewAndOnClickListenerForPaymentRedirection(paymentCollection?.paymentCollection)
        }
        deleteBtn.showView()
    }

    private fun showPpobPaymentCollectionsPaidStatusUi(paymentCollection: PaymentCollection?) {
        showViewAndOnClickListenerForPaymentRedirection(paymentCollection)
        payment_layout.hideView()
        ll_info.hideView()
        val view = layoutInflater.inflate(R.layout.layout_transaction_marked_paid, findViewById(R.id.tv_transaction_marked_paid))
        val toast = Toast(this)
        toast.view = view
        toast.setGravity(Gravity.TOP or Gravity.FILL_HORIZONTAL, 0, 52.dp)
        toast.show()

        // LUNAS trx : show delete button only
        editBtn.hideView()
        deleteBtn.showView()
    }

    private fun showViewAndOnClickListenerForPaymentRedirection(paymentCollection: PaymentCollection?) {
        if (paymentCollection?.paymentRequestId.isNotNullOrBlank()) {
            layout_redirect_to_payments.showView()
            layout_redirect_to_payments.setSingleClickListener {
                finish()
                startActivity(
                    PaymentHistoryDetailsActivity.createIntent(
                        context = this,
                        customerId = paymentCollection?.customerId ?: "", orderId = paymentCollection?.paymentRequestId ?: "", paymentType = PaymentHistory.TYPE_PAYMENT_IN, fromMainActivity = false, displayName = ""
                    )
                )
            }
        }
    }

}
