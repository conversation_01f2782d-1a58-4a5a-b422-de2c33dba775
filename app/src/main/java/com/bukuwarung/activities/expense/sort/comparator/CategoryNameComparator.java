package com.bukuwarung.activities.expense.sort.comparator;

import com.bukuwarung.activities.customer.sort.SortOrder;
import com.bukuwarung.database.dto.CashTransactionDto;

import java.util.Comparator;

public final class CategoryNameComparator implements Comparator<CashTransactionDto> {

    SortOrder sortOrder = SortOrder.ASC;
    public CategoryNameComparator(SortOrder order){
        this.sortOrder=order;
    }

    @Override
    public int compare(CashTransactionDto cashTransactionDto1, CashTransactionDto cashTransactionDto2) {
        String name2 = cashTransactionDto2.categoryName;
        String name1 = cashTransactionDto1.categoryName;
        if (name2 != null && name1!=null) {
            if(sortOrder == SortOrder.ASC)
                return name1.toLowerCase().compareTo(name2.toLowerCase());
            else
                return name2.toLowerCase().compareTo(name1.toLowerCase());
        }
        return -1;
    }
}
