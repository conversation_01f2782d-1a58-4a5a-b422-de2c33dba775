package com.bukuwarung.activities.expense.sort;

import android.view.MenuItem;
import android.widget.PopupMenu;

import com.bukuwarung.R;
import com.bukuwarung.activities.expense.IncomeExpenseTab;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.IncomeExpenseSort;
import com.bukuwarung.preference.FeaturePrefManager;

public class CashSortMenuClickHandler implements PopupMenu.OnMenuItemClickListener {
    private IncomeExpenseTab cashTab;
    private PopupMenu sortPopupMenu;
    public CashSortMenuClickHandler(IncomeExpenseTab cashTab, PopupMenu sortPopupMenu){
        this.cashTab = cashTab;
        this.sortPopupMenu = sortPopupMenu;
    }
    @Override
    public boolean onMenuItemClick(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.leastAmount:
                actionMenuItem((PopupMenu) sortPopupMenu, IncomeExpenseSort.LEAST_AMOUNT);
//                AppAnalytics.trackEvent("cash_tap_sort","sort","least amount");
                break;
            case R.id.mostAmount:
                actionMenuItem((PopupMenu) sortPopupMenu, IncomeExpenseSort.MOST_AMOUNT);
//                AppAnalytics.trackEvent("cash_tap_sort","sort","most amount");
                break;
            case R.id.mostRecent:
                actionMenuItem((PopupMenu) sortPopupMenu, IncomeExpenseSort.MOST_RECENT);
//                AppAnalytics.trackEvent("cash_tap_sort","sort","most recent");
                break;
            case R.id.nameAsc:
                actionMenuItem((PopupMenu) sortPopupMenu, IncomeExpenseSort.NAME_ASC);
//                AppAnalytics.trackEvent("cash_tap_sort","sort","name ascending");
                break;
            case R.id.nameDsc:
                actionMenuItem((PopupMenu) sortPopupMenu, IncomeExpenseSort.NAME_DSC);
//                AppAnalytics.trackEvent("cash_tap_sort","sort","name descending");
                break;
            default:
                actionMenuItem((PopupMenu) sortPopupMenu, IncomeExpenseSort.MOST_RECENT);
//                AppAnalytics.trackEvent("cash_tap_sort","sort","most recent");
                break;
        }
        return true;
    }
    public final void actionMenuItem(PopupMenu sortPopupMenu, int selectedSortOrder) {
        FeaturePrefManager.getInstance().setCashListSortOrder(selectedSortOrder);
        sortPopupMenu.dismiss();
    }
}
