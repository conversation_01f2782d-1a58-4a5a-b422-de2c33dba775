package com.bukuwarung.activities.expense.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.databinding.NewCashCategoryItemBinding

class NewCashCategoryAdapter(private val clickAction: (Category?) -> Unit) :
    RecyclerView.Adapter<NewCashCategoryAdapter.NewCashCategoryViewHolder>() {

    private var list = mutableListOf<Category>()
    var selectedNewCashCategory = ""
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NewCashCategoryViewHolder {
        val itemBinding =
            NewCashCategoryItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return NewCashCategoryViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: NewCashCategoryViewHolder, position: Int) {
        holder.bind(list[position], selectedNewCashCategory)
    }

    override fun getItemCount(): Int = list.size

    fun setNewCashCategoryName(NewCashCategoryName: String?) {
        selectedNewCashCategory = NewCashCategoryName ?: ""
        notifyDataSetChanged()
    }

    fun setData(list: MutableList<Category>) {
        this.list = list
        notifyDataSetChanged()
    }

    class NewCashCategoryViewHolder(
        private val binding: NewCashCategoryItemBinding,
        private val clickAction: (Category?) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(category: Category, selectedNewCashCategory: String) {

            binding.chCategory.text = category.categoryName
            binding.chCategory.setOnClickListener {
                clickAction(category)
            }
            binding.chCategory.isChecked = selectedNewCashCategory == category.categoryName
        }
    }
}
