package com.bukuwarung.activities.expense.category;

import com.bukuwarung.bulk.CashTransactionType;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.payments.constants.PpobConst;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;


public final class CategoryUtil implements CategoryUtility {

    public static int CATEGORY_TYPE_OUT = -1;
    public static int CATEGORY_TYPE_IN = 1;

    public static int CATEGORY_TYPE_OUT_CST = -1;
    public static int CATEGORY_TYPE_IN_CST = 1;

    public static final List<Category> getCashOutCategories(){
        if(FeaturePrefManager.getInstance().isNewUser()) {
            return cashOutCategoriesNew;
        }else{
            return cashOutCategoriesOld;
        }
    }

    public static final List<Category> getCashInCategories(){
        if(FeaturePrefManager.getInstance().isNewUser()) {
            return cashInCategoriesNew;
        }else{
            return cashInCategoriesOld;
        }
    }

    public static final List<Category> cashOutCategoriesNew = new ArrayList<Category>() {{
        add(new Category("cashOut","Cash Out","Pengeluaran",-1));

        try{
            String categoriesFromRemote = RemoteConfigUtils.SelectCategory.INSTANCE.getDebitCategoriesNew();
            Gson gson = new GsonBuilder().create();
            Type jsonType =  new TypeToken<List<com.bukuwarung.activities.expense.data.Category>>() {}.getType();
            List<com.bukuwarung.activities.expense.data.Category> categoriesDebit = gson.fromJson(categoriesFromRemote, jsonType);

            for(int i = 0;i< categoriesDebit.size();i++){
                com.bukuwarung.activities.expense.data.Category category = categoriesDebit.get(i);
                add(new Category(category.getCategoryId(),category.getCategoryName(),category.getCategoryName(),-1));
            }
        }catch(Exception e){
            String categoriesFromRemote = RemoteConfigUtils.SelectCategory.INSTANCE.getDebitCategoriesNewBackUp();
            Gson gson = new GsonBuilder().create();
            Type jsonType =  new TypeToken<List<com.bukuwarung.activities.expense.data.Category>>() {}.getType();
            List<com.bukuwarung.activities.expense.data.Category> categoriesDebit = gson.fromJson(categoriesFromRemote, jsonType);

            for(int i = 0;i< categoriesDebit.size();i++){
                com.bukuwarung.activities.expense.data.Category category = categoriesDebit.get(i);
                add(new Category(category.getCategoryId(),category.getCategoryName(),category.getCategoryName(),-1));
            }
        }

    }};


    public static final List<Category> cashInCategoriesNew = new ArrayList<Category>() {{
        add(new Category("cashIn","Cash In","Penjualan",1));

        try{
            String categoriesFromRemote = RemoteConfigUtils.SelectCategory.INSTANCE.getCreditCategoriesNew();
            Gson gson = new GsonBuilder().create();
            Type jsonType =  new TypeToken<List<com.bukuwarung.activities.expense.data.Category>>() {}.getType();
            List<com.bukuwarung.activities.expense.data.Category> categoriesCredit = gson.fromJson(categoriesFromRemote, jsonType);

            for(int i = 0;i< categoriesCredit.size();i++){
                com.bukuwarung.activities.expense.data.Category category = categoriesCredit.get(i);
                add(new Category(category.getCategoryId(),category.getCategoryName(),category.getCategoryName(),1));
            }
        }catch(Exception e){
            String categoriesFromRemote = RemoteConfigUtils.SelectCategory.INSTANCE.getCreditCategoriesNewBackUp();
            Gson gson = new GsonBuilder().create();
            Type jsonType =  new TypeToken<List<com.bukuwarung.activities.expense.data.Category>>() {}.getType();
            List<com.bukuwarung.activities.expense.data.Category> categoriesCredit = gson.fromJson(categoriesFromRemote, jsonType);

            for(int i = 0;i< categoriesCredit.size();i++){
                com.bukuwarung.activities.expense.data.Category category = categoriesCredit.get(i);
                add(new Category(category.getCategoryId(),category.getCategoryName(),category.getCategoryName(),1));
            }
        }


    }};

    public static final List<Category> cashOutCategoriesOld = new ArrayList<Category>() {{
        add(new Category("rent","Rent","Sewa",-1));
        add(new Category("belipulsa","Beli Pulsa","Beli Pulsa",-1));
        add(new Category("electricity","Electricity","Bayar Listrik",-1));
        add(new Category("family","Family","Keluarga",-1));
        add(new Category("installment","Installment","Cicilan",-1));
        add(new Category("renovation","Renovation","Renovasi",-1));
        add(new Category("restock","Restock","Beli Stok",-1));
        add(new Category("debtPaymentOut","Debt Payment","Bayar utang",-1));
        add(new Category("cashOut","Cash Out","Pengeluaran",-1));
    }};

    public static final List<Category> cashInCategoriesOld = new ArrayList<Category>() {{
        add(new Category("sales","Sales","Pemasukan",1));
        add(new Category("modal","Capital","Modal",1));
        add(new Category("bonus","Bonus","Bonus",1));
        add(new Category("packetData","Packet Date","Paket Data",1));
        add(new Category("tokenListrik","Token Listrik","Token Listrik",1));
        add(new Category("pulsa","Pulsa","Pulsa",1));
        add(new Category("internet","Internet","Internet",1));
        add(new Category("rokok","Cigarette","Rokok",1));
        add(new Category("Kopi","Coffee","Kopi",1));
        add(new Category("PDAM","PDAM","PDAM",1));
        add(new Category("debtPaymentIn","Debt Payment","Pembayaran utang",1));
        add(new Category("lending","Lending","Pinjaman",1));
        add(new Category("holidayGift","Holiday Gift","THR",1));
        add(new Category("cashIn","Cash In","Penjualan",1));
        add(new Category("pos", "Pos", "Kasir", 1));
    }};


    public static List<Category> convertToCategory(List<CashCategoryEntity> cashEntries) {
        List<Category> result = new ArrayList<>();
        for(CashCategoryEntity entry:cashEntries){
            result.add(new Category(entry.cashCategoryId,entry.name,entry.name,entry.type));
        }
        return result;
    }

    @NotNull
    @Override
    public Category getCategoryIfExists(@NotNull String categoryId, CashTransactionType type) {
        List<Category> list = null;
        if (type == CashTransactionType.CREDIT) {
            list = CategoryUtil.getCashInCategories();
        } else {
            list = CategoryUtil.getCashOutCategories();
        }

        for (Category category : list) {
            if (Utility.areEqual(categoryId, category.id)) {
                return category;
            }
        }
        return null;
    }
}
