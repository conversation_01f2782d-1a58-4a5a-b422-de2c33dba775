package com.bukuwarung.activities.expense;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.paging.PageKeyedDataSource;

import com.bukuwarung.activities.superclasses.DataHolder;

import java.util.List;

public class StringDataSource extends PageKeyedDataSource<Integer, DataHolder> {

    public static final int PAGE_SIZE = 10;
    private StringListProvider provider;
    private int firstPage = 0;
    public StringDataSource(StringListProvider provider) {
        this.provider = provider;
    }

    @Override
    public void loadInitial(@NonNull LoadInitialParams<Integer> params, @NonNull LoadInitialCallback<Integer, DataHolder> callback) {
        List<DataHolder> result = provider.getDataHolderList(firstPage, params.requestedLoadSize);
        int pageBefore = firstPage - 1;
        int pageAfter = firstPage + 1;
        callback.onResult(result, pageBefore, pageAfter);
    }

    @Override
    public void loadBefore(@NonNull LoadParams<Integer> params, @NonNull LoadCallback<Integer, DataHolder> callback) {
    }

    @Override
    public void loadAfter(@NonNull LoadParams<Integer> params, @NonNull LoadCallback<Integer, DataHolder> callback) {
        int start = params.key * params.requestedLoadSize;
        List<DataHolder> result = provider.getDataHolderList(start, start + params.requestedLoadSize);
        callback.onResult(result, params.key + 1);
    }
}
