package com.bukuwarung.activities.expense.category;


import androidx.annotation.Nullable;

import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType;

import kotlinx.android.parcel.Parcelize;

@Parcelize
public final class Category extends SelectableObject {

    public String category_name_en;
    public String category_name_id;
    public String id;
    public int category_type;

    public Category() {
        super();
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT);
    }

    public Category(String id, String category_name_en, String category_name_id) {
        super();
        this.id = id;
        this.category_name_en = category_name_en;
        this.category_name_id = category_name_id;
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT);
    }

    public Category(String id, String category_name_en, String category_name_id, int category_type) {
        this.id = id;
        this.category_name_en = category_name_en;
        this.category_name_id = category_name_id;
        this.category_type = category_type;
        setType(SelectableObjectViewHolderType.PRIMARY_CONTENT);
    }

    public Category(String id, String category_name_en, String category_name_id, int category_type, int type) {
        this.id = id;
        this.category_name_en = category_name_en;
        this.category_name_id = category_name_id;
        this.category_type = category_type;

        setType(type);
    }

    @Override
    public int getId() {
        return id.hashCode();
    }

    @Override
    public String getIdString() {
        return id;
    }

    @Override
    public String getName() {
        return category_name_id;
    }

    @Override
    public void setName(String newName) {
        this.category_name_id = newName;
    }

    public void setId(String newId) {
        this.id = newId;
    }

    @Override
    public int getOrder() {
        return 0;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        return obj instanceof Category && ((Category) obj).getName().equalsIgnoreCase(getName());
    }
}
