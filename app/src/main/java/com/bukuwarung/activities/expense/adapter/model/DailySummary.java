package com.bukuwarung.activities.expense.adapter.model;


public final class DailySummary {
    private final String name;
    public int trxCount = 0;
    private final String expense;
    private final String income;
    private final String id;
    private final int type;
    private String buyingPrice = "0";

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public int getTrxCount() {
        return trxCount;
    }

    public String getExpense() {
        return expense;
    }

    public String getIncome() {
        return income;
    }

    public String getId() {
        return id;
    }

    public String getBuyingPrice() {
        return buyingPrice;
    }

    public DailySummary(String name, String expense, String income, String targetId, int type) {
        this.name = name;
        this.expense = expense == null?"0":expense;
        this.income = income == null?"0":income;
        this.id = targetId;
        this.type = type;
    }

    public DailySummary(String name, String expense, String income, String buyingPrice, String id, int type) {
        this.name = name;
        this.expense = expense;
        this.income = income;
        this.id = id;
        this.type = type;
        this.buyingPrice = buyingPrice;
    }

    public DailySummary(String name, int trxCount, String expense, String income, String targetId, int type) {
        this.name = name;
        this.trxCount = trxCount;
        this.expense = expense == null?"0":expense;
        this.income = income == null?"0":income;
        this.id = targetId;
        this.type = type;
    }
}
