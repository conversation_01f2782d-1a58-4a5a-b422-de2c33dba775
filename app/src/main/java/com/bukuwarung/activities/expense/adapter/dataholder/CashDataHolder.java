package com.bukuwarung.activities.expense.adapter.dataholder;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.dto.CashTransactionDto;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CustomerEntity;

public class CashDataHolder extends DataHolder {

    private final CashTransactionDto dto;

    public CashDataHolder(CashTransactionDto cashEntity) {
        this.dto = cashEntity;
        setTag(Tag.CUSTOMER_TAB_CUSTOMER_VIEW);
    }

    public final CashTransactionDto getCashCategoryEntity() {
        return this.dto;
    }

    public String getName() {
        return this.dto.categoryName;
    }
}