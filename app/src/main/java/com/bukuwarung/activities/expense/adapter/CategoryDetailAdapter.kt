package com.bukuwarung.activities.expense.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.data.CategoryInfo
import kotlinx.android.synthetic.main.item_category_details.view.*

class CategoryDetailAdapter(val categoryInfo: List<CategoryInfo>): RecyclerView.Adapter<CategoryDetailAdapter.CategoryDetailViewHolder>()  {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryDetailViewHolder {
        return CategoryDetailViewHolder(parent)
    }

    override fun onBindViewHolder(holder: CategoryDetailAdapter.CategoryDetailViewHolder, position: Int) {
        holder.bind(categoryInfo[position])
    }

    override fun getItemCount(): Int {
        return categoryInfo.size
    }

    inner class CategoryDetailViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {

        constructor(parent: ViewGroup) : this(
                LayoutInflater.from(parent.context).inflate(
                        R.layout.item_category_details,
                        parent, false
                )
        )

        fun bind(categoryInfo: CategoryInfo) {
            with(itemView) {
                tv_category_name.text = categoryInfo.categoryName
                tv_category_details.text = categoryInfo.categoryDescription
            }
        }
    }
}