package com.bukuwarung.activities.expense.detail

import android.os.Bundle
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.savedstate.SavedStateRegistryOwner
import com.bukuwarung.Application
import com.bukuwarung.data.restclient.RestClient
import com.bukuwarung.database.AppDatabase
import com.bukuwarung.database.dao.BankAccountDao
import com.bukuwarung.data.session.SessionRemoteDataSource
import com.bukuwarung.data.session.SessionRepository
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.data.repository.*
import com.bukuwarung.utils.RemoteConfigUtils

class CashTransactionDetailViewModelFactory(
        private val owner: SavedStateRegistryOwner,
        private val cashRepository: CashRepository,
        private val customerRepository: CustomerRepository,
        private val transactionRepository: TransactionRepository,
        private val transactionId: String,
        defaultArgs: Bundle? = null
) : AbstractSavedStateViewModelFactory(owner, defaultArgs) {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel?> create(
            key: String,
            modelClass: Class<T>,
            handle: SavedStateHandle
    ): T {
        return CashTransactionDetailViewModel(
                cashRepository,
                customerRepository,
                FinproUseCase(provideFinproRepository()),
                PaymentUseCase(providerPaymentsRepository(), providerBankAccountLocalRepository(), transactionRepository, providerBankAccountLocalDataStore()),
                transactionId
        ) as T
    }

    private fun provideFinproRepository(): FinproRemoteRepository {
        return FinproRepository(provideFinproRemoteDataSource(), providePaymentsRemoteDataSource(), provideSessionRepository())
    }

    private fun provideFinproRemoteDataSource(): FinproRemoteDataSource {
        return RestClient.retrofit(baseUrl = RemoteConfigUtils.getPaymentConfigs().finProApi)
                .create(FinproRemoteDataSource::class.java);
    }

    private fun providerPaymentsRepository(): PaymentsRepository {
        return PaymentsRepository(providePaymentsRemoteDataSource(),
                provideSessionRepository())
    }

    private fun providerBankAccountLocalRepository(): BankAccountLocalRepository {
        return BankAccountLocalRepositoryImpl(providerBankAccountLocalDataStore())
    }

    private fun providerBankAccountLocalDataStore(): BankAccountLocalDataStore {
        return BankAccountDataStore(provideBankAccountDao())
    }

    private fun provideBankAccountDao(): BankAccountDao {
        return AppDatabase.getDatabase(Application.getAppContext()).bankAccountDao()
    }


    private fun providePaymentsRemoteDataSource(): PaymentsRemoteDataSource {
        return RestClient.retrofit().create(PaymentsRemoteDataSource::class.java);
    }

    private fun provideSessionRemoteDataSource(): SessionRemoteDataSource{
        return RestClient.retrofit().create(SessionRemoteDataSource::class.java)
    }

    private fun provideSessionRepository(): SessionRepository {
        return SessionRepository(provideSessionRemoteDataSource())
    }

}