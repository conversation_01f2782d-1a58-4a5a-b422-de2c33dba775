package com.bukuwarung.activities.expense

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.Application
import com.bukuwarung.inventory.usecases.ProductInventory
import javax.inject.Inject

class CashListViewModelFactory @Inject constructor(private val application: Application, private val inventory: ProductInventory) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return CashListViewModel(application, inventory) as T
    }
}