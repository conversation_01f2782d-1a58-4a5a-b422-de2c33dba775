package com.bukuwarung.activities.expense.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.bukuwarung.R;

public final class DeleteConfirmationDialog extends Dialog implements View.OnClickListener, CompoundButton.OnCheckedChangeListener {

    private Context mContext;
    private View.OnClickListener okClickListener;
    //dialog title
    private final String title;
    //warning message for delete operation
    private final String warningMessage;
    CheckBox cb;

    private CheckBox.OnCheckedChangeListener onCheckedChangeListener;

    public DeleteConfirmationDialog(Context context, String title, String warningMessage, CheckBox.OnCheckedChangeListener onCheckedChangeListener, View.OnClickListener okClickListener) {
        super(context);
        this.mContext = context;
        this.okClickListener = okClickListener;
        this.title = title;
        this.warningMessage = warningMessage;
        this.onCheckedChangeListener = onCheckedChangeListener;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_delete_category);
        Window window = getWindow();
//        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);;
        TextView okTv =  findViewById(R.id.btn_hapus);
        TextView cancelTv =  findViewById(R.id.btn_batal);
        TextView titleTv = findViewById(R.id.tv_title);
        TextView bodyTv = findViewById(R.id.tv_body);
        cb = findViewById(R.id.cb_cat_del_confirm);
        cb.setOnCheckedChangeListener(this);
        titleTv.setText(title);
        bodyTv.setText(warningMessage);
        cancelTv.setOnClickListener(this);
        okTv.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_batal:
                this.hide();
                return;
            case R.id.btn_hapus:
                this.okClickListener.onClick(view);
                this.hide();
                return;
        }
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            this.onCheckedChangeListener.onCheckedChanged(buttonView, isChecked);
    }
}
