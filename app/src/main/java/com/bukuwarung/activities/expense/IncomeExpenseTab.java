package com.bukuwarung.activities.expense;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.bukuwarung.constants.AnalyticsConst.DEFAULT_FILTER;
import static com.bukuwarung.constants.AnalyticsConst.SMS_CHECKBOX_ENABLED;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.Icon;
import android.net.Uri;
import android.os.Build;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.core.util.Pair;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;
import androidx.paging.PagedList;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.bukuwarung.R;
import com.bukuwarung.activities.WebviewActivity;
import com.bukuwarung.activities.expense.adapter.CashAdapter;
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity;
import com.bukuwarung.activities.expense.filter.CashTabFilter;
import com.bukuwarung.activities.expense.sort.SortOrder;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.pos.PosActivity;
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet;
import com.bukuwarung.activities.superclasses.AppFragment;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity;
import com.bukuwarung.activities.transactionreport.DateFilter;
import com.bukuwarung.activities.transactionreport.DateRange;
import com.bukuwarung.activities.transactionreport.TransactionReportActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.IncomeExpenseSort;
import com.bukuwarung.constants.RemoteConfigConst;
import com.bukuwarung.database.dto.BookSummaryModel;
import com.bukuwarung.database.dto.CashTransactionDto;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.datasync.restore.ManualDataRestoreDialog;
import com.bukuwarung.utils.CoroutineHelper;
import com.bukuwarung.enums.DatePeriod;
import com.bukuwarung.enums.GamifyTarget;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.OnboardingPrefManager;
import com.bukuwarung.session.AuthHelper;
import com.bukuwarung.session.User;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.tutor.shape.FocusGravity;
import com.bukuwarung.tutor.shape.ShapeType;
import com.bukuwarung.tutor.view.OnboardingWidget;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.TooltipBuilder;
import com.bukuwarung.utils.Utility;
import com.bukuwarung.utils.UtilsKt;
import com.bukuwarung.utils.WhatsAppUtils;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.datepicker.CalendarConstraints;
import com.google.android.material.datepicker.MaterialDatePicker;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;

import org.jetbrains.annotations.NotNull;

import java.util.Arrays;
import java.util.Calendar;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Executors;

import javax.inject.Inject;

import dagger.android.support.AndroidSupportInjection;
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip;

public final class IncomeExpenseTab extends AppFragment implements OnClickListener, OnboardingWidget.OnboardingWidgetListener,
        CashAdapter.CashAdapterListener, Observer<BookEntity> {

    private boolean fromDailyBusiness = false;
    private int firstAutoRecordTxn = -1;
    private RecyclerView transactionRecyclerView;
    private CoroutineHelper coroutineHandler = new CoroutineHelper();

    @Override
    public void goToDetail(@NotNull String id, boolean isExpense, int status, boolean isDetailTransaksi,boolean isAutoRecordTxn) {
        if (getContext() != null) {
            Intent intent = CashTransactionDetailActivity.Companion.getNewIntent(getContext(), id, false);
            intent.putExtra(CashTransactionDetailActivity.IS_EXPENSE_PARAM, isExpense);
            intent.putExtra(CashTransactionDetailActivity.TRX_STATUS_PARAM, status);
            intent.putExtra(CashTransactionDetailActivity.IS_DETAIL_TRANSACTION, isDetailTransaksi);
            intent.putExtra(CashTransactionDetailActivity.IS_AUTO_RECORD_TRANSACTION, isAutoRecordTxn);
            startActivity(intent);
        }
    }

    @Override
    public void goToCategory(@NotNull String id, @NotNull String name) {
        try {
            Intent intent = new Intent(getContext(), CategoryTransactionsActivity.class);
            intent.putExtra(CategoryTransactionsActivity.CASH_CATEGORY_ID, id);
            //AppAnalytics.trackEvent("open_cash_transaction_list", "", "");
            startActivity(intent);
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("category", name);
            //AppAnalytics.trackEvent("view_category_transaction_list", propBuilder);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void seeOnboardingTutorial() {
        showOnboardingTutorialOnClick();
    }

    @Override
    public void onChanged(BookEntity bookEntity) {
        refreshToolBar(getActivity());
    }

    @Override
    public void seeAutoRecordOnboardingTutorial(@NotNull View itemView, int firstAutoRecordTxnIndex) {
        showAutoRecordOnboardingTutorialOnClick(itemView,firstAutoRecordTxnIndex);
    }

    public interface IncomeExpenseTabListener {
        void handleNormalBackPressedFromIncomeExpense();
    }

    private IncomeExpenseTabListener listener;

    private CashAdapter adapter;
    public BookEntity bookEntity;
    private int cashFilter;

    private SwipeRefreshLayout expensePTR;

    @Inject
    CashListViewModelFactory cashListViewModelFactory;
    private CashListViewModel cashListViewModel;
    private int cashListCount = 0;
    private CountDownTimer timer;
    private Toolbar toolbar;
    private TextView toolbarTitle;

    public View sort, filterMenu;
    private int sortOrder;

    public ExtendedFloatingActionButton addBtn;
    private ImageView dataRefreshBtn;
    private TextView tvExpense, tvIncome;
    private AppBarLayout appBarLayout;

    private TextView tvMarginType, tvMarginNominal;
    private View vMarginBg;

    public LinearLayout dateHeader;
    public TextView dateColumn;

    public ImageView search;

    private HorizontalScrollView chipScrollView;
    private ChipGroup cgFilter;
    private Chip chipRanged, chipThisMonth, chipLastMonth, chipAll, chipThisWeek, chipToday;

    private TextView tvOfflineMsg;
    private DateRange selectedRangedDate = null;

    private int allTrxCount = 0;
    private int rangeFilter = 0;
    private boolean reportCoachmarkShown = false;
    private boolean searchCoachmarkShown = false;
    private boolean dateFilterCoachmarkShown = false;
    private boolean isCoachmarkShowing = false;
    private OnboardingWidget onboardingWidget;
    private long dateFilterVisibilityLimit = 0;
    private long searchVisibilityLimit = 0;
    private boolean showOldForm;
    private boolean showLastTransactionType;
    private final MediatorLiveData<List<DataHolder>> liveDataMerger = new MediatorLiveData<>();
    private boolean isExitDialogEnabled = false;
    private boolean areNewCategoriesEnabled = false;

    private boolean mergedTab;
    ImageView arrow;
    LinearLayout arrowArea;

    private Boolean showHelpIcon = false;
    private String redirectHelpTo = "";

    public void onDestroyView() {
        super.onDestroyView();
    }

    public IncomeExpenseTab() {
        this.cashFilter = featureManager.getSelectedCashFilter();
    }

    public CashAdapter getCashAdapter() {
        return this.adapter;
    }


    public static CashListViewModel getCashListViewModel(IncomeExpenseTab incomeExpenseTab) {
        return incomeExpenseTab.cashListViewModel;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setHasOptionsMenu(true);
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        AndroidSupportInjection.inject(this);
        if (context instanceof IncomeExpenseTabListener) {
            listener = (IncomeExpenseTabListener) context;
        }
    }

    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        setHasOptionsMenu(true);
        return layoutInflater.inflate(R.layout.tab_layout_expense, viewGroup, false);
    }

    private boolean byCategory = false;

    private TextView creditTotal;
    private TextView debitTotal;
    private TextView tvUnpaidCustomerValue;
    private ImageView notificationIcon;
    TextView notify_highlighter;
    private CollapsingToolbarLayout summaryCard;
    private View btnSummary;

    private Chip chipGroupByDate, chipGroupByCategory;

    public View searchContainer;
    private EditText searchBox;
    private RelativeLayout searchFilterLayout;
    private LinearLayout bottomTabs;
    private View collectingCalendarIcon;
    private boolean coachmarkShown = false;

    TextView helpIcon;
    TextView tvPosIcon;
    private int inventorySize = 0;

    private String trxBlankScreenVariant = "";

    public void onActivityCreated(Bundle bundle) {
        super.onActivityCreated(bundle);
        FragmentActivity activity = getActivity();
        showOldForm = RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm();
        mergedTab = RemoteConfigUtils.TransactionTabConfig.INSTANCE.mergedTab();

        showHelpIcon = RemoteConfigUtils.INSTANCE.shouldShowHelpIcon();
        redirectHelpTo = RemoteConfigUtils.INSTANCE.redirectHelpIcon();
        showLastTransactionType = RemoteConfigUtils.INSTANCE.showPreviousTransaction();

        trxBlankScreenVariant = RemoteConfigUtils.TrxBlankScreenExperiment.getVariant();
        String lastTransaction = AnalyticsConst.HARD_CODED;
        if (showLastTransactionType) {
            lastTransaction = AnalyticsConst.LAST_ACTION;
        }

        if (activity != null) {
            View view = getView();
            if (view == null) return;
            if (getParentFragment() != null) {
                View parentView = getParentFragment().getView();
                this.toolbar = parentView.findViewById(R.id.toolbar);
                view.findViewById(R.id.toolbar).setVisibility(GONE);
            } else {
                this.toolbar = view.findViewById(R.id.toolbar);
            }
            tvOfflineMsg=getView().findViewById(R.id.tvOffline);
            helpIcon = toolbar.findViewById(R.id.tv_help_icon);
            tvPosIcon = toolbar.findViewById(R.id.tv_pos_icon);
            initToolBar();

            searchFilterLayout = getView().findViewById(R.id.searchFilterLayout);
            summaryCard = view.findViewById(R.id.toolbar_view);
            tvUnpaidCustomerValue = view.findViewById(R.id.under_paid_customers);

            this.searchContainer = getView().findViewById(R.id.searchContainer);
            this.appBarLayout = getView().findViewById(R.id.app_bar);

            bottomTabs = getView().findViewById(R.id.type_rg);

            creditTotal = view.findViewById(R.id.creditTotal);
            debitTotal = view.findViewById(R.id.debitTotal);

            this.search = view.findViewById(R.id.openSearchBtn);

            this.searchBox = view.findViewById(R.id.searchQueryBox);
            ImageView clear = view.findViewById(R.id.clear);

            OnClickListener onClickListener = this;
            clear.setOnClickListener(onClickListener);

            this.sortOrder = IncomeExpenseSort.MOST_RECENT;
            this.sort = getView().findViewById(R.id.sortMenu);
            this.filterMenu = getView().findViewById(R.id.filterMenu);
            this.dateHeader = getView().findViewById(R.id.header_date);
            this.dateColumn = getView().findViewById(R.id.name);
            this.sort.setOnClickListener(this);
            this.filterMenu.setOnClickListener(this);
            this.searchContainer.setOnClickListener(onClickListener);

            btnSummary = view.findViewById(R.id.summary_btn);
            btnSummary.setOnClickListener(view1 ->
                            checkInternetConnection());

            this.searchBox.addTextChangedListener(new SearchBoxTextWatcher(this));
            this.expensePTR = getView().findViewById(R.id.ptrExpenseCategory);
            this.expensePTR.setColorSchemeResources(R.color.colorPrimary);
            this.expensePTR.setOnRefreshListener(this::refreshDataPTR);
            transactionRecyclerView = getView().findViewById(R.id.transactionRecyclerView);

            Fragment fragment = this;
            cashListViewModel = ViewModelProviders.of(this, cashListViewModelFactory).get(CashListViewModel.class);
            adapter = new CashAdapter(this);
            adapter.setHasStableIds(false);

            final Context activityCtx = getContext();
            if (mergedTab) {
                addBtn = getView().findViewById(R.id.addCashTransactionBtn);
                arrow = getView().findViewById(R.id.tutorarrow);
                arrowArea = getView().findViewById(R.id.arrowArea);
            } else {
                addBtn = getView().findViewById(R.id.addCashTransactionNoTabMergeBtn);
                arrow = getView().findViewById(R.id.tutorarrowForNoTabMerge);
                arrowArea = getView().findViewById(R.id.arrowAreaForNoTabMerge);

            }
            addBtn.setOnClickListener(view12 -> {
                addTransaction(activityCtx, false);
            });
            dataRefreshBtn = getView().findViewById(R.id.img_refresh);
            dataRefreshBtn.setOnClickListener(v -> {
                ManualDataRestoreDialog manualDataRestoreDialog = new ManualDataRestoreDialog(getActivity(), getActivity(),2);
                manualDataRestoreDialog.show();
            });
            tvIncome = getView().findViewById(R.id.text_income);
            tvExpense = getView().findViewById(R.id.text_expense);

            isExitDialogEnabled = RemoteConfigUtils.INSTANCE.shouldShowExitDialog();
            areNewCategoriesEnabled = RemoteConfigUtils.SelectCategory.INSTANCE.areNewCategoriesVisible();

            // set user property for transaction blank screen experiement
            String trxBlankScreenVariant = RemoteConfigUtils.TrxBlankScreenExperiment.getVariant();

            tvIncome.setOnClickListener(v -> {
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put(AnalyticsConst.TRX_TYPE, AnalyticsConst.SALES);
                propBuilder.put(AnalyticsConst.NEW_TRX_CATEGORY_ENABLED, areNewCategoriesEnabled);
                propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsTransaction());
                propBuilder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewPosInvoice());
                //AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, propBuilder);
                Intent intent = NewCashTransactionActivity.createIntent(activityCtx);
                intent.putExtra(NewCashTransactionActivity.TRX_TYPE, transCount < RemoteConfigUtils.INSTANCE.getSalesOnboardingThreshold());
                startActivity(intent);
            });

            tvExpense.setOnClickListener(v -> {
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put(AnalyticsConst.TRX_TYPE, AnalyticsConst.EXPENSE);
                propBuilder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled);
                propBuilder.put(AnalyticsConst.NEW_TRX_CATEGORY_ENABLED, areNewCategoriesEnabled);
                propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsTransaction());
                propBuilder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewPosInvoice());
                //AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, propBuilder);

                Intent intent = NewCashTransactionActivity.createIntent(activityCtx);
                intent.putExtra(NewCashTransactionActivity.TRX_TYPE, transCount < RemoteConfigUtils.INSTANCE.getSalesOnboardingThreshold());
                startActivity(intent);
            });

//            if(showOldForm) {
//                tvIncome.setVisibility(GONE);
//                tvExpense.setVisibility(GONE);
//                addBtn.setVisibility(VISIBLE);
//                tvUnpaidCustomerValue.setVisibility(GONE);
//            } else {
//                tvIncome.setVisibility(VISIBLE);
//                tvExpense.setVisibility(VISIBLE);
//                addBtn.setVisibility(GONE);
//                tvUnpaidCustomerValue.setVisibility(VISIBLE);
////                sFirstSessionAfterInstall == true and 0 transactio
//            }
            addBtn.setVisibility(VISIBLE);

            transactionRecyclerView.setLayoutManager(new WrapContentLinearLayoutManager(getContext()));
            LifecycleOwner lifecycleOwner = this;

            dateFilterVisibilityLimit = RemoteConfigUtils.INSTANCE.getValueOfN();
            searchVisibilityLimit = RemoteConfigUtils.INSTANCE.getValueOfM();

            this.cashListViewModel.getBusinessLiveData().observe(lifecycleOwner, this);

            this.cashListViewModel.cashVal.observe(lifecycleOwner, list -> {
                if (byCategory) {
                    updateCashAdapter(null, cashListViewModel.setTypeFilter(CashTabFilter.BY_CATEGORY));
                } else updateCashAdapter(list, null);
            });

            transactionRecyclerView.setAdapter(adapter);

            categoryLayout = view.findViewById(R.id.category_layout);

            this.tvMarginType = view.findViewById(R.id.tv_margin_type);
            this.tvMarginNominal = view.findViewById(R.id.tv_margin_nominal);
            this.vMarginBg = view.findViewById(R.id.bg_margin);
            readjustSummary(new BookSummaryModel());
            this.cashListViewModel.getBookSummaryModel().observe(getViewLifecycleOwner(), this::readjustSummary);
            setupChips(view);
        }

        cashListViewModel.productInventory.getAllProducts().observe(getViewLifecycleOwner(), productEntities -> {
            inventorySize = productEntities.size();
            updatePosIconAndTooltip();
        });
    }

    private void checkInternetConnection() {
        if (Utility.hasInternet()) {
            goToTransactionReport();
        } else {
            new CommonConfirmationBottomSheet().showNoInternetBottomSheet(requireContext()
                    , requireActivity().getSupportFragmentManager());
        }
    }

    private void updatePosIconAndTooltip() {
        if (cashListCount > 0) {
            tvPosIcon.setVisibility(VISIBLE);

            // show pos tooltip if trx count > target and it hasn't been shown
            if (cashListCount > FeaturePrefManager.getInstance().getPosTooltipTarget() && !AppConfigManager.getInstance().getPosTransaksiIntroTooltipShown()) {
                if (getActivity() != null && getContext() != null) {
                    SimpleTooltip tooltip = TooltipBuilder.Companion.builder(requireContext())
                            .setAnchor(tvPosIcon)
                            .setGravity(Gravity.BOTTOM)
                            .setText(getContext().getString(R.string.pos_intro_tooltip))
                            .build();

                    tooltip.show();
                    AppConfigManager.getInstance().setPosTransaksiIntroTooltipShown(true);
                }
            }
        } else {
            tvPosIcon.setVisibility(GONE);
        }
    }

    public void updateCashAdapter(PagedList<CashTransactionDto> list, List<DataHolder> dataHolders) {
        PagedList.Config config = new PagedList.Config.Builder()
                .setPageSize(100)
                .setEnablePlaceholders(false)
                .setInitialLoadSizeHint(100)
                .build();

        List<DataHolder> holders = new LinkedList<>();

        if (list == null && dataHolders == null) {
            adapter.notifyDataSetChanged();
            return;
        }

        if (list != null) {
            holders = cashListViewModel.refreshDateFilter(list);
        }
        if (dataHolders != null) {
            holders = dataHolders;
        }


        StringListProvider provider = new StringListProvider(holders);

        StringDataSource dataSource = new StringDataSource(provider);

        PagedList<DataHolder> pagedDataHolder = new PagedList.Builder<Integer, DataHolder>(dataSource, config)
                .setNotifyExecutor(new UiThreadExecutor())
                .setFetchExecutor(Executors.newSingleThreadExecutor())
                .setInitialKey(0)
                .build();
        adapter.setSize(holders.size());
        adapter.submitList(pagedDataHolder);

        List<CashTransactionDto> cashList = cashListViewModel.getCashList();

        if (cashList != null) {
            cashListCount = cashList.size();
        }

        updatePosIconAndTooltip();

        setFiltersVisibility();

        if (VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            updateShortcut(pagedDataHolder.size());
        }
        if (cashList == null || cashList.isEmpty()) {
            dateHeader.setVisibility(GONE);
            if (pagedDataHolder.size()>0 && pagedDataHolder.get(0) instanceof DataHolder.NoFilterResultRowHolder) {
                // to stop the toolbar from collapsing
                return;
            }
            summaryCard.setVisibility(GONE);
            updateCountLayout(0, pagedDataHolder);
            chipScrollView.setVisibility(GONE);
            searchFilterLayout.setVisibility(GONE);

            if (showHelpIcon) {
                helpIcon.setVisibility(VISIBLE);
            }
        } else {
            summaryCard.setVisibility(VISIBLE);
            helpIcon.setVisibility(GONE);
            if (!chipScrollView.isShown()) {
                ComponentUtil.setVisible(chipScrollView, cashList.size() >= dateFilterVisibilityLimit);
            }
            if (!searchFilterLayout.isShown()) {
                ComponentUtil.setVisible(searchFilterLayout, cashList.size() >= searchVisibilityLimit);
            }
            try {
                ComponentUtil.setVisible(dateHeader, byCategory);
                AppBarLayout.LayoutParams params = (AppBarLayout.LayoutParams) summaryCard.getLayoutParams();
                params.setScrollFlags(AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL | AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED);
                summaryCard.setLayoutParams(params);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (cashList.size() > featureManager.getTooltipTarget()) {
                featureManager.disableTooltips();
            }
            updateCountLayout(cashList.size(), pagedDataHolder);
            boolean isQueryBlank = searchBox.getText().toString().equals("");
            if (isQueryBlank) {
                appBarLayout.setExpanded(true, true);
            }

        }
        cashListViewModel.dataloaded = false;
    }

    private void goToTransactionReport() {
        if (getActivity() != null) {
            Intent intent = new Intent(getActivity(), TransactionReportActivity.class);
            intent.putExtra("targetId", "2");
            startActivity(intent);
        }
        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
        prop.put("source", "transaksi");
        prop.put(DEFAULT_FILTER, DateFilter.Companion.getFilterString(requireContext(),RemoteConfigUtils.PdfDefaultFilterExperiment.INSTANCE.getVariant()));
        //AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TRANSACTION_REPORT, prop);
    }

    private void setupChips(View view) {
        chipScrollView = view.findViewById(R.id.chipScrollView);
        cgFilter = view.findViewById(R.id.cgTrxFilter);
        chipRanged = view.findViewById(R.id.chip_filter_ranged);
        chipLastMonth = view.findViewById(R.id.chip_filter_last_month);
        chipThisMonth = view.findViewById(R.id.chip_filter_current_month);
        chipThisWeek = view.findViewById(R.id.chip_filter_this_week);
        chipAll = view.findViewById(R.id.chip_filter_all);
        chipToday = view.findViewById(R.id.chip_filter_today);

        cgFilter.setOnCheckedChangeListener((group, checkedId) -> {
            View selectedView = view.findViewById(checkedId);
            autoScroll(selectedView);
        });

        chipRanged.setOnCheckedChangeListener((compoundButton, b) -> {
            chipRanged.setTypeface(b ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipRanged.setTextColor(ContextCompat.getColor(requireContext(), b ? R.color.black_80 : R.color.black_60));

            // If user hasn't clear previous ranged-date, then we'll use previous date filter
            if (b) {
                if (selectedRangedDate == null) {
                    openRangedDatePicker();
                } else {
                    updateCashAdapter(null, cashListViewModel.setDateRange(selectedRangedDate));;
                }
            }
        });
        chipRanged.setOnCloseIconClickListener(chip -> {
            if (selectedRangedDate == null) {
                openRangedDatePicker();
                return;
            }

            chipRanged.setCloseIconResource(R.drawable.ic_cevron_down_white);
            chipAll.setChecked(true);
            chipRanged.setText(getString(R.string.set_range));
            selectedRangedDate = null;
//            //AppAnalytics.trackEvent("cash_tab_tap_clear_custom_date",false,false,false);
        });

        chipThisMonth.setOnCheckedChangeListener((compoundButton, b) -> {
            chipThisMonth.setTypeface(b ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipThisMonth.setTextColor(ContextCompat.getColor(requireContext(), b ? R.color.black_80 : R.color.black_60));
            if (b) {
                cashListViewModel.setDateRange(DateTimeUtils.getDateRange(DatePeriod.THIS_MONTH));
//                //AppAnalytics.trackEvent("cash_tab_tap_current_month",false,true,false);
            }
        });

        chipLastMonth.setOnCheckedChangeListener((compoundButton, b) -> {
            chipLastMonth.setTypeface(b ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipLastMonth.setTextColor(ContextCompat.getColor(requireContext(), b ? R.color.black_80 : R.color.black_60));
            if (b) {
                cashListViewModel.setDateRange(DateTimeUtils.getDateRange(DatePeriod.LAST_MONTH));
//                //AppAnalytics.trackEvent("cash_tab_tap_last_month",false,true,false);
            }
        });

        chipThisWeek.setOnCheckedChangeListener((compoundButton, b) -> {
            chipThisWeek.setTypeface(b ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipThisWeek.setTextColor(ContextCompat.getColor(requireContext(), b ? R.color.black_80 : R.color.black_60));
            if (b) {
                cashListViewModel.setDateRange(DateTimeUtils.getDateRange(DatePeriod.THIS_WEEK));
//                //AppAnalytics.trackEvent("cash_tab_tap_current_week",false,true,false);
            }
        });

        chipAll.setOnCheckedChangeListener((compoundButton, b) -> {
            chipAll.setTypeface(b ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipAll.setTextColor(ContextCompat.getColor(requireContext(), b ? R.color.black_80 : R.color.black_60));
            if (b) {
                cashListViewModel.setDateRange(DateTimeUtils.getDateRange(DatePeriod.ALL));
                //AppAnalytics.trackEvent("cash_tab_tap_all_date",false,true,false);
            }
        });

        chipToday.setOnCheckedChangeListener((compoundButton, b) -> {
            chipToday.setTypeface(b ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
            chipToday.setTextColor(ContextCompat.getColor(requireContext(), b ? R.color.black_80 : R.color.black_60));
            if (b) {
                cashListViewModel.setDateRange(DateTimeUtils.getDateRange(DatePeriod.TODAY));
                //AppAnalytics.trackEvent("cash_tab_tap_current_date",false,true,false);
            }
        });
        chipGroupByDate = view.findViewById(R.id.chipTransactionGroupByDate);
        chipGroupByCategory = view.findViewById(R.id.chipTransactionGroupByCategory);

        chipGroupByDate.setTypeface(Typeface.DEFAULT_BOLD);
        chipGroupByCategory.setTypeface(Typeface.DEFAULT_BOLD);

        chipGroupByDate.setOnClickListener(this::showDateGroupingPopup);
        chipGroupByDate.setOnCloseIconClickListener(this::showDateGroupingPopup);

        chipGroupByCategory.setOnClickListener(this::showCategoryGroupingPopup);
        chipGroupByCategory.setOnCloseIconClickListener(this::showCategoryGroupingPopup);
    }

    private void showCategoryGroupingPopup(View chip) {
        PopupMenu popup = new PopupMenu(requireContext(), chip);
        popup.getMenuInflater()
                .inflate(R.menu.popup_group_by_category, popup.getMenu());
        popup.setOnMenuItemClickListener(menuItem -> {
            switch (menuItem.getItemId()) {
                case R.id.groupByAll:
                    checkCurrentFilter(CashTabFilter.ALL);
                    chipGroupByCategory.setText(getString(R.string.all));
                    break;
                case R.id.groupByIn:
                    checkCurrentFilter(CashTabFilter.IN);
                    chipGroupByCategory.setText(getString(R.string.income_label));
                    break;
                case R.id.groupByOut:
                    checkCurrentFilter(CashTabFilter.OUT);
                    chipGroupByCategory.setText(getString(R.string.expense_label));
                    break;
            }
            return false;
        });
        popup.show();
    }

    private void showNewFormOnboardingTutorial() {
        onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), IncomeExpenseTab.this,
                OnboardingPrefManager.TUTOR_TRANSACTION_TAB, bottomTabs, R.drawable.onboarding_smile, "",
                getString(R.string.transaction_tab_coachmark), getString(R.string.next), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                1, 3, true, false);
    }

    private void showDateGroupingPopup(View chip) {
        PopupMenu popup = new PopupMenu(requireContext(), chip);
        popup.getMenuInflater()
                .inflate(R.menu.popup_group_by_date, popup.getMenu());
        popup.setOnMenuItemClickListener(menuItem -> {
            switch (menuItem.getItemId()) {
                case R.id.groupByDay:
                    checkCurrentFilter(CashTabFilter.DAILY);
                    chipGroupByDate.setText(getString(R.string.Daily));
                    break;
                case R.id.groupByWeek:
                    checkCurrentFilter(CashTabFilter.WEEKLY);
                    chipGroupByDate.setText(getString(R.string.weekly));
                    if (selectedRangedDate != null) {
                        convertCurrentDateRange(CashTabFilter.WEEKLY);
                    }
                    break;
                case R.id.groupByMonth:
                    checkCurrentFilter(CashTabFilter.MONTHLY);
                    chipGroupByDate.setText(getString(R.string.monthly));
                    if (selectedRangedDate != null) {
                        convertCurrentDateRange(CashTabFilter.MONTHLY);
                    }
                    break;
            }
            return false;
        });
        popup.show();
    }

    private void convertCurrentDateRange(int group) {
        selectedRangedDate = DateTimeUtils.convertByDateGroup(selectedRangedDate, group);
        Pair<Long, Long> pair = new Pair<>(selectedRangedDate.getStartDateAsDate().getTime(), selectedRangedDate.getEndDateAsDate().getTime());
        chipRanged.setText(Utility.getRegularRangedDateStringForChip(pair));
        cashListViewModel.setDateRange(selectedRangedDate);
    }

    private void refreshDataPTR() {
        refreshTransactionData();
    }

    private void refreshTransactionData() {
        // TODO: REPLACE WITH ACTUAL FUNCTION TO REFRESH DATA
        SetupManager.getInstance().setSynchedPaymentData(false);
        //new AsyncCashTransactionDataSync().execute();
        coroutineHandler.syncCashTransactionData();
        //new AsyncPaymentCashTransactionDataSync().execute();
        coroutineHandler.syncPaymentCashTransactionData();
        new Handler().postDelayed(() -> {
            expensePTR.setRefreshing(false);
        }, 2000);
    }

    private void refreshCategoryData() {
        // TODO: REPLACE WITH ACTUAL FUNCTION TO REFRESH DATA
        //new AsyncCashTransactionDataSync().execute();
        coroutineHandler.syncCashTransactionData();
        new Handler().postDelayed(() -> {
            expensePTR.setRefreshing(false);
        }, 2000);
    }

    private void readjustSummary(BookSummaryModel summary) {
        try {
            creditTotal.setText(String.format("%s%s", Utility.getCurrency(), Utility.formatCurrency(Math.abs(summary.amountIn))));
            debitTotal.setText(String.format("%s%s", Utility.getCurrency(), Utility.formatCurrency(Math.abs(summary.amountOut))));

            int marginBg = 0;
            int textColor = 0;
            String marginType;
            if (summary.profit >= 0) {
                marginType = getString(R.string.profit_text);
                textColor = ContextCompat.getColor(requireContext(), R.color.in_green);
                marginBg = ContextCompat.getColor(requireContext(), R.color.profit_green_bg);
                if (summary.profit == 0) {
                    FeaturePrefManager.getInstance().setCashBalanceStatus("none");
                } else {
                    FeaturePrefManager.getInstance().setCashBalanceStatus(AnalyticsConst.PROFIT);
                }
            } else {
                marginType = getString(R.string.loss_text);
                textColor = ContextCompat.getColor(requireContext(), R.color.out_red);
                marginBg = ContextCompat.getColor(requireContext(), R.color.red_5);
                FeaturePrefManager.getInstance().setCashBalanceStatus(AnalyticsConst.LOSS);
            }


            this.tvMarginType.setText(marginType);
            this.tvMarginType.setTextColor(textColor);
            this.tvMarginNominal.setText(String.format("%s%s", Utility.getCurrency(), Utility.formatCurrency(Math.abs(summary.profit))));
            this.tvUnpaidCustomerValue.setText(String.format(getStringRes(R.string.total_customer_unpaid_amount), Utility.getCurrency() + "" + Utility.formatCurrency(Math.abs(summary.customerUnpaid))));
            FeaturePrefManager.getInstance().setCashBalanceValue(Utility.formatCurrency(Math.abs(summary.profit)));
            FeaturePrefManager.getInstance().setCashInValue(String.format("%s%s", Utility.getCurrency(),Utility.formatCurrency(Math.abs(summary.amountIn))));
            FeaturePrefManager.getInstance().setCashOutValue(String.format("%s%s", Utility.getCurrency(),Utility.formatCurrency(Math.abs(summary.amountOut))));
            this.tvMarginNominal.setTextColor(textColor);
            this.vMarginBg.setBackgroundColor(marginBg);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public LinearLayout categoryLayout;
    private boolean isSearchOpen;
    private int transCount = 0;


    private void updateCountLayout(int itemCount, List<? extends DataHolder> list) {
        if (cashListViewModel != null && cashListViewModel.dataloaded) {
            this.transCount = itemCount;
            if (itemCount > 0) {
                this.addBtn.setVisibility(View.VISIBLE);
                FeaturePrefManager.getInstance().setCashTourFinished(true);
                summaryCard.setVisibility(VISIBLE);
                setFiltersVisibility();
            } else {
                summaryCard.setVisibility(GONE);
                chipScrollView.setVisibility(GONE);
                searchFilterLayout.setVisibility(GONE);
            }
        }
        if (itemCount > 0) {
            expensePTR.setEnabled(!byCategory);
        }

        if (itemCount > 0 && cashFilter != CashTabFilter.ALL && byCategory) {
            summaryCard.setVisibility(VISIBLE);
            setFiltersVisibility();
        }
        if (itemCount < 2 && !isSearchOpen) {
            Animation mAnimation = AnimationUtils.loadAnimation(getContext(), R.anim.swinging);
            if (UtilsKt.isAnimEnabled(getContext())) arrow.startAnimation(mAnimation);
            arrowArea.setVisibility(View.VISIBLE);
        } else {
            arrowArea.setVisibility(View.GONE);
        }
        cashListViewModel.dataloaded = true;
    }

    public final void openRangedDatePicker() {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();

        long today = MaterialDatePicker.todayInUtcMilliseconds();
        calendar.setTimeInMillis(today);


        // constraint
        CalendarConstraints constraintBuilder = new CalendarConstraints.Builder().build();

        //MaterialDatePicker
        MaterialDatePicker<androidx.core.util.Pair<Long, Long>> datePicker = MaterialDatePicker.Builder
                .dateRangePicker()
                .setTitleText(R.string.chose_date_range_label)
                .setCalendarConstraints(constraintBuilder)
                .setSelection(new androidx.core.util.Pair<>(today, today))
                .setTheme(R.style.CustomCalendarStyle)
                .build();

        datePicker.addOnPositiveButtonClickListener(selection -> {
            try {
                selectedRangedDate = DateTimeUtils.getDateRange(selection.first, selection.second);
                chipRanged.setChecked(true);
                chipRanged.setCloseIconResource(R.drawable.ic_close);

                switch (rangeFilter) {
                    case CashTabFilter.DAILY:
                        chipRanged.setText(Utility.getRegularRangedDateStringForChip(selection));
                        cashListViewModel.setDateRange(selectedRangedDate);
                        break;
                    case CashTabFilter.WEEKLY:
                        convertCurrentDateRange(CashTabFilter.WEEKLY);
                        break;
                    case CashTabFilter.MONTHLY:
                        convertCurrentDateRange(CashTabFilter.MONTHLY);
                        break;
                }

                //AppAnalytics.trackEvent("cash_tab_tap_custom_date",false,true,false);
                InputUtils.hideKeyBoardWithCheck(requireActivity());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        });

        datePicker.addOnNegativeButtonClickListener(view -> {
            InputUtils.hideKeyBoardWithCheck(requireActivity());
            chipAll.setChecked(true);
        });

        datePicker.addOnCancelListener(dialog -> {
            chipAll.setChecked(true);
        });

        datePicker.show(getChildFragmentManager(), "DATE_PICKER");
    }

    public void onDestroy() {
        super.onDestroy();
        CountDownTimer countDownTimer = this.timer;
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        this.timer = null;
        coroutineHandler.cancel();
    }

    public void onResume() {
        super.onResume();
        initToolBar();
        if (Utility.hasInternet()) {
            tvOfflineMsg.setVisibility(GONE);
        } else {
            tvOfflineMsg.setVisibility(VISIBLE);
        }
        showGamifyPopup();
        showCstIntro();
        showTooltip();
        cashFilter = featureManager.getSelectedCashFilter();
        try {
            refreshToolBar(getContext());
//            cashListViewModel.refresh(); // TODO: can be improved because we're refreshing a lot of data
            updateCashAdapter(null, null);
            int checkedId = cgFilter.getCheckedChipId();
            cgFilter.clearCheck();
            cgFilter.check(checkedId);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public void onClick(View view) {
        switch (view.getId()) {
           /* case R.id.dailyBtn:
                checkCurrentFilter(CashTabFilter.DAILY);
//                //AppAnalytics.trackEvent("customer_tap_filter", "filter", "daily");
                break;*/
            case R.id.clear:
                if (getView() != null)
                    InputUtils.hideKeyboardFrom(getActivity(), getView().findFocus());
                setFiltersVisibility();
                getView().findViewById(R.id.searchLayout).setVisibility(View.GONE);

                this.addBtn.setVisibility(View.VISIBLE);
                isSearchOpen = false;
                this.searchBox.setText("");
                appBarLayout.setExpanded(true, false);
                this.searchBox.clearFocus();
                //AppAnalytics.trackEvent("cash_tab_tap_clear",false,true,false);
                break;
           /* case R.id.weeklyBtn:
                checkCurrentFilter(CashTabFilter.WEEKLY);
                ////AppAnalytics.trackEvent("cash_tab_tap_filter", "filter", "weekly");
                break;*/
            case R.id.searchContainer:
                startSearch();
                break;
           /* case R.id.monthlyBtn:
                checkCurrentFilter(CashTabFilter.MONTHLY);
                ////AppAnalytics.trackEvent("cash_tab_tap_filter", "filter", "monthly");
                break;*/

            case R.id.sortMenu:
                showSortOrderDialog();
                break;
            case R.id.filterMenu:
                showTransactionFilterDialog();
                break;
            // TODO check events
           /* case R.id.allButton:
                checkCurrentFilter(CashTabFilter.ALL);
                ////AppAnalytics.trackEvent("cash_tab_tap_filter", "filter", "ALL");
                break;
            case R.id.expenseBtn:
                checkCurrentFilter(CashTabFilter.OUT);
                ////AppAnalytics.trackEvent("cash_tab_tap_filter", "filter", "OUT");
                break;
            case R.id.incomeBtn:
                checkCurrentFilter(CashTabFilter.IN);
                ////AppAnalytics.trackEvent("cash_tab_tap_filter", "filter", "IN");
                break;*/
           /* case R.id.tvMonthPicker:
                //AppAnalytics.trackEvent("open_month_picker_dialog");
                showMonthPicker();
                break;*/
        }
    }

    private void startSearch() {
        this.addBtn.setVisibility(View.GONE);
        isSearchOpen = true;
        getView().findViewById(R.id.searchLayout).setVisibility(View.VISIBLE);
        this.searchBox.requestFocus();
        getView().findViewById(R.id.tutorarrow).setVisibility(View.GONE);
        appBarLayout.setExpanded(false, true);
        InputUtils.showKeyboard(getActivity());
        //AppAnalytics.trackEvent("cash_tab_tap_search");
    }

    private void showTooltip() {
        if (isCoachmarkShowing || getActivity() == null || getActivity().getApplicationContext() == null || !IncomeExpenseTab.this.isResumed())
            return;
        int count = TransactionRepository.getInstance(getActivity().getApplicationContext()).countAllCashTransWithDeleted(User.getBusinessId());

        if (!dateFilterCoachmarkShown && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_DATE_FILTER)) {
            if (count == dateFilterVisibilityLimit) {
                dateFilterCoachmarkShown = true;
                isCoachmarkShowing = true;
                onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), IncomeExpenseTab.this,
                        OnboardingPrefManager.TUTORIAL_DATE_FILTER, chipScrollView, R.drawable.onboarding_announce, getString(R.string.new_feature),
                        getString(R.string.onboarding_date_filter), getString(R.string.understand), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                        1, 1, true, false);
            }
        }

        if (count == searchVisibilityLimit) {
            if (!searchCoachmarkShown && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_SEARCH_CASH)) {
                isCoachmarkShowing = true;
                searchCoachmarkShown = true;
                onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), IncomeExpenseTab.this,
                        OnboardingPrefManager.TUTORIAL_SEARCH_CASH, searchFilterLayout, R.drawable.onboarding_announce, getString(R.string.new_feature),
                        getString(R.string.onboarding_search_cash), getString(R.string.understand), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                        1, 1, true, false);
            }
        } else if (count == 3) {
            if (!reportCoachmarkShown && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_DOWNLOAD_REPORT_CASH)) {
                isCoachmarkShowing = true;
                reportCoachmarkShown = true;
                onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), IncomeExpenseTab.this,
                        OnboardingPrefManager.TUTORIAL_DOWNLOAD_REPORT_CASH, btnSummary, R.drawable.onboarding_announce, getString(R.string.new_feature),
                        getString(R.string.onboarding_download_report_cash), getString(R.string.try_feature), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                        1, 1, true, false);
            }
        }
    }


    public final void initToolBar() {
        FragmentActivity activity = getActivity();
        if (activity != null) {
            MainActivity mainActivity = (MainActivity) activity;
            mainActivity.setSupportActionBar(this.toolbar);
            ActionBar supportActionBar = mainActivity.getSupportActionBar();
            supportActionBar.setDisplayShowTitleEnabled(false);
            supportActionBar.setDisplayHomeAsUpEnabled(true);
            supportActionBar.setHomeButtonEnabled(true);

            if (FeaturePrefManager.getInstance().isDailyHighlightExplored()) {
                supportActionBar.setHomeAsUpIndicator((int) R.mipmap.ic_menu_white_24dp);
            } else {
                supportActionBar.setHomeAsUpIndicator(R.drawable.notification_hamburger);
            }
            this.toolbarTitle = this.toolbar.findViewById(R.id.title);
            this.toolbar.setNavigationOnClickListener(v -> {
                if (getActivity() != null) {
                    ((MainActivity) getActivity()).handleSideMenuIconClick();
                }
            });

            helpIcon.setOnClickListener(view -> {
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.TRANSAKSI_TAB);
                propBuilder.put(AnalyticsConst.HELP_ICON_REDIRECTED_TO, redirectHelpTo);
                //AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_SUPPORT_REQUESTED, propBuilder);
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CUSTOMER_SUPPORT_REQUESTED, requireActivity());

                if (redirectHelpTo.equals(RemoteConfigConst.REDIRECT_HELP_ICON_CUSTOMER_SUPPORT)) {
                    WhatsAppUtils.openWABotWithoutProp(getActivity());
                } else if(redirectHelpTo.equals(RemoteConfigConst.REDIRECT_HELP_ICON_TUTORIAL)) {
                    //AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN);
                    SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN, requireActivity());
                    startActivity(WebviewActivity.Companion.createIntent(getActivity(), AppConst.TUTORIAL_URL,
                            getString(R.string.learn_bukuwarung)));
                }
            });

            tvPosIcon.setOnClickListener(view -> {
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.TRANSAKSI);
                //AppAnalytics.trackEvent(AnalyticsConst.ENTER_POS_MODE_BUTTON_CLICKED, propBuilder);
                startActivity(new Intent(getActivity(), PosActivity.class));
            });

            refreshToolBar(getContext());
        }
    }

    public final void refreshToolBar(Context ctx) {
        BusinessRepository businessRepository = BusinessRepository.getInstance(ctx);
        this.bookEntity = businessRepository.getBusinessByIdSync(User.getBusinessId());
        if (this.bookEntity != null) {
            String selectedBusiness = this.bookEntity.businessName;
            if (!Utility.isBlank(selectedBusiness)) {
                if (selectedBusiness.equals(getString(R.string.defaulBusinessName))) {
                    this.toolbarTitle.setText(getString(R.string.mybusiness));
                } else {
                    this.toolbarTitle.setText(selectedBusiness);
                }

            } else {
                this.toolbarTitle.setText(getString(R.string.mybusiness));
                BookEntity bookEntity = this.bookEntity;
                if (!Utility.isBlank(bookEntity.businessOwnerName)) {
                    businessRepository.updateBusinessProfile(User.getUserId(), User.getDeviceId(), User.getBusinessId(), bookEntity.businessName, bookEntity.bookType, bookEntity.bookTypeName, bookEntity.businessOwnerName);
                    this.toolbarTitle.setText(bookEntity.businessOwnerName);
                }
            }
        }

        if (cashListCount == 0) {
            if (showHelpIcon) {
                helpIcon.setVisibility(VISIBLE);
            }
        } else {
            helpIcon.setVisibility(GONE);
        }
    }

    public void onRequestPermissionsResult(int i, @NotNull String[] strArr, @NotNull int[] iArr) {
        if (i == 331 && VERSION.SDK_INT >= 23) {
            Context context = getContext();
            if (!PermissonUtil.hasStoragePermission()) {
                NotificationUtils.alertToast(getString(R.string.write_storage_perm_denied_error));
                return;
            }
            //AppAnalytics.trackEvent("granted_storage_permission");
        }
    }

    public void showCstIntro() {
        if (coachmarkShown || getActivity() == null || getActivity().getApplicationContext() == null ||
                OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN) ||
                TransactionRepository.getInstance(getActivity().getApplicationContext()).getCashTransactionCountWithDeletedRecords() > 0 ||
                getView() == null || getActivity() == null) return;
        try {
            new Handler().postDelayed(() -> {
                if (getActivity() == null || getActivity().isFinishing()) return;
                showOnboardingTutorial();
            }, 300);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public final void updateUi(boolean byCat) {
        this.byCategory = byCat;
        if (byCat) {
//            //AppAnalytics.trackEvent("cash_tab_tap_by_category");
            this.chipGroupByCategory.setVisibility(View.VISIBLE);

            this.chipGroupByDate.setVisibility(View.GONE);
            updateCashAdapter(null, cashListViewModel.setTypeFilter(CashTabFilter.BY_CATEGORY));
            updateCashAdapter(null, cashListViewModel.setTransactionFilter(CashTabFilter.ALL));
            return;
        }
//        //AppAnalytics.trackEvent("cash_tab_tap_by_date");

        this.chipGroupByCategory.setVisibility(View.GONE);
        this.chipGroupByDate.setVisibility(View.VISIBLE);
        updateCashAdapter(null, cashListViewModel.setTypeFilter(CashTabFilter.BY_DATE));
        updateCashAdapter(null, cashListViewModel.setTransactionFilter(CashTabFilter.DAILY));
    }

    private void checkCurrentFilter(int i) {
        this.rangeFilter = i;
        updateTransactionFilter(i);
    }

    private void updateTransactionFilter(int i) {
        if (i == CashTabFilter.DAILY) {
            //AppAnalytics.trackEvent("cash_tab_tap_daily",false,true,false);
        } else if (i == CashTabFilter.WEEKLY) {
            //AppAnalytics.trackEvent("cash_tab_tap_weekly",false,true,false);
        } else if (i == CashTabFilter.MONTHLY) {
            //AppAnalytics.trackEvent("cash_tab_tap_monthly",false,true,false);
        }

        if (i == CashTabFilter.ALL) {
            //AppAnalytics.trackEvent("cash_tab_tap_filter_all",false,true,false);
        } else if (i == CashTabFilter.OUT) {
            //AppAnalytics.trackEvent("cash_tab_tap_filter_out",false,true,false);
        } else if (i == CashTabFilter.IN) {
            //AppAnalytics.trackEvent("cash_tab_tap_filter_in",false,true,false);
        }

        CashListViewModel customerListViewModel = this.cashListViewModel;
        updateCashAdapter(null, customerListViewModel.setTransactionFilter(i));
    }

    private void showTransactionFilterDialog() {
        Context wrapper = new ContextThemeWrapper(getContext(), R.style.PopupMenu);

        final PopupMenu sortDialog = new PopupMenu(wrapper, filterMenu);
        sortDialog.getMenuInflater().inflate(R.menu.cash_filter_menu, sortDialog.getMenu());
        if (byCategory) {
            ////AppAnalytics.trackEvent("cash_tap_filter", "filter", "byCategory");
            highlightMenu(sortDialog, getString(R.string.byCategory), R.id.byCategory);
        } else {
            ////AppAnalytics.trackEvent("cash_tap_filter", "filter", "byDate");
            highlightMenu(sortDialog, getString(R.string.byDate), R.id.byDate);
        }

        sortDialog.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem menuItem) {
                String string = "";
                AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder();


                switch (menuItem.getItemId()) {

                    case R.id.byCategory:
                        updateUi(true);
                        byCategory = true;
                        ////AppAnalytics.trackEvent("cash_tap_filter", "filter", "byCategory");
                        string = getString(R.string.byCategory);
                        dateHeader.setVisibility(VISIBLE);
                        highlightMenu(sortDialog, string, R.id.byCategory);
                        dateColumn.setText(getStringRes(R.string.category_label));
                        searchBox.setHint(R.string.search_by_category_hint);
                        break;
                    case R.id.byDate:
                        updateUi(false);
                        byCategory = false;
                        dateHeader.setVisibility(GONE);
                        ////AppAnalytics.trackEvent("cash_tap_filter", "filter", "byDate");
                        string = getString(R.string.byDate);
                        highlightMenu(sortDialog, string, R.id.byDate);
                        dateColumn.setText(getStringRes(R.string.date));
                        searchBox.setHint(R.string.search_by_date_hint);
                        break;

                    default:
                        updateUi(false);
                        byCategory = false;
                        string = getString(R.string.byDate);
                        highlightMenu(sortDialog, string, R.id.byDate);
                        break;
                }
                return true;
            }
        });
        sortDialog.show();
    }

    private void showSortOrderDialog() {
        Context wrapper = new ContextThemeWrapper(getContext(), R.style.PopupMenu);
        final PopupMenu popupMenu = new PopupMenu(wrapper, sort);
        popupMenu.getMenuInflater().inflate(R.menu.activity_expense_sort_menu, popupMenu.getMenu());
        int i = this.sortOrder;
        if (i == SortOrder.NAME_ASC) {
            String string = getString(R.string.activity_main_search_menu_name_asc);
            highlightMenu(popupMenu, string, R.id.nameAsc);
        } else if (i == SortOrder.NAME_DSC) {
            String string2 = getString(R.string.activity_main_search_menu_name_dsc);
            highlightMenu(popupMenu, string2, R.id.nameDsc);
        } else if (i == SortOrder.MOST_OUT) {
            String string3 = getString(R.string.activity_main_search_menu_most_cash_out);
            highlightMenu(popupMenu, string3, R.id.mostOut);
        } else if (i == SortOrder.LEAST_OUT) {
            String string4 = getString(R.string.activity_main_search_menu_least_cash_out);
            highlightMenu(popupMenu, string4, R.id.leastOut);
        } else if (i == SortOrder.MOST_IN) {
            String string4 = getString(R.string.activity_main_search_menu_most_cash_in);
            highlightMenu(popupMenu, string4, R.id.mostIn);
        } else if (i == SortOrder.LEAST_IN) {
            String string4 = getString(R.string.activity_main_search_menu_least_cash_in);
            highlightMenu(popupMenu, string4, R.id.leastIn);
        } else if (i == SortOrder.MOST_RECENT) {
            String string5 = getString(R.string.activity_main_search_menu_most_recent);
            highlightMenu(popupMenu, string5, R.id.mostRecent);
        }

        popupMenu.setOnMenuItemClickListener(item -> {
            switch (item.getItemId()) {
                case R.id.leastOut:
                    actionMenuItem(popupMenu, SortOrder.LEAST_OUT);
                    break;
                case R.id.mostOut:
                    actionMenuItem(popupMenu, SortOrder.MOST_OUT);
                    break;
                case R.id.mostRecent:
                    actionMenuItem(popupMenu, SortOrder.MOST_RECENT);
                    break;
                case R.id.nameAsc:
                    actionMenuItem(popupMenu, SortOrder.NAME_ASC);
                    break;
                case R.id.mostIn:
                    actionMenuItem(popupMenu, SortOrder.MOST_IN);
                    break;
                case R.id.leastIn:
                    actionMenuItem(popupMenu, SortOrder.LEAST_IN);
                    break;
                default:
                    actionMenuItem(popupMenu, SortOrder.MOST_RECENT);
                    break;
            }
            return true;
        });
        popupMenu.show();
    }

    public final void actionMenuItem(PopupMenu popupMenu, int i) {
        this.sortOrder = i;
        updateCashAdapter(null, cashListViewModel.setSortOrder(i));
        popupMenu.dismiss();
    }

    private void highlightMenu(PopupMenu popupMenu, String str, int i) {
        SpannableString spannableString = new SpannableString(str);
        spannableString.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.colorPrimaryDark)), 0, spannableString.length(), 0);
        popupMenu.getMenu().findItem(i).setTitle(spannableString);
    }

    private void autoScroll(View view) {
        if (view == null) {
            return;
        }
        Rect sRect = new Rect();
        chipScrollView.getDrawingRect(sRect);

        int left = view.getLeft();
        int right = view.getRight();
        int x = 48;
        Log.d("FILTER_SCROLL VIEW", String.format("%s | %s", left, right));
        Log.d("FILTER_SCROLL sVIEW", String.format("%s | %s", sRect.left, sRect.right));

        if (left <= sRect.left && right <= sRect.right) {
            // left-side of the view is partially visible
            int post = left - sRect.left - x;
            chipScrollView.smoothScrollBy(post, 0);
            Log.d("FILTER_SCROLL L", "scroll to " + post);
        } else if (left >= sRect.left && right >= sRect.right) {
            // right-side of the view is partially visible
            int post = right - sRect.right + x;
            chipScrollView.smoothScrollBy(post, 0);
            Log.d("FILTER_SCROLL R", "scroll to " + post);
        }
    }

    @Override
    public void onOnboardingDismiss(@Nullable String id, @NotNull String body, boolean isFromButton, boolean isFromCloseButton, boolean isFromOutside) {
        isCoachmarkShowing = false;
        OnboardingPrefManager.Companion.getInstance().setHasFinishedForId(id);
        if (OnboardingPrefManager.TUTORIAL_DATE_FILTER.equals(id) || OnboardingPrefManager.TUTORIAL_DOWNLOAD_REPORT_CASH.equals(id)) {
            showTooltip();
        }
    }

    @Override
    public void onOnboardingButtonClicked(@Nullable String id, boolean isFromHighlight) {
        isExitDialogEnabled = RemoteConfigUtils.INSTANCE.shouldShowExitDialog();
        areNewCategoriesEnabled = RemoteConfigUtils.SelectCategory.INSTANCE.areNewCategoriesVisible();
        if (id == null) return;
        switch (id) {
            case OnboardingPrefManager.TUTOR_TRANSACTION_TAB: {
                AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder();
                if (isFromHighlight)
                    builder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TOOLTIP_CREATE);
                else
                    builder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TOOLTIP_CONTINUE);
                builder.put(AnalyticsConst.TYPE, "NEW");
                builder.put(AnalyticsConst.EXIT_DIALOGUE_ENABLED, isExitDialogEnabled);
                builder.put(AnalyticsConst.NEW_TRX_CATEGORY_ENABLED, areNewCategoriesEnabled);
                builder.put(AnalyticsConst.MANDATORY_CATEGORY_ENABLED, RemoteConfigUtils.INSTANCE.showMandatoryTransactionCategory());
                builder.put(AnalyticsConst.DEFAULT_CATEGORY_EXPENSES, RemoteConfigUtils.INSTANCE.isDefaultPengeluaranStock());
                builder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsTransaction());
                builder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewPosInvoice());
                //AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, builder);
                Intent intent = NewCashTransactionActivity.createIntent(getActivity());
                intent.putExtra(NewCashTransactionActivity.SHOW_INTRO, true);
                intent.putExtra(NewCashTransactionActivity.TRX_TYPE, transCount < RemoteConfigUtils.INSTANCE.getSalesOnboardingThreshold());
                startActivity(intent);
                break;
            }
            case OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN: {
                AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder();
                if (isFromHighlight)
                    builder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TOOLTIP_CREATE);
                else
                    builder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.TOOLTIP_CONTINUE);
                builder.put(AnalyticsConst.DEFAULT_CATEGORY_EXPENSES, RemoteConfigUtils.INSTANCE.isDefaultPengeluaranStock());
                builder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsTransaction());
                builder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewPosInvoice());
                //AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, builder);
                addTransaction(getActivity(), true);
                break;
            }
            case OnboardingPrefManager.TUTORIAL_DOWNLOAD_REPORT_CASH: {
                goToTransactionReport();
                break;
            }
            case OnboardingPrefManager.TOUR_AUTORECORD_TXN_BTN: {
                if(firstAutoRecordTxn != -1 && transactionRecyclerView.findViewHolderForAdapterPosition(firstAutoRecordTxn)!=null){
                    transactionRecyclerView.findViewHolderForAdapterPosition(firstAutoRecordTxn).itemView.performClick();
                }
                break;
            }
        }
    }

    private void showGamifyPopup() {
        GamifyTarget transactionGamify = GamifyTarget.NEW_TRANSAKSI_TRANSACTION;
        ((MainActivity) getActivity()).showGamifyPopup(transactionGamify);
    }

    public void onGamifyShow() {
        isCoachmarkShowing = true;
    }

    public void setCoachmarkStatus() {
        if (!AppConfigManager.getInstance().getTransaksiOnboardingCoachmarkShown()) {
            AppConfigManager.getInstance().setTransaksiOnboardingCoachmarkShown(true);
        }
        coachmarkShown = true;
        isCoachmarkShowing = true;
    }

    public void showOnboardingTutorial() {
        setCoachmarkStatus();
        if (trxBlankScreenVariant.equals(RemoteConfigUtils.TRX_BLANK_SCREEN_VARIANT_SHOW_COACHMARK))
            showOldFormOnboarding();
    }

    public void showOnboardingTutorialOnClick() {
        setCoachmarkStatus();
        showOldFormOnboarding();
    }

    public void showAutoRecordOnboardingTutorialOnClick(View itemView, int firstAutoRecordTxnIndex) {
         firstAutoRecordTxn = firstAutoRecordTxnIndex;
//         transactionRecyclerView.getLayoutManager().scrollToPosition(firstAutoRecordTxn);
            try {
                boolean isSnackBarClick = AppConfigManager.getInstance().getTransaksiOnboardingCoachmarkShown();
                onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), IncomeExpenseTab.this,
                        OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN, itemView, null, getString(R.string.automatically_recorded_txn_onboarding),
                        getString(R.string.all_expense_income_successfully_recorded), getString(R.string.check_details), FocusGravity.CENTER, ShapeType.ROUND_RECT,
                        1, 1, true, isSnackBarClick);

            } catch (Exception e) {
                e.printStackTrace();
            }
    }

    public void showOldFormOnboarding() {
        try {
            boolean isSnackBarClick = AppConfigManager.getInstance().getTransaksiOnboardingCoachmarkShown();
            onboardingWidget = OnboardingWidget.Companion.createInstance(getActivity(), IncomeExpenseTab.this,
                    OnboardingPrefManager.TOUR_CASH_TAB_ADD_BTN, addBtn, R.drawable.onboarding_smile, "",
                    getString(R.string.body_add_cash_btn), getString(R.string.next), FocusGravity.CENTER, ShapeType.ROUND_RECT,
                    1, 2, true, isSnackBarClick);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onGamifyDismissed(boolean shouldShowTooltip) {
        isCoachmarkShowing = false;
        if (shouldShowTooltip) showTooltip();
    }

    public void onBackPressed() {
        if (onboardingWidget != null && onboardingWidget.isShown()) {
            onboardingWidget.dismiss(false, false, true);
        } else if (getActivity() != null && listener != null) {
            listener.handleNormalBackPressedFromIncomeExpense();
        } else if (getActivity() != null) {
            getActivity().finish();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N_MR1)
    private void updateShortcut(int trxCount) {

        if (trxCount > 1) {
            ShortcutManager shortcutManager = getContext().getSystemService(ShortcutManager.class);

            Intent intent = new Intent(getActivity(), TransactionReportActivity.class);
            intent.putExtra("targetId", "2");
            intent.setAction(Intent.ACTION_VIEW);
            intent.setType(getString(R.string.from_transaction));

            String label = getString(R.string.check_profit);
            String profit = FeaturePrefManager.getInstance().getCashBalanceStatus();

            if (profit.equalsIgnoreCase(AnalyticsConst.PROFIT)) {
                label = Utility.getCurrency() + FeaturePrefManager.getInstance().getCashBalanceValue()
                        + " " + getString(R.string.profit_shortcut);
            }

            ShortcutInfo profitShortcut = new ShortcutInfo.Builder(getContext(), getString(R.string.shortcut_1))
                    .setShortLabel(label)
                    .setLongLabel(label)
                    .setIcon(Icon.createWithResource(getContext(), R.drawable.ic_profit))
                    .setIntents(new Intent[]{
                            new Intent(Intent.ACTION_MAIN, Uri.EMPTY, getActivity(), MainActivity.class).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK),
                            intent
                    })
                    .build();

            Intent transactionIntent;
            transactionIntent = NewCashTransactionActivity.createIntent(getContext());
            transactionIntent.putExtra(NewCashTransactionActivity.TRX_TYPE, AppConfigManager.getInstance().getTransactionType());

            transactionIntent.setAction(Intent.ACTION_VIEW);
            transactionIntent.setType(getString(R.string.show_profit));
            ShortcutInfo transaction = new ShortcutInfo.Builder(getContext(), getString(R.string.shortcut_2))
                    .setShortLabel(getString(R.string.record_transaksi))
                    .setLongLabel(getString(R.string.record_transaksi))
                    .setIntents(new Intent[]{
                            new Intent(Intent.ACTION_MAIN, Uri.EMPTY, getContext(), MainActivity.class).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK),
                            transactionIntent
                    })
                    .setIcon(Icon.createWithResource(getContext(), R.drawable.ic_transaksi))
                    .setRank(2)
                    .build();

            if (shortcutManager != null) {
                shortcutManager.setDynamicShortcuts(Arrays.asList(profitShortcut, transaction));
            }
        }
    }

    private void setFiltersVisibility() {
        int count = TransactionRepository.getInstance(getActivity().getApplicationContext()).countAllCashTransWithDeleted(User.getBusinessId());
        if (!FeaturePrefManager.getInstance().isDateFilterTransactionSeen()) {
            ComponentUtil.setVisible(chipScrollView, count >= dateFilterVisibilityLimit);
        }
        if (!FeaturePrefManager.getInstance().isSearchFilterTransactionSeen()) {
            ComponentUtil.setVisible(searchFilterLayout, count >= searchVisibilityLimit);
        }

        if (count >= dateFilterVisibilityLimit) {
            FeaturePrefManager.getInstance().setIsDateFilterTransactionSeen();
        }

        if (count >= searchVisibilityLimit) {
            FeaturePrefManager.getInstance().setIsSearchFilterTransactionSeen();
        }
    }

    public void initiateTransactionFromDailyBusinessUpdate(Context context, boolean showIntro) {
        fromDailyBusiness = true;
        addTransaction(context, showIntro);
    }

    public void addTransaction(Context context, boolean showIntro) {


        isExitDialogEnabled = RemoteConfigUtils.INSTANCE.shouldShowExitDialog();
        areNewCategoriesEnabled = RemoteConfigUtils.SelectCategory.INSTANCE.areNewCategoriesVisible();
        if (!AuthHelper.isValidSessionOperation()) {
            if (isAdded() && getActivity() != null)
                ((MainActivity) getActivity()).callLoginBottomsheet(false, AnalyticsConst.TRANSAKSI);
            return;
        }
        if (context == null)
            return;

        AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder();
        String type = "grid";
        if (RemoteConfigUtils.INSTANCE.getCategoryUIVariant() == 2) {
            type = "list";
        }
        builder.put(AnalyticsConst.ENTRY_POINT2, fromDailyBusiness ? AnalyticsConst.BW_STORY : AnalyticsConst.NON_BW_STORY );
        builder.put(AnalyticsConst.DEFAULT_CATEGORY_EXPENSES, RemoteConfigUtils.INSTANCE.isDefaultPengeluaranStock());
        builder.put(AnalyticsConst.CATEGORY_PAGE_VIEW_VARIANT, type);
        builder.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsTransaction());
        builder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewPosInvoice());
        //AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, builder);
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, requireActivity());
        boolean trxType = transCount < RemoteConfigUtils.INSTANCE.getSalesOnboardingThreshold();
        Intent intent = NewCashTransactionActivity.createIntent(context);
        if (showLastTransactionType) {
            trxType = AppConfigManager.getInstance().getTransactionType();
        }
        intent.putExtra(NewCashTransactionActivity.TRX_TYPE, trxType);
        intent.putExtra(NewCashTransactionActivity.SHOW_INTRO, showIntro);
        if(fromDailyBusiness){
            intent.putExtra(NewCashTransactionActivity.FROM_DAILY_BUSINESS_UPDATE, true);
        }
        startActivity(intent);
        fromDailyBusiness = false;
    }
}
