package com.bukuwarung.activities.expense

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.core.widget.doAfterTextChanged
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.dataholder.BaseProductDataHolder
import com.bukuwarung.activities.expense.adapter.dataholder.ProductDataHolder
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.getClassTag
import kotlinx.android.synthetic.main.layout_customer_number_stepper.view.*

class NumberStepper @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    private var checkBox: AppCompatCheckBox? = null
    private var dataHolder: ProductDataHolder? = null
    private var callback: ((ProductDataHolder?) -> Unit)? = null


    init {
        View.inflate(context, R.layout.layout_customer_number_stepper, this)
    }

    fun setup(_checkBox: AppCompatCheckBox, _dataHolder: ProductDataHolder, _callback: (ProductDataHolder?) -> Unit) {
        callback = _callback
        dataHolder = _dataHolder
        checkBox = _checkBox
        updateView(false)


        checkBox?.setOnCheckedChangeListener { _, b ->
            Log.d(getClassTag(), "listener: $b | value : ${dataHolder?.quantity} ")
            if (dataHolder?.quantity!! == 0.0 && b) {
                Log.d(getClassTag(), "set to 1")
                dataHolder?.quantity = 1.0
                updateView()
                val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CHECKMARK)
                prop.put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(dataHolder?.quantity))
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAIL_ADD, prop)
                return@setOnCheckedChangeListener
            }

            if (dataHolder?.quantity!! > 0 && !b) {
                Log.d(getClassTag(), "set to 0")
                dataHolder?.quantity = 0.0
                updateView()
                return@setOnCheckedChangeListener
            }
        }

        setupView()
    }

    private fun setupView() {
        btnDecrease.setOnClickListener {
            etNumber?.clearFocus()
            if (dataHolder?.quantity!! > 0) {
                dataHolder?.apply { quantity-- }
                updateView()
            }
        }

        btnIncrease.setOnClickListener {
            etNumber?.clearFocus()
            if (dataHolder?.quantity == 0.0) {
                val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.PLUS_BUTTON)
                prop.put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(dataHolder?.quantity))
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAIL_ADD, prop)
            }
            dataHolder?.apply { quantity++ }
            updateView()
        }

        etNumber.setOnEditorActionListener { _, i, _ ->
            var handled = false
            if (i == EditorInfo.IME_ACTION_DONE) {
                dataHolder?.quantity = Utility.extractAmountFromText(etNumber.text.toString())
                updateView()

                etNumber.clearFocus()

                val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CUSTOM_QUANTITY)
                prop.put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(dataHolder?.quantity))
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAIL_ADD, prop)
                handled = true
            }

            handled
        }

        etNumber.doAfterTextChanged {
            dataHolder?.quantity = Utility.extractAmountFromText(it.toString())
        }

        etNumber.setOnFocusChangeListener { _, b ->
            if (b) {
                etNumber.setSelection(etNumber.text.length)
            } else {
                val tvValue = Utility.extractAmountFromText(etNumber.text.toString())
                Log.d(getClassTag(), "lost focus : $tvValue")
                dataHolder?.quantity = tvValue

                val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CUSTOM_QUANTITY)
                prop.put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(dataHolder?.quantity))
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAIL_ADD, prop)

                updateView()
            }
        }
    }

    fun toggleChecked() {
        when (dataHolder?.quantity!!) {
            0.0 -> {
                dataHolder?.quantity = 1.0
                val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CHECKMARK)
                prop.put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(dataHolder?.quantity))
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PRODUCT_DETAIL_ADD, prop)
            }
            else -> dataHolder?.quantity = 0.0
        }

        updateView()
    }


    private fun updateView(invoke: Boolean = true) {
        checkBox?.isChecked = dataHolder?.quantity!! > 0
        dataHolder?.apply {
            isChecked = checkBox?.isChecked ?: false
            tag = if (isChecked) {
                BaseProductDataHolder.CHECKED
            } else {
                BaseProductDataHolder.UNCHECKED
            }
        }

        etNumber.apply {
            isEnabled = dataHolder?.quantity!! > 0

            dataHolder?.let {
                if (it.quantity > 0) {
                    etNumber.setText(Utility.getRoundedOffPrice(it.quantity))
                } else {
                    etNumber.setText("")
                }
            }
        }

        if (invoke) {
            callback?.invoke(dataHolder)
        }
    }

}