package com.bukuwarung.activities.expense

import android.graphics.Bitmap
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bukuwarung.Application
import com.bukuwarung.activities.expense.category.Category
import com.bukuwarung.activities.expense.category.CategoryUtil
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.NOTA_STANDARD_ENABLED
import com.bukuwarung.constants.AnalyticsConst.SMS_CHECKBOX_ENABLED
import com.bukuwarung.constants.AppConst.*
import com.bukuwarung.database.dto.TransactionItemDto
import com.bukuwarung.database.entity.*
import com.bukuwarung.database.entity.extension.getCustomerForInvoice
import com.bukuwarung.database.entity.extension.getNonExistCustomerContact
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObjectViewHolderType
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.cash.CashDto
import com.bukuwarung.domain.cash.CashUseCase
import com.bukuwarung.domain.customer.CustomerUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.domain.product.ProductUseCase
import com.bukuwarung.domain.transaction.TransactionUseCase
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.viewmodels.BasePaymentViewModel
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bukuwarung.utils.RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm
import com.bukuwarung.utils.RemoteConfigUtils.getCategoryUIVariant
import com.bukuwarung.utils.RemoteConfigUtils.showNewTransactionCategory
import com.bukuwarung.wrapper.EventWrapper
import com.google.android.gms.tasks.OnFailureListener
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.UploadTask
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream
import java.util.*
import javax.inject.Inject
import kotlin.math.abs


class CashTransactionViewModel @Inject constructor(private var cashUseCase: CashUseCase,
                                                   private var transactionUseCase: TransactionUseCase,
                                                   private var productUseCase: ProductUseCase,
                                                   private var businessUseCase: BusinessUseCase,
                                                   private var paymentUseCase: PaymentUseCase,
                                                   private var customerUseCase: CustomerUseCase,
                                                   private var onboardingPrefManager: OnboardingPrefManager,
                                                   private var sessionManager: SessionManager) : BasePaymentViewModel(paymentUseCase) {

    sealed class State {
        data class GetLanguage(val language: Int) : State()
        data class UpdateUi(val isExpense: Boolean,val trxType: Int) : State()
        data class InitKeyboardView(val cashTransactionEntity: CashTransactionEntity?) : State()
        data class SetTransactionEntityData(val cashTransactionEntity: CashTransactionEntity, val selectedCategory: Category?) : State()
        data class SetCategoryData(val selectedCategory: Category?) : State()
        data class SetCategoryByFrequency(val topfiveCategory: List<CashCategoryEntity>?) : State()
        data class SetSelectedProduct(val products: List<TransactionItemDto>) : State()
        data class ShowProductDialog(val existingProducts: List<TransactionItemDto>) : State()
        data class ShowCategoryDialog(val categoryList: MutableList<Category>, val trxType: Int,
                                      val selectedCategory: Category?) : State()

        data class ShowCategorySelectionOption(val trxType: Int, val trxCategory: String?): State()

        data class OnAddCashFinished(val cashTrxId: String?, val showMessage: Boolean = false, val status: Int) : State()
        data class ShowDatePicker(val transactionDate: String?) : State()
        object StartMainActivity : State()
        object ShowModalTutorial : State()
        object InventoryFeatureEnabled : State()
        data class ShowProductDetailTutorial(val isInventoryEnabled: Boolean) : State()
        data class SetFieldsState(val showDetailFields: Boolean, val showProductField: Boolean, val showLabel: Boolean = false) : State()
        data class SetExistingTrxStatus(val isPaid: Boolean) : State()
        data class ShowSelectedCustomer(val customerEntity: CustomerEntity?, val contact: Contact? = null) : State()
        data class SelectedUrlForAttachment(val firebaseUrl: String?): State()
        object OnDeleteAttachment: State()
    }

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private var bookEntity: BookEntity? = null
    private var cashTransactionId: String? = null
    private var cashCategoryEntity: CashCategoryEntity? = null
    var transactionEntity: CashTransactionEntity? = null
    private var selectedCategory: Category? = null
    private var category: String? = null
    private var trxType = -1
    private var productList = listOf<TransactionItemDto>()
    private var transactionDate: String? = null

    private var customerEntity: CustomerEntity? = null
    private var contact: Contact? = null
    private var optionalMenuState = false
    private var categoryFilled: String? = null
    private var customerSource: String? = null
    private var FIREBASE_PATH: String = "ocr_demo"



    sealed class Event {
        data class OnCategorySelected(val selectedCategory: Category?) : Event()
        data class OnImageCaptured(val bitmap: Bitmap?): Event()
        data class OnTrxTypeChange(val type: Int) : Event()
        data class OnProductListSelected(val productList: List<TransactionItemDto>) : Event()
        data class OnCreateView (val id: String?, val type: String?, val categoryId: String?) : Event()
        object OnCategoryLayoutClicked : Event()
        object OnProductClicked : Event()
        object OnDateClicked : Event()
        object OnBalanceModalClicked : Event()
        data class OnTransactionDateChanged(val transactionDate: String?) : Event()
        data class SaveCustomerTransaction(
            val balanceText: String,
            val balanceModal: String,
            val note: String,
            val status: Int,
            val categorySelectedStatus: Int,
            val sendSms: Boolean,
            val attachment: String?,
            var imgSrc: String?,
            val fromDailyBusinessUpdate: Boolean,
            val from: String? = null,
            val categorySelected: String? = null,
            val transactionType: Int
        ) : Event()
        data class OnProductSelectionComplete(val products: List<TransactionItemDto>) : Event()
        data class OnKeyboardSubmit(val balanceStr: String, val modalStr: String) : Event()
        data class OnCustomerSelected(val contact: Contact?, val customerSource: String? = null) : Event()
        data class OptionalMenuStateChange(val state: Boolean) : Event()
        data class PaidStatusChange(val status: NewCashTransactionActivity.Companion.TransactionStatus) : Event()
        data class OnDeleteAttachment(val attachmentUrl: String?): Event()
        data class OnNewCategoryClicked(val category: String?): Event()

    }

    data class PaymentViewState(
            val showLoading: Boolean = false,
            val hasBankAccount: Boolean = false,
            val showError: Boolean = false
    )

    private fun currentPaymentViewState(): PaymentViewState = paymentViewState.value!!
    val paymentViewState: MutableLiveData<PaymentViewState> = MutableLiveData(PaymentViewState())

    private var hasInitBankAccount = false

    private var currentMode = NewCashTransactionActivity.Companion.TransactionStatus.MODE_FULLY_PAID
    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnCreateView -> handleOnCreateView(event.id, event.type, event.categoryId)
            is Event.OnTrxTypeChange -> handleTrxTypeChange(event.type)
            Event.OnCategoryLayoutClicked -> categoryLayoutClicked()
            is Event.OnCategorySelected -> selectedCategory = event.selectedCategory
            Event.OnProductClicked -> setState(State.ShowProductDialog(productList))
            Event.OnBalanceModalClicked -> checkModalTutorial()
            is Event.OnProductListSelected -> productList = event.productList
            is Event.OnTransactionDateChanged -> transactionDate = event.transactionDate
            is Event.OnDateClicked -> setState(State.ShowDatePicker(transactionDate))
            is Event.SaveCustomerTransaction -> saveCustomerTransaction(event.balanceText, event.balanceModal, event.note,
                event.status, event.categorySelectedStatus, event.sendSms, event.attachment, event.imgSrc,event.fromDailyBusinessUpdate,
                event.from, event.categorySelected, event.transactionType)
            is Event.OnProductSelectionComplete -> setProductSelected(event.products)
            is Event.OnKeyboardSubmit -> handleKeyboardSubmit(event.balanceStr, event.modalStr)
            is Event.OnCustomerSelected -> handleOnCustomerSelected(event.contact, event.customerSource)
            is Event.OptionalMenuStateChange -> optionalMenuState = event.state
            is Event.PaidStatusChange -> handlePaidStatusChange(event.status)
            is Event.OnImageCaptured -> uploadToFirebase(event.bitmap)
            is Event.OnDeleteAttachment -> deleteAttachment(event.attachmentUrl)
            is Event.OnNewCategoryClicked -> categoryClicked(event.category)
        }
    }

    private fun deleteAttachment(attachmentUrl: String?) {
        attachmentUrl?.let {
            val imageLocationRef = FirebaseStorage.getInstance().getReferenceFromUrl(attachmentUrl);
            imageLocationRef.delete().addOnSuccessListener {
                setState(State.OnDeleteAttachment)
            }.addOnFailureListener {

            }
        }
    }

    private fun handleTrxTypeChange(type: Int) {
        trxType = type
        getCategoryByFrequency(trxType)
        setState(State.SetSelectedProduct(productList))
    }

    private fun handlePaidStatusChange(status: NewCashTransactionActivity.Companion.TransactionStatus) {
        currentMode = status
    }

    private fun handleOnCustomerSelected(contact: Contact?, customerSource: String?) {
        this.contact = contact
        this.customerSource = customerSource
        if (contact?.customerId != null && contact.customerId.isNotBlank()) {
            customerEntity = customerUseCase.getCustomerById(contact.customerId)
            setState(State.ShowSelectedCustomer(customerEntity))
        } else {
            setState(State.ShowSelectedCustomer(null, contact))
        }
    }

    private fun uploadToFirebase(bitmap: Bitmap?) {
        bitmap?.let {
            val baos = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, baos)
            val data: ByteArray = baos.toByteArray()
            var userId = if(Utility.isBlank(User.getUserId())) {
                    "user_upload"
            } else {
                User.getUserId()
            }
            val imageLocationRef = FirebaseStorage.getInstance().getReference().child( FIREBASE_PATH + "/"
                    + userId + "/" + Utility.uuid())

            val uploadTask: UploadTask = imageLocationRef.putBytes(data)

            uploadTask.addOnFailureListener(object : OnFailureListener {
                override fun onFailure(exception: java.lang.Exception) {

                }
            }).addOnSuccessListener {
                val result = it.metadata!!.reference!!.downloadUrl;
                result.addOnSuccessListener {
                    val imageLink = it.toString()
                    setState(State.SelectedUrlForAttachment(imageLink))
                }
            }
        }
    }

    private fun handleKeyboardSubmit(balanceStr: String, modalStr: String) {
        val balanceValue = Utilities.cleanStrNominal(balanceStr)
        val modalValue = Utilities.cleanStrNominal(modalStr)

        val cashTrxCount = cashUseCase.getCashTrxCountForCurrentBusinessId()
        val showDetailFields = (balanceValue > 0) || (modalValue > 0)

        if (!onboardingPrefManager.getHasFinishedForId(OnboardingPrefManager.TUTOR_PRODUCT_DETAIL) && cashTrxCount == 1) {
            setState(State.ShowProductDetailTutorial(false)) // show ProductDetailTutorial on 2nd trx creation
        }
        //disable stock enabled tutorial as it's coming behind keyboard
//        else if (FeaturePrefManager.getInstance().stockTabEnabled() && cashTrxCount >= 1) {
//            if (!onboardingPrefManager.getHasFinishedForId(OnboardingPrefManager.TUTOR_PRODUCT_DETAIL_WITH_INVENTORY)) {
//                setState(State.ShowProductDetailTutorial(true))
//            }
//        }

        setState(State.SetFieldsState(showDetailFields, cashTrxCount > 0 && showDetailFields, FeaturePrefManager.getInstance().stockTabEnabled() && cashTrxCount >= 1))

    }

    private fun checkModalTutorial() {
        if (!onboardingPrefManager.getHasFinishedForId(OnboardingPrefManager.TUTORIAL_CASH_MODAL)) {
            setState(State.ShowModalTutorial)
            onboardingPrefManager.setHasFinishedForId(OnboardingPrefManager.TUTORIAL_CASH_MODAL)
        }
    }

    /**this method will decide to create or to update CashTransaction*/
    private fun saveCustomerTransaction(
        balanceText: String,
        balanceModal: String,
        note: String,
        status: Int,
        categorySelectedStatus: Int,
        sendSms: Boolean,
        attachment: String?,
        imgSrc: String?,
        fromDailyBusinessUpdate: Boolean,
        from: String? = null,
        categorySelected: String? = null,
        transactionType: Int
    ) = launch {
        if (selectedCategory == null) {
            if (RemoteConfigUtils.showMandatoryTransactionCategory()) {
                selectedCategory =
                    getCategoryById(if (trxType == DEBIT) CATEGORY_PEMBELIAN_STOK else CATEGORY_CASH_IN)
            } else {
                selectedCategory = getCategoryById(if (trxType == DEBIT) CATEGORY_CASH_OUT else CATEGORY_CASH_IN)
            }
            categoryFilled = AnalyticsConst.NOT_FILLED
        } else {

            categoryFilled = selectedCategory?.name
        }

        val balanceStr = try {
            if (SessionManager.getInstance().countryCode.contains("62") || SessionManager.getInstance().countryCode.contains("+62")) {
                balanceText.replace(".", "").replace(",", ".")
            } else {
                balanceText.replace(",", "")
            }
        } catch (ex: Exception) {
            ex.recordException()
            "0"
        }

        SessionManager.getInstance().prevTab = FeaturePrefManager.getInstance().getTabIndex(TabName.TRANSACTION)
        var balance = if (!Utility.isBlank(balanceStr)) balanceStr.toDouble() else 0.0
        var currentModal = 0.0
        if (trxType == DEBIT) {
            balance = 0 - balance
        } else {
            var cleanModal: String = balanceModal.replace(".", "").replace(",", ".")
            if (cleanModal.isEmpty()) cleanModal = "0"
            currentModal = cleanModal.toDouble()
        }
        trxType = transactionType

        if (transactionEntity == null) {
            Utility.trackTransactionCount()
            try {

                FeaturePrefManager.getInstance().setHasRecordedFirstCash(true)
            } catch (e: java.lang.Exception) {
                FirebaseCrashlytics.getInstance().log(e.localizedMessage)
            }
            createNewCashTransaction(balance, currentModal, note, status, categorySelectedStatus, sendSms, attachment, imgSrc,fromDailyBusinessUpdate, from, categorySelected)
        } else {
            updateExistingCashTransaction(balance, currentModal, note, status,categorySelectedStatus, sendSms, attachment, trxType)
        }
    }

    private fun handleOnCreateView(id: String?, type: String?, categoryId: String?) = launch {
        setState(State.GetLanguage(SessionManager.getInstance().appLanguage))
        cashTransactionId = id
        type?.run {
            if (this == DEBIT.toString() || this == CREDIT.toString()) {
                trxType = this.toInt()
            }
        }
        if (!cashTransactionId.isNullOrBlank()) {
            transactionEntity = cashUseCase.getSingleRawCashTransaction(cashTransactionId)
        }
        cashCategoryEntity = cashUseCase.getCashCategoryById(categoryId)
        when {
            transactionEntity != null -> {
                setState(State.UpdateUi(transactionEntity!!.amount < 0,cashCategoryEntity?.type!!))
                selectedCategory = getCategoryById(transactionEntity!!.cashCategoryId)
                transactionDate = transactionEntity!!.date
                val products = getTransactionProducts(transactionEntity!!.cashTransactionId)
                setState(State.SetSelectedProduct(products))
                setState(State.SetTransactionEntityData(transactionEntity!!, selectedCategory))

                val cashTrxCount = cashUseCase.getCashTrxCountForCurrentBusinessId()

                // populate current transaction customer if any
                if (transactionEntity!!.customerTransactionId?.isNotBlank().isTrue) {
                    val customerTransactionEntity = transactionUseCase.getCustomerTransactionById(transactionEntity!!.customerTransactionId!!)
                    customerTransactionEntity?.let {
                        val customerEntity = transactionUseCase.getCustomerById(it.customerId)
                                ?: return@let
                        setState(State.ShowSelectedCustomer(customerEntity))
                    }
                }
                setState(State.SetFieldsState(true, cashTrxCount > 0))
                setState(State.SetExistingTrxStatus(transactionEntity!!.status != 0))

                val customerForInvoice = transactionEntity?.getCustomerForInvoice()
                contact = transactionEntity?.getNonExistCustomerContact()
                setState(State.ShowSelectedCustomer(customerForInvoice))
            }
            cashCategoryEntity != null -> {
                selectedCategory = getCategoryById(cashCategoryEntity!!.cashCategoryId)

                setState(State.SetCategoryData(selectedCategory))
                setState(State.UpdateUi(cashCategoryEntity!!.type < 0,selectedCategory?.category_type!!))
                setState(State.SetFieldsState(showDetailFields = false, showProductField = false)) // hide productEt on 1st trx creation
            }
            trxType == CREDIT -> {
                setState(State.UpdateUi(false,trxType))
            }
            else -> {
//                val defaultCashOption = AppConfigManager.getInstance().defaultCash
//                // default value for creating new trx
//                setState(State.UpdateUi(defaultCashOption <= 0))
                setState(State.SetFieldsState(showDetailFields = false, showProductField = false)) // hide productEt on 1st trx creation
            }
        }
        setState(State.InitKeyboardView(transactionEntity))

        getCategoryByFrequency(trxType)

        if (FeaturePrefManager.getInstance().stockTabEnabled()) {
            setState(State.InventoryFeatureEnabled)
        }
    }

    private fun setProductSelected(products: List<TransactionItemDto>) {
      val tempListZeroPrice: ArrayList<TransactionItemDto>  = arrayListOf()
        val tempListNonZeroPrice: ArrayList<TransactionItemDto>  = arrayListOf()
        products.forEach {
            if( it.sellingPrice  == 0.0) {
                tempListZeroPrice.add(it)
            } else {
                tempListNonZeroPrice.add(it);
            }
        }
        tempListZeroPrice.sortBy { it.productName.toLowerCase() }
        tempListNonZeroPrice.sortBy { it.productName.toLowerCase() }
        tempListZeroPrice.addAll(tempListNonZeroPrice)

        setState(State.SetSelectedProduct(tempListZeroPrice))
    }


    private fun categoryLayoutClicked() {
        val data: MutableList<Category> = if (trxType == CREDIT) ArrayList(CategoryUtil.getCashInCategories()) else ArrayList(CategoryUtil.getCashOutCategories())

        val cashEntries = cashUseCase.getUniqueCategoryEntries(trxType)
        val additionalData = CategoryUtil.convertToCategory(cashEntries)


        for (additionalDatum in additionalData) {
            if (!data.contains(additionalDatum)) {
                additionalDatum.type = SelectableObjectViewHolderType.SECONDARY_CONTENT
                data.add(additionalDatum)
            }
        }
        // we add selected category if category didn't exist
        if (cashEntries!!.isNotEmpty() && selectedCategory != null && !data.contains(selectedCategory!!)) {
            data.add(selectedCategory!!)
        }
        setState(State.ShowCategoryDialog(data, trxType, selectedCategory))
    }

    private fun categoryClicked(category: String?) {
        setState(State.ShowCategorySelectionOption(trxType, category))
    }

    private fun getTransactionProducts(cashTransactionId: String?): List<TransactionItemDto> {
        return try {
            if (cashTransactionId != null) {
                val transactionItems = transactionUseCase.getTransactionItems(cashTransactionId)
                return convertTransactionItemsToDto(transactionItems)
            }
            ArrayList()
        } catch (ex: Exception) {
            ex.recordException()
            ArrayList()
        }
    }

    private fun convertTransactionItemsToDto(transactionItems: List<TransactionItemsEntity>?): List<TransactionItemDto> {
        val ret = ArrayList<TransactionItemDto>()
        if (transactionItems == null) return ret
        try {
            for (transactionItem in transactionItems) {
                val productEntity = productUseCase.getProductsById(transactionItem.productId)
                if (productEntity != null) {
                    val productSelection = TransactionItemDto()
                    if (cashTransactionId != null) productSelection.transactionId = cashTransactionId
                    productSelection.apply {
                        productId = transactionItem.productId
                        productName = productEntity.name
                        quantity = transactionItem.quantity
                        sellingPrice = productEntity.unitPrice
                        buyingPrice = productEntity.buyingPrice
                        measurementUnit = productEntity.measurementName
                    }
                    ret.add(productSelection)
                }
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
        return ret
    }

    public fun getCategoryById(id: String): Category {
        val categoryId = id.takeIf { !it.contains("::") } ?: id.split("::".toRegex()).first()

        val list = if (trxType == CREDIT) CategoryUtil.getCashInCategories() else CategoryUtil.getCashOutCategories()
        for (category in list) {
            if (Utility.areEqual(categoryId, category.id) || Utility.areEqual(id, category.id)) {
                return category
            }
        }
        if (categoryId.isNotEmpty()) {
            try {
                val categoryEntity = cashUseCase.getCashCategoryById(id)

                var category: Category? = null

                if (categoryEntity != null) {
                    category = Category(
                            categoryId,
                            categoryEntity?.name,
                            categoryEntity?.name,
                            trxType,
                            SelectableObjectViewHolderType.SECONDARY_CONTENT
                    )
                } else {
                    category = Category(
                            categoryId,
                            id,
                            id,
                            trxType,
                            SelectableObjectViewHolderType.SECONDARY_CONTENT
                    )
                }
                    val predefinedCategories = if (trxType == CREDIT) CategoryUtil.getCashInCategories() else CategoryUtil.getCashOutCategories()
                    if (predefinedCategories.contains(category)) category.type = SelectableObjectViewHolderType.PRIMARY_CONTENT
//                AppConfigManager.getInstance().addNewCashCategory(category, transactionType);
                return category
            } catch (ex: Exception) {
                ex.recordException()
            }
        }
        val propBuilder = PropBuilder()

        propBuilder.put("category_id", categoryId)
        if (transactionEntity != null && transactionEntity!!.cashTransactionId != null) propBuilder.put("cash_transaction_id", transactionEntity!!.cashTransactionId)
        AppAnalytics.trackEvent("trx_category_null", propBuilder)
        return Category("none", "None", "None", -1)
    }

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    private fun createNewCashTransaction(
        balance: Double,
        currentModal: Double,
        note: String,
        status: Int,
        categorySelectedStatus: Int,
        sendSms: Boolean,
        attachment: String?,
        imgSrc: String?,
        fromDailyBusinessUpdate: Boolean,
        from: String? = null,
        categorySelected: String? = null
    ) {
        val masterCashDto = CashDto(selectedCategory!!, contact, status, transactionDate, balance, currentModal, note, null, productList)
        val newTrxId = cashUseCase.createCashTransaction(masterCashDto, categorySelectedStatus,
            attachment, trxType)

        // TODO referral doesn't changed
        try {
            if (AppConfigManager.getInstance().useReferral()
                    && bookEntity != null && abs(balance) > AppConfigManager.getInstance().referralTxnVal) {
                ReferralRepository.getInstance().addTransactionPoints(bookEntity, false)
            }
            setState(State.OnAddCashFinished(cashTrxId = newTrxId, status = status))
        } catch (e: Exception) {
            e.recordException()
        }

        try {
            customerSource = when {
                contact?.customerId.isNotNullOrBlank() -> customerSource
                contact?.name.isNullOrBlank() -> AnalyticsConst.NOT_FILLED
                contact?.mobile.isNullOrBlank() -> AnalyticsConst.CUSTOMER_CREATE_MANUAL
                else -> customerSource
            }
            val totalPenjualan = balance
            val sumOfItemTotal = productList.sumByDouble { it.sellingPrice * it.quantity }
            val equationStatus = when {
                totalPenjualan == sumOfItemTotal -> {
                    AnalyticsConst.EQUALS
                }
                totalPenjualan > sumOfItemTotal -> {
                    AnalyticsConst.GREATER
                }
                else -> {
                    AnalyticsConst.LESS
                }
            }

            PropBuilder().apply {
                put(AnalyticsConst.CASH_TRANSACTION_TYPE, if (trxType == CREDIT) AnalyticsConst.CREDIT else AnalyticsConst.DEBIT)
                put(AnalyticsConst.CASH_TRANSACTION_AMOUNT, balance.toString())
                put(AnalyticsConst.BUYING_PRICE, currentModal)
                put(AnalyticsConst.TRANSACTION_ID, newTrxId)
                put(AnalyticsConst.FULLY_PAID, status == 1)
                put(AnalyticsConst.CUSTOMER_FILLED, contact?.name?.isNotEmpty() == true)
                put(AnalyticsConst.CUSTOMER_SOURCE, customerSource)
                put(AnalyticsConst.DESCRIPTION_FILLED, note.isNotEmpty())
                put(AnalyticsConst.CATEGORY_FILLED, categoryFilled)
                put(AnalyticsConst.DEFAULT_CATEGORY, if (trxType == DEBIT) TransactionCategory.PEMBELIAN_STOCK.category else TransactionCategory.PENJUALAN.category)
                put(AnalyticsConst.COLLAPSE_MENU_OPEN, optionalMenuState)
                put(AnalyticsConst.PRODUCT_COUNT, productList.size)
                put(AnalyticsConst.SUM_OF_ITEM_TOTAL, sumOfItemTotal)
                put(AnalyticsConst.TOTAL_PENJUALAN, totalPenjualan)
                put(AnalyticsConst.SEND_SMS_REMINDER, sendSms)
                put(AnalyticsConst.TOTAL_PENJUALAN_VS_ITEM_SUBTOTAL, equationStatus)
                put(AnalyticsConst.FORM, if (canShowOldTransactionForm()) AnalyticsConst.OLD_TRANSAKSI_FORM else AnalyticsConst.NEW_TRANSAKSI_FORM)
            }.also {
                val isFirstTransactionGlobally = cashUseCase.getCashTrxCountForCurrentBusinessId() <= 1
                val isFirstTransactionOnCategory = cashUseCase.getCashCategoryTrxCountForCurrentBusinessId(selectedCategory!!.getFormattedId()) <= 1

                val isTransksiImageUploadEnabled = RemoteConfigUtils.showTransksiImage()

                if (isTransksiImageUploadEnabled && imgSrc.isNotNullOrEmpty()) {
                    it.put(AnalyticsConst.IMAGE_SOURCE, imgSrc)
                }

                var totalProductBuyingPrice = 0.0
                productList.forEach { product ->
                    totalProductBuyingPrice += (product.buyingPrice * product.quantity)
                }
                it.put(AnalyticsConst.SUM_PRODUCT_BUYING_PRICE, totalProductBuyingPrice.toString())
                it.put(AnalyticsConst.ENTRY_POINT2, if (fromDailyBusinessUpdate) AnalyticsConst.BW_STORY else AnalyticsConst.NON_BW_STORY)

                if (from.isNotNullOrEmpty()) {
                    it.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }

                it.put(AnalyticsConst.MANDATORY_CATEGORY_ENABLED, RemoteConfigUtils.showMandatoryTransactionCategory())
                it.put(AnalyticsConst.DEFAULT_CATEGORY_EXPENSES, RemoteConfigUtils.isDefaultPengeluaranStock())
                var type = "grid"
                if (getCategoryUIVariant() == 2) {
                    type = "list"
                }
                if (categorySelected.isNullOrEmpty()) {
                    it.put(AnalyticsConst.TRX_CATEGORY_SOURCE, AnalyticsConst.CATEGORY_PIN)
                } else {
                    it.put(AnalyticsConst.TRX_CATEGORY_SOURCE, AnalyticsConst.CATEGORY_PAGE)
                }
                if (selectedCategory!!.name == TransactionCategory.PENJUALAN.category ||
                    selectedCategory!!.name == TransactionCategory.PEMBELIAN_STOCK.category) {
                    it.put(AnalyticsConst.DEFAULT_CATEGORY_CHOOSEN, true)
                    it.put(AnalyticsConst.CATEGORY_FILLED, "none")
                } else {
                    it.put(AnalyticsConst.DEFAULT_CATEGORY_CHOOSEN, false)
                    it.put(AnalyticsConst.CATEGORY_FILLED, selectedCategory!!.name)
                }
                it.put(AnalyticsConst.NEW_UX_CATEGORY_SELECTION_VARIANT, showNewTransactionCategory())
                it.put(AnalyticsConst.CATEGORY_PAGE_VIEW_VARIANT, type)
                it.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsTransaction())
                it.put("UI_form", "old_UI_Pre_june2022")
                it.put(NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())
                val event = if (isFirstTransactionGlobally || isFirstTransactionOnCategory) AnalyticsConst.EVENT_CREATE_NEW_CASH_TRANSACTION else AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_SAVED_CASH

                AppAnalytics.trackEvent(event, it)
            }
        } catch (ex: Exception) {
            ex.recordException()
        }

        try {
            if (sendSms && customerEntity != null) {
                val type = Utility.getTransactionTypeInt(balance)
                Utility.sendSms(customerEntity!!.phone, balance, type, Application.getAppContext(), customerEntity!!.altCustomerId, -1)
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }


    private fun updateExistingCashTransaction(balance: Double, currentModal: Double, note: String, status: Int, categorySelectedStatus: Int, sendSms: Boolean, attachment: String?, trxType: Int) {
        val cashDto = CashDto(selectedCategory!!, contact, status, transactionDate, balance, currentModal, note, transactionEntity, productList)
        val newTrxId = cashDto.currentCashTransaction?.cashTransactionId
        // possible
        cashUseCase.updateCashTransaction(cashDto, categorySelectedStatus, attachment, trxType)

        try {
            PropBuilder().apply {
                put(AnalyticsConst.BUYING_PRICE, currentModal)
                put(AnalyticsConst.CASH_TRANSACTION_AMOUNT, balance.toString())
                put(AnalyticsConst.CASH_TRANSACTION_TYPE, if (trxType == CREDIT) AnalyticsConst.CREDIT else AnalyticsConst.DEBIT)
                put(AnalyticsConst.CATEGORY_FILLED, categoryFilled)
                put(AnalyticsConst.COLLAPSE_MENU_OPEN, optionalMenuState)
                put(AnalyticsConst.CUSTOMER_FILLED, contact?.name?.isNotEmpty() == true)
                put(AnalyticsConst.DESCRIPTION_FILLED, note.isNotEmpty())
                put(AnalyticsConst.FORM, if (canShowOldTransactionForm()) AnalyticsConst.OLD_TRANSAKSI_FORM else AnalyticsConst.NEW_TRANSAKSI_FORM)
                put(AnalyticsConst.ACTION_BY, if (transactionEntity?.orderId.isNotNullOrEmpty()) AnalyticsConst.PAYMENTS else AnalyticsConst.ACCOUNTING)
                put(AnalyticsConst.FULLY_PAID, status == 1)
                put(AnalyticsConst.TRANSACTION_ID, newTrxId)
                put(AnalyticsConst.UPDATE_TYPE, AnalyticsConst.EDIT)
                put(AnalyticsConst.CUSTOMER_ID, customerEntity?.customerId)
                put(AnalyticsConst.ORDER_ID, cashDto.currentCashTransaction?.orderId)
                cashCategoryEntity?.cashCategoryId?.run {
                    val analyticsType = PpobConst.CATEGORY_ANALYTICS_MAP[this.substringBefore(":")]
                    if (analyticsType != null) put(AnalyticsConst.PPOB_TYPE, analyticsType)
                }
            }
        } catch (ex: Exception) {
            ex.recordException()
        }

        setState(State.OnAddCashFinished(cashTrxId = null, status = status))
    }

    fun onCategoryChosen(category: Category) {
        selectedCategory = category
        setState(State.SetCategoryData(selectedCategory))
    }


    private fun getCategoryByFrequency(trxType:Int) {

        val cashEntries = cashUseCase.getCategoryByFrequency(trxType)
        setState(State.SetCategoryByFrequency(cashEntries))
    }
}
