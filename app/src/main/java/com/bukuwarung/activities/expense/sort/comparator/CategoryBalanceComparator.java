package com.bukuwarung.activities.expense.sort.comparator;

import com.bukuwarung.activities.customer.sort.SortOrder;
import com.bukuwarung.database.dto.CashTransactionDto;

import java.util.Comparator;

public final class CategoryBalanceComparator implements Comparator<CashTransactionDto> {

    SortOrder sortOrder = SortOrder.ASC;
    public CategoryBalanceComparator(SortOrder order){
        this.sortOrder=order;
    }

    @Override
    public int compare(CashTransactionDto cashTransactionDto1, CashTransactionDto cashTransactionDto2) {
        double bal2 = Math.abs(cashTransactionDto2.transactionAmount);
        Double bal1 = Math.abs(cashTransactionDto1.transactionAmount);
        if(sortOrder == SortOrder.ASC)
            return Double.compare(bal1,bal2);
        else
            return Double.compare(bal2,bal1);
    }
}
