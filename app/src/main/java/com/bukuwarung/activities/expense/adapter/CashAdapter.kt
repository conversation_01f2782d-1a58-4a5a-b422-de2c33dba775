package com.bukuwarung.activities.expense.adapter


import android.content.ComponentName
import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.paging.AsyncPagedListDiffer
import androidx.paging.PagedList
import androidx.paging.PagedListAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.expense.adapter.dataholder.CashDataHolder
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.superclasses.DataHolder.CategoryRowHolder
import com.bukuwarung.activities.superclasses.DataHolder.DayDataHolder
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.Tag
import com.bukuwarung.database.entity.TransactionEntityType
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.RemoteConfigUtils.TransactionTabConfig.canShowOldTransactionForm
import com.google.android.material.button.MaterialButton
import kotlin.math.abs

class CashAdapter(val listener: CashAdapterListener? = null) : PagedListAdapter<DataHolder,RecyclerView.ViewHolder>(DIFF_CALLBACK) {
    private var firstAutoRecordTxnIndex: Int = -1
    private var autoRecordTxnOnboardingShown: Boolean = false
    private val oldForm: Boolean by lazy {canShowOldTransactionForm()}

    private var cashDataHolderList: List<DataHolder?>? = emptyList()

    private val mDiffer: AsyncPagedListDiffer<DataHolder> = AsyncPagedListDiffer(this, DIFF_CALLBACK)
    private var pageSize = 0
    private var total = 0

    private var trxBlankScreenVariant = RemoteConfigUtils.TrxBlankScreenExperiment.getVariant()
    private var trxBlankScreenExperimentVideoURL = RemoteConfigUtils.TrxBlankScreenExperiment.getVideoTutorialURL()

    interface CashAdapterListener {
        fun goToDetail(id: String, isExpense: Boolean, status: Int, isDetailTransaksi: Boolean,isAutoRecordTxn : Boolean)
        fun goToCategory(id: String, name: String)
        fun seeOnboardingTutorial()
        fun seeAutoRecordOnboardingTutorial(itemView: View, firstAutoRecordTxnIndex: Int)
    }

    class SummaryViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val amount: TextView = view.findViewById(R.id.expenseAmount)
        val name: TextView = view.findViewById(R.id.name)
        val transactionHeader = view.findViewById<View>(R.id.transaction_header)
        val transactionHeaderTitle = transactionHeader.findViewById<TextView>(R.id.dateHeader)
        val transactionHeaderAmount = transactionHeader.findViewById<TextView>(R.id.amountHeader)
    }

    class CategorySummaryViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvExpense: TextView = view.findViewById(R.id.tvExpense)
        val name: TextView = view.findViewById(R.id.name)
        val trxCount: TextView = view.findViewById(R.id.trxCount)
        val tvIncome: TextView = view.findViewById(R.id.tvIncome)
    }

    class NoResultViewHolder(view: View?) : RecyclerView.ViewHolder(view!!)
    class LastViewHolder(view: View?) : RecyclerView.ViewHolder(view!!)

    class CashViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val incomeAmount: TextView = view.findViewById(R.id.incomeAmount)
        val expenseAmount: TextView = view.findViewById(R.id.expenseAmount)
        val name: TextView = view.findViewById(R.id.name)
        val category: TextView = view.findViewById(R.id.category)
        val status: TextView = view.findViewById(R.id.not_paid)
    }

    inner class TutorialVideoViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        private val supportBtn: RelativeLayout = view.findViewById(R.id.cashHelpBtn)
        val cashWalkThruBtn: RelativeLayout = view.findViewById(R.id.cashWalkThruBtn)
        val clVideoTutorialSnackBar: ConstraintLayout = view.findViewById(R.id.cl_video_tutorial_snack_bar)
        val clCoachmarkVideoSnackBar: ConstraintLayout = view.findViewById(R.id.cl_coachmark_video_snack_bar)
        val btnCoachMark: MaterialButton = view.findViewById(R.id.btn_coachmark)
        val btnVideoTutorial: MaterialButton = view.findViewById(R.id.btn_video_tutorial)
        private val textView: TextView = view.findViewById(R.id.no_trans_header)
        private var userName: TextView = view.findViewById(R.id.userName)
        private var userMessage: TextView = view.findViewById(R.id.social_message)
        private var userImage: AppCompatImageView = view.findViewById(R.id.userImage)
        private var userReviewDate: TextView = view.findViewById(R.id.messageDate)

        init {
            try {
                when (trxBlankScreenVariant) {
                    RemoteConfigUtils.TRX_BLANK_SCREEN_VARIANT_SHOW_COACHMARK -> {
                        cashWalkThruBtn.visibility = View.VISIBLE
                        clVideoTutorialSnackBar.visibility = View.GONE
                        clCoachmarkVideoSnackBar.visibility = View.GONE
                    }
                    RemoteConfigUtils.TRX_BLANK_SCREEN_VARIANT_SHOW_VIDEO -> {
                        cashWalkThruBtn.visibility = View.GONE
                        clVideoTutorialSnackBar.visibility = View.VISIBLE
                        clCoachmarkVideoSnackBar.visibility = View.GONE
                    }
                    RemoteConfigUtils.TRX_BLANK_SCREEN_VARIANT_NO_COACHMARK_AND_VIDEO -> {
                        cashWalkThruBtn.visibility = View.GONE
                        clVideoTutorialSnackBar.visibility = View.GONE
                        clCoachmarkVideoSnackBar.visibility = View.GONE
                    }
                    RemoteConfigUtils.TRX_BLANK_SCREEN_VARIANT_SHOW_COACHMARK_AND_VIDEO -> {
                        cashWalkThruBtn.visibility = View.GONE
                        clVideoTutorialSnackBar.visibility = View.GONE
                        clCoachmarkVideoSnackBar.visibility = View.VISIBLE
                    }
                }

                userName.text = AppConfigManager.getInstance().transaksiUserName
                userImage = view.findViewById(R.id.userImage)
                if (!Utility.isBlank(AppConfigManager.getInstance().transaksiUserImage)) {
                    userImage.loadImageCircleCropped(AppConfigManager.getInstance().transaksiUserImage)
                } else {
                    userImage.loadImageCircleCropped(R.drawable.img_profile_2)
                }
                userMessage.text = AppConfigManager.getInstance().transaksiUserMsg
                userReviewDate.text = AppConfigManager.getInstance().transaksiReviewDate
            } catch (e: Exception) {
                e.recordException()
            }
            try {
                val stringBuilder = SpannableStringBuilder(view.context.getString(R.string.no_transaction_yet_basic))
                stringBuilder.setSpan(ForegroundColorSpan(
                        view.context.getColorCompat(R.color.buku_CTA)
                ), 6, 23, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
                textView.text = stringBuilder
                textView.textSize = 16f
//                if (!oldForm) {
//                    textView.visibility = View.INVISIBLE
//                }
            } catch (ex: Exception) {
                ex.recordException()
            }
            supportBtn.setOnClickListener {
                try {
                    AppAnalytics.trackEvent("transaksi_help_requested")
                    val sendIntent = Intent("android.intent.action.MAIN")
                    sendIntent.component = ComponentName("com.whatsapp", "com.whatsapp.Conversation")
                    sendIntent.action = Intent.ACTION_SEND
                    sendIntent.type = "text/plain"
                    sendIntent.putExtra(Intent.EXTRA_TEXT, "")
                    sendIntent.putExtra("jid", FeaturePrefManager.getInstance().whatsappId + "@s.whatsapp.net")
                    sendIntent.setPackage("com.whatsapp")
                    supportBtn.context.startActivity(sendIntent)
                } catch (e: Exception) {
                    e.recordException()
                }
            }
        }
    }

    private fun bindCashViewHolder(
        cashViewHolder: CashViewHolder,
        cashDataHolder: CashDataHolder,
        index: Int
    ) {
        val context = cashViewHolder.itemView.context
        cashViewHolder.itemView.setOnClickListener {
            if (cashDataHolder.cashCategoryEntity != null) {
                val isExpense = cashDataHolder.cashCategoryEntity.type == -1
                val isAutoRecordTxn = cashDataHolder.cashCategoryEntity.transactionType == TransactionEntityType.BRICK_TRANSACTION.name
                listener?.goToDetail(
                    cashDataHolder.cashCategoryEntity.cashTransactionid,
                    isExpense, cashDataHolder.cashCategoryEntity.status, true,isAutoRecordTxn
                )
            }
        }
        val cashEntity = cashDataHolder.cashCategoryEntity
        if (cashEntity == null)
            return
        var note = cashEntity.transactionDescription
        if (note == null || note.isEmpty()) note = context.getString(R.string.default_placeholder)

        if (AppConst.POS in cashEntity.cashCategoryId) {
            cashViewHolder.name.text =
                Utilities.getFormattedInvoiceId(cashEntity.getCashTransactionid())
        } else {
            cashViewHolder.name.text = note
        }

        cashViewHolder.category.text = cashDataHolder.cashCategoryEntity.categoryName
        val sb = StringBuilder()
        val amount = cashDataHolder.cashCategoryEntity.transactionAmount
        sb.append(Utility.formatAmount(abs(amount)))
        if (amount < 0) {
            cashViewHolder.expenseAmount.text = sb.toString()
            cashViewHolder.incomeAmount.text = context.getString(R.string.default_placeholder)
        } else {
            cashViewHolder.incomeAmount.text = sb.toString()
            val buyingSb = StringBuilder()
            val buyingPrice = cashDataHolder.cashCategoryEntity.buyingPrice
            buyingSb.append(Utility.formatAmount(abs(buyingPrice)))
            cashViewHolder.expenseAmount.text = buyingSb.toString()
        }
        if (cashDataHolder.cashCategoryEntity.status == AppConst.BELUM_LUNAS) {
            cashViewHolder.status.visibility = View.VISIBLE
        } else {
            cashViewHolder.status.visibility = View.INVISIBLE
        }

        if(cashDataHolder.cashCategoryEntity.transactionType == TransactionEntityType.BRICK_TRANSACTION.name && !autoRecordTxnOnboardingShown && FeaturePrefManager.getInstance().isAutoRecordIntroduced == false){
            firstAutoRecordTxnIndex = index
            showAutoRecordCoachmark(cashViewHolder.itemView,firstAutoRecordTxnIndex)
            autoRecordTxnOnboardingShown = true
            FeaturePrefManager.getInstance().isAutoRecordIntroduced = true
        }
    }

    private fun bindSummaryViewHolder(summaryViewHolder: SummaryViewHolder, dayRowHolder: DayDataHolder) {
        val context = summaryViewHolder.itemView.context
        summaryViewHolder.name.text = dayRowHolder.cash.name
        val expenseDouble: Double = if (Utility.isBlank(dayRowHolder.cash.expense)) 0.0 else dayRowHolder.cash.expense.toDouble()
        val incomeDouble: Double = if (Utility.isBlank(dayRowHolder.cash.income)) 0.0 else dayRowHolder.cash.income.toDouble()
//        if (!oldForm) {
//            summaryViewHolder.transactionHeaderTitle.text = context.getString(R.string.name)
//            summaryViewHolder.transactionHeaderAmount.text = context.getString(R.string.sales)
//        }
        //calculate nett balance
        val balanceAmount = abs(incomeDouble) - abs(expenseDouble)
        val sb = StringBuilder()
        //set profile or loss suffix based on nett balance
        sb.append(if (balanceAmount < 0) context.getString(R.string.loss_text) else context.getString(R.string.profit_text))
        sb.append(" ")
        //currency
        sb.append(Utility.getCurrency())
        //format amount based on currency
        sb.append(Utility.formatCurrency(abs(balanceAmount)))
        summaryViewHolder.amount.text = sb.toString()

        //decide text color by nett amount, default value is profit
        summaryViewHolder.amount.setTextColor(context.getColorCompat(if (balanceAmount < 0) R.color.header_red_font else R.color.header_green_font))
    }

    private fun bindTutorialViewHolder(viewHolder: TutorialVideoViewHolder) {
        viewHolder.cashWalkThruBtn.setOnClickListener {
            showCoachmark()
        }

        viewHolder.clVideoTutorialSnackBar.setOnClickListener {
            openVideoTutorial(viewHolder)
        }

        viewHolder.btnCoachMark.setOnClickListener {
            showCoachmark()
        }

        viewHolder.btnVideoTutorial.setOnClickListener {
            openVideoTutorial(viewHolder)
        }
    }

    private fun bindCatSummaryViewHolder(summaryViewHolder: CategorySummaryViewHolder, dayRowHolder: CategoryRowHolder) {
        val context = summaryViewHolder.itemView.context
        summaryViewHolder.itemView.setOnClickListener {
            listener?.goToCategory(dayRowHolder.cash.id, dayRowHolder.cash.name)
        }
        summaryViewHolder.name.text = dayRowHolder.cash.name
        try {
            val trxCount = dayRowHolder.cash.getTrxCount()
            summaryViewHolder.trxCount.text = context.getString(R.string.cash_tab_trx_count, trxCount)
        } catch (ex: Exception) {
            summaryViewHolder.trxCount.text = context.getString(R.string.cash_tab_trx_count, 0)
            ex.recordException()
        }
        if (dayRowHolder.cash.type <= 0) {
            val sb = StringBuilder()
            sb.append(Utility.getCurrency())
            val d: Double = if (Utility.isBlank(dayRowHolder.cash.expense)) 0.0 else dayRowHolder.cash.expense.toDouble()
            sb.append(Utility.formatCurrency(abs(d)))
            summaryViewHolder.tvExpense.text = sb.toString()
            summaryViewHolder.tvIncome.text = "-"
        } else {
            val sbIncome = StringBuilder()
            sbIncome.append(Utility.getCurrency())
            val incomeDouble: Double = if (Utility.isBlank(dayRowHolder.cash.income)) 0.0 else dayRowHolder.cash.income.toDouble()
            sbIncome.append(Utility.formatCurrency(abs(incomeDouble)))
            summaryViewHolder.tvIncome.text = sbIncome.toString()

            /*TODO set expense value from expense property*/
            val buyingPrice = dayRowHolder.cash.buyingPrice
            val expenseDouble: Double = if (Utility.isBlank(buyingPrice)) 0.0 else buyingPrice.toDouble()
            if (expenseDouble > 0) {
                val strExpense = Utility.getCurrency() + Utility.formatCurrency(expenseDouble)
                summaryViewHolder.tvExpense.text = strExpense
            } else {
                summaryViewHolder.tvExpense.text = "-"
            }
        }
    }

    override fun submitList(pagedList: PagedList<DataHolder>?) {
        pageSize = pagedList!!.size
        mDiffer.submitList(pagedList)
    }

    fun setSize(count : Int) {
        total = count
    }


    override fun getItemViewType(i: Int): Int {
        try {
            return mDiffer.getItem(i)!!.tag
        } catch (e: ArrayIndexOutOfBoundsException) {
            return mDiffer.getItem(i - 1)!!.tag
        } catch (e: IndexOutOfBoundsException) {
            return mDiffer.getItem(i - 1)!!.tag
        }
    }

    override fun getItemId(i: Int): Long {
        val dataHolder = mDiffer.getItem(i)
        return if (dataHolder is CashDataHolder && dataHolder.cashCategoryEntity!= null) {
            (dataHolder.getTag().toString() + ":" + dataHolder.cashCategoryEntity.cashTransactionid).hashCode().toLong()
        } else {
            (dataHolder?.tag.toString() + ":" + i).hashCode().toLong()
        }
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, tag: Int): RecyclerView.ViewHolder {
        return when (tag) {
            Tag.CUSTOMER_TAB_CUSTOMER_VIEW -> {
                val cashView = LayoutInflater.from(viewGroup.context).inflate(R.layout.cash_view_item, viewGroup, false)
                CashViewHolder(cashView)
            }
            Tag.VIEW_DAY_SUMMARY -> {
                val daySummaryView = LayoutInflater.from(viewGroup.context).inflate(R.layout.cash_summary_item_bytime, viewGroup, false)
                SummaryViewHolder(daySummaryView)
            }
            Tag.VIEW_CAT_SUMMARY -> {
                val categoryItemView = LayoutInflater.from(viewGroup.context).inflate(R.layout.category_summary_item, viewGroup, false)
                CategorySummaryViewHolder(categoryItemView)
            }
            Tag.VIEW_NO_RESULT -> {
                val noresult = LayoutInflater.from(viewGroup.context).inflate(R.layout.no_search_result_view, viewGroup, false)
                NoResultViewHolder(noresult)
            }
            Tag.VIEW_NO_FILTER_RESULT -> {
                val noFilterResult = LayoutInflater.from(viewGroup.context).inflate(R.layout.cash_tab_no_filter_result_item, viewGroup, false)
                NoResultViewHolder(noFilterResult)
            }
            Tag.LAST_ROW -> {
                val inflate = LayoutInflater.from(viewGroup.context).inflate(R.layout.activity_main_view_customer_last, viewGroup, false)
                LastViewHolder(inflate)
            }
            else -> {
                val tutorialView = LayoutInflater.from(viewGroup.context).inflate(R.layout.cash_tab_tutorial_item, viewGroup, false)
                TutorialVideoViewHolder(tutorialView)
            }
        }
    }

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, i: Int) {
        if (mDiffer == null || mDiffer.itemCount == 0 || mDiffer.getItem(i) == null) return
        val dataHolder = mDiffer.getItem(i)
        val itemViewType = viewHolder.itemViewType
        if (itemViewType == Tag.CUSTOMER_TAB_CUSTOMER_VIEW) {
            val cashViewHolder = viewHolder as CashViewHolder
            if (dataHolder != null) {
                bindCashViewHolder(cashViewHolder, dataHolder as CashDataHolder,i)
            }
        } else if (itemViewType == Tag.VIEW_DAY_SUMMARY) {
            val summaryViewHolder = viewHolder as SummaryViewHolder
            if (dataHolder != null) {
                bindSummaryViewHolder(summaryViewHolder, dataHolder as DayDataHolder)
            }
        } else if (itemViewType == Tag.VIEW_CAT_SUMMARY) {
            val summaryViewHolder = viewHolder as CategorySummaryViewHolder
            if (dataHolder != null) {
                bindCatSummaryViewHolder(summaryViewHolder, dataHolder as CategoryRowHolder)
            }
        } else if (viewHolder is TutorialVideoViewHolder) {
            bindTutorialViewHolder(viewHolder)
        }
    }

    companion object {
        val DIFF_CALLBACK = object : DiffUtil.ItemCallback<DataHolder>() {
            override fun areItemsTheSame(oldItem: DataHolder, newItem: DataHolder): Boolean {
                return oldItem.tag == newItem.tag
            }

            override fun areContentsTheSame(oldItem: DataHolder, newItem: DataHolder): Boolean {
                return when {
                    (oldItem is CashDataHolder) && (newItem is CashDataHolder) ->
                        oldItem.cashCategoryEntity.equals(newItem.cashCategoryEntity)
                    else -> oldItem.equals(newItem)
                }
            }
        }
    }

    private fun showCoachmark() {
        listener?.seeOnboardingTutorial()
    }

    private fun showAutoRecordCoachmark(itemView: View, firstAutoRecordTxnIndex: Int) {
        listener?.seeAutoRecordOnboardingTutorial(itemView,firstAutoRecordTxnIndex)
    }


    private fun openVideoTutorial(viewHolder: RecyclerView.ViewHolder) {
        viewHolder.itemView.context.startActivity(
            WebviewActivity.createIntent(
                viewHolder.itemView.context,
                trxBlankScreenExperimentVideoURL,
                viewHolder.itemView.context.getString(R.string.learn_bukuwarung),
                true,
                AnalyticsConst.TRANSAKSI_TUTORIAL,
                AnalyticsConst.TRANSAKSI_SNACK_BAR
            )
        )
    }

    override fun getItemCount(): Int {
        if (total != 0) {
            return total
        }
        return super.getItemCount() + pageSize
    }

}
