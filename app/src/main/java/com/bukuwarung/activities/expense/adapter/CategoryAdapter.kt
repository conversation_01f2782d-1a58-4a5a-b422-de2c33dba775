package com.bukuwarung.activities.expense.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RadioButton
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.utils.RemoteConfigUtils
import com.bumptech.glide.Glide
import org.w3c.dom.Text

import kotlinx.android.synthetic.main.item_select_category_variant2.view.parent_container as parent2
import kotlinx.android.synthetic.main.item_select_category_variant1.view.parent_container as parent1

class CategoryAdapter(val categories: List<Category>, val category: String?, val getCategory: (Int) -> Unit): RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder>()  {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        return CategoryViewHolder(parent)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        holder.bind(categories[position], category)
    }

    override fun getItemCount(): Int {
        return categories.size
    }

    inner class CategoryViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {

        constructor(parent: ViewGroup) : this(
            if (RemoteConfigUtils.getCategoryUIVariant() == 1) {
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_select_category_variant1,
                    parent, false
                )
            } else if (RemoteConfigUtils.getCategoryUIVariant() == 2) {
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_select_category_variant2,
                    parent, false
                )
            } else {
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_select_category_variant1,
                    parent, false
                )
            }

        )

        fun bind(category: Category, trxCategory: String?) {
            with(itemView) {
                if(RemoteConfigUtils.getCategoryUIVariant()==1){
                    Glide.with(context.applicationContext).load(category.categoryImage).centerCrop().into(parent1.findViewById(R.id.iv_category))
                    parent1.findViewById<TextView>(R.id.tv_category).text = category.categoryName

                    if (category.categoryName.equals(trxCategory, true)) {
                        parent1.findViewById<RelativeLayout>(R.id.rl_category).setBackgroundResource(R.drawable.circle_selected_category)
                        parent1.findViewById<ImageView>(R.id.iv_category_selected).visibility = View.VISIBLE
                    } else {
                        parent1.findViewById<RelativeLayout>(R.id.rl_category).setBackgroundResource(R.drawable.circle_gray_category)
                        parent1.findViewById<ImageView>(R.id.iv_category_selected).visibility = View.GONE
                    }
                    parent1.findViewById<RelativeLayout>(R.id.rl_category).setOnClickListener {
                        getCategory(categories.indexOf(category))
                    }

                }
                else{
                    Glide.with(context.applicationContext).load(category.categoryImage).into(parent2.findViewById(R.id.iv_category))
                    parent2.findViewById<TextView>(R.id.tv_category).text = category.categoryName

                    parent2.findViewById<ConstraintLayout>(R.id.parent_container).setOnClickListener {
                        getCategory(categories.indexOf(category))
                    }
                    parent2.findViewById<RadioButton>(R.id.selectCategoryRadioButton).setOnClickListener {
                        getCategory(categories.indexOf(category))
                    }

                    if (category.categoryName.equals(trxCategory, true)) {
                        parent2.findViewById<RadioButton>(R.id.selectCategoryRadioButton).isChecked = true
                    } else {
                        parent2.findViewById<ImageView>(R.id.iv_category_selected).visibility = View.GONE
                        parent2.findViewById<RadioButton>(R.id.selectCategoryRadioButton).isChecked = false
                    }
                }

            }
        }
    }
}