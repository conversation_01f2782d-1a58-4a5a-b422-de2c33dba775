package com.bukuwarung.activities.expense.category

import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.CategoryDetailAdapter
import com.bukuwarung.activities.expense.data.CategoryInfo
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.ActivityCategoryDescriptionBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken

class CategoryDescriptionActivity : BaseActivity() {

    private lateinit var binding: ActivityCategoryDescriptionBinding
    var transactionType = -1

    override fun setViewBinding() {
        binding = ActivityCategoryDescriptionBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)

        val toolbar = binding.toolbarMain

        setUpToolbarWithHomeUp(toolbar)

        val gson : Gson = GsonBuilder().create()

        if (intent.hasExtra(SelectCategory.TRANSACTION_TYPE)) {
            transactionType = intent.getIntExtra(SelectCategory.TRANSACTION_TYPE, -1)
        }

        if(transactionType == -1){
            binding.tvToolbarTitle.text = getString(R.string.debit_category_description)
        }
        else{
            binding.tvToolbarTitle.text = getString(R.string.credit_category_description)
        }

        val categorieDetails: String?

        if (transactionType != -1) {
            if(RemoteConfigUtils.getCategoryUIVariant()==1){
                categorieDetails = RemoteConfigUtils.SelectCategory.getCreditCategoryDetails()
            }
            else{
                categorieDetails = RemoteConfigUtils.SelectCategory.getCreditCategoryDetailsNew()
            }
        } else {
            if(RemoteConfigUtils.getCategoryUIVariant()==1){
                categorieDetails = RemoteConfigUtils.SelectCategory.getDebitCategoryDetails()
            }
            else{
                categorieDetails = RemoteConfigUtils.SelectCategory.getDebitCategoryDetailsNew()
            }
        }

        val jsonType = object : TypeToken<List<CategoryInfo?>?>() {}.type
        val categoryInfoToDisplay: List<CategoryInfo> = gson.fromJson(categorieDetails, jsonType)

        val categoryDetailsAdapter = CategoryDetailAdapter(categoryInfoToDisplay)

        binding.rvCategoryDetails.layoutManager = LinearLayoutManager(this)

        binding.rvCategoryDetails.adapter = categoryDetailsAdapter


    }

    override fun subscribeState() {
    }

}