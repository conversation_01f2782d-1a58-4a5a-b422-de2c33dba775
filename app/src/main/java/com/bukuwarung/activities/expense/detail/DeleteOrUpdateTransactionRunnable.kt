package com.bukuwarung.activities.expense.detail

import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.TransactionEntity
import com.bukuwarung.database.entity.TransactionEntityType
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.session.User
import com.bukuwarung.utils.Utility

class DeleteOrUpdateTransactionRunnable(
        val activity: CashTransactionDetailActivity,
        val transactionId: String
): Runnable {

    override fun run() {
        val cashRepository = CashRepository.getInstance(activity)
        val userId = User.getUserId()
        val deviceId = User.getDeviceId()

        val cashTransaction = cashRepository.getSingleRawCashTransaction(transactionId)

        // delete trx
        cashRepository.updateExistingCashTransaction(userId, deviceId, cashTransaction.cashTransactionId,
                cashTransaction.cashCategoryId, cashTransaction.amount, cashTransaction.date,
                cashTransaction.description, 1)

        try {
            if(!Utility.isBlank(cashTransaction.customerTransactionId)){
                val transaction: TransactionEntity = TransactionRepository.getInstance(activity).getTransactionById(cashTransaction.customerTransactionId)
                val userId2 = User.getUserId()
                val deviceId2 = User.getDeviceId()
                AppAnalytics.trackEvent("customer_transaction_delete_from_linked_transaksi")
                TransactionRepository.getInstance(activity).updateExistingTransaction(userId2, deviceId2,
                        transaction.transactionId, transaction.customerId, transaction.amount, transaction.date,
                        transaction.description, 1)
            }
            TransactionRepository.getInstance(activity).deleteAllHistoryItemsForTransaction(transactionId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}