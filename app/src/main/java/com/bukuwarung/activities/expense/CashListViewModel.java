package com.bukuwarung.activities.expense;

import android.app.Application;
import android.util.Pair;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.paging.LivePagedListBuilder;
import androidx.paging.PagedList;

import com.bukuwarung.activities.expense.adapter.dataholder.CashDataHolder;
import com.bukuwarung.activities.expense.adapter.dataholder.TutorialVideoDataHolder;
import com.bukuwarung.activities.expense.adapter.model.DailySummary;
import com.bukuwarung.activities.expense.filter.CashTabFilter;
import com.bukuwarung.activities.expense.sort.SortOrder;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transactionreport.DateRange;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.dto.BookSummaryModel;
import com.bukuwarung.database.dto.CashTransactionDto;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.inventory.usecases.ProductInventory;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.Utility;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

public final class CashListViewModel extends AndroidViewModel {

    public static final String TAG = "CashListViewModel";
    private final LiveData<Integer> trxCountLiveData;
    private final CustomerRepository customerRepository;
    private int trxCount,allIncomeTrxCount,allExpenseTrxCount;
    private final CashRepository cashRepository;
    private final LiveData<BookEntity> businessLiveData;
    private final MutableLiveData<BookSummaryModel> bookSummaryModel = new MutableLiveData<>();
    public List<CashTransactionDto> cashList = new ArrayList<>();
    private final MediatorLiveData<List<DataHolder>> liveDataMerger = new MediatorLiveData<>();
    public ProductInventory productInventory;

    public LiveData<PagedList<CashTransactionDto>> rangedCashList;
    private MediatorLiveData<List<CashTransactionDto>> rangedCash = new MediatorLiveData<>();
    public MediatorLiveData<PagedList<CashTransactionDto>> cashVal = new MediatorLiveData<>();
    private String searchQuery;
    private int sortOrder;
    private int transactionFilter;
    private int typeFilter = CashTabFilter.BY_DATE;
    public boolean dataloaded;


    public int currentMonth;
    public int currentYear;

    public String startDate = "";
    public String endDate = "";

    public CashListViewModel(Application application, ProductInventory productInventory) {
        super(application);
        this.cashRepository = CashRepository.getInstance(application);
        this.sortOrder = FeaturePrefManager.getInstance().getCashListSortOrder();
        this.transactionFilter = FeaturePrefManager.getInstance().getSelectedCashFilter();
        trxCountLiveData = TransactionRepository.getInstance(application).getTransactionCountLiveData(User.getUserId());
        trxCount = TransactionRepository.getInstance(application).countAllCashTrans(User.getBusinessId());
        allIncomeTrxCount = TransactionRepository.getInstance(application).countAllIncomeTrans(User.getBusinessId());
        allExpenseTrxCount = TransactionRepository.getInstance(application).countAllExpenseTrans(User.getBusinessId());
        this.currentMonth = Utility.getCurrentMonthInt();
        this.currentYear = Utility.getCurrentYearInt();
        businessLiveData = BusinessRepository.getInstance(application).getBusinessById(User.getBusinessId());
        this.rangedCashList = new LivePagedListBuilder<>(this.cashRepository.getAllCashTransactionsWithDateRangePaging(User.getBusinessId(), startDate, endDate), 2000000).build();
        this.cashVal.addSource(rangedCashList, list -> {
            cashList = list;
            cashVal.setValue(list);
        });
        this.customerRepository = CustomerRepository.getInstance(application);
        this.productInventory = productInventory;
    }

    public final MutableLiveData<BookSummaryModel> getBookSummaryModel() {
        return this.bookSummaryModel;
    }

    public final LiveData<List<DataHolder>> getDataHolderList() {
        return this.liveDataMerger;
    }

    public final List<CashTransactionDto> getCashList() {
        return this.cashList;
    }

    public final LiveData<Integer> getTrxCountLiveData() {
        return this.trxCountLiveData;
    }

    public final List<DataHolder> setSearchQuery(String str) {
        this.searchQuery = str;
        return refresh();
    }

    public final List<DataHolder> setDateRange(DateRange range) {
        if (range == null || startDate.equals(range.getStartDate()) || endDate.equals(range.getEndDate()))
            return null;
        this.startDate = range.getStartDate();
        this.endDate = range.getEndDate();
        this.rangedCashList = new LivePagedListBuilder<>(this.cashRepository.getAllCashTransactionsWithDateRangePaging(User.getBusinessId(), startDate, endDate), 20000).build();

        this.cashVal.addSource(rangedCashList, list -> {
            cashList = list;
            cashVal.setValue(list);
        });
        this.trxCount = this.cashRepository.getCountCashTransaction(User.getBusinessId());

        return refresh();
    }

    public final List<DataHolder> setSortOrder(int i) {
        this.sortOrder = i;
        return refresh();
    }

    public final List<DataHolder> setTransactionFilter(int i) {
        this.transactionFilter = i;
        return refresh();
    }

    public final List<DataHolder> setTypeFilter(int i) {
        this.typeFilter = i;
        return refresh();
    }

    public final LiveData<BookEntity> getBusinessLiveData() {
        return this.businessLiveData;
    }


    public final List<DataHolder> refreshDateFilter(List<CashTransactionDto> cashList) {
        ArrayList<CashTransactionDto> arrayList = new ArrayList<>();
        if (cashList != null) {
            arrayList.addAll(cashList);
        }
        if (Utility.isBlank(searchQuery)) {
            arrayList = getFilteredList(arrayList);
        }

        LinkedHashMap<String, List<CashTransactionDto>> dataMap = new LinkedHashMap<>();

        sortCashList(arrayList, SortOrder.MOST_RECENT);
        ArrayList<CashTransactionDto> searchInCashs = searchInCashs(arrayList, searchQuery);
        ArrayList<DataHolder> resultList = new ArrayList<>();

        BookSummaryModel summaryModel = new BookSummaryModel(0.0, 0.0);
        if (searchInCashs != null && !searchInCashs.isEmpty()) {
            Map<String, Double> summaryExpenseMap = new HashMap<>();
            Map<String, Double> summaryIncomeMap = new HashMap<>();
            Map<String, Boolean> summaryExistMap = new HashMap<>();
            for (CashTransactionDto cashRowHolder : searchInCashs) {
                if (cashRowHolder == null)
                    continue;
                if (DateTimeUtils.isSameMonth(cashRowHolder.transactionDate, this.currentMonth, this.currentYear)) {
                    if (cashRowHolder.transactionAmount > 0) {
                        summaryModel.amountIn += cashRowHolder.transactionAmount;
                        summaryModel.amountOut += -1 * cashRowHolder.buyingPrice;
                    } else {
                        summaryModel.amountOut += cashRowHolder.transactionAmount;
                    }
                }
                String id = getGroupId(cashRowHolder);
                if (!summaryExpenseMap.containsKey(id)) {
                    summaryExpenseMap.put(id, 0d);
                    summaryExistMap.put(id, true);
                }
                if (!summaryIncomeMap.containsKey(id)) {
                    summaryIncomeMap.put(id, 0d);
                    summaryExistMap.put(id, true);
                }
                if (cashRowHolder.transactionAmount <= 0) {
                    if (summaryExpenseMap.containsKey(id)) {
                        Double total = cashRowHolder.transactionAmount + summaryExpenseMap.get(id);
                        summaryExpenseMap.put(id, total);
                    } else {
                        summaryExpenseMap.put(id, cashRowHolder.transactionAmount);
                        summaryExistMap.put(id, true);
                    }
                } else {
                    if (summaryIncomeMap.containsKey(id)) {
                        Double total = cashRowHolder.transactionAmount + summaryIncomeMap.get(id);
                        summaryIncomeMap.put(id, total);
                    } else {
                        summaryIncomeMap.put(id, cashRowHolder.transactionAmount);
                        summaryExistMap.put(id, true);
                    }

                    // add buying price if any
                    if (cashRowHolder.buyingPrice > 0) {
                        if (summaryExpenseMap.containsKey(id)) {
                            Double total = summaryExpenseMap.get(id) - cashRowHolder.buyingPrice;
                            summaryExpenseMap.put(id, total);
                        } else {
                            summaryExpenseMap.put(id, cashRowHolder.buyingPrice);
                            summaryExistMap.put(id, true);
                        }
                    }
                }
                if (dataMap.get(id) == null) {
                    List<CashTransactionDto> dataList = new ArrayList<>();
                    dataMap.put(id, dataList);
                }
                dataMap.get(id).add(cashRowHolder);
            }

            if (this.sortOrder == SortOrder.MOST_OUT || this.sortOrder == SortOrder.LEAST_OUT) {
                dataMap = sortedMap(sortByComparator(summaryExpenseMap, this.sortOrder), dataMap);
            } else if (this.sortOrder == SortOrder.MOST_IN || this.sortOrder == SortOrder.LEAST_IN) {
                dataMap = sortedMap(sortByComparator(summaryIncomeMap, this.sortOrder), dataMap);
            }
            for (Map.Entry<String, List<CashTransactionDto>> entry : dataMap.entrySet()) {
                if (this.sortOrder == SortOrder.MOST_RECENT) {
                    sortCashListByDate(entry.getValue(), this.sortOrder);
                } else {
                    sortCashList(entry.getValue(), this.sortOrder);
                }
                Iterable<CashTransactionDto> iterableData = entry.getValue();
                for (CashTransactionDto cashRowHolder : iterableData) {
                    String id = getGroupId(cashRowHolder);
                    if (summaryExistMap.get(id)) {
                        String expenseVal = summaryExpenseMap.get(id) == null ? "0" : String.valueOf(summaryExpenseMap.get(id));
                        String incomeVal = summaryIncomeMap.get(id) == null ? "0" : String.valueOf(summaryIncomeMap.get(id));
                        DailySummary dailySummary = new DailySummary(id, expenseVal, incomeVal, cashRowHolder.cashCategoryId, cashRowHolder.type);
                        DataHolder.DayDataHolder dayRowHolder = new DataHolder.DayDataHolder(dailySummary);
                        resultList.add(dayRowHolder);
                        summaryExistMap.put(id, false);
                    }
                    resultList.add(new CashDataHolder(cashRowHolder));
                }
            }
            resultList.add(new DataHolder.LastRowHolder());
        } else {
            if (Utility.isBlank(searchQuery) && trxCount == 0) {
                resultList.add(new TutorialVideoDataHolder());
            } else if ((!Utility.isBlank(startDate) || !Utility.isBlank(endDate)) && Utility.isBlank(searchQuery)) {
                resultList.add(new DataHolder.NoFilterResultRowHolder());
            } else {
                resultList.add(new DataHolder.NoResultRowHolder());
            }
        }

        this.bookSummaryModel.setValue(new BookSummaryModel(cashList));
        this.liveDataMerger.setValue(resultList);

        return resultList;
    }

    private LinkedHashMap<String, List<CashTransactionDto>> sortedMap(Map<String, Double> amountMap, Map<String, List<CashTransactionDto>> map) {
        LinkedHashMap<String, List<CashTransactionDto>> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<String, Double> entry : amountMap.entrySet()) {
            sortedMap.put(entry.getKey(), map.get(entry.getKey()));
        }
        return sortedMap;
    }

    private static Map<String, Double> sortByComparator(Map<String, Double> unsortMap, final int order) {

        List<Map.Entry<String, Double>> list = new LinkedList<>(unsortMap.entrySet());

        Collections.sort(list, (o1, o2) -> {
            if ((order == SortOrder.MOST_OUT || order == SortOrder.LEAST_IN)) {
                return o1.getValue().compareTo(o2.getValue());
            } else if (order == SortOrder.MOST_IN || order == SortOrder.LEAST_OUT) {
                return o2.getValue().compareTo(o1.getValue());
            } else {
                return o1.getValue().compareTo(o2.getValue());
            }
        });

        // Maintaining insertion order with the help of LinkedList
        Map<String, Double> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<String, Double> entry : list) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }

        return sortedMap;
    }


    public final List<DataHolder> refresh() {
        List<CashTransactionDto> allList = this.cashRepository.getAllCashTransactionsWithDateRange(User.getBusinessId(), startDate, endDate);
        rangedCash.setValue(allList);
        trxCount = this.cashRepository.getCountCashTransaction(User.getBusinessId());
        if (this.typeFilter == CashTabFilter.BY_DATE) {
            return refreshDateFilter(allList);
        }

        ArrayList<CashTransactionDto> arrayList = new ArrayList<>();
        if (this.cashList != null) {
            arrayList.addAll(this.cashList);
        }
        if (Utility.isBlank(searchQuery)) {
            arrayList = getFilteredList(arrayList);
        }
        sortCashList(arrayList, sortOrder);

        ArrayList<CashTransactionDto> searchInCashs = searchInCashs(arrayList, searchQuery);

        ArrayList<DataHolder> resultList = new ArrayList<>();
        BookSummaryModel summaryModel = new BookSummaryModel(0.0, 0.0);
        if (searchInCashs != null && !searchInCashs.isEmpty()) {
            Map<String, Double> summaryExpenseMap = new HashMap<>();
            Map<String, Double> summaryIncomeMap = new HashMap<>();
            Map<String, Boolean> summaryExistMap = new HashMap<>();
            Map<String, Integer> summaryCountMap = new HashMap<>();
            Map<String, Double> summaryBuyingPrice = new HashMap<>();
            for (CashTransactionDto cashRowHolder : searchInCashs) {
                if (this.typeFilter == CashTabFilter.BY_CATEGORY) {
                    if (cashRowHolder.transactionAmount <= 0) {
                        if (summaryExpenseMap.containsKey(cashRowHolder.cashCategoryId)) {
                            Double total = cashRowHolder.transactionAmount + summaryExpenseMap.get(cashRowHolder.cashCategoryId);
                            summaryExpenseMap.put(cashRowHolder.cashCategoryId, total);

                            int old = summaryCountMap.get(cashRowHolder.cashCategoryId);
                            summaryCountMap.put(
                                    cashRowHolder.cashCategoryId,
                                    old + 1
                            );
                        } else {
                            summaryExpenseMap.put(cashRowHolder.cashCategoryId, cashRowHolder.transactionAmount);
                            summaryExistMap.put(cashRowHolder.cashCategoryId, true);
                            summaryCountMap.put(cashRowHolder.cashCategoryId, 1);
                        }
                    } else {
                        if (summaryIncomeMap.containsKey(cashRowHolder.cashCategoryId)) {
                            Double total = cashRowHolder.transactionAmount + summaryIncomeMap.get(cashRowHolder.cashCategoryId);
                            summaryIncomeMap.put(cashRowHolder.cashCategoryId, total);

                            int old = summaryCountMap.get(cashRowHolder.cashCategoryId);
                            summaryCountMap.put(
                                    cashRowHolder.cashCategoryId,
                                    old + 1
                            );
                        } else {
                            summaryIncomeMap.put(cashRowHolder.cashCategoryId, cashRowHolder.transactionAmount);
                            summaryExistMap.put(cashRowHolder.cashCategoryId, true);
                            summaryCountMap.put(cashRowHolder.cashCategoryId, 1);
                        }

                        if (summaryBuyingPrice.containsKey(cashRowHolder.cashCategoryId)) {
                            double totalBuyingPrice = cashRowHolder.buyingPrice + summaryBuyingPrice.get(cashRowHolder.cashCategoryId);
                            summaryBuyingPrice.put(cashRowHolder.cashCategoryId, totalBuyingPrice);
                        } else {
                            summaryBuyingPrice.put(cashRowHolder.cashCategoryId, cashRowHolder.buyingPrice);
                        }
                    }
                    if (DateTimeUtils.isSameMonth(cashRowHolder.transactionDate, this.currentMonth, this.currentYear)) {
                        if (cashRowHolder.transactionAmount > 0 && CashTabFilter.IN == transactionFilter) {
                            summaryModel.amountIn += cashRowHolder.transactionAmount;
                            summaryModel.amountOut += -1 * cashRowHolder.buyingPrice;
                        } else if (cashRowHolder.transactionAmount < 0 && CashTabFilter.OUT == transactionFilter) {
                            summaryModel.amountOut += cashRowHolder.transactionAmount;
                        } else {
                            if (cashRowHolder.transactionAmount > 0) {
                                summaryModel.amountIn += cashRowHolder.transactionAmount;
                                summaryModel.amountOut += -1 * cashRowHolder.buyingPrice;
                            } else {
                                summaryModel.amountOut += cashRowHolder.transactionAmount;
                            }
                        }
                    }
                } else {
                    String id = getGroupId(cashRowHolder);
                    if (cashRowHolder.transactionAmount <= 0) {
                        if (summaryExpenseMap.containsKey(id)) {
                            Double total = cashRowHolder.transactionAmount + summaryExpenseMap.get(id);
                            summaryExpenseMap.put(id, total);
                        } else {
                            summaryExpenseMap.put(id, cashRowHolder.transactionAmount);
                            summaryExistMap.put(id, true);
                        }
                    } else {
                        if (summaryIncomeMap.containsKey(id)) {
                            Double total = cashRowHolder.transactionAmount + summaryIncomeMap.get(id);
                            summaryIncomeMap.put(id, total);
                        } else {
                            summaryIncomeMap.put(id, cashRowHolder.transactionAmount);
                            summaryExistMap.put(id, true);
                        }
                    }
                }
            }
            for (CashTransactionDto cashRowHolder : searchInCashs) {
                if (this.typeFilter == CashTabFilter.BY_DATE) {
                    String id = getGroupId(cashRowHolder);
                    if (summaryExistMap.get(id)) {
                        String expenseVal = summaryExpenseMap.get(id) == null ? "0" : String.valueOf(summaryExpenseMap.get(id));
                        String incomeVal = summaryIncomeMap.get(id) == null ? "0" : String.valueOf(summaryIncomeMap.get(id));
                        DailySummary dailySummary = new DailySummary(id, expenseVal, incomeVal, cashRowHolder.cashCategoryId, cashRowHolder.type);
                        DataHolder.DayDataHolder dayRowHolder = new DataHolder.DayDataHolder(dailySummary);
                        resultList.add(dayRowHolder);
                        summaryExistMap.put(id, false);
                    }
                    resultList.add(new CashDataHolder(cashRowHolder));
                } else {
                    if (summaryExistMap.get(cashRowHolder.cashCategoryId)) {
                        String expenseVal = summaryExpenseMap.get(cashRowHolder.cashCategoryId) == null ? "0" : String.valueOf(summaryExpenseMap.get(cashRowHolder.cashCategoryId));
                        String incomeVal = summaryIncomeMap.get(cashRowHolder.cashCategoryId) == null ? "0" : String.valueOf(summaryIncomeMap.get(cashRowHolder.cashCategoryId));
                        String buyingPrice = summaryBuyingPrice.get(cashRowHolder.cashCategoryId) == null ? "0" : String.valueOf(summaryBuyingPrice.get(cashRowHolder.cashCategoryId));

                        DailySummary dailySummary = new DailySummary(cashRowHolder.categoryName, expenseVal, incomeVal, buyingPrice, cashRowHolder.cashCategoryId, cashRowHolder.type);

                        if (summaryCountMap.containsKey(cashRowHolder.cashCategoryId))
                            dailySummary.trxCount = summaryCountMap.get(cashRowHolder.cashCategoryId);

                        DataHolder.CategoryRowHolder categoryRowHolder = new DataHolder.CategoryRowHolder(dailySummary);
                        resultList.add(categoryRowHolder);
                        summaryExistMap.put(cashRowHolder.cashCategoryId, false);
                    }
                }

            }
            sortCashListForCat(resultList, this.sortOrder);
            resultList.add(new DataHolder.LastRowHolder());
        } else {
            if (Utility.isBlank(searchQuery) && trxCount == 0) {
                resultList.add(new TutorialVideoDataHolder());
            } else if ((!Utility.isBlank(startDate) || !Utility.isBlank(endDate)) && Utility.isBlank(searchQuery)) {
                resultList.add(new DataHolder.NoFilterResultRowHolder());
            } else {
                resultList.add(new DataHolder.NoResultRowHolder());
            }
        }
//        this.bookSummaryModel.setValue(summaryModel);
        this.bookSummaryModel.setValue(new BookSummaryModel(cashList));
        this.liveDataMerger.setValue(resultList);
        return resultList;
//        this.cashVal.setValue((PagedList<CashTransactionDto>) cashList);;
    }

    public final Pair<List<DataHolder>, Integer> dataForBusinessDashBoard(DateRange range) {
        List<CashTransactionDto> allList = this.cashRepository.getAllCashTransactionsWithDateRange(User.getBusinessId(), range.getStartDate(), range.getEndDate());
        rangedCash.setValue(allList);
        trxCount = allList.size();

        ArrayList<CashTransactionDto> searchInCashs = searchInCashs((ArrayList<CashTransactionDto>) allList, searchQuery);

        ArrayList<DataHolder> resultList = new ArrayList<>();
        BookSummaryModel summaryModel = new BookSummaryModel(0.0, 0.0);
        int countOfPOSandRecordTrans = 0;
        if (searchInCashs != null && !searchInCashs.isEmpty()) {
            Map<String, Double> summaryExpenseMap = new HashMap<>();
            Map<String, Double> summaryIncomeMap = new HashMap<>();
            Map<String, Boolean> summaryExistMap = new HashMap<>();
            Map<String, Integer> summaryCountMap = new HashMap<>();
            Map<String, Double> summaryBuyingPrice = new HashMap<>();
            for (CashTransactionDto cashRowHolder : searchInCashs) {
                if (cashRowHolder.transactionAmount <= 0) {
                    if (summaryExpenseMap.containsKey(cashRowHolder.cashCategoryId)) {
                        Double total = cashRowHolder.transactionAmount + summaryExpenseMap.get(cashRowHolder.cashCategoryId);
                        summaryExpenseMap.put(cashRowHolder.cashCategoryId, total);

                        int old = summaryCountMap.get(cashRowHolder.cashCategoryId);
                        summaryCountMap.put(
                                cashRowHolder.cashCategoryId,
                                old + 1
                        );
                    } else {
                        summaryExpenseMap.put(cashRowHolder.cashCategoryId, cashRowHolder.transactionAmount);
                        summaryExistMap.put(cashRowHolder.cashCategoryId, true);
                        summaryCountMap.put(cashRowHolder.cashCategoryId, 1);
                    }
                } else {
                    if (summaryIncomeMap.containsKey(cashRowHolder.cashCategoryId)) {
                        Double total = cashRowHolder.transactionAmount + summaryIncomeMap.get(cashRowHolder.cashCategoryId);
                        summaryIncomeMap.put(cashRowHolder.cashCategoryId, total);

                        int old = summaryCountMap.get(cashRowHolder.cashCategoryId);
                        summaryCountMap.put(
                                cashRowHolder.cashCategoryId,
                                old + 1
                        );
                    } else {
                        summaryIncomeMap.put(cashRowHolder.cashCategoryId, cashRowHolder.transactionAmount);
                        summaryExistMap.put(cashRowHolder.cashCategoryId, true);
                        summaryCountMap.put(cashRowHolder.cashCategoryId, 1);
                    }

                    if (summaryBuyingPrice.containsKey(cashRowHolder.cashCategoryId)) {
                        double totalBuyingPrice = cashRowHolder.buyingPrice + summaryBuyingPrice.get(cashRowHolder.cashCategoryId);
                        summaryBuyingPrice.put(cashRowHolder.cashCategoryId, totalBuyingPrice);
                    } else {
                        summaryBuyingPrice.put(cashRowHolder.cashCategoryId, cashRowHolder.buyingPrice);
                    }
                }
                if (DateTimeUtils.isSameMonth(cashRowHolder.transactionDate, this.currentMonth, this.currentYear)) {
                    if (cashRowHolder.transactionAmount > 0 && CashTabFilter.IN == transactionFilter) {
                        summaryModel.amountIn += cashRowHolder.transactionAmount;
                        summaryModel.amountOut += -1 * cashRowHolder.buyingPrice;
                    } else if (cashRowHolder.transactionAmount < 0 && CashTabFilter.OUT == transactionFilter) {
                        summaryModel.amountOut += cashRowHolder.transactionAmount;
                    } else {
                        if (cashRowHolder.transactionAmount > 0) {
                            summaryModel.amountIn += cashRowHolder.transactionAmount;
                            summaryModel.amountOut += -1 * cashRowHolder.buyingPrice;
                        } else {
                            summaryModel.amountOut += cashRowHolder.transactionAmount;
                        }
                    }
                }
            }

            for (CashTransactionDto cashRowHolder : searchInCashs) {
                if (summaryExistMap.get(cashRowHolder.cashCategoryId)) {
                    String expenseVal = summaryExpenseMap.get(cashRowHolder.cashCategoryId) == null ? "0" : String.valueOf(summaryExpenseMap.get(cashRowHolder.cashCategoryId));
                    String incomeVal = summaryIncomeMap.get(cashRowHolder.cashCategoryId) == null ? "0" : String.valueOf(summaryIncomeMap.get(cashRowHolder.cashCategoryId));
                    String buyingPrice = summaryBuyingPrice.get(cashRowHolder.cashCategoryId) == null ? "0" : String.valueOf(summaryBuyingPrice.get(cashRowHolder.cashCategoryId));

                    DailySummary dailySummary = new DailySummary(cashRowHolder.categoryName, expenseVal, incomeVal, buyingPrice, cashRowHolder.cashCategoryId, cashRowHolder.type);

                    if (summaryCountMap.containsKey(cashRowHolder.cashCategoryId))
                        dailySummary.trxCount = summaryCountMap.get(cashRowHolder.cashCategoryId);

                    DataHolder.CategoryRowHolder categoryRowHolder = new DataHolder.CategoryRowHolder(dailySummary);
                    resultList.add(categoryRowHolder);
                    summaryExistMap.put(cashRowHolder.cashCategoryId, false);
                }
            }
            sortCashListForCat(resultList, this.sortOrder);
        }
        this.bookSummaryModel.setValue(new BookSummaryModel(cashList));
        for(CashTransactionDto item : allList) {
            if(item.transactionType.equals(TransactionEntityType.POS_TRANSACTION.toString()) || item.transactionType.equals(TransactionEntityType.DEFAULT.toString())) {
                countOfPOSandRecordTrans++;
            }
        }
        return new Pair(resultList, countOfPOSandRecordTrans);
    }


    public final Pair<List<DataHolder>, Integer> autoRecordDataForBusinessDashBoard(DateRange range) {
        List<CashTransactionDto> allList = this.cashRepository.getAllAutoRecordCashTransactionsWithDateRange(User.getBusinessId(), range.getStartDate(), range.getEndDate());
        rangedCash.setValue(allList);
        trxCount = this.cashRepository.getCountAutoRecordTransactions(User.getBusinessId());

        ArrayList<CashTransactionDto> searchInCashs = searchInCashs((ArrayList<CashTransactionDto>) allList, searchQuery);

        ArrayList<DataHolder> resultList = new ArrayList<>();
        int sizeOfList = allList.size();
        BookSummaryModel summaryModel = new BookSummaryModel(0.0, 0.0);
        if (searchInCashs != null && !searchInCashs.isEmpty()) {
            Map<String, Double> summaryExpenseMap = new HashMap<>();
            Map<String, Double> summaryIncomeMap = new HashMap<>();
            Map<String, Boolean> summaryExistMap = new HashMap<>();
            Map<String, Integer> summaryCountMap = new HashMap<>();
            Map<String, Double> summaryBuyingPrice = new HashMap<>();
            for (CashTransactionDto cashRowHolder : searchInCashs) {
                if (cashRowHolder.transactionAmount <= 0) {
                    if (summaryExpenseMap.containsKey(cashRowHolder.brickInstitutionId.toString())) {
                        Double total = cashRowHolder.transactionAmount + summaryExpenseMap.get(cashRowHolder.brickInstitutionId.toString());
                        summaryExpenseMap.put(cashRowHolder.brickInstitutionId.toString(), total);

                        int old = summaryCountMap.get(cashRowHolder.brickInstitutionId.toString());
                        summaryCountMap.put(
                                cashRowHolder.brickInstitutionId.toString(),
                                old + 1
                        );
                    } else {
                        summaryExpenseMap.put(cashRowHolder.brickInstitutionId.toString(), cashRowHolder.transactionAmount);
                        summaryExistMap.put(cashRowHolder.brickInstitutionId.toString(), true);
                        summaryCountMap.put(cashRowHolder.brickInstitutionId.toString(), 1);
                    }
                } else {
                    if (summaryIncomeMap.containsKey(cashRowHolder.brickInstitutionId.toString())) {
                        Double total = cashRowHolder.transactionAmount + summaryIncomeMap.get(cashRowHolder.brickInstitutionId.toString());
                        summaryIncomeMap.put(cashRowHolder.brickInstitutionId.toString(), total);

                        int old = summaryCountMap.get(cashRowHolder.brickInstitutionId.toString());
                        summaryCountMap.put(
                                cashRowHolder.brickInstitutionId.toString(),
                                old + 1
                        );
                    } else {
                        summaryIncomeMap.put(cashRowHolder.brickInstitutionId.toString(), cashRowHolder.transactionAmount);
                        summaryExistMap.put(cashRowHolder.brickInstitutionId.toString(), true);
                        summaryCountMap.put(cashRowHolder.brickInstitutionId.toString(), 1);
                    }

                    if (summaryBuyingPrice.containsKey(cashRowHolder.brickInstitutionId.toString())) {
                        double totalBuyingPrice = cashRowHolder.buyingPrice + summaryBuyingPrice.get(cashRowHolder.brickInstitutionId.toString());
                        summaryBuyingPrice.put(cashRowHolder.brickInstitutionId.toString(), totalBuyingPrice);
                    } else {
                        summaryBuyingPrice.put(cashRowHolder.brickInstitutionId.toString(), cashRowHolder.buyingPrice);
                    }
                }
                if (DateTimeUtils.isSameMonth(cashRowHolder.transactionDate, this.currentMonth, this.currentYear)) {
                    if (cashRowHolder.transactionAmount > 0 && CashTabFilter.IN == transactionFilter) {
                        summaryModel.amountIn += cashRowHolder.transactionAmount;
                        summaryModel.amountOut += -1 * cashRowHolder.buyingPrice;
                    } else if (cashRowHolder.transactionAmount < 0 && CashTabFilter.OUT == transactionFilter) {
                        summaryModel.amountOut += cashRowHolder.transactionAmount;
                    } else {
                        if (cashRowHolder.transactionAmount > 0) {
                            summaryModel.amountIn += cashRowHolder.transactionAmount;
                            summaryModel.amountOut += -1 * cashRowHolder.buyingPrice;
                        } else {
                            summaryModel.amountOut += cashRowHolder.transactionAmount;
                        }
                    }
                }
            }

            for (CashTransactionDto cashRowHolder : searchInCashs) {
                if (summaryExistMap.get(cashRowHolder.brickInstitutionId.toString())) {
                    String expenseVal = summaryExpenseMap.get(cashRowHolder.brickInstitutionId.toString()) == null ? "0" : String.valueOf(summaryExpenseMap.get(cashRowHolder.brickInstitutionId.toString()));
                    String incomeVal = summaryIncomeMap.get(cashRowHolder.brickInstitutionId.toString()) == null ? "0" : String.valueOf(summaryIncomeMap.get(cashRowHolder.brickInstitutionId.toString()));
                    String buyingPrice = summaryBuyingPrice.get(cashRowHolder.brickInstitutionId.toString()) == null ? "0" : String.valueOf(summaryBuyingPrice.get(cashRowHolder.brickInstitutionId.toString()));

//                        DailySummary dailySummary = new DailySummary(cashRowHolder.categoryName, expenseVal, incomeVal, buyingPrice, cashRowHolder.brickInstitutionId.toString(), cashRowHolder.type);
                    DailySummary dailySummary = new DailySummary("-", expenseVal, incomeVal, buyingPrice, cashRowHolder.brickInstitutionId.toString(), cashRowHolder.type);

                    if (summaryCountMap.containsKey(cashRowHolder.brickInstitutionId.toString()))
                        dailySummary.trxCount = summaryCountMap.get(cashRowHolder.brickInstitutionId.toString());

                    DataHolder.CategoryRowHolder categoryRowHolder = new DataHolder.CategoryRowHolder(dailySummary);
                    resultList.add(categoryRowHolder);
                    summaryExistMap.put(cashRowHolder.brickInstitutionId.toString(), false);
                }

            }
            sortCashListForCat(resultList, this.sortOrder);
//            resultList.add(new DataHolder.LastRowHolder());
        } else {
            if (Utility.isBlank(searchQuery) && trxCount == 0) {
                resultList.add(new TutorialVideoDataHolder());
            } else if ((!Utility.isBlank(startDate) || !Utility.isBlank(endDate)) && Utility.isBlank(searchQuery)) {
                resultList.add(new DataHolder.NoFilterResultRowHolder());
            }
//            else {
//                resultList.add(new DataHolder.NoResultRowHolder());
//            }
        }
        this.bookSummaryModel.setValue(new BookSummaryModel(cashList));
        return new Pair(resultList, sizeOfList);
    }

    private String getGroupId(CashTransactionDto info) {
        if (this.transactionFilter == CashTabFilter.DAILY) {
            try {
                    LocalDate date = LocalDate.parse(info.transactionDate);
                    return date.format(
                        DateTimeFormatter.ofPattern("dd MMM yyyy")
                            .withLocale(new Locale("ID", "id"))
                    );

            } catch (Exception e) {
                e.printStackTrace();
                return info.transactionDate;
            }
        } else if (this.transactionFilter == CashTabFilter.WEEKLY) {
            return DateTimeUtils.getWeekBoundStr(info.transactionDate);
        } else {
            try {
                    LocalDate date = LocalDate.parse(info.transactionDate);
                    return date.format(
                        DateTimeFormatter.ofPattern("MMMM yyyy")
                            .withLocale(new Locale("ID", "id"))
                    );
            } catch (Exception e) {
                e.printStackTrace();
                return info.transactionDate;
            }
        }
    }

    /**
     * sort list of CashTransactionDto by updatedAt date.
     * It can be used to sort list of transaction for a date group
     * Data Map : dataMap('2020-07-06') = [transaction 1, transaction2, transcation3]
     * return transaction list in sorted order
     *
     * @param list list of transaction for a date group
     * @param i    sorting order
     */
    private void sortCashListByDate(List<CashTransactionDto> list, int i) {
        if (!(list == null || list.isEmpty())) {
            Collections.sort(list, (Comparator<CashTransactionDto>) (cashEntity, cashEntity2) -> {
                Long modifiedDate2 = cashEntity2.updatedAt;
                Long modifiedDate1 = cashEntity.updatedAt;
                return (modifiedDate2.compareTo(modifiedDate1) > 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
            });
        }
    }

    private void sortCashList(List<CashTransactionDto> list, int i) {
        if (!(list == null || list.isEmpty())) {
            if (i == SortOrder.MOST_RECENT) {
                Collections.sort(list, (cashEntity, cashEntity2) -> {
                    try {
                        String modifiedDate2 = cashEntity2.transactionDate;
                        String modifiedDate1 = cashEntity.transactionDate;
                        return (modifiedDate2.compareTo(modifiedDate1) > 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
                    } catch (Exception ex) {
                        // error in parsing
                        return 0;
                    }
                });
            } else if (i == SortOrder.MOST_OUT || i == SortOrder.LEAST_IN) {
                Collections.sort(list, (cashEntity, cashEntity2) -> {
                    try {
                        Double modifiedDate2 = cashEntity2.transactionAmount;
                        Double modifiedDate1 = cashEntity.transactionAmount;
                        return (modifiedDate2.compareTo(modifiedDate1) < 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
                    } catch (Exception ex) {
                        // error in parsing
                        return 0;
                    }
                });
            } else if (i == SortOrder.LEAST_OUT || i == SortOrder.MOST_IN) {
                Collections.sort(list, (cashEntity, cashEntity2) -> {
                    try {
                        Double modifiedDate2 = cashEntity2.transactionAmount;
                        Double modifiedDate1 = cashEntity.transactionAmount;
                        return (modifiedDate2.compareTo(modifiedDate1) > 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
                    } catch (Exception ex) {
                        // error in parsing
                        return 0;
                    }
                });
            } else if (i == SortOrder.NAME_ASC) {
                Collections.sort(list, (cashEntity, cashEntity2) -> {
                    try {
                        String name2 = cashEntity2.categoryName;
                        String name1 = cashEntity.categoryName;
                        if (name2 != null && name1 != null) {
                            return name1.toLowerCase().compareTo(name2.toLowerCase());
                        }
                        return -1;
                    } catch (Exception ex) {
                        // error in parsing
                        return 0;
                    }
                });
            }
        }
    }

    private void sortCashListForCat(ArrayList list, int i) {
        if (!(list == null || list.isEmpty())) {
            if (i == SortOrder.MOST_OUT) {
                Collections.sort(list, (Comparator<DataHolder.CategoryRowHolder>) (cashEntity, cashEntity2) -> {
                    Double modifiedDate2 = Math.abs(Double.parseDouble(cashEntity2.getExpense())) - Math.abs(Double.parseDouble(cashEntity2.getIncome()));
                    Double modifiedDate1 = Math.abs(Double.parseDouble(cashEntity.getExpense())) - Math.abs(Double.parseDouble(cashEntity.getIncome()));
                    return (modifiedDate2.compareTo(modifiedDate1) > 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
                });
            } else if (i == SortOrder.LEAST_OUT) {
                Collections.sort(list, (Comparator<DataHolder.CategoryRowHolder>) (cashEntity, cashEntity2) -> {
                    Double modifiedDate2 = Math.abs(Double.parseDouble(cashEntity2.getExpense())) - Math.abs(Double.parseDouble(cashEntity2.getIncome()));
                    Double modifiedDate1 = Math.abs(Double.parseDouble(cashEntity.getExpense())) - Math.abs(Double.parseDouble(cashEntity.getIncome()));
                    return (modifiedDate2.compareTo(modifiedDate1) < 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
                });
            } else if (i == SortOrder.LEAST_IN) {
                Collections.sort(list, (Comparator<DataHolder.CategoryRowHolder>) (cashEntity, cashEntity2) -> {
                    Double modifiedDate2 = Math.abs(Double.parseDouble(cashEntity2.getIncome())) - Math.abs(Double.parseDouble(cashEntity2.getExpense()));
                    Double modifiedDate1 = Math.abs(Double.parseDouble(cashEntity.getIncome())) - Math.abs(Double.parseDouble(cashEntity.getExpense()));
                    return (modifiedDate2.compareTo(modifiedDate1) < 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
                });
            } else if (i == SortOrder.MOST_IN) {
                Collections.sort(list, (Comparator<DataHolder.CategoryRowHolder>) (cashEntity, cashEntity2) -> {
                    Double modifiedDate2 = Math.abs(Double.parseDouble(cashEntity2.getIncome())) - Math.abs(Double.parseDouble(cashEntity2.getExpense()));
                    Double modifiedDate1 = Math.abs(Double.parseDouble(cashEntity.getIncome())) - Math.abs(Double.parseDouble(cashEntity.getExpense()));
                    return (modifiedDate2.compareTo(modifiedDate1) > 0 ? 1 : (Objects.equals(modifiedDate2, modifiedDate1) ? 0 : -1));
                });
            } else if (i == SortOrder.NAME_ASC) {
                Collections.sort(list, (Comparator<DataHolder.CategoryRowHolder>) (cashEntity, cashEntity2) -> {
                    String name2 = cashEntity2.getName();
                    String name1 = cashEntity.getName();
                    if (name2 != null && name1 != null) {
                        return name1.toLowerCase().compareTo(name2.toLowerCase());
                    }
                    return -1;
                });
            }
        }
    }

    private ArrayList<CashTransactionDto> searchInCashs(ArrayList<CashTransactionDto> cashList, String searchStr) {
        BookSummaryModel summaryModel = new BookSummaryModel();
        if (cashList == null || cashList.isEmpty() || Utility.isBlank(searchStr)) {
            this.bookSummaryModel.setValue(new BookSummaryModel(0.0, 0.0));
            if (!Utility.isBlank(FeaturePrefManager.getInstance().getTransaksiSearchKeyword())) {
                AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
                prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.TRANSAKSI_HOME_PAGE);
                prop.put(AnalyticsConst.SEARCH_KEYWORD, FeaturePrefManager.getInstance().getTransaksiSearchKeyword());
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SEARCH_PERFORMED, prop);
                FeaturePrefManager.getInstance().setTransaksiSearchKeyword("");
            }
            return cashList;
        }
        FeaturePrefManager.getInstance().setTransaksiSearchKeyword(searchStr);
        ArrayList<CashTransactionDto> matchingCashs = new ArrayList<>();
        for (CashTransactionDto cashEntity : cashList) {
            if (cashEntity.getCustomerName() != null && isMatchingSearchStr(cashEntity.getCustomerName(), searchStr)) {
                matchingCashs.add(cashEntity);
            }

            if (isMatchingSearchStr(cashEntity.categoryName, searchStr) ||
                    isMatchingSearchStr(cashEntity.transactionDescription, searchStr) ||
                    isMatchingSearchStr(String.valueOf(cashEntity.transactionAmount), searchStr)) {
                matchingCashs.add(cashEntity);
            }

            if (DateTimeUtils.isSameMonth(cashEntity.transactionDate, this.currentMonth, this.currentYear)) {
                if (cashEntity.transactionAmount > 0) {
                    summaryModel.amountIn += cashEntity.transactionAmount;
                } else {
                    summaryModel.amountOut += cashEntity.transactionAmount;
                }
            }
        }
        this.bookSummaryModel.setValue(summaryModel);
        return matchingCashs;
    }

    private boolean isMatchingSearchStr(String targetField, String searchStr) {
        if (!Utility.isBlank(targetField)) {
            return targetField.toLowerCase().contains(searchStr.toLowerCase());
        }
        return false;
    }

    private ArrayList<CashTransactionDto> getFilteredList(ArrayList<CashTransactionDto> arrayList) {
        if (arrayList == null) {
            return new ArrayList<>();
        }
        ArrayList<CashTransactionDto> filteredResults = new ArrayList<>();
        if (transactionFilter == CashTabFilter.OUT) {
            for (CashTransactionDto customerEntity : arrayList) {
                if (Double.compare(customerEntity.transactionAmount, (double) 0) < 0) {
                    filteredResults.add(customerEntity);
                }
            }
            return filteredResults;
        } else if (transactionFilter == CashTabFilter.IN) {
            for (CashTransactionDto customerEntity : arrayList) {
                if (Double.compare(customerEntity.transactionAmount, (double) 0) > 0) {
                    filteredResults.add(customerEntity);
                }
            }
            return filteredResults;
        }
        return arrayList;
    }

    public int getAllIncomeTrxCount() {
        return allIncomeTrxCount;
    }

    public int getAllIncomeTrxCountWithProductByDate(String sdate,String edate) {
        return TransactionRepository.getInstance(getApplication()).getAllIncomeTrxCountWithProductByDate(User.getBusinessId(),sdate,edate);
    }

    public int getAllExpenseTrxCountWithProductByDate(String sdate,String edate) {
        return TransactionRepository.getInstance(getApplication()).getAllExpenseTrxCountWithProductByDate(User.getBusinessId(),sdate,edate);
    }

    public ArrayList<CashCategoryEntity> getAllIncomeCategories(String sdate, String edate){
       return TransactionRepository.getInstance(getApplication()).getAllIncomeCategories(User.getBusinessId(),sdate,edate);
    }

    public ArrayList<CashCategoryEntity> getAllExpenseCategories(String sdate, String edate){
       return TransactionRepository.getInstance(getApplication()).getAllExpenseCategories(User.getBusinessId(),sdate,edate);
    }

    public int getAllExpenseTrxCount() {
        return allExpenseTrxCount;
    }

    public int getIncomeTrxCountWithIdByDate(String catId, String sdate, String edate) {
        return TransactionRepository.getInstance(getApplication()).countIncomeTransactionWithIdByDate(User.getBusinessId(),catId,sdate,edate);
    }

    public int getCategoryAmountByDate(String catId, String sdate, String edate) {
        return TransactionRepository.getInstance(getApplication()).getCategoryAmountByDate(User.getBusinessId(),catId,sdate,edate);
    }
    public int getExpenseTrxCountWithIdByDate(String catId, String sdate, String edate) {
        return TransactionRepository.getInstance(getApplication()).countExpenseTransactionWithIdByDate(User.getBusinessId(),catId,sdate,edate);
    }
}
