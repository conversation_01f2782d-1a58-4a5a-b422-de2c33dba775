package com.bukuwarung.activities.expense

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.database.dao.ProductDao
import com.bukuwarung.domain.cash.CashUseCase
import com.bukuwarung.domain.product.ProductUseCase
import com.bukuwarung.inventory.usecases.ProductInventory
import javax.inject.Inject

// TODO use repo / usecase instead of DAO
class ProductListViewModelFactory @Inject constructor(private val productUseCase: ProductUseCase, private val productDao: ProductDao) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return ProductListViewModel(productUseCase, productDao) as T
    }
}