package com.bukuwarung.activities.expense;

import android.text.Editable;
import android.text.TextWatcher;

public class SearchBoxTextWatcher implements TextWatcher {
    IncomeExpenseTab incomeExpenseTab;
    public SearchBoxTextWatcher(IncomeExpenseTab tab){
        incomeExpenseTab = tab;
    }
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        incomeExpenseTab.updateCashAdapter(null, incomeExpenseTab.getCashListViewModel(incomeExpenseTab).setSearchQuery(String.valueOf(editable)));
    }
}
