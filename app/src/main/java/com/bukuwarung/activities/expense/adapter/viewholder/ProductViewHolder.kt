package com.bukuwarung.activities.expense.adapter.viewholder

import android.util.Log
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.adapter.dataholder.ProductDataHolder
import com.bukuwarung.constants.AppConst
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.getColorCompat
import kotlinx.android.synthetic.main.item_product.view.*
import kotlinx.android.synthetic.main.item_product.view.checkbox
import kotlinx.android.synthetic.main.item_product.view.numberStepper
import kotlinx.android.synthetic.main.item_product.view.tv_product_name
import kotlinx.android.synthetic.main.item_product.view.tv_stock
import kotlinx.android.synthetic.main.item_selling_product.view.tv_stock_unit
import kotlinx.android.synthetic.main.item_selling_product.view.*
import kotlin.math.abs

class ProductViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    fun bind(dataHolder: ProductDataHolder, isInventoryEnabled: Boolean, isExpense: Boolean, checkCallback: (ProductDataHolder, ProductViewHolderEvent) -> Unit) {
        itemView.apply {
            if (!isExpense) {
                selling_price_txt.text = Utility.formatCurrency(dataHolder.productEntity?.unitPrice)
                selling_price_currency_txt.text = Utility.getCurrency()
                tv_edit.setOnClickListener { checkCallback(dataHolder, ProductViewHolderEvent.Edit) }
                if (dataHolder.isChecked)
                    tv_edit.visibility = View.VISIBLE
                else
                    tv_edit.visibility = View.GONE

            }

            if(dataHolder.productEntity?.trackInventory == AppConst.INVENTORY_TRACKING_DISABLED) {
                tv_stock.visibility = View.GONE
            }

            tv_product_name.text = dataHolder.productEntity?.name

            numberStepper.setup(checkbox, dataHolder) { product ->
                product ?: return@setup
                Log.d("PRODUCT VH", "$product")
                checkCallback(product, ProductViewHolderEvent.Selection)
                if (!isExpense) {
                    dataHolder.isEditShow = checkbox.isChecked
                }

            }

            tv_product_name.setOnClickListener { numberStepper.toggleChecked() }

            tv_stock.setTextColor(context.getColorCompat(R.color.black_80))
            tv_stock.text = resources.getString(R.string.product_stock_placeholder)
                .format(Utility.getRoundedOffPrice(dataHolder.productEntity?.stock), " ")
            val stockUnit  = "${resources.getString(R.string.forward_slash)}${dataHolder.productEntity?.measurementName}"
            tv_stock_unit.text = stockUnit
        }
    }
}

class ProductDividerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

enum class ProductViewHolderEvent {
    Selection, Edit, Delete
}