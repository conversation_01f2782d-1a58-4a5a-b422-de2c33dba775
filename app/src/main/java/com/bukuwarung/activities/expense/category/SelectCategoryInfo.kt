package com.bukuwarung.activities.expense.category

import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.SelectCategoryInfoBinding

class SelectCategoryInfo: BaseActivity() {

    lateinit var binding: SelectCategoryInfoBinding

    override fun setViewBinding() {
        binding = SelectCategoryInfoBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)

        val toolbar = binding.tbSelectCategoryInfo

        setUpToolbarWithHomeUp(toolbar)
    }

    override fun subscribeState() {

    }
}