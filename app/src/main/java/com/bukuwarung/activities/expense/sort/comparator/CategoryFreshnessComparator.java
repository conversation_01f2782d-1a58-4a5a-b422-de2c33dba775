package com.bukuwarung.activities.expense.sort.comparator;

import com.bukuwarung.activities.customer.sort.SortOrder;
import com.bukuwarung.database.dto.CashTransactionDto;

import java.util.Comparator;

public final class CategoryFreshnessComparator implements Comparator<CashTransactionDto> {

    SortOrder sortOrder = SortOrder.ASC;
    public CategoryFreshnessComparator(SortOrder order){
        this.sortOrder=order;
    }

    @Override
    public int compare(CashTransactionDto cashCategoryEntity1, CashTransactionDto cashCategoryEntity2) {
        String modifiedDate2 = cashCategoryEntity2.transactionDate;
        String modifiedDate1 = cashCategoryEntity1.transactionDate;
        if(sortOrder == SortOrder.DESC)
            return modifiedDate2.compareTo(modifiedDate1);
        else
            return modifiedDate1.compareTo(modifiedDate2);
    }
}
