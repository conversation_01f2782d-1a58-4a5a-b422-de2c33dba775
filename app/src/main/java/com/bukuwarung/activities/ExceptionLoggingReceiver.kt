package com.bukuwarung.activities

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.google.firebase.crashlytics.FirebaseCrashlytics

class ExceptionLoggingReceiver : BroadcastReceiver() {

    companion object {
        const val TAG = "ExceptionLoggingReceiver"
        const val ACTION_LOG_EXCEPTION = "com.bukuwarung.action.ACTION_LOG_EXCEPTION"
        const val EXCEPTION = "exception"
    }

    override fun onReceive(context: Context, intent: Intent?) {
        intent?.let {
            (intent.getSerializableExtra(EXCEPTION) as? Exception)?.let {
                FirebaseCrashlytics.getInstance().recordException(it)
            }
        }
    }
}