///**
// * Copyright 2017 Google Inc. All Rights Reserved.
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// * http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
// package com.bukuwarung.activities.notification.eoy;
//
//import android.content.Context;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.bukuwarung.R;
//import com.bukuwarung.database.entity.EoyEntry;
//import com.bukuwarung.session.SessionManager;
//import com.bukuwarung.utils.DateTimeUtils;
//import com.bukuwarung.utils.ProfileIconHelper;
//import com.facebook.drawee.view.SimpleDraweeView;
//import com.google.firebase.firestore.DocumentSnapshot;
//import com.google.firebase.firestore.Query;
//
///**
// * RecyclerView adapter for a list of eoyEntrys.
// */
//public class EntriesOfYouAdapter extends FirestoreAdapter<EntriesOfYouAdapter.ViewHolder> {
//
//    public interface OnEntrySelectedListener {
//
//        void onEntrySelected(DocumentSnapshot eoyEntry);
//
//    }
//
//    private OnEntrySelectedListener mListener;
//    private static Context mContext;
//
//    public EntriesOfYouAdapter(Query query, OnEntrySelectedListener listener, Context context) {
//        super(query);
//        mListener = listener;
//        mContext = context;
//    }
//
//    @NonNull
//    @Override
//    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
//        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
//        return new ViewHolder(inflater.inflate(R.layout.item_entryonyou, parent, false));
//    }
//
//    @Override
//    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
//        holder.bind(getSnapshot(position), mListener);
//    }
//
//    static class ViewHolder extends RecyclerView.ViewHolder {
//
//        SimpleDraweeView shopImageView;
//        TextView shopNameView;
//        TextView firstLetterTV;
//        TextView messageView;
//        TextView dateView;
//        TextView ownerName;
//        TextView ownerPhone;
//
//        public ViewHolder(View itemView) {
//            super(itemView);
//            shopImageView = itemView.findViewById(R.id.eoyShopPic);
//            shopNameView = itemView.findViewById(R.id.business_name);
//            messageView = itemView.findViewById(R.id.transaction_message);
//            dateView = itemView.findViewById(R.id.transaction_date);
//            ownerName = itemView.findViewById(R.id.shop_owner_name);
//            ownerPhone = itemView.findViewById(R.id.owner_phone);
//            firstLetterTV = itemView.findViewById(R.id.firstLetter);
//        }
//
//        public void bind(final DocumentSnapshot snapshot,
//                         final OnEntrySelectedListener listener) {
//
//            EoyEntry eoyEntry = snapshot.toObject(EoyEntry.class);
//            ProfileIconHelper.setProfilePic(mContext, shopImageView, firstLetterTV, eoyEntry.getShopName(), null);
//
//
//            shopNameView.setText(eoyEntry.getShopName());
//            long createAt = eoyEntry.getCreatedAt();
//            dateView.setText(DateTimeUtils.getReadableTimestamp(createAt));
//            messageView.setText(translateMessage(eoyEntry.getMessage()));
//            ownerName.setText(eoyEntry.getOwnerName());
//            ownerPhone.setText(eoyEntry.getOwnerPhone());
//
//            // Click listener
//            itemView.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View view) {
//                    if (listener != null) {
//                        listener.onEntrySelected(snapshot);
//                    }
//                }
//            });
//        }
//
//        private String translateMessage(String message){
//            try {
//                if (SessionManager.getInstance().getAppLanguage() == 1) {
//                    message = message.replace("Transaksi debetmu sebesar", "Debit transaction of");
//                    message = message.replace("Transaksi kreditmu sebesar", "Credit transaction of");
//                    message = message.replace("tercatat", "was recorded.");
//                    message = message.replace("Tercatat di", "New transaction at");
//                    message = message.replace("dengan utang sebesar", "was recorded of amount");
//                }
//            }catch(Exception e){
//                e.printStackTrace();
//            }
//            return message;
//        }
//
//    }
//}
