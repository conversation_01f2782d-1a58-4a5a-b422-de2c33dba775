//package com.bukuwarung.activities.notification.eoy;
//
//import android.os.Bundle;
//import android.util.Log;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.TextView;
//
//import androidx.recyclerview.widget.LinearLayoutManager;
//import androidx.recyclerview.widget.RecyclerView;
//
//import com.bukuwarung.R;
//import com.bukuwarung.activities.superclasses.AppFragment;
//import com.bukuwarung.database.entity.EoyEntry;
//import com.bukuwarung.session.SessionManager;
//import com.bukuwarung.session.User;
//import com.google.firebase.firestore.DocumentSnapshot;
//import com.google.firebase.firestore.FirebaseFirestore;
//import com.google.firebase.firestore.FirebaseFirestoreException;
//import com.google.firebase.firestore.Query;
//
//public class EntriesOfYouFragment extends AppFragment implements
//        View.OnClickListener,
//        EntriesOfYouAdapter.OnEntrySelectedListener {
//
//    private static final String TAG = "EoyFragment";
//
//    private RecyclerView mRestaurantsRecycler;
//    private ViewGroup mEmptyView;
//
//    private FirebaseFirestore mFirestore;
//    private Query mQuery;
//    private EntriesOfYouAdapter mAdapter;
//    private NotesDialogFragment mNotesDialog;
//
//    public void onCreate(Bundle paramBundle) {
//        super.onCreate(paramBundle);
//    }
//
//    public View onCreateView(LayoutInflater paramLayoutInflater, ViewGroup paramViewGroup, Bundle paramBundle) {
//
//        View view = paramLayoutInflater.inflate(R.layout.fragment_notifications_entries_against_you, paramViewGroup, false);
//        try {
//            mNotesDialog = new NotesDialogFragment();
//            mRestaurantsRecycler = view.findViewById(R.id.recycler_restaurants);
//            mEmptyView = view.findViewById(R.id.view_empty);
//            try {
//                TextView emptyTv = view.findViewById(R.id.view_empty_tv);
//                emptyTv.setText(SessionManager.getInstance().getAppLanguage() == 1? "No entries added against your quantity":"Kamu belum memiliki catatan apapun");
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//            FirebaseFirestore.setLoggingEnabled(true);
//
//            // Initialize Firestore and the main RecyclerView
//            initFirestore();
//            initRecyclerView();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return view;
//    }
//
//    private void initFirestore() {
//        mFirestore = FirebaseFirestore.getInstance();
//        SessionManager session = SessionManager.getInstance();
//        mQuery = mFirestore.collection("transaction_entries").whereEqualTo("receiver", session.getCountryCode().substring(1) + "" + User.getUserId()).orderBy("createdAt", Query.Direction.DESCENDING);
//    }
//
//    private void initRecyclerView() {
//        if (mQuery == null) {
//            Log.w(TAG, "No query, not initializing RecyclerView");
//        }
//        unseenCount = 0;
//        mAdapter = new EntriesOfYouAdapter(mQuery, this, getContext()) {
//
//            @Override
//            protected void onDataChanged() {
//                // Show/hide content if the query returns empty.
//                if (getItemCount() == 0) {
//                    mRestaurantsRecycler.setVisibility(View.GONE);
//                    mEmptyView.setVisibility(View.VISIBLE);
////                        unseenTv.setText("0");
//                } else {
//                    mRestaurantsRecycler.setVisibility(View.VISIBLE);
//                    mEmptyView.setVisibility(View.GONE);
//                    for (int i = 0; i < getItemCount(); i++) {
//                        DocumentSnapshot doc = getSnapshot(i);
//                        EoyEntry customer = doc.toObject(EoyEntry.class);
//                        if (customer.getUnseen() == 1) {
//                            unseenCount++;
//                        }
//                    }
////                        unseenTv.setText(String.valueOf(unseenCount));
//                }
//
//            }
//
//            @Override
//            protected void onError(FirebaseFirestoreException e) {
//                // Show a snackbar on errors
//                e.printStackTrace();
//            }
//        };
//
//        mRestaurantsRecycler.setLayoutManager(new LinearLayoutManager(getContext()));
//        mRestaurantsRecycler.setAdapter(mAdapter);
//    }
//
//    private int unseenCount = 0;
//
//
//    @Override
//    public void onStart() {
//        super.onStart();
//        if (mAdapter != null) {
//            mAdapter.startListening();
//        }
//    }
//
//    @Override
//    public void onStop() {
//        super.onStop();
//        if (mAdapter != null) {
//            mAdapter.stopListening();
//        }
//    }
//
//    @Override
//    public void onClick(View v) {
//
//    }
//
//    @Override
//    public void onEntrySelected(DocumentSnapshot eoyentry) {
//        mNotesDialog.show(getChildFragmentManager(), NotesDialogFragment.TAG);
//        EoyEntry entry = eoyentry.toObject(EoyEntry.class);
//        if (entry.getNote() == null || entry.getNote().isEmpty()) {
//            mNotesDialog.mNote = SessionManager.getInstance().getAppLanguage() == 1 ? "Comments were not provided for this transaction" : "tidak ada catatan dari warung";
//        } else {
//            mNotesDialog.mNote = entry.getNote();
//        }
//        mNotesDialog.ownerRegisteredNumber = entry.getOwnerPhone();
//    }
//}
