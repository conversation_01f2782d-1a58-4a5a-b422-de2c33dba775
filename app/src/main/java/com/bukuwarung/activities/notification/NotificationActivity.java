package com.bukuwarung.activities.notification;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.notification.discover.InfoDiscoveryAdapter;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.VerticalSwipeAnim;
import com.bukuwarung.database.entity.AppNotification;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.neuro.api.Neuro;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.android.AndroidInjection;

public class NotificationActivity extends AppActivity {
    @Inject
    Neuro neuro;

    public NotificationActivity() {
        super(new VerticalSwipeAnim());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AndroidInjection.inject(this);
        setContentView(R.layout.activity_entries_against_you);
        try {
            ImageView back = findViewById(R.id.backBtn);
            back.setOnClickListener(view -> onBackPressed());

            List<AppNotification> notificationList = BusinessRepository.getInstance(this).getNotificationsList();
            List<AppNotification> filteredNotificationList = new ArrayList<>();
            int totalTrx = 0;

            try {
                totalTrx = TransactionRepository.getInstance(getApplicationContext()).countAllUtangTrans();
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            for (AppNotification appNotification : notificationList) {
                if (totalTrx >= appNotification.minTrxToShow)
                    filteredNotificationList.add(appNotification);
            }

            RecyclerView recyclerView = findViewById(R.id.notificationRv);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            InfoDiscoveryAdapter adapter = new InfoDiscoveryAdapter(filteredNotificationList, recyclerView, this, neuro);
            recyclerView.setAdapter(adapter);

            if (ListUtils.isEmpty(filteredNotificationList)){
                recyclerView.setVisibility(View.GONE);
                findViewById(R.id.view_empty_tv).setVisibility(View.VISIBLE);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
