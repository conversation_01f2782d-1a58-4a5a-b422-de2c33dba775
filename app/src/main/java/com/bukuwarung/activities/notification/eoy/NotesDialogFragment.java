///**
// * Copyright 2017 Google Inc. All Rights Reserved.
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// * http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
// package com.bukuwarung.activities.notification.eoy;
//
//import android.content.Context;
//import android.content.Intent;
//import android.content.pm.PackageManager;
//import android.net.Uri;
//import android.os.Build;
//import android.os.Bundle;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.Button;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//import androidx.fragment.app.DialogFragment;
//
//import com.bukuwarung.Application;
//import com.bukuwarung.R;
//import com.bukuwarung.analytics.AppAnalytics;
//import com.bukuwarung.session.SessionManager;
//import com.bukuwarung.utils.Utility;
//import com.google.firebase.crashlytics.FirebaseCrashlytics;
//
//import java.util.concurrent.ExecutionException;
//
//public class NotesDialogFragment extends DialogFragment implements View.OnClickListener {
//
//    public static final String TAG = "EoyDetailDialog";
//
//    private TextView mNoteText;
//    public String mNote;
//    public String ownerRegisteredNumber="";
//
//    @Nullable
//    @Override
//    public View onCreateView(@NonNull LayoutInflater inflater,
//                             @Nullable ViewGroup container,
//                             @Nullable Bundle savedInstanceState) {
//        View v = inflater.inflate(R.layout.dialog_entry_notes, container, false);
//        mNoteText = v.findViewById(R.id.note);
//        AppAnalytics.trackEvent("open_transaction_detail_eoy");
//        v.findViewById(R.id.call_owner).setOnClickListener(this);
//        v.findViewById(R.id.whatsapp_owner).setOnClickListener(this);
//        v.findViewById(R.id.form_cancel).setOnClickListener(this);
//        mNoteText.setText(mNote);
//        try {
//            int lang = SessionManager.getInstance().getAppLanguage();
//            Button callBtn = v.findViewById(R.id.call_owner);
//            TextView note = v.findViewById(R.id.notesDlgTitle);
//            callBtn.setText(lang==1?"Call Shop":"telpon warung");
//            note.setText(lang==1?"Notes":"Catatan");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//        return v;
//    }
//
//    @Override
//    public void onAttach(Context context) {
//        super.onAttach(context);
//    }
//
//    @Override
//    public void onResume() {
//        super.onResume();
//        getDialog().getWindow().setLayout(
//                ViewGroup.LayoutParams.MATCH_PARENT,
//                ViewGroup.LayoutParams.WRAP_CONTENT);
//
//    }
//
//    @Override
//    public void onClick(View v) {
//        switch (v.getId()) {
//            case R.id.call_owner:
//                callCustomer();
//                break;
//            case R.id.form_cancel:
//                onCancelClicked(v);
//                break;
//            case R.id.whatsapp_owner:
//                AppAnalytics.trackEvent("whatsapp_owner_from_eoy");
//                openWhatsApp(ownerRegisteredNumber);
//                break;
//        }
//    }
//
//    private void callCustomer() {
//        try {
//            Utility.makeCall(getActivity(), ownerRegisteredNumber);
//            AppAnalytics.trackEvent("call_owner_from_eoy");
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//    }
//
//    /*private final void callCustomer() {
//        if (Build.VERSION.SDK_INT >= 23) {
//            if (getActivity().checkSelfPermission("android.permission.CALL_PHONE") == PackageManager.PERMISSION_GRANTED) {
//                Intent intent = new Intent("android.intent.action.CALL");
//                StringBuilder sb = new StringBuilder();
//                sb.append("tel:");
//                if(ownerRegisteredNumber.length()<=9){
//                    sb.append(ownerRegisteredNumber);
//                }else {
//                    sb.append(ownerRegisteredNumber);
//                }
//                intent.setData(Uri.parse(sb.toString()));
//                startActivity(intent);
//                return;
//            }
//            requestPermissions(new String[]{"android.permission.CALL_PHONE"}, 320);
//            return;
//        }
//        Intent callIntent = new Intent("android.intent.action.CALL");
//        StringBuilder sb2 = new StringBuilder();
//        sb2.append("tel:");
//        sb2.append(ownerRegisteredNumber);
//        callIntent.setData(Uri.parse(sb2.toString()));
//        startActivity(callIntent);
//    }*/
//
//    private final void openWhatsApp(String phoneNumber) {
//        PackageManager packageManager = Application.getAppContext().getPackageManager();
//        Intent intent = new Intent("android.intent.action.VIEW");
//        try {
//            StringBuilder sb = new StringBuilder();
//            sb.append("https://api.whatsapp.com/send?phone=");
////            sb.append(phoneNumber);
//            if(ownerRegisteredNumber.length()<=9){
//                sb.append("65"+ownerRegisteredNumber);
//            }else {
//                sb.append("62"+ownerRegisteredNumber);
//            }
//            intent.setPackage("com.whatsapp");
//            intent.setData(Uri.parse(sb.toString()));
//            if (intent.resolveActivity(packageManager) != null) {
//                startActivity(intent);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            FirebaseCrashlytics.getInstance().recordException(e);
//        }
//    }
//
//    public void onCancelClicked(View view) {
//        dismiss();
//    }
//}
