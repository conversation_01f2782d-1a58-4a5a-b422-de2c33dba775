package com.bukuwarung.activities.notification.discover;

import static com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL;

import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.AsyncTask;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.ImageView;
import android.widget.PopupMenu;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.WebviewActivity;
import com.bukuwarung.activities.referral.leaderboard.LeaderboardWebviewActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.entity.AppNotification;
import com.bukuwarung.neuro.api.Neuro;
import com.bukuwarung.neuro.api.SourceLink;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

public final class InfoDiscoveryAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements OnCheckedChangeListener {
    private final Context context;
//    private OnClickListener mOnClickListener = new CustomerAdapter$mOnClickListener$1(this);
    public final RecyclerView mRecyclerView;
    public List<AppNotification> rowHolderList;
    private final Neuro neuro;

    public final static class LoadImage extends AsyncTask<Void, Void, Bitmap> {


        final private String imageUrl;
        private ImageView imageView;
        private boolean hide;

        public LoadImage(ImageView imageView,String imageUrl,boolean hide){
            this.imageUrl = imageUrl;
            this.imageView = imageView;
            this.hide = hide;
        }

        @Override
        protected Bitmap doInBackground(Void... params) {
            try {
                URL url = new URL(imageUrl);
                return BitmapFactory.decodeStream(url.openConnection().getInputStream());
            }catch (Exception e){
                e.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(Bitmap image) {
            try {
                if(image == null){
                    if(hide)
                        imageView.setVisibility(View.GONE);
                }else {
                    imageView.setImageBitmap(image);
                    if (hide)
                        imageView.setVisibility(View.GONE);
                    else
                        imageView.setVisibility(View.VISIBLE);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    public final class InfoViewHolder extends RecyclerView.ViewHolder {

        private ImageView thumbnail;
        private ImageView bodyImg;
        private TextView title;
        private TextView bodyText;
        private ImageView menu;
        private TextView share;

        public InfoViewHolder(InfoDiscoveryAdapter customerAdapter, View view) {
            super(view);
            this.thumbnail = view.findViewById(R.id.thumbnail);
            this.bodyImg = view.findViewById(R.id.bodyImg);
            this.title = view.findViewById(R.id.title);
            this.bodyText = view.findViewById(R.id.bodyText);
            this.menu = view.findViewById(R.id.action_menu);
            this.share = view.findViewById(R.id.share_notification);
        }

        public ImageView getThumbnail() {
            return thumbnail;
        }

        public ImageView getBodyImg() {
            return bodyImg;
        }

        public TextView getTitle() {
            return title;
        }

        public TextView getBodyText() {
            return bodyText;
        }

        public ImageView getMenu() {
            return menu;
        }

        public TextView getShare() {
            return share;
        }
    }

    public final class EmptyViewHolder extends RecyclerView.ViewHolder {

        final InfoDiscoveryAdapter this$0;

        public EmptyViewHolder(InfoDiscoveryAdapter customerAdapter, final View view) {
            super(view);
            this.this$0 = customerAdapter;
        }
    }

    public final Context getContext() {
        return this.context;
    }

    public InfoDiscoveryAdapter(List<AppNotification> list, RecyclerView recyclerView, Context context2, Neuro neuro) {

        this.rowHolderList = list;
        this.mRecyclerView = recyclerView;
        this.context = context2;
        this.neuro = neuro;
    }

    private final void bindInfoViewHolder(InfoViewHolder infoViewHolder, final AppNotification appNotification, int i) {

        if(Utility.isBlank(appNotification.bannerUrl)){
            infoViewHolder.getBodyImg().setVisibility(View.GONE);
        }else{
            try {
                new LoadImage(infoViewHolder.bodyImg,appNotification.bannerUrl,false).execute(new Void[0]);
            }catch (Exception e){
                e.printStackTrace();
            }
            infoViewHolder.bodyImg.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    redirect(appNotification);
                }
            });
        }

        try{
            if(!Utility.isBlank(appNotification.thumbnailUrl)){
                new LoadImage(infoViewHolder.thumbnail,appNotification.thumbnailUrl,false).execute(new Void[0]);
            }
            infoViewHolder.share.setVisibility(appNotification.share.booleanValue() == true ? View.VISIBLE : View.GONE);
            infoViewHolder.share.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    shareNotification(appNotification.body,appNotification.link);
                }
            });

            final ImageView menu = infoViewHolder.menu;
            infoViewHolder.menu.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    showMenu(menu);
                }
            });

            infoViewHolder.bodyText.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    redirect(appNotification);
                }
            });

        }catch (Exception e){
            e.printStackTrace();
        }
        infoViewHolder.getBodyText().setText(appNotification.body);
        infoViewHolder.getTitle().setText(appNotification.title);
    }

    private final void showMenu(ImageView imageView) {
        final Context context = getContext();
        Context wrapper = new ContextThemeWrapper(getContext(), R.style.PopupMenu);
        final PopupMenu popupMenu = new PopupMenu(wrapper, imageView);
        popupMenu.getMenuInflater().inflate(R.menu.activity_notification_menu, popupMenu.getMenu());
        popupMenu.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                switch (item.getItemId()) {
                    case R.id.support:
                        openChat();
                        break;
                    case R.id.bookmark:
                        break;
                    case R.id.delete:
                        break;
                    default:
                        break;
                }
                return true;
            }
        });
        popupMenu.show();
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }

    private void shareNotification(String notificationTxt, String link) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append(notificationTxt);
            sb.append("\n"+link);

            Intent share = new Intent(Intent.ACTION_SEND);
            share.setType("text/plain");
            share.addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET);
            share.putExtra(Intent.EXTRA_TEXT, sb.toString());
            context.startActivity(Intent.createChooser(share, "Choose an App!"));
        } catch (ActivityNotFoundException e) {
            NotificationUtils.alertToast("WhatApp Not Installed");
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    private void openChat() {
        try{
            AppAnalytics.trackEvent("mh_support_requested");
            Intent sendIntent =new Intent("android.intent.action.MAIN");
            sendIntent.setComponent(new ComponentName("com.whatsapp", "com.whatsapp.Conversation"));
            sendIntent.setAction(Intent.ACTION_SEND);
            sendIntent.setType("text/plain");
            sendIntent.putExtra(Intent.EXTRA_TEXT,"");
            sendIntent.putExtra("jid", FeaturePrefManager.getInstance().getWhatsappId() +"@s.whatsapp.net");
            sendIntent.setPackage("com.whatsapp");
            context.startActivity(sendIntent);

        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
    }

    public final void setRowHolderList(List<AppNotification> list) {
        if (list == null) {
            this.rowHolderList = new ArrayList();
        } else {
            this.rowHolderList = list;
        }
        notifyDataSetChanged();
    }

    public long getItemId(int i) {
        String str;
        AppNotification rowHolder = (AppNotification) this.rowHolderList.get(i);

        StringBuilder sb = new StringBuilder();
        sb.append(i);
        sb.append(rowHolder.body);
        sb.append(rowHolder.id);
        str = sb.toString();

        return Utility.getHash(str);
    }

    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) {
        View inflate = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.notification_item, viewGroup, false);
        return new InfoViewHolder(this, inflate);
    }

    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        AppNotification rowHolder = (AppNotification) this.rowHolderList.get(i);
        int itemViewType = viewHolder.getItemViewType();
        InfoViewHolder customerViewHolder = (InfoViewHolder) viewHolder;
        if (rowHolder != null) {
            bindInfoViewHolder(customerViewHolder, rowHolder, i);
            return;
        }
    }

    private void redirect(AppNotification appNotification) {
        String link = appNotification.link;
        if (!Utility.isBlank(link)) {
            SourceLink sourceLink = new SourceLink(context, link);
            neuro.route(
                sourceLink,
                context::startActivity,
                () -> null,
                throwable -> {
                    redirectWithLegacyLink(appNotification);
                    return null;
                }
            );
        }
    }

    private void redirectWithLegacyLink(AppNotification appNotification){
        if(!Utility.isBlank(appNotification.link)) {
            if(appNotification.link.contains("app::")){
                try {
                    String activityName = appNotification.link.split("::")[1];
                    String link = DEEPLINK_INTERNAL_URL + "?type=act&data=" + activityName;
                    SourceLink sourceLink = new SourceLink(context, link);

                    neuro.route(
                        sourceLink,
                        context::startActivity,
                        () -> null,
                        throwable -> {
                            throwable.printStackTrace();
                            return null;
                        }
                    );
                }catch (Exception e){
                    e.printStackTrace();
                }
            }else if(appNotification.link.contains("leaderboard::")){
                String url = AppConfigManager.getInstance().getReferralLeaderboardUrl();
                Intent webViewIntent = LeaderboardWebviewActivity.Companion.createIntent(context, url, context.getString(R.string.leaderboard_program),"bell_banner");
                webViewIntent.putExtra(LeaderboardWebviewActivity.WEBVIEW_PARAM_IS_LEADERBOARD, true);
                context.startActivity(webViewIntent);
            }else {
                if (appNotification.useWebview) {
                    Intent intent = new Intent(context, WebviewActivity.class);
                    intent.putExtra("link", appNotification.link);
                    intent.putExtra("title", appNotification.title);

                    context.startActivity(intent);
                } else {
                    Intent browserIntent = new Intent(
                            Intent.ACTION_VIEW, Uri.parse(appNotification.link));
                    context.startActivity(browserIntent);
                }
            }
        }
    }

    public int getItemCount() {
        if (this.rowHolderList == null) {
            return 0;
        }
        return this.rowHolderList.size();
    }
}
