package com.bukuwarung.activities.transaction.customer;

import android.app.Application;
import android.graphics.Bitmap;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.customer.adapter.dataholder.TransactionDataHolder;
import com.bukuwarung.activities.transaction.customer.adapter.dataholder.TransactionEmptyDataHolder;
import com.bukuwarung.data.repository.FirebaseStorageRepository;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.domain.customer.CustomerUseCase;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Nullable;

public final class TransactionListViewModel extends AndroidViewModel {

    private LiveData<CustomerEntity> customerEntityLiveData;

    private String customerId;
    private List<TransactionEntity> transactionList;

    private MediatorLiveData<List<DataHolder>> liveDataMerger = new MediatorLiveData<>();

    private TransactionRepository transactionRepository;
    private CashRepository cashRepository;
    private CustomerUseCase customerUseCase;

    public TransactionListViewModel(Application application, String customerId) {
        super(application);
        this.customerId = customerId;
        customerEntityLiveData = CustomerRepository.getInstance(application).getObservableCustomerById(this.customerId);
        this.transactionRepository = TransactionRepository.getInstance(application);
        this.cashRepository = CashRepository.getInstance(application);
        this.customerUseCase = new CustomerUseCase(CustomerRepository.getInstance(application), new FirebaseStorageRepository(), SessionManager.getInstance());
        this.liveDataMerger.addSource(TransactionRepository.getInstance(application).getTransactionListByCustomerId(this.customerId), new Observer<List<TransactionEntity>>() {

            @Override
            public void onChanged(List<TransactionEntity> list) {
                transactionList = list;
                ArrayList convertedList = convertToViewObject();
                liveDataMerger.setValue(convertedList);
            }
        });
    }

    public final LiveData<List<DataHolder>> getDataHolderList() {
        return this.liveDataMerger;
    }

    public final LiveData<CustomerEntity> getCustomerLiveData() {
        return this.customerEntityLiveData;
    }

    public final List<TransactionEntity> getTransactionList() {
        return this.transactionList;
    }

    public ArrayList convertToViewObject() {

        ArrayList convertedList = new ArrayList();
        for (TransactionEntity transactionEntity : this.transactionList) {
            convertedList.add(new TransactionDataHolder(transactionEntity));
        }
        if (ListUtils.isEmpty(this.transactionList)) {
            convertedList.add(new TransactionEmptyDataHolder());
        }
        return convertedList;
    }

    public void settleRelatedCashTransactions() {
        if (transactionList.isEmpty())
            return;
        for (TransactionEntity transaction : transactionList) {
            // change cash-trx status with transaction.transactionId from "Belum Lunas" (-1) to "Lunas" (1)
            cashRepository.settleCashTransactionByCustomerTransactionId(transaction.transactionId, 1);
        }
    }

    public void updateExistingCustomer(CustomerEntity customerEntity, @Nullable Bitmap image){
        customerUseCase.updateCustomer(customerEntity, image);
    }
}
