package com.bukuwarung.activities.transaction.customer;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.DatePicker;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelProviders;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.customerprofile.CustomerDetailActivity;
import com.bukuwarung.activities.customerprofile.tasks.DeleteCustomerAsyncTask;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.superclasses.FadeAnim;
import com.bukuwarung.activities.transaction.customer.adapter.TransactionAdapter;
import com.bukuwarung.activities.transaction.customer.add.AddTransactionActivity;
import com.bukuwarung.activities.transaction.customer.download.DisplayDownloadedReportPDF;
import com.bukuwarung.activities.transaction.customer.download.PrepareCustomerTransactionPayLoad;
import com.bukuwarung.activities.transaction.customer.observer.CustomerLiveDataObserver;
import com.bukuwarung.activities.transaction.customer.reminder.DeleteCstDialog;
import com.bukuwarung.activities.transaction.customer.reminder.PaymentReminderDateHandler;
import com.bukuwarung.activities.transaction.customer.reminder.UpdateCustomerPaymentReminderDate;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.PaymentConst;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.dialogs.login.LoginBottomSheetDialog;
import com.bukuwarung.dialogs.login.VerifyOtpBottomSheetDialog;
import com.bukuwarung.dialogs.payment.NeedKycDialog;
import com.bukuwarung.enums.ReleasedCountry;
import com.bukuwarung.neuro.api.Navigator;
import com.bukuwarung.neuro.api.Neuro;
import com.bukuwarung.neuro.api.SourceLink;
import com.bukuwarung.payments.bottomsheet.KycProcessingBottomSheet;
import com.bukuwarung.payments.checkout.PaymentCheckoutActivity;
import com.bukuwarung.payments.constants.KycStatusMetadata;
import com.bukuwarung.payments.data.model.PaymentHistory;
import com.bukuwarung.payments.data.model.PaymentMetadata;
import com.bukuwarung.payments.deeplink.handler.PaymentCheckoutSignalHandler;
import com.bukuwarung.payments.pref.PaymentPrefManager;
import com.bukuwarung.payments.utils.InjectorUtils;
import com.bukuwarung.payments.utils.PaymentUtils;
import com.bukuwarung.payments.viewmodels.PaymentsViewModelFactory;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.OnboardingPrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.share.ShareLayoutImage;
import com.bukuwarung.tutor.shape.FocusGravity;
import com.bukuwarung.tutor.shape.ShapeType;
import com.bukuwarung.tutor.view.OnboardingWidget;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.ImageUtils;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.ProfileIconHelper;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.ShareUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskExecutors;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.button.MaterialButton;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import dagger.android.AndroidInjection;
import kotlin.Unit;

public final class CustomerTransactionActivity extends AppActivity implements View.OnClickListener, LoginBottomSheetDialog.LoginSheetListener, VerifyOtpBottomSheetDialog.OtpSheetListener, OnboardingWidget.OnboardingWidgetListener, LunaskanBottomSheetDialog.LunaskanSheetListener, Navigator {

    public CustomerEntity customerEntity;
    public DatePicker reminderDatePicker;
    public TransactionListViewModel transactionListViewModel;
    private CustomerTransactionViewModel customerTransactionViewModel;
    public PaymentsViewModelFactory paymentsViewModelFactory;
    public TextView effectiveBalanceTv;
    public TextView balanceTypeStr;
    public TextView paymentReminderDate;
    public RelativeLayout setDueDateArea;
    public TextView btnWa;
    public TextView btnShare;
    public TextView btnPdf;
    public ImageView imageView;
    public TextView bottomText;
    public String currentReminderDate;
    public RelativeLayout parent;
    Toolbar toolbar;
    TextView btnGiveMoney;
    TextView btnReceivePayment;
    MaterialButton btnLunaskan;
    private String customerId;
    private TextView lunas;
    private TextView customerNameTv;
    private TextView customerContactSubTv;
    private TextView customerNameInitials;
    private AppCompatImageView customerPic;
    private RecyclerView transactionsRecyclerView;
    private TransactionAdapter adapter;
    public LinearLayout reminderArea;
    private ManualReminderDialog manualReminderDialog;
    private MenuItem menuCallCustomer;
    private View layoutPayment;
    private String launchPaymentType;

    private LoginBottomSheetDialog loginBtSheet;
    private VerifyOtpBottomSheetDialog otpBtSheet;
    private LunaskanBottomSheetDialog lunaskanBtnSheet;
    private String currentEntryPoint = "";
    private boolean hasShownTutorialReminder = false;
    private boolean hasShownTutorialReminderForPaymentLink = false;
    private boolean hasShownTutorialCalendar = false;
    private boolean hasShownTutorialPayment = false;
    private boolean hasShownTutorialOneClickSettlement = false;
    private boolean isCoachmarkShowing = false;
    private OnboardingWidget onboardingWidget;

    private boolean isLayoutHidden = false;

    @Inject
    Neuro neuro;

    public CustomerTransactionActivity() {
        super(new FadeAnim());
    }

    public static void startActivity(Context context, String customerId) {
        Intent starter = new Intent(context, CustomerTransactionActivity.class);
        starter.putExtra("customerId", customerId);
        context.startActivity(starter);
    }

    public String getCustomerId() {
        return this.customerId;
    }

    @Override
    public void onCreate(Bundle bundle) {

        super.onCreate(bundle);
        AndroidInjection.inject(this);
        setContentView(R.layout.activity_transactions);

        this.parent = findViewById(R.id.rl_parent);


        this.customerId = getIntent().getStringExtra("customerId");
        this.customerEntity = CustomerRepository.getInstance(getApplicationContext()).getCustomerById(this.customerId);
        this.toolbar = findViewById(R.id.toolbar);
        this.customerNameTv = toolbar.findViewById(R.id.name);
        this.customerContactSubTv = toolbar.findViewById(R.id.phone);
        this.customerPic = toolbar.findViewById(R.id.customerPic);
        this.customerNameInitials = toolbar.findViewById(R.id.firstLetter);
        this.bottomText = findViewById(R.id.tvMenuPayment);
        this.imageView = findViewById(R.id.imgWallet);
        toolbar.findViewById(R.id.back_btn).setOnClickListener(this);
        setSupportActionBar(toolbar);
        Objects.requireNonNull(getSupportActionBar()).setDisplayHomeAsUpEnabled(false);
        refreshToolbar(this.customerEntity);

        this.effectiveBalanceTv = findViewById(R.id.customerBalance);
        this.balanceTypeStr = findViewById(R.id.balanceStatus);
        this.setDueDateArea = findViewById(R.id.setDueDateArea);
        this.paymentReminderDate = findViewById(R.id.paymentReminderDate);
        this.reminderArea = findViewById(R.id.reminderArea);
        this.layoutPayment = findViewById(R.id.layoutPayment);

        setDueDateArea.setOnClickListener(new PaymentReminderDateHandler(
                this
        ));
        setLayoutPayment();
        initTransactionListView();
        this.btnLunaskan = findViewById(R.id.lunaskanBtn);
        this.btnGiveMoney = findViewById(R.id.debitBtn);
        this.btnReceivePayment = findViewById(R.id.creditBtn);
        lunas = findViewById(R.id.lunas_label);
        this.btnWa = findViewById(R.id.tvMenuWhatsApp);
        this.btnShare = findViewById(R.id.tvMenuShare);
        this.btnPdf = findViewById(R.id.tvMenuLaporan);
        this.btnWa.setOnClickListener(this);
        this.btnShare.setOnClickListener(this);
        this.btnPdf.setOnClickListener(this);
        this.layoutPayment.setOnClickListener(this);

        boolean isNewUtangFlow = !RemoteConfigUtils.INSTANCE.shouldShowOldUtangForm();
        if (isNewUtangFlow) {
            this.btnGiveMoney.setText(getResources().getText(R.string.bal_debit));
            this.btnReceivePayment.setText(getResources().getText(R.string.bal_credit));
        }

        btnGiveMoney.setOnClickListener(new AddTransactionBtnClickListener(this, AddTransactionActivity.DEBIT, this.customerEntity, this));
        btnReceivePayment.setOnClickListener(new AddTransactionBtnClickListener(this, AddTransactionActivity.CREDIT, this.customerEntity, this));
        btnLunaskan.setOnClickListener(this);
        initBankAccountList();
        setCustomerDetailClickHandler();
        PaymentMetadata metadata = PaymentPrefManager.Companion.getInstance().getPaymentMetadata();
        if (metadata != null && metadata.isPaidUsers()) {
            TextView free = findViewById(R.id.free_txt);
            free.setVisibility(View.GONE);
        }

        if (getIntent().hasExtra("isHidden")) {
            isLayoutHidden = true;
//            this.parent.setVisibility(View.GONE);
            onShareClicked(true);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NotNull String[] permissions,
                                           @NotNull int[] grantResults) {
        if (requestCode == PermissionConst.WRITE_EXTERNAL_STORAGE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                AppAnalytics.trackEvent("granted_storage_permission");
                showShareReportWithCustomerDialog();
            }
        }
    }

    private void setLayoutPayment() {
        if (InputUtils.getTransactionType(customerEntity.balance) == 1) {
            imageView.setImageResource(R.drawable.ic_payment_out_new);
            bottomText.setText(getString(R.string.pay_debt));
        } else {
            imageView.setImageResource(R.drawable.ic_payment_in_new);
            bottomText.setText(getString(R.string.collect_debt));
        }
    }

    private void initBankAccountList(){
        BookEntity book = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
        paymentsViewModelFactory = InjectorUtils.INSTANCE.providePaymentsViewModelFactory(this,customerId,null,book.bookId,
                0,null,null,null,
                null,null,false,null,null,false,null, false, null);
        customerTransactionViewModel = new ViewModelProvider(this, paymentsViewModelFactory).get(CustomerTransactionViewModel.class);
        customerTransactionViewModel.getObserveEvent().observe(this, event -> {
            if(event instanceof CustomerTransactionViewModel.Event.ShareWithoutPaymentLink) {
                if(manualReminderDialog != null)
                    manualReminderDialog.dismiss();
                CustomerTransactionViewModel.Event.ShareWithoutPaymentLink e = (CustomerTransactionViewModel.Event.ShareWithoutPaymentLink) event;
                shareTransaction(customerEntity, e.isWa(), e.getBalanceAmount(), "");
            } else if(event instanceof CustomerTransactionViewModel.Event.ShareWithPaymentLink) {
                if(manualReminderDialog != null)
                    manualReminderDialog.dismiss();
                CustomerTransactionViewModel.Event.ShareWithPaymentLink e = (CustomerTransactionViewModel.Event.ShareWithPaymentLink) event;
                shareTransaction(customerEntity, e.isWa(), e.getBalanceAmount(), e.getUrl());
            } else if(event instanceof CustomerTransactionViewModel.Event.ShowReminderPopup) {
                manualReminderDialog = new ManualReminderDialog(this,(String balanceAmount, Boolean createPayment) -> {
                    manualReminderDialog.findViewById(R.id.manualReminderContent).setVisibility(View.GONE);
                    manualReminderDialog.findViewById(R.id.manualReminderLoader).setVisibility(View.VISIBLE);
                    customerTransactionViewModel.shareInvoice(balanceAmount, createPayment);
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_SEND_REMINDER);
                    return Unit.INSTANCE;
                },customerEntity.balance);
                manualReminderDialog.show();
            }
            });
        customerTransactionViewModel.getViewState().observe(this, viewState -> {
            if(manualReminderDialog == null || !manualReminderDialog.isShowing()) return;
            if(viewState.getShowLoading()) {
                manualReminderDialog.findViewById(R.id.manualReminderLoader).setVisibility(View.VISIBLE);
                manualReminderDialog.findViewById(R.id.manualReminderContent).setVisibility(View.GONE);
            } else {
                manualReminderDialog.findViewById(R.id.manualReminderLoader).setVisibility(View.GONE);
                manualReminderDialog.findViewById(R.id.manualReminderContent).setVisibility(View.VISIBLE);
            }
            manualReminderDialog.setAdminFee(viewState.getAdminFee());
        });
    }

    private void showLunaskanBottomSheet() {
        if(!Utility.isBlank(this.customerId) && Utility.isBlank(this.customerEntity.name)){
            this.customerEntity = CustomerRepository.getInstance(getApplicationContext()).getCustomerById(this.customerId);
        }
        if(Utility.isBlank(this.customerEntity.name)){
            this.customerEntity.name = " ";
        }
        lunaskanBtnSheet = LunaskanBottomSheetDialog.Companion.newInstance(this.customerEntity.balance, this.customerEntity.customerId, this.customerEntity.name, this);
        lunaskanBtnSheet.show(getSupportFragmentManager(), "lunaskan-dialog");
    }

    private void saveCustomerTransaction(boolean sendSmsProof) {
        // to settle customer trx
        TransactionRepository.getInstance(Application.getAppContext()).saveNewTransaction(User.getBusinessId(), this.customerId, -this.customerEntity.balance, Utility.getStorableDateString(new Date()), this.customerEntity.balance > 0 ? getString(R.string.filter_nil) : getString(R.string.paid_off), 1);
        // to update all cash-trx status from "belum lunas" to "lunas"
        transactionListViewModel.settleRelatedCashTransactions();
        if (sendSmsProof && !Utility.isBlank(AppConfigManager.getInstance().getLunasApi())) {
            Utility.sendSms(this.customerEntity.phone, -this.customerEntity.balance, 5, this, this.customerEntity.altCustomerId, -1, 0);
        }
    }

    private void setCustomerDetailClickHandler() {
        findViewById(R.id.customer_name_layout).setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                Intent intent = new Intent(getBaseContext(), CustomerDetailActivity.class);
                intent.putExtra("customerId", customerId);
                startActivity(intent);
            }
        });
        findViewById(R.id.customerIcon).setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                Intent intent = new Intent(getBaseContext(), CustomerDetailActivity.class);
                intent.putExtra("customerId", customerId);
                startActivity(intent);
            }
        });
    }

    private void showShareReportWithCustomerDialog() {
        if (Build.VERSION.SDK_INT >= 23 && !PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            requestPermissions(new String[]{"android.permission.WRITE_EXTERNAL_STORAGE"}, PermissionConst.WRITE_EXTERNAL_STORAGE);
        } else {
            downloadReport(true);
        }
    }

    public final void downloadReport(boolean downloadPdf) {

        ProgressDialog progressDialog = ComponentUtil.showProgressDialog(this, this.getString(R.string.preparing_customer_pdf));
        List list = transactionListViewModel.getTransactionList();
        Task<PrepareCustomerTransactionPayLoad.ReportTaskResult> task = new PrepareCustomerTransactionPayLoad(this.getCustomerId()).getTask(list);
        task.continueWith(TaskExecutors.MAIN_THREAD, new DisplayDownloadedReportPDF(this, progressDialog, downloadPdf, "com.whatsapp"));
    }

    private void initTransactionListView() {

        this.transactionsRecyclerView = findViewById(R.id.transactionRV);
        this.transactionListViewModel = ViewModelProviders.of(this, new TransactionViewModelFactory(new Application(), this.customerId)).get(TransactionListViewModel.class);
        List arrayList = new ArrayList();
        this.adapter = new TransactionAdapter(arrayList, this.transactionsRecyclerView, this, this);
        this.transactionsRecyclerView.setAdapter(this.adapter);
        this.transactionsRecyclerView.setLayoutManager(new LinearLayoutManager(this));

        LifecycleOwner lifecycleOwner = this;
        this.transactionListViewModel.getDataHolderList().observe(lifecycleOwner, new Observer<List<? extends DataHolder>>() {
            @Override
            public void onChanged(List<? extends DataHolder> list) {
                adapter.setDataHolderList(list);
            }
        });
        this.transactionListViewModel.getCustomerLiveData().observe(lifecycleOwner, new CustomerLiveDataObserver(this));

    }

    public void refreshToolbar(CustomerEntity customerEntity) {
        this.customerNameTv.setText(customerEntity.name);
        ProfileIconHelper.setProfilePic(this, this.customerPic, this.customerNameInitials, customerEntity.name, customerEntity.image);
        if (!Utility.isBlank(customerEntity.phone)) {
            customerContactSubTv.setVisibility(View.VISIBLE);
            String customerPhone = customerEntity.phone;
            if (customerEntity.countryCode != null && customerEntity.countryCode.contains(ReleasedCountry.INDONESIAN.getPhoneCd())) {
                customerPhone = "0" + customerPhone;
            } else {
                customerPhone = customerEntity.countryCode + customerPhone;
            }
            this.customerContactSubTv.setText(customerPhone);
        }
    }

    public final void setReminderDate(String str) {
        new Thread(new UpdateCustomerPaymentReminderDate(this, str)).start();
        this.currentReminderDate = str;
    }

    private String getReportUrl(CustomerEntity customerEntity) {
        StringBuilder sb = new StringBuilder();
        if (TextUtils.isEmpty(getCustomerId())) {
            String format = String.format(Utility.getReportUrl(), customerEntity.altCustomerId);
            sb.append(format);
            sb.append("\n");
        } else {
            return "https://bukuwarung.com/app";
        }
        return sb.toString();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tvMenuWhatsApp:
                AppAnalytics.trackEvent("send_reminder_via_whatsapp");
                onShareClicked(true);
                break;
            case R.id.tvMenuShare:
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SEND_REMINDER_VIA_SMS);
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_SEND_REMINDER_VIA_SMS, this);
                onShareClicked(false);
                break;
            case R.id.back_btn:
                onBackPressed();
                break;
            case R.id.tvMenuLaporan:
                AppAnalytics.trackEvent("click_customer_report_pdf");
                if (Utility.hasInternet()) {
                    showShareReportWithCustomerDialog();
                } else {
                    new CommonConfirmationBottomSheet().showNoInternetBottomSheet(this, getSupportFragmentManager());
                }
                break;
            case R.id.layoutPayment:
                onPaymentClicked();
                break;
            case R.id.lunaskanBtn:
                showLunaskanBottomSheet();
                break;
        }
    }

    private void onPaymentClicked() {
        if (!SessionManager.getInstance().isGuestUser()) {
            AppAnalytics.trackEvent("payment_customer_menu_pembayaran");
            if (InputUtils.getTransactionType(customerEntity.balance) == 1) {
                navigateToPaymentOut();
            } else {
                navigateToPaymentIn();
            }
        } else {
            callLoginBottomsheet(false, AnalyticsConst.PAYMENT);
        }
    }

    private boolean needKyc(PaymentMetadata metadata) {
        return metadata != null && metadata.isKycUsers() && metadata.getKyc() != null &&
                (metadata.getKyc().getStatus() == null || metadata.getKyc().getStatus() != KycStatusMetadata.VERIFIED);
    }

    private void showKycDialog(KycStatusMetadata status) {
        if (status != KycStatusMetadata.PENDING) {
            new NeedKycDialog(this, status == KycStatusMetadata.REJECTED).show();
        } else {
            KycProcessingBottomSheet kycProcessingBottomSheet = KycProcessingBottomSheet.Companion.createInstance(false);
            kycProcessingBottomSheet.show(getSupportFragmentManager(), KycProcessingBottomSheet.TAG);
        }
    }

    private void navigateToPaymentIn() {
        launchPaymentType = String.valueOf(PaymentConst.TYPE_PAYMENT_IN);
        AppAnalytics.trackEvent("payment_customer_tagih_clicked");
        SourceLink sourceLink = new SourceLink(
                this,
                PaymentCheckoutSignalHandler.Companion.getPaymentLink(
                        PaymentHistory.TYPE_PAYMENT_IN,
                        AnalyticsConst.CUSTOMER,
                        AnalyticsConst.CUSTOMER,
                        PaymentConst.DEBT_PAYMENT_CATEGORY, customerEntity.customerId, customerEntity.balance
                )
        );
        neuro.route(sourceLink, this, this::onNeuroSuccess, this::onNeuroFailure);
    }

    private Unit onNeuroSuccess() {
        return Unit.INSTANCE;
    }

    private Unit onNeuroFailure(Throwable ex) {
        String paymentType;
        if (launchPaymentType.equals(String.valueOf(PaymentConst.TYPE_PAYMENT_IN))) {
            paymentType = PaymentConst.KYC_PAYMENT_IN;
        } else {
            paymentType = PaymentConst.KYC_PAYMENT_OUT;
        }
        if (PaymentUtils.INSTANCE.shouldBeBlockedAsPerKycTier(paymentType)) {
            PaymentUtils.INSTANCE.showKycKybStatusBottomSheet(getSupportFragmentManager(), AnalyticsConst.ACCOUNTING);
            return Unit.INSTANCE;
        }
        startActivity(
                PaymentCheckoutActivity.Companion.createIntent(
                        this, launchPaymentType,
                        customerEntity.customerId, "customer",
                        customerEntity.bookId, null,
                        PaymentConst.DEBT_PAYMENT_CATEGORY, null
                )
        );
        FirebaseCrashlytics.getInstance().recordException(ex);
        return Unit.INSTANCE;
    }

    private void navigateToPaymentOut() {
        AppAnalytics.trackEvent("payment_customer_bayar_clicked");
        launchPaymentType = String.valueOf(PaymentConst.TYPE_PAYMENT_OUT);
        SourceLink sourceLink = new SourceLink(
                this,
                PaymentCheckoutSignalHandler.Companion.getPaymentLink(
                        PaymentHistory.TYPE_PAYMENT_OUT,
                        AnalyticsConst.CUSTOMER,
                        AnalyticsConst.CUSTOMER,
                        PaymentConst.DEBT_PAYMENT_CATEGORY, customerEntity.customerId, customerEntity.balance
                )
        );
        neuro.route(sourceLink, this, this::onNeuroSuccess, this::onNeuroFailure);
    }

    private void onShareClicked(boolean isWhatsapp) {
        int count = TransactionRepository.getInstance(this).getUtangTransactionCountWithDeletedRecords();
        PaymentMetadata metadata = PaymentPrefManager.Companion.getInstance().getPaymentMetadata();
        if (count < AppConfigManager.getInstance().getManualReminderPaymentLinkCount() || -customerEntity.balance < RemoteConfigUtils.INSTANCE.getMinimumPaymentAmount()
                || !Utility.hasInternet() || needKyc(metadata)) {
            shareTransaction(customerEntity, isWhatsapp, "", "");
            return;
        }
        customerTransactionViewModel.checkBankAccounts(isWhatsapp,customerId);
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PAYMENT_REQUEST_CREATED, this);
    }

    private void shareTransaction(CustomerEntity customerEntity, boolean withWhatsApp,String balanceAmount, String paymentLink) {
        try {

            TextView tvWarungName = findViewById(R.id.shopName);
            TextView tvWarungPhone = findViewById(R.id.shopPhone);
            TextView tvDueAmount = findViewById(R.id.dueAmount);
            TextView tvReminder = findViewById(R.id.reminderExplain);

            BookEntity bookEntity = getCurrentBook();

            String amount = Utility.getCurrency() + Utility.formatCurrency(Math.abs(customerEntity.balance));
            if (bookEntity != null) {
                if (Utility.isBlank(bookEntity.bookName)) {
                    tvWarungName.setText("-");
                } else {
                    tvWarungName.setText(bookEntity.bookName);
                }
                tvWarungPhone.setText(Utility.beautifyPhoneNumber(getCurrentBook().businessPhone));
                if (SessionManager.getInstance().isGuestUser()) {
                    tvWarungPhone.setText("-");
                }
                if (customerEntity.balance != null) {
                    if (!balanceAmount.equals(""))
                        amount = balanceAmount;
                    tvDueAmount.setText(amount);

                    String message = getString(R.string.payment_reminder_string);
                    String date = DateTimeUtils.getReadableReminderDate(System.currentTimeMillis());
                    String formattedMessage = String.format(message, amount, date);
                    tvReminder.setText(formattedMessage);
                }
            }
            shareReceipt(withWhatsApp, paymentLink, amount);

        } catch (Exception ex) {
            Toast.makeText(this, "Terjadi kesalahan", Toast.LENGTH_SHORT).show();
            FirebaseCrashlytics.getInstance().recordException(ex);
        }
    }

    public void shareReceipt(boolean withWhatsApp,String paymentLink, String amount) {
        Double balance;
        try {
            balance = Utility.extractAmountFromText(amount);
        } catch (Exception e) {
            balance = customerEntity.balance;
        }
        CardView layoutWhatsAppPreview = findViewById(R.id.layoutWhatsAppPreview);
        Task<ImageUtils.SaveToDiskTaskResult> saveViewSnapshot = ImageUtils.saveLayoutConvertedImage(layoutWhatsAppPreview, false);
        CustomerEntity customer = CustomerRepository.getInstance(this).getCustomerById(customerEntity.customerId);
        ShareLayoutImage shareLayoutImage = new ShareLayoutImage(ShareUtils.getReminderSharingText(this, getReportUrl(customerEntity), balance, customerEntity.name, customer.language, paymentLink), this, "com.whatsapp", customerEntity.phone, withWhatsApp, false);
        saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.customer_detail_menu, menu);
        try {
            menuCallCustomer = menu.getItem(0);
            menuCallCustomer.setVisible(!Utility.isBlank(customerEntity.phone));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                break;
            case R.id.menuEditCustomer:
                editCustomer();
                break;
            case R.id.menuDeleteCustomer:
                showDeleteDialog();
                break;
            case R.id.menuCallCustomer:
                callCustomer();
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void callCustomer() {
        try {
            Utility.makeCall(this, customerEntity.phone);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void editCustomer() {
        AppAnalytics.trackEvent("edit_customer");
        Intent intent = new Intent(getBaseContext(), CustomerDetailActivity.class);
        intent.putExtra("customerId", customerId);
        startActivity(intent);
    }

    private void showDeleteDialog() {
        final DeleteCstDialog dialog =
                new DeleteCstDialog(
                        this,
                        promptResult -> {
                            if (promptResult) {
                                new DeleteCustomerAsyncTask(this).execute(new CustomerEntity[]{customerEntity});
                                AppAnalytics.trackEvent("delete_customer");
                            }
                            return null; // java to kotlin unit conversion
                        }
                );
        dialog.show();
    }

    @Override
    public void onBackPressed() {
        if (onboardingWidget != null && onboardingWidget.isShown()) {
            onboardingWidget.dismiss(false, false, true);
        } else {
            MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
            finish();
        }
    }

    public void showLunas(boolean show) {
        if (show) {
            btnLunaskan.setVisibility(View.GONE);
            lunas.setVisibility(View.VISIBLE);
        } else {
            btnLunaskan.setVisibility(View.VISIBLE);
            lunas.setVisibility(View.GONE);
        }
    }

    public String getCustomerName() {
        return customerEntity.name;
    }

    public String getCustomerPhone() {
        return customerEntity.phone;
    }

    @Override
    public void goToVerifyOtp(@NotNull String phone, @NotNull String countryCode, @NotNull String method) {
        otpBtSheet = VerifyOtpBottomSheetDialog.Companion.newInstance(phone, countryCode, currentEntryPoint, this);
        otpBtSheet.show(getSupportFragmentManager(), LoginBottomSheetDialog.TAG);
    }

    @Override
    public void callLoginBottomsheet(boolean openKeyboard, @NotNull String entryPoint) {
        currentEntryPoint = entryPoint;
        showLogin(openKeyboard);
    }

    private void showLogin(boolean openKeyboard) {
        loginBtSheet = LoginBottomSheetDialog.Companion.newInstance(currentEntryPoint, this);
        loginBtSheet.show(getSupportFragmentManager(), LoginBottomSheetDialog.TAG);
    }

    @Override
    public void onFinishOtp() {
        BusinessRepository.getInstance(this).mergeGuestRecords();
        CashRepository.getInstance(this).mergeGuestRecords();
        ProductRepository.getInstance(this).mergeGuestRecords();
        SessionManager.getInstance().isGuestUser(false);
        Thread mThread = new Thread() {
            @Override
            public void run() {
                TransactionUtil.backupAllTransactions();
                MainActivity.startActivityAndClearTop(CustomerTransactionActivity.this);
            }
        };
        mThread.start();
    }

    public void showTooltip() {
        if (isCoachmarkShowing) return;
        int count = TransactionRepository.getInstance(this).getUtangTransactionCountWithDeletedRecords();
        if (btnWa.getVisibility() == View.VISIBLE) {
            if (count >= AppConfigManager.getInstance().getManualReminderPaymentLinkCount()) {
                if (!hasShownTutorialReminder && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_MANUAL_REMINDER)) {
                    hasShownTutorialReminder = true;
                    isCoachmarkShowing = true;
                    onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                            OnboardingPrefManager.TUTORIAL_MANUAL_REMINDER, reminderArea, R.drawable.onboarding_announce, getString(R.string.new_feature),
                            getString(R.string.onboarding_reminder), getString(R.string.try_feature), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                            1, 1, true, false);
                    return;
                }
            }
            if (count >= 2) {
                if (!hasShownTutorialCalendar && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_CALENDAR)) {
                    hasShownTutorialCalendar = true;
                    isCoachmarkShowing = true;
                    onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                            OnboardingPrefManager.TUTORIAL_CALENDAR, findViewById(R.id.card_view_top), R.drawable.onboarding_announce, getString(R.string.new_feature),
                            getString(R.string.onboarding_calendar), getString(R.string.try_feature), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                            1, 1, true, false);
                    return;
                }
            }
        }
        if(btnLunaskan.getVisibility() == View.VISIBLE){
            if (count >= 3) {
                if (!hasShownTutorialOneClickSettlement && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_UTANG_SETTLEMENT)) {
                    hasShownTutorialOneClickSettlement = true;
                    isCoachmarkShowing = true;
                    onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                            OnboardingPrefManager.TUTORIAL_UTANG_SETTLEMENT, findViewById(R.id.lunaskan_layout_tooltip), R.drawable.onboarding_announce, getString(R.string.new_feature),
                            getString(R.string.onboarding_single_click_settlement), "", FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                            1, 1, true, false);
                    return;
                }
            }
        }
        if (count >= 7 && !hasShownTutorialPayment && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTORIAL_PAYMENT)) {
            AppBarLayout appBarLayout = findViewById(R.id.app_bar);
            appBarLayout.setExpanded(true, false);
            hasShownTutorialPayment = true;
            isCoachmarkShowing = true;
            onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                    OnboardingPrefManager.TUTORIAL_PAYMENT, layoutPayment, R.drawable.onboarding_announce, getString(R.string.new_feature),
                    getString(R.string.onboarding_payment), getString(R.string.try_feature), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                    1, 1, true, false);
            return;
        }
    }

    @Override
    public void onOnboardingDismiss(@androidx.annotation.Nullable String id, @NotNull String body, boolean isFromButton, boolean isFromCloseButton, boolean isFromOutside) {
        isCoachmarkShowing = false;
        if (id == null)
            OnboardingPrefManager.Companion.getInstance().setHasFinishedForId(OnboardingPrefManager.TOUR_CUSTOMER_TAB_ADD_BTN);
        OnboardingPrefManager.Companion.getInstance().setHasFinishedForId(id);
        showTooltip();
    }

    @Override
    public void onOnboardingButtonClicked(@Nullable String id, boolean isFromHighlight) {
        if (id == null) return;
        switch (id) {
            case OnboardingPrefManager.TUTORIAL_MANUAL_REMINDER:
                onShareClicked(false);
                break;
            case OnboardingPrefManager.TUTORIAL_CALENDAR:
                setDueDateArea.performClick();
                break;
            case OnboardingPrefManager.TUTORIAL_PAYMENT:
                onPaymentClicked();
                break;
        }
    }

    @Override
    public void saveTransactionAfterLunaskanClick(boolean sendSmsProof) {
        saveCustomerTransaction(sendSmsProof);
    }

    @Override
    public void navigate(@NonNull Intent intent) {
        startActivity(intent);
    }
}
