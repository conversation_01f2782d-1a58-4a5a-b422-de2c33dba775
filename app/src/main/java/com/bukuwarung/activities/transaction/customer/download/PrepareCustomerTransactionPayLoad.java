package com.bukuwarung.activities.transaction.customer.download;

import android.net.Uri;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.preference.FeaturePrefManager;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskCompletionSource;

import java.util.List;

public final class PrepareCustomerTransactionPayLoad {

    final String customerId;
    public PrepareCustomerTransactionPayLoad(String cstId){
        customerId = cstId;
    }

    public static class ReportTaskResult {
        public Uri contentUri;
        public String error;

        public ReportTaskResult(Uri uri, String errorMessage) {
            this.contentUri = uri;
            this.error = errorMessage;
        }
    }

    public final Task<ReportTaskResult> getTask(List<? extends DataHolder> list) {
        TaskCompletionSource taskCompletionSource = new TaskCompletionSource();
        CustomerTransactionTaskExecutor bookReportTaskExecutor = new CustomerTransactionTaskExecutor(this, FeaturePrefManager.getInstance().getSelectedFilter(), list, taskCompletionSource,customerId);
        new Thread(bookReportTaskExecutor).start();
        return taskCompletionSource.getTask();
    }
}
