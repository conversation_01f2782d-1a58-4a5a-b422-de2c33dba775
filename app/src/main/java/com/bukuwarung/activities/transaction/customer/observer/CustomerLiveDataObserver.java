package com.bukuwarung.activities.transaction.customer.observer;

import android.view.View;

import androidx.lifecycle.Observer;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.Utility;

public final class CustomerLiveDataObserver<T> implements Observer<CustomerEntity> {

    final CustomerTransactionActivity activity;

    public CustomerLiveDataObserver(CustomerTransactionActivity customerTransactionActivity) {
        this.activity = customerTransactionActivity;
    }

    public final void onChanged(CustomerEntity customerEntity) {
        activity.customerEntity = customerEntity;
        activity.refreshToolbar(customerEntity);
        activity.effectiveBalanceTv.setText(getFormattedBalance(customerEntity));

        if (customerEntity.dueDate == null) {
            activity.paymentReminderDate.setText(activity.getString(R.string.set_due_date));
            if(InputUtils.getTransactionType(customerEntity.balance)==1){
                StringBuilder sb = new StringBuilder();
                sb.append(activity.getString(R.string.money_owes_you));
                if(SessionManager.getInstance().getAppLanguage()!=1) {
                    sb.append(" " + firstName(customerEntity.name));
                }
                activity.setDueDateArea.setVisibility(View.GONE);
                activity.btnShare.setVisibility(View.GONE);
                activity.btnWa.setVisibility(View.GONE);
                activity.reminderArea.setVisibility(View.GONE);
                activity.balanceTypeStr.setText(sb.toString());
                activity.effectiveBalanceTv.setTextColor(activity.getResources().getColor(R.color.in_green));
                activity.imageView.setImageResource(R.drawable.ic_payment_out_new);
                activity.bottomText.setText(activity.getString(R.string.pay_debt));
            }else {
                StringBuilder sb = new StringBuilder();
                sb.append(activity.getString(R.string.money_owes_you));
                if(SessionManager.getInstance().getAppLanguage()!=1) {
                    sb.append(" " + firstName(customerEntity.name));
                }
                activity.setDueDateArea.setVisibility(View.VISIBLE);
                activity.btnShare.setVisibility(View.VISIBLE);
                activity.btnWa.setVisibility(View.VISIBLE);
                activity.reminderArea.setVisibility(View.VISIBLE);
                activity.balanceTypeStr.setText(sb.toString());
                activity.effectiveBalanceTv.setTextColor(activity.getResources().getColor(R.color.out_red));
                activity.imageView.setImageResource(R.drawable.ic_payment_in_new);
                activity.bottomText.setText(activity.getString(R.string.collect_debt));
            }

        } else {

            if(InputUtils.getTransactionType(customerEntity.balance)==1){
                StringBuilder sb = new StringBuilder();
                sb.append(activity.getString(R.string.money_owed_to));
//                sb.append(" "+InputUtils.getFirstName(customerEntity.name));
                activity.btnShare.setVisibility(View.GONE);
                activity.btnWa.setVisibility(View.GONE);
                activity.balanceTypeStr.setText(sb.toString());
                activity.setDueDateArea.setVisibility(View.GONE);
                activity.effectiveBalanceTv.setTextColor(activity.getResources().getColor(R.color.in_green));
                activity.imageView.setImageResource(R.drawable.ic_payment_out_new);
                activity.bottomText.setText(activity.getString(R.string.pay_debt));
            }else{
                StringBuilder sb = new StringBuilder();
                sb.append(activity.getString(R.string.money_owes_you));
                if(SessionManager.getInstance().getAppLanguage()!=1) {
                    sb.append(" " + firstName(customerEntity.name));
                }
                activity.btnShare.setVisibility(View.VISIBLE);
                activity.btnWa.setVisibility(View.VISIBLE);
                activity.balanceTypeStr.setText(sb.toString());
                activity.setDueDateArea.setVisibility(View.VISIBLE);
                activity.effectiveBalanceTv.setTextColor(activity.getResources().getColor(R.color.out_red));
                activity.imageView.setImageResource(R.drawable.ic_payment_in_new);
                activity.bottomText.setText(activity.getString(R.string.collect_debt));
            }



            int remainingDays = DateTimeUtils.getPaymentRemainingDays(customerEntity.dueDate);

                activity.paymentReminderDate.setText(activity.getString(R.string.payment_due_date_title, Utility.getReadableDateString(customerEntity.dueDate)));
                activity.currentReminderDate = customerEntity.dueDate;
        }
        activity.showTooltip();
        if(customerEntity.balance == 0){
            activity.showLunas(true);
        }else{
            activity.showLunas(false);
        }
    }

    private String getFormattedBalance(CustomerEntity customerEntity){
        StringBuilder sb = new StringBuilder();
        sb.append(Utility.getCurrency());
        sb.append(" ");
        Double d = customerEntity.balance;
        sb.append(Utility.formatCurrency(Double.valueOf(Math.abs(d.doubleValue()))));
        return sb.toString();
    }

    private String firstName(String name){
        try {
            return name.split(" ")[0];
        }catch (Exception e){
            e.printStackTrace();
        }
        return name;
    }
}
