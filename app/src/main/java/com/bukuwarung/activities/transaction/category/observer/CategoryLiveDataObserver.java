package com.bukuwarung.activities.transaction.category.observer;

import android.view.View;

import androidx.lifecycle.Observer;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity;
import com.bukuwarung.activities.transaction.category.LoadCategoryProfitSummary;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.Utility;

public final class CategoryLiveDataObserver<T> implements Observer<CashCategoryEntity> {

    final CategoryTransactionsActivity activity;

    public CategoryLiveDataObserver(CategoryTransactionsActivity customerTransactionActivity) {
        this.activity = customerTransactionActivity;
    }

    public final void onChanged(CashCategoryEntity cashCategoryEntity) {
        if(cashCategoryEntity!=null) {
            activity.refreshToolbar(cashCategoryEntity);
            activity.effectiveBalanceTv.setText(getFormattedBalance(cashCategoryEntity));

            if (InputUtils.getTransactionType(cashCategoryEntity.balance) == 1) {
                StringBuilder sb = new StringBuilder();
                sb.append(activity.getString(R.string.sales));
                activity.profitLayout.setVisibility(View.VISIBLE);
                activity.balanceTypeStr.setText(sb.toString());
                activity.tvRowStatus.setText(activity.getText(R.string.modal_price));
                activity.tvColMoneyIn.setText(sb.toString());
                activity.tvColMoneyIn.setVisibility(View.VISIBLE);
                activity.effectiveBalanceTv.setTextColor(activity.getResources().getColor(R.color.in_green));
                new LoadCategoryProfitSummary(activity, cashCategoryEntity.cashCategoryId,activity).execute(new Void[0]);
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append(activity.getString(R.string.cash_out_summary_text));
                activity.profitLayout.setVisibility(View.GONE);
                activity.tvColMoneyIn.setVisibility(View.GONE);
                activity.balanceTypeStr.setText(sb.toString());
                activity.tvRowStatus.setText(sb.toString());
                activity.effectiveBalanceTv.setTextColor(activity.getResources().getColor(R.color.out_red));
            }
        }

    }

    private String getFormattedBalance(CashCategoryEntity cashCategoryEntity){
        StringBuilder sb = new StringBuilder();
        sb.append(Utility.getCurrency());
        sb.append(" ");
        sb.append(Utility.formatCurrency(Double.valueOf(Math.abs(cashCategoryEntity.balance))));
        return sb.toString();
    }
}
