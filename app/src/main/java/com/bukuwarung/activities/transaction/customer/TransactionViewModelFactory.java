package com.bukuwarung.activities.transaction.customer;

import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.bukuwarung.Application;

public final class TransactionViewModelFactory implements ViewModelProvider.Factory {

    private final com.bukuwarung.Application mApplication;
    private final String customerId;

    public TransactionViewModelFactory(Application application, String customerId) {
        this.mApplication = application;
        this.customerId = customerId;
    }

    public <T extends ViewModel> T create(Class<T> cls) {
        return (T) new TransactionListViewModel(this.mApplication, this.customerId);
    }
}
