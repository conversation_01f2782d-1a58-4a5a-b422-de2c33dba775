package com.bukuwarung.activities.transaction.category.adapter.dataholder;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.CashTransactionEntity;

public class CategoryTransactionDataHolder extends DataHolder {

    private final CashTransactionEntity transactionEntity;

    public CategoryTransactionDataHolder(CashTransactionEntity transactionEntity) {
        this.transactionEntity = transactionEntity;
        setTag(Tag.TRANSACTION_LIST_TRANSACTION_VIEW);
    }

    public final CashTransactionEntity getCashTransactionEntity() {
        return this.transactionEntity;
    }

    public String getName() {
        return this.transactionEntity.description;
    }
}