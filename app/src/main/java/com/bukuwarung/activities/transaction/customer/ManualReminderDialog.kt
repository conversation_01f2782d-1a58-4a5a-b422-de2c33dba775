package com.bukuwarung.activities.transaction.customer

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.view.View
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.boldText
import kotlinx.android.synthetic.main.manual_reminder_dialog.*
import java.util.*

class ManualReminderDialog(context: Context, private val action: (balanceAmount: String, createPayment: Boolean) -> Unit, private val balance: Double) : BaseDialog(context, BaseDialogType.POPUP) {

    init {
        setCancellable(true)
        setUseFullWidth(false)
    }

    override fun getResId(): Int {
        return R.layout.manual_reminder_dialog
    }

    fun setAdminFee(adminFee: Double) {
        val boldText: String
        val spannableSb: SpannableStringBuilder
        if (adminFee <= 0.0) {
            boldText = context.getString(R.string.free).uppercase(Locale.getDefault())
            spannableSb =
                SpannableStringBuilder(context.getString(R.string.create_payment_checkbox_text))

        } else {
            boldText = Utility.formatAmount(adminFee)
            spannableSb = SpannableStringBuilder(context.getString(R.string.create_payment_checkbox_pay_text, boldText))
        }
        spannableSb.boldText(boldText)
        create_payment_txt.text = spannableSb
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        utangAmount.text = Utility.formatAmount(balance)

        input_nominal.setText(Utility.formatAmount(balance))
        amount_error_message_txt.text = context.getString(R.string.minimum_amount_error_message, Utility.formatAmount(RemoteConfigUtils.getMinimumPaymentAmount()))
        input_nominal.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                if (input_nominal.getNumberValue() >= RemoteConfigUtils.getMinimumPaymentAmount()) {
                    minimumAmountErrorMessage.visibility = View.GONE
                    create_payment_checkbox.isEnabled = true
                } else {
                    minimumAmountErrorMessage.visibility = View.VISIBLE
                    create_payment_checkbox.isEnabled = false
                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }

        })

        cancel.setOnClickListener {
            dismiss()
        }

        requestMoney.setOnClickListener {
            action(input_nominal.text.toString(), create_payment_checkbox.isChecked) //need to change code and add a listener to send the edited amount
        }

    }
}
