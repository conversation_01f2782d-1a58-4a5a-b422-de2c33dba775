package com.bukuwarung.activities.transaction

import android.app.Dialog
import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.TextView
import androidx.core.text.HtmlCompat
import androidx.core.text.bold
import androidx.core.text.buildSpannedString
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.activities.expense.detail.DeleteOrUpdateSelectedTransactionRunnable
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.BottomSheetChangeTransactionCategoryBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.setSingleClickListener
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.button.MaterialButton
import java.lang.Exception
import java.util.ArrayList

class ChangeTransactionCategoryBottomSheet : BaseBottomSheetDialogFragment() {
    private var allTxnSelected: Boolean = false
    lateinit var currentCategory: String
    private var allcategories: ArrayList<Category> = ArrayList()
    private var bottomSheetChangeTransactionCategoryBinding: BottomSheetChangeTransactionCategoryBinding? = null

    private val binding get() = bottomSheetChangeTransactionCategoryBinding!!
    var selectedTransactionIds : List<String> = ArrayList()

    companion object {
        private const val ALL_CATEGORIES = "ALL_CATEGORIES"
        fun newInstance(
            allCategory: ArrayList<Category>,
            selectedTransactionIds: MutableList<String>,
            currentCategory: String,
            allTxnSelected: Boolean
        ): ChangeTransactionCategoryBottomSheet = ChangeTransactionCategoryBottomSheet().apply {
            this.selectedTransactionIds = selectedTransactionIds
            this.currentCategory = currentCategory
            this.allTxnSelected = allTxnSelected
            val bundle = Bundle()
            bundle.putParcelableArrayList(ALL_CATEGORIES, allCategory)
            arguments = bundle
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        bottomSheetChangeTransactionCategoryBinding = BottomSheetChangeTransactionCategoryBinding.inflate(inflater, container, false)

        if(arguments?.getParcelableArrayList<Category>(ALL_CATEGORIES)!=null){
            allcategories = arguments?.getParcelableArrayList<Category>(ALL_CATEGORIES)!!
        }

        with(binding) {

            if(allcategories.size > 0 && allcategories[0].categoryType==-1) binding.changeCategoryLabel.text =
                getString(R.string.select_expenditure_category)
            else binding.changeCategoryLabel.text = getString(R.string.select_income_category)

            closeDialog.setSingleClickListener {
                dialog?.dismiss()
            }

            binding.categoryRecyclerView.layoutManager = LinearLayoutManager(activity)

            binding.categoryRecyclerView.adapter = CategoryAdapter(
                allcategories
            ) {
                //TODO move seleted transactions to selected category

                val dialog = Dialog(requireActivity())
                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
                dialog.setContentView(R.layout.dialog_delete_change_transaction)

                dialog.setCancelable(true)
                val yesBtn: MaterialButton = dialog.findViewById(R.id.yes_btn)
                val noBtn: MaterialButton = dialog.findViewById(R.id.no_btn)
                val title = dialog.findViewById<TextView>(R.id.tv_title)
                val message = dialog.findViewById<TextView>(R.id.tv_message)

                title.text = getString(R.string.change_transaction_category_alert_title)

                val formattedString = buildSpannedString {
                    append("${getString(R.string.change_transaction_message_part1)} ")
                    bold { append(currentCategory) }
                    append(" ${getString(R.string.change_transaction_message_part2)} ")
                    bold { append(it) }
                }

                message.text = formattedString
                yesBtn.text = getString(R.string.move)
                yesBtn.setOnClickListener { yesBtnView: View? ->


                    val t = Thread(
                        DeleteOrUpdateSelectedTransactionRunnable(
                            requireContext(),
                            selectedTransactionIds,
                            it,
                            categoryType = allcategories.filter { cat  -> cat.categoryName.equals(it)}.get(0).categoryType,
                            currentCategory = currentCategory
                        )
                    )
                    t.start()
                    try {
                        t.join()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }

                    dialog.dismiss()

                    <EMAIL>?.dismiss()
                }

                noBtn.setOnClickListener { noBtnView: View? ->
                    dialog.dismiss()
                }
                dialog.show()

            }
        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {

            val dialog = it as BottomSheetDialog
            val bottomSheet = dialog.findViewById<View>(R.id.design_bottom_sheet)
            bottomSheet?.let { sheet ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 200
            }

        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        bottomSheetChangeTransactionCategoryBinding = null
    }
}
