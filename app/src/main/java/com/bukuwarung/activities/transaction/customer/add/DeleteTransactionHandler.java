package com.bukuwarung.activities.transaction.customer.add;

import android.app.Dialog;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.R;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.utils.NotificationUtils;

public final class DeleteTransactionHandler implements OnClickListener {
    final  Dialog deleteDlg;
    final  AddTransactionActivity activity;

    DeleteTransactionHandler(AddTransactionActivity transactionDetailsActivity, Dialog dialog) {
        this.activity = transactionDetailsActivity;
        this.deleteDlg = dialog;
    }

    public final void onClick(View view) {
        this.activity.updateTransaction(1);
        NotificationUtils.alertToast(this.activity.getString(R.string.transaction_deleted));
        this.activity.finish();
        this.deleteDlg.dismiss();
    }
}
