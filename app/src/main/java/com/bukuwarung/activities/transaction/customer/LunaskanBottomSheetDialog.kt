package com.bukuwarung.activities.transaction.customer

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics.Companion.invokeEventTracker
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.dp
import kotlinx.android.synthetic.main.lunaskan_bottom_sheet.view.*
import kotlinx.android.synthetic.main.tutorial_completion_dialog.view.closeDialog

class LunaskanBottomSheetDialog() : BaseBottomSheetDialogFragment() {

    companion object {
        fun newInstance(amount: Double,customerId:String, customerName: String, listener: LunaskanSheetListener? = null): LunaskanBottomSheetDialog {
            val dialog = LunaskanBottomSheetDialog()
            dialog.listener = listener
            val bundle = Bundle()
            bundle.putDouble("amount", amount)
            bundle.putString("customerId", customerId)
            bundle.putString("customerName", customerName)
            dialog.arguments = bundle
            return dialog
        }
    }

    private var sendSmsProof = false
    private var listener: LunaskanSheetListener? = null
    private val amount by lazy {
        arguments?.getDouble("amount") ?: 0.0
    }

    private val customerId by lazy {
        arguments?.getString("customerId") ?: ""
    }

    private val customerName by lazy {
        arguments?.getString("customerName") ?: ""
    }

    interface LunaskanSheetListener {
        fun saveTransactionAfterLunaskanClick(sendSmsProof: Boolean)
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        val view: View = inflater.inflate(R.layout.lunaskan_bottom_sheet, container, false)

        invokeEventTracker(AnalyticsConst.EVENT_SETTLE_OPEN, requireActivity())

        view.cb_sendSms.visibility = if (RemoteConfigUtils.showShowLunaskanSMS()) View.VISIBLE else View.GONE

        view.cb_sendSms.setOnCheckedChangeListener { _, isChecked ->
            val layoutParams = view.cb_sendSms.layoutParams as ConstraintLayout.LayoutParams
            sendSmsProof = if (isChecked) {
                layoutParams.marginStart = 1.dp
                view.cb_sendSms.layoutParams = layoutParams
                view.cb_sendSms.setButtonDrawable(R.drawable.ic_checkbox_checked)
                true
            } else {
                layoutParams.marginStart = 4.dp
                view.cb_sendSms.layoutParams = layoutParams
                view.cb_sendSms.setButtonDrawable(R.drawable.ic_checkbox_not_checked)
                false
            }
        }

        view.confirmationBtn.setOnClickListener{
            val propBuilder = PropBuilder()
            val showOldUTang = RemoteConfigUtils.shouldShowOldUtangForm();
            propBuilder.put(AnalyticsConst.REMAINING_UTANG, amount)
            propBuilder.put(AnalyticsConst.CUSTOMER_ID, customerId)
            propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.CUSTOMER_DETAILS)
            if (showOldUTang) {
                propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);
            } else {
                propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.UPDATED_JUNE);
            }
            propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsUtang())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_SETTLE_CONFIRM, propBuilder)
            invokeEventTracker(AnalyticsConst.EVENT_SETTLE_CONFIRM, requireActivity())

            val intent = Intent(context, LunaskanSuccessMessageActivity::class.java)
            intent.putExtra("customerName",customerName)
            intent.putExtra("amount",amount)
            intent.putExtra("customerId",customerId)
            val type = if (amount > 0) 0 else 1
            intent.putExtra("type", type)
            startActivity(intent)
            dialog?.dismiss()
            listener?.saveTransactionAfterLunaskanClick(sendSmsProof)
        }
        view.closeDialog.setOnClickListener {
            dialog?.dismiss()
        }
        view.balance.text = Utility.formatAmount(amount)
        if(amount>0){
            view.txt_main_title.setText(R.string.giving_label)
            view.balance.setTextColor(getResources().getColor(R.color.out_red))
        }else{
            view.txt_main_title.setText(R.string.receiving_label)
            view.balance.setTextColor(getResources().getColor(R.color.in_green))
        }

        return view
    }
}