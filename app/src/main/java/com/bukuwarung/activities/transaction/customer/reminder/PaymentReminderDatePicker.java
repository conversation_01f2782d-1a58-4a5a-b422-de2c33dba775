package com.bukuwarung.activities.transaction.customer.reminder;

import android.app.DatePickerDialog.OnDateSetListener;
import android.widget.DatePicker;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.Utility;

import java.util.Calendar;

public class PaymentReminderDatePicker implements OnDateSetListener {
    final CustomerTransactionActivity activity;

    public PaymentReminderDatePicker(CustomerTransactionActivity transactionsActivity) {
        this.activity = transactionsActivity;
    }

    public final void onDateSet(DatePicker datePicker, int year, int month, int date) {
        Calendar instance = Calendar.getInstance();
        instance.set(year, month, date);

        String storableDateString = Utility.getStorableDateString(instance.getTime());
        activity.paymentReminderDate.setText(activity.getString(R.string.payment_due_date_title, Utility.getReadableDateString(storableDateString)));

        if (Utility.areEqual(activity.paymentReminderDate.getText().toString(), activity.getString(R.string.set_due_date))) {
            activity.setReminderDate(null);
            AppAnalytics.trackEvent("collecting_delete_collecting_date");
            AppAnalytics.trackEvent("customer_detail_reminder_unset");
        } else {
            activity.setReminderDate(storableDateString);
            AppAnalytics.trackEvent("customer_detail_reminder_set");
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("date", storableDateString);
            propBuilder.put("entryPoint", "customer");
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_COLLECTING_DATE_SET_NEW_COLLECTION, propBuilder);
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_COLLECTING_DATE_SET_NEW_COLLECTION, activity);

        }
        activity.reminderDatePicker =new DatePicker(activity);
        activity.reminderDatePicker.init(year, month, date, null);
        try {
            if (Utility.isBlank(activity.customerEntity.phone)) {
                NotificationUtils.alertToast(activity.getString(R.string.reminder_without_phone));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
