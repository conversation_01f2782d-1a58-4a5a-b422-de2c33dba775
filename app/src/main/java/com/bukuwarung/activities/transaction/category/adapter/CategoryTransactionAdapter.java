package com.bukuwarung.activities.transaction.category.adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity;
import com.bukuwarung.activities.transaction.category.adapter.dataholder.CategoryTransactionDataHolder;
import com.bukuwarung.activities.transaction.category.adapter.viewholder.TransactionEmptyViewHolder;
import com.bukuwarung.activities.transaction.category.adapter.viewholder.TransactionItemViewHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.ArrayList;
import java.util.List;

public final class CategoryTransactionAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context context;
    private OnClickListener mOnClickListener = new TransactionItemClickHandler(this);
    public final RecyclerView transactionRecyclerView;

    public List<? extends DataHolder> transactionDataHolderList;

    public final Context getContext() {
        return this.context;
    }

    public CategoryTransactionAdapter(List<? extends DataHolder> transactionList, RecyclerView recyclerView, Context context) {
        this.transactionDataHolderList = transactionList;
        this.transactionRecyclerView = recyclerView;
        this.context = context;
    }

    private final void bindTransactionViewHolder(TransactionItemViewHolder transactionViewHolder, CategoryTransactionDataHolder transactionDataHolder, int i) {
        CashTransactionEntity cashTransactionEntity = (CashTransactionEntity) transactionDataHolder.getCashTransactionEntity();
        ComponentUtil.setVisible(transactionViewHolder.getHeader(),false); /*i==0*/
        transactionViewHolder.getDate().setText(DateTimeUtils.formatTransactionDate(cashTransactionEntity.date));
        if (transactionDataHolder.isChecked) {
            transactionViewHolder.getSelector().setChecked(true);
        } else {
            transactionViewHolder.getSelector().setChecked(false);
        }
        if (Utility.isBlank(cashTransactionEntity.description)) {
            transactionViewHolder.getNote().setVisibility(View.GONE);
        } else {
            transactionViewHolder.getNote().setVisibility(View.VISIBLE);
            transactionViewHolder.getNote().setText(cashTransactionEntity.description);
        }
        if (TransactionUtil.isExpenseTrans(cashTransactionEntity)) {
            transactionViewHolder.getAmount().setText(Utility.formatAmount(Math.abs(cashTransactionEntity.amount)));
            transactionViewHolder.getAmount().setTextColor(getContext().getResources().getColor(R.color.out_red));
            transactionViewHolder.getAmountHeader().setText(getContext().getString(R.string.expense_label));
            transactionViewHolder.getAmountHeader().setTextColor(getContext().getResources().getColor(R.color.out_red));

            transactionViewHolder.getAmount().setTextAlignment(View.TEXT_ALIGNMENT_TEXT_END);
            transactionViewHolder.getBuyingAmountHeader().setVisibility(View.GONE);
            transactionViewHolder.getBuyingAmount().setVisibility(View.GONE);
            return;
        }else{
            transactionViewHolder.getAmount().setText(Utility.formatAmount(Math.abs(cashTransactionEntity.amount)));
            transactionViewHolder.getAmount().setTextColor(getContext().getResources().getColor(R.color.in_green));
            transactionViewHolder.getAmountHeader().setText(getContext().getString(R.string.income_label));
            transactionViewHolder.getAmountHeader().setTextColor(getContext().getResources().getColor(R.color.in_green));

            transactionViewHolder.getBuyingAmount().setText(Utility.formatAmount(Math.abs(cashTransactionEntity.buyingPrice)));


            return;
        }
    }

    public final void setDataHolderList(List<? extends DataHolder> dataHolderList) {
        if (dataHolderList == null) {
            this.transactionDataHolderList = new ArrayList();
        } else {
            this.transactionDataHolderList = dataHolderList;
        }
        notifyDataSetChanged();
    }

    public int getItemViewType(int i) {
        return ((DataHolder) this.transactionDataHolderList.get(i)).getTag();
    }

    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int tag) {
        if (tag == Tag.TRANSACTION_LIST_TRANSACTION_VIEW) {
            View transactionView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.category_trans_view_data_item, viewGroup, false);
            transactionView.setOnClickListener(this.mOnClickListener);

            ((CheckBox)transactionView.findViewById(R.id.selectorSingle)).setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                    if(!isChecked)((CategoryTransactionsActivity)context).resetBulkSelector();

                    int childAdapterPosition = transactionRecyclerView.getChildAdapterPosition(transactionView);
                    try {
                        DataHolder dataHolder = transactionDataHolderList.get(childAdapterPosition);
                        if (dataHolder instanceof CategoryTransactionDataHolder) {
//                            CashTransactionEntity entity = ((CategoryTransactionDataHolder) dataHolder).getCashTransactionEntity();
                            if(isChecked) dataHolder.isChecked = true;
                            else dataHolder.isChecked = false;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        FirebaseCrashlytics.getInstance().recordException(e);
                    }

                }
            });

            return new TransactionItemViewHolder(this, transactionView);
        } else if (tag == Tag.TRANSACTION_LIST_EMPTY_VIEW) {
            View tutorialView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.transaction_view_empty, viewGroup, false);
            return new TransactionEmptyViewHolder(this, tutorialView);
        } else {
            View tutorialView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.transaction_view_empty, viewGroup, false);
            return new TransactionEmptyViewHolder(this, tutorialView);
        }
    }

    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        DataHolder dataHolder = this.transactionDataHolderList.get(i);
        int itemViewType = viewHolder.getItemViewType();
        if (itemViewType == Tag.TRANSACTION_LIST_TRANSACTION_VIEW) {
            TransactionItemViewHolder transactionViewHolder = (TransactionItemViewHolder) viewHolder;
            if (dataHolder != null) {
                bindTransactionViewHolder(transactionViewHolder, (CategoryTransactionDataHolder) dataHolder, i);
                return;
            }
        }
    }

    public int getItemCount() {
        if (this.transactionDataHolderList == null) {
            return 0;
        }
        return this.transactionDataHolderList.size();
    }

    public void bulkSelectUnselectItems(boolean isSelectOrUnselect) {
        for (DataHolder holder : transactionDataHolderList) {
            holder.isChecked = isSelectOrUnselect;
        }
        notifyDataSetChanged();
    }
}
