package com.bukuwarung.activities.transaction.customer.add;

import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;

final class UpdateExistingTransactionThread implements Runnable {
    final  int deleteFlg;
    final  AddTransactionActivity activity;

    UpdateExistingTransactionThread(AddTransactionActivity activity, int deleteFlg) {
        this.activity = activity;
        this.deleteFlg = deleteFlg;
    }

    public final void run() {
        String amountStr = activity.keyboardView.getInputAmount();
        TransactionEntity transaction = activity.transaction;
        double amountDbl = !Utility.isBlank(amountStr) ? Double.parseDouble(amountStr) : 0.0d;
        if (activity.TRANSACTION_TYPE == activity.DEBIT) {
            double d = (double) 0;
            Double.isNaN(d);
            amountDbl = d - amountDbl;
        }

        String actionBy = AnalyticsConst.ACCOUNTING;
        if (transaction.transactionType == TransactionEntityType.PAYMENT) {
            actionBy = AnalyticsConst.PAYMENTS;
        }

        if (this.deleteFlg == 0) {
            String userId = User.getUserId();
            String deviceId = User.getDeviceId();
            AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
            prop.put(AnalyticsConst.UPDATE_TYPE, AnalyticsConst.EDIT_NEW);
            prop.put(AnalyticsConst.ACTION_BY, actionBy);
            prop.put(AnalyticsConst.TRANSACTION_ID, transaction.transactionId);
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_TRANSACTION_UPDATE);
            TransactionRepository.getInstance(activity).updateExistingTransaction(userId, deviceId, transaction.transactionId, transaction.customerId, amountDbl, activity.transactionDate, activity.mTransactionInputDescription.getText().toString(), this.deleteFlg);
        } else {
            String userId2 = User.getUserId();
            String deviceId2 = User.getDeviceId();
            AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
            prop.put(AnalyticsConst.UPDATE_TYPE, AnalyticsConst.DELETE);
            prop.put(AnalyticsConst.ACTION_BY, actionBy);
            prop.put(AnalyticsConst.TRANSACTION_ID, transaction.transactionId);
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_TRANSACTION_UPDATE);
//            AppAnalytics.trackEvent("customer_transaction_delete");
            TransactionRepository.getInstance(activity).updateExistingTransaction(userId2, deviceId2, transaction.transactionId, transaction.customerId, amountDbl, transaction.date, transaction.description, deleteFlg);
        }
    }
}
