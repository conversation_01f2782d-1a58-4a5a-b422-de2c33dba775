package com.bukuwarung.activities.transaction.category;

import android.content.Context;
import android.os.AsyncTask;

import com.bukuwarung.Application;
import com.bukuwarung.database.dto.CategorySummaryModel;
import com.bukuwarung.database.repository.TransactionRepository;

public final class LoadCategoryProfitSummary extends AsyncTask<Void, Void, CategorySummaryModel> {

    private final Context mContext;
    final CategoryTransactionsActivity parent;
    final String categoryId;

    public LoadCategoryProfitSummary(CategoryTransactionsActivity activity,String categoryId, Context context) {
        this.parent = activity;
        this.mContext = context;
        this.categoryId = categoryId;
    }

    public CategorySummaryModel doInBackground(Void... voidArr) {
        CategorySummaryModel summaryData = TransactionRepository.getInstance(Application.getAppContext()).getCategorySummary(categoryId);
        return summaryData;
    }


    public void onPostExecute(CategorySummaryModel summaryData) {
        this.parent.displayIncomeSummary(summaryData);
    }
}
