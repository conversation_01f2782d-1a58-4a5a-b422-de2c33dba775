package com.bukuwarung.activities.transaction.category;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.activities.expense.NewCashTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;

final class AddTransactionBtnClickListener implements OnClickListener {
    final CategoryTransactionsActivity cashTransactionsActivity;
    final int type;

    AddTransactionBtnClickListener(CategoryTransactionsActivity cashTransactionsActivity, int type) {
        this.cashTransactionsActivity = cashTransactionsActivity;
        this.type = type;
    }

    public final void onClick(View view) {
        Intent intent = NewCashTransactionActivity.createIntent(this.cashTransactionsActivity);
        Bundle bundle = new Bundle();
        bundle.putInt("TRANSACTION_TYPE", type);
        bundle.putString(CategoryTransactionsActivity.TRANS_TYPE_EXTRA, String.valueOf( this.cashTransactionsActivity.categoryEntity.type));
        bundle.putString(CategoryTransactionsActivity.CASH_CATEGORY_ID,  this.cashTransactionsActivity.categoryEntity.cashCategoryId);
        intent.putExtras(bundle);
        AppAnalytics.trackEvent("tap_new_cash_category_button");
        cashTransactionsActivity.startActivity(intent);
    }
}
