package com.bukuwarung.activities.transaction

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.AppCompatRadioButton
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.database.entity.MeasurementEntity

class CategoryAdapter(private val categoryList: ArrayList<Category>, val click: (id: String) -> Unit) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return CategoryViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.category_item, null), click)
    }


    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            return (holder as CategoryViewHolder).bind(categoryList[position])
    }

    override fun getItemCount(): Int {
        return categoryList.size
    }




    class CategoryViewHolder(val view: View, val click: (id: String) -> Unit) : RecyclerView.ViewHolder(view) {
        @SuppressLint("SetTextI18n")
        fun bind(category : Category) {

            view.setOnClickListener {
                click(category.categoryName)
            }

            view.findViewById<TextView>(R.id.categoryName).setText(category.categoryName)

        }
    }

//    fun updateData(newcategoryList: List<MeasurementEntity>) {
//        categoryList.clear()
//        categoryList.addAll(newcategoryList)
//        notifyDataSetChanged()
//
//    }

}