package com.bukuwarung.activities.transaction.category;

import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.bukuwarung.Application;

public final class TransactionViewModelFactory implements ViewModelProvider.Factory {

    private final Application mApplication;
    private final String categoryId;
    private final String brickInstitutionId;
    private final String startDate;
    private final String endDate;
    private final boolean isBrickInstitution;

    public TransactionViewModelFactory(Application application, String categoryId,String brickInstitutionId, Boolean isBrickInstitution,String startDate,String endDate) {
        this.mApplication = application;
        this.isBrickInstitution = isBrickInstitution;
        this.categoryId = categoryId;
        this.brickInstitutionId = brickInstitutionId;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public <T extends ViewModel> T create(Class<T> cls) {
        return (T) new TransactionListViewModel(this.mApplication, this.categoryId,this.brickInstitutionId,startDate,endDate,this.isBrickInstitution);
    }
}
