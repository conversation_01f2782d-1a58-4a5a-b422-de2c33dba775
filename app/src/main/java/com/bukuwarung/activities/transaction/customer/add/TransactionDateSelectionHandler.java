package com.bukuwarung.activities.transaction.customer.add;

import android.app.DatePickerDialog.OnDateSetListener;
import android.widget.DatePicker;

import com.bukuwarung.utils.Utility;

import java.util.Calendar;

final class TransactionDateSelectionHandler implements OnDateSetListener {
    final  AddTransactionActivity addTransactionActivity;

    TransactionDateSelectionHandler(AddTransactionActivity addTransactionActivity) {
        this.addTransactionActivity = addTransactionActivity;
    }

    public final void onDateSet(DatePicker datePicker, int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month, day);
        String storableDateString = Utility.getStorableDateString(calendar.getTime());
        addTransactionActivity.transactionDate = storableDateString;
        addTransactionActivity.mTransactionInputDate.setText(Utility.getReadableDateString(storableDateString));
    }
}
