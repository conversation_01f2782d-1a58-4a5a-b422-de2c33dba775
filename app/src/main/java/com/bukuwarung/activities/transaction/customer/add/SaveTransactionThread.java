package com.bukuwarung.activities.transaction.customer.add;

import com.bukuwarung.Application;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.repository.ReferralRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;

final class SaveTransactionThread implements Runnable {
    final  AddTransactionActivity addTransactionActivity;

    SaveTransactionThread(AddTransactionActivity addTransactionActivity) {
        this.addTransactionActivity = addTransactionActivity;
    }

    public final void run() {
        String amountStr = addTransactionActivity.keyboardView.getInputAmount();

        double amountDouble = !Utility.isBlank(amountStr) ? Double.parseDouble(amountStr) : 0.0d;
        if (this.addTransactionActivity.TRANSACTION_TYPE == AddTransactionActivity.DEBIT) {
            double d = (double) 0;
            Double.isNaN(d);
            amountDouble = d - amountDouble;
        }
        String cstId = addTransactionActivity.getCustomerId();
        String txnId = TransactionRepository.getInstance(Application.getAppContext()).saveNewTransaction(User.getBusinessId(), cstId, amountDouble, addTransactionActivity.transactionDate, addTransactionActivity.mTransactionInputDescription.getText().toString(),1);
        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        propBuilder.put("transaction_type", this.addTransactionActivity.TRANSACTION_TYPE == this.addTransactionActivity.CREDIT ? "CREDIT" : "DEBIT");
        propBuilder.put("transaction_amount", String.valueOf(amountDouble));
        propBuilder.put("cst_trans_seq", SessionManager.getInstance().getTransSeq());
        propBuilder.put("cst_seq", SessionManager.getInstance().getCstSeq());
        propBuilder.put("transaction_id", txnId);
        propBuilder.put("form", RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm()?"old_utang_form":"new_utang_form");
        AppAnalytics.trackEvent("customer_detail_tap_saved", propBuilder);

        if (AppConfigManager.getInstance().useReferral()
                && addTransactionActivity.getCurrentBook() != null && Math.abs(amountDouble)>AppConfigManager.getInstance().getReferralTxnVal()) {
            ReferralRepository.getInstance().addTransactionPoints(
                addTransactionActivity.getCurrentBook(), false
            );
        }
    }


}
