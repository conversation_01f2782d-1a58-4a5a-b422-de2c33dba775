package com.bukuwarung.activities.transaction.category.adapter.viewholder;

import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.category.adapter.CategoryTransactionAdapter;


public final class TransactionItemViewHolder extends RecyclerView.ViewHolder {

    final CategoryTransactionAdapter adapter;

    private TextView amount;
    private TextView date;
    private TextView amountHeader;
    private TextView buyingAmount;
    private TextView buyingAmountHeader;
    private LinearLayout header;
    private TextView note;
    private CheckBox selector;

    public TransactionItemViewHolder(CategoryTransactionAdapter categoryTransactionAdapter, View view) {
        super(view);
        this.adapter = categoryTransactionAdapter;
        this.header = view.findViewById(R.id.listHeader);
        this.note =  view.findViewById(R.id.note);
        this.amount = view.findViewById(R.id.amount);
        this.amountHeader = view.findViewById(R.id.amountHeader);
        this.buyingAmount = view.findViewById(R.id.buying_price);
        this.buyingAmountHeader = view.findViewById(R.id.buyingPriceHeader);
        this.selector = view.findViewById(R.id.selectorSingle);

        this.date = view.findViewById(R.id.date);
    }

    public final LinearLayout getHeader() {
        return this.header;
    }

    public final TextView getNote() {
        return this.note;
    }

    public final TextView getAmount() {
        return this.amount;
    }

    public final TextView getAmountHeader() {
        return this.amountHeader;
    }

    public final TextView getDate() {
        return this.date;
    }

    public TextView getBuyingAmount() {
        return buyingAmount;
    }

    public TextView getBuyingAmountHeader() {
        return buyingAmountHeader;
    }

    public CheckBox getSelector() {
        return selector;
    }

    public void setSelector(CheckBox selector) {
        this.selector = selector;
    }
}