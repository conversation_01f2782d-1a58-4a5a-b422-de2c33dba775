package com.bukuwarung.activities.transaction.customer

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.widget.CheckBox
import androidx.viewbinding.ViewBinding
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseBottomSheetDialog
import kotlinx.android.synthetic.main.customer_type_dialog.*

class CustomerTypeBottomSheet(context: Context, private val currentSelectedType: List<Int> = listOf(), private val onDialogClose: (List<Int>) -> Unit) : BaseBottomSheetDialog(context) {
    private val selectedType = mutableListOf<Int>()

    init {
        setUseFullWidth(true)
        setCancellable(true)
    }

    override fun getResId(): Int = R.layout.customer_type_dialog
    override fun getBinding(): ViewBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupView()
    }

    private fun setupView() {
        selectedType.addAll(currentSelectedType)
        close_dialog.setOnClickListener {
            onDialogClose(selectedType)
            dismiss()
        }
        setupCb(cb_reseller, RESELLER)
        setupCb(cb_dropshipper, DROPSHIPPER)
        setupCb(cb_personal, PERSONAL)

    }

    private fun setupCb(checkBox: CheckBox, type: Int) {
        checkBox.apply {
            isChecked = currentSelectedType.contains(type)
            setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    selectedType.add(type)
                } else {
                    selectedType.remove(type)
                }
            }
        }
    }

    override fun dismiss() {
        super.dismiss()
        onDialogClose(selectedType)
    }

    companion object {
        const val RESELLER = 1
        const val DROPSHIPPER = 2
        const val PERSONAL = 3
    }

}