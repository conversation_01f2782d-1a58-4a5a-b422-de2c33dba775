package com.bukuwarung.activities.transaction.customer

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.CompoundButton
import android.widget.EditText
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.NewVerifyOtpActivity
import com.bukuwarung.activities.referral.payment_referral.ReferralActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog.BusinessSelectedListener
import com.bukuwarung.dialogs.businessselector.BusinessType
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.RemoteConfigUtils.OnBoarding.shouldShowFullOnBoardingQuestion
import kotlinx.android.synthetic.main.profile_completion_dialog.*
import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEvent
import net.yslibrary.android.keyboardvisibilityevent.util.UIUtil

class ProfileCompletionDialog(
        private val activity: AppCompatActivity,
        context: Context,
        private val action: (String) -> Unit,
        private val eventId: String,
        private val isBusinessTypeShown: Boolean,
        private val isSkipButtonShown: Boolean = true,
        private val handleValidation: Boolean = false,
        val customerId: String = "",
        val useParentBusinessHandler: Boolean = false
) : BaseDialog(context, BaseDialogType.POPUP), BusinessSelectedListener {

    var bookEntity = BusinessRepository.getInstance(context).getBusinessByIdSync(User.getBusinessId())
    var dialog: BusinessSelectorDialog? = null
    private val customerType = mutableListOf<Int>()
    private val shouldShowFUllQuestion = RemoteConfigUtils.OnBoarding.shouldShowFullOnBoardingQuestion()

    private val checkBoxCheckedListener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
        if (KeyboardVisibilityEvent.isKeyboardVisible(activity)){
            UIUtil.hideKeyboard(activity)
        }
        if (isChecked) {
            updateSellingMethodValidation(false)
        }
    }

    init {
        setCancellable(false)
        setUseFullWidth(false)
    }

    override fun getResId(): Int {
        return R.layout.profile_completion_dialog
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (!isBusinessTypeShown) {
            bizLayout.visibility = View.GONE
            businessTypeError.visibility = View.GONE
        }

        if(useParentBusinessHandler){
            bookEntity = BusinessRepository.getInstance(activity).createBusiness(User.getUserId(), User.getDeviceId(), "BukuWarung", "Usaha Saya", -1, "")
        }

        val refCode = ReferralPrefManager.getInstance().temporaryReferralCode
        val businessTypes = AppConfigManager.getInstance().businessTypes
        var selectedBusiness: BusinessType? = BusinessType(bookEntity.bookType, bookEntity.bookTypeName)
        checkForErrorMessage()
//        businessName.setText(bookEntity.businessName)
        businessName.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE){
                businessName.clearFocus()
                Utility.hideKeyboard(activity)
            }
            true
        }

        if (bookEntity != null && businessTypes.contains(selectedBusiness)) {
            val index: Int = businessTypes.indexOf(selectedBusiness)
            selectedBusiness = businessTypes.get(index)
            businessTypeET.setText(selectedBusiness.getName())
        } else if (bookEntity != null) {
            businessTypeET.setText(bookEntity.bookTypeName)
        }

        if (isBusinessTypeShown) {
            iv_top_pic.visibility = View.VISIBLE
            cl_info.visibility = View.VISIBLE
        } else {
            iv_top_pic.visibility = View.GONE
            cl_info.visibility = View.GONE
        }

        businessTypeET.setOnClickListener { showBusinessTypeDlg(businessTypes, bookEntity.bookType); }

        customerTypeEt.setOnClickListener { showCustomerTypeBottomSheet() }
        if (!isSkipButtonShown) {
            buttonSkip.visibility = View.GONE
        }
        buttonSkip.setOnClickListener {
            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.ID, eventId)
            if (customerId != "")
                propBuilder.put(AnalyticsConst.CUSTOMER_ID, customerId)
            dismissDialogAndShowShareOptions()
        }

        businessName.doOnTextChanged { text, _, _, _ ->
            if (text?.isNotEmpty().isTrue){
                businessNameLayout.error = ""
            }
        }

        businessTypeET.doOnTextChanged { text, _, _, _ ->
            if (text?.isNotEmpty().isTrue){
                bizLayout.error = ""
            }
        }

        customerTypeEt.doOnTextChanged { text, _, _, _ ->
            if (text?.isNotEmpty().isTrue) {
                customerLayout.error = ""
            }
        }

        cb_selling_online.setOnCheckedChangeListener(checkBoxCheckedListener)
        cb_selling_offline.setOnCheckedChangeListener(checkBoxCheckedListener)

        buttonSubmit.setOnClickListener {
            val bizName = businessName.text.toString().replace("\\s", "")
            val formValidity = mutableListOf<Boolean>()

            val bizNameMissing = !Utility.hasBusinessName(bizName)
            businessNameLayout.setErrorIf(!Utility.hasBusinessName(bizName), context.getString(R.string.biz_name_missing))
            formValidity.add(bizNameMissing)

            val isCategoryMissing = isBusinessTypeShown && (bookEntity.bookType == null || Utility.isBlank(businessTypeET.text.toString()))
            bizLayout.setErrorIf(isCategoryMissing, context.getString(R.string.biz_category_missing))
            formValidity.add(isCategoryMissing)

            val isCustomerTypeMissing = shouldShowFUllQuestion && customerType.isEmpty()
            customerLayout.setErrorIf(isCustomerTypeMissing, context.getString(R.string.biz_customer_type_missing))
            formValidity.add(isCustomerTypeMissing)

            val isSellingMethodMissing = shouldShowFUllQuestion && !cb_selling_online.isChecked && !cb_selling_offline.isChecked
            formValidity.add(isSellingMethodMissing)
            updateSellingMethodValidation(isSellingMethodMissing)

            if (formValidity.any { it }) return@setOnClickListener

            val sellingMethodForEvent = mutableListOf<String>().apply {
                if (cb_selling_online.isChecked) add(AnalyticsConst.ONLINE)
                if (cb_selling_offline.isChecked) add(AnalyticsConst.OFFLINE)
            }

            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.BUSINESS_NAME, bizName)
            AppAnalytics.setUserProperty(AnalyticsConst.BUSINESS_NAME, bizName)
            if (customerId != "") {
                propBuilder.put(AnalyticsConst.CUSTOMER_ID, customerId)
            }
            if (!Utility.isBlank(eventId) && eventId.equals(AnalyticsConst.LANDING_POPUP)) {
                propBuilder.put(AnalyticsConst.TYPE, bookEntity.bookType.toString())
                if (shouldShowFUllQuestion) {
                    propBuilder.put(AnalyticsConst.MERCHANT_CUSTOMER_TYPE, customerTypeEt.text.toString().toLowerCase())
                    propBuilder.put(AnalyticsConst.SELLING_METHOD, sellingMethodForEvent.joinToString())
                }

                if (!refCode.isNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.REFERRAL_CODE, refCode)
                    propBuilder.put("referral_input_method", "deeplink")
                    propBuilder.put("entered_referral_code", refCode)
                }

                AppAnalytics.trackEvent(AnalyticsConst.LANDING_POPUP, propBuilder)
            } else {
            }
            if(!useParentBusinessHandler) {
                BusinessRepository.getInstance(context).updateBusinessProfile(User.getUserId(), User.getDeviceId(), User.getBusinessId(), bizName, bookEntity.bookType, bookEntity.bookTypeName, bookEntity.businessOwnerName)
            }else{
                BusinessRepository.getInstance(context).createBusiness(User.getUserId(), User.getDeviceId(), bizName ?: "BukuWarung", bizName ?: "Usaha Saya", bookEntity.bookType, bookEntity.bookTypeName)
            }
            dismissDialogAndShowShareOptions()
            if (isBusinessTypeShown) {
                AppAnalytics.setUserProperty("business_type", bookEntity.bookType.toString())
                AppAnalytics.setUserProperty("business_type_name", bookEntity.bookTypeName)

                if (shouldShowFUllQuestion) {
                    AppAnalytics.setUserProperty(AnalyticsConst.MERCHANT_CUSTOMER_TYPE, customerTypeEt.text.toString().toLowerCase())
                    AppAnalytics.setUserProperty(AnalyticsConst.SELLING_METHOD, sellingMethodForEvent.joinToString())
                }
            }

            // TODO submit the referral code
        }

        val referralCode = ReferralPrefManager.getInstance().referralCode
        Log.d("DeeplinkManager", referralCode.value + "receivedVal")
        referralCode.observe(activity, Observer {
            Log.d("DeeplinkManager", "visibility on" + it)
            til_referral.visibility = it.isNotEmpty().asVisibility()
            tie_referral.setText(it)
        })


        full_question_group.visibility = shouldShowFUllQuestion.asVisibility()
        KeyboardEventListener(activity) { isOpen ->
            if (!isOpen) {
                businessName.clearFocus()
                businessNameLayout.clearFocus()
            }
        }

        trackOnOpenEvent()
    }


    private fun trackOnOpenEvent(){
        val pageType =  if (shouldShowFullOnBoardingQuestion()) {
            AnalyticsConst.FOUR_QUESTIONS
        } else {
            AnalyticsConst.TWO_QUESTIONS
        }
        val prop = PropBuilder().put(AnalyticsConst.PAGE_TYPE, pageType)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_BUSINESS_DETAIL_DIALOG, prop)
    }

    private fun updateSellingMethodValidation(isInvalid: Boolean) {
        val color  = if (isInvalid)  ContextCompat.getColor(context, R.color.red_80) else ContextCompat.getColor(context, R.color.black_80)
        val helperText = if (isInvalid) context.getString(R.string.biz_selling_method_missing) else context.getString(R.string.mutliple_choice_allowed)

        tv_selling_method.setTextColor(color)
        tv_selling_method_helper.setTextColor(color)
        tv_selling_method_helper.text = helperText
    }

    private fun showCustomerTypeBottomSheet() {
        CustomerTypeBottomSheet(activity, customerType) { type ->
            customerType.clear()
            customerType.addAll(type)

            customerType.sortBy { it }
            val customerTypeStr = customerType.map {
                when (it) {
                    CustomerTypeBottomSheet.RESELLER -> context.getString(R.string.reseller)
                    CustomerTypeBottomSheet.DROPSHIPPER -> context.getString(R.string.dropshipper)
                    else -> context.getString(R.string.personal_use)
                }
            }

            customerTypeEt.setText(customerTypeStr.joinToString())
        }.show()
    }

    public fun getBusinessDetails(): BookEntity {
        return bookEntity
    }

    private fun dismissDialogAndShowShareOptions() {
        InputUtils.hideKeyboard(context)
        
        val referralCode = tie_referral.text.toString()
        if (referralCode.isNotNullOrEmpty()) {
            ReferralPrefManager.getInstance().temporaryReferralCode = referralCode
            dismiss()
            ReferralActivity.refCode = referralCode
//            activity.startActivity(Intent(activity, ReferralActivity::class.java))
            return
        }
        dismiss()
        if(useParentBusinessHandler && this.activity is NewVerifyOtpActivity){
            SetupManager.getInstance().hasRestored(true)
        }else {
            action(businessName.text.toString().replace("\\s", ""))
        }
    }

    private fun checkForErrorMessage() {
        if (isBusinessTypeShown && bookEntity.bookType == -1 && handleValidation) {
            businessTypeError.visibility = View.VISIBLE
        } else {
            businessTypeError.visibility = View.GONE
        }
        val bizName = businessName.text.toString()
        if (!Utility.hasBusinessName(bizName) && handleValidation) {
            ownerNameError.visibility = View.VISIBLE
        } else {
            ownerNameError.visibility = View.GONE
        }
    }

    private fun showBusinessTypeDlg(businessTypes: List<BusinessType?>?,
                                    bookType: Int) {
        dialog = BusinessSelectorDialog.getInstance(
                activity,
                businessTypes,
                this
        )
        if (bookType != 1) dialog?.selectedId = bookType
        dialog?.show()
    }

    override fun onBusinessSelected(datum: SelectableObject?) {
        bookEntity.bookType = datum!!.id
        bookEntity.bookTypeName = datum.name
        businessTypeET.setText(datum.name)
        dialog?.dismiss()
        checkForErrorMessage()
    }

    override fun onNewBusinessAdded(datum: SelectableObject?) {
        bookEntity.bookType = datum!!.id
        bookEntity.bookTypeName = datum.name
        businessTypeET.setText(datum.name)
        if (dialog != null) dialog!!.dismiss()
        checkForErrorMessage()
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN){
            val view = currentFocus ?: return super.dispatchTouchEvent(ev)
            if (view is EditText){
                val outRect = Rect()
                view.getGlobalVisibleRect(outRect)
                if (!outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())){
                    view.clearFocus()
                    Utility.hideKeyboard(activity)
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }
}