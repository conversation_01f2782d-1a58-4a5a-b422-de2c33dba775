package com.bukuwarung.activities.transaction.customer.adapter.viewholder;

import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.customer.adapter.TransactionAdapter;
import com.bukuwarung.session.SessionManager;

public final class TransactionEmptyViewHolder extends RecyclerView.ViewHolder {

    TextView emptyScreenTv;
    public TransactionEmptyViewHolder(TransactionAdapter transactionAdapter, View view) {
        super(view);
        this.emptyScreenTv = view.findViewById(R.id.empty_trans_text);
        if(SessionManager.getInstance().getAppLanguage() == 1){
            this.emptyScreenTv.setText("Your data is secured with BukuWarung, only you and "+ transactionAdapter.activity.getCustomerName() +" can view these transactions.");
        }else{
            this.emptyScreenTv.setText("Data kamu tersimpan dengan aman. Cuma kamu dan " + transactionAdapter.activity.getCustomerName() + " yang bisa melihat transaksi ini.");
        }
    }
}