package com.bukuwarung.activities.transaction.customer.adapter.viewholder;

import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.customer.adapter.TransactionAdapter;


public final class TransactionHeaderItemViewHolder extends RecyclerView.ViewHolder {

    final TransactionAdapter adapter;

    private TextView dateHeader;
    private TextView creditHeader;
    private TextView debitHeader;

    public TransactionHeaderItemViewHolder(TransactionAdapter transactionAdapter, View view) {
        super(view);
        this.adapter = transactionAdapter;
        this.creditHeader = view.findViewById(R.id.creditHeader);
        this.debitHeader =  view.findViewById(R.id.debitHeader);
        this.dateHeader = view.findViewById(R.id.dateHeader);
    }

}