package com.bukuwarung.activities.transaction.customer.adapter.dataholder;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.TransactionEntity;

public class TransactionDataHolder extends DataHolder {

    private final TransactionEntity transactionEntity;

    public TransactionDataHolder(TransactionEntity transactionEntity) {
        this.transactionEntity = transactionEntity;
        setTag(Tag.TRANSACTION_LIST_TRANSACTION_VIEW);
    }

    public final TransactionEntity getTransactionEntity() {
        return this.transactionEntity;
    }

    public String getName() {
        return this.transactionEntity.description;
    }
}