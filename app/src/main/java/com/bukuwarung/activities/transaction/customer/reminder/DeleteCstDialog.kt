package com.bukuwarung.activities.transaction.customer.reminder

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BasePromptDialog

class DeleteCstDialog(
        context: Context,
        onPromptClicked: ((Boolean) -> Unit)
): BasePromptDialog(context, onPromptClicked) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setTitle(context.resources.getString(R.string.delete_cst))
        setContent(context.resources.getString(R.string.delete_cst_msg))
    }

}