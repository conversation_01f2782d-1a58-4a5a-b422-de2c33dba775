package com.bukuwarung.activities.transaction.customer.download;

import android.app.ProgressDialog;
import android.os.Build;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.R;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.Utility;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskExecutors;

import java.util.List;

public final class DownloadPdfClickHandler implements OnClickListener {
    final CustomerTransactionActivity transactionActivity;

    public DownloadPdfClickHandler(CustomerTransactionActivity transactionActivity ) {
        this.transactionActivity = transactionActivity;
    }

    public final void onClick(View view) {
        List transactionList = transactionActivity.transactionListViewModel.getTransactionList();
        if (Utility.hasInternet() && !ListUtils.isEmpty(transactionList)) {
            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23) {
                this.transactionActivity.requestPermissions(PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR, PermissionConst.WRITE_EXTERNAL_STORAGE);
            } else{
                downloadReportPdf(transactionList);
            }
        }
    }

    public final void downloadReportPdf(List<? extends DataHolder> matchingTransactions) {
        ProgressDialog progressDialog = ComponentUtil.showProgressDialog(transactionActivity,transactionActivity.getString(R.string.preparing_customer_pdf));
        List<? extends DataHolder> list = matchingTransactions;
        try {
            list.remove(matchingTransactions.size()-1);
        }catch (Exception e){
            e.printStackTrace();
        }
        Task<PrepareCustomerTransactionPayLoad.ReportTaskResult> task = new PrepareCustomerTransactionPayLoad(transactionActivity.getCustomerId()).getTask(matchingTransactions);
        task.continueWith(TaskExecutors.MAIN_THREAD, new DisplayDownloadedReportPDF(transactionActivity, progressDialog,true,"com.whatsapp"));

    }
}
