package com.bukuwarung.activities.transaction.customer.add;

import android.app.DatePickerDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.airbnb.lottie.LottieAnimationView;
import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.customer.transactiondetail.CustomerTransactionDetailActivity;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.MixSwipeAnim;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.dialogs.login.LoginBottomSheetDialog;
import com.bukuwarung.dialogs.login.VerifyOtpBottomSheetDialog;
import com.bukuwarung.keyboard.CustomKeyboardView;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.share.ShareLayoutImage;
import com.bukuwarung.utils.AnimationExtensionKt;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.ImageUtils;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.ShareUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;
import com.bukuwarung.utils.UtilsKt;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskExecutors;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.checkbox.MaterialCheckBox;

import org.jetbrains.annotations.NotNull;

import java.util.Calendar;
import java.util.Date;

public final class AddTransactionActivity extends AppActivity implements LoginBottomSheetDialog.LoginSheetListener, VerifyOtpBottomSheetDialog.OtpSheetListener {

    private LoginBottomSheetDialog loginBtSheet;
    private VerifyOtpBottomSheetDialog otpBtSheet;

    public static int CREDIT = 1;
    public static int DEBIT = -1;
    public static final String TRANS_ID_STR = "transactionId";
    public CustomKeyboardView keyboardView;
    private View cursor;
    public int TRANSACTION_TYPE;
    public String customerId;
    private MaterialButton mSaveButton;
    public TextView mTransactionInputAmount;
    public TextView mTransactionInputAmountRes;
    private ImageView inputAmountImage;
    public TextView mTransactionTitle;
    public TextView mTransactionInputDate;
    public EditText mTransactionInputDescription;
    private TextView currencySymbol;
    private ImageView noteIcon;
    private ImageView dateIcon;
    private LinearLayout actionLayout;
    private TextView tvPreviewLabel;

    public String transactionDate;
    public TransactionEntity transaction;

    // to compare if anything changed
    public TransactionEntity oldTrxInstance;
    public String transactionId;

    private TextView tvSmsPreview;
    private View svSmsPreview;
    public MaterialCheckBox cbSendNewTransaction;

    private View successView;
    private LottieAnimationView lavSuccessView;

    private CustomerEntity customerEntity;
    private FeaturePrefManager featurePrefManager;
    private String currentEntryPoint = "";
    private SaveTransactionHandler onSaveButtonClicked = new SaveTransactionHandler(this);
    private int txnCount = 0;
    private long notaVisibleThreshold = 0;

    public final String getCustomerId() {
        return this.customerId;
    }

    public static String getTransactionId(AddTransactionActivity activity) {
        return activity.transactionId;
    }

    public AddTransactionActivity() {
        super(new MixSwipeAnim());
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        FeaturePrefManager.getInstance().exitWithoutTransaction(AppConst.CUSTOMER_TRANSACTION);
        setContentView(R.layout.activity_add_customer_transaction);

        this.mTransactionInputAmount = findViewById(R.id.transaction_input_amount);
        this.mTransactionInputAmountRes = (TextView) findViewById(R.id.transaction_input_amount_result);
        this.inputAmountImage = findViewById(R.id.amountImage);
        this.mTransactionTitle = (TextView) findViewById(R.id.txt_main_title);
        this.mTransactionInputDescription = findViewById(R.id.transaction_note);
        this.mTransactionInputDate = findViewById(R.id.transaction_date);
        this.noteIcon = findViewById(R.id.transaction_note_icon);
        this.dateIcon = findViewById(R.id.transaction_date_icon);
        this.actionLayout = findViewById(R.id.action_btn);
        this.tvSmsPreview = findViewById(R.id.tvSmsPreview);
        this.svSmsPreview = findViewById(R.id.svSMSPreview);
        this.cbSendNewTransaction = findViewById(R.id.cbSendNewTransaction);
        ComponentUtil.setVisible(cbSendNewTransaction, !SessionManager.getInstance().isGuestUser());
        this.mSaveButton = findViewById(R.id.btn_save_transaction);
        this.successView = findViewById(R.id.success_view);
        this.lavSuccessView = findViewById(R.id.lav_success);
        this.tvPreviewLabel = findViewById(R.id.tvPreviewLabel);


        final String amt = mTransactionInputAmountRes.getText().toString();
        mSaveButton.setEnabled(true);
//        if (!amt.isEmpty() && !amt.equalsIgnoreCase("0")) {
//            mSaveButton.setEnabled(true);
//        } else {
//            mSaveButton.setEnabled(false);
//        }

        MaterialButton deleteBtn = findViewById(R.id.deleteBtn);
        deleteBtn.setVisibility(View.GONE);
        findViewById(R.id.close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mSaveButton.getVisibility() == View.VISIBLE) {
                    onBackPressed();
                } else {
                    finish();
                }
            }
        });
//        deleteBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                showDeletionDialog();
//            }
//        });

        this.featurePrefManager = FeaturePrefManager.getInstance();
//        cbSendNewTransaction.setChecked(featurePrefManager.sendSMSOnNewTransactionByDefault());
        setSmsPreviewVisibility(cbSendNewTransaction.isChecked());


        findViewById(R.id.close).setOnClickListener(view -> finish());
        deleteBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showDeletionDialog();
            }
        });

        cbSendNewTransaction.setOnCheckedChangeListener((compoundButton, b) -> {
            setSmsPreviewVisibility(b);
            featurePrefManager.setSendSMSOnNewTransactionByDefault(b);
            AppAnalytics.trackEvent("Toggle sms preview");
            setCustomerSmsEnabled(b);
//            if (!b) {
//                final NotifyWAPromptDialog dialog = new NotifyWAPromptDialog(
//                        this,
//                        promptResult -> {
//                            if (promptResult) {
//                                cbSendNewTransaction.setChecked(false);
//                                setCustomerSmsEnabled(false);
//                            } else {
//                                cbSendNewTransaction.setChecked(true);
//                                setCustomerSmsEnabled(true);
//                            }
//                            return null; // java to kotlin unit conversion
//                        }
//                );
//                dialog.show();
//            }
        });

        this.mSaveButton = findViewById(R.id.btn_save_transaction);
        String storableDateString = Utility.getStorableDateString(new Date());

        this.transactionDate = storableDateString;
        this.mTransactionInputDate.setText(Utility.getReadableDateString(this.transactionDate));
        TextView header = findViewById(R.id.transaction_title);

        Intent intent = getIntent();

        Bundle extras = intent.getExtras();
        if (extras != null) {
            this.customerId = extras.getString("customer_id");
            if (extras.getString("transactionId") != null) {
                String string = extras.getString("transactionId");
                this.transactionId = string;
            }
        }

        boolean isNewUtangFlow = RemoteConfigUtils.INSTANCE.shouldShowOldUtangForm();

        if (isNewUtangFlow) {

        }

        currencySymbol = findViewById(R.id.currency_symbol);
        this.TRANSACTION_TYPE = extras.getInt("TRANSACTION_TYPE");
        final AddTransactionActivity activity = this;


        MaterialButton shareBtn = findViewById(R.id.shareBtn);

        final LinearLayout formLayout = findViewById(R.id.formLayout);
        shareBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                generateAndShareViewImage(activity, "com.whatsapp", formLayout, "", true);
            }
        });

        keyboardView = findViewById(R.id.keyboardView);
        keyboardView.setVisibility(View.VISIBLE);
        keyboardView.setResultTv(mTransactionInputAmountRes);
        keyboardView.setExprTv(mTransactionInputAmount);
        mTransactionInputAmount.setText(mTransactionInputAmountRes.getText());
        keyboardView.setResultLayout((LinearLayout) findViewById(R.id.exprLayour));
        cursor = findViewById(R.id.cursor);
        keyboardView.setCursor(cursor);

//        editBtn.setVisibility(View.GONE);
//        editBtn.setVisibility(View.INVISIBLE);
        actionLayout.setVisibility(View.GONE);
        amountClickListener(activity);
        mTransactionInputDate.setEnabled(true);
        mTransactionInputDescription.setEnabled(true);
//        editBtn.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                mSaveButton.setVisibility(View.VISIBLE);
//                editBtn.setVisibility(View.INVISIBLE);
//                actionLayout.setVisibility(View.GONE);
//                amountClickListener(activity);
//                mTransactionInputDate.setEnabled(true);
//                mTransactionInputDescription.setEnabled(true);
//            }
//        });
        shareBtn.setVisibility(View.GONE);
        mSaveButton.setVisibility(View.VISIBLE);
        actionLayout.setVisibility(View.GONE);
        amountClickListener(activity);
        mTransactionInputDate.setEnabled(true);
        mTransactionInputDescription.setEnabled(true);

        currencySymbol.setText(Utility.getCurrency());
        keyboardView.setCurrency(currencySymbol);

        if (!Utility.isBlank(transactionId)) {
//            mTransactionInputDate.setEnabled(false);
//            mTransactionInputDescription.setEnabled(false);
            if (UtilsKt.isAnimEnabled(this)) keyboardView.clearAnimation();
            keyboardView.setVisibility(View.GONE);
            keyboardView.hideCursor();
        }

        this.mSaveButton.setOnClickListener(onSaveButtonClicked);
        this.mTransactionInputDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                setDateFromDatePicker();
            }
        });


        this.mTransactionInputDescription.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            public void onFocusChange(View v, boolean hasFocus) {
                if (!hasFocus) {
//                    hideSoftKeyboard();

                    if (UtilsKt.isAnimEnabled(AddTransactionActivity.this))
                        keyboardView.clearAnimation();
                    keyboardView.setVisibility(View.GONE);
                    keyboardView.hideCursor();
                }
            }
        });

        if (!Utility.isBlank(transactionId)) {
            this.mSaveButton.setVisibility(View.VISIBLE);
            actionLayout.setVisibility(View.VISIBLE);
            this.transaction = TransactionRepository.getInstance(this).getTransactionById(transactionId);
            initTransactionValues();
            keyboardView.updateResult(this.mTransactionInputAmount.getText().toString());
        } else {
            amountClickListener(this);
        }
        if (this.TRANSACTION_TYPE == 1) {
            header.setText(getString(R.string.activity_payments_received));
            mTransactionInputAmountRes.setTextColor(getResources().getColor(R.color.in_green));
            mTransactionInputAmountRes.setHintTextColor(getResources().getColor(R.color.in_green));
            currencySymbol.setTextColor(getResources().getColor(R.color.in_green));
            inputAmountImage.setImageResource(R.drawable.ic_amount_income);
            mTransactionTitle.setText("Menerima");
        } else {
            header.setText(getString(R.string.activity_payments_given));
            mTransactionInputAmountRes.setTextColor(getResources().getColor(R.color.out_red));
            mTransactionInputAmountRes.setHintTextColor(getResources().getColor(R.color.out_red));
            currencySymbol.setTextColor(getResources().getColor(R.color.out_red));
            inputAmountImage.setImageResource(R.drawable.ic_amount_new);
            mTransactionTitle.setText("Memberikan");
            tvPreviewLabel.setText(R.string.messages_that_customers_will_receive);
        }

        if (!Utility.isBlank(customerId)) {
            customerEntity = CustomerRepository.getInstance(this).getCustomerById(customerId);
            cbSendNewTransaction.setText(String.format("%s %s", getString(R.string.send_sms_notification_to), customerEntity.name));
        }

        mTransactionInputAmountRes.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                try {
                    setSmsPreviewText(charSequence.toString());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {
                final String amt = editable.toString();
//                if (!amt.isEmpty() && !amt.equalsIgnoreCase("0")) {
//                    mSaveButton.setEnabled(true);
//                } else {
//                    mSaveButton.setEnabled(false);
//                }
            }
        });

        String trxAmount = "0";
        try {
            trxAmount = Utility.formatAmount(transaction.amount);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            setSmsPreviewText(trxAmount);
        }
        if (!RemoteConfigUtils.INSTANCE.shouldSendSmsUtang()) {
            cbSendNewTransaction.setChecked(false);
            cbSendNewTransaction.setVisibility(View.GONE);
        }
        txnCount = TransactionRepository.getInstance(this).getUtangTransactionCountWithDeletedRecords();
        notaVisibleThreshold = RemoteConfigUtils.INSTANCE.getForcedUtangInvoiceVisibilityThreshold();
    }

    private void setSmsPreviewVisibility(boolean isVisible) {
        if (isVisible) {
            svSmsPreview.setVisibility(View.VISIBLE);
        } else {
            svSmsPreview.setVisibility(View.GONE);
        }
    }

    private void amountClickListener(final AddTransactionActivity activity) {
        Animation animBlink = AnimationUtils.loadAnimation(activity,
                R.anim.move_up);
        if (UtilsKt.isAnimEnabled(this)) keyboardView.startAnimation(animBlink);
        keyboardView.setVisibility(View.VISIBLE);
        keyboardView.showCursor();
        findViewById(R.id.transaction_input_amount_layout).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                hideSoftKeyboard();
                mTransactionInputDescription.clearFocus();
                Animation animBlink = AnimationUtils.loadAnimation(activity,
                        R.anim.move_up);
                if (UtilsKt.isAnimEnabled(AddTransactionActivity.this))
                    keyboardView.startAnimation(animBlink);
                keyboardView.setVisibility(View.VISIBLE);
                keyboardView.showCursor();
                findViewById(R.id.currency_symbol).setVisibility(View.VISIBLE);
            }
        });
    }

    public final void generateAndShareViewImage(Context context, String packageNm, View receiptLayout, String mobile, boolean useWA) {
        try {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewPosInvoice());
            AppAnalytics.trackEvent(AnalyticsConst.SHARE_SINGLE_TRANSACTION_RECEIPT, propBuilder);
            Task saveViewSnapshot = ImageUtils.saveLayoutConvertedImage(receiptLayout, false);
            ShareLayoutImage shareLayoutImage = new ShareLayoutImage(ShareUtils.getReceiptSharingText(context, "", 0, ""), context, packageNm, mobile, useWA, false);
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void hideSoftKeyboard() {
        if (getCurrentFocus() != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        }
    }

    public void onSaveInstanceState(Bundle bundle) {
        super.onSaveInstanceState(bundle);
        if (bundle == null) {

        }
        String str = "customerId";
        String str2 = this.customerId;
        if (str2 == null) {

        }
        bundle.putString(str, str2);
    }

    public void onRestoreInstanceState(Bundle bundle) {
        super.onRestoreInstanceState(bundle);
        String string = bundle.getString("customerId");
        this.customerId = string;
    }

    public final void setDateFromDatePicker() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int mm = calendar.get(Calendar.MONTH);
        int dd = calendar.get(Calendar.DATE);
        calendar.get(Calendar.DAY_OF_WEEK);

        try {
            String currentTrxDate = transactionDate;
            if (currentTrxDate != null && !currentTrxDate.isEmpty()) {
                Date currentDate = DateTimeUtils.convertToDateYYYYMMDD(currentTrxDate);
                calendar.setTime(currentDate);
                year = calendar.get(Calendar.YEAR);
                mm = calendar.get(Calendar.MONTH);
                dd = calendar.get(Calendar.DATE);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(this, android.R.style.Theme_DeviceDefault_Light_Dialog, new TransactionDateSelectionHandler(this), year, mm, dd);
        datePickerDialog.setTitle(R.string.date);
        datePickerDialog.show();

        Animation moveDown = AnimationUtils.loadAnimation(AddTransactionActivity.this,
                R.anim.move_down);
        if (UtilsKt.isAnimEnabled(this)) keyboardView.startAnimation(moveDown);
        keyboardView.setVisibility(View.GONE);
        mTransactionInputAmount.setBackgroundResource(R.drawable.credit_amount_inactive);
        keyboardView.hideCursor();
        try {
            if (this.mTransactionInputDescription.hasFocus()) {
                this.mTransactionInputDescription.clearFocus();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onBackPressed() {
        if (keyboardView.getVisibility() == View.VISIBLE) {
            keyboardView.setVisibility(View.GONE);
        } else {
            finish();
        }
    }

    //    @Override
//    public void onBackPressed() {
//        boolean isTrxDataChanged = true;
//        boolean isAmountChanged = true;
//        boolean isNoteChanged = true;
//        boolean isDateChanged = true;
//        boolean isSmsChanged = true;
//        try {
//            if (oldTrxInstance != null) {
//                // check amount changed
//                if (oldTrxInstance.amount != null) {
//                    this.keyboardView.submit();
//                    String inputAmount = this.keyboardView.getInputAmount();
//                    double amount = Math.abs(Double.parseDouble(inputAmount));
//                    double oldAmount = Math.abs(oldTrxInstance.amount);
//
//                    if (oldAmount == amount)
//                        isAmountChanged = false;
//                }
//
//                // check note changed
//                if (oldTrxInstance.description != null) {
//                    String note = this.mTransactionInputDescription.getText().toString();
//                    if (note.equalsIgnoreCase(oldTrxInstance.description))
//                        isNoteChanged = false;
//                }
//
//                // check date changed
//                if (oldTrxInstance.date != null) {
//                    String date = this.transactionDate;
//                    if (date.equalsIgnoreCase(oldTrxInstance.date))
//                        isDateChanged = false;
//                }
//
//                CustomerEntity customerEntity = CustomerRepository.getInstance(this).getCustomerById(customerId);
//                // check sms changed
//                if (customerEntity != null && customerEntity.enableSmsAlerts != null) {
//                    int smsStatus = this.cbSendNewTransaction.isChecked() ? 1 : 0;
//                    if (smsStatus == customerEntity.enableSmsAlerts) isSmsChanged = false;
//                }
//
//                isTrxDataChanged = isAmountChanged || isNoteChanged || isDateChanged || isSmsChanged;
//            }
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//
//        if (mSaveButton.getVisibility() == View.VISIBLE && isTrxDataChanged) {
//            final ExpenseBackConfirmationDialog dialog =
//                    new ExpenseBackConfirmationDialog(
//                            this,
//                            promptResult -> {
//                                if (promptResult) finish();
//                                return null; // java to kotlin unit conversion
//                            }
//                    );
//            dialog.show();
//        } else {
//            finish();
//        }
//    }

    public final void saveTransaction() {
        keyboardView.submit();
        String amountStr = keyboardView.getInputAmount();
        double amountDouble = !Utility.isBlank(amountStr) ? Double.parseDouble(amountStr) : 0.0d;
        AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder()
                .put(AnalyticsConst.DATE, new Date())
                .put(AnalyticsConst.FEATURE, AnalyticsConst.UTANG);
        builder.put(AnalyticsConst.AMOUNT, amountDouble);
        if (customerEntity != null) {
            builder.put(AnalyticsConst.ADDED_TO, customerEntity.phone);
        }
        if (TRANSACTION_TYPE == AddTransactionActivity.DEBIT) {
            builder.put(AnalyticsConst.TYPE, AnalyticsConst.DEBIT);
        } else {
            builder.put(AnalyticsConst.TYPE, AnalyticsConst.CREDIT);
        }
        if (Utility.isBlank(transactionId)) {
            Utility.trackTransactionCount();

            // lottie animation
            if (RemoteConfigUtils.INSTANCE.shouldShowUtangSuccessAnimation()) {
                new Thread(new SaveTransactionThread(AddTransactionActivity.this)).start();
                ((TextView) findViewById(R.id.tv_trx_success)).setText(RemoteConfigUtils.INSTANCE.getTrxSuccessMessage());
                AnimationExtensionKt.showForOnce(this.lavSuccessView, this.successView, 75, () -> {
                    if (txnCount > notaVisibleThreshold) {
                        Intent intent = new Intent(this, CustomerTransactionDetailActivity.class);
                        intent.putExtra(CustomerTransactionDetailActivity.TRX_ID_PARAM, TransactionRepository.getInstance(Application.getAppContext()).getTransactionId());
                        intent.putExtra(CustomerTransactionDetailActivity.CST_ID_PARAM, customerId);
                        intent.putExtra("OPERATION_TYPE", "ADD_TRANSACTION");
                        startActivity(intent);
                    }
                    finish();
                    return null;
                });
            } else {
                new Thread(new SaveTransactionThread(this)).start();
                if (txnCount > notaVisibleThreshold) {
                    Intent intent = new Intent(this, CustomerTransactionDetailActivity.class);
                    intent.putExtra(CustomerTransactionDetailActivity.TRX_ID_PARAM, TransactionRepository.getInstance(Application.getAppContext()).getTransactionId());
                    intent.putExtra(CustomerTransactionDetailActivity.CST_ID_PARAM, customerId);
                    intent.putExtra("OPERATION_TYPE", "ADD_TRANSACTION");
                    startActivity(intent);
                }
                finish();
            }
        } else {
            updateTransaction(0);
            finish();
        }
    }

    public final void initTransactionValues() {
        TransactionEntity transactionEntity = this.transaction;
        CustomerEntity customerEntity = CustomerRepository.getInstance(this).getCustomerById(this.transaction.customerId);
        if (transactionEntity != null) {
            TransactionEntity copiedTrxInstance = new TransactionEntity();
            copiedTrxInstance.amount = transactionEntity.amount;
            copiedTrxInstance.description = transactionEntity.description;
            copiedTrxInstance.date = transactionEntity.date;
            copiedTrxInstance.smsStatus = transactionEntity.smsStatus;
            this.oldTrxInstance = copiedTrxInstance;

            int transactionType = InputUtils.getTransactionType(transactionEntity.amount);
            this.TRANSACTION_TYPE = transactionType;
            setViewTransactionValues(transactionEntity.createdAt, transactionType, transactionEntity.amount, transactionEntity.date, transactionEntity.description);
        }
    }

    public final void setViewTransactionValues(long timestamp, int transactionType, double amount, String date, String notes) {
        this.mTransactionInputAmountRes.setText(Utility.formatCurrencyForEditing(amount));
        this.mTransactionInputAmount.setText(Utility.formatCurrencyForEditing(amount));
        this.customerId = this.transaction.customerId;
        this.transactionDate = date;
        this.mTransactionInputDate.setText(DateTimeUtils.getDateTimeStr(transactionDate, timestamp));
        this.mTransactionInputDescription.setText(notes);
    }

    public final void updateTransaction(int delFlg) {
        new Thread(new UpdateExistingTransactionThread(this, delFlg)).start();
    }

    public final void showDeletionDialog() {
        final Dialog dialog = new Dialog(this);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_generic);
        Window window = dialog.getWindow();
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        ;

        TextView deleteOk = (TextView) dialog.findViewById(R.id.ok);
        TextView cancel = (TextView) dialog.findViewById(R.id.cancel);
        TextView dlgTitle = (TextView) dialog.findViewById(R.id.title);
        TextView dlgBody = (TextView) dialog.findViewById(R.id.body);
        dlgTitle.setText(this.getString(R.string.transaction_deletion_dialog_title));
        dlgBody.setText(this.getString(R.string.delete_trans_body));

        deleteOk.setOnClickListener(new DeleteTransactionHandler(this, dialog));
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.show();
    }

    private void setSmsPreviewText(String transactionAmount) {
        if (transactionAmount.isEmpty()) {
            transactionAmount = "0";
        }
        String text = ShareUtils.getSmsPreviewText(transactionAmount, getReportUrl(customerEntity), this.TRANSACTION_TYPE);
        tvSmsPreview.setText(text);
    }

    public static String getReportUrl(CustomerEntity customerEntity) {
        StringBuilder sb = new StringBuilder();
        try {
            String format = String.format(Utility.getReportUrl(), customerEntity.altCustomerId);
            sb.append(format);
        } catch (Exception ex) {
            ex.printStackTrace();
            sb.append("https://bukuwarung.com/app");
        }

        return sb.toString();
    }

    private void setCustomerSmsEnabled(Boolean isEnabled) {
        if (!Utility.isBlank(getCustomerId())) {
            SessionManager sessionManager = SessionManager.getInstance();
            int smsFlag = 1;
            if (isEnabled) {
                smsFlag = 1;
            } else {
                smsFlag = 0;
            }

            CustomerRepository.getInstance(this).updateEnableSmsAlertsForCustomer(
                    sessionManager.getUserId(),
                    sessionManager.getDeviceId(),
                    getCustomerId(),
                    smsFlag
            );
        }
    }

    @Override
    public void goToVerifyOtp(@NotNull String phone, @NotNull String countryCode, @NotNull String method) {
        otpBtSheet = VerifyOtpBottomSheetDialog.Companion.newInstance(phone, countryCode, currentEntryPoint, this);
        otpBtSheet.show(getSupportFragmentManager(), LoginBottomSheetDialog.TAG);
    }

    @Override
    public void callLoginBottomsheet(boolean openKeyboard, @NotNull String entryPoint) {
        currentEntryPoint = entryPoint;
        showLogin(openKeyboard);
    }

    private void showLogin(boolean openKeyboard) {
        loginBtSheet = LoginBottomSheetDialog.Companion.newInstance(currentEntryPoint, this);
        loginBtSheet.show(getSupportFragmentManager(), LoginBottomSheetDialog.TAG);
    }

    @Override
    public void onFinishOtp() {
        BusinessRepository.getInstance(this).mergeGuestRecords();
        CashRepository.getInstance(this).mergeGuestRecords();
        ProductRepository.getInstance(this).mergeGuestRecords();
        SessionManager.getInstance().isGuestUser(false);
        Thread mThread = new Thread() {
            @Override
            public void run() {
                TransactionUtil.backupAllTransactions();
                MainActivity.startActivityAndClearTop(AddTransactionActivity.this);
            }
        };
        mThread.start();
    }

}
