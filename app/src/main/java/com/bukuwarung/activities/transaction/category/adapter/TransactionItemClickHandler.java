package com.bukuwarung.activities.transaction.category.adapter;

import android.content.Intent;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.category.adapter.dataholder.CategoryTransactionDataHolder;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

final class TransactionItemClickHandler implements OnClickListener {

    final CategoryTransactionAdapter parent;

    TransactionItemClickHandler(CategoryTransactionAdapter categoryTransactionAdapter) {
        this.parent = categoryTransactionAdapter;
    }

    public final void onClick(View view) {
        int childAdapterPosition = this.parent.transactionRecyclerView.getChildAdapterPosition(view);
        try {
            DataHolder dataHolder = this.parent.transactionDataHolderList.get(childAdapterPosition);
            if (dataHolder instanceof CategoryTransactionDataHolder) {
                CashTransactionEntity entity = ((CategoryTransactionDataHolder) dataHolder).getCashTransactionEntity();
                Intent intent = CashTransactionDetailActivity.Companion.getNewIntent(
                        this.parent.getContext(), entity.cashTransactionId, false
                );
                intent.putExtra("OPERATION_TYPE`", "ADD_TRANSACTION");
                intent.putExtra(CashTransactionDetailActivity.IS_AUTO_RECORD_TRANSACTION,
                ((CategoryTransactionDataHolder)dataHolder).getCashTransactionEntity().transactionType == TransactionEntityType.BRICK_TRANSACTION);
                this.parent.getContext().startActivity(intent);
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
