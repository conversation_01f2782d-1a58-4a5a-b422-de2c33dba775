package com.bukuwarung.activities.transaction.customer.adapter;

import android.content.Intent;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.activities.customer.transactiondetail.CustomerTransactionDetailActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.customer.adapter.dataholder.TransactionDataHolder;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.entity.TransactionEntity;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

final class TransactionItemClickHandler implements OnClickListener {

    final TransactionAdapter parent;

    TransactionItemClickHandler(TransactionAdapter transactionAdapter) {
        this.parent = transactionAdapter;
    }

    public final void onClick(View view) {
        int childAdapterPosition = this.parent.transactionRecyclerView.getChildAdapterPosition(view);
        try {
            DataHolder dataHolder = this.parent.transactionDataHolderList.get(childAdapterPosition);
            if (dataHolder instanceof TransactionDataHolder) {
                Intent intent = new Intent(this.parent.getContext(), CustomerTransactionDetailActivity.class);
                TransactionEntity entity = ((TransactionDataHolder) dataHolder).getTransactionEntity();
                intent.putExtra(CustomerTransactionDetailActivity.TRX_ID_PARAM, entity.transactionId);
                intent.putExtra(CustomerTransactionDetailActivity.CST_ID_PARAM, entity.customerId);
                intent.putExtra("OPERATION_TYPE", "ADD_TRANSACTION");
                this.parent.getContext().startActivity(intent);
            }
            AppAnalytics.trackEvent("open_single_utang_transaction");
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
