package com.bukuwarung.activities.transaction.customer

import android.content.Context
import android.os.Bundle
import android.view.View
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.ShareUtils
import com.bukuwarung.utils.Utility
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import kotlinx.android.synthetic.main.activity_lunaskan_success_message.*

class LunaskanSuccessMessageActivity : AppActivity() {

    var amount: Double = 0.0
    var formattedAmount:String = ""
    var customerName:String = ""
    var customerId:String = ""
    var type = 0
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_lunaskan_success_message)

        var bookEntity : BookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
        ownerName.text = if (SessionManager.getInstance().isGuestUser()) "Nama Saya" else bookEntity.businessName
        mobileNumber.text = if (SessionManager.getInstance().isGuestUser()) "" else bookEntity.businessPhone
        if(intent.hasExtra("amount")){
            amount = intent.getDoubleExtra("amount", 0.0)
            formattedAmount = Utility.formatAmount(amount)
            paymentCompletionAmount.setText("Utang $formattedAmount")
        }
        if(intent.hasExtra("customerId"))
            customerId = intent.getStringExtra("customerId") ?: ""
        if(intent.hasExtra("customerName"))
            customerName = intent.getStringExtra("customerName") ?: ""

        if (intent.hasExtra("type")) {
            type = intent.getIntExtra("type", 0)
        }

        val isNewUtangFlow = !RemoteConfigUtils.shouldShowOldUtangForm()
        if (isNewUtangFlow) {
            if (type == 0) {
                paymentCompletionAmount.text = "Utang saya ke $customerName sebesar $formattedAmount"
                paymentCompletionMessage.text = "sudah dibayar lunas"
            } else {
                paymentCompletionAmount.text = "Utang $customerName ke saya sebesar $formattedAmount"
                paymentCompletionMessage.text = "sudah dibayar lunas"
            }
        }


        shareBtn.setOnClickListener{
            //if the business name is incomplete show a dialog to complete the business name only once
            bookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
            if(!OnboardingPrefManager.getInstance().getHasFinishedForId(OnboardingPrefManager.PROFILE_COMPLETION_DIALOG) && !Utility.hasBusinessName(bookEntity.businessName)){
                val profileCompletionDialog = ProfileCompletionDialog(this,this,{
                    bookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId())
                    ownerName.text = bookEntity.businessName
                    shareSuccessMessage()
                },AnalyticsConst.SETTLE_UTANG,false,customerId = customerId)
                profileCompletionDialog.show()
                OnboardingPrefManager.getInstance().setHasFinishedForId(OnboardingPrefManager.PROFILE_COMPLETION_DIALOG)
            }else{
                shareSuccessMessage()
            }
        }
        closeMessage.setOnClickListener{
            finish()
        }
    }

    fun shareSuccessMessage() {
        generateAndShareViewImage(this, "", lunaskanSuccessMessageLayout, "", false)
    }

    fun generateAndShareViewImage(context: Context?, packageNm: String?, lunaskanMessageLayout: View?, mobile: String?, useWA: Boolean) {
        try {
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(lunaskanMessageLayout, false)
            val shareLayoutImage = ShareLayoutImage(ShareUtils.getLunaskanSharingText(context, customerName, 0), context, packageNm, mobile, useWA, false)
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
}