package com.bukuwarung.activities.transaction.customer.download;

import android.net.Uri;

import com.bukuwarung.Application;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.api.ApiRepository;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ReportUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.TaskCompletionSource;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.List;

import retrofit2.Call;

final class CustomerTransactionTaskExecutor implements Runnable {

    final int filter;
    final List transactionList;
    final String customerId;
    final TaskCompletionSource tcs;
    final PrepareCustomerTransactionPayLoad prepareBookTransactionsReportPayLoad;

    CustomerTransactionTaskExecutor(PrepareCustomerTransactionPayLoad customerListReportPayload, int filter, List list, TaskCompletionSource taskCompletionSource,String customerId) {
        this.prepareBookTransactionsReportPayLoad = customerListReportPayload;
        this.filter = filter;
        this.transactionList = list;
        this.tcs = taskCompletionSource;
        this.customerId = customerId;
    }

    public final void run() {
        try {
            JsonObject payload = transactionListPayload(this.filter, this.transactionList);
            AppAnalytics.trackEvent("download_customer_report");
            Call postAccountReport = ApiRepository.getReportsApiService().generateCustomerTransactionsReport(payload);
            Uri callApiAndDownloadPdf = ReportUtils.callReportApi(null, postAccountReport,"BukuWarung_Pelanggan",true);
            if (callApiAndDownloadPdf != null) {
                this.tcs.setResult(new PrepareCustomerTransactionPayLoad.ReportTaskResult(callApiAndDownloadPdf, null));
                return;
            }
        } catch (Exception e) {
            this.tcs.setResult(new PrepareCustomerTransactionPayLoad.ReportTaskResult(null, e.getMessage()));
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public final JsonObject transactionListPayload(int filter, List<TransactionEntity> transactionList) {

        String phoneWithCountryCode = SessionManager.getInstance().getCountryCode()+""+ User.getUserId();
        BookEntity bookSync = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
        CustomerEntity customer = CustomerRepository.getInstance(Application.getAppContext()).getCustomerById(customerId);
        JsonObject reportObj = new JsonObject();
        reportObj.addProperty("language", SessionManager.getInstance().getAppLanguage());
        reportObj.addProperty("merchantName", bookSync.businessName);
        reportObj.addProperty("merchantPhone", phoneWithCountryCode);
        reportObj.addProperty("businessImage", "");
        reportObj.addProperty("businessCaption", bookSync.businessTagLine);
        reportObj.addProperty("customerName", customer.name);
        reportObj.addProperty("includeDescription", true);
        JsonArray jsonArray = new JsonArray();

        for (TransactionEntity transactionEntity : transactionList) {
            JsonObject dataObject = new JsonObject();
            dataObject.addProperty("amount", transactionEntity.amount);
            dataObject.addProperty("type", transactionEntity.amount > 0.0d ? 1 : -1);
            dataObject.addProperty("date", transactionEntity.date);
            dataObject.addProperty("description", transactionEntity.description);
            jsonArray.add(dataObject);
        }
        reportObj.add("transactions", jsonArray);
        return reportObj;
        //{"language":12,"businessName":"rest😎😎","businessPhone":"+6596430673","businessImage":"","startDate":"2020-01-01","endDate":"2020-02-08","transactions":[{"date":"2020-01-08","amount":-56.0,"name":"himan","description":""},{"date":"2020-01-19","amount":-56.0,"name":"himan","description":""},{"date":"2020-01-24","amount":56.0,"name":"himan","description":""}],"includeDescription":true}
    }
}
