package com.bukuwarung.activities.transaction.customer.download;

import android.app.ProgressDialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;

import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.ShareUtils;
import com.bukuwarung.utils.Utility;
import com.google.android.gms.tasks.Continuation;
import com.google.android.gms.tasks.Task;

public final class DisplayDownloadedReportPDF implements Continuation<PrepareCustomerTransactionPayLoad.ReportTaskResult, Object> {
    final ProgressDialog progressDialog;
    final CustomerTransactionActivity transactionActivity;
    final boolean isPdfDownload;
    final String packageName;

    public DisplayDownloadedReportPDF(CustomerTransactionActivity transactionActivity, ProgressDialog pDialog, boolean download, String packageName) {
        this.transactionActivity = transactionActivity;
        this.progressDialog = pDialog;
        this.isPdfDownload = download;
        this.packageName = packageName;
    }

    public Object then(Task<PrepareCustomerTransactionPayLoad.ReportTaskResult> task) {
        try {
            this.progressDialog.dismiss();
            PrepareCustomerTransactionPayLoad.ReportTaskResult apiResult = task.getResult();
            if (apiResult == null || apiResult.contentUri == null) {
                NotificationUtils.alertToast("Report service is not available. Please try later.");
            } else {
                if (isPdfDownload) {
                    Intent intent = new Intent("android.intent.action.VIEW");
                    intent.setType("application/pdf");
                    intent.setData(apiResult.contentUri);
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    transactionActivity.startActivity(Intent.createChooser(intent, "Open with"));
                } else {
                    shareFileOnWhatsapp(packageName, apiResult.contentUri, null, transactionActivity.getCustomerPhone());
                }
            }
        } catch (ActivityNotFoundException ne) {
            NotificationUtils.alertToast("PDF viewer not found.");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public final void shareFileOnWhatsapp(String packageName, Uri uri, String extraText, String phone) {
        StringBuilder sb = new StringBuilder();
        String countryCode = SessionManager.getInstance().getCountryCode();
        if (countryCode != null) {
            sb.append(countryCode);
            sb.append(Utility.cleanPhonenumber(phone));
            try {
                this.transactionActivity.startActivity(Intent.createChooser(ShareUtils.getShareUriOnWhatsappIntentWithPackage(packageName, uri, sb.toString(), extraText), "Choose an app"));
            } catch (ActivityNotFoundException e) {
                NotificationUtils.alertToast("WhatApp Not Installed");
                e.printStackTrace();
            }
        } else {
            //throw new TypeCastException("null cannot be cast to non-null type java.lang.String");
        }
    }
}
