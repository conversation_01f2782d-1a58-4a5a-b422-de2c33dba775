package com.bukuwarung.activities.transaction.customer.add;

import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.R;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.session.AuthHelper;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

final class SaveTransactionHandler implements OnClickListener {
    final  AddTransactionActivity addTransactionActivity;
    private long lastButtonSaveClicked = 0;

    SaveTransactionHandler(AddTransactionActivity addTransactionActivity) {
        this.addTransactionActivity = addTransactionActivity;
    }

    public final void onClick(View view) {
        if(!AuthHelper.isValidSessionOperation() && Utility.isBlank(this.addTransactionActivity.transactionId)){
            this.addTransactionActivity.callLoginBottomsheet(false, AnalyticsConst.UTANG);
            return;
        }
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600){
            return;
        }
        lastButtonSaveClicked = SystemClock.elapsedRealtime();
        this.addTransactionActivity.keyboardView.submit();
        String inputAmount = addTransactionActivity.keyboardView.getInputAmount();

        boolean isSmsEnabled = false;
        if (addTransactionActivity.cbSendNewTransaction != null) {
            isSmsEnabled = addTransactionActivity.cbSendNewTransaction.isChecked();
        }

        if (TextUtils.isEmpty(addTransactionActivity.transactionDate)) {
            addTransactionActivity.mTransactionInputDate.setError(addTransactionActivity.getString(R.string.date_warning));
        } else if (TextUtils.isEmpty(inputAmount)) {
            addTransactionActivity.mTransactionInputAmount.setError(addTransactionActivity.getString(R.string.amount_warning));
        } else {
            try {
                double amount = Double.parseDouble(inputAmount);
                if (amount < ((double) 0)) {
                    addTransactionActivity.mTransactionInputAmount.setError(this.addTransactionActivity.getString(R.string.amount_enter_positive_number));
                    return;
                }
                CustomerEntity customerEntity = CustomerRepository
                        .getInstance(addTransactionActivity)
                        .getCustomerById(addTransactionActivity.customerId);

                addTransactionActivity.saveTransaction();
                String receiverPhone = customerEntity.countryCode+""+customerEntity.phone;
                if(isSmsEnabled) {
                    Utility.sendSms(receiverPhone, amount, this.addTransactionActivity.TRANSACTION_TYPE, addTransactionActivity, customerEntity.altCustomerId,customerEntity.language,customerEntity.balance);

                    CustomerRepository.getInstance(addTransactionActivity)
                            .updateEnableSmsAlertsForCustomer(User.getUserId(), User.getDeviceId(), addTransactionActivity.customerId, Integer.valueOf(1));
                } else {
                    CustomerRepository.getInstance(addTransactionActivity)
                            .updateEnableSmsAlertsForCustomer(User.getUserId(), User.getDeviceId(), addTransactionActivity.customerId, Integer.valueOf(0));
                }
//                boolean sendMessage = sendSMS;
//                boolean sendMessage = customerEntity!=null && customerEntity.enableSmsAlerts == 1 && !Utility.isBlank(customerEntity.phone) && customerEntity.phone.length() >6;

                // Product Team request to disable it
               // Utility.showSmsSentToast(this.addTransactionActivity.TRANSACTION_TYPE, addTransactionActivity, isSmsEnabled);
            } catch (NumberFormatException unused) {
                addTransactionActivity.mTransactionInputAmount.setError(this.addTransactionActivity.getString(R.string.amount_invalid_number));
            } catch (Exception e) {
                e.printStackTrace();
                FirebaseCrashlytics.getInstance().recordException(e);
            }
        }
    }
}
