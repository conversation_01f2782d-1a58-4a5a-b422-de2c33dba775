package com.bukuwarung.activities.transaction.customer.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.activities.transaction.customer.adapter.dataholder.TransactionDataHolder;
import com.bukuwarung.activities.transaction.customer.adapter.viewholder.TransactionItemViewHolder;
import com.bukuwarung.activities.transaction.customer.adapter.viewholder.TransactionEmptyViewHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.List;

public final class TransactionAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context context;
    public final CustomerTransactionActivity activity;
    private OnClickListener mOnClickListener = new TransactionItemClickHandler(this);
    public final RecyclerView transactionRecyclerView;

    public List<? extends DataHolder> transactionDataHolderList;

    public final Context getContext() {
        return this.context;
    }

    public TransactionAdapter(List<? extends DataHolder> transactionList, RecyclerView recyclerView, Context context, CustomerTransactionActivity activity) {
        this.transactionDataHolderList = transactionList;
        this.transactionRecyclerView = recyclerView;
        this.context = context;
        this.activity = activity;
    }

    private final void bindTransactionViewHolder(TransactionItemViewHolder transactionViewHolder, TransactionDataHolder transactionDataHolder, int i) {
        TransactionEntity transactionEntity = (TransactionEntity) transactionDataHolder.getTransactionEntity();
        ComponentUtil.setVisible(transactionViewHolder.getHeader(),i==0);
        ComponentUtil.setVisible(transactionViewHolder.getIntegratedLabel(),transactionEntity.transactionType == TransactionEntityType.CASH_TRANSACTION);
        transactionViewHolder.getDate().setText(DateTimeUtils.formatTransactionDate(transactionEntity.date));
        if (Utility.isBlank(transactionEntity.description)) {
            transactionViewHolder.getNote().setVisibility(View.GONE);
        } else {
            transactionViewHolder.getNote().setVisibility(View.VISIBLE);
            transactionViewHolder.getNote().setText(transactionEntity.description);
        }
        if (TransactionUtil.isDebitTrans(transactionEntity)) {
            transactionViewHolder.getDebitAmount().setText(Utility.formatAmount(Math.abs(transactionEntity.amount)));
            transactionViewHolder.getDebitAmount().setVisibility(View.VISIBLE);
            transactionViewHolder.getCreditAmount().setVisibility(View.VISIBLE);
            transactionViewHolder.getCreditAmount().setText("-  ");
        }else{
            transactionViewHolder.getCreditAmount().setText(Utility.formatAmount(Math.abs(transactionEntity.amount)));
            transactionViewHolder.getCreditAmount().setVisibility(View.VISIBLE);
            transactionViewHolder.getDebitAmount().setVisibility(View.VISIBLE);
            transactionViewHolder.getDebitAmount().setText("-  ");
        }
    }

    public final void setDataHolderList(List<? extends DataHolder> dataHolderList) {
        if (dataHolderList == null) {
            this.transactionDataHolderList = new ArrayList();
        } else {
            this.transactionDataHolderList = dataHolderList;
        }
        notifyDataSetChanged();
    }

    public int getItemViewType(int i) {
        return ((DataHolder) this.transactionDataHolderList.get(i)).getTag();
    }

    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int tag) {
        if (tag == Tag.TRANSACTION_LIST_TRANSACTION_VIEW) {
            View transactionView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.transaction_view_data_item, viewGroup, false);
            transactionView.setOnClickListener(this.mOnClickListener);
            return new TransactionItemViewHolder(this, transactionView);
        } else if (tag == Tag.TRANSACTION_LIST_EMPTY_VIEW) {
            View tutorialView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.transaction_view_empty, viewGroup, false);
            return new TransactionEmptyViewHolder(this, tutorialView);
        } else {
            View tutorialView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.transaction_view_empty, viewGroup, false);
            return new TransactionEmptyViewHolder(this, tutorialView);
        }
    }

    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        DataHolder dataHolder = this.transactionDataHolderList.get(i);
        int itemViewType = viewHolder.getItemViewType();
        if (itemViewType == Tag.TRANSACTION_LIST_TRANSACTION_VIEW) {
            TransactionItemViewHolder transactionViewHolder = (TransactionItemViewHolder) viewHolder;
            if (dataHolder != null) {
                bindTransactionViewHolder(transactionViewHolder, (TransactionDataHolder) dataHolder, i);
                return;
            }
        }
    }

    public int getItemCount() {
        if (this.transactionDataHolderList == null) {
            return 0;
        }
        return this.transactionDataHolderList.size();
    }
}
