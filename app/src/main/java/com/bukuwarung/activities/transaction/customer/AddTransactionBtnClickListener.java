package com.bukuwarung.activities.transaction.customer;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.activities.addcustomer.detail.CustomerActivity;
import com.bukuwarung.activities.transaction.customer.add.AddTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.utils.RemoteConfigUtils;

final class AddTransactionBtnClickListener implements OnClickListener {
    final  CustomerTransactionActivity transactionsActivity;
    final int type;
    final
    CustomerTransactionActivity activity;
    CustomerEntity entity;

    AddTransactionBtnClickListener(CustomerTransactionActivity transactionsActivity, int type, CustomerEntity customerEntity, CustomerTransactionActivity activity) {
        this.transactionsActivity = transactionsActivity;
        this.type = type;
        this.activity = activity;
        this.entity = customerEntity;
    }

    public final void onClick(View view) {

        boolean isNewUtangFlow = !RemoteConfigUtils.INSTANCE.shouldShowOldUtangForm();
        if (isNewUtangFlow) {
            Intent intent = new Intent(this.transactionsActivity, CustomerActivity.class);
            intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, type);
            intent.putExtra(CustomerActivity.TRANSACTING_USER_ENTITY, entity);
            intent.putExtra(CustomerActivity.IS_EDIT_TRANSACTION, true);
            transactionsActivity.startActivity(intent);
            transactionsActivity.finish();
            return;
        }

        Intent intent = new Intent(this.transactionsActivity, AddTransactionActivity.class);
        Bundle bundle = new Bundle();
        bundle.putInt("TRANSACTION_TYPE", type);
        bundle.putString("OPERATION_TYPE", "ADD_TRANSACTION");
        bundle.putString("customer_id", this.transactionsActivity.getCustomerId());
        intent.putExtras(bundle);
        if (type > 0) {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_CREDIT);
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_CREDIT, activity);
        }
        else {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_DEBIT);
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_DEBIT, activity);

        }

        transactionsActivity.startActivity(intent);
    }
}
