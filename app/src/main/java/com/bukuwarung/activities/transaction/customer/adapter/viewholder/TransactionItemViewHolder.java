package com.bukuwarung.activities.transaction.customer.adapter.viewholder;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.customer.adapter.TransactionAdapter;
import com.bukuwarung.utils.TooltipBuilder;

import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip;


public final class TransactionItemViewHolder extends RecyclerView.ViewHolder {

    final TransactionAdapter adapter;

    private TextView creditAmount;
    private TextView date;
    private TextView debitAmount;
    private LinearLayout header;
    private TextView note;
    private TextView debitHeader;
    private ImageView debitTooltip;
    private TextView creditHeader;
    private TextView integratedLabel;
    private ImageView creditTooltip;

    private int tooltipTargetId = -1;
    private SimpleTooltip tooltip;

    public TransactionItemViewHolder(TransactionAdapter transactionAdapter, View view) {
        super(view);
        this.adapter = transactionAdapter;
        this.header = view.findViewById(R.id.listHeader);
        this.debitAmount = view.findViewById(R.id.debitAmount);
        this.note =  view.findViewById(R.id.note);
        this.creditAmount = view.findViewById(R.id.creditAmount);
        this.date = view.findViewById(R.id.date);
        this.debitHeader = view.findViewById(R.id.debitHeader);
        this.creditHeader = view.findViewById(R.id.creditHeader);
        this.debitTooltip = view.findViewById(R.id.debitTooltip);
        this.creditTooltip = view.findViewById(R.id.creditTooltip);
        this.integratedLabel = view.findViewById(R.id.tv_integrated_label);

        this.debitTooltip.setOnClickListener(view1 -> {
            showTooltip(debitTooltip,itemView.getResources().getString(R.string.tooltip_debit));
        });

        this.creditTooltip.setOnClickListener(view1 -> {
            showTooltip(creditTooltip,itemView.getResources().getString(R.string.tooltip_credit));
        });
    }

    private void showTooltip(ImageView anchor, String message) {
        try {
            //hide existing tooltip if any
            if(tooltip!=null && tooltip.isShowing())
                tooltip.dismiss();

            //no need to create tooltip again if click on anchor again
            if(tooltipTargetId==anchor.getId()) {
                tooltipTargetId = -1;
                return;
            }

            //recreate tooltip
            TooltipBuilder tooltipBuilder = TooltipBuilder.Companion
                    .builder(itemView.getContext())
                    .setAnchor(anchor)
                    .setText(message);
            tooltip = tooltipBuilder.build();
            tooltip.show();
            //ideally we should have a wrapper for simpleTooltip with id, simpletooltip, visibility fields and a builder
            tooltipTargetId = anchor.getId();

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public final LinearLayout getHeader() {
        return this.header;
    }

    public final TextView getDebitAmount() {
        return this.debitAmount;
    }

    public final TextView getNote() {
        return this.note;
    }

    public final TextView getCreditAmount() {
        return this.creditAmount;
    }

    public final TextView getDate() {
        return this.date;
    }

    public TextView getIntegratedLabel() {
        return integratedLabel;
    }
}