package com.bukuwarung.activities.transaction.customer.reminder;

import com.bukuwarung.Application;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.session.User;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

public final class UpdateCustomerPaymentReminderDate implements Runnable {

    final  String reminderDate;
    final CustomerTransactionActivity activity;

    public UpdateCustomerPaymentReminderDate(CustomerTransactionActivity activity, String reminderDate) {
        this.activity = activity;
        this.reminderDate = reminderDate;
    }

    public final void run() {
        try {
            AppAnalytics.trackEvent("customer_set_reminder_date", "date", this.reminderDate);
            CustomerRepository.getInstance(Application.getAppContext())
                    .setCustomerReminderDate(User.getUserId(), User.getDeviceId(),
                            activity.customerEntity.customerId, reminderDate);
        }catch (Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
