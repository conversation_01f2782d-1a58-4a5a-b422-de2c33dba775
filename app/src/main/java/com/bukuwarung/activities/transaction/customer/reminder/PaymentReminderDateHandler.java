package com.bukuwarung.activities.transaction.customer.reminder;

import android.annotation.SuppressLint;
import android.app.DatePickerDialog;
import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.DatePicker;

import com.bukuwarung.R;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.dialogs.duedate_selector_dialog.DueDateSelectorBottomSheetDialog;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.Utility;

import java.util.Calendar;

public final class PaymentReminderDateHandler implements OnClickListener {
    final CustomerTransactionActivity activity;

    public PaymentReminderDateHandler(CustomerTransactionActivity activity) {
        this.activity = activity;
    }

    public final void onClick(View view) {
//        if (Utility.isBlank(activity.customerEntity.phone)) {
//            dialogAddCustomerPhone(activity);
//            return;
//        }

        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        propBuilder.put("entryPoint", "customer");
        AppAnalytics.trackEvent("collecting_calendar_manage_collecting_date", propBuilder);

        if (Utility.isBlank(activity.currentReminderDate)) {
            setReminderDateAsText();
        } else {
            DueDateSelectorBottomSheetDialog.show(activity.getSupportFragmentManager());
        }
    }

    @SuppressLint("UseRequireInsteadOfGet")
    private void setReminderDateAsText() {
        final Calendar calendar = Calendar.getInstance();
        final String prefilledDate = activity.currentReminderDate;
        if (prefilledDate != null && !prefilledDate.isEmpty()) {
            try {
                calendar.setTime(DateTimeUtils.convertToDateYYYYMMDD(prefilledDate));
            } catch (Exception ex) {
                Log.e("PaymentReminderDate", "Exception", ex);
            }
        }
        final int y = calendar.get(Calendar.YEAR);
        final int m = calendar.get(Calendar.MONTH);
        final int d = calendar.get(Calendar.DATE);
        final Context context = activity;
        final DatePickerDialog datePickerDialog = new DatePickerDialog(
                context,
                R.style.DatePickerDialogParent,
                new PaymentReminderDatePicker(activity),
                y,
                m,
                d
        );
        if (!Utility.isBlank(activity.paymentReminderDate.getText().toString()) && activity.reminderDatePicker != null) {
            final DatePicker datePicker = activity.reminderDatePicker;
            final int year = datePicker.getYear();
            final int month = datePicker.getMonth();
            datePicker.setBackground(activity.getResources().getDrawable(R.drawable.bg_button));
            datePicker.setBackgroundColor(Color.WHITE);
            datePickerDialog.updateDate(year, month, datePicker.getDayOfMonth());
        }

        final Calendar calendarNow = Calendar.getInstance();
        final DatePicker datePicker = datePickerDialog.getDatePicker();
        datePickerDialog.setButton(DatePickerDialog.BUTTON_POSITIVE, context.getText(R.string.save), datePickerDialog);
        //subtracting 10sec to avoid validation error for min data, min data cannot me less than or equal to current timestamp
        //problem in using currentTimestamp -> fromDate: Sun Jun 21 01:53:21 GMT+07:00 2020 does not precede toDate: Sun Jun 21 01:53:21 GMT+07:00 2020
        //TODO: remove hacky solution of subtracting time to avoid validation error
        datePicker.setMinDate(calendarNow.getTimeInMillis() - 10000);
        datePickerDialog.show();
    }

}
