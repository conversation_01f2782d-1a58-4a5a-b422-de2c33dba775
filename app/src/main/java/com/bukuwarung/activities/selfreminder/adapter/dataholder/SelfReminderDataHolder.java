package com.bukuwarung.activities.selfreminder.adapter.dataholder;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.SelfReminderEntity;

public class SelfReminderDataHolder extends DataHolder {

    private final SelfReminderEntity selfReminderEntity;

    public SelfReminderDataHolder(SelfReminderEntity selfReminderEntity) {
        this.selfReminderEntity = selfReminderEntity;
        setTag(Tag.SELF_REMINDER_DATA_VIEW);
    }

    public final SelfReminderEntity getSelfReminderEntity() {
        return this.selfReminderEntity;
    }

    public String getName() {
        return this.selfReminderEntity.reminderId;
    }

}
