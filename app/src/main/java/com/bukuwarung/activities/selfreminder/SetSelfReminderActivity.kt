package com.bukuwarung.activities.selfreminder

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.TextView
import android.widget.TimePicker
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst.DEF_REMINDER_HOUR
import com.bukuwarung.constants.AppConst.DEF_REMINDER_MINUTE
import com.bukuwarung.database.entity.enums.ReminderCategory
import com.bukuwarung.database.repository.SelfReminderRepository
import com.bukuwarung.managers.local_notification.LocalNotificationManager
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.RemoteConfigUtils.NewHomePage.shouldShowNewHomePage
import kotlinx.android.synthetic.main.activity_set_self_reminder.*
import java.lang.ref.WeakReference
import java.util.*


class SetSelfReminderActivity : AppActivity(), TimePicker.OnTimeChangedListener {


    private var reminderId: String = ""
    private var notes: TextView? = null
    private var remainderHoursTime: String = DEF_REMINDER_HOUR.toString()
    private var remainderMinutesTime: String = DEF_REMINDER_MINUTE.toString()
    private var remainderCategory: Int = SelfReminderConstants.NO_CATEGORY_SELECTED
    private var isActive: Int = SelfReminderConstants.REMINDER_SOUND_ON

    private var from: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_set_self_reminder)
        setupView()

        timePicker.setDescendantFocusability(TimePicker.FOCUS_BLOCK_DESCENDANTS);
        timePicker.setIs24HourView(true)

        if (Build.VERSION.SDK_INT >= 23) {
            timePicker.hour = DEF_REMINDER_HOUR
            timePicker.minute = DEF_REMINDER_MINUTE // from api level 23
        } else {
            timePicker.currentHour = DEF_REMINDER_HOUR
            timePicker.currentMinute = DEF_REMINDER_MINUTE
        }
        val selfReminderRepo = SelfReminderRepository.getInstance(Application.getAppContext())

        val localNotificationManager = LocalNotificationManager(
            WeakReference(getApplicationContext())
        )
        localNotificationManager.start(null)

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

        remainderSoundSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked)
                isActive = SelfReminderConstants.REMINDER_SOUND_ON
            else
                isActive = SelfReminderConstants.REMINDER_SOUND_OFF
        }

        saveSelfRemainder.setOnClickListener {
            // Before creating reminder a category has to be selected
            if (remainderCategory == SelfReminderConstants.NO_CATEGORY_SELECTED) {
                Toast.makeText(this, R.string.no_remainder_category_selected, Toast.LENGTH_LONG).show()
            } else {

                val prop = AppAnalytics.PropBuilder()

                if (remainderCategory == ReminderCategory.UTANG.ordinal) {
                    reminderId = selfReminderRepo.saveReminder(remainderHoursTime.toInt(), remainderMinutesTime.toInt(), notes?.text.toString(), ReminderCategory.UTANG, isActive)
                    prop.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                    prop.put("status", "type")
                    prop.put("detail", "utang")
                    AppAnalytics.trackEvent("self_reminder_save", prop)
                } else if (remainderCategory == ReminderCategory.TRANSAKSI.ordinal) {
                    reminderId = selfReminderRepo.saveReminder(remainderHoursTime.toInt(), remainderMinutesTime.toInt(), notes?.text.toString(), ReminderCategory.TRANSAKSI, isActive)
                    prop.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                    prop.put("status", "type")
                    prop.put("detail", "transksi")
                    AppAnalytics.trackEvent("self_reminder_save", prop)
                } else {
                    reminderId = selfReminderRepo.saveReminder(remainderHoursTime.toInt(), remainderMinutesTime.toInt(), notes?.text.toString(), ReminderCategory.PAYMENT, isActive)
                    prop.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                    prop.put("status", "type")
                    prop.put("detail", "payment")
                    AppAnalytics.trackEvent("self_reminder_save", prop)
                }
                startAlarm()
                var intent = Intent(this, SelfReminderActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                startActivity(intent)
                finish()
            }
        }

        layoutUtangPiutang.setOnClickListener(View.OnClickListener {
            var prevSelectedCategory: Int = remainderCategory
            remainderCategory = ReminderCategory.UTANG.ordinal
            changeCategorySelected(prevSelectedCategory)
        })

        layoutTransaksi.setOnClickListener(View.OnClickListener {
            var prevSelectedCategory: Int = remainderCategory
            remainderCategory = ReminderCategory.TRANSAKSI.ordinal
            changeCategorySelected(prevSelectedCategory)
        })

        layoutPembayaran.setOnClickListener(View.OnClickListener {
            var prevSelectedCategory: Int = remainderCategory
            remainderCategory = ReminderCategory.PAYMENT.ordinal
            changeCategorySelected(prevSelectedCategory)
        })

        notes = findViewById(R.id.selfReminderNotes) as TextView
        timePicker?.setOnTimeChangedListener(this)
        updateTimeMessage(DEF_REMINDER_HOUR, DEF_REMINDER_MINUTE);

        // Initially select utang category
        remainderCategory = ReminderCategory.TRANSAKSI.ordinal
        changeCategorySelected(ReminderCategory.TRANSAKSI.ordinal)
    }

    override fun onBackPressed() {
        if (shouldShowNewHomePage()) {
            MainActivity.startActivitySingleTopToTab(this, TabName.HOME)
        } else {
            MainActivity.startActivitySingleTopToTab(this, TabName.OTHERS)
        }
    }

    private fun startAlarm() {
        val alarmManager = getSystemService(ALARM_SERVICE) as AlarmManager
        val alarmIntent = Intent(this, SelfReminderNotificationReceiver::class.java)
        alarmIntent.putExtra("id", reminderId)
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getBroadcast(this, System.currentTimeMillis().toInt(), alarmIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getBroadcast(this, System.currentTimeMillis().toInt(), alarmIntent, PendingIntent.FLAG_UPDATE_CURRENT)
        }
        val alarmStartTime = Calendar.getInstance()
        alarmStartTime[Calendar.HOUR_OF_DAY] = remainderHoursTime.toInt()
        alarmStartTime[Calendar.MINUTE] = remainderMinutesTime.toInt()
        alarmStartTime[Calendar.SECOND] = 0
        if (Calendar.getInstance().timeInMillis - alarmStartTime.timeInMillis > 0)
            alarmStartTime[Calendar.DATE] += 1
        alarmManager.setRepeating(AlarmManager.RTC_WAKEUP, alarmStartTime.getTimeInMillis(), AlarmManager.INTERVAL_DAY, pendingIntent)
    }

    override fun onTimeChanged(picker: TimePicker?, hour: Int, minute: Int) {
        updateTimeMessage(hour, minute);
    }

    fun updateTimeMessage(selectedHour: Int, selectedMin: Int) {
        val timeDiff = DateTimeUtils.differenceFromNow(selectedHour, selectedMin)
        val message = "Mengingatkan dalam " + timeDiff.hours + " jam " + timeDiff.minutes + " menit"
        remainderHoursTime = selectedHour.toString()
        remainderMinutesTime = selectedMin.toString()
        selfRemainderMessage!!.setText(message)
    }

    private fun setupView() {
        backBtn.setOnClickListener { onBackPressed() }
    }

    private fun changeCategorySelected(prevSelectedCategory: Int) {
        // Removing Hightlighting from the previously selected category
        var color: Int = ContextCompat.getColor(this, R.color.black_60)
        var isHighLight: Int = SelfReminderConstants.UNHIGHLIGHT_CATEGORY
        if (prevSelectedCategory == ReminderCategory.UTANG.ordinal) {
            changeColorUtangPiutang(color, isHighLight)
        } else if (prevSelectedCategory == ReminderCategory.TRANSAKSI.ordinal) {
            changeColorTransaksi(color, isHighLight)
        } else {
            changeColorPembayaran(color, isHighLight)
        }
        // Highlighting the selected category
        color = ContextCompat.getColor(this, R.color.blue_60)
        isHighLight = SelfReminderConstants.HIGHLIGHT_CATEGORY
        if (remainderCategory == ReminderCategory.UTANG.ordinal) {
            changeColorUtangPiutang(color, isHighLight)
            notes?.text = getString(R.string.default_note_self_reminder_utang)
        } else if (remainderCategory == ReminderCategory.TRANSAKSI.ordinal) {
            changeColorTransaksi(color, isHighLight)
            notes?.text = getString(R.string.default_note_self_reminder_transaksi)
        } else {
            changeColorPembayaran(color, isHighLight)
            notes?.text = getString(R.string.default_note_self_reminder_pembayaran)
        }
    }

    private fun changeColorUtangPiutang(color: Int, isHighLight: Int) {
        imageUtangPiutang.setColorFilter(color)
        textUtangPiutang.setTextColor(color)
        if (isHighLight == SelfReminderConstants.UNHIGHLIGHT_CATEGORY)
            layoutUtangPiutang.setBackgroundResource(R.drawable.product_edittext_bg)
        else
            layoutUtangPiutang.setBackgroundResource(R.drawable.product_edittext_bg_blue)
    }

    private fun changeColorTransaksi(color: Int, isHighLight: Int) {
        imageTransaksi.setColorFilter(color)
        textTransaksi.setTextColor(color)
        if (isHighLight == SelfReminderConstants.UNHIGHLIGHT_CATEGORY)
            layoutTransaksi.setBackgroundResource(R.drawable.product_edittext_bg)
        else
            layoutTransaksi.setBackgroundResource(R.drawable.product_edittext_bg_blue)
    }

    private fun changeColorPembayaran(color: Int, isHighLight: Int) {
        imagePembayaran.setColorFilter(color)
        textPembayaran.setTextColor(color)
        if (isHighLight == SelfReminderConstants.UNHIGHLIGHT_CATEGORY)
            layoutPembayaran.setBackgroundResource(R.drawable.product_edittext_bg)
        else
            layoutPembayaran.setBackgroundResource(R.drawable.product_edittext_bg_blue)
    }

}