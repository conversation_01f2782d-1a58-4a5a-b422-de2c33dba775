package com.bukuwarung.activities.selfreminder;

import static com.bukuwarung.managers.local_notification.LocalNotificationManager.NOTIFICATION_REMINDER_CHANNEL_ID;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.home.constants.MainActivityConstants;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.entity.SelfReminderEntity;
import com.bukuwarung.database.entity.enums.ReminderCategory;
import com.bukuwarung.database.repository.SelfReminderRepository;
import com.bukuwarung.managers.local_notification.LocalNotificationData;
import com.bukuwarung.managers.local_notification.LocalNotificationIcon;
import com.bukuwarung.managers.local_notification.LocalNotificationManager;
import com.bukuwarung.managers.local_notification.LocalNotificationStyle;

import java.util.Random;

// This class will be called at the remainder set time for sending notification to the user
public class SelfReminderNotificationReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {

        String reminderId = intent.getStringExtra("id");
        String title;
        SelfReminderEntity selfReminderEntity = SelfReminderRepository.getInstance(Application.getAppContext()).getReminderById(reminderId);

        // if the reminder is not deleted then push notification will be created
        if (selfReminderEntity != null) {
            if (selfReminderEntity.reminderCategory == ReminderCategory.UTANG.ordinal())
                title = context.getString(R.string.utang_piutang_reminder);
            else if (selfReminderEntity.reminderCategory == ReminderCategory.TRANSAKSI.ordinal())
                title = context.getString(R.string.transaksi_reminder);
            else
                title = context.getString(R.string.pembayaran_reminder);

            // Notification title and message is set here
            LocalNotificationData localNotificationData = new LocalNotificationData(
                    title,
                    selfReminderEntity.notes,
                    LocalNotificationIcon.DEFAULT
            );
            Intent i = new Intent(context, MainActivity.class);
            TabName tabName = TabName.TRANSACTION;
            if (selfReminderEntity.reminderCategory == ReminderCategory.UTANG.ordinal()) {
                tabName = TabName.CUSTOMER;
            } else if (selfReminderEntity.reminderCategory == ReminderCategory.TRANSAKSI.ordinal()) {
                tabName = TabName.TRANSACTION;
            } else if (selfReminderEntity.reminderCategory == ReminderCategory.PAYMENT.ordinal()) //TODO: "payment" naming no longer valid
                tabName = TabName.PAYMENT;
            i.putExtra(MainActivityConstants.TAB_NAME, tabName.name());
            i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            Random random = new Random();
            int randomNumber = random.nextInt(1000);
            final PendingIntent pendingIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivities(context, randomNumber, new Intent[]{i}, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivities(context, randomNumber, new Intent[]{i}, PendingIntent.FLAG_UPDATE_CURRENT);
            }

            LocalNotificationManager.Companion.showDefaultNotification(
                    Application.getAppContext(),
                    localNotificationData,
                    LocalNotificationStyle.BIG_TEXT,
                    pendingIntent, NOTIFICATION_REMINDER_CHANNEL_ID
            );
            AppAnalytics.trackEvent("self_reminder_notification_receive");
        }

    }
}
