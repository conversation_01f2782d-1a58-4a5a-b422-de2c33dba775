package com.bukuwarung.activities.selfreminder.adapter;

import android.app.Activity;
import android.view.MenuItem;
import android.widget.PopupMenu;

import com.bukuwarung.R;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.repository.SelfReminderRepository;

public class ReminderItemMenuClickHandler implements PopupMenu.OnMenuItemClickListener {
    private PopupMenu popupMenu;
    private Activity activity;
    final private String reminderId;
    public ReminderItemMenuClickHandler(Activity activity, PopupMenu popupMenu,String reminderId){
        this.popupMenu = popupMenu;
        this.activity = activity;
        this.reminderId = reminderId;
    }
    @Override
    public boolean onMenuItemClick(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.reminderDelete:
                SelfReminderRepository.getInstance(activity).deleteReminder(reminderId);
                AppAnalytics.trackEvent("self_reminder_delete");
                break;
            default:
                break;
        }
        return true;
    }
}
