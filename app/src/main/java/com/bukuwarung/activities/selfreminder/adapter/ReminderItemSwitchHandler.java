package com.bukuwarung.activities.selfreminder.adapter;

import android.widget.CompoundButton;

import com.bukuwarung.Application;
import com.bukuwarung.database.repository.SelfReminderRepository;
import com.bukuwarung.utils.NotificationUtils;

final class ReminderItemSwitchHandler implements CompoundButton.OnCheckedChangeListener {

    final String reminderId;

    ReminderItemSwitchHandler(String reminderId) {
        this.reminderId = reminderId;
    }

    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean b) {

        if(b) {
            SelfReminderRepository.getInstance(Application.getAppContext()).enableReminder(reminderId);
            NotificationUtils.alertDarkToastOnTop("Pengingat aktif",compoundButton.getContext());
//            AppAnalytics.trackEvent("self_reminder_turn_on");
        } else {
            SelfReminderRepository.getInstance(Application.getAppContext()).disableReminder(reminderId);
            NotificationUtils.alertDarkToastOnTop("Pengingat tidak aktif",compoundButton.getContext());
//            AppAnalytics.trackEvent("self_reminder_turn_off");
        }
    }
}
