package com.bukuwarung.activities.selfreminder.adapter;

import android.content.Context;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.PopupMenu;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.selfreminder.SelfReminderActivity;
import com.bukuwarung.activities.selfreminder.adapter.dataholder.SelfReminderDataHolder;
import com.bukuwarung.activities.selfreminder.adapter.viewholder.SelfReminderViewHolder;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.SelfReminderEntity;
import com.bukuwarung.database.entity.enums.ReminderCategory;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.Utility;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

public final class SelfReminderAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final Context context;
    private final SelfReminderActivity activity;
    public final RecyclerView selfReminderRecyclerView;

    public List<? extends DataHolder> selfReminderDataHolderList;

    public final Context getContext() {
        return this.context;
    }

    public SelfReminderAdapter(List<? extends DataHolder> selfReminderList, RecyclerView recyclerView, Context context, SelfReminderActivity activity) {
        this.selfReminderDataHolderList = selfReminderList;
        this.selfReminderRecyclerView = recyclerView;
        this.context = context;
        this.activity = activity;
    }

    public final class LastViewHolder extends RecyclerView.ViewHolder {

        public LastViewHolder(final View view) {
            super(view);
        }
    }

    private final void bindSelfReminderViewHolder(SelfReminderViewHolder selfReminderViewHolder, SelfReminderDataHolder selfReminderDataHolder, int i) {
        SelfReminderEntity selfReminderEntity = selfReminderDataHolder.getSelfReminderEntity();
        NumberFormat formatter = new DecimalFormat("00");

        selfReminderViewHolder.getTime().setText(formatter.format(selfReminderEntity.hour)+":"+formatter.format(selfReminderEntity.minute));
        selfReminderViewHolder.getStatus().setChecked(selfReminderEntity.isActive == 1);
        selfReminderViewHolder.getNotes().setText(selfReminderEntity.notes);
        ComponentUtil.setVisible(selfReminderViewHolder.getNotes(), !Utility.isBlank(selfReminderEntity.notes));
        ReminderCategory reminderCategoryEnum = ReminderCategory.values()[selfReminderEntity.reminderCategory];
        selfReminderViewHolder.getReminderTarget().setText(reminderCategoryEnum.toString());
        selfReminderViewHolder.getCatgoryIcon().setImageResource(reminderCategoryEnum.getIcon());
        selfReminderViewHolder.getStatus().setOnCheckedChangeListener(new ReminderItemSwitchHandler(selfReminderEntity.reminderId));
        selfReminderViewHolder.getDotMenu().setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                showItemMenu(view,selfReminderEntity.reminderId);
            }
        });
    }

    private final void showItemMenu(View dotMenu,String reminderId) {
        final PopupMenu menuDialog = new PopupMenu(activity.getBaseContext(), dotMenu);
        Context contextWrapper = new ContextThemeWrapper(getContext(), R.style.PopupMenu);
        menuDialog.getMenuInflater().inflate(R.menu.self_reminder_menu, menuDialog.getMenu());
        menuDialog.setOnMenuItemClickListener(new ReminderItemMenuClickHandler(this.activity, menuDialog, reminderId));
        menuDialog.show();
    }

    public final void setDataHolderList(List<? extends DataHolder> dataHolderList) {
        if (dataHolderList == null) {
            this.selfReminderDataHolderList = new ArrayList();
        } else {
            this.selfReminderDataHolderList = dataHolderList;
        }
        notifyDataSetChanged();
    }

    public int getItemViewType(int i) {
        return ((DataHolder) this.selfReminderDataHolderList.get(i)).getTag();
    }

    public long getItemId(int i) {
        DataHolder dataHolder = (DataHolder) this.selfReminderDataHolderList.get(i);
        if (dataHolder instanceof SelfReminderDataHolder) {
            return (dataHolder.getTag()+":"+((SelfReminderDataHolder) dataHolder).getSelfReminderEntity().reminderId).hashCode();
        } else {
            return (dataHolder.getTag()+":"+i).hashCode();
        }
    }

    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int tag) {
        if (tag == Tag.SELF_REMINDER_DATA_VIEW) {
            View selfReminderView = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.self_reminder_view_item, viewGroup, false);
            return new SelfReminderViewHolder( selfReminderView,this);
        }
        View inflate = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.activity_main_view_customer_end, viewGroup, false);
        return new LastViewHolder(inflate);
    }

    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        DataHolder dataHolder = this.selfReminderDataHolderList.get(i);
        int itemViewType = viewHolder.getItemViewType();
        if (itemViewType == Tag.SELF_REMINDER_DATA_VIEW) {
            SelfReminderViewHolder selfReminderViewHolder = (SelfReminderViewHolder) viewHolder;
            if (dataHolder != null) {
                bindSelfReminderViewHolder(selfReminderViewHolder, (SelfReminderDataHolder) dataHolder, i);
                return;
            }
        }
    }

    public int getItemCount() {
        if (this.selfReminderDataHolderList == null) {
            return 0;
        }
        return this.selfReminderDataHolderList.size();
    }

    onLongItemClickListener mOnLongItemClickListener;

    public void setOnLongItemClickListener(onLongItemClickListener onLongItemClickListener) {
        mOnLongItemClickListener = onLongItemClickListener;
    }

    public interface onLongItemClickListener {
        void ItemLongClicked(View v, int position);
    }
}
