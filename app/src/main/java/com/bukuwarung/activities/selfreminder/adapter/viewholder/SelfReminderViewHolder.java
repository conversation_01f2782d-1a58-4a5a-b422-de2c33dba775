package com.bukuwarung.activities.selfreminder.adapter.viewholder;

import android.view.View;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.selfreminder.adapter.SelfReminderAdapter;


public final class SelfReminderViewHolder extends RecyclerView.ViewHolder {

    private TextView time;
    private TextView reminderTarget;
    private TextView notes;
    private ImageView catgoryIcon;
    private ImageView dotMenu;
    private Switch status;

    final SelfReminderAdapter adapter;

    public SelfReminderViewHolder(@NonNull View view, SelfReminderAdapter adapter) {
        super(view);
        this.time = view.findViewById(R.id.selfRemainderTime);
        this.catgoryIcon = view.findViewById(R.id.selfRemainderCategoryImage);
        this.reminderTarget = view.findViewById(R.id.selfRemainderCategoryText);
        this.notes = view.findViewById(R.id.remainderNotes);
        this.status = view.findViewById(R.id.selfRemainderSwitch);
        this.dotMenu  =view.findViewById(R.id.icon_menu);
        this.adapter = adapter;
    }

    public TextView getTime() {
        return time;
    }

    public TextView getReminderTarget() {
        return reminderTarget;
    }

    public TextView getNotes() {
        return notes;
    }
    public Switch getStatus() {
        return status;
    }

    public ImageView getDotMenu() {
        return dotMenu;
    }
    public ImageView getCatgoryIcon() {
        return catgoryIcon;
    }

}