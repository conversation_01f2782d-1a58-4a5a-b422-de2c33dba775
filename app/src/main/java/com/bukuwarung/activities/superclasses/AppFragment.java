package com.bukuwarung.activities.superclasses;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.bukuwarung.Application;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;

import dagger.android.support.AndroidSupportInjection;

public class AppFragment extends Fragment {
    private String TAG;
    public SessionManager sessionManager;
    public FeaturePrefManager featureManager;

    public void onDestroyView() {
        super.onDestroyView();
    }

    public AppFragment() {
        StringBuilder sb = new StringBuilder();
        sb.append("``");
        sb.append(getClass().getSimpleName());
        this.TAG = sb.toString();
        sessionManager = SessionManager.getInstance();
        featureManager = FeaturePrefManager.getInstance();
        businessRepository = BusinessRepository.getInstance(Application.getAppContext());
    }

    public BookEntity getCurrentBook(){
        return BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
    }

    public BusinessRepository businessRepository;

    public final String getTAG() {
        return this.TAG;
    }

    public void onStart() {
        super.onStart();
    }

    public void onResume() {
        super.onResume();
    }

    public void onPause() {
        super.onPause();
    }

    public String getStringRes(int resId){
        return getContext().getString(resId);
    }
    public int getColor(int resId){
        return getActivity().getResources().getColor(resId);
    }
}
