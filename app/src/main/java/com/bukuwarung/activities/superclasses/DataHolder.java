package com.bukuwarung.activities.superclasses;

import com.bukuwarung.activities.expense.adapter.model.DailySummary;
import com.bukuwarung.activities.phonebook.model.RowHolder;
import com.bukuwarung.constants.Tag;


public abstract class DataHolder {

    public boolean isChecked = false;
    private int tag;

    public abstract String getName();

    public final int getTag() {
        return this.tag;
    }

    public final void setTag(int i) {
        this.tag = i;
    }

    public static final class CategoryRowHolder extends DataHolder {
        private final DailySummary daily;

        public CategoryRowHolder(DailySummary summary) {
            this.daily = summary;
            setTag(Tag.VIEW_CAT_SUMMARY);
        }

        public final DailySummary getCash() {
            return this.daily;
        }

        public String getName() {
            return this.daily.getName();
        }

        public String getExpense() {
            return this.daily.getExpense();
        }

        public String getIncome() {
            return this.daily.getIncome();
        }

        public Double getIncomeDbl() {
            return Double.valueOf(this.daily.getIncome());
        }

        public Double getExpenseDbl() {
            return Double.valueOf(this.daily.getExpense());
        }
    }

    public static final class DayDataHolder extends DataHolder {
        private final DailySummary daily;

        public DayDataHolder(DailySummary summary) {
            this.daily = summary;
            setTag(Tag.VIEW_DAY_SUMMARY);
        }

        public final DailySummary getCash() {
            return this.daily;
        }

        public String getName() {
            return this.daily.getName();
        }

        public String getExpense() {
            return this.daily.getExpense();
        }

        public String getIncome() {
            return this.daily.getIncome();
        }
    }

    public static final class NoResultRowHolder extends DataHolder {
        public String getName() {
            return " ";
        }

        public NoResultRowHolder() {
            setTag(Tag.VIEW_NO_RESULT);
        }
    }

    public static final class LastRowHolder extends DataHolder {
        public String getName() {
            return " ";
        }

        public LastRowHolder() {
            setTag(Tag.LAST_ROW);
        }
    }

    public static final class NoFilterResultRowHolder extends DataHolder {
        public String getName() {
            return " ";
        }

        public NoFilterResultRowHolder() {
            setTag(Tag.VIEW_NO_FILTER_RESULT);
        }
    }
}
