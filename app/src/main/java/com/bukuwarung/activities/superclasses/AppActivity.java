package com.bukuwarung.activities.superclasses;

import android.content.Context;
import android.os.Bundle;

import com.bukuwarung.Application;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.locale.LocaleManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.InputUtils;

import java.util.Objects;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import javax.annotation.Nullable;

/*
Base App activity to set debugging tag.
 */
public abstract class AppActivity extends AppCompatActivity {

    protected String TAG;
    protected SessionManager sessionManager;
    protected BusinessRepository businessRepository;

    private AnimationChoices animationChoices;

    public AppActivity() {
        super();

        // Default animation
        this.animationChoices = new DefaultAnim();
    }


    public AppActivity(AnimationChoices animationChoices) {
        super();

        this.animationChoices = animationChoices;
    }

    @Override
    public void finish() {
        super.finish();

        // DISABLE ANIM TRANSITION TEMPORARILY
//        final Pair<Integer, Integer> exitAnim = animationChoices.exit();
//        overridePendingTransition(exitAnim.getFirst(), exitAnim.getSecond());
    }

    @Override
    public void onCreate(Bundle bundle) {
        // DISABLE ANIM TRANSITION TEMPORARILY
//        final Pair<Integer, Integer> entranceAnim = animationChoices.entrance();
//        overridePendingTransition(entranceAnim.getFirst(), entranceAnim.getSecond());

        super.onCreate(bundle);

        this.TAG = getClass().getSimpleName();
        sessionManager = SessionManager.getInstance();
        businessRepository = BusinessRepository.getInstance(Application.getAppContext());
    }
    protected void setUpToolbarWithHomeUp(Toolbar toolbar){
        setSupportActionBar(toolbar);
        Objects.requireNonNull(getSupportActionBar()).setDisplayHomeAsUpEnabled(true);
        toolbar.setNavigationOnClickListener(v -> {
            InputUtils.hideKeyBoardWithCheck(this);
            onBackPressed();
        });
    }
    public BookEntity getCurrentBook(){
        return BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
    }

    public final String getTAG() {
        String str = this.TAG;
        return str;
    }

    public long getTimestamp(){
        return System.currentTimeMillis();
    }

    public void onResume() {
        super.onResume();
    }

    public void onPause() {
        super.onPause();
    }

    public void attachBaseContext(Context context) {
        super.attachBaseContext(LocaleManager.setLocale(context));
    }
}
