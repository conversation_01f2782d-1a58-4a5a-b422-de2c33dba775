package com.bukuwarung.activities.superclasses

import com.bukuwarung.R

internal abstract class AnimationChoices {

    abstract fun entrance(): Pair<Int, Int>
    abstract fun exit(): Pair<Int, Int>

}

/**
 * In & Out Animation
 */
internal class DefaultAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.slide_in, R.anim.slide_out)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.slide_in_inverted, R.anim.slide_out_inverted)

}

/**
 * Fade In & Fade Out Animation
 */
internal class FadeAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.fade_in, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.fade_out)

}

/**
 * Swipe Left & Swipe Right Animation
 */
internal class HorizontalSwipeAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.slide_left, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.slide_right)

}

/**
 * Swipe Left & Swipe Right Animation With Fade
 */
internal class HorizontalFadeSwipeAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.slide_left_fade_in, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.slide_right_fade_out)

}

/**
 * Swipe Up & Swipe Down Animation
 */
internal class VerticalSwipeAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.slide_up, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.slide_down)

}

/**
 * Swipe Up & Swipe Down Animation With Fade
 */
internal class VerticalFadeSwipeAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.slide_up_fade_in, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.slide_down_fade_out)

}

/**
 * Swipe Right & Swipe Down Animation
 */
internal class MixSwipeAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.slide_left, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.slide_down)

}

/**
 * Zoom In & Zoom Out Animation
 */
internal class ZoomAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.zoom_in, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.zoom_out)

}

/**
 * Bounce In & Bounce Out Animation
 */
internal class BounceAnim: AnimationChoices() {

    override fun entrance(): Pair<Int, Int> = Pair(R.anim.bounce_in, R.anim.nothing)

    override fun exit(): Pair<Int, Int> = Pair(R.anim.nothing, R.anim.bounce_out)

}