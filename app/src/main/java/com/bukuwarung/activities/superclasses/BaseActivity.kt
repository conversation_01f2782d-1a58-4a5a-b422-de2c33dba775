package com.bukuwarung.activities.superclasses

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import com.bukuwarung.locale.LocaleManager
import com.bukuwarung.utils.InputUtils
import dagger.android.AndroidInjection

abstract class BaseActivity: AppCompatActivity() {
    protected var TAG: String? = null
    abstract fun setViewBinding()
    abstract fun setupView()
    abstract fun subscribeState()

    protected fun setUpToolbarWithHomeUp(toolbar: Toolbar) {
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        toolbar.setNavigationOnClickListener {
            InputUtils.hideKeyBoardWithCheck(this)
            onBackPressed()
        }
    }

    @CallSuper
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setViewBinding()
        AndroidInjection.inject(this)
        setupView()
        subscribeState()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
    }

    override fun attachBaseContext(context: Context?) {
        super.attachBaseContext(LocaleManager.setLocale(context))
    }
}
