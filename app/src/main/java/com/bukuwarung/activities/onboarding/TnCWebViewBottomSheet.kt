package com.bukuwarung.activities.onboarding

import android.app.Activity
import android.app.Dialog
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.annotation.NonNull
import androidx.fragment.app.DialogFragment
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.FirebaseRepository
import com.bukuwarung.databinding.BottomsheetLoginTncBinding
import com.bukuwarung.utils.asVisibility
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class TnCWebViewBottomSheet(private val onDismiss: ((isAgree: Boolean) -> Unit)? = null): BottomSheetDialogFragment() {

    private lateinit var binding: BottomsheetLoginTncBinding

    companion object {
        const val TAG = "LoginTnCBottomSheet"

        const val url_key = "url"
        const val entry_point = "entry_point"
        const val link_type = "link_type"
        const val show_accept_button_key = "show_accept_button"

        fun newInstance(url: String, entryPoint: String, linkType: String, showAcceptButton: Boolean, onDismiss: ((isAgree: Boolean) -> Unit)? = null): TnCWebViewBottomSheet {
            val instance = TnCWebViewBottomSheet(onDismiss)

            // Supply url as an argument.
            val args = Bundle().apply {
                putString(url_key, url)
                putString(entry_point, entryPoint)
                putString(link_type, linkType)
                putBoolean(show_accept_button_key, showAcceptButton)
            }
            instance.arguments = args
            return instance
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        setFullScreen()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = BottomsheetLoginTncBinding.inflate(inflater, container, false)
        binding.apply {
            val url = arguments?.getString(url_key).toString()
            webView.webViewClient = WebViewClient()
            webView.loadUrl(url)

            val showAcceptButton = arguments?.getBoolean(show_accept_button_key) == true
            acceptBtn.apply {
                visibility = showAcceptButton.asVisibility()
                setOnClickListener {
                    FirebaseRepository.getInstance(context).setAgreedTnC(true)
                    onDismiss?.invoke(true)
                    dialog?.dismiss()
                }
            }

            back.setOnClickListener {
                onDismiss?.invoke(false)
                dismiss()
            }

            title.apply{
                val linkType = arguments?.getString(link_type)
                text = when(linkType){
                    AnalyticsConst.PRIVACY_POLICY -> context.getString(R.string.privacy_policy1)
                    AnalyticsConst.TNC -> context.getString(R.string.terms_and_condition)
                    else -> ""
                }
            }
        }

        return binding.root
    }

    @NonNull
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            setupRatio(bottomSheetDialog)
        }
        return dialog
    }

    private fun setupRatio(bottomSheetDialog: BottomSheetDialog) {
        val bottomSheet = bottomSheetDialog.findViewById<View>(R.id.design_bottom_sheet) as FrameLayout?
        val behavior: BottomSheetBehavior<*> = BottomSheetBehavior.from<FrameLayout?>(bottomSheet!!)
        val layoutParams = bottomSheet.layoutParams
        layoutParams.height = getBottomSheetDialogDefaultHeight()
        bottomSheet.layoutParams = layoutParams
        behavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    private fun getBottomSheetDialogDefaultHeight(): Int {
        return getWindowHeight()
    }

    private fun getWindowHeight(): Int {
        // Calculate window height for fullscreen use
        val displayMetrics = DisplayMetrics()
        (context as Activity?)!!.windowManager.defaultDisplay.getMetrics(displayMetrics)
        return displayMetrics.heightPixels
    }

    override fun getTheme(): Int {
        return R.style.BottomSheetDialogTheme_RoundCorner
    }

}

fun DialogFragment.setFullScreen() {
    dialog?.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
}