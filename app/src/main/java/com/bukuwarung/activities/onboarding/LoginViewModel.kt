package com.bukuwarung.activities.onboarding

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.amplitude.api.Amplitude
import com.bukuwarung.BuildConfig
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.auth.AuthUseCase
import com.bukuwarung.enums.AuthAction
import com.bukuwarung.enums.OtpRequestStatus
import com.bukuwarung.model.request.GuestSessionRequest
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.utils.AppIdGenerator
import com.bukuwarung.utils.LoginUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.wrapper.EventWrapper
import kotlinx.coroutines.launch
import org.json.JSONObject
import javax.inject.Inject

class LoginViewModel @Inject constructor(private var authUseCase: AuthUseCase) : BaseViewModel() {
    sealed class State {
        data class Error(val code: Int, val message: String, val phone: String, val method: String, val status: OtpRequestStatus,val isUserBlocked : Boolean = false) : State()
        data class GoToVerifyOtp(val phone: String, val countryCode: String, val method: String, val channel: List<String>?) : State()
        data class GoToVerifyPassword(
            val phone: String,
            val countryCode: String,
            val method: String,
            val channel: List<String>?,
            val userId: String,
            val showCaptcha: Boolean,
        ) : State()
        data class SkipUserLogin(val userId: String, val propBuilder: AppAnalytics.PropBuilder) : State()
        data class ProceedWithGuestSession(val userId: String) : State()
        data class SetSkipLoginButton(val skipButton: Boolean): State()
    }

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private var countryCode = SessionManager.getInstance().countryCode

    sealed class Event {
        data class OnCountryPicked(val countryCode: String) : Event()
        object OnCreateView : Event()
        data class OnRequestOTP(val phone: String, val method: String) : Event()
        data class OnCheckPasswordExists(val phone: String, val method: String) : Event()
//        data class OnCheckExisitingUser(val phone: String, val method: String) : Event()
        data class OnRequestGuestSession(val countryCode:String) : Event()
        object OnLoadAppConfigRequest: Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnCreateView -> handleOnCreateView()
            is Event.OnCountryPicked -> countryCode = event.countryCode
            is Event.OnRequestOTP -> requestOtp(event.phone, event.method)
//            is Event.OnCheckExisitingUser -> checkExistingUser(event.phone, event.method)
            is Event.OnCheckPasswordExists -> checkPasswordExists(event.phone, event.method)
            is Event.OnRequestGuestSession -> requestGuestSession(event.countryCode)
            is Event.OnLoadAppConfigRequest -> requestAppLoad()
            else -> {}
        }
    }

    private fun handleOnCreateView() {
        if (SessionManager.getInstance().deviceGUID.isNullOrBlank() && !Utility.isBlank(Amplitude.getInstance().deviceId)) {
            SessionManager.getInstance().deviceGUID = Amplitude.getInstance().deviceId
        }else if (SessionManager.getInstance().deviceGUID.isNullOrBlank()) {
            SessionManager.getInstance().deviceGUID = AppIdGenerator.resourceUUID()
        }
        SessionManager.getInstance().tryCount = 1
    }

    private fun requestOtp(phone: String, method: String) = launch {
        SessionManager.getInstance().loginMethod = method
        val lastCount: Int = SessionManager.getInstance().tryCount
        SessionManager.getInstance().tryCount = lastCount + 1
        val request = LoginRequest(
            countryCode = countryCode.replace("+", ""),
            method = method,
            action = AuthAction.LOGIN_OTP.value,
            deviceId = SessionManager.getInstance().deviceGUID,
            clientId = BukuWarungKeys.clientId.orEmpty(),
            clientSecret = BukuWarungKeys.clientSecret.orEmpty()
        )
        when (val result = authUseCase.getOtp(request, phone)) {
            is ApiSuccessResponse -> {
                if(result?.body?.token != null) {
                    SessionManager.getInstance().opToken = result.body.token
                    setState(State.GoToVerifyOtp(phone, countryCode, method, result.body.channel))
                } else {
                    setState(State.Error(-1, result.body.message, phone, method,result.body.status))
                }
            }
            is ApiErrorResponse -> {
                if (result.statusCode != 307) {
                    setState(State.Error(result.statusCode, result.errorMessage, phone, method, OtpRequestStatus.OTHER))
                }
            }

            is ApiEmptyResponse -> {}
        }
    }


    private fun checkExistingUser(phone: String, method: String) = launch {
        SessionManager.getInstance().loginMethod = method
        val lastCount: Int = SessionManager.getInstance().tryCount
        SessionManager.getInstance().tryCount = lastCount + 1
        val request = LoginRequest(
            countryCode = countryCode.replace("+", ""),
            method = method,
            action = AuthAction.LOGIN_OTP.value,
            deviceId = SessionManager.getInstance().deviceGUID,
            clientId = BukuWarungKeys.clientId.orEmpty(),
            clientSecret = BukuWarungKeys.clientSecret.orEmpty()
        )
        when (val result = authUseCase.getOtp(request,phone)) {
            is ApiSuccessResponse -> {
                if(result?.body?.token != null) {
                    SessionManager.getInstance().opToken = result.body.token
                    setState(State.GoToVerifyPassword(phone, countryCode, method, result.body.channel,"",false))
                } else {
                    setState(State.Error(-1, result.body.message, phone, method,result.body.status))
                }
            }
            is ApiErrorResponse -> {
                if (result.statusCode != 307) {
                    setState(State.Error(result.statusCode, result.errorMessage, phone, method, OtpRequestStatus.OTHER))
                }
            }

            is ApiEmptyResponse -> {}
        }
    }

    private fun checkPasswordExists(phone: String, method: String) = launch {
        SessionManager.getInstance().loginMethod = method
        val lastCount: Int = SessionManager.getInstance().tryCount
        SessionManager.getInstance().tryCount = lastCount + 1
        val request = LoginRequest(countryCode = countryCode.replace("+", ""),
                phone = phone,
        )
        when (val result = authUseCase.checkPasswordExists(request)) {
            is ApiSuccessResponse -> {
                if(result.body.isPasswordSet) {
                    setState(State.GoToVerifyPassword(phone, countryCode, method, null,result.body.userId, result.body.showCaptcha))
                } else {
                    requestOtp(phone,method)
                }
            }
            is ApiErrorResponse -> {
                if (result.statusCode != 307) {
                    if(result.statusCode.equals(403)){
                        val responseError = JSONObject(result.code)
                        val isUserBlocked = responseError.getBoolean("isBlocked")
                        setState(State.Error(result.statusCode, result.errorMessage, phone, method, OtpRequestStatus.OTHER,isUserBlocked))
                    }else{
                        setState(State.Error(result.statusCode, result.errorMessage, phone, method, OtpRequestStatus.OTHER))
                    }
                }
            }
            else -> {}
        }
    }

    private fun requestGuestSession(countryCode: String) = launch {
        val request = GuestSessionRequest(client = AppConst.CLIENT_NAME,
                deviceId = SessionManager.getInstance().deviceGUID)
        when (val result = authUseCase.getGuestUserSession(request)) {
            is ApiSuccessResponse -> {
                if(result?.body?.idToken != null) {
                    SessionManager.getInstance().sessionToken = result.body.sessionToken
                    var userId:String = result.body.userId?:"";
                    var customToken:String = result.body.idToken?:"";
                    SessionManager.getInstance().isGuestUser(true)
                    val authResult = authUseCase.signInToFirebaseAuth(customToken)
                    if (authResult == null) {
                        return@launch
                    }
                    val tokenResult = authUseCase.updateRefreshToken(false, userId) ?: return@launch
                    val idToken = tokenResult.token ?: return@launch
                    SessionManager.getInstance().bukuwarungToken = idToken
                    SessionManager.getInstance().trackGuestLoginSuccess(true);
                    SetupManager.getInstance().hasRestored(true)
                    LoginUtils.prepareSession(countryCode, userId, "");
                    val propBuilder = AppAnalytics.PropBuilder()
                    propBuilder.put(AnalyticsConst.USER_ID, if(SessionManager.getInstance().isLoggedIn()) User.getUserId() else "")
                    propBuilder.put(AnalyticsConst.GUEST_USER_ID, userId)
                    propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
                    setState(State.SkipUserLogin(userId,propBuilder))
                } else {
                    setState(State.Error(-1, "", "", "", OtpRequestStatus.OTHER))
                }
            }
            is ApiErrorResponse -> {
                setState(State.Error(result.statusCode, result.errorMessage, "", "", OtpRequestStatus.OTHER))
            }
            else -> {}
        }
    }

    private fun requestAppLoad() = launch {
        val result = authUseCase.loadAppConfiguration()
        setState(State.SetSkipLoginButton(result))
    }

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }
}