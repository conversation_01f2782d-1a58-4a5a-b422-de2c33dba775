package com.bukuwarung.activities.onboarding

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.databinding.GeneralLoadingDialogLayoutBinding
import com.bukuwarung.datasync.AppBookSyncManager
import com.bukuwarung.datasync.AppCustomerSyncManager
import com.bukuwarung.datasync.AppTransSyncManager
import com.bukuwarung.utils.CoroutineHelper
import com.bukuwarung.preference.SyncManager
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.utils.LoginUtils
import com.bumptech.glide.Glide
import com.google.firebase.crashlytics.FirebaseCrashlytics

class GeneralLoadingDialog : DialogFragment() {

    companion object {
        const val TAG = "GeneralLoadingDialog"
        private const val DIALOG_TYPE = "dialog_type"

        fun createInstance(dialogType: DialogType = DialogType.GENERIC) =
            GeneralLoadingDialog().apply {
                arguments = Bundle().apply { putSerializable(DIALOG_TYPE, dialogType) }
                coroutineHandler = CoroutineHelper()
            }
    }

    private lateinit var binding: GeneralLoadingDialogLayoutBinding
    private var dialogType: DialogType? = DialogType.GENERIC
    private var coroutineHandler: CoroutineHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        retainInstance = true
        setStyle(STYLE_NORMAL, R.style.AppTheme_FullScreenDialog)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = GeneralLoadingDialogLayoutBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = false
        dialogType = arguments?.getSerializable(DIALOG_TYPE) as DialogType?
        initView()
    }

    override fun onStop() {
        coroutineHandler!!.cancel()
        super.onStop()
    }

    fun initView() {
        when (dialogType) {
            DialogType.GENERIC -> {
                binding.imgIcon.visibility = View.GONE
                binding.tvTitle.apply {
                    visibility = View.VISIBLE
                    text = context.getString(R.string.please_wait_ya)
                }
                binding.tvMessage.apply {
                    visibility = View.VISIBLE
                    text = context.getString(R.string.we_are_preparing_next_page_for_you)
                }
                binding.progressbarCircle.visibility = View.VISIBLE
                binding.progressbarHorizontal.visibility = View.GONE
                binding.tvWarning.visibility = View.GONE
            }
            DialogType.OTP_VERIFICATION -> {
                binding.imgIcon.apply {
                    visibility = View.VISIBLE
                    Glide.with(this).load(R.drawable.otp_verify_icon).into(this)
                }
                binding.tvTitle.apply {
                    visibility = View.VISIBLE
                    text = context.getString(R.string.please_wait_ya)
                }
                binding.tvMessage.apply {
                    visibility = View.VISIBLE
                    text = context.getString(R.string.we_are_doing_verification)
                }
                binding.progressbarCircle.visibility = View.VISIBLE
                binding.progressbarHorizontal.visibility = View.GONE
                binding.tvWarning.visibility = View.GONE
            }
            DialogType.DATA_RESTORE -> {
                binding.imgIcon.apply {
                    visibility = View.VISIBLE
                    Glide.with(this).load(R.drawable.ic_data_restore).into(this)
                }

                binding.tvTitle.apply {
                    visibility = View.VISIBLE
                    text = context.getString(R.string.data_backup_found)
                }

                binding.tvMessage.apply {
                    visibility = View.VISIBLE
                    text = context.getString(R.string.we_are_restoring_your_data)
                }

                binding.progressbarCircle.visibility = View.GONE
                binding.progressbarHorizontal.visibility = View.VISIBLE
                binding.tvWarning.visibility = View.VISIBLE
                startDataRestore()
            }
            DialogType.DATA_RESTORE_COMPLETED -> {
                binding.imgIcon.apply {
                    visibility = View.VISIBLE
                    Glide.with(this).load(R.drawable.data_restore_success).into(this)
                }
                binding.tvTitle.apply {
                    visibility = View.VISIBLE
                    text = context.getString(R.string.data_restore_completed)
                }
                binding.tvMessage.visibility = View.GONE
                binding.progressbarCircle.visibility = View.GONE
                binding.progressbarHorizontal.visibility = View.GONE
                binding.tvWarning.visibility = View.GONE
            }
            else -> {}
        }
    }

    fun setType(newType: DialogType) {
        dialogType = newType
    }

    private fun startDataRestore() {
        if (!SyncManager.getInstance().hasDataRestored()) {
            //NewAsyncFirstDataRestore(this, requireActivity(), binding.progressbarHorizontal).execute()
            coroutineHandler?.syncNewFirstDataRestore(this, activity!!, binding.progressbarHorizontal)
        } else {
            updateFirstSyncStatus()
        }
    }

    private fun updateFirstSyncStatus() {
        try {
            SetupManager.getInstance().hasRestored(true)
            AppAnalytics.trackEvent("load_homescreen_after_restore",false,false,false)
            val intent = Intent(activity, MainActivity::class.java)
            intent.putExtra(LoginUtils.IS_NEW_LOGIN_EXTRA, true)
            if (SyncManager.getInstance().hasMultipleBusiness()) {
                intent.putExtra(LoginUtils.HAS_MULTIPLE_BOOKS, true)
            }
            setType(DialogType.DATA_RESTORE_COMPLETED)
            initView()
            Handler().postDelayed({
                requireActivity().let {
                    it.startActivity(intent)
                    it.finish()
                }
                try {
                    dismiss()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }, 1000)
        } catch (e: Exception) {
            e.printStackTrace()
            FirebaseCrashlytics.getInstance().recordException(e)
        }
        try {
            AppTransSyncManager.getInstance().transSyncTime = System.currentTimeMillis()
            AppBookSyncManager.getInstance().bookSyncTime = System.currentTimeMillis()
            AppCustomerSyncManager.getInstance().customerSyncTime = System.currentTimeMillis()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun isAdded(fm: FragmentManager) : Boolean{
        return fm.findFragmentByTag(TAG) != null
    }

    enum class DialogType {
        GENERIC, OTP_VERIFICATION, DATA_RESTORE, DATA_RESTORE_COMPLETED
    }
}