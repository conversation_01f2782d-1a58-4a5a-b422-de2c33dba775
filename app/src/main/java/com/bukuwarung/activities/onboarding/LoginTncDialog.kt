package com.bukuwarung.activities.onboarding

import android.content.Context
import android.graphics.Color
import android.graphics.Outline
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewOutlineProvider
import android.webkit.WebViewClient
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import kotlinx.android.synthetic.main.layout_login_tnc.*

class LoginTncDialog(
    private val urlWebView: String,
    context: Context,
    private val callback: () -> Unit
) : BaseDialog(context, BaseDialogType.FULL_SCREEN) {

    init {
        setUseFullWidth(false)
        setCancelable(true)
    }

    override fun getResId(): Int {
        return R.layout.layout_login_tnc
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        webview_content.webViewClient = WebViewClient()
        webview_content.loadUrl(urlWebView)
        bt_okay.setOnClickListener {
            callback()
            dismiss()
        }
        this.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val provider: ViewOutlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 40f)
                }
            }
            cl_tnc.outlineProvider = provider
            cl_tnc.clipToOutline = true
        }
    }


}