package com.bukuwarung.activities.onboarding

import android.os.Build
import androidx.lifecycle.*
import com.auth0.android.jwt.JWT
import com.bukuwarung.Application
import com.bukuwarung.BukuWarungKeys
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.repository.FirebaseRepository
import com.bukuwarung.datasync.AppBookSyncManager
import com.bukuwarung.domain.auth.AuthUseCase
import com.bukuwarung.enums.AuthAction
import com.bukuwarung.enums.NotificationChannel
import com.bukuwarung.enums.OtpRequestStatus
import com.bukuwarung.model.request.GuestSessionRequest
import com.bukuwarung.model.request.LoginRequest
import com.bukuwarung.model.request.VerifyOtpLoginRequest
import com.bukuwarung.model.request.VerifyOtpRequest
import com.bukuwarung.preference.SyncManager
import com.bukuwarung.session.AccountingMigrationHelper.Companion.initialDataMigration
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.tutor.utils.Constants
import com.bukuwarung.utils.LoginUtils
import com.bukuwarung.utils.NumberUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.wrapper.EventWrapper
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.DocumentChange
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject


class VerifyOtpViewModel @Inject constructor(
    private var authUseCase: AuthUseCase,
    private var firebaseRepository: FirebaseRepository
) : BaseViewModel(), LifecycleObserver {

    companion object {
        const val FORBIDDEN_STATUS_CODE = 403
        const val BLACKLISTED_DEVICE_CODE = "40301"
    }

    sealed class State {
        data class Error(val code: Int, val message: String) : State()

        object WrongOtp : State()
        object FirebaseAuthError : State()
        object StartAfterVerifyOtp : State()
        object SuccessSignInFirebase : State()
        data class ShowLoading(val isLoading: Boolean) : State()
        object ProceedWithLogin : State()
        data class SkipUserLogin(val userId: String, val propBuilder: AppAnalytics.PropBuilder) : State()

        data class OtpChannelChanged(val channel: String) : State()
        object NewOtpRequested : State()
        object NewOtpRequestSuccess : State()
        data class OtpRequestError(val code: Int, val message: String, val phone: String, val method: String, val status: OtpRequestStatus) : State()
        data class OtpVerified(val token: String?, val status: OtpRequestStatus) : State()
    }

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state
    private var countryCode = ""
    private var phone = ""
    private var opsToken = ""
    var firstOtpRequested = false
    private var otpChannel = RemoteConfigUtils.getOtpChannel()

    sealed class Event {
        data class OnVerifyOtp(
            val phone: String, val countryCode: String, val otp: String,
            val androidId: String?, val imeiNumber: String?, val wideVineId: String?,
            val advertisingId: String?, val rooted: Boolean, val autoVerify: Boolean,
            val action: AuthAction = AuthAction.LOGIN_OTP
        ) : Event()

        data class OnCreateView(val countryCode: String, val phone: String) : Event()
        data class DeleteFirestoreData(val phone: String) : Event()
        data class AfterOtpVerify(
            val token: String?, val phone: String, val autoVerify: Boolean
        ) : Event()
        object OnRequestGuestSession : Event()

        data class RequestNewOtp(
            val isRetry: Boolean, val action: AuthAction = AuthAction.LOGIN_OTP
        ) : Event()
        data class RequestFirstOtp(val action: AuthAction) : Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnCreateView -> handleOnCreateView(event.countryCode, event.phone)
            is Event.OnVerifyOtp -> startVerifyOtp(event.phone, event.countryCode, event.otp, event.androidId, event.imeiNumber, event.wideVineId, event.advertisingId, event.rooted, event.autoVerify, event.action)
            is Event.AfterOtpVerify -> afterOtpVerify(event.token, event.phone, event.autoVerify)
            is Event.DeleteFirestoreData -> deleteFirestoreWaAuthData(event.phone)
            Event.OnRequestGuestSession -> requestGuestSession()
            is Event.RequestNewOtp -> requestNewOtp(event.isRetry, event.action)
            is Event.RequestFirstOtp -> requestFirstOtp(event.action)
        }
    }

    private fun requestNewOtp(isRetry: Boolean, action: AuthAction) = viewModelScope.launch(Dispatchers.IO) {
        if (action != AuthAction.SELLER_OTP_REQUEST) {
            SessionManager.getInstance().loginMethod = getSwitchedOtpChannel()
            val lastCount: Int = SessionManager.getInstance().tryCount
            SessionManager.getInstance().tryCount = lastCount + 1
        }
        val channel = if (isRetry) otpChannel else getSwitchedOtpChannel()
        val request = LoginRequest(
            countryCode = countryCode.replace("+", ""),
            method = channel,
            action = action.value,
            deviceId = SessionManager.getInstance().deviceGUID,
            clientId = BukuWarungKeys.clientId.orEmpty(),
            clientSecret = BukuWarungKeys.clientSecret.orEmpty()
        )
        when (val result = authUseCase.getOtp(request, phone)) {
            is ApiSuccessResponse -> {
                if (result.body.token != null) {
                    when (action) {
                        AuthAction.SELLER_OTP_REQUEST -> {
                            opsToken = result.body.token
                        }
                        else -> {
                            SessionManager.getInstance().opToken = result.body.token
                        }
                    }
                    otpChannel = channel
                    setState(State.OtpChannelChanged(otpChannel))
                    setState(State.NewOtpRequestSuccess)
                    setState(State.ShowLoading(false))
                } else {
                    setState(State.OtpRequestError(-1, "", phone, getSwitchedOtpChannel(), result.body.status))
                }
            }
            is ApiErrorResponse -> {
                setState(State.ShowLoading(false))
                setState(State.OtpRequestError(result.statusCode, result.errorMessage, phone, getSwitchedOtpChannel(), OtpRequestStatus.OTHER))
            }
            else ->{}
        }
    }

    private fun requestFirstOtp(action: AuthAction) = viewModelScope.launch(Dispatchers.IO) {
        firstOtpRequested = true
        val request = LoginRequest(
            countryCode = countryCode.replace("+", ""),
            method = otpChannel,
            action = action.value,
            deviceId = SessionManager.getInstance().deviceGUID,
            clientId = BukuWarungKeys.clientId.orEmpty(),
            clientSecret = BukuWarungKeys.clientSecret.orEmpty()
        )
        when (val result = authUseCase.getOtp(request,phone)) {
            is ApiSuccessResponse -> {
                if (result.body.token != null) {
                    when (action) {
                        AuthAction.SELLER_OTP_REQUEST -> {
                            opsToken = result.body.token
                        }
                        else -> {
                            SessionManager.getInstance().opToken = result.body.token
                        }
                    }
                    setState(State.ShowLoading(false))
                } else {
                    setState(State.OtpRequestError(-1, "", phone, getSwitchedOtpChannel(), result.body.status))
                }
            }
            is ApiErrorResponse -> {
                setState(State.ShowLoading(false))
                setState(State.OtpRequestError(result.statusCode, result.errorMessage, phone, getSwitchedOtpChannel(), OtpRequestStatus.OTHER))
            }

            is ApiEmptyResponse ->{}
        }
    }

    init {
        viewModelScope.launch {
            setState(State.OtpChannelChanged(otpChannel))
        }
    }

    private fun handleOnCreateView(countryCode: String, phone: String) {
        this.countryCode = countryCode
        this.phone = phone
        SessionManager.getInstance().invalidOtpCount = 1
    }

    private fun requestGuestSession() = viewModelScope.launch(Dispatchers.IO) {

        val request = GuestSessionRequest(client = AppConst.CLIENT_NAME,
                deviceId = SessionManager.getInstance().deviceGUID)
        when (val result = authUseCase.getGuestUserSession(request)) {
            is ApiSuccessResponse -> {
                if (result?.body?.idToken != null) {
                    SessionManager.getInstance().sessionToken = result.body.sessionToken
                    val userId: String = result.body.userId ?: ""
                    val customToken: String = result.body.idToken ?: ""
                    SessionManager.getInstance().isGuestUser(true)
                    authUseCase.signInToFirebaseAuth(customToken) ?: return@launch
                    val tokenResult = authUseCase.updateRefreshToken(false, userId) ?: return@launch
                    val idToken = tokenResult.token ?: return@launch
                    SessionManager.getInstance().bukuwarungToken = idToken
                    SessionManager.getInstance().trackGuestLoginSuccess(true)
                    SetupManager.getInstance().hasRestored(true)
                    LoginUtils.prepareSession("+$countryCode", userId, "")
                    val propBuilder = AppAnalytics.PropBuilder()
                    SurvicateAnalytics.setUserIdentifier()
                    propBuilder.put(AnalyticsConst.USER_ID, if (!SessionManager.getInstance().isGuestUser) User.getUserId() else "")
                    propBuilder.put(AnalyticsConst.GUEST_USER_ID, userId)
                    propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.VERIFY_PAGE)
                    setState(State.SkipUserLogin(userId, propBuilder))
                } else {
                    setState(State.Error(-1, ""))
                }
            }
            is ApiErrorResponse -> {
                setState(State.Error(result.statusCode, result.errorMessage))
            }
            else ->{}
        }
    }

    private fun startVerifyOtp(
        phone: String, countryCode: String, otp: String,
        androidId: String?, imeiNumber: String?, wideVineId: String?,
        advertisingId: String?, isRooted: Boolean = false, autoVerify: Boolean,
        action: AuthAction
    ) = viewModelScope.launch(Dispatchers.IO) {
        when (action) {
            AuthAction.SELLER_OTP_REQUEST -> {
                val request = VerifyOtpRequest(
                    action = AuthAction.VERIFY_SALES_CODE.name,
                    countryCode = countryCode,
                    deviceId = User.getDeviceId(),
                    otp = otp,
                    phone = phone,
                    clientId = BukuWarungKeys.clientId.orEmpty(),
                    clientSecret = BukuWarungKeys.clientSecret.orEmpty()
                )
                when (val result = authUseCase.verifyGenericOtp(opsToken, request)) {
                    is ApiSuccessResponse -> {
                        if (result.body.status != OtpRequestStatus.OTP_VERIFIED) {
                            setState(State.WrongOtp)
                            increaseTryCount()
                            setState(State.ShowLoading(false))
                            return@launch
                        }
                        setState(State.OtpVerified(result.body.token, result.body.status))
                    }
                    is ApiErrorResponse -> {
                        setState(State.Error(result.statusCode, result.errorMessage))
                        increaseTryCount()
                        setState(State.ShowLoading(false))
                    }
                    else -> {}
                }
            }
            else -> {
                val deviceId = SessionManager.getInstance().deviceGUID
                val request = VerifyOtpLoginRequest(
                    countryCode = countryCode,
                    phone = phone,
                    otp = otp,
                    clientId = BukuWarungKeys.clientId.orEmpty(),
                    clientSecret = BukuWarungKeys.clientSecret.orEmpty(),
                    deviceId = deviceId,
                    deviceBrand = Build.MANUFACTURER,
                    deviceModel = Build.MODEL,
                    androidId = androidId,
                    imeiNumber = imeiNumber,
                    wideVineId = wideVineId,
                    advertisingId = advertisingId,
                    rooted = isRooted,
                )
                when (val result = authUseCase.verifyOtp(SessionManager.getInstance().bureauSessionId, request)) {
                    is ApiSuccessResponse -> {
                        try {
                            if (result?.body?.success != true) {
                                setState(State.WrongOtp)
                                increaseTryCount()
                                setState(State.ShowLoading(false))
                                return@launch
                            }

                            val parsedJWT = JWT(result.body.idToken!!)
                            val claim = parsedJWT.getClaim("claims").asObject(ClaimStoreId::class.java)

                            claim?.let { SessionManager.getInstance().uuid = it?.auth_user_id }

                            afterOtpVerify(result?.body?.idToken, phone, autoVerify).join()

                            SessionManager.getInstance().sessionToken = result?.body?.sessionToken
                            SessionManager.getInstance().setSessionStart()
                            firebaseRepository.checkAgreedTnC()
                        } catch (e: Exception) {
                            FirebaseCrashlytics.getInstance().log(e.stackTraceToString())
                            setState(State.Error(-1, e.localizedMessage))

                            e.printStackTrace()
                            setState(State.ShowLoading(false))
                        }
                    }
                    is ApiErrorResponse -> {
                        if (result.statusCode == FORBIDDEN_STATUS_CODE && result.code.equals(BLACKLISTED_DEVICE_CODE)) {
                            // device is blacklisted.
                            setState(State.Error(result.statusCode, result.errorMessage))
                            return@launch
                        }
                        setState(State.Error(result.statusCode, result.errorMessage))
                        increaseTryCount()
                        setState(State.ShowLoading(false))
                    }
                    else -> {}
                }
            }
        }
    }

    private fun afterOtpVerify(
        token: String?, phone: String, autoVerify: Boolean
    ) = viewModelScope.launch {
        token ?: return@launch
        setState(State.ShowLoading(true))
        setState(State.StartAfterVerifyOtp)
        SessionManager.getInstance().isMigrated = true
        val authResult = authUseCase.signInToFirebaseAuth(token)
        if (authResult == null) {
            AppAnalytics.trackEvent("failed_firebase_login", phone, token)
            setState(State.FirebaseAuthError)
            return@launch
        }
        setState(State.SuccessSignInFirebase)
        val tokenResult = authUseCase.updateRefreshToken(autoVerify, phone) ?: return@launch
        val idToken = tokenResult.token ?: return@launch
        //no handler if result null?
        //keep single place to set idToken, keep refreshing on each session
        SessionManager.getInstance().bukuwarungToken = idToken
        val phoneNumber: String? = NumberUtils.formatPhoneNumber(phone)
        SessionManager.getInstance().isGuestUser(false)
        if (SessionManager.getInstance().userId != null && SessionManager.getInstance().userId != phoneNumber) {
            SessionManager.getInstance().appState = 0
            SetupManager.getInstance().hasRestored(false)
            SyncManager.getInstance().setRestoredAll(false)
        }

        LoginUtils.prepareSession("+$countryCode", phoneNumber, token)
        AppBookSyncManager.getInstance().migrationStatus = Constants.MIGRATION_NOT_REQUESTED
        initialDataMigration(Application.getAppContext())
        setState(State.ProceedWithLogin)
    }

    private fun increaseTryCount() {
        val tryCount = SessionManager.getInstance().invalidOtpCount
        SessionManager.getInstance().invalidOtpCount = tryCount + 1
    }

    private fun deleteFirestoreWaAuthData(phone: String) {
        authUseCase.deleteFirestoreWaAuthData(phone)
    }

    private suspend fun setState(state: State) = withContext(Dispatchers.Main) {
        _state.value = EventWrapper(state)
    }

    fun listenToFirestoreWaAuth(): LiveData<List<DocumentChange>> {
        return authUseCase.listenToFirestoreWaAuth()
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun removeFirestoreListener() {
        authUseCase.removeFirestoreListener()
    }

    // switch from SMS to WA and vice versa
    private fun getSwitchedOtpChannel() = if (otpChannel == NotificationChannel.SMS.value) NotificationChannel.WA.value else NotificationChannel.SMS.value

}
