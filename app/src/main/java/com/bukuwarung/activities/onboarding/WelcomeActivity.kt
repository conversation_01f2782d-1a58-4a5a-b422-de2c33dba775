package com.bukuwarung.activities.onboarding

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.SCROLL_STATE_DRAGGING
import com.bukuwarung.R
import com.bukuwarung.WelcomeAdapter
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityWelcomeBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import kotlinx.android.synthetic.main.activity_welcome.*

class WelcomeActivity : AppActivity() {

    lateinit var binding: ActivityWelcomeBinding
    private val thresholdOffset = 0.5f
    private var scrollStarted = false
    private var checkDirection:Boolean = false

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        setContentView(R.layout.activity_welcome)

        val screens = RemoteConfigUtils.getWelcomeScreens()

        val gson: Gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<WelcomeScreens?>?>() {}.type
        val screenToDisplay: List<WelcomeScreens> = gson.fromJson(screens, jsonType)

        val images = arrayListOf(R.drawable.welcome1, R.drawable.welcome2, R.drawable.welcome3)
        val titles = arrayListOf(getString(R.string.title_1), getString(R.string.title_2), getString(R.string.title_3))
        val subTitles = arrayListOf(getString(R.string.subtitle_1), getString(R.string.subtitle_2), getString(R.string.subtitle_3))
        val shouldShowNewLoginScreen = RemoteConfigUtils.shouldShowNewLoginScreen()
        val welcomeAdapter = WelcomeAdapter(images, titles, subTitles, shouldShowNewLoginScreen)

        val handler = Handler()

        welcomeAdapter.setItem(screenToDisplay)
        welcome_viewpager.adapter = welcomeAdapter

        btn_login.setOnClickListener {
            goToLogin()
        }

        btn_skip.setOnClickListener {
            goToLogin()
        }

        btn_next.setOnClickListener {
            if (getCurrentItem() < screenToDisplay.size) {
                welcome_viewpager.currentItem = getCurrentItem() + 1
            }
        }


        welcome_viewpager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)

                val isLast = (position + 1 == welcomeAdapter.itemCount)

                if (isLast) {
                    showLogin()
                } else {
                    hideLogin()
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                /**
                 * The user swiped forward or back and we need to
                 * invalidate the previous handler.
                 */
                if (state == SCROLL_STATE_DRAGGING) {
                    handler.removeMessages(0)

                }
                if (!scrollStarted && state == ViewPager.SCROLL_STATE_DRAGGING) {
                    scrollStarted = true
                    checkDirection = true
                } else {
                    scrollStarted = false
                }
            }

        })

        TabLayoutMediator(tab_layout, welcome_viewpager) {tab, position ->
        }.attach()
    }

    private fun showLogin() {
        btn_next.hideView()
        btn_skip.hideView()
        btn_login.showView()
    }

    private fun hideLogin() {
        btn_next.showView()
        btn_skip.showView()
        btn_login.hideView()
    }

    private fun goToLogin() {
        val intent = Intent(this, NewLoginActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        startActivity(intent)
        finish()
    }

    private fun getCurrentItem(): Int {
        return welcome_viewpager.currentItem
    }

}