package com.bukuwarung.activities.onboarding;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.datasync.AppBookSyncManager;
import com.bukuwarung.datasync.AppCustomerSyncManager;
import com.bukuwarung.datasync.AppTransSyncManager;
import com.bukuwarung.utils.CoroutineHelper;
import com.bukuwarung.preference.SyncManager;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.LoginUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.material.button.MaterialButton;

public class InitialDataRestoreDialog extends Dialog {

    public static int UPDATE_PROGRESS = 1;
    TextView statusTv;
    LinearLayout preparingSetupLayout;
    LinearLayout restoringDataLayout;
    LinearLayout restoredLayout;
    MaterialButton completeBtn;
    Activity activity;
    private CoroutineHelper coroutineHandler;

    public InitialDataRestoreDialog(Context context, Activity activity) {
        super(context);
        this.activity = activity;
        this.coroutineHandler = new CoroutineHelper();
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_data_restore);
        Window window = getWindow();
        window.setLayout(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        getWindow().setGravity(Gravity.CENTER);
        this.setCancelable(false);
        ProgressBar progressBar = findViewById(R.id.progress_bar);
        preparingSetupLayout = findViewById(R.id.preparing_layout);
        completeBtn = findViewById(R.id.completeBtn);
        restoringDataLayout = findViewById(R.id.restoring_layout);
        restoredLayout = findViewById(R.id.recovered_layout);
        final Dialog dlg = this;
        completeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    dlg.dismiss();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });
        statusTv = findViewById(R.id.statusTv);
        setRecoveringState();
        if (!SyncManager.getInstance().hasDataRestored()) {
            //new AsyncFirstDataRestore(this, activity, progressBar, statusTv).execute(new Void[0]);
            coroutineHandler.syncFirstDataRestore(this, activity, progressBar);
        } else {
            updateFirstSyncStatus(true);
        }
    }

    @Override
    protected void onStop() {
        coroutineHandler.cancel();
        super.onStop();
    }

    public void updateStatus(final String statusText) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                statusTv.setText(statusText);
            }
        });
    }

    public void setRecoveringState() {
        ComponentUtil.show(preparingSetupLayout, !SyncManager.getInstance().hasRestoredBooks());
        ComponentUtil.show(restoringDataLayout, SyncManager.getInstance().hasRestoredBooks());
    }

    public void setCompleteState() {
        ComponentUtil.show(preparingSetupLayout, false);
        ComponentUtil.show(restoringDataLayout, false);
        ComponentUtil.show(restoredLayout, true);
    }

    private void updateFirstSyncStatus(boolean skip) {
        try {
            SetupManager.getInstance().hasRestored(true);
            AppAnalytics.trackEvent("load_homescreen_after_restore",false,false,false);
            Intent intent = new Intent(activity, MainActivity.class);
            intent.putExtra(LoginUtils.IS_NEW_LOGIN_EXTRA, true);
            if (SyncManager.getInstance().hasMultipleBusiness()) {
                intent.putExtra(LoginUtils.HAS_MULTIPLE_BOOKS, true);
            }
            final Handler handler = new Handler();
            final Dialog dlg = this;
            if (skip) {
                try {
                    dlg.dismiss();
                }catch (Exception e){
                    e.printStackTrace();
                }
                activity.startActivity(intent);
                activity.finish();
            } else {
                setCompleteState();
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        activity.startActivity(intent);
                        activity.finish();
                        try {
                            dlg.dismiss();
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }
                }, 1000);
            }

        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        try {
            AppTransSyncManager.getInstance().setTransSyncTime(System.currentTimeMillis());
            AppBookSyncManager.getInstance().setBookSyncTime(System.currentTimeMillis());
            AppCustomerSyncManager.getInstance().setCustomerSyncTime(System.currentTimeMillis());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
