package com.bukuwarung.activities.onboarding.form

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.databinding.CategoryOptionItemBinding
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.getDrawableCompat
import com.bumptech.glide.Glide

class OnBoardingCategoryAdapter(val onSelectionChange: (CategoryOption) -> Unit) : RecyclerView.Adapter<OnBoardingCategoryViewHolder>() {
    private val data = mutableListOf<CategoryOption>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OnBoardingCategoryViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = CategoryOptionItemBinding.inflate(inflater, parent, false)
        return OnBoardingCategoryViewHolder(parent.context, binding) {
            setSelected(it)
        }
    }

    override fun onBindViewHolder(holder: OnBoardingCategoryViewHolder, position: Int) {
        holder.bind(data[position])
    }

    override fun getItemCount(): Int = data.size

    fun setData(_data: List<CategoryOption>) {
        data.apply {
            clear()
            addAll(_data)
        }

        notifyDataSetChanged()
    }

    fun setSelected(categoryOption: CategoryOption) {
        // set previously selected to unselected
        data.filter { it.isSelected }.forEach {
            val position = data.indexOf(it)
            it.isSelected = false
            notifyItemChanged(position)
        }

        // set new selected
        (data.find { it.id == categoryOption.id } ?: return).apply {
            val position = data.indexOf(this)
            isSelected = true
            notifyItemChanged(position)
            onSelectionChange(this)
        }
    }
}

class OnBoardingCategoryViewHolder(val context: Context, val binding: CategoryOptionItemBinding, val onClick: (CategoryOption) -> Unit) : RecyclerView.ViewHolder(binding.root) {
    fun bind(categoryOption: CategoryOption) {
        binding.apply {
            tvCategoryName.text = categoryOption.name
            Glide.with(context).load(categoryOption.imageUrl).into(ivCategory)
            ivSelected.visibility = categoryOption.isSelected.asVisibility()
            val bg = context.getDrawableCompat(if (categoryOption.isSelected) R.drawable.category_selected_background else R.drawable.category_unselected_background)
            Glide.with(context).load(bg).into(ivBg)

            root.setOnClickListener {
                categoryOption.isSelected = !categoryOption.isSelected
                onClick(categoryOption)
            }
        }
    }
}