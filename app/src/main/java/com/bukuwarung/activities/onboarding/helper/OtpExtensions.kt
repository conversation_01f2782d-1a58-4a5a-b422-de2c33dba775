package com.bukuwarung.activities.onboarding.helper

import android.view.KeyEvent
import android.view.View
import android.widget.EditText
import androidx.core.widget.addTextChangedListener
import com.bukuwarung.R
import com.bukuwarung.utils.InputUtils

fun setupAutoMove(vararg editTexts: EditText, onOtpInputted: (String) -> Unit) {
    fun getAllOtpText(): String {
        return editTexts.joinToString(separator = "") { it.text.toString() }
    }

    editTexts.forEachIndexed { index, _ ->
        when (index) {
            0 -> editTexts[index].setupAutoMove(null, editTexts[index + 1]) { onOtpInputted(getAllOtpText()) }
            3 -> editTexts[index].setupAutoMove(editTexts[index - 1], null) { onOtpInputted(getAllOtpText()) }
            else -> editTexts[index].setupAutoMove(editTexts[index - 1], editTexts[index + 1]) { onOtpInputted(getAllOtpText()) }
        }
    }
}

private fun EditText.setupAutoMove(previousField: EditText?, nextField: EditText?, afterChanged: () -> Unit) {
    addTextChangedListener {
        val number = it.toString()
        when (id) {
            R.id.tv_one, R.id.tv_two, R.id.tv_three -> {
                if (number.isNotEmpty()) {
                    nextField?.isEnabled = true
                    nextField?.requestFocus()
                }
            }
        }
        afterChanged()
    }

    setOnKeyListener { _, keyCode, event ->
        if (event!!.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DEL && id != R.id.tv_one && text.isEmpty()) {
            previousField?.text = null
            previousField?.requestFocus()
            InputUtils.showKeyboard(context)
            isEnabled = false
            return@setOnKeyListener true
        }

        false
    }

    onFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
        if (!hasFocus && text.toString().isEmpty()){
            isEnabled = false
        }
    }
}