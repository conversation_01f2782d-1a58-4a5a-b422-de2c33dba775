package com.bukuwarung.activities.onboarding

import android.app.Activity
import android.content.Intent
import android.os.CountDownTimer
import android.provider.Settings
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.Gravity
import android.view.View
import android.widget.Toast
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.helper.AutoDetectOTP
import com.bukuwarung.activities.onboarding.helper.AutoDetectOTP.SmsCallback
import com.bukuwarung.activities.onboarding.helper.setupAutoMove
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.DETAIL_CLICK_RETRY
import com.bukuwarung.constants.AnalyticsConst.DETAIL_ENTERED_OTP
import com.bukuwarung.constants.AnalyticsConst.DETAIL_INVALID_OTP
import com.bukuwarung.constants.AnalyticsConst.DETAIL_OTP_CORRECT
import com.bukuwarung.constants.AnalyticsConst.DETAIL_WA_SUCCESS
import com.bukuwarung.constants.AnalyticsConst.EVENT_AUTO_DETECT_OTP
import com.bukuwarung.constants.AnalyticsConst.EVENT_INPUT_INVALID_OTP
import com.bukuwarung.constants.AnalyticsConst.EVENT_REGISTRATION_VERIFY_OTP
import com.bukuwarung.constants.AnalyticsConst.EVENT_WHATSAPP_AUTO_LOGIN
import com.bukuwarung.constants.AnalyticsConst.STATUS_CANCELLED
import com.bukuwarung.constants.AnalyticsConst.STATUS_COMPLETE
import com.bukuwarung.constants.AnalyticsConst.STATUS_FAIL
import com.bukuwarung.constants.AnalyticsConst.STATUS_F_AUTH
import com.bukuwarung.constants.AnalyticsConst.STATUS_RECEIVED_TOKEN
import com.bukuwarung.constants.AnalyticsConst.STATUS_START
import com.bukuwarung.constants.AnalyticsConst.STATUS_SUCCESS
import com.bukuwarung.constants.AppConst.ONE_SECOND
import com.bukuwarung.constants.AppConst.OTP_WAIT_TIME
import com.bukuwarung.constants.AppConst.PRODUCTION_FLAVOR
import com.bukuwarung.constants.RemoteConfigConst
import com.bukuwarung.database.repository.FirebaseRepository
import com.bukuwarung.databinding.ActivityVerifyOtpNewBinding
import com.bukuwarung.dialogs.HelpDialog
import com.bukuwarung.domain.auth.AuthUseCase
import com.bukuwarung.enums.AuthAction
import com.bukuwarung.enums.NotificationChannel
import com.bukuwarung.feature.login.createPassword.screen.CreateNewPasswordActivity
import com.bukuwarung.feature.login.createPassword.screen.ForgotPasswordActivity
import com.bukuwarung.feature.onboarding.form.newScreen.NewOnBoardingFormActivity
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormActivity
import com.bukuwarung.session.AuthHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui.BukuAgenDialog
import com.bukuwarung.utils.DeviceUtils
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.LoginUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.WhatsAppUtils
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.boldText
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.getDrawableCompat
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.bukuwarung.utils.tryToGetValueOrDefault
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.FirebaseFirestore
import dagger.hilt.android.AndroidEntryPoint
import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEvent
import java.util.regex.Matcher
import java.util.regex.Pattern
import javax.inject.Inject

@AndroidEntryPoint
class NewVerifyOtpActivity : BaseActivity() {

    companion object {
        const val STATUS_CODE_403 = 403
        const val STATUS_CODE_500 = 500
        const val STATUS_CODE_DEFAULT = -1
        const val OTP_LENGTH = 4
        private const val VERIFY_OTP_PARAM_PHONE = "phone"
        private const val VERIFY_OTP_PARAM_OTP = "otp"
        private const val VERIFY_OTP_PARAM_COUNTRY_CODE = "country"
        private const val OTP_USE_CASE = "otp_use_case"
        private const val IS_FORGOT_PASSWORD = "is_forgot_password"

        const val OTP_STATUS = "otp_status"
        const val OTP_TOKEN = "otp_token"

        enum class UseCase {
            VERIFY_OTP_LOGIN,
            VERIFY_OTP_FORGOT_PASSWORD,
            VERIFY_OTP_SALES_CODE
        }

        fun createIntent(
            origin: Activity?,
            phone: String?,
            otpCode: String?,
            countryCode: String,
            useCase: UseCase = Companion.UseCase.VERIFY_OTP_LOGIN
        ): Intent {
            val intent = Intent(origin, NewVerifyOtpActivity::class.java)
            intent.putExtra(VERIFY_OTP_PARAM_PHONE, phone)
            intent.putExtra(VERIFY_OTP_PARAM_OTP, otpCode)
            intent.putExtra(VERIFY_OTP_PARAM_COUNTRY_CODE, countryCode.replace("+", ""))
            intent.putExtra(OTP_USE_CASE, useCase)
            return intent
        }
    }

    private lateinit var binding: ActivityVerifyOtpNewBinding
    private val phone by lazy { intent.getStringExtra(VERIFY_OTP_PARAM_PHONE).orEmpty() }
    private val countryCode by lazy { intent.getStringExtra(VERIFY_OTP_PARAM_COUNTRY_CODE).orEmpty() }
    private var isForgotPassword: Boolean = false
    private var autoVerify = false
    private var cTimer: CountDownTimer? = null
    private var otpChannel = RemoteConfigUtils.getOtpChannel()
    private val showDefaultOtpScreen = RemoteConfigUtils.shouldShowDefaultOtpScreen()
    private var loadingDialog: GeneralLoadingDialog? = null
    private var isShowingDialog = false
    private val useCase by lazy { intent.getSerializableExtra(OTP_USE_CASE) as? UseCase }
    private var authAction = AuthAction.LOGIN_OTP

    @Inject
    lateinit var viewModel: VerifyOtpViewModel

    @Inject
    lateinit var authUseCase: AuthUseCase

    //timer function
    private var autoDetectOTP: AutoDetectOTP? = null
    private var autoDetect = false

    override fun setViewBinding() {
        binding = ActivityVerifyOtpNewBinding.inflate(layoutInflater)
        if (useCase == UseCase.VERIFY_OTP_LOGIN || useCase == UseCase.VERIFY_OTP_FORGOT_PASSWORD) createBureauEventId()
        setContentView(binding.root)
    }

    override fun setupView() {
        lifecycle.addObserver(viewModel)
        with(binding) {
            backBtn.setSingleClickListener { onBackPressed() }
            btnHelp.setSingleClickListener { openWaBotHelp() }
            btnRetryRequestOtp.setSingleClickListener { tryOTPAgain() }
            otpField.tvOne.text = null
            otpField.tvTwo.text = null
            otpField.tvThree.text = null
            otpField.tvFour.text = null
            //TODO: is this actually used? I see no activities sending "token"
            if (intent.hasExtra("token")) {
                val intent = intent
                val token = intent.getStringExtra("token")
                autoVerify = true
                showVerifying(true)
                viewModel.onEventReceived(VerifyOtpViewModel.Event.AfterOtpVerify(token, phone, autoVerify))
            } else if (intent.hasExtra(VERIFY_OTP_PARAM_PHONE)) {
                val intent = intent
                isForgotPassword = intent.getBooleanExtra(IS_FORGOT_PASSWORD,false)

            } else {
                callLoginActivity()
                return
            }
            viewModel.onEventReceived(VerifyOtpViewModel.Event.OnCreateView(countryCode, phone))
            val otpChannelUsed =
                if (otpChannel == NotificationChannel.SMS.value) getString(R.string.sms) else getString(
                    R.string.whatsapp
                )
            tvOtpMessage.apply {
                val phoneNum = if (phone.startsWith("62")) "+$phone" else "+$countryCode$phone"
                val message =
                    getString(R.string.otp_message_placeholder2, otpChannelUsed, phoneNum)
                val spannableStringBuilder = SpannableStringBuilder(message)
                text = spannableStringBuilder.boldText(otpChannelUsed)
            }
            btnSwitchOtpChannel.apply {
                val newChannel =
                    if (otpChannel == NotificationChannel.SMS.value) getString(R.string.whatsapp) else getString(
                        R.string.sms
                    )
                val formattedText =
                    getString(R.string.otp_change_channel_placeholder, newChannel)
                text = formattedText
                isEnabled = false
            }
            if (!showDefaultOtpScreen) {
                Toast.makeText(
                    this@NewVerifyOtpActivity,
                    getString(R.string.otp_sent_placeholder, otpChannelUsed),
                    Toast.LENGTH_SHORT
                ).apply {
                    setGravity(Gravity.TOP, 0, 20)
                }.show()
            }
            otpField.tvTwo.isEnabled = false
            otpField.tvThree.isEnabled = false
            otpField.tvFour.isEnabled = false
            setupAutoMove(otpField.tvOne, otpField.tvTwo, otpField.tvThree, otpField.tvFour) {
                if (it.length == OTP_LENGTH) {
                    verifyOtp(it)
                }
            }

            when (useCase) {
                UseCase.VERIFY_OTP_LOGIN -> {
                    setAutoDetectOTP()
                    performUserOnboardingRequirementCheck(phone)
                }
                UseCase.VERIFY_OTP_FORGOT_PASSWORD -> {
                    isForgotPassword = true
                    setAutoDetectOTP()
                    performUserOnboardingRequirementCheck(phone)
                }
                UseCase.VERIFY_OTP_SALES_CODE -> {
                    authAction = AuthAction.SELLER_OTP_REQUEST
                    groupSkipLogin.hideView()
                    if (!viewModel.firstOtpRequested) {
                        viewModel.onEventReceived(
                                VerifyOtpViewModel.Event.RequestFirstOtp(authAction)
                        )
                    }
                }
                null -> {
                    callLoginActivity()
                    return
                }
            }
        }

        setupKeyboardListener()
        startTimer()
    }

    private fun getDefaultEventProps() = AppAnalytics.PropBuilder().apply {
        put(AnalyticsConst.ENTRY_POINT, useCase?.name)
    }

    private fun registerForgotPasswordVerifyOtp(resendCount: Int) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.RESEND_COUNT, resendCount)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_FORGOT_PASSWORD_ASK_OTP, propBuilder)
    }

    private fun performUserOnboardingRequirementCheck(phone: String) {
        if (Utility.isBlank(phone) || (Utility.isEqual(
                phone,
                SessionManager.getInstance().userIdForExistingBusinessCheck
            ) && SessionManager.getInstance().hasExistingBusiness())
        ) {
            return
        }

        SessionManager.getInstance().setExistingBusinessFlag(false)
        SessionManager.getInstance().userIdForExistingBusinessCheck = ""
        FirebaseFirestore
            .getInstance().collection("book_store")
            .whereEqualTo("ownerId", phone)
            .get()
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val itemCount = task.result.size()
                    SessionManager.getInstance().setExistingBusinessFlag(itemCount > 0)
                    SessionManager.getInstance().userIdForExistingBusinessCheck = phone
                }
            }
    }

    private fun setupKeyboardListener() {
        KeyboardVisibilityEvent.setEventListener(
            this
        ) { isOpen ->
            if (!isOpen) {
                binding.btnHelp.requestFocus()
            }
        }

        binding.otpField.tvOne.apply {
            requestFocus()
            setOnFocusChangeListener { _, hasFocus ->
                background = if (hasFocus || this.text.toString().isNotEmpty()) {
                    <EMAIL>(R.drawable.otp_field_enabled)
                } else {
                    <EMAIL>(R.drawable.otp_field_disabled)
                }
            }
        }
    }

    private fun setAutoDetectOTP() {
        try {
            autoDetectOTP = AutoDetectOTP(this)
            autoDetectOTP?.startSmsRetriver(object : SmsCallback {
                override fun connectionfailed() {
//                    AppAnalytics.trackEvent(EVENT_AUTO_DETECT_OTP, getDefaultEventProps().apply {
//                        put(AnalyticsConst.STATUS, STATUS_CONNECTION_FAIL)
//                    })
                }

                override fun connectionSuccess(aVoid: Void?) {
//                    AppAnalytics.trackEvent(EVENT_AUTO_DETECT_OTP, getDefaultEventProps().apply {
//                        put(AnalyticsConst.STATUS, STATUS_CONNECTED)
//                        put(
//                            AnalyticsConst.DETAIL,
//                            AutoDetectOTP.getHashCode(this@NewVerifyOtpActivity)
//                        )
//                    })
                }

                override fun smsCallback(sms: String) {
                    try {
                        if (sms.contains("adalah") || sms.contains("BukuWarung")) {
                            val pattern: Pattern = Pattern.compile("(\\d{4})")
                            val matcher: Matcher = pattern.matcher(sms)
                            var otp: String? = ""
                            if (matcher.find()) {
                                otp = matcher.group(0) // 4 digit number
                            }
                            otp?.let {
                                AppAnalytics.trackEvent(
                                    EVENT_AUTO_DETECT_OTP,
                                    getDefaultEventProps().apply {
                                        put(AnalyticsConst.STATUS, STATUS_SUCCESS)
                                    }
                                )
                                fillOtp(it)
                            }
                        }
                    } catch (ex: Exception) {
                        FirebaseCrashlytics.getInstance().recordException(ex)
                    }
                }
            })
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }

    private fun fillOtp(otp: String) {
        binding.otpField.tvOne.apply {
            isEnabled = true
            setText(otp[0].toString())
        }
        binding.otpField.tvTwo.apply {
            isEnabled = true
            setText(otp[1].toString())
        }
        binding.otpField.tvThree.apply {
            isEnabled = true
            setText(otp[2].toString())
        }
        binding.otpField.tvFour.apply {
            isEnabled = true
            setText(otp[3].toString())
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is VerifyOtpViewModel.State.Error -> handleError(it.code, it.message)
                is VerifyOtpViewModel.State.ShowLoading -> showVerifying(it.isLoading)
                VerifyOtpViewModel.State.WrongOtp -> handleWrongOtp()
                VerifyOtpViewModel.State.FirebaseAuthError -> handleFirebaseAuthError()
                VerifyOtpViewModel.State.StartAfterVerifyOtp -> handleStartAfterVerifyOtp()
                VerifyOtpViewModel.State.SuccessSignInFirebase -> handleOnFirebaseSignIn()
                VerifyOtpViewModel.State.ProceedWithLogin -> if (RemoteConfigUtils.getAuthVariant() == 0) {
                    proceedWithLoginOld()
                } else {
                    proceedWithLogin()
                }
                is VerifyOtpViewModel.State.SkipUserLogin -> skipUserLogin(
                    it.propBuilder
                )
                is VerifyOtpViewModel.State.OtpChannelChanged -> otpChannel = it.channel
                VerifyOtpViewModel.State.NewOtpRequested -> {
                    cTimer?.cancel()
                    with(binding) {
                        btnSwitchOtpChannel.apply {
                            isEnabled = false
                            setTextColor(getColorCompat(R.color.black_40))
                        }
                        loadingDialog = GeneralLoadingDialog.createInstance()
                        loadingDialog?.show(supportFragmentManager, GeneralLoadingDialog.TAG)
                        isShowingDialog = true
                        btnRetryRequestOtp.hideView()
                        groupSkipLogin.hideView()
                        btnHelp.hideView()
                        tvErrorMessage.hideView()
                    }
                }
                VerifyOtpViewModel.State.NewOtpRequestSuccess -> {
                    loadingDialog?.dismiss()
                    isShowingDialog = false
                    setupView()
                    if (isForgotPassword) registerForgotPasswordVerifyOtp(SessionManager.getInstance().tryCount)
                }
                is VerifyOtpViewModel.State.OtpRequestError -> handleError(it.code, it.message)
                is VerifyOtpViewModel.State.OtpVerified -> {
                    setResult(Activity.RESULT_OK, Intent().apply {
                        putExtra(OTP_TOKEN, it.token)
                        putExtra(OTP_STATUS, it.status.value)
                    })
                    finish()
                }
            }
        }
    }

    private fun skipUserLogin(propBuilder: AppAnalytics.PropBuilder) {
        AppAnalytics.trackEvent(AnalyticsConst.SKIP_LOGIN, propBuilder)
        MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
    }

    private fun trackRegistrationVerifyOtpAnalytics(status: String, detail: String) {
        val propBuilder = getDefaultEventProps()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.DETAIL, detail)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        propBuilder.put(
            AnalyticsConst.SETTING_REASON,
            if (isForgotPassword) AnalyticsConst.FORGOT else AnalyticsConst.INITIAL
        )
        AppAnalytics.trackEvent(EVENT_REGISTRATION_VERIFY_OTP, propBuilder)
    }

    private fun trackRegistrationVerifyOtpAnalytics(status: String, detail: String, code: Int, message: String) {
        val propBuilder = getDefaultEventProps()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.DETAIL, detail)
        propBuilder.put("status_code", code)
        propBuilder.put("message", message)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LOGIN_PAGE)
        propBuilder.put(
            AnalyticsConst.SETTING_REASON,
            if (isForgotPassword) AnalyticsConst.FORGOT else AnalyticsConst.INITIAL
        )
        AppAnalytics.trackEvent(EVENT_REGISTRATION_VERIFY_OTP, propBuilder)
    }

    private fun trackRetryOtpRequest(currentMethod: String, newMethod: String, isChanged: Boolean) {
        val propBuilder = getDefaultEventProps().apply {
            put(AnalyticsConst.CURRENT_METHOD, currentMethod)
            put(AnalyticsConst.NEW_METHOD, newMethod)
            put(AnalyticsConst.IS_OTP_METHOD_CHANGED, isChanged)
        }

        AppAnalytics.trackEvent(AnalyticsConst.EVENT_RESEND_OTP_CLICKED, propBuilder)
    }

    private fun handleOnFirebaseSignIn() {
        if (autoVerify) {
            AppAnalytics.trackEvent(EVENT_WHATSAPP_AUTO_LOGIN, STATUS_COMPLETE, phone)
            trackRegistrationVerifyOtpAnalytics(STATUS_SUCCESS, DETAIL_WA_SUCCESS)
        } else {
            trackRegistrationVerifyOtpAnalytics(STATUS_SUCCESS, DETAIL_OTP_CORRECT)
        }
    }

    private fun handleStartAfterVerifyOtp() {
        trackRegistrationVerifyOtpAnalytics(STATUS_RECEIVED_TOKEN, DETAIL_ENTERED_OTP)
    }

    private fun handleFirebaseAuthError() {
        // If sign in fails, display a message to the user.
        onTimerFinish()
        binding.tvErrorMessage.showView()
        AppAnalytics.trackEvent(EVENT_INPUT_INVALID_OTP, STATUS_F_AUTH, phone)
        showVerifying(false)
    }

    private fun handleError(code: Int, message: String) {
        var errorMessage = message;
        when (code) {
            STATUS_CODE_403 -> {
                showDeviceBlacklistedDialog()
            }

            STATUS_CODE_500 -> {
                registerForgotPasswordOtpVerify(STATUS_FAIL, AnalyticsConst.SERVER_ERROR)
            }

            STATUS_CODE_DEFAULT -> {
                registerForgotPasswordOtpVerify(STATUS_FAIL, message)
                errorMessage = "-"
            }

            else -> {
                registerForgotPasswordOtpVerify(STATUS_FAIL, AnalyticsConst.WRONG_OTP)
            }
        }
        trackRegistrationVerifyOtpAnalytics(STATUS_FAIL, DETAIL_INVALID_OTP, code, message)
        binding.tvErrorMessage.apply {
            text = errorMessage
            movementMethod = LinkMovementMethod.getInstance()
            showView()
        }
        onTimerFinish()
        loadingDialog?.dismiss()
        isShowingDialog = false
    }

    private fun handleWrongOtp() {
        trackRegistrationVerifyOtpAnalytics(STATUS_FAIL, DETAIL_INVALID_OTP)
        val message = getString(R.string.otp_error_wrong)
        val stringBuilder = SpannableStringBuilder(message)
        val clickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                tryOTPAgain()
            }
        }
        //bold WA atau SMS
        stringBuilder.setSpan(clickableSpan, 16, 25, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        // OTP code error
        binding.tvErrorMessage.apply {
            text = stringBuilder
            movementMethod = LinkMovementMethod.getInstance()
            showView()
        }
        onTimerFinish()
    }

    private fun tryOTPAgain() {
        trackRetryOtpRequest(otpChannel, otpChannel, false)
        trackRegistrationVerifyOtpAnalytics(STATUS_CANCELLED, DETAIL_CLICK_RETRY)
        viewModel.onEventReceived(VerifyOtpViewModel.Event.RequestNewOtp(true, authAction))
    }

    public override fun onDestroy() {
        super.onDestroy()
        autoDetectOTP?.setOnDestory()
        cancelTimer()
    }

    private fun callLoginActivity() {
        val intent = NewLoginActivity.createIntent(this, skipToLogin = true)
        startActivity(intent)
    }

    override fun onBackPressed() {
        super.onBackPressed()
        when (useCase) {
            UseCase.VERIFY_OTP_SALES_CODE -> {
                finish()
            }
            else -> {
                callLoginActivity()
            }
        }
    }

    private fun startTimer() {
        binding.tvTimer.showView()
        cTimer = object : CountDownTimer(OTP_WAIT_TIME * ONE_SECOND, ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                binding.tvTimer.text = millisecondsToTime(millisUntilFinished)
            }

            override fun onFinish() {
                onTimerFinish()
            }
        }
        cTimer?.start()
    }

    private fun onTimerFinish() {
        cancelTimer()
        with(binding) {
            tvTimer.hideView()
            btnHelp.showView()
            groupSkipLogin.hideView()
            btnRetryRequestOtp.showView()
            btnSwitchOtpChannel.apply {
                val newChannel =
                    if (otpChannel == NotificationChannel.SMS.value) getString(R.string.whatsapp) else getString(
                        R.string.sms
                    )
                val formattedText = getString(R.string.otp_change_channel_placeholder, newChannel)
                setTextColor(getColorCompat(R.color.blue_60))
                text = formattedText
                isEnabled = true
                visibility = (!showDefaultOtpScreen).asVisibility()
                setSingleClickListener {
                    val newChannel =
                        if (otpChannel == NotificationChannel.SMS.value) NotificationChannel.WA.value else NotificationChannel.SMS.value
                    trackRetryOtpRequest(otpChannel, newChannel, true)

                    viewModel.onEventReceived(VerifyOtpViewModel.Event.RequestNewOtp(false, authAction))
                }
            }
        }
    }

    //cancel timer
    private fun cancelTimer() {
        cTimer?.cancel()
    }

    private fun millisecondsToTime(milliseconds: Long): String {
        val minutes = milliseconds / 1000 / 60
        val seconds = milliseconds / 1000 % 60
        val secondsStr = seconds.toString()
        val secs = if (secondsStr.length >= 2) secondsStr.substring(0, 2)
        else "0$secondsStr"
        return "$minutes:$secs"
    }

    private fun proceedWithLogin() {
        loadingDialog?.dismiss()
        isShowingDialog = false
        SessionManager.getInstance().isCreateOrForgotPasswordInitiated = true
        val onboardingType = RemoteConfigUtils.getOnboardingType()
        var onboardProp = "old"
        if (onboardingType == 1) {
            onboardProp = "new"
        }
        if (isForgotPassword) {
            SessionManager.getInstance().tryCount = 1
            registerForgotPasswordOtpVerify(STATUS_SUCCESS, AnalyticsConst.NONE)
            val intent = ForgotPasswordActivity.createIntent(this, phone, countryCode)
            startActivity(intent)
            finish()
        } else {
            try {
                loadingDialog?.dismiss()
                isShowingDialog = false
                val intent = CreateNewPasswordActivity.createIntent(this, phone, "-", countryCode)
                startActivity(intent)
                // if finished, then trycount always reset after going to verify otp activity. expected?
                finish()
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }
    }

    private fun registerForgotPasswordOtpVerify(status: String, failureReason: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.FAIL_REASON, failureReason)
        AppAnalytics.trackEvent(
            if (isForgotPassword) AnalyticsConst.EVENT_FORGOT_PASSWORD_VERIFY_OTP else AnalyticsConst.EVENT_LOGIN_PAGE_VISIT,
            propBuilder
        )
    }

    private fun proceedWithLoginOld() {
        try {
            checkTnC()
            binding.coordinatorLayout.visibility = View.GONE
            if(!SessionManager.getInstance().hasExistingBusiness() && Utility.isEqual(phone,SessionManager.getInstance().userIdForExistingBusinessCheck) && RemoteConfigUtils.OnBoarding.getOnBoardingVariant() == RemoteConfigConst.USE_NEW_ONBOARDING_VARIANT) {
                SessionManager.getInstance().setIsExistingOldUser(false)
                val onboardingType = RemoteConfigUtils.getOnboardingType()
                var onboardProp = "old"
                if (onboardingType == 1) {
                    onboardProp = "new"
                }
                if(onboardingType == 1) {
                    startActivity(Intent(this, NewOnBoardingFormActivity::class.java))
                } else {
                    startActivity(Intent(this, OnBoardingFormActivity::class.java))
                }
                finishAffinity()
            }else {
                MainActivity.startActivityAndClearTop(this, LoginUtils.IS_NEW_LOGIN_EXTRA, true)
                MainActivity().sendAppsFlyerId()
                finishAffinity()
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    private fun checkTnC() {
        val isVisibleTnCVisible = RemoteConfigUtils.isTncVisible()
        FirebaseRepository.getInstance(this).createInitialUserConfig()
        FirebaseRepository.getInstance(this).setAgreedTnC(isVisibleTnCVisible)
    }

    fun getEnteredOtp(): String {
        with(binding.otpField) {
            return arrayOf(tvOne, tvTwo, tvThree, tvFour)
                .joinToString(separator = "") { it.text.toString() }
        }
    }

    fun verifyOtp(otp: String) {
        binding.tvErrorMessage.apply {
            text = null
            hideView()
        }
        if (!Utility.hasInternet()) {
            onTimerFinish()
            binding.tvErrorMessage.showView()
            val stringBuilder = SpannableStringBuilder()
            stringBuilder.append(getString(R.string.otp_no_internet))
            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(view: View) {
                    try {
                        verifyOtp(getEnteredOtp())
                    } catch (ex: Exception) {
                        FirebaseCrashlytics.getInstance().recordException(ex)
                    }
                }
            }
            stringBuilder.setSpan(
                clickableSpan,
                75,
                stringBuilder.length,
                Spannable.SPAN_INCLUSIVE_INCLUSIVE
            )
            // OTP code error
            binding.tvErrorMessage.apply {
                text = stringBuilder
                movementMethod = LinkMovementMethod.getInstance()
            }
            return
        }
        if (Utility.invalidOTPExceeded()) {
            onTimerFinish()
            binding.tvErrorMessage.apply {
                showView()
                text = getString(R.string.otp_too_many)
            }
            return
        }
        trackRegistrationVerifyOtpAnalytics(STATUS_START, DETAIL_ENTERED_OTP)
        val androidId = tryToGetValueOrDefault(
            { Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID) },
            null
        )
        val imeiNumber = tryToGetValueOrDefault({ DeviceUtils.getImeiNumber() }, null)

        val advertisingId: String? = SessionManager.getInstance().advertisingId
        val wideVineId: String? = DeviceUtils.getWideVineId()
        val rooted: Boolean = Utility.isRooted(this)

        InputUtils.hideKeyboard(this)
        val prop = AppAnalytics.PropBuilder()
        prop.put(AnalyticsConst.ANDROID_ID, androidId)
        prop.put(AnalyticsConst.IMEI_NUMBER, imeiNumber)
        prop.put(AnalyticsConst.WIDE_VINE_ID, wideVineId)
        prop.put(AnalyticsConst.ADVERTISING_ID, advertisingId)
        if (PRODUCTION_FLAVOR != BuildConfig.FLAVOR) {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_DEVICE_FINGERPRINT_LOGIN, prop)
        }
        viewModel.onEventReceived(
            VerifyOtpViewModel.Event.OnVerifyOtp(
                phone,
                countryCode.replace("+", ""),
                otp,
                androidId,
                imeiNumber,
                wideVineId,
                advertisingId,
                rooted,
                autoVerify,
                authAction
            )
        )
    }

    private fun showVerifying(show: Boolean) {
        if (show) {
            if (!isShowingDialog) {
                loadingDialog =
                    GeneralLoadingDialog.createInstance(GeneralLoadingDialog.DialogType.OTP_VERIFICATION)
                loadingDialog?.show(supportFragmentManager, GeneralLoadingDialog.TAG)
                isShowingDialog = true
            }
        } else {
            loadingDialog?.dismiss()
            isShowingDialog = false
        }
    }

    private fun openWaBotHelp() {
        if(Utility.isBlank(RemoteConfigUtils.getOtpAssistUrl())){
            WhatsAppUtils.openWABotWithHelpText(this, getString(R.string.wa_help_text_general), AnalyticsConst.LOGIN_OTP_PAGE)
        }else{
            startActivity(WebviewActivity.createIntent(this, RemoteConfigUtils.getOtpAssistUrl(), "Pusat Bantuan Juragan"))
        }
    }

    private fun createBureauEventId() {
        AuthHelper.callBureau(
            "",// event name is not sent to skip tracking.
            "OTP_VERIFICATION_SCREEN",
            SessionManager.getInstance().bukuwarungToken,
            phone
        )
    }

    private fun showDeviceBlacklistedDialog() {
        var dialog: BukuAgenDialog? = null
        dialog = BukuAgenDialog(
            context = this,
            title = getString(R.string.access_denied_title),
            subTitle = getString(R.string.access_denied_description),
            image = R.drawable.ic_system_error,
            isLoader = false,
            btnLeftListener = {
                //open Zoho live chat
                HelpDialog(this).show()
            },
            btnRightListener = {},
            btnLeftText = getString(R.string.contact_cs),
            btnRightText = ""
        )
        Utilities.showDialogIfActivityAlive(this, dialog)
    }
}
