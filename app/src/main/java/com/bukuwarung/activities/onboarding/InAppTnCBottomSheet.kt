package com.bukuwarung.activities.onboarding

import android.content.Context
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.viewbinding.ViewBinding
import com.bukuwarung.R
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.home.TabName.*
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.FirebaseRepository
import com.bukuwarung.databinding.InAppTncBottomSheetLayoutBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialog
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.getColorCompat
import java.util.*

class InAppTnCBottomSheet(context: Context, val fm: FragmentManager) : BaseBottomSheetDialog(context) {
    private val sp = context.getSharedPreferences("InAppTnc", Context.MODE_PRIVATE)
    private val editor = sp.edit()
    private var entryPoint: String = ""
    private var hasOpened: Boolean = false
    private lateinit var binding: InAppTncBottomSheetLayoutBinding

    companion object {
        private const val LAST_SHOWN = "last_shown"
    }

    override fun getResId(): Int = 0
    override fun getBinding(): ViewBinding {
        binding = InAppTncBottomSheetLayoutBinding.inflate(layoutInflater)
        return binding
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setUseFullWidth(true)
        setupView()
    }

    private fun setupView() {
        binding.apply {

            btnClose.setOnClickListener {
                trackPopUpActionClick(AnalyticsConst.DISMISS)
                dismiss()
            }
            btnAgree.setOnClickListener {
                trackPopUpActionClick(AnalyticsConst.ACCEPT)
                FirebaseRepository.getInstance(context).setAgreedTnC(true)
                dismiss()
            }
            tvContent.apply {
                val onWebViewBottomSheetDismiss: (Boolean) -> Unit = { isAgree ->
                    // to dismiss this bottom sheet when user give consent from the webView bottomSheet
                    if (isAgree) {
                        dismiss()
                    }
                }

                val spannableString =
                    SpannableStringBuilder(context.getString(R.string.accept_tnc_inside_app)).apply {
                        val tncText = object : ClickableSpan() {
                            override fun onClick(textView: View) {
                                TnCWebViewBottomSheet.newInstance(
                                    RemoteConfigUtils.getTnCUrl(),
                                    entryPoint,
                                    AnalyticsConst.TNC,
                                    true,
                                    onWebViewBottomSheetDismiss
                                ).apply {
                                    show(fm, TnCWebViewBottomSheet.TAG)
                                }
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                super.updateDrawState(ds)
                                ds.color = context.getColorCompat(R.color.colorPrimary)
                            }
                        }

                        val privacyPolicy = object : ClickableSpan() {
                            override fun onClick(textView: View) {
                                TnCWebViewBottomSheet.newInstance(
                                    RemoteConfigUtils.getPrivacyPolicyUrl(),
                                    entryPoint,
                                    AnalyticsConst.PRIVACY_POLICY,
                                    true,
                                    onWebViewBottomSheetDismiss
                                ).apply {
                                    show(fm, TnCWebViewBottomSheet.TAG)
                                }


                            }

                            override fun updateDrawState(ds: TextPaint) {
                                super.updateDrawState(ds)
                                ds.color = context.getColorCompat(R.color.colorPrimary)
                            }
                        }

                        setSpan(tncText, 86, 106, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                        setSpan(privacyPolicy, 113, 130, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }

                text = spannableString
                movementMethod = LinkMovementMethod.getInstance()
            }
        }


    }

    /**
     * configurable threshold to show the BottomSheet by Hour
     * example:
     * 24 -> show BottomSheet once in 24 H
     * 0 -> show BottomSheet everytime user open the app
     * */
    private fun isValidToShowAfterThreshold(threshold: Int): Boolean {
        if (threshold == 0) return true
        val lastShown = sp.getLong(LAST_SHOWN, 0)
        val hourDiff = (Date().time - lastShown) / 1000 / 60 / 60
        val rem = hourDiff.rem(threshold)

        return rem > 0 && hourDiff >= threshold
    }

    fun setEntryPointAndShow(tabName: TabName) {
        val threshold = RemoteConfigUtils.getTncTimeThreshold()
        if (!isValidToShowAfterThreshold(threshold)) return
        if (hasOpened) return

        editor.putLong(LAST_SHOWN, Date().time).commit()
        hasOpened = true
        entryPoint = when(tabName){
            CUSTOMER -> AnalyticsConst.UTANG_TAB_BOTTOM_SHEET
            PAYMENT -> AnalyticsConst.PAYMENT_TAB_BOTTOM_SHEET
            TRANSACTION, TRANSACTION_HOME -> AnalyticsConst.TRANSAKSI_TAB_BOTTOM_SHEET
            STOCK -> AnalyticsConst.INVENTORY_TAB_BOTTOM_SHEET
            OTHERS -> AnalyticsConst.LAINNYA_TAB_BOTTOM_SHEET
            else -> AnalyticsConst.TRANSAKSI_TAB_BOTTOM_SHEET
        }
        show()
    }

    private fun trackPopUpActionClick(action: String){
        AppAnalytics.setUserProperty(AnalyticsConst.BW_TNC_ACCEPT, (action == AnalyticsConst.ACCEPT).toString())
    }

}