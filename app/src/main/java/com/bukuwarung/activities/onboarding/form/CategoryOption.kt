package com.bukuwarung.activities.onboarding.form

import androidx.annotation.Keep
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.constants.AppConst.DEFAULT_BUSINESS_NAME
import com.google.gson.annotations.SerializedName

@Keep
data class CategoryOption(
        val id: Int,
        @SerializedName("resource_id") val resourceId: String,
        val order: Int,
        val name: String,
        @SerializedName("image_url") val imageUrl: String,
        var isSelected: Boolean = false
)

@Keep
data class OnboardingData(
    var businessName: String=DEFAULT_BUSINESS_NAME,
    var businessCategory: String="",
    var businessCategoryId: String="-1", //default category to not set
    var goalUsage: String="",
    var tabId: Int=TabName.TRANSACTION.ordinal,
    var pastUsage: String=""
)

sealed class FormType(val name: String) {
    object BusinessName : FormType("business_name")
    object BusinessCategory : FormType("business_category")
    object BusinessTiming : FormType("business_timing")
    object UsageGoal : FormType("usage_goal")
    object UsagePast : FormType("usage_past")
}