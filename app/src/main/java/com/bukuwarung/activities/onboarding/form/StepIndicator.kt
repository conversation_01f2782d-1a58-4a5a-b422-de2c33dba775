package com.bukuwarung.activities.onboarding.form

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.databinding.StepIndicatorViewBinding
import com.bukuwarung.utils.getDrawableCompat

class StepIndicator @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {
    private val binding = StepIndicatorViewBinding.inflate(LayoutInflater.from(context), this, true)
    private var isIndicatorEnabled = false

    fun setIndicatorEnabled(enabled: Boolean) {
        isIndicatorEnabled = enabled

        val bg = context.getDrawableCompat(if (isIndicatorEnabled) R.drawable.indicator_enabled else R.drawable.indicator_disabled)
        binding.indicator.background = bg
    }

}