/*
 * Copyright (c) WhatsApp Inc. and its affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.bukuwarung.activities.stickers;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.google.android.material.button.MaterialButton;

class StickerPackListItemViewHolder extends RecyclerView.ViewHolder {

    final View container;
    final TextView titleView;
    final TextView publisherView;
    final TextView filesizeView;
    final ImageView addButton;
    final MaterialButton action;
    final LinearLayout imageRowView;
    final LinearLayout imageSecondRowView;

    StickerPackListItemViewHolder(final View itemView) {
        super(itemView);
        container = itemView;
        titleView = itemView.findViewById(R.id.sticker_pack_title);
        publisherView = itemView.findViewById(R.id.sticker_pack_publisher);
        filesizeView = itemView.findViewById(R.id.sticker_pack_filesize);
        addButton = itemView.findViewById(R.id.add_button_on_list);
        action = itemView.findViewById(R.id.action_button);
        imageRowView = itemView.findViewById(R.id.sticker_packs_list_item_image_list);
        imageSecondRowView = itemView.findViewById(R.id.sticker_packs_list_item_image_list_second);
    }
}