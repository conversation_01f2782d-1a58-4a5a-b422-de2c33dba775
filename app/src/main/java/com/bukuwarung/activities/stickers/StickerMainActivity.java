/*
 * Copyright (c) WhatsApp Inc. and its affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.bukuwarung.activities.stickers;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.util.Pair;
import android.view.View;

import androidx.annotation.Nullable;

import com.bukuwarung.R;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DefaultAnim;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.preference.FeaturePrefManager;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import static com.bukuwarung.activities.stickers.StickerPackDetailsActivity.EXTRA_POS_TARGET;
import static com.bukuwarung.activities.stickers.StickerPackDetailsActivity.EXTRA_STICKER_PACK_DATA;
import static com.bukuwarung.activities.stickers.StickerPackListActivity.EXTRA_STICKER_PACK_LIST_DATA;

public class StickerMainActivity extends AppActivity {
    private View progressBar;
    private LoadListAsyncTask loadListAsyncTask;
    public static String IS_FOR_STREAK = "is_for_streak";

    private String from = null;

    public StickerMainActivity(){
        super(new DefaultAnim());
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_sticker_main);

//        if (getSupportActionBar() != null) {
//            getSupportActionBar().hide();
//        }

            if (getIntent().hasExtra("from")) {
                from = getIntent().getStringExtra("from");
            }

            try {
                //TODO: strange logic here. need to verify previous requirement
                if (FeaturePrefManager.getInstance().getOldTransactionCount() < 0) {
                    FeaturePrefManager.getInstance().setOldTransactionCount(TransactionRepository.getInstance(this).getTransactionCountWithDeletedRecords());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            progressBar = findViewById(R.id.entry_activity_progress);
            if(getIntent().hasExtra(EXTRA_STICKER_PACK_LIST_DATA)){
                ArrayList<StickerPack> stickerPackList = getIntent().getParcelableArrayListExtra(EXTRA_STICKER_PACK_LIST_DATA);
                showStickerPack(stickerPackList, from);
            }else {
                loadListAsyncTask = new LoadListAsyncTask(this, from);
                loadListAsyncTask.execute();
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void showStickerPack(ArrayList<StickerPack> stickerPackList, String from) {
        progressBar.setVisibility(View.GONE);
        if (stickerPackList.size() > 1) {
            Intent intent;
            Bundle bundle = getIntent().getExtras();

            StickerPack streakSticker = stickerPackList.get(stickerPackList.size() - 1);

            if (getIntent().hasExtra(EXTRA_POS_TARGET)) {
                intent = new Intent(this, StickerPackDetailsActivity.class);
                intent.putExtra(EXTRA_STICKER_PACK_DATA, stickerPackList.get(2));
                intent.putExtra(EXTRA_POS_TARGET, true);
                startActivity(intent);

            } else if (getIntent().hasExtra(IS_FOR_STREAK)) {
                boolean isForStreak = Boolean.parseBoolean(bundle.getString(IS_FOR_STREAK));

                if (!isForStreak) {
                    isForStreak = getIntent().getBooleanExtra(IS_FOR_STREAK, false);
                }
                if (isForStreak) {
                    intent = new Intent(this, StickerPackDetailsActivity.class);
                    intent.putExtra(EXTRA_STICKER_PACK_DATA, streakSticker);
                    intent.putExtra(IS_FOR_STREAK, true);
                    startActivity(intent);
                }
            } else {
                intent = new Intent(this, StickerPackListActivity.class);
                // remove the last sticker pack
                stickerPackList.remove(streakSticker);
                intent.putExtra("from", from);
                intent.putParcelableArrayListExtra(EXTRA_STICKER_PACK_LIST_DATA, stickerPackList);
                startActivity(intent);
            }

        }
        else {
            final Intent intent = new Intent(this, StickerPackDetailsActivity.class);
            intent.putExtra("from", from);
            intent.putExtra(StickerPackDetailsActivity.EXTRA_SHOW_UP_BUTTON, false);
            intent.putExtra(EXTRA_STICKER_PACK_DATA, stickerPackList.get(0));
            startActivity(intent);
        }
        finish();
    }

    private void showErrorMessage(String errorMessage) {
        progressBar.setVisibility(View.GONE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (loadListAsyncTask != null && !loadListAsyncTask.isCancelled()) {
            loadListAsyncTask.cancel(true);
        }
    }

    static class LoadListAsyncTask extends AsyncTask<Void, Void, Pair<String, ArrayList<StickerPack>>> {
        private final WeakReference<StickerMainActivity> contextWeakReference;
        private String from = null;

        LoadListAsyncTask(StickerMainActivity activity, String from) {
            this.contextWeakReference = new WeakReference<>(activity);
            this.from = from;
        }

        @Override
        protected Pair<String, ArrayList<StickerPack>> doInBackground(Void... voids) {
            ArrayList<StickerPack> stickerPackList;
            try {
                final Context context = contextWeakReference.get();
                if (context != null) {
                    stickerPackList = StickerPackLoader.fetchStickerPacks(context);
                    if (stickerPackList.size() == 0) {
                        return new Pair<>("could not find any packs", null);
                    }
                    for (StickerPack stickerPack : stickerPackList) {
                        StickerPackValidator.verifyStickerPackValidity(context, stickerPack);
                    }
                    return new Pair<>(null, stickerPackList);
                } else {
                    return new Pair<>("could not fetch sticker packs", null);
                }
            } catch (Exception e) {
                Log.e("EntryActivity", "error fetching sticker packs", e);
                return new Pair<>(e.getMessage(), null);
            }
        }

        @Override
        protected void onPostExecute(Pair<String, ArrayList<StickerPack>> stringListPair) {

            final StickerMainActivity stickerMainActivity = contextWeakReference.get();
            if (stickerMainActivity != null) {
                if (stringListPair.first != null) {
                    stickerMainActivity.showErrorMessage(stringListPair.first);
                } else {
                    stickerMainActivity.showStickerPack(stringListPair.second, from);
                }
            }
        }
    }
}
