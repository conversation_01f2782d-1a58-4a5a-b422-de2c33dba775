/*
 * Copyright (c) WhatsApp Inc. and its affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.bukuwarung.activities.stickers;

import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.format.Formatter;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.utils.ListUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import static android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP;
import static android.content.Intent.FLAG_ACTIVITY_SINGLE_TOP;
import static com.bukuwarung.activities.stickers.StickerPackListActivity.EXTRA_STICKER_PACK_LIST_DATA;

public class StickerPackDetailsActivity extends AddStickerPackActivity {

    /**
     * Do not change below values of below 3 lines as this is also used by WhatsApp
     */
    public static final String EXTRA_STICKER_PACK_ID = "sticker_pack_id";
    public static final String EXTRA_STICKER_PACK_AUTHORITY = "sticker_pack_authority";
    public static final String EXTRA_STICKER_PACK_NAME = "sticker_pack_name";

    public static final String EXTRA_STICKER_PACK_WEBSITE = "sticker_pack_website";
    public static final String EXTRA_STICKER_PACK_EMAIL = "sticker_pack_email";
    public static final String EXTRA_STICKER_PACK_PRIVACY_POLICY = "sticker_pack_privacy_policy";
    public static final String EXTRA_STICKER_PACK_LICENSE_AGREEMENT = "sticker_pack_license_agreement";
    public static final String EXTRA_STICKER_PACK_TRAY_ICON = "sticker_pack_tray_icon";
    public static final String EXTRA_SHOW_UP_BUTTON = "show_up_button";
    public static final String EXTRA_STICKER_PACK_DATA = "sticker_pack";
    public static final String EXTRA_TRANSACTION_TARGET = "trans_target";
    public static final String EXTRA_STOCK_TARGET = "stock_target";
    public static final String EXTRA_POS_TARGET = "pos_target";

    private RecyclerView recyclerView;
    private GridLayoutManager layoutManager;
    private StickerPreviewAdapter stickerPreviewAdapter;
    private int numColumns;
    private View addButton;
    private View addTranasctionButton;
    private View alreadyAddedText;
    private StickerPack stickerPack;
    private ArrayList<StickerPack> stickerPackList;
    private View divider;
    private WhiteListCheckAsyncTask whiteListCheckAsyncTask;
    private int transactionTarget = 5;
    private int stock_target = 3;
    private boolean isTargetPosNotification = false;
    public String from = null;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sticker_pack_details);
        boolean showUpButton = getIntent().getBooleanExtra(EXTRA_SHOW_UP_BUTTON, false);
        if(getIntent().hasExtra(EXTRA_TRANSACTION_TARGET)) {
            transactionTarget = getIntent().getIntExtra(EXTRA_TRANSACTION_TARGET,0);
        }
        if(getIntent().hasExtra(EXTRA_STICKER_PACK_LIST_DATA)) {
            stickerPackList = getIntent().getParcelableArrayListExtra(EXTRA_STICKER_PACK_LIST_DATA);
            stickerPack = stickerPackList.get(0);
        }else {
            stickerPack = getIntent().getParcelableExtra(EXTRA_STICKER_PACK_DATA);
        }

        if (getIntent().hasExtra("from")) {
            from = getIntent().getStringExtra("from");
        }

        TextView packNameTextView = findViewById(R.id.pack_name);
        TextView packPublisherTextView = findViewById(R.id.author);
        ImageView packTrayIcon = findViewById(R.id.tray_image);
        TextView packSizeTextView = findViewById(R.id.pack_size);

        addButton = findViewById(R.id.add_to_whatsapp_button);
        addTranasctionButton = findViewById(R.id.add_transaction_btn);
        TextView bigText = findViewById(R.id.locked_sticker);
        TextView smallText = findViewById(R.id.locked_sticker_subtext);
        TextView title = findViewById(R.id.screen_title);
        ImageView lockIcon = findViewById(R.id.icLock);

        boolean isForStreak = getIntent().getBooleanExtra(StickerMainActivity.IS_FOR_STREAK, false);
        isTargetPosNotification = getIntent().getBooleanExtra(EXTRA_POS_TARGET, false);
        if (isForStreak) {
            addTranasctionButton.setVisibility(View.GONE);
            addButton.setVisibility(View.VISIBLE);
            lockIcon.setVisibility(View.GONE);
            title.setText(getString(R.string.claim_gift));
            bigText.setText(getResources().getString(R.string.new_stickers_congratulate));
            smallText.setText(getResources().getString(R.string.keep_use_bw_more_gift));
        } else {
            if (isTargetPosNotification) {
                addTranasctionButton.setVisibility(View.GONE);
                addButton.setVisibility(View.VISIBLE);
                lockIcon.setVisibility(View.GONE);
                title.setText(getResources().getString(R.string.share_your_stickers));
                bigText.setText(getResources().getString(R.string.congratulations));
                smallText.setText(getResources().getString(R.string.more_trans_more_gift));
            } else if (transactionTarget > 0) {
                addTranasctionButton.setVisibility(View.VISIBLE);
                addButton.setVisibility(View.GONE);
                lockIcon.setVisibility(View.VISIBLE);
                String str = getResources().getString(R.string.just_add_x_transactions);
                smallText.setText(String.format(str, String.valueOf(transactionTarget)));//"Just add "+transactionTarget+" more transactions to unlock!");
            } else {
                addTranasctionButton.setVisibility(View.GONE);
                addButton.setVisibility(View.VISIBLE);
                lockIcon.setVisibility(View.GONE);
                title.setText(getResources().getString(R.string.share_your_stickers));
                bigText.setText(getResources().getString(R.string.congratulations));
                smallText.setText(getResources().getString(R.string.more_trans_more_gift));
            }
        }


        findViewById(R.id.backBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });
        alreadyAddedText = findViewById(R.id.already_added_text);
        layoutManager = new GridLayoutManager(this, 1);
        recyclerView = findViewById(R.id.sticker_list);

        recyclerView.setLayoutManager(layoutManager);
        recyclerView.getViewTreeObserver().addOnGlobalLayoutListener(pageLayoutListener);
        recyclerView.addOnScrollListener(dividerScrollListener);
        divider = findViewById(R.id.divider);
        if (stickerPreviewAdapter == null) {
            stickerPreviewAdapter = new StickerPreviewAdapter(getLayoutInflater(), R.drawable.ic_fingerprint, getResources().getDimensionPixelSize(R.dimen.sticker_pack_details_image_size), getResources().getDimensionPixelSize(R.dimen.sticker_pack_details_image_padding), stickerPack);
            recyclerView.setAdapter(stickerPreviewAdapter);
        }
        packNameTextView.setText(stickerPack.name);
        packPublisherTextView.setText(stickerPack.publisher);
        packTrayIcon.setImageURI(StickerPackLoader.getStickerAssetUri(stickerPack.identifier, stickerPack.trayImageFile));
        packSizeTextView.setText(Formatter.formatShortFileSize(this, stickerPack.getTotalSize()));
        addButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                addStickerPackToWhatsApp(stickerPack.identifier, stickerPack.name, from);
            }
        });
        addTranasctionButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                MainActivity.startActivitySingleTopToTab(StickerPackDetailsActivity.this, TabName.CUSTOMER);
                finish();
            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        if(!ListUtils.isEmpty(stickerPackList)) {
            Intent intent = new Intent(this, StickerMainActivity.class);
            intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP | FLAG_ACTIVITY_SINGLE_TOP);
            intent.putParcelableArrayListExtra(EXTRA_STICKER_PACK_LIST_DATA, stickerPackList);
            startActivity(intent);
        }
        finish();
    }


    private void launchInfoActivity(String publisherWebsite, String publisherEmail, String privacyPolicyWebsite, String licenseAgreementWebsite, String trayIconUriString) {
        Intent intent = new Intent(StickerPackDetailsActivity.this, StickerPackInfoActivity.class);
        intent.putExtra(StickerPackDetailsActivity.EXTRA_STICKER_PACK_ID, stickerPack.identifier);
        intent.putExtra(StickerPackDetailsActivity.EXTRA_STICKER_PACK_WEBSITE, publisherWebsite);
        intent.putExtra(StickerPackDetailsActivity.EXTRA_STICKER_PACK_EMAIL, publisherEmail);
        intent.putExtra(StickerPackDetailsActivity.EXTRA_STICKER_PACK_PRIVACY_POLICY, privacyPolicyWebsite);
        intent.putExtra(StickerPackDetailsActivity.EXTRA_STICKER_PACK_LICENSE_AGREEMENT, licenseAgreementWebsite);
        intent.putExtra(StickerPackDetailsActivity.EXTRA_STICKER_PACK_TRAY_ICON, trayIconUriString);
        startActivity(intent);
    }


    private final ViewTreeObserver.OnGlobalLayoutListener pageLayoutListener = new ViewTreeObserver.OnGlobalLayoutListener() {
        @Override
        public void onGlobalLayout() {
            setNumColumns(recyclerView.getWidth() / recyclerView.getContext().getResources().getDimensionPixelSize(R.dimen.sticker_pack_details_image_size));
        }
    };

    private void setNumColumns(int numColumns) {
        if (this.numColumns != numColumns) {
            layoutManager.setSpanCount(numColumns);
            this.numColumns = numColumns;
            if (stickerPreviewAdapter != null) {
                stickerPreviewAdapter.notifyDataSetChanged();
            }
        }
    }

    private final RecyclerView.OnScrollListener dividerScrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrollStateChanged(@NonNull final RecyclerView recyclerView, final int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            updateDivider(recyclerView);
        }

        @Override
        public void onScrolled(@NonNull final RecyclerView recyclerView, final int dx, final int dy) {
            super.onScrolled(recyclerView, dx, dy);
            updateDivider(recyclerView);
        }

        private void updateDivider(RecyclerView recyclerView) {
            boolean showDivider = recyclerView.computeVerticalScrollOffset() > 0;
            if (divider != null) {
                divider.setVisibility(showDivider ? View.VISIBLE : View.INVISIBLE);
            }
        }
    };

    @Override
    public void onResume() {
        super.onResume();
        whiteListCheckAsyncTask = new WhiteListCheckAsyncTask(this);
        whiteListCheckAsyncTask.execute(stickerPack);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (whiteListCheckAsyncTask != null && !whiteListCheckAsyncTask.isCancelled()) {
            whiteListCheckAsyncTask.cancel(true);
        }
    }

    private void updateAddUI(Boolean isWhitelisted) {
        if (isWhitelisted && !isTargetPosNotification) {
            addButton.setVisibility(View.GONE);
            alreadyAddedText.setVisibility(View.VISIBLE);
        } else {
            if (isTargetPosNotification) {
                isTargetPosNotification = false;
            }
            addButton.setVisibility(View.VISIBLE);
            alreadyAddedText.setVisibility(View.GONE);
        }
    }

    static class WhiteListCheckAsyncTask extends AsyncTask<StickerPack, Void, Boolean> {
        private final WeakReference<StickerPackDetailsActivity> stickerPackDetailsActivityWeakReference;

        WhiteListCheckAsyncTask(StickerPackDetailsActivity stickerPackListActivity) {
            this.stickerPackDetailsActivityWeakReference = new WeakReference<>(stickerPackListActivity);
        }

        @Override
        protected final Boolean doInBackground(StickerPack... stickerPacks) {
            StickerPack stickerPack = stickerPacks[0];
            final StickerPackDetailsActivity stickerPackDetailsActivity = stickerPackDetailsActivityWeakReference.get();
            if (stickerPackDetailsActivity == null) {
                return false;
            }
            return WhitelistCheck.isWhitelisted(stickerPackDetailsActivity, stickerPack.identifier);
        }

        @Override
        protected void onPostExecute(Boolean isWhitelisted) {
            final StickerPackDetailsActivity stickerPackDetailsActivity = stickerPackDetailsActivityWeakReference.get();
            if (stickerPackDetailsActivity != null) {
                stickerPackDetailsActivity.updateAddUI(isWhitelisted);
            }
        }
    }
}
