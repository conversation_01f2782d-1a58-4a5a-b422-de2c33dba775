/*
 * Copyright (c) WhatsApp Inc. and its affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.bukuwarung.activities.stickers;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;

import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.payments.data.repository.PaymentsRepository;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.utils.ImageUtils;
import com.bumptech.glide.Glide;

import java.util.List;

import javax.inject.Inject;

public class StickerPackListAdapter extends RecyclerView.Adapter<StickerPackListItemViewHolder> {
    @NonNull
    private List<StickerPack> stickerPacks;
    @NonNull
    private final OnAddButtonClickedListener onAddButtonClickedListener;
    private int maxNumberOfStickersInARow;
    private int minMarginBetweenImages;
    private String from;

    @Inject
    PaymentsRepository paymentsRepository;
    StickerPackListAdapter(@NonNull List<StickerPack> stickerPacks, @NonNull OnAddButtonClickedListener onAddButtonClickedListener, String from) {
        this.stickerPacks = stickerPacks;
        this.onAddButtonClickedListener = onAddButtonClickedListener;
        this.from = from;
    }

    @NonNull
    @Override
    public StickerPackListItemViewHolder onCreateViewHolder(@NonNull final ViewGroup viewGroup, final int i) {
        final Context context = viewGroup.getContext();
        final LayoutInflater layoutInflater = LayoutInflater.from(context);
        final View stickerPackRow = layoutInflater.inflate(R.layout.sticker_packs_list_item, viewGroup, false);
        return new StickerPackListItemViewHolder(stickerPackRow);
    }

    @Override
    public void onBindViewHolder(@NonNull final StickerPackListItemViewHolder viewHolder, final int index) {
        final StickerPack pack = stickerPacks.get(index);
        final Context context = viewHolder.publisherView.getContext();
        viewHolder.publisherView.setText("");//pack.publisher);
        viewHolder.filesizeView.setText("");//Formatter.formatShortFileSize(context, pack.getTotalSize()));
        viewHolder.setIsRecyclable(false);
        int additional = index == 3 ? 5: (index - 2) * 10;
        final int transactionTarget = additional - TransactionRepository.getInstance(context).getTransactionCountWithDeletedRecords();
        final int posTarget = TransactionRepository.getInstance(context).getPosTransactionCountWithDeletedRecords();
        final int stockTarget = ProductRepository.getInstance(context).getProductCount();
        final int paymentTarget = FeaturePrefManager.getInstance().getPaymentTransactionCount();
        viewHolder.titleView.setText(pack.name);
        viewHolder.action.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(view.getContext(), StickerPackDetailsActivity.class);
                intent.putExtra(StickerPackDetailsActivity.EXTRA_SHOW_UP_BUTTON, true);
                intent.putExtra(StickerPackDetailsActivity.EXTRA_STICKER_PACK_DATA, pack);
                intent.putExtra("from", from);
                intent.putExtra(StickerPackDetailsActivity.EXTRA_TRANSACTION_TARGET, transactionTarget<=0?0:transactionTarget);
                view.getContext().startActivity(intent);
            }
        });
        viewHolder.imageRowView.removeAllViews();

        //if this sticker pack contains less stickers than the max, then take the smaller size.
        int actualNumberOfStickersToShow = Math.min(maxNumberOfStickersInARow, pack.getStickers().size());
        int i = 0;
        for (i = 0; i < actualNumberOfStickersToShow; i++) {
            final AppCompatImageView rowImage = (AppCompatImageView) LayoutInflater.from(context).inflate(R.layout.sticker_packs_list_image_item, viewHolder.imageRowView, false);
            ImageUtils.loadImage(
                rowImage.getContext(), rowImage,
                StickerPackLoader.getStickerAssetUri(pack.identifier, pack.getStickers().get(i).imageFileName),
                R.color.white, R.color.white
            );
            final LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) rowImage.getLayoutParams();
            final int marginBetweenImages = minMarginBetweenImages - lp.leftMargin - lp.rightMargin;
            if (i != actualNumberOfStickersToShow - 1 && marginBetweenImages > 0) { //do not set the margin for the last image
                lp.setMargins(lp.leftMargin, lp.topMargin, lp.rightMargin + marginBetweenImages, lp.bottomMargin);
                rowImage.setLayoutParams(lp);
            }
            viewHolder.imageRowView.addView(rowImage);
        }
        if(actualNumberOfStickersToShow>0 && pack.getStickers().size()>3){
            while ( i < pack.getStickers().size()) {
                final AppCompatImageView rowImage = (AppCompatImageView) LayoutInflater.from(context).inflate(R.layout.sticker_packs_list_image_item, viewHolder.imageRowView, false);
                ImageUtils.loadImage(
                    rowImage.getContext(), rowImage,
                    StickerPackLoader.getStickerAssetUri(pack.identifier, pack.getStickers().get(i).imageFileName),
                    R.color.white, R.color.white
                );
                final LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) rowImage.getLayoutParams();
                final int marginBetweenImages = minMarginBetweenImages - lp.leftMargin - lp.rightMargin;
                if (i != actualNumberOfStickersToShow - 1 && marginBetweenImages > 0) { //do not set the margin for the last image
                    lp.setMargins(lp.leftMargin, lp.topMargin, lp.rightMargin + marginBetweenImages, lp.bottomMargin);
                    rowImage.setLayoutParams(lp);
                }
                viewHolder.imageSecondRowView.addView(rowImage);
                i++;
            }
        }
        viewHolder.action.setText(context.getResources().getString(R.string.download));
        String unlockedStr = String.format(context.getResources().getString(R.string.unlocked_after_x_transactions),String.valueOf(additional));
        String lockedStr = String.format(context.getResources().getString(R.string.add_x_transactions),String.valueOf(transactionTarget));

        if (transactionTarget <= 0) {
            viewHolder.titleView.setText(unlockedStr);
        } else {
            viewHolder.titleView.setText(lockedStr);
        }
        if (index == 3) {
            if (transactionTarget <= 0) {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.buku_CTA_New));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.black_80));
                viewHolder.action.setEnabled(true);
                viewHolder.imageRowView.setAlpha(1f);
                viewHolder.imageSecondRowView.setAlpha(1f);
            } else {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.black_20));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.white));
                viewHolder.action.setEnabled(false);
                viewHolder.imageRowView.setAlpha(.7f);
                viewHolder.imageSecondRowView.setAlpha(.7f);
            }
        } else if (index == 4) {
            if (transactionTarget <= 0) {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.buku_CTA_New));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.black_80));
                viewHolder.action.setEnabled(true);
                viewHolder.imageRowView.setAlpha(1f);
                viewHolder.imageSecondRowView.setAlpha(1f);
            } else {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.black_20));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.white));
                viewHolder.action.setEnabled(false);
                viewHolder.imageRowView.setAlpha(.7f);
                viewHolder.imageSecondRowView.setAlpha(.7f);
            }

        } else if (index == 5) {
            if (FeaturePrefManager.getInstance().hasCompleteUserProfile()) {
                viewHolder.titleView.setText("Telah melengkapi profil");
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.buku_CTA_New));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.black_80));
                viewHolder.action.setEnabled(true);
                viewHolder.imageRowView.setAlpha(1f);
                viewHolder.imageSecondRowView.setAlpha(1f);
            } else {
                viewHolder.titleView.setText("Lengkapi profil kamu untuk membuka ini");
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.black_20));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.white));
                viewHolder.action.setEnabled(false);
                viewHolder.imageRowView.setAlpha(.7f);
                viewHolder.imageSecondRowView.setAlpha(.7f);
            }
        } else if (index == 0) {
            viewHolder.titleView.setText(context.getResources().getString(R.string.stock_min_for_unlock));
            if (stockTarget >= 3) {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.buku_CTA_New));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.black_80));
                viewHolder.action.setEnabled(true);
                viewHolder.imageRowView.setAlpha(1f);
                viewHolder.imageSecondRowView.setAlpha(1f);
            } else {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.black_20));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.white));
                viewHolder.action.setEnabled(false);
                viewHolder.imageRowView.setAlpha(.7f);
                viewHolder.imageSecondRowView.setAlpha(.7f);
            }
        } else if (index == 2) {
            viewHolder.titleView.setText(context.getResources().getString(R.string.pos_min_unlock));
            if (posTarget >= 15) {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.buku_CTA_New));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.black_80));
                viewHolder.action.setEnabled(true);
                viewHolder.imageRowView.setAlpha(1f);
                viewHolder.imageSecondRowView.setAlpha(1f);
            } else {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.black_20));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.white));
                viewHolder.action.setEnabled(false);
                viewHolder.imageRowView.setAlpha(.7f);
                viewHolder.imageSecondRowView.setAlpha(.7f);
            }
        } else if (index == 1) {
            viewHolder.titleView.setText(context.getResources().getString(R.string.payment_min_unlock));
            if (paymentTarget >= 3) {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.buku_CTA_New));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.black_80));
                viewHolder.action.setEnabled(true);
                viewHolder.imageRowView.setAlpha(1f);
                viewHolder.imageSecondRowView.setAlpha(1f);
            } else {
                viewHolder.action.setBackgroundColor(context.getResources().getColor(R.color.black_20));
                viewHolder.action.setTextColor(context.getResources().getColor(R.color.white));
                viewHolder.action.setEnabled(false);
                viewHolder.imageRowView.setAlpha(.7f);
                viewHolder.imageSecondRowView.setAlpha(.7f);
            }
        }
        setAddButtonAppearance(viewHolder.addButton, pack,index);
    }

    private void setAddButtonAppearance(ImageView addButton, final StickerPack pack,int index) {
        if (pack.getIsWhitelisted()) {
//            addButton.setImageResource(R.drawable.sticker_3rdparty_added);
//            addButton.setClickable(false);
//            addButton.setOnClickListener(null);
//            setBackground(addButton, null);
        } else {
            addButton.setImageResource(R.drawable.ic_check_mark);
            addButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    onAddButtonClickedListener.onAddButtonClicked(pack);
                }
            });
            TypedValue outValue = new TypedValue();
            addButton.getContext().getTheme().resolveAttribute(android.R.attr.selectableItemBackground, outValue, true);
            addButton.setBackgroundResource(outValue.resourceId);
        }
    }

    private void setBackground(View view, Drawable background) {
        if (Build.VERSION.SDK_INT >= 16) {
            view.setBackground(background);
        } else {
            view.setBackgroundDrawable(background);
        }
    }

    @Override
    public int getItemCount() {
        return stickerPacks.size();
    }

    void setImageRowSpec(int maxNumberOfStickersInARow, int minMarginBetweenImages) {
        this.minMarginBetweenImages = minMarginBetweenImages;
        if (this.maxNumberOfStickersInARow != maxNumberOfStickersInARow) {
            this.maxNumberOfStickersInARow = maxNumberOfStickersInARow;
            notifyDataSetChanged();
        }
    }

    void setStickerPackList(List<StickerPack> stickerPackList) {
        this.stickerPacks = stickerPackList;
    }

    public interface OnAddButtonClickedListener {
        void onAddButtonClicked(StickerPack stickerPack);
    }
}
