package com.bukuwarung.activities.vida

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Message
import android.view.ViewGroup
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebView.WebViewTransport
import android.webkit.WebViewClient
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.databinding.ActivityWebviewVidaBinding
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils

class VidaWebViewActivity : AppCompatActivity() {
    private val binding: ActivityWebviewVidaBinding by lazy {
        DataBindingUtil.setContentView(this, R.layout.activity_webview_vida)
    }

    private val webUrl by lazy {
        intent.extras?.getString(EXTRA_PAGE_URL)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        dealWebSetting(binding.wvView)
        initListener()
        startLoad(webUrl)
    }

    override fun onBackPressed() {
        binding.wvView.loadUrl("javascript:onBackPressed()")
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun dealWebSetting(webView: WebView) {
        val webSettings: WebSettings = webView.getSettings()
        webSettings.javaScriptEnabled = true
        webSettings.javaScriptCanOpenWindowsAutomatically = true
        webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        webSettings.domStorageEnabled = true
        webSettings.useWideViewPort = true
        webSettings.loadWithOverviewMode = true
        webSettings.allowUniversalAccessFromFileURLs = true
        webSettings.setSupportMultipleWindows(true)
        webView.addJavascriptInterface(
            WebViewJavaScriptInterface(this, binding.wvView),
            APP_JS_LISTENER_SIGN
        )
    }


    private fun startLoad(url: String?) {
        val url = dealWebSignLink(url ?: "")
        binding.wvView.loadUrl(url)
    }

    private fun dealWebSignLink(originUrl: String): String {
        val uri = Uri.parse(originUrl)
        if (uri.getBooleanQueryParameter(WEB_SIGN_PATH_BACK_TO_APP, false)) {
            return originUrl
        }
        val newUri = uri.buildUpon().appendQueryParameter(
            WEB_SIGN_PATH_BACK_TO_APP,
            "true"
        ).build()
        return newUri.toString()
    }

    private fun initListener() {
        binding.wvView.webChromeClient = object : WebChromeClient() {

            override fun onCreateWindow(
                view: WebView?,
                isDialog: Boolean,
                isUserGesture: Boolean,
                resultMsg: Message?,
            ): Boolean {
                val newWebView = WebView(this@VidaWebViewActivity)
                newWebView.layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                )
                view?.addView(newWebView)
                dealWebSetting(newWebView)
                newWebView.webViewClient = WebViewClient()
                newWebView.webChromeClient = object : WebChromeClient() {
                    override fun onCloseWindow(window: WebView?) {
                        super.onCloseWindow(window)
                        view?.removeView(newWebView)
                    }
                }
                val transport = resultMsg?.obj as WebViewTransport
                transport.webView = newWebView
                resultMsg.sendToTarget()
                return true
            }
        }

        binding.wvView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView,
                request: WebResourceRequest,
            ): Boolean {
                val url = request.url.toString()
                startLoad(url)
                return true
            }

            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                startLoad(url)
                return true
            }

            override fun onPageFinished(view: WebView, url: String?) {
            }
        }
    }

    class WebViewJavaScriptInterface(val activity: VidaWebViewActivity, val webview: WebView) {
        @JavascriptInterface
        fun signFinish(redirectUrl: String?) {
            activity.startActivity(
                WebviewActivity.createIntent(
                    activity,
                    redirectUrl ?: RemoteConfigUtils.getBukuModalUrl(),
                    ""
                )
            )
            activity.finish()
        }

        @JavascriptInterface
        fun getBWAppToken(): String? {
            return SessionManager.getInstance().bukuwarungToken
        }

        @JavascriptInterface
        fun androidHandleBackPressed() {
            activity.runOnUiThread {
                webview.loadUrl("javascript:getAndroidBackPressed()")
                if (webview.canGoBack()) {
                    webview.goBack()
                } else {
                    activity.onBackPressed()
                }
            }
        }
    }

    companion object {
        private const val EXTRA_PAGE_URL = "url"
        private const val APP_JS_LISTENER_SIGN = "BukuWebContainer"
        private const val WEB_SIGN_PATH_BACK_TO_APP =
            "auto_back_to_app"

        fun openViewWebViewActivity(
            context: Context?,
            url: String,
        ) {
            var intent: Intent
            context?.let { ctx ->
                intent = getIntent(
                    context = ctx,
                    url = url
                )
                context.startActivity(intent)
            }
        }

        fun getIntent(
            context: Context,
            url: String
        ): Intent {
            return Intent(context, VidaWebViewActivity::class.java).apply {
                putExtra(EXTRA_PAGE_URL, url)
            }
        }
    }
}