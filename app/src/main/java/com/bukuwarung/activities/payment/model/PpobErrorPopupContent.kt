package com.bukuwarung.activities.payment.model

import com.google.gson.annotations.SerializedName

data class PpobErrorPopupContent(
    @SerializedName("ic_ppob_error")
    val icPpobError: String? = null,
    @SerializedName("tv_ppob_error_heading")
    val tvPpobErrorHeading: String? = null,
    @SerializedName("tv_ppob_error_body")
    val tvPpobErrorBody: String? = null,
    @SerializedName("tv_ppob_error_button")
    val tvPpobErrorButton: String? = null,
    @SerializedName("ppob_category")
    val ppobCategory: List<String>? = null,
    @SerializedName("text_after_timer_complete")
    val textAfterTimerComplete: String? = null,
    @SerializedName("start_time_millis")
    val startTimeMillis: Long? = null,
    @SerializedName("error_wait_time_hrs")
    val errorWaitTimeHrs: Int? = null
)
