package com.bukuwarung.activities.payment

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.*
import com.bukuwarung.payments.constants.KycStatus
import com.bukuwarung.payments.constants.isVerified
import com.bukuwarung.payments.data.model.PaymentBannerInfoResponse
import com.bukuwarung.payments.data.model.QrisResponse
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.widget.QrisVerificationView
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.isValidGlideContext
import com.bukuwarung.utils.setSingleClickListener
import com.bumptech.glide.Glide


class PaymentBannerAdapter(
    var callback: Callback? = null,
    var verificationViewCallback: QrisVerificationView.Callback? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var bannerInfoList: List<PaymentBannerInfoResponse> = listOf()
    private var qrisResponse: QrisResponse? = null

    companion object {
        const val KYC_BANNER_TYPE = "kyc"
        const val QRIS_BANNER_TYPE = "qris"
        const val KYB_BANNER_TYPE = "kyb"
        private const val KYB_VIEW_TYPE = 3
        private const val QRIS_VIEW_TYPE = 2
        private const val KYC_VIEW_TYPE = 1
        private const val BANNER_VIEW_TYPE = 0
    }

    interface Callback {
        fun bannerClick(
            url: String, isKyc: Boolean = false,
            status: KycStatus? = null, bannerTitle: String?, index: Int
        )
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            BANNER_VIEW_TYPE -> {
                BannerInfoViewHolder(PpobBannerItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            }
            KYC_VIEW_TYPE -> {
                KycBannerInfoViewHolder(LayoutKycViewBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            }
            QRIS_VIEW_TYPE -> {
                QrisBannerInfoViewHolder(LayoutQrisViewBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            }
            KYB_VIEW_TYPE -> {
                KybBannerInfoViewHolder(LayoutKybVerificationViewBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            }
            else -> {
                BannerInfoViewHolder(PpobBannerItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            }
        }
    }

    override fun getItemCount(): Int = bannerInfoList.size


    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is BannerInfoViewHolder -> {
                holder.bind(bannerInfoList[position])
            }
            is KycBannerInfoViewHolder -> {
                holder.bind(bannerInfoList[position])
            }
            is QrisBannerInfoViewHolder -> {
                holder.bind(bannerInfoList[position])
            }
            is KybBannerInfoViewHolder -> {
                holder.bind()
            }
        }
    }

    fun setItem(
        list: List<PaymentBannerInfoResponse>,
        qrisResponse: QrisResponse?
    ) {
        this.bannerInfoList = list
        this.qrisResponse = qrisResponse
        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int): Int {
        return when (bannerInfoList[position].meta?.bannerType) {
            KYC_BANNER_TYPE -> KYC_VIEW_TYPE
            KYB_BANNER_TYPE -> KYB_VIEW_TYPE
            QRIS_BANNER_TYPE -> {
                // Show Qris verification view if final status is not null
                if (qrisResponse?.finalStatus != null
                    && !PaymentUtils.shouldHideQrisRejectedWidget())
                QRIS_VIEW_TYPE
                else BANNER_VIEW_TYPE
            }
            else -> BANNER_VIEW_TYPE
        }
    }

    inner class BannerInfoViewHolder constructor(private val binding: PpobBannerItemBinding) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setSingleClickListener {
                if (adapterPosition != -1) {
                    val banner = bannerInfoList[adapterPosition]
                    banner.mobileLandingUrl?.let {
                        callback?.bannerClick(it, false, null, banner.title, adapterPosition+1)
                    }
                }
            }
        }

        fun bind(bannerInfo: PaymentBannerInfoResponse) {
            with(binding) {
                bannerInfo.imageUrl?.let { imageUrl ->
                    if (root.context.isValidGlideContext()) {
                        Glide.with(root.context).load(imageUrl).into(ivBanner)
                    }
                }
            }
        }
    }

    inner class KycBannerInfoViewHolder constructor(private val binding: LayoutKycViewBinding) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setSingleClickListener {
                if (adapterPosition != -1) {
                    val banner = bannerInfoList[adapterPosition]
                    val kycStatus = PaymentPrefManager.getInstance().getKycStatus()
                    val kybStatus = PaymentPrefManager.getInstance().getKybStatus()
                    if (kycStatus.isVerified().isTrue && kybStatus.isVerified().isTrue) {
                        return@setSingleClickListener
                    }
                    val url = RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl +
                            "?entryPoint=${AnalyticsConst.PAYMENTS}"
                    val neuroLink = "${AppConst.DEEPLINK_INTERNAL_URL}/web?link=$url"
                    callback?.bannerClick(
                        url = neuroLink,
                        isKyc = true,
                        status = banner.meta?.kycStatus,
                        bannerTitle = banner.title,
                        index = adapterPosition + 1
                    )
                }
            }
        }

        fun bind(bannerInfo: PaymentBannerInfoResponse) {
            binding.layoutKycProcess.setStatus()
        }
    }

    inner class QrisBannerInfoViewHolder constructor(private val binding: LayoutQrisViewBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(bannerInfo: PaymentBannerInfoResponse) {
            qrisResponse?.let {
                binding.layoutQrisProcess.setStatus(
                    it, bannerInfo, callback, verificationViewCallback
                )
            }
        }
    }

    inner class KybBannerInfoViewHolder(private val binding: LayoutKybVerificationViewBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind() {
            binding.tvQrisStatusHeading.text = RemoteConfigUtils.getAppText().kybPendingBannerTitle
            binding.tvQrisStatusBody.text = RemoteConfigUtils.getAppText().kybPendingBannerMessage
        }
    }
}