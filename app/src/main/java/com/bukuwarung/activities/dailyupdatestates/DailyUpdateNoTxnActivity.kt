package com.bukuwarung.activities.dailyupdatestates

import android.app.Dialog
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.ActivityDailyUpdateNoTxnBinding
import com.bukuwarung.utils.isNotNullOrEmpty


class DailyUpdateNoTxnActivity : BaseActivity() {

    private lateinit var binding: ActivityDailyUpdateNoTxnBinding
    lateinit var dialog: Dialog

    private var isFirstTimeUser: Boolean = false
    lateinit var bookId: String

    override fun setViewBinding() {
        binding = ActivityDailyUpdateNoTxnBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)
    }

    override fun onDestroy() {
        super.onDestroy()
        dialog.dismiss()
    }

    override fun onBackPressed() {
        dialog.dismiss()
        finish()
        super.onBackPressed()
    }


    override fun subscribeState() {
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_daily_update_no_txn)

        if (intent.hasExtra(BOOK_ID) && intent.getStringExtra(BOOK_ID).isNotNullOrEmpty()) {
            intent.getStringExtra(BOOK_ID)?.let {
                bookId = it
            } ?: run {
                finish()
            }
        } else {
            finish()
        }

        if (TransactionRepository.getInstance(this).getTransactionCountWithBookId(bookId) == 0) {
            isFirstTimeUser = true
        }

        if (isFirstTimeUser) {
            showNoTransactionStatePopup(true)
        } else {
            showNoTransactionStatePopup(false)
        }
    }

    private fun showNoTransactionStatePopup(isFirstTimeUser: Boolean) {

        dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(R.layout.dialog_daily_reminder_no_txn)

        val dialogWindow = dialog.window
        dialogWindow?.decorView?.setPadding(0, 0, 0, 0)

        val lp: WindowManager.LayoutParams? = dialogWindow?.attributes
        //Set width
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        //Set high
        lp?.height = WindowManager.LayoutParams.WRAP_CONTENT

        dialogWindow?.attributes = lp
        dialog.setCancelable(true)
        dialog.setOnDismissListener { v -> finish() }
        val yesBtn = dialog.findViewById(R.id.ok_btn) as Button

        val title = dialog.findViewById(R.id.tv_title) as TextView
        val message = dialog.findViewById(R.id.tv_message) as TextView

        if (isFirstTimeUser) {
            title.setText(R.string.start_recording_transaction)
            message.setText(R.string.make_record_of_first_transaction)
        } else {
            title.setText(R.string.well_todays_recap_is_blank)
            message.setText(R.string.because_yesterday_you_didnt_make_notes)
        }

        yesBtn.setOnClickListener {


            var bundle = Bundle()
            bundle.putBoolean(FROM_DAILY_UPDATE_NO_TXN, true)
            MainActivity.startActivitySingleTopToTab(this, TabName.TRANSACTION, bundle)
            finish()
        }
        dialog.show()
    }

    companion object {
        @kotlin.jvm.JvmField
        var BOOK_ID: String = "BookId"

        var FROM_DAILY_UPDATE_NO_TXN = "FROM_DAILY_UPDATE_NO_TXN"
    }


}