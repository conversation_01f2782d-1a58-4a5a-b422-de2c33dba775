package com.bukuwarung.activities.card

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Parcelable
import android.view.View
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.activities.card.newcard.BusinessCardDesign
import com.bukuwarung.activities.card.newcard.NewBusinessCardActivity
import com.bukuwarung.activities.card.preview.LayoutToImageHandler
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.google.android.gms.tasks.TaskExecutors
import com.google.android.material.snackbar.Snackbar
import kotlinx.android.synthetic.main.activity_business_card_share.*
import kotlinx.android.synthetic.main.activity_business_card_share.view.*
import kotlinx.android.synthetic.main.business_card_layout.*
import kotlinx.android.synthetic.main.share_option.view.*
import java.io.File

class BusinessCardShareActivity : AppActivity () {

    lateinit var bookEntity: BookEntity
    private var isNewBusinessCardEnabled = RemoteConfigUtils.NewBusinessCard.isEnabled()
    private var from: String? = null

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)

        setContentView(R.layout.activity_business_card_share)
        new_business_card_preview_share.visibility = isNewBusinessCardEnabled.asVisibility()
        business_card_preview_share.visibility = (!isNewBusinessCardEnabled).asVisibility()
        bookEntity = businessRepository.getBusinessByIdSync(User.getBusinessId())

        if (isNewBusinessCardEnabled) {
            renderNewDesignBusinessCard()
        } else {
            renderOldDesignBusinessCard()
        }

        setShareOptions()

        checkForWhatsappInstagram()

        btn_close.setOnClickListener {
            super.onBackPressed()
        }

        editBusinessCard.setOnClickListener {

            AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_BUSINESS_CARD)

            val isNewBusinessCardEnabled = RemoteConfigUtils.NewBusinessCard.isEnabled()
            val clazz = if (isNewBusinessCardEnabled) NewBusinessCardActivity::class.java else BusinessCardActivity::class.java
            startActivity(Intent(this, clazz))
            finish()
        }

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

        ll_download.setOnClickListener {
            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23) {
                requestPermissions(PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR, PermissionConst.WRITE_EXTERNAL_STORAGE)
            } else {
                downloadBusinessCard()
                Snackbar.make(it, "File berhasil diunduh", Snackbar.LENGTH_LONG).show()
            }
        }
    }


    private fun downloadBusinessCard() {
        ImageUtils.saveLayoutConvertedImage(if (isNewBusinessCardEnabled) new_business_card_preview_share else business_card_preview_share, true)

        val downloadDirectory = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "business_card.png")

        val path = Uri.fromFile(downloadDirectory)
        val imageOpenIntent = Intent(Intent.ACTION_VIEW)
        imageOpenIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        imageOpenIntent.setDataAndType(path, "image/*")
        try {
            this.startActivity(imageOpenIntent)
        } catch (e: ActivityNotFoundException) {
        }
    }

    private fun renderOldDesignBusinessCard() {
        if (Utility.isBlank(bookEntity.businessOwnerName)) {
            nameLayout.visibility = View.GONE
        }
        if (Utility.isBlank(bookEntity.businessPhone)) {
            phoneLayout.visibility = View.GONE
        }
        if (Utility.isBlank(bookEntity.businessEmail)) {
            emailLayout.visibility = View.GONE
        }
        if (Utility.isBlank(bookEntity.businessAddress)) {
            addrLayout.visibility = View.GONE
        }

        business_card_canvas.setBackgroundResource(FeaturePrefManager.getInstance().cardColor)

        setTextView("Hi, " + bookEntity.businessOwnerName, "", tv_share_name)
        setTextView(bookEntity.businessName, getString(R.string.business_card_shop_name), name)
        setTextView(Utility.beautifyPhoneNumber(bookEntity.businessPhone), getString(R.string.phone_label), phone)
        if (!bookEntity.businessTagLine.isNullOrEmpty()) {
            setTextView(bookEntity.businessTagLine, "", caption)
        }
        setTextView(bookEntity.businessOwnerName, getString(R.string.owner_name_label), owner)
        setTextView(bookEntity.businessEmail, getString(R.string.email_label), email)
        setTextView(bookEntity.businessAddress, getString(R.string.address_label), address)
    }

    private fun renderNewDesignBusinessCard() {
        setTextView("Hi, " + bookEntity.businessOwnerName, "", tv_share_name)
        val design = intent.getSerializableExtra(NEW_CARD_DESIGN) as BusinessCardDesign
        new_business_card_preview_share.setData(bookEntity, design)
    }

    private fun setTextView(value: String?, emptyMsg: String, targetTv: TextView) {
        if (!Utility.isBlank(value)) {
            targetTv.text = value
        } else {
            targetTv.text = emptyMsg
        }
    }

    private fun setShareOptions() {
        var view = share_instagram
        val layout = if (isNewBusinessCardEnabled) new_business_card_preview_share else business_card_preview_share

        with(view) {
            iv_share.setImageResource(R.drawable.instagram_icon)
//            iv_share.setImageResource(R.drawable.insta)
            tv_share.text = "Instagram"

            iv_share.setOnClickListener {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.INSTAGRAM)
                if (from.isNotNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SHARE_FROM, propBuilder)
                ImageUtils.saveLayoutConvertedImage(layout, false).continueWith(TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(this@BusinessCardShareActivity, "", null, false, true))
            }
        }

        view = share_whatsapp

        with(view) {
            iv_share.setBackgroundResource(R.drawable.whatsapp_icon)
//            iv_share.setImageResource(R.mipmap.ic_whatsapp_white_24dp)
            tv_share.text = getString(R.string.entries_whatsapp)

            iv_share.setOnClickListener {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.WHATSAPP)

                if (from.isNotNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SHARE_FROM, propBuilder)

                ImageUtils.saveLayoutConvertedImage(layout, false).continueWith(TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(this@BusinessCardShareActivity, "com.whatsapp", null, true, false))
            }
        }

        view = share_normal

        with(view) {
            iv_share.setBackgroundResource(R.drawable.other_share_icon)
//            iv_share.setImageResource(R.drawable.share_white)
            tv_share.text = getString(R.string.tab_others_label)

            iv_share.setOnClickListener {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.OTHER)

                if (from.isNotNullOrEmpty()) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
                }

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SHARE_FROM, propBuilder)
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_BUSINESS_CARD_SHARE_PREVIEW, this@BusinessCardShareActivity)
                ImageUtils.saveLayoutConvertedImage(layout, false).continueWith(TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(this@BusinessCardShareActivity, "", null, false, false))
            }
        }
    }

    override fun onResume() {
        super.onResume()
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_SAVE_BUSINESS_CARD, this)
        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

    }

    private fun checkForWhatsappInstagram() {
        val packageManager = this.packageManager
        var isInstalled = ShareUtils.isPackageInstalled("com.whatsapp", packageManager)

        if (!isInstalled) {
            share_whatsapp.visibility = View.GONE
        }

        isInstalled = ShareUtils.isPackageInstalled("com.instagram.android", packageManager)
        if (!isInstalled) {
            share_instagram.visibility = View.GONE
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.WRITE_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    downloadBusinessCard()
                }
            }
        }
    }

    companion object{
        const val NEW_CARD_DESIGN = "NEW_CARD_DESIGN"
        const val FROM = "from"
    }
}