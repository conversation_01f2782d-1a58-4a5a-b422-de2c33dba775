package com.bukuwarung.activities.card.newcard

import androidx.annotation.Keep
import kotlinx.android.parcel.RawValue
import java.io.Serializable

const val white = "#FFFFFF"

@Keep
data class BusinessCardDesign(
        var cardUID: String,
        var backgroundUrl: @RawValue Any,
        var topTextColor: String = white,
        var tagLineTextColor: String = white,
        var bottomTextColor: String = white,
        var iconColor: String = "#FAAD17",
        var iconBgColor: String = white,
        var loadedImage: Boolean = false
) : Serializable