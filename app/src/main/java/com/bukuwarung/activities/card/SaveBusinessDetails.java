package com.bukuwarung.activities.card;

import com.bukuwarung.Application;
import com.bukuwarung.database.entity.AppConfig;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.ReferralRepository;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.session.User;

public class SaveBusinessDetails implements Runnable {

    final String businessName;
    final String businessOwnerName;
    final String businessTagLine;
    final String businessPhone;
    final String businessAddress;
    final String businessEmail;

    public SaveBusinessDetails(String businessName, String businessOwnerName, String businessTagLine, String businessPhone, String businessAddress, String businessEmail) {
        this.businessName = businessName;
        this.businessOwnerName = businessOwnerName;
        this.businessTagLine = businessTagLine;
        this.businessPhone = businessPhone;
        this.businessAddress = businessAddress;
        this.businessEmail = businessEmail;
    }



    public final void run() {
        BusinessRepository businessRepository = BusinessRepository.getInstance(Application.getAppContext());
        String userId = User.getUserId();
        String deviceId = User.getDeviceId();
        String bookId = User.getBusinessId();
        businessRepository.updateBusinessProfileComplete(userId, deviceId, bookId, businessName, businessOwnerName, businessTagLine, businessPhone, businessAddress, businessEmail);

        if (AppConfigManager.getInstance().useReferral()) {
            // try to add points for business card completion if feasible
            BookEntity newBookEntity = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
            ReferralRepository.getInstance().addTransactionPoints(newBookEntity, true);
        }
    }
}
