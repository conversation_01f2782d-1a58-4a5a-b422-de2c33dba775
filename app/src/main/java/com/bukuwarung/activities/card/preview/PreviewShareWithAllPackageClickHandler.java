package com.bukuwarung.activities.card.preview;

import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.utils.Utility;


final class PreviewShareWithAllPackageClickHandler implements OnClickListener {
    final CardPreviewShareDialog dialog;

    PreviewShareWithAllPackageClickHandler(CardPreviewShareDialog shareBusinessCardDialog) {
        this.dialog = shareBusinessCardDialog;
    }

    public final void onClick(View view) {
        if (Utility.hasInternet()) {
            AppAnalytics.trackEvent("business_card_other_option");
            this.dialog.convertLayoutToImage("", this.dialog.getCardContainerLayout(), this.dialog.getTargetContact(), false);
        }
    }
}
