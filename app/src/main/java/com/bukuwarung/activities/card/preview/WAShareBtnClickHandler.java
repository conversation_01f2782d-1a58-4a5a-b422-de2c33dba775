package com.bukuwarung.activities.card.preview;

import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.utils.Utility;

import static com.bukuwarung.constants.AppConst.WA_PACKAGE;


final class WAShareBtnClickHandler implements OnClickListener {
    final CardPreviewShareDialog dialog;

    WAShareBtnClickHandler(CardPreviewShareDialog shareBusinessCardDialog) {
        this.dialog = shareBusinessCardDialog;
    }

    public final void onClick(View view) {
        if (Utility.hasInternet()) {
            this.dialog.convertLayoutToImage(WA_PACKAGE, this.dialog.getCardContainerLayout(), this.dialog.getTargetContact(), true);

        }
    }
}
