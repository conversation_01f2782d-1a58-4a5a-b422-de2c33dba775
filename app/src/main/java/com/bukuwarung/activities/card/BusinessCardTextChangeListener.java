package com.bukuwarung.activities.card;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bukuwarung.R;


public class BusinessCardTextChangeListener implements TextWatcher {

    final BusinessCardActivity activity;
    TextView targetTv;
    LinearLayout targetLayout;

    public void beforeTextChanged(CharSequence charSequence, int i, int i2, int i3) {
    }

    public void onTextChanged(CharSequence charSequence, int i, int i2, int i3) {
    }

    BusinessCardTextChangeListener(BusinessCardActivity businessCardActivity, TextView target, LinearLayout targetLayout) {
        this.activity = businessCardActivity;
        this.targetTv = target;
        this.targetLayout = targetLayout;
    }

    public void afterTextChanged(Editable editable) {
        this.targetTv.setText(String.valueOf(editable));
        if (String.valueOf(editable).length() > 0) {
            if(targetLayout!=null && targetLayout.getVisibility() == View.GONE){
                this.targetLayout.setVisibility(View.VISIBLE);
            }
        } else if(targetLayout!=null){
            this.targetLayout.setVisibility(View.GONE);
        }
    }
}
