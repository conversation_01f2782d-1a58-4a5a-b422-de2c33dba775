package com.bukuwarung.activities.card.preview;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.utils.ImageUtils;
import com.bukuwarung.utils.Utility;
import com.google.android.material.button.MaterialButton;

import static android.view.Window.FEATURE_NO_TITLE;
import static android.widget.ListPopupWindow.MATCH_PARENT;
import static android.widget.ListPopupWindow.WRAP_CONTENT;

import androidx.appcompat.widget.AppCompatImageView;

public final class CardPreviewShareDialog extends Dialog{

    private final Activity activity;
    public TextView address;
    private final BookEntity bookEntity;
    public LinearLayout card_layout;
    public ImageView close;
    public TextView email;
    public ImageView emailIcon;
    private final String targetContact;
    public MaterialButton moreOptions;
    public TextView name;
    public TextView owner;
    public ImageView ownerIcon;
    public TextView phone;
    public AppCompatImageView profile;
    public MaterialButton shareOnWhatsapp;
    public TextView caption;
    private int color;

    public CardPreviewShareDialog(Activity activity, String shareWith, BookEntity bookEntity, int color) {
        super(activity);
        this.activity = activity;
        this.targetContact = shareWith;
        this.bookEntity = bookEntity;
        this.color = color;
    }

    public final Activity getActivity() {
        return this.activity;
    }

    public final String getTargetContact() {
        return this.targetContact;
    }

    public final LinearLayout getCardContainerLayout() {
        LinearLayout linearLayout = this.card_layout;
        return linearLayout;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        requestWindowFeature(FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_preview_share_layout_component);
        Window window = getWindow();
        window.setLayout(MATCH_PARENT, WRAP_CONTENT);
        getWindow().setGravity(Gravity.BOTTOM);
        
        this.close = findViewById(R.id.close);
        this.emailIcon = findViewById(R.id.emailIcon);
        this.ownerIcon = findViewById(R.id.ownerIcon);
        this.profile = findViewById(R.id.profilePic);
        this.name = findViewById(R.id.name);
        this.caption = findViewById(R.id.caption);
        this.owner = findViewById(R.id.owner);
        this.phone = findViewById(R.id.phone);
        this.email = findViewById(R.id.email);
        this.address = findViewById(R.id.address);
        this.card_layout = findViewById(R.id.card_layout);

        this.moreOptions = findViewById(R.id.moreOptions);
        this.shareOnWhatsapp = findViewById(R.id.shareOnWhatsapp);

        this.card_layout.setBackgroundColor(color);
        
        if (bookEntity != null) {
            if (Utility.isBlank(bookEntity.businessImage)) {
                this.profile.setVisibility(View.GONE);
            } else {
                this.profile.setVisibility(View.VISIBLE);
                ImageUtils.loadImageCircleCropped(
                    getContext(), profile, bookEntity.businessImage,
                    R.color.white, R.mipmap.default_icon
                );
            }
            this.name.setText(bookEntity.businessName);
            this.caption.setText(bookEntity.businessTagLine);
            this.phone.setText(Utility.beautifyPhoneNumber(bookEntity.businessPhone));
            this.address.setText(bookEntity.businessAddress);
            if (!Utility.isBlank(bookEntity.businessEmail)) {
                this.email.setText(bookEntity.businessEmail);
            } else {
                this.email.setVisibility(View.GONE);
                this.emailIcon.setVisibility(View.GONE);
            }
            if (!Utility.isBlank(bookEntity.businessOwnerName)) {
                this.owner.setText(bookEntity.businessOwnerName);
            } else {
                this.owner.setVisibility(View.GONE);
                this.ownerIcon.setVisibility(View.GONE);
            }
            this.shareOnWhatsapp.setOnClickListener(new WAShareBtnClickHandler(this));
            this.close.setOnClickListener(new PreviewCloseBtnClickHandler(this));
            this.moreOptions.setOnClickListener(new PreviewShareWithAllPackageClickHandler(this));
        }
    }

    public final void convertLayoutToImage(String packageNm, View view, String shareWith, boolean whatsappClicked) {
//        ImageUtils.saveLayoutConvertedImage(view).continueWith(TaskExecutors.MAIN_THREAD, new LayoutToImageHandler(this, packageNm, shareWith, whatsappClicked));
    }
}
