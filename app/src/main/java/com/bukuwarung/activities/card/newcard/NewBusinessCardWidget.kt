package com.bukuwarung.activities.card.newcard

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.bukuwarung.R
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.databinding.NewBusinessCardLayoutBinding
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.extensions.loadCircleImage
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.loadImage

class NewBusinessCardWidget @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var cardDesign: BusinessCardDesign? = null
    private var bookEntity: BookEntity? = null
    private val binding = NewBusinessCardLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    fun setData(book: BookEntity?, design: BusinessCardDesign?) {
        bookEntity = book
        cardDesign = design
        setupView()
    }

    private fun setupView() {
        cardDesign ?: return
        bookEntity ?: return

        binding.apply {
            cardContainer.tag = cardDesign!!.cardUID

            when (cardDesign!!.backgroundUrl) {
                is String -> cardBackground.loadImage(cardDesign!!.backgroundUrl as String, R.drawable.app_logo)
                is Int -> cardBackground.setImageResource(cardDesign!!.backgroundUrl as Int)
            }

            with(cardDesign!!.topTextColor) {
                val color = Color.parseColor(this)
                cardShopTitle.setTextColor(color)
                cardShopOwner.setTextColor(color)
            }

            with(cardDesign!!.tagLineTextColor){
                tvSlogan.setTextColor(Color.parseColor(this))
            }

            with(cardDesign!!.bottomTextColor) {
                val color = Color.parseColor(this)
                cardShopPhone.setTextColor(color)
                cardShopAddress.setTextColor(color)
            }

            with(cardDesign!!.iconColor) {
                val color = Color.parseColor(this)
                cardShopPhoneIcon.drawable.setTint(color)
                cardShopLinkIcon.drawable.setTint(color)
            }

            with(cardDesign!!.iconBgColor) {
                val drawable = android.graphics.drawable.ColorDrawable(Color.parseColor(this))
                cardShopPhoneIconBg.loadCircleImage(drawable)
                cardShopLinkIconBg.loadCircleImage(drawable)
            }

            bookEntity?.run {
                cardShopTitle.text = businessName
                cardShopOwner.text = businessOwnerName
                tvSlogan.text = businessTagLine

                with(businessPhone) {
                    cardShopPhone.text = Utility.beautifyPhoneNumber(this)
                    val visibility = this.isNotNullOrEmpty().asVisibility()
                    cardShopPhone.visibility = visibility
                    cardShopPhoneIconBg.visibility = visibility
                    cardShopPhoneIcon.visibility = visibility
                }

                with(businessAddress) {
                    if(!this.isNullOrEmpty()) {
                        cardShopAddress.text = Utility.getCompleteAddress(this@run)
                        val visibility = this.isNotEmpty().asVisibility()
                        cardShopAddress.visibility = visibility
                        cardShopLinkIconBg.visibility = visibility
                        cardShopLinkIcon.visibility = visibility
                    }
                }
            }

        }
    }
}