package com.bukuwarung.activities.card.preview;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;

import com.bukuwarung.activities.card.BusinessCardActivity;
import com.bukuwarung.enums.Language;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.ImageUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.Continuation;
import com.google.android.gms.tasks.Task;

public final class LayoutToImageHandler implements Continuation<ImageUtils.SaveToDiskTaskResult, Object> {

    final boolean whatsappShare;
    final boolean instagramShare;
    final String shareWithContact;
    final String shareOn;
    final Activity parent;

    public LayoutToImageHandler(Activity activity, String shareOn, String shareWithContact, boolean isWAClicked, boolean isInstaClicked) {
        this.parent = activity;
        this.shareOn = shareOn;
        this.shareWithContact = shareWithContact;
        this.whatsappShare = isWAClicked;
        this.instagramShare = isInstaClicked;
    }

    public Object then(Task<ImageUtils.SaveToDiskTaskResult> task) {
        if (task.getResult() != null) {
            Object result = task.getResult();
            if (((ImageUtils.SaveToDiskTaskResult) result).uri != null) {
                try {
                    String sharingReqStr = "Berikut kartu nama saya, Anda bisa hubungi saya di kontak tersebut. Semoga kerjasama kita berjalan makin lancar \uD83D\uDE0A\n\n" +
                            "Kartu nama dibuat secara gratis dengan aplikasi BukuWarung\n" +
                            "https://bukuwarung.id/unduhGratis-b";
                    if(SessionManager.getInstance().getAppLanguage() == Language.ENGLISH.getLangCd()){
                        sharingReqStr = "Shared via BukuWarung http://bukuwarung.com/app";
                    }
                    if (result == null) {
                        return null;
                    }
                    Uri uri = ((ImageUtils.SaveToDiskTaskResult) result).uri;
                    shareLayoutImage(this.shareOn, uri, sharingReqStr, this.shareWithContact, this.whatsappShare, this.instagramShare);
                } catch (Exception e) {
                    e.printStackTrace();
                    FirebaseCrashlytics.getInstance().recordException(e);
                }
                return null;
            }
        }
        return null;
    }

    public final void shareLayoutImage(String packageNm, Uri imageUri, String shareMessage, String shareWith, boolean waContact, boolean isInsta) {
        Intent intent;
        StringBuilder shareWithContact = new StringBuilder();
        String countryCode = SessionManager.getInstance().getCountryCode();
        if (countryCode != null) {
            String codeWithoutPlus = countryCode.substring(1);
            shareWithContact.append(codeWithoutPlus);
            shareWithContact.append(Utility.cleanPhonenumber(shareWith));
            if (waContact) {
                intent = ComponentUtil.getShareUriWithPackage(packageNm, imageUri, shareWithContact.toString(), shareMessage);
            } else if (isInsta) {
                String sourceApplication = "com.bukuwarung";

                intent = new Intent("com.instagram.share.ADD_TO_STORY");
                intent.putExtra("source_application", sourceApplication);

                intent.setType("image/*");
                intent.putExtra("interactive_asset_uri", imageUri);

                parent.grantUriPermission(
                        "com.instagram.android", imageUri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
                if (parent.getPackageManager().resolveActivity(intent, 0) != null) {
                    parent.startActivityForResult(intent, 0);
                }
            } else {
                intent = ComponentUtil.shareOnAnyApp(imageUri, shareMessage);
            }
            try {
                parent.startActivity(Intent.createChooser(intent, "Choose an app"));
            } catch (ActivityNotFoundException e) {
                NotificationUtils.alertToast("WhatApp Not Installed");
                e.printStackTrace();
                FirebaseCrashlytics.getInstance().recordException(e);
            }
        }
    }
}
