package com.bukuwarung.activities.card;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LiveData;
import androidx.work.Data;
import androidx.work.WorkInfo;

import com.bukuwarung.R;
import com.bukuwarung.activities.card.preview.LayoutToImageHandler;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DefaultAnim;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.dialogs.confirmation_dialog.StandardConfirmationDialog;
import com.bukuwarung.location.LocationUtil;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ImageUtils;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.Utility;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.gms.tasks.TaskExecutors;
import com.google.android.material.button.MaterialButton;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

public final class BusinessCardActivity extends AppActivity implements View.OnClickListener {

    private BookEntity bookEntity;

    public EditText businessAddresss;
    public EditText businessEmail;
    public EditText businessName;
    public EditText businessPhone;
    public EditText businessTagLine;
    public EditText ownerName;

    public TextView email;
    public TextView name;
    public TextView owner;
    public TextView caption;
    public TextView address;
    public TextView phone;

    public AppCompatImageView businessProfile;
    private LinearLayout nameLayout;
    private LinearLayout phoneLayout;
    private LinearLayout emailLayout;
    private LinearLayout addrLayout;
    private LinearLayout captionLayout;
    ConstraintLayout cardCanvas;

    public static String BUSINESS_NAME = "name";
    public static String OWNER_NAME = "ownerName";
    public static String BUSINESS_TAGLINE = "businessTagLine";
    public static String CLEAN_PHONE_NUMBER = "cleanPhoneNumber";
    public static String BUSINESS_ADDRESS = "businessAddress";
    public static String BUSINESS_EMAIL = "businessEmail";

    public BusinessCardActivity() {
        super(new DefaultAnim());
    }

    public int getBacgroundColor() {
        return R.drawable.bcard1;
    }

    public void onClickThumbnail(View v) {
        NotificationUtils.alertError(v.toString());
    }

    public void onCreate(Bundle bundle) {

        super.onCreate(bundle);

        setContentView((int) R.layout.activity_business_card);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(getString(R.string.business_card));
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }

        this.businessName = findViewById(R.id.businessName);
        this.ownerName = findViewById(R.id.ownerName);
        this.businessPhone = findViewById(R.id.businessPhone);
        this.businessTagLine = findViewById(R.id.businessTagLine);
        this.businessAddresss = findViewById(R.id.businessAddresss);
        this.businessEmail = findViewById(R.id.businessEmail);
        MaterialButton saveBtn = findViewById(R.id.btn_save);
        this.captionLayout = findViewById(R.id.captionLayout);

        MaterialButton shareBusinessCard = findViewById(R.id.btn_share);
        this.name = findViewById(R.id.name);
        this.caption = findViewById(R.id.caption);
        this.owner = findViewById(R.id.owner);
        this.phone = findViewById(R.id.phone);
        this.email = findViewById(R.id.email);
        this.address = findViewById(R.id.address);

        this.nameLayout = findViewById(R.id.nameLayout);
        this.phoneLayout = findViewById(R.id.phoneLayout);
        this.emailLayout = findViewById(R.id.emailLayout);
        this.addrLayout = findViewById(R.id.addrLayout);
        cardCanvas = findViewById(R.id.business_card_canvas);

        this.bookEntity = businessRepository.getBusinessByIdSync(User.getBusinessId());
        final Activity activity = this;
        findViewById(R.id.backBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                InputUtils.hideKeyBoardWithCheck(activity);
                finish();
                onBackPressed();
            }
        });

        init();

        findViewById(R.id.bcard1).setOnClickListener(this);
        findViewById(R.id.bcard2).setOnClickListener(this);
        findViewById(R.id.bcard3).setOnClickListener(this);
        findViewById(R.id.bcard4).setOnClickListener(this);
        findViewById(R.id.bcard5).setOnClickListener(this);

        shareBusinessCard.setOnClickListener(view -> shareBusinessCard());
        saveBtn.setOnClickListener(view -> onSave());
        setCardTextChangeListeners();

        businessAddresss.setOnFocusChangeListener((view, b) -> {
            if (view.isFocused()) {
                if (!PermissonUtil.hasLocationPermission()) {
                    PermissonUtil.requestLocationPermission(this);
                } else {
                    storeLocation();
                }
            }
        });
    }

    private void shareBusinessCard() {
        if(!confirmValidBusinessCard()) return;

        onSave();
        String extraMobile = getIntent().getStringExtra("mobile");
        convertLayoutToImage("", findViewById(R.id.business_card_canvas), extraMobile, false, false);
    }
    private void onSave() {
        if(!confirmValidBusinessCard()) return;
        /*if (getCurrentFocus() != null) {
            getCurrentFocus().clearFocus();
        }*/
        saveProfileData(true);
        FeaturePrefManager.getInstance().setSimpanForBusinessCardClicked();
    }

    private final void setCardTextChangeListeners() {
        this.businessName.addTextChangedListener(new BusinessCardTextChangeListener(this, name, null));
        this.businessTagLine.addTextChangedListener(new BusinessCardTextChangeListener(this, caption, captionLayout));
        this.ownerName.addTextChangedListener(new BusinessCardTextChangeListener(this, owner, nameLayout));
        this.businessPhone.addTextChangedListener(new BusinessCardTextChangeListener(this, phone, phoneLayout));
        this.businessEmail.addTextChangedListener(new BusinessCardTextChangeListener(this, email, emailLayout));
        this.businessAddresss.addTextChangedListener(new BusinessCardTextChangeListener(this, address, addrLayout));
    }

    private final void init() {
        if (this.bookEntity != null) {
            setBusinessPhoto();
            initFormValues();
            renderBusinessCardArea();
        }
    }

    @Override
    public void onBackPressed() {
        InputUtils.hideKeyBoardWithCheck(this);
        this.businessName.clearFocus();
        this.ownerName.clearFocus();
        this.businessPhone.clearFocus();
        this.businessAddresss.clearFocus();
        this.businessTagLine.clearFocus();
        this.businessEmail.clearFocus();
        super.onBackPressed();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull @NotNull String[] permissions, @NonNull @NotNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PermissionConst.ACCESS_LOCATION) {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_ALLOW);
            } else {
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_DENY);
            }
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_LOCATION_PERMISSION_REQUEST, propBuilder);

            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                storeLocation();
            }
        }
    }

    private void storeLocation() {
        try {
            LiveData<WorkInfo> workInfoLiveData = LocationUtil.INSTANCE.getLocation(this, "business card");
            if (workInfoLiveData != null) {
                workInfoLiveData.observe(this, workInfo -> {
                    if (workInfo != null && workInfo.getState() == WorkInfo.State.SUCCEEDED) {
                        Data workInfoOutputData = workInfo.getOutputData();
                        String streetName = workInfoOutputData.getString("streetName");
                        businessAddresss.setText(streetName);
                        businessAddresss.setSelection(businessAddresss.getText().length());
                    }
                });
            }
        } catch (Exception ex) {
            FirebaseCrashlytics.getInstance().recordException(ex);
        }
    }

    private void renderBusinessCardArea() {
        setTextView(bookEntity.businessName, getString(R.string.business_card_shop_name), name);
        setTextView(Utility.beautifyPhoneNumber(bookEntity.businessPhone), getString(R.string.phone_label), phone);
        setTextView(bookEntity.businessTagLine, "", caption);
        setTextView(bookEntity.businessOwnerName, getString(R.string.owner_name_label), owner);
        setTextView(bookEntity.businessEmail, getString(R.string.email_label), email);
        setTextView(bookEntity.businessAddress, getString(R.string.address_label), address);
    }

    private void initFormValues() {
        try {
            if (Utility.isBlank(bookEntity.businessOwnerName)) {
                nameLayout.setVisibility(View.GONE);
            }
            if (Utility.isBlank(bookEntity.businessPhone)) {
                phoneLayout.setVisibility(View.GONE);
            }
            if (Utility.isBlank(bookEntity.businessEmail)) {
                emailLayout.setVisibility(View.GONE);
            }
            if (Utility.isBlank(bookEntity.businessAddress)) {
                addrLayout.setVisibility(View.GONE);
            }
            this.businessName.setText(bookEntity.businessName);
            this.businessPhone.setText(Utility.beautifyPhoneNumber(bookEntity.businessPhone));
            this.businessEmail.setText(bookEntity.businessEmail);
            this.businessAddresss.setText(bookEntity.businessAddress);
            this.businessTagLine.setText(bookEntity.businessTagLine);
            this.ownerName.setText(bookEntity.businessOwnerName);
            cardCanvas.setBackground(ContextCompat.getDrawable(this, FeaturePrefManager.getInstance().getCardColor()));
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private void setBusinessPhoto() {
        try {
            if (Utility.isBlank(bookEntity.businessImage)) {
                this.businessProfile.setVisibility(View.GONE);
            } else {
                this.businessProfile.setVisibility(View.VISIBLE);
                ImageUtils.loadImageCircleCropped(
                    this, businessProfile, bookEntity.businessImage,
                    R.color.white, R.mipmap.default_icon
                );
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setTextView(String value, String emptyMsg, TextView targetTv) {
        if (!Utility.isBlank(value)) {
            targetTv.setText(value);
        } else {
            targetTv.setText(emptyMsg);
        }
    }

    public final void saveProfileData(boolean save) {
        InputUtils.hideKeyBoardWithCheck(this);
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SAVE_BUSINESS_CARD);
        String cleanPhoneNumber = Utility.cleanPhonenumber(getValue(businessPhone));
        saveBusinessDetails(getValue(businessName), getValue(ownerName), getValue(businessTagLine), cleanPhoneNumber, getValue(businessAddresss), getValue(businessEmail));
        SessionManager.getInstance().setHasEditCard(true);
        if(bookEntity!=null && !Utility.isBlank(bookEntity.businessEmail)) {
            //MoEHelper.getInstance(this).setEmail(bookEntity.businessEmail);
            AppAnalytics.setUserProperty("email",bookEntity.businessEmail);
        }
        Intent intent = new Intent(this, BusinessCardShareActivity.class);
        intent.putExtra(BUSINESS_NAME, getValue(businessName));
        intent.putExtra(OWNER_NAME, getValue(ownerName));
        intent.putExtra(BUSINESS_TAGLINE, getValue(businessTagLine));
        intent.putExtra(CLEAN_PHONE_NUMBER, cleanPhoneNumber);
        intent.putExtra(BUSINESS_ADDRESS, getValue(businessAddresss));
        intent.putExtra(BUSINESS_EMAIL, getValue(businessEmail));

        startActivity(intent);
        finish();

    }

    private boolean confirmValidBusinessCard() {
        String cleanPhoneNumber = Utility.cleanPhonenumber(getValue(businessPhone));
        if (Utility.isBlank(cleanPhoneNumber) || Utility.isBlank(getValue(businessName))) {
            final StandardConfirmationDialog dialog =
                    new StandardConfirmationDialog(this, "Info kartu nama belum lengkap", "Lengkapi informasi pada kartu nama sebelum bisa kamu bagikan");
            dialog.show();
            return false;
        }
        return true;
    }

    private String getValue(EditText formField) {
        String formValueStr = formField.getText().toString();
        if (formValueStr != null) {
            return formValueStr.trim();
        }
        return null;
    }

    private final void saveBusinessDetails(String businessName, String businessOwnerName, String businessTagLine, String businessPhone, String businessAddress, String businessEmail) {
        new Thread(new SaveBusinessDetails(businessName, businessOwnerName, businessTagLine, businessPhone, businessAddress, businessEmail)).start();
    }

    @Override
    public void onClick(View view) {

        switch (view.getId()) {
            case R.id.bcard1:
                cardCanvas.setBackground(ContextCompat.getDrawable(this, R.drawable.bcard1));
                AppAnalytics.trackEvent("change_business_card_template", "color", "template_1");
                FeaturePrefManager.getInstance().setCardColor(R.drawable.bcard1);
                break;
            case R.id.bcard2:
                cardCanvas.setBackground(ContextCompat.getDrawable(this, R.drawable.bcard2));
                AppAnalytics.trackEvent("change_business_card_template", "color", "template_2");
                FeaturePrefManager.getInstance().setCardColor(R.drawable.bcard2);
                break;
            case R.id.bcard3:
                cardCanvas.setBackground(ContextCompat.getDrawable(this, R.drawable.bcard3));
                AppAnalytics.trackEvent("change_business_card_template", "color", "template_3");
                FeaturePrefManager.getInstance().setCardColor(R.drawable.bcard3);
                break;
            case R.id.bcard4:
                cardCanvas.setBackground(ContextCompat.getDrawable(this, R.drawable.bcard4));
                AppAnalytics.trackEvent("change_business_card_template", "color", "template_4");
                FeaturePrefManager.getInstance().setCardColor(R.drawable.bcard4);
                break;
            case R.id.bcard5:
                cardCanvas.setBackground(ContextCompat.getDrawable(this, R.drawable.bcard5));
                AppAnalytics.trackEvent("change_business_card_template", "color", "template_5");
                FeaturePrefManager.getInstance().setCardColor(R.drawable.bcard5);
                break;
        }
    }

    public final void convertLayoutToImage(String packageNm, View view, String shareWith, boolean whatsappClicked, boolean isInstaClicked) {
        ImageUtils.saveLayoutConvertedImage(view, false).continueWith(TaskExecutors.MAIN_THREAD, new LayoutToImageHandler(this, packageNm, shareWith, whatsappClicked, isInstaClicked));
    }
}
