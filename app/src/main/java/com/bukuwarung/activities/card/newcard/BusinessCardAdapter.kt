package com.bukuwarung.activities.card.newcard

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.viewpager.widget.PagerAdapter
import com.bukuwarung.database.entity.BookEntity

class BusinessCardAdapter(private val context: Context) : PagerAdapter() {
    private var businessCard = listOf<BusinessCardDesign>()
    private var bookEntity: BookEntity? = null

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val card = NewBusinessCardWidget(context).apply { setData(bookEntity, businessCard[position]) }
        container.addView(card)
        return card
    }

    override fun isViewFromObject(view: View, `object`: Any): Boolean = view == `object`

    override fun getCount(): Int = businessCard.size

    override fun getItemPosition(item: Any): Int = POSITION_NONE

    override fun destroyItem(collection: ViewGroup, position: Int, view: Any) {
        collection.removeView(view as View)
    }

    fun updateCardDesign(list: List<BusinessCardDesign>) {
        businessCard = list
        notifyDataSetChanged()
    }

    fun updateBusinessData(data: BookEntity?) {
        bookEntity = data
        notifyDataSetChanged()
    }

}