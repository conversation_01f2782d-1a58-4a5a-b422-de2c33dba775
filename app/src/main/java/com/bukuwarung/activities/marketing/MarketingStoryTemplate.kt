package com.bukuwarung.activities.marketing

import androidx.annotation.Keep
import com.bukuwarung.database.entity.AppEntity
import com.google.gson.annotations.SerializedName

data class MarketingTemplate(
        @SerializedName("data") val list: List<MarketingStoryTemplate> = emptyList()
)

@Keep
data class MarketingStoryTemplate(
        var story_id:String? = null,
        var background_image: String? = null,
        var title_text: String? = null,
        var title_text_color: String? = null,
        var detail_text: String? = null,
        var detail_text_color: String? = null,
        var footer_text: String? = null,
        var footer_text_color: String? = null,
        var store_link_tint_color: String? = null,
        var store_link_text_color: String? = null
) : AppEntity()

enum class MarketingType{
    STATUS, POSTER
}