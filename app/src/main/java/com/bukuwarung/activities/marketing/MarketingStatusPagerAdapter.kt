package com.bukuwarung.activities.marketing

import android.content.Context
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.cardview.widget.CardView
import androidx.viewpager.widget.PagerAdapter
import com.bukuwarung.R
import com.bukuwarung.activities.marketing.MarketingType.*
import com.bukuwarung.utils.loadImage

class MarketingStatusPagerAdapter(
        context: Context,
        private var socialMediaStoriesCards: List<MarketingStoryTemplate>,
        private var bookName: String,
        private val marketingType: MarketingType
) : PagerAdapter() {
    private val inflater: LayoutInflater = LayoutInflater.from(context)

    override fun instantiateItem(collection: ViewGroup, position: Int): Any {
        val layoutRes = when(marketingType){
            STATUS -> R.layout.marketing_status_card_layout
            POSTER -> R.layout.marketing_poster_card_layout
        }

        val card = socialMediaStoriesCards[position]
        val layout = inflater.inflate(
                layoutRes,
                collection,
                false
        ) as ViewGroup

        val cardContainer = layout.findViewById<CardView>(R.id.card_container)
        cardContainer.tag = card.story_id


        val backgroundImageView = layout.findViewById<ImageView>(R.id.backgroundImageView)
        val tvTitle = layout.findViewById<AppCompatTextView>(R.id.tvTitle)
        val tvDetail = layout.findViewById<AppCompatTextView>(R.id.tvDetail)
        val tvStoreLinkMessage = layout.findViewById<AppCompatTextView>(R.id.tvStoreLinkMessage)
        val tvStoreLink = layout.findViewById<AppCompatTextView>(R.id.tvStoreLink)

        backgroundImageView.loadImage(card.background_image as String, R.drawable.app_logo)

        tvTitle.setTextColor(Color.parseColor(card.title_text_color))
        tvDetail.setTextColor(Color.parseColor(card.detail_text_color))
        tvStoreLinkMessage.setTextColor(Color.parseColor(card.footer_text_color))
        tvStoreLink.setTextColor(Color.parseColor(card.store_link_text_color))

        tvStoreLink.text = bookName
        tvTitle.text = card.title_text
        tvDetail.text = card.detail_text
        tvStoreLinkMessage.text = card.footer_text

        tvStoreLink.getBackground().setColorFilter(PorterDuffColorFilter(Color.parseColor(card.store_link_tint_color), PorterDuff.Mode.SRC_ATOP))

        collection.addView(layout)
        return layout
    }

    override fun destroyItem(collection: ViewGroup, position: Int, view: Any) {
        collection.removeView(view as View)
    }

    override fun isViewFromObject(view: View, `object`: Any): Boolean {
        return view == `object`
    }

    override fun getCount(): Int {
        return socialMediaStoriesCards.size
    }

    override fun getItemPosition(item: Any): Int {
        return POSITION_NONE
    }


//    fun updateStoreLink(storeLink: String) {
//        this.storeLink = storeLink
//    }

    fun updateSocialMediaStoriesCards(socialMediaStoriesCards: List<MarketingStoryTemplate>) {
        this.socialMediaStoriesCards = socialMediaStoriesCards
        notifyDataSetChanged()
    }
}