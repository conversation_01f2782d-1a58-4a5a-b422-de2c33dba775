package com.bukuwarung.activities.marketing

import android.content.Context
import com.google.gson.FieldNamingPolicy
import com.google.gson.GsonBuilder
import com.google.gson.internal.bind.DateTypeAdapter
import java.io.IOException
import java.util.*

val gson by lazy {
    GsonBuilder()
            .setFieldNamingPolicy(FieldNamingPolicy.IDENTITY)
            .registerTypeAdapter(Date::class.java, DateTypeAdapter())
            .create()
}

class GsonHandler(private val context: Context) {

    fun <T> load(clazz: Class<T>, file: String): T {
        val fixtureStreamReader = getJsonDataFromAsset(context, file)
        return gson.fromJson(fixtureStreamReader, clazz)
    }

    fun <T> loadJson(clazz : Class<T>, json: String): T {
        return gson.fromJson(json, clazz)
    }

    private fun getJsonDataFromAsset(context: Context, fileName: String): String? {
        val jsonString: String
        try {
            jsonString =
                    context.assets.open(fileName).bufferedReader().use { reader -> reader.readText() }
        } catch (ioException: IOException) {
            ioException.printStackTrace()
            return null
        }
        return jsonString
    }
}