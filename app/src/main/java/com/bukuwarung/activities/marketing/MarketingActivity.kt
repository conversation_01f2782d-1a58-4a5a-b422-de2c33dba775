package com.bukuwarung.activities.marketing

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.text.SpannableStringBuilder
import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import com.bukuwarung.R
import com.bukuwarung.activities.marketing.MarketingType.*
import com.bukuwarung.activities.referral.share.ReferralUploadReceiver
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.databinding.ActivityMarketingBinding
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.boldText
import com.bukuwarung.utils.dp
import com.bukuwarung.utils.getScreenWidth
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import java.util.*
import javax.inject.Inject

class MarketingActivity : BaseActivity() {
    private lateinit var binding: ActivityMarketingBinding

    private var pagerAdapter: MarketingStatusPagerAdapter? = null
    private lateinit var marketingStoryTemplates: List<MarketingStoryTemplate>
    private var selectedIndex = 0
    private var selectedStoryCard: View? = null
    private var marketingType = STATUS

    @Inject
    lateinit var viewModel: MarketingViewModel

    override fun setViewBinding() {
        binding = ActivityMarketingBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)
        val type = intent.extras?.getString(MARKETING_TYPE) ?: intent.getStringExtra(MARKETING_TYPE) ?: STATUS.name
        marketingType = valueOf(type.uppercase(Locale.getDefault()))

        binding.toolbar.apply {
            title = when (marketingType) {
                STATUS -> {
                    getString(R.string.social_media_status)
                }
                POSTER -> {
                    getString(R.string.social_media_poster)
                }
            }

            setUpToolbarWithHomeUp(this)
        }


        binding.tvInfo.apply {
            text = when (marketingType) {
                STATUS -> {
                    SpannableStringBuilder(getString(R.string.social_media_status_msg)).boldText("750 x 1334px").boldText("rasio 16:9")
                }
                POSTER -> {
                    SpannableStringBuilder(getString(R.string.social_media_poster_msg)).boldText("800 x 800px").boldText("rasio 1:1")
                }
            }

            val drawable = when (marketingType) {
                STATUS -> {
                    R.drawable.ic_social_media_story
                }
                POSTER -> {
                    R.drawable.ic_social_media_post
                }
            }
            setCompoundDrawablesWithIntrinsicBounds(drawable, 0, 0, 0)
        }


        marketingStoryTemplates = emptyList()
        pagerAdapter = MarketingStatusPagerAdapter(this, marketingStoryTemplates, viewModel.getCurrentBook().bookName, marketingType)

        // text fields listener
        binding.title.addTextChangedListener {
            if (!this::marketingStoryTemplates.isInitialized || marketingStoryTemplates.isNullOrEmpty()) return@addTextChangedListener
            updateStoryCardView()
            selectedStoryCard?.findViewById<AppCompatTextView>(R.id.tvTitle)?.text = it.toString()
        }

        binding.discription.addTextChangedListener {
            val text = it.toString()
            if (!this::marketingStoryTemplates.isInitialized || marketingStoryTemplates.isNullOrEmpty()) return@addTextChangedListener
            if (text.count { c -> c == '\n' } >= 3) {
                binding.discription.setText(text.substring(0, text.length - 1))
            }
            updateStoryCardView()
            selectedStoryCard?.findViewById<AppCompatTextView>(R.id.tvDetail)?.text = text
        }

        binding.closing.addTextChangedListener {
            if (!this::marketingStoryTemplates.isInitialized || marketingStoryTemplates.isNullOrEmpty()) return@addTextChangedListener
            updateStoryCardView()
            selectedStoryCard?.findViewById<AppCompatTextView>(R.id.tvStoreLinkMessage)?.text = it.toString()
        }

        binding.cardPreviewViewpager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {}

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {}

            override fun onPageSelected(position: Int) {
                selectedIndex = position
                binding.title.setText(marketingStoryTemplates[position].title_text)
                binding.discription.setText(marketingStoryTemplates[position].detail_text)
                binding.closing.setText(marketingStoryTemplates[position].footer_text)
            }

        })

        binding.btnShare.setOnClickListener {
            if (!this::marketingStoryTemplates.isInitialized || marketingStoryTemplates.isNullOrEmpty()) return@setOnClickListener
            val currentIndex = binding.cardPreviewViewpager.currentItem
            updateStoryCardView()
            generateAndShareViewImage()
        }
    }

    private fun updateStoryCardView() {
        selectedStoryCard =
            binding.cardPreviewViewpager.findViewWithTag(marketingStoryTemplates[selectedIndex].story_id)
    }

    override fun subscribeState() {
        viewModel.init(marketingType)
        viewModel.socialMediaStories.observe(this, Observer { marketStories ->
            marketingStoryTemplates = marketStories

            val windowWidthPixels = getScreenWidth(this)
            val margin = (windowWidthPixels * 0.05).toInt()
            val padding = (windowWidthPixels * 0.15).toInt()

            binding.cardPreviewViewpager.apply {
                adapter = pagerAdapter
                clipToPadding = false
                setPadding(padding, 0, padding, 0)
                pageMargin = margin

                layoutParams.width = ViewPager.LayoutParams.MATCH_PARENT
                layoutParams.height = 420.dp
                requestLayout()
            }

            binding.dotsIndicator.setViewPager(binding.cardPreviewViewpager)

            pagerAdapter?.updateSocialMediaStoriesCards(marketStories)

            binding.title.setText(marketingStoryTemplates[selectedIndex].title_text)
            binding.discription.setText(marketingStoryTemplates[selectedIndex].detail_text)
            binding.closing.setText(marketingStoryTemplates[selectedIndex].footer_text)
        })
    }

    private fun generateAndShareViewImage() {
        try {
            val text = "Thanks Tokoko Team for making this easy to copy-pase!"
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> = ImageUtils.saveLayoutConvertedImage(selectedStoryCard, false)
            val receiverIntent = Intent(this, ReferralUploadReceiver::class.java)
            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
            } else {
                PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT)
            }

            val shareLayoutImage = ShareLayoutImage(text, this, "", "", false, "Bagikan Dengan", pendingIntent)
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    companion object {
        private const val MARKETING_TYPE = "marketing_type"
        fun createIntent(context: Context, marketType: MarketingType): Intent =
            Intent(context, MarketingActivity::class.java).apply {
                putExtra(MARKETING_TYPE, marketType.name)
            }
    }
}