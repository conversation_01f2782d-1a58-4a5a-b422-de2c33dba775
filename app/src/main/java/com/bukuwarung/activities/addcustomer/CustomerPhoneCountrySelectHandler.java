package com.bukuwarung.activities.addcustomer;

import com.bukuwarung.activities.addcustomer.detail.AddCustomerContactForm;
import com.bukuwarung.session.SessionManager;
import com.hbb20.CountryCodePicker.OnCountryChangeListener;

public final class CustomerPhoneCountrySelectHandler implements OnCountryChangeListener {
    final AddCustomerContactForm customerContactForm;
    SessionManager sessionManager = SessionManager.getInstance();
    public CustomerPhoneCountrySelectHandler(AddCustomerContactForm customerContactForm) {
        this.customerContactForm = customerContactForm;
    }

    public final void onCountrySelected() {
        AddCustomerContactForm customerContactForm = this.customerContactForm;
        customerContactForm.countryCode = AddCustomerContactForm.getCountrySelector(this.customerContactForm).getSelectedCountryCodeWithPlus();
        if (!sessionManager.isLoggedIn()) {
            sessionManager.setCountryCode(this.customerContactForm.countryCode);
        }
    }
}
