package com.bukuwarung.activities.addcustomer;


import com.bukuwarung.utils.RemoteConfigUtils;

public final class CustomerForm {

    private String address;
    private double balance;
    private String countryCode;
    private String id;
    public String reportId;
    public String mobile;
    public String name;
    private Boolean smsEnabled;

    public CustomerForm(String id, String name, String mobile, String countryCode, String addr, double bal, Boolean smsEnabled) {
        this.id = id;
        this.name = name;
        this.mobile = mobile;
        this.countryCode = countryCode;
        this.address = addr;
        this.balance = bal;
        this.smsEnabled = smsEnabled;
    }

    public final String getAddress() {
        return this.address;
    }

    public final String getCountryCode() {
        return this.countryCode;
    }

    public final String getId() {
        return this.id;
    }

    public final String getMobile() {
        return this.mobile;
    }

    public final String getName() {
        return this.name;
    }

    public final void setAddress(String address) {

        this.address = address;
    }

    public final void setId(String str) {
        this.id = str;
    }

    public final double getBalance() {
        return this.balance;
    }

    public final Boolean getSmsEnabled() {
        if(!RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm())
            return true;
        return this.smsEnabled == null ? Boolean.FALSE:this.smsEnabled;
    }

    public final void setSmsEnabled(boolean enabled) {
        this.smsEnabled = enabled;
    }
}
