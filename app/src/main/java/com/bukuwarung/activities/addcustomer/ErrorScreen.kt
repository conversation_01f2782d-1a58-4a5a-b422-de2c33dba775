package com.bukuwarung.activities.addcustomer

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.annotation.DrawableRes
import com.bukuwarung.databinding.ErrorScreenLayoutBinding
import com.bukuwarung.utils.asVisibility
import com.bumptech.glide.Glide

class ErrorScreen @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {
    private val binding = ErrorScreenLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    @DrawableRes
    private var imageRes: Int = 0
    private var title: String = ""
    private var message: String = ""

    private var buttonText : String = ""
    private var buttonAction: (() -> Unit)? = null

    private fun reloadView() {
        binding.apply {
            if (imageRes != 0) {
                Glide.with(context).load(imageRes).into(imgLogo)
            }

            tvTitle.apply {
                visibility = title.isNotEmpty().asVisibility()
                text = title
            }

            tvMessage.apply {
                visibility = message.isNotEmpty().asVisibility()
                text = message
            }

            btnAction.apply {
                visibility = buttonText.isNotEmpty().asVisibility()
                text = buttonText
                setOnClickListener { buttonAction?.invoke() }
            }
        }
    }

    fun setup(init: Builder.() -> Unit) = Builder(init)

    inner class Builder private constructor() {
        constructor(init: Builder.() -> Unit) : this() {
            init()
        }

        fun setTitle(init: Builder.() -> String) = apply {
            title = init()
            reloadView()
        }

        fun setMessage(init: Builder.() -> String) = apply {
            message = init()
            reloadView()
        }

        fun setImageRes(init: Builder.() -> Int) = apply {
            imageRes = init()
            reloadView()
        }

        fun setButtonTextAndAction(text: String, init: Builder.() -> (() -> Unit)) = apply {
            buttonText = text
            buttonAction = init()
            reloadView()
        }
    }

}