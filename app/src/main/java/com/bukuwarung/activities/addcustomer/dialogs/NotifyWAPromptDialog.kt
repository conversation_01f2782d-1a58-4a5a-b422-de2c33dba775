package com.bukuwarung.activities.addcustomer.dialogs

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BasePromptDialog

class NotifyWAPromptDialog(
        context: Context,
        onPromptClicked: ((Boolean) -> Unit)
): BasePromptDialog(context, onPromptClicked) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setTitle(context.resources.getString(R.string.check_box_wa_prompt_title))
        setContent(context.resources.getString(R.string.check_box_wa_prompt_content))
    }

}