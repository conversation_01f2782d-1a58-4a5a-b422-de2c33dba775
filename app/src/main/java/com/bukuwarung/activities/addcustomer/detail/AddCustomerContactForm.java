package com.bukuwarung.activities.addcustomer.detail;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.addcustomer.CustomerForm;
import com.bukuwarung.activities.superclasses.AppFragment;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.Utility;
import com.google.android.material.button.MaterialButton;
import com.hbb20.CountryCodePicker;

import java.util.List;


public final class AddCustomerContactForm extends AppFragment implements OnClickListener {

    public String countryCode;
    private TextView customerTitleTv;
    private TextView toolBarTitle;

    private CountryCodePicker countryPicker;
    public EditText customerName;
    public EditText mobile;

    public void onDestroyView() {
        super.onDestroyView();
    }

    public AddCustomerContactForm() {
        this.countryCode = sessionManager.getCountryCode();
    }

    public static final CountryCodePicker getCountrySelector(AddCustomerContactForm addCustomerDetailsFragment) {
        return addCustomerDetailsFragment.countryPicker;
    }

    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        return layoutInflater.inflate(R.layout.add_customer_contact_form, viewGroup, false);
    }

    public void onActivityCreated(Bundle bundle) {
        super.onActivityCreated(bundle);
        View view = getView();

        Toolbar toolbar = view.findViewById(R.id.toolbar);
        FragmentActivity activity = getActivity();

        if (activity != null) {
            ((AppCompatActivity) activity).setSupportActionBar(toolbar);
            this.customerName = view.findViewById(R.id.customerName);
            this.mobile = view.findViewById(R.id.mobile);
            this.countryPicker = view.findViewById(R.id.countryPicker);
            this.toolBarTitle = toolbar.findViewById(R.id.toolBarTitle);
            this.customerTitleTv = view.findViewById(R.id.customerTitle);

            MaterialButton btnNext = view.findViewById(R.id.next);
            ImageView closeBtn = toolbar.findViewById(R.id.close);
            btnNext.setOnClickListener(this);
            closeBtn.setOnClickListener(this);
            initFormFields(activity);
        }
    }

    private void initFormFields(FragmentActivity activity){
//        String name = ((AddCustomerActivity) activity).getName();
//        String customerMobile = ((AddCustomerActivity) activity).getMobile();
//        if (!Utility.isBlank(name)) {
//            this.customerName.setText(name);
//        }
//        if (!Utility.isBlank(customerMobile)) {
//            this.mobile.setText(Utility.cleanPhonenumber(customerMobile));
//        }
//        String countryCode = sessionManager.getCountryCode();
//        if (countryCode != null) {
//            this.countryPicker.setCountryForPhoneCode(Integer.parseInt(countryCode.substring(1)));
//            this.countryPicker.setOnCountryChangeListener(new CustomerPhoneCountrySelectHandler(this));
//            return;
//        }
    }

    public void onClick(View view) {

        int id = view.getId();
        if (id == R.id.close) {
            getActivity().finish();
        }  else if (id == R.id.next) {

            FragmentActivity activity = getActivity();
            String mobileStr = this.mobile.getText().toString();
            String customerNameStr = this.customerName.getText().toString();

            if (Utility.isBlank(customerNameStr)) {
                Context context = getContext();
                alertFormErr(context.getString(R.string.customer_name_hint));
                return;
            }

            if (!Utility.isBlank(mobileStr)) {
                List<CustomerEntity> customersWithPhone = CustomerRepository.getInstance(getContext()).getCustomerByNumber(User.getBusinessId(), this.countryCode, Utility.cleanPhonenumber(this.mobile.getText().toString()));
                if (customersWithPhone != null && customersWithPhone.size() > 0) {
                    if (getContext() != null) {
                        alertDuplicateNumber((AddCustomerActivity) getContext(), customersWithPhone);
                        return;
                    }
                }
            }

            if (activity != null) {

                AddCustomerActivity addCustomerActivity = (AddCustomerActivity) activity;
                String customerNm = this.customerName.getText().toString();
                String purifyMobileNumber = Utility.cleanPhonenumber(this.mobile.getText().toString());

                CustomerForm customerForm = new CustomerForm(null, customerNm, purifyMobileNumber, this.countryCode, "", 0.0d, null);
//                addCustomerActivity.setCustomer(customerForm);

                FragmentTransaction beginTransaction = activity.getSupportFragmentManager().beginTransaction();
//                beginTransaction.replace(R.id.frameLayout, new CustomerFirstTransactionForm());
                beginTransaction.addToBackStack(null);
                beginTransaction.commitAllowingStateLoss();
            }
        }
    }

    private final void alertDuplicateNumber(Activity activity, List<? extends CustomerEntity> list) {
        String message = Utility.getNumberWithCC(this.countryCode,this.mobile.getText().toString())+" already exists";
        NotificationUtils.alertToastWithContext(getContext(),message);
    }

    public static void alertFormErr(String err) {
        Toast makeText = Toast.makeText(Application.getAppContext(), err, Toast.LENGTH_SHORT);
        makeText.setGravity(48, 0, 100);
        makeText.show();
    }
}
