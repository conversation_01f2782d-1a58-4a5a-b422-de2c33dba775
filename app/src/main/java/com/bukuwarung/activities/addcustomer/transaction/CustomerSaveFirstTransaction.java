package com.bukuwarung.activities.addcustomer.transaction;

import com.bukuwarung.Application;
import com.bukuwarung.activities.addcustomer.CustomerForm;
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;

import java.util.Date;

public final class CustomerSaveFirstTransaction implements Runnable {
    final AddCustomerActivity activity;
    final double amount;
    final String note;
    final int smsStatus;
    final String storableDate;

    public CustomerSaveFirstTransaction(AddCustomerActivity addCustomerActivity, double bal, String note,int smsStatus, String storableDate) {
        this.activity = addCustomerActivity;
        this.amount = bal;
        this.note = note;
        this.smsStatus = smsStatus;
        this.storableDate = storableDate;
    }

    public final void run() {
        String storableDateString = storableDate == null ? Utility.getStorableDateString(new Date()) : storableDate;
        TransactionRepository transactionRepository = TransactionRepository.getInstance(Application.getAppContext());
        String businessId = User.getBusinessId();
        CustomerForm customer = this.activity.getCustomer();
        transactionRepository.saveNewTransaction(businessId, customer.getId(), this.amount, storableDateString, this.note,this.smsStatus);
    }
}
