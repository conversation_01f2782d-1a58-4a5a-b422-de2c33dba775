package com.bukuwarung.activities.addcustomer.detail;

import static com.bukuwarung.constants.AnalyticsConst.NOTA_STANDARD_ENABLED;
import static com.bukuwarung.constants.AnalyticsConst.SMS_CHECKBOX_ENABLED;
import static com.bukuwarung.preference.OnboardingPrefManager.TUTOR_SAVE_UTANG_PIUTANG;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.widget.ImageViewCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.addcustomer.CustomerForm;
import com.bukuwarung.activities.addcustomer.transaction.CustomerSaveFirstTransaction;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.phonebook.PhonebookSearchDialog;
import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DefaultAnim;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.collectingcalendar.main.CollectingCalendarActivity;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.EoyEntry;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.FirebaseRepository;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.ReferralRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.dialogs.login.LoginBottomSheetDialog;
import com.bukuwarung.dialogs.login.VerifyOtpBottomSheetDialog;
import com.bukuwarung.keyboard.CustomKeyboardView;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.OnboardingPrefManager;
import com.bukuwarung.session.AuthHelper;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.shape.FocusGravity;
import com.bukuwarung.tutor.shape.ShapeType;
import com.bukuwarung.tutor.view.OnboardingWidget;
import com.bukuwarung.utils.AnimationExtensionKt;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.ShareUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;
import com.google.android.material.button.MaterialButton;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Calendar;
import java.util.Date;

public final class AddCustomerActivity extends AppActivity implements LoginBottomSheetDialog.LoginSheetListener, VerifyOtpBottomSheetDialog.OtpSheetListener, OnboardingWidget.OnboardingWidgetListener {

    private static final String TUTORIAL_EXPENSE = "TUTORIAL_EXPENSE";
    private static final String TUTORIAL_INCOME = "TUTORIAL_INCOME";
    private static final String TUTORIAL_ADD_CUSTOMER = "TUTORIAL_ADD_CUSTOMER";
    private LoginBottomSheetDialog loginBtSheet;
    private VerifyOtpBottomSheetDialog otpBtSheet;

    public String transactionDate;
    private AppCompatRadioButton creditButton;
    private AppCompatRadioButton debitButton;

    public TextView mTransactionInputAmount;
    public TextView mTransactionInputAmountRes;
    public TextView mTransactionTitle;
    private LinearLayout inputAmountLayout;
    private ImageView inputAmountImage;
    public PhonebookSearchDialog dialog;

    private CustomKeyboardView keyboardView;
    private View cursor;

    private LinearLayout additionalDataLayout;

    private LinearLayout income_layout;
    private LinearLayout expense_layout;

    private ImageView expenseIcon;
    private ImageView incomeIcon;

    private TextView expenseText;
    private TextView incomeText;

    private ImageView phoneIcon;
    private ImageView chevronIcon;
    public TextView phoneText;
    public TextView customerNameText;
    private TextView currencySymbol;
    public EditText dateEditText;
    private EditText note;

    private CheckBox checkboxSendSms;

    private MaterialButton saveBtn;

    private String previewAmount = "0";
    private View clSmsPreview;
    private TextView tvSmsPreview;
    private int transactionType = -1; /*debit = -1. credit = 1*/

    private String newReportId = "";
    private boolean hasShownSuccessTutorial = false;
    private boolean showTutorialExtra = false;
    private OnboardingWidget onboardingWidget;

    public AddCustomerActivity() {
        super(new DefaultAnim());
    }

    public final void setCustomer(CustomerForm customerUi) {
        this.customer = customerUi;
    }

    private CustomerForm customer;
    private String predefinedAmount;
    private long lastButtonSaveClicked = 0;
    private String currentEntryPoint = "";
    private LinearLayout layoutNewCustomer;

    private View successView;
    private LottieAnimationView lavSuccessView;

    @Override
    public void onOnboardingDismiss(@Nullable String id, @NotNull String body, boolean isFromButton, boolean isFromCloseButton, boolean isFromOutside) {
        // possible that user go to step 2 of onboarding directly without going from step 1
        OnboardingPrefManager.Companion.getInstance().setHasFinishedForId(OnboardingPrefManager.TOUR_CUSTOMER_TAB_ADD_BTN);
    }

    @Override
    public void onOnboardingButtonClicked(@Nullable String id, boolean isFromHighlight) {
        if (id == null) return;
        switch (id) {
            case TUTORIAL_EXPENSE: {
                onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                        TUTORIAL_INCOME, income_layout, R.drawable.onboarding_attention, "",
                        getString(R.string.onboarding_debt_income), getString(R.string.next), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                        3, 4, true, false);
                break;
            }
            case TUTORIAL_INCOME: {
                onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                        TUTORIAL_ADD_CUSTOMER, layoutNewCustomer, R.drawable.onboarding_attention, "",
                        getString(R.string.onboarding_debt_add_customer), getString(R.string.understand), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                        4, 4, true, false);
                break;
            }
            case TUTORIAL_ADD_CUSTOMER: {
                onNewCustomerClicked();
                break;
            }
        }
    }

    public static final class SaveCustomerHandler {

        public static final class SaveCustomerAsyncTask extends AsyncTask<CustomerForm, Void, String> {

            private final AddCustomerActivity activity;
            private final double amount;
            private final String note;
            private final String storableDate;
            private final String reportId;

            public SaveCustomerAsyncTask(AddCustomerActivity addCustomerActivity, boolean z, double amount, String note, String storableDate, String reportId) {
                this.activity = addCustomerActivity;
                this.amount = amount;
                this.note = note;
                this.storableDate = storableDate;
                this.reportId = reportId;
            }

            public String doInBackground(CustomerForm... customerUiArr) {
                CustomerForm customerUi = customerUiArr[0];
                customerUi.reportId = reportId;
                String customerId = CustomerRepository.getInstance(Application.getAppContext())
                        .saveCustomer(User.getBusinessId(), customerUi.getName(), customerUi.getAddress(),
                                customerUi.getMobile(), customerUi.getCountryCode(), Double.valueOf(customerUi.getBalance()),
                                reportId, customerUi.getSmsEnabled(), null);

                try {

                    String receiverPhone = customerUi.getCountryCode() + "" + customerUi.getMobile();
                    boolean sendSms = customerUi != null && activity.checkboxSendSms.isChecked() &&
                            !Utility.isBlank(customerUi.mobile) && customerUi.mobile.length() > 6;
                    int type = Utility.getTransactionTypeInt(this.amount);
                    if (sendSms) {
                        Utility.sendSms(receiverPhone, this.amount, type,
                                activity, customerUi.reportId,
                                -1);

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return customerId;
            }

            public void onPostExecute(String cstId) {
                CustomerForm customer = this.activity.getCustomer();
                customer.setId(cstId);
                int smsStatus = customer.getSmsEnabled() ? 1 : 0;
                AddCustomerActivity.SaveCustomerHandler.saveTransaction(
                        this.activity, this.amount, this.note, smsStatus, this.storableDate, cstId);
            }
        }


        public static void saveTransaction(AddCustomerActivity addCustomerActivity,
                                           double bal, String notes, int smsStatus, String storableDate, String cstId) {
            CustomerSaveFirstTransaction customerSaveFirstTransaction =
                    new CustomerSaveFirstTransaction(addCustomerActivity, bal, notes, smsStatus, storableDate);
            new Thread(customerSaveFirstTransaction).start();
            addCustomerActivity.openCustomerTransaction(cstId);
        }
    }

    public final String getPredefinedAmount() {
        return this.predefinedAmount;
    }

    private void onNewCustomerClicked() {
        hideCalculator();
        if (dialog != null) {
            dialog.show();
        }
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        if (!RemoteConfigUtils.TransactionTabConfig.INSTANCE.canShowOldTransactionForm()) {
            finish();
            Intent intent = new Intent(this, CustomerActivity.class);
            if (getIntent().hasExtra("pn_target_form")) {
                intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, getIntent().getExtras().getString("pn_target_form").equals("CREDIT") ? AppConst.CREDIT : AppConst.DEBIT);
            } else {
                intent.putExtra(CustomerActivity.TRANSACTION_TYPE_TAG, AppConst.DEBIT);
            }
            startActivity(intent);
            return;
        }
        FeaturePrefManager.getInstance().exitWithoutTransaction(AppConst.CUSTOMER_TRANSACTION);
        setContentView((int) R.layout.customer_first_transaction_form);
        newReportId = AppIdGenerator.getReportId();
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        ActionBar supportActionBar = getSupportActionBar();
        supportActionBar.setDisplayHomeAsUpEnabled(false);
        this.saveBtn = findViewById(R.id.save);

        this.additionalDataLayout = findViewById(R.id.additional_data_layout);

        this.mTransactionInputAmount = findViewById(R.id.transaction_input_amount);
        this.mTransactionInputAmountRes = (TextView) findViewById(R.id.transaction_input_amount_result);
        this.mTransactionTitle = (TextView) findViewById(R.id.txt_main_title);

        this.expense_layout = findViewById(R.id.expense);
        this.income_layout = findViewById(R.id.income);

        this.expenseIcon = findViewById(R.id.icon_expense);
        this.incomeIcon = findViewById(R.id.icon_income);

        this.expenseText = findViewById(R.id.text_expense);
        this.incomeText = findViewById(R.id.text_income);

        this.inputAmountLayout = findViewById(R.id.transaction_input_amount_layout);
        inputAmountLayout.setBackgroundResource(R.drawable.credit_amount_active); // we'd always focus on amount layout first

        this.inputAmountImage = findViewById(R.id.amountImage);

        this.phoneIcon = findViewById(R.id.phone_icon);
        this.chevronIcon = findViewById(R.id.chevron_contact);
        this.phoneText = findViewById(R.id.phone);
        this.customerNameText = findViewById(R.id.name);
        this.note = findViewById(R.id.note);
        this.checkboxSendSms = findViewById(R.id.sendCustomerSms);
        this.clSmsPreview = findViewById(R.id.clSMSPreview);
        this.tvSmsPreview = findViewById(R.id.tvSmsPreview);
        this.successView = findViewById(R.id.success_view);
        this.lavSuccessView = findViewById(R.id.lav_success);

        setSmsPreviewVisibility(this.checkboxSendSms.isChecked());
        ComponentUtil.setVisible(checkboxSendSms, !SessionManager.getInstance().isGuestUser());
        this.checkboxSendSms.setOnCheckedChangeListener((compoundButton, b) -> {
            setSmsPreviewVisibility(this.checkboxSendSms.isChecked());
            getCustomer().setSmsEnabled(this.checkboxSendSms.isChecked());
        });

        note.clearFocus();

        note.setOnClickListener(view -> hideCalculator());

        note.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                if (b) {
                    hideCalculator();
                }
            }
        });

        this.dateEditText = findViewById(R.id.calender_picker);
        this.debitButton = findViewById(R.id.debit_button);
        this.creditButton = findViewById(R.id.credit_button);

        dialog = new PhonebookSearchDialog(this, this);
        dialog.setOnCustomerSelectedCallback(this::onNewCustomer);

        keyboardView = findViewById(R.id.keyboardView);
        findViewById(R.id.keyboardView).setVisibility(View.VISIBLE);
        keyboardView.setResultTv(findViewById(R.id.transaction_input_amount_result));
        keyboardView.setExprTv(findViewById(R.id.transaction_input_amount));
        keyboardView.setResultLayout(findViewById(R.id.exprLayour));
        cursor = findViewById(R.id.cursor);
        keyboardView.setCursor(cursor);
        currencySymbol = findViewById(R.id.currency_symbol);
        currencySymbol.setText(Utility.getCurrency());
        keyboardView.setCurrency(currencySymbol);
        keyboardView.setOnSubmitListener(() -> {
            inputAmountLayout.setBackgroundResource(R.drawable.credit_amount_inactive);
            if (showTutorialExtra && !hasShownSuccessTutorial) {
                hasShownSuccessTutorial = true;
                onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                        TUTOR_SAVE_UTANG_PIUTANG, saveBtn, R.drawable.onboarding_great, getString(R.string.done_exclmark),
                        getString(R.string.onboarding_save_debt_subtitle), "",
                        FocusGravity.CENTER, ShapeType.RECTANGLE_FULL, 1, 1, false, false);
            }
        });

        layoutNewCustomer = findViewById(R.id.layoutNewCustomer);
        layoutNewCustomer.setOnClickListener(v -> onNewCustomerClicked());

        String storableDateString = Utility.getStorableDateString(new Date());
        this.transactionDate = storableDateString;
        this.dateEditText.setText(Utility.getReadableDateString(transactionDate));

        keyboardView.showCursor();

        CustomerForm customer = this.getCustomer();
        if (customer != null) {
            if (!Utility.isBlank(customer.getName())) {
                String cstName = customer.getName();
                String cstPhone = customer.getMobile();
                if (cstPhone != null && !cstPhone.isEmpty())
                    phoneText.setText(Utility.beautifyPhoneNumber(cstPhone));
                else
                    phoneText.setText(getResources().getString(R.string.default_placeholder));

                customerNameText.setText(cstName);
                phoneText.setTextColor(getResources().getColor(R.color.black));
                customerNameText.setTextColor(getResources().getColor(R.color.black));
                chevronIcon.setVisibility(View.GONE);
                setNameInitials(cstName);
                this.checkboxSendSms.setText(getResources().getString(R.string.send_customer_sms, cstName));
                additionalDataLayout.setVisibility(View.VISIBLE);
                if (getCustomer() != null && !Utility.isBlank(getCustomer().getName())) {
                    saveBtn.setEnabled(true);
                }
            }
        }

        mTransactionInputAmountRes.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                try {
                    previewAmount = charSequence.toString();
                    setSmsPreviewText();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {
            }
        });

        this.getIntent().getLongExtra("customer_id", 0);
        saveBtn.setOnClickListener(v -> {
            if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600) {
                return;
            }
            lastButtonSaveClicked = SystemClock.elapsedRealtime();
            if (getCustomer() != null && Utility.isBlank(getCustomer().getName())) {
                NotificationUtils.alertToast("Anda belum menambahkan pelanggan");
                return;
            }
            if (!AuthHelper.isValidSessionOperation()) {
                callLoginBottomsheet(false, AnalyticsConst.UTANG);
                return;
            }

            saveCustomerTransaction();
            setResult(Activity.RESULT_OK);
        });

        this.debitButton.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                updateFormType(true);
            }
        });
        this.creditButton.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                updateFormType(false);
            }
        });
        expense_layout.setOnClickListener(view12 -> {
            if (creditButton.isChecked()) {
                debitButton.setChecked(true);
                creditButton.setChecked(false);
            }
        });

        income_layout.setOnClickListener(view13 -> {
            if (debitButton.isChecked()) {
                debitButton.setChecked(false);
                creditButton.setChecked(true);
            }
        });

        this.dateEditText.setOnClickListener(v -> setDateToString());
        final Activity activity = this;
        findViewById(R.id.close).setOnClickListener(view -> {
            InputUtils.hideKeyBoardWithCheck(activity);
            MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
            finish();
        });
        this.inputAmountLayout.setOnClickListener(view1 -> toggleKeyboard());

        // new flow for add new utang

        updateFormType(true);
        String predefinedAmount = (this).getPredefinedAmount();
        if (predefinedAmount != null && !predefinedAmount.equals("0.0")) {
            setViewTransactionValues(predefinedAmount);
            keyboardView.setResultAmount(predefinedAmount);
            mTransactionInputAmount.setText(predefinedAmount);
            keyboardView.updateResult(predefinedAmount);
        }

        mTransactionTitle.setText("Memberikan");

        showTutorialExtra = getIntent().getBooleanExtra("ShowCustomerTutorial", false);
        if (showTutorialExtra) {
            showTransactionTypeTutorial();
        }
    }

    private void hideCalculator() {
        if (keyboardView.getVisibility() == View.VISIBLE) {
            keyboardView.setVisibility(View.GONE);
            inputAmountLayout.setBackgroundResource(
                    R.drawable.credit_amount_inactive);
            keyboardView.hideCursor();
        }
    }

    @Override
    public void onBackPressed() {
        InputUtils.hideKeyboard(this);
        if (onboardingWidget != null && onboardingWidget.isShown()) {
            onboardingWidget.dismiss(false, false, true);
        } else if (keyboardView.getVisibility() == View.VISIBLE) {
            keyboardView.setVisibility(View.GONE);
        } else {
            MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
            finish();
        }
    }

    private void setNameInitials(String name) {
        try {
            if (name == null || name.isEmpty()) return;
            LinearLayout phoneLayout = findViewById(R.id.phoneLayout);
            if (phoneLayout == null) return;
            phoneLayout.setVisibility(View.VISIBLE);
            phoneIcon.setVisibility(View.GONE);
            TextView firstLetterTv = findViewById(R.id.firstLetter);

            String firstLetterText = Utility.getNameInitials(name);
            firstLetterTv.setText(firstLetterText);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String oldCustomerId = "";

    private void onNewCustomer(Contact contact) {
        final String cstName = contact.getName();
        final String cstMobile = contact.getMobile();
        oldCustomerId = contact.getCustomerId();
        if (cstName == null || cstMobile == null) return;
        String cstPhone = contact.getMobile();
        if (cstPhone != null && !cstPhone.isEmpty())
            phoneText.setText(Utility.beautifyPhoneNumber(cstMobile));
        else
            phoneText.setText(getResources().getString(R.string.default_placeholder));

        customerNameText.setText(cstName);
        phoneText.setTextColor(getResources().getColor(R.color.black));
        customerNameText.setTextColor(getResources().getColor(R.color.black));
        chevronIcon.setVisibility(View.GONE);
        setNameInitials(cstName);
        this.checkboxSendSms.setText(getResources().getString(R.string.send_customer_sms, cstName));
        additionalDataLayout.setVisibility(View.VISIBLE);

        final String amt = mTransactionInputAmountRes.getText().toString();

        getCustomer().mobile = !Utility.isBlank(contact.getMobile())
                ? Utility.cleanPhonenumber(contact.getMobile()) : "";
        getCustomer().name = contact.getName();

        if (getCustomer() != null && !Utility.isBlank(getCustomer().getName())) {
            saveBtn.setEnabled(true);
        }

        if (!Utility.isBlank(oldCustomerId)) {
            CustomerEntity customerEntity = CustomerRepository.getInstance(this).getCustomerById(oldCustomerId);
            newReportId = customerEntity.altCustomerId;
            setSmsPreviewText();
        }
    }

    public CustomerForm getCustomer() {
        if (this.customer == null)
            this.customer = new CustomerForm("", "", "", SessionManager.getInstance().getCountryCode().substring(1), "", 0,
                    checkboxSendSms == null ? false : checkboxSendSms.isChecked());
        return this.customer;
    }

    public final void setViewTransactionValues(String amountText) {
        try {
            final double amount = Double.parseDouble(amountText);
            this.mTransactionInputAmountRes.setText(Utility.formatCurrencyForEditing(amount));
            this.mTransactionInputAmount.setText(Utility.formatCurrencyForEditing(amount));
        } catch (Exception ex) {
            Log.e("CustomerFirstTx", "Error parsing", ex);
        }
    }

    public final void requestContactPermissions() {
        if (!PermissonUtil.hasContactPermission()) {
            AppAnalytics.trackEvent("request_contact_permission");
            ActivityCompat.requestPermissions(this, PermissionConst.READ_WRITE_CONTACTS, PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION);
        }
    }

    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        if (i == PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION && Build.VERSION.SDK_INT >= 23) {
            if (PermissonUtil.hasContactPermission() && dialog != null && dialog.rowHolderListViewModel != null) {
                dialog.rowHolderListViewModel.loadContactsInList();
                dialog.adapter.notifyDataSetChanged();
                dialog.permissionExplainLayout.setVisibility(View.GONE);
                dialog.recyclerView.setVisibility(View.VISIBLE);
                AppAnalytics.trackEvent("granted_contact_permission");
                return;
            }
            Toast.makeText(this, this.getString(R.string.alert_deny_contact_permission), Toast.LENGTH_LONG).show();
        }
    }

    public final void saveCustomerTransaction() {
        final Activity activity = this;
        String balanceStr = keyboardView.getInputAmount();
        boolean isNewTransaction = false;

        double balAmount = !Utility.isBlank(balanceStr) ? Double.parseDouble(balanceStr) : 0.0d;
        if (this.debitButton.isChecked()) {
            balAmount = 0 - balAmount;
        }

        double balance = balAmount;

        if (!SessionManager.getInstance().hasCreatedUtangRecord()) {
            SessionManager.getInstance().setHasCreatedUtangRecord(true);
        }
        Utility.trackTransactionCount();
        if (activity != null && Utility.isBlank(oldCustomerId)) {
            isNewTransaction = true;
            AddCustomerActivity addCustomerActivity = this;
            if (addCustomerActivity.getCustomer() != null) {
                AddCustomerActivity.SaveCustomerHandler
                        .SaveCustomerAsyncTask saveCustomerAsyncTask = new AddCustomerActivity.SaveCustomerHandler.
                        SaveCustomerAsyncTask(addCustomerActivity, true, balance,
                        this.note.getText().toString(), this.transactionDate, this.newReportId);

                saveCustomerAsyncTask.execute(new CustomerForm[]{this.getCustomer()});

                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put("source", "phone_book");
                propBuilder.put("transaction_type", Utility.getTransactionTypeStr(balAmount));
                propBuilder.put("transaction_amount", balanceStr);
                propBuilder.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsUtang());
                propBuilder.put(NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewUtangInvoice());
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CREATED_NEW_CUSTOMER, propBuilder);
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CREATED_NEW_CUSTOMER, this);

                // disabled by product team request
                // Utility.showSmsSentToast(balance < 0 ? -1 : 1, this, sendSms);
            }
        } else {
            String savedTxnId = TransactionRepository.getInstance(Application.getAppContext()).saveNewTransaction(User.getBusinessId(), oldCustomerId, balAmount, transactionDate, note.getText().toString(), 1);
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("transaction_type", InputUtils.getTransactionTypeStr(balAmount));
            propBuilder.put("transaction_amount", String.valueOf(balAmount));
            propBuilder.put("cst_trans_seq", SessionManager.getInstance().getTransSeq());
            propBuilder.put("cst_seq", SessionManager.getInstance().getCstSeq());
            propBuilder.put("form", "old_utang_form");
            propBuilder.put("transaction_id", savedTxnId);
            propBuilder.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsUtang());
            AppAnalytics.trackEvent("customer_detail_tap_saved", propBuilder);
            String receiverPhone = this.getCustomer().getCountryCode() + "" + this.getCustomer().getMobile();
            try {
                CustomerEntity customerEntity = CustomerRepository.getInstance(this).getCustomerById(oldCustomerId);
                double finalBalance = customerEntity.balance;
                String reportId = customerEntity.altCustomerId;
                boolean sendMessage = customerEntity != null && checkboxSendSms.isChecked() && !Utility.isBlank(customerEntity.phone) && customerEntity.phone.length() > 6;
                if (sendMessage) {
                    Utility.sendSms(receiverPhone, balance, 0, this, reportId, -1, finalBalance);
                }
                // disabled by product team request
                // Utility.showSmsSentToast(balance < 0 ? -1 : 1, this, sendMessage);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        if (AppConfigManager.getInstance().useReferral()
                && getCurrentBook() != null && Math.abs(balance) > AppConfigManager.getInstance().getReferralTxnVal()) {
            ReferralRepository.getInstance().addTransactionPoints(getCurrentBook(), false);
        }

        if (!isNewTransaction) {
            final boolean fromCollecting = getIntent().getBooleanExtra(PARAM_FROM_COLLECTING, false);
            Intent intentCollecting = new Intent(this, CollectingCalendarActivity.class);
            intentCollecting.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);

            // lottie animation
            if (RemoteConfigUtils.INSTANCE.shouldShowUtangSuccessAnimation()) {
                ((TextView) findViewById(R.id.tv_trx_success)).setText(RemoteConfigUtils.INSTANCE.getTrxSuccessMessage());
                AnimationExtensionKt.showForOnce(lavSuccessView, successView, 75, () -> {
                    if (!fromCollecting) {
                        MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
                        finish();
                    } else {
                        startActivity(intentCollecting);
                        if (!Utility.isBlank(oldCustomerId)) finish();
                    }
                    return null;
                });
            } else {
                if (!fromCollecting) {
                    MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
                    finish();
                } else {
                    startActivity(intentCollecting);
                    if (!Utility.isBlank(oldCustomerId)) finish();
                }
            }
        }
        InputUtils.hideKeyBoardWithCheck(this);
    }

    private void openCustomerTransaction(String cstId) {
        Intent intent = new Intent(this, CustomerTransactionActivity.class);
        intent.putExtra("customerId", cstId);
        intent.putExtra("firstTrx", true);
        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);

        if (RemoteConfigUtils.INSTANCE.shouldShowUtangSuccessAnimation()) {
            ((TextView) findViewById(R.id.tv_trx_success)).setText(RemoteConfigUtils.INSTANCE.getTrxSuccessMessage());
            AnimationExtensionKt.showForOnce(lavSuccessView, successView, 75, () -> {
                startActivity(intent);
                finish();
                return null;
            });
        } else {
            startActivity(intent);
            finish();
        }
    }

    private void toggleKeyboard() {
        InputUtils.hideKeyBoardWithCheck(this);

        if (keyboardView.getVisibility() == View.GONE) {
            Animation moveup = AnimationUtils.loadAnimation(this,
                    R.anim.move_up);
            keyboardView.startAnimation(moveup);
            keyboardView.setVisibility(View.VISIBLE);
            keyboardView.showCursor();
            note.clearFocus();
            inputAmountLayout.setBackgroundResource(R.drawable.credit_amount_active);
        }
    }

    private void controlSelect(LinearLayout target,
                               ImageView icon, TextView filterTxt, boolean isSelected, boolean isExpense) {

        Drawable selected = isExpense ? this.getResources().getDrawable(R.drawable.type_exp_selected_bg) : this.getResources().getDrawable(R.drawable.type_inc_selected_bg);
        Drawable unselected = this.getResources().getDrawable(R.drawable.type_unselected_bg);

        int selectedColor = this.getResources().getColor(R.color.white);
        int disabledColor = this.getResources().getColor(R.color.greyDisabled);

        target.setBackground(isSelected ? selected : unselected);
        ImageViewCompat.setImageTintList(icon, ColorStateList.valueOf(isSelected ? selectedColor : disabledColor));
        filterTxt.setTextColor(isSelected ? selectedColor : disabledColor);
        filterTxt.setTypeface(isSelected ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);

    }


    public final void updateFormType(boolean isDebit) {
        this.debitButton.setChecked(isDebit);
        this.creditButton.setChecked(!isDebit);

        if (isDebit) {
            mTransactionTitle.setText("Memberikan");
            inputAmountImage.setImageResource(R.drawable.ic_amount_new);
        } else {
            mTransactionTitle.setText("Menerima");
            inputAmountImage.setImageResource(R.drawable.ic_amount_income);
        }
        if (isDebit) {
            controlSelect(expense_layout, expenseIcon, expenseText, true, true);
            controlSelect(income_layout, incomeIcon, incomeText, false, false);
            mTransactionInputAmountRes.setTextColor(getResources().getColor(
                    R.color.out_red
            ));
            mTransactionInputAmountRes.setHintTextColor(getResources().getColor(
                    R.color.out_red
            ));
            currencySymbol.setTextColor(getResources().getColor(
                    R.color.out_red
            ));
            transactionType = -1;
            setSmsPreviewText();
            return;
        }
        controlSelect(expense_layout, expenseIcon, expenseText, false, true);
        controlSelect(income_layout, incomeIcon, incomeText, true, false);
        mTransactionInputAmountRes.setTextColor(getResources().getColor(
                R.color.in_green
        ));
        mTransactionInputAmountRes.setHintTextColor(getResources().getColor(
                R.color.in_green
        ));
        currencySymbol.setTextColor(getResources().getColor(
                R.color.in_green
        ));
        transactionType = 1;
        setSmsPreviewText();
    }

    public final void setDateToString() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int date = calendar.get(Calendar.DATE);
        DatePickerDialog datePickerDialog = new DatePickerDialog
                (this,
                        (datePicker, year1, month1, day) -> {
                            Calendar instance = Calendar.getInstance();
                            instance.set(year1, month1, day);
                            String storableDateString = Utility.getStorableDateString(instance.getTime());
                            transactionDate = storableDateString;
                            dateEditText.setText(
                                    Utility.getReadableDateString(transactionDate)
                            );

                        }, year, month, date);
        datePickerDialog.setTitle(R.string.date);
        datePickerDialog.show();

        Animation moveDown = AnimationUtils.loadAnimation(AddCustomerActivity.this,
                R.anim.move_down);
        keyboardView.startAnimation(moveDown);
        keyboardView.setVisibility(View.GONE);
        inputAmountLayout.setBackgroundResource(R.drawable.credit_amount_inactive);
        keyboardView.hideCursor();
    }

    private void setSmsPreviewVisibility(boolean isVisible) {
        if (isVisible) {
            clSmsPreview.setVisibility(View.VISIBLE);
        } else {
            clSmsPreview.setVisibility(View.GONE);
        }
    }

    private void setSmsPreviewText() {
        if (previewAmount.isEmpty()) {
            previewAmount = "0";
        }
        String text = ShareUtils.getSmsPreviewText(previewAmount, getReportUrl(), this.transactionType);
        tvSmsPreview.setText(text);
    }

    private String getReportUrl() {
        StringBuilder sb = new StringBuilder();
        if (!Utility.isBlank(newReportId)) {
            String format = String.format(Utility.getReportUrl(), newReportId);
            sb.append(format);
        } else {
            return "https://bukuwarung.com/app";
        }
        return sb.toString();
    }

    public void showTransactionTypeTutorial() {
        onboardingWidget = OnboardingWidget.Companion.createInstance(this, this,
                TUTORIAL_EXPENSE, expense_layout, R.drawable.onboarding_attention, "",
                getString(R.string.onboarding_debt_expense), getString(R.string.next), FocusGravity.CENTER, ShapeType.RECTANGLE_FULL,
                2, 4, true, false);
    }

    public static String PARAM_FROM_COLLECTING = "collectingcalendar";

    public static Intent getNewIntentFromCollecting(Activity origin) {
        Intent intent = new Intent(origin, AddCustomerActivity.class);
        intent.putExtra(PARAM_FROM_COLLECTING, true);
        return intent;
    }

    @Override
    public void goToVerifyOtp(@NotNull String phone, @NotNull String countryCode, @NotNull String method) {
        otpBtSheet = VerifyOtpBottomSheetDialog.Companion.newInstance(phone, countryCode, currentEntryPoint, this);
        otpBtSheet.show(getSupportFragmentManager(), VerifyOtpBottomSheetDialog.TAG);
    }

    @Override
    public void callLoginBottomsheet(boolean openKeyboard, @NotNull String entryPoint) {
        currentEntryPoint = entryPoint;
        showLogin(openKeyboard);
    }

    private void showLogin(boolean openKeyboard) {
        loginBtSheet = LoginBottomSheetDialog.Companion.newInstance(currentEntryPoint, this);
        loginBtSheet.show(getSupportFragmentManager(), LoginBottomSheetDialog.TAG);
        loginBtSheet.keyboardState(openKeyboard);
    }

    @Override
    public void onFinishOtp() {
        BusinessRepository.getInstance(this).mergeGuestRecords();
        CashRepository.getInstance(this).mergeGuestRecords();
        ProductRepository.getInstance(this).mergeGuestRecords();
        SessionManager.getInstance().isGuestUser(false);
        Thread mThread = new Thread() {
            @Override
            public void run() {
                TransactionUtil.backupAllTransactions();
                MainActivity.startActivityAndClearTop(AddCustomerActivity.this);
            }
        };
        mThread.start();
    }

}