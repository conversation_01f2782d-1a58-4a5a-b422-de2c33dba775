package com.bukuwarung.activities.addcustomer.transaction;

import com.bukuwarung.Application;
import com.bukuwarung.activities.addcustomer.CustomerForm;
import com.bukuwarung.activities.addcustomer.detail.AddCustomerActivity;
import com.bukuwarung.activities.addcustomer.detail.CustomerActivity;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utility;

import java.util.Date;

import okhttp3.internal.Util;

public final class CustomerSaveTransaction implements Runnable {
    final CustomerActivity activity;
    final double amount;
    final String note;
    final int smsStatus;
    final String storableDate;
    final String customerId;

    public CustomerSaveTransaction(CustomerActivity addCustomerActivity, double bal, String note, int smsStatus, String storableDate, String customerId) {
        this.activity = addCustomerActivity;
        this.amount = bal;
        this.note = note;
        this.smsStatus = smsStatus;
        this.storableDate = storableDate;
        this.customerId = customerId;
    }

    public final void run() {
        String storableDateString = storableDate == null ? Utility.getStorableDateString(new Date()) : storableDate;
        TransactionRepository transactionRepository = TransactionRepository.getInstance(Application.getAppContext());
        String businessId = User.getBusinessId();
        CustomerForm customer = this.activity.getCustomer();
        if(Utility.isBlank(customer.getId())){
            customer.setId(customerId);
        }
        transactionRepository.saveNewTransaction(businessId, customer.getId(), this.amount, storableDateString, this.note,this.smsStatus);
    }
}
