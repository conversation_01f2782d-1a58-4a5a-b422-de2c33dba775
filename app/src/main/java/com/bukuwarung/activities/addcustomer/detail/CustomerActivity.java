package com.bukuwarung.activities.addcustomer.detail;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CompoundButton;

import androidx.appcompat.app.ActionBar;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.addcustomer.CustomerForm;
import com.bukuwarung.activities.addcustomer.transaction.CustomerSaveTransaction;
import com.bukuwarung.activities.customer.transactiondetail.CustomerTransactionDetailActivity;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.phonebook.model.Contact;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.transaction.customer.CustomerTransactionActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.collectingcalendar.main.CollectingCalendarActivity;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.contact.ui.ContactSearchResultsFragment;
import com.bukuwarung.contact.ui.UserContactFragment;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.TransactionEntityType;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.ReferralRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.databinding.ActivityCustomerBinding;
import com.bukuwarung.dialogs.login.LoginBottomSheetDialog;
import com.bukuwarung.dialogs.login.VerifyOtpBottomSheetDialog;
import com.bukuwarung.favoritecustomer.FavoriteCustomerWidget;
import com.bukuwarung.keyboard.CustomKeyboardView;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.OnboardingPrefManager;
import com.bukuwarung.session.AuthHelper;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.tutor.shape.FocusGravity;
import com.bukuwarung.tutor.shape.ShapeType;
import com.bukuwarung.tutor.view.OnboardingWidget;
import com.bukuwarung.utils.AnimationExtensionKt;
import com.bukuwarung.utils.AppIdGenerator;
import com.bukuwarung.utils.ExtensionsKt;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Calendar;
import java.util.Date;

import dagger.android.AndroidInjection;

import static com.bukuwarung.constants.AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_SAVED;
import static com.bukuwarung.constants.AnalyticsConst.NOTA_STANDARD_ENABLED;
import static com.bukuwarung.constants.AnalyticsConst.SMS_CHECKBOX_ENABLED;
import static com.bukuwarung.preference.OnboardingPrefManager.TUTOR_ADD_CUSTOMER;
import static com.bukuwarung.preference.OnboardingPrefManager.TUTOR_SAVE_UTANG_PIUTANG;

public final class CustomerActivity extends AppActivity implements LoginBottomSheetDialog.LoginSheetListener, VerifyOtpBottomSheetDialog.OtpSheetListener, ContactSearchResultsFragment.OnCustomerSelectedCallback, OnboardingWidget.OnboardingWidgetListener, FavoriteCustomerWidget.FavoriteCustomerCallback {

    // region Private Variables
    private String transactionDate;
    private CustomKeyboardView keyboardView;
    private int transactionType = -1; /*debit = -1. credit = 1*/
    private String newReportId = "";
    private long lastEventClicked = 0;
    private String currentEntryPoint = "";
    private ActivityCustomerBinding activityCustomerDataBinding;
    private String oldCustomerId = "";
    private UserContactFragment userContactFragment;
    private CustomerForm customer;
    private CustomerEntity customerEntity;
    // endregion
    private boolean showTutorial = false;
    public static final String TRANSACTION_TYPE_TAG = "transaction_type";
    public static final String EXTRA_SHOW_TUTORIAL = "showTutorial";
    public static final String TRANSACTING_USER_ENTITY = "transactingUserEntity";
    public static final String IS_EDIT_TRANSACTION = "isEditTransaction";
    public static final String IS_FROM_EDIT_TRANSACTION = "isFromEditTransaction";
    public static final String TRANSACTION_ID = "transaction_id";
    public static final String SHOW_BUSINESS_DASHBOARD = "show_business_dashboard";

    private boolean fullyPaid = false;
    private boolean fullySettled = false;
    private boolean sendSms = false;
    private String contactType = null;
    private boolean isFromEdit;
    private String transactionId;
    private String from = null;
    private int txnCount = 0;
    private long notaVisibleThreshold = 0;

    private boolean isFavoriteCustomerPinEnabled = false;

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        AndroidInjection.inject(this);
        activityCustomerDataBinding = DataBindingUtil.setContentView(this, R.layout.activity_customer);
        setSupportActionBar(activityCustomerDataBinding.toolbar);

        ActionBar supportActionBar = getSupportActionBar();
        if (supportActionBar != null)
            supportActionBar.setDisplayHomeAsUpEnabled(false);

        if (getIntent().hasExtra(TRANSACTION_TYPE_TAG)) {
            transactionType = getIntent().getExtras().getInt(TRANSACTION_TYPE_TAG);
        }
        String valFromHome = getIntent().getStringExtra(TRANSACTION_TYPE_TAG);
        if (!TextUtils.isEmpty(valFromHome)) {
            transactionType = Integer.parseInt(valFromHome);
        }

        // initially save button will be in disable state.
        disableSaveButton();

        // call setup data to set the bindings
        setupData();

        // call event listeners handling
        eventListenersHandling();

        // change currency and nominal text color base on transaction type
        updateNominalColor(false);

        if (getIntent().hasExtra(EXTRA_SHOW_TUTORIAL)) {
            showTutorial = getIntent().getBooleanExtra(EXTRA_SHOW_TUTORIAL, false);
        }

        if (showTutorial && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(TUTOR_ADD_CUSTOMER)) {
            OnboardingWidget.Companion.createInstance(this, this, TUTOR_ADD_CUSTOMER, activityCustomerDataBinding.onboardingAddCustomer, R.drawable.onboarding_attention, "",
                    getString(R.string.onboarding_new_customer), "", FocusGravity.CENTER, ShapeType.RECTANGLE_FULL, 2, 2, true, false);
            }

        refreshSettlementCheckbox();
        keyboardView.setVisibility(View.VISIBLE);
        txnCount = TransactionRepository.getInstance(this).getUtangTransactionCountWithDeletedRecords();
        notaVisibleThreshold = RemoteConfigUtils.INSTANCE.getForcedUtangInvoiceVisibilityThreshold();
    }


    @Override
    public void onResume() {
        super.onResume();

        if (getIntent().hasExtra("from")) {
            from = getIntent().getStringExtra("from");
        }
    }

    private void eventListenersHandling() {
            activityCustomerDataBinding.note.setOnFocusChangeListener((view, b) -> {
                if (b) {
                    hideCalculator();
                }
            });

            activityCustomerDataBinding.paymentCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (customerEntity != null && !Utility.isBlank(customerEntity.name)) {
                    if (isChecked) {
                        fullyPaid = true;
                        onCheckboxEnabledState(buttonView);
                    } else {
                        fullyPaid = false;
                        onCheckboxDisabledState(buttonView);
                    }
                    updateNominalColor(isChecked);
                }
            });

            activityCustomerDataBinding.saveButton.setOnClickListener(v -> {
                if (SystemClock.elapsedRealtime() - lastEventClicked < 600) {
                    return;
                }
                lastEventClicked = SystemClock.elapsedRealtime();

                if (!AuthHelper.isValidSessionOperation()) {
                    callLoginBottomsheet(false, AnalyticsConst.UTANG);
                    return;
                }
                saveCustomerTransaction();
                setResult(Activity.RESULT_OK);
            });

            activityCustomerDataBinding.dateTextView.setOnClickListener(v -> setTransactionDate());

            activityCustomerDataBinding.close.setOnClickListener(view -> {
                InputUtils.hideKeyBoardWithCheck(CustomerActivity.this);
                if (!RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage()) {
                    MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
                }
                onBackPressed();
            });

            activityCustomerDataBinding.inputAmountLayout.setOnClickListener(view1 -> {
                if (!activityCustomerDataBinding.paymentCheckbox.isChecked()) {
                    keyboardView.setVisibility(View.VISIBLE);
                    toggleKeyboard();
                    keyboardView.showCursor();
                }

            });

            keyboardView.setOnSubmitListener(this::showSaveButton);

            activityCustomerDataBinding.sendCustomerSms.setOnCheckedChangeListener((buttonView, isChecked) -> {
                sendSms = isChecked;
            });

            activityCustomerDataBinding.contactLayout.setOnClickListener(v -> loadUserContactFragment());
    }

    // checkbox enable state handling
    private void onCheckboxEnabledState(CompoundButton compoundButton) {
        updateNominalColor(false);
        compoundButton.setButtonTintList(ColorStateList.valueOf(getResources().getColor(R.color.blue_60)));
                 //disable the input field
            activityCustomerDataBinding.transactionInputAmountResult.setEnabled(false);
            activityCustomerDataBinding.transactionInputAmountResult.clearFocus();
            hideCalculator();
            keyboardView.hideCursor();
            activityCustomerDataBinding.currencySymbol.setText(Utility.getCurrency());
            activityCustomerDataBinding.transactionInputAmountResult.setText(activityCustomerDataBinding.customerBalance.getText());
            activityCustomerDataBinding.additionalDataLayout.setVisibility(View.VISIBLE);

            // change the save button state
            activityCustomerDataBinding.saveButton.setBackgroundColor(ContextCompat.getColor(this, R.color.new_yellow));
            activityCustomerDataBinding.saveButton.setTextColor(ContextCompat.getColor(this, R.color.black_80));
        enableSaveButton();
    }

    // checkbox disable state handling
    private void onCheckboxDisabledState(CompoundButton compoundButton) {
        compoundButton.setButtonTintList(ColorStateList.valueOf(getResources().getColor(R.color.outline_text_input_color)));

            //enable the input field
            activityCustomerDataBinding.transactionInputAmountResult.setEnabled(true);
            activityCustomerDataBinding.saveButton.setClickable(true);
            activityCustomerDataBinding.transactionInputAmountResult.setText(Utility.formatCurrency(keyboardView.getInputAmountInDouble()));
            activityCustomerDataBinding.transactionInputAmountResult.setFocusable(true);
        keyboardView.showCursor();
    }

    // setup data using data binding
    private void setupData() {
        try {
            getCustomer();
            FeaturePrefManager.getInstance().exitWithoutTransaction(AppConst.CUSTOMER_TRANSACTION);
            newReportId = AppIdGenerator.getReportId();
            transactionDate = Utility.getStorableDateString(new Date());
             if (Utility.hasInternet()) {
                    activityCustomerDataBinding.tvOffline.setVisibility(View.GONE);
                } else {
                    activityCustomerDataBinding.tvOffline.setVisibility(View.VISIBLE);
                }
                View cursor = activityCustomerDataBinding.cursor;
                // binding calls initialization
                activityCustomerDataBinding.note.clearFocus();
                activityCustomerDataBinding.setTransactionType(transactionType);
                activityCustomerDataBinding.note.setOnClickListener(view -> hideCalculator());
                activityCustomerDataBinding.currencySymbol.setText(Utility.getCurrency());
                activityCustomerDataBinding.dateTextView.setText(Utility.getReadableDateString(transactionDate));
                activityCustomerDataBinding.customerBalanceCurrencySymbol.setText(Utility.getCurrency());
                activityCustomerDataBinding.customerBalanceLayout.setVisibility(View.GONE);
                // custom keyboard initialization
                keyboardView = activityCustomerDataBinding.keyboardView;
                keyboardView.setResultTv(activityCustomerDataBinding.transactionInputAmountResult);
                keyboardView.setExprTv(activityCustomerDataBinding.transactionInputAmount);
                keyboardView.setResultLayout(activityCustomerDataBinding.expressionLayout);
                keyboardView.setCursor(cursor);
                keyboardView.setCurrency(activityCustomerDataBinding.currencySymbol);
                keyboardView.showCursor();

                activityCustomerDataBinding.scrollView.setVisibility(View.VISIBLE);
                activityCustomerDataBinding.bottomLayout.setVisibility(View.VISIBLE);

                activityCustomerDataBinding.rgTrxType.setOnCheckedChangeListener((group, checkedId) -> {
                    if (checkedId == activityCustomerDataBinding.rbCredit.getId()) {
                        transactionType = AppConst.CREDIT;
                    } else {
                        transactionType = AppConst.DEBIT;
                    }
                    activityCustomerDataBinding.paymentCheckbox.setChecked(false);
                    refreshSettlementCheckbox();
                    activityCustomerDataBinding.setTransactionType(transactionType);
                    updateNominalColor(false);
                    updateReceiveAmountTitle();
                });
                updateReceiveAmountTitle();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateNominalColor(boolean isChecked) {
        int color = getCheckedColor(isChecked);
         activityCustomerDataBinding.transactionInputAmountResult.setTextColor(color);
            activityCustomerDataBinding.currencySymbol.setTextColor(color);
            activityCustomerDataBinding.cursor.setBackgroundColor(color);
    }

    private int getCheckedColor(boolean isChecked) {
        int color;
        if (!isChecked) {
            if (transactionType == AppConst.DEBIT) {
                color = ContextCompat.getColor(this, R.color.red_80);
            } else {
                color = ContextCompat.getColor(this, R.color.green_80);
            }
        } else {
            color = ContextCompat.getColor(this, R.color.black_40);
        }
        return color;
    }

    // Set transaction date using date picker dialog
    private void setTransactionDate() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int date = calendar.get(Calendar.DATE);
            DatePickerDialog datePickerDialog = new DatePickerDialog
                    (this,
                            (datePicker, year1, month1, day) -> {
                                Calendar instance = Calendar.getInstance();
                                instance.set(year1, month1, day);
                                transactionDate = Utility.getStorableDateString(instance.getTime());
                                activityCustomerDataBinding.dateTextView.setText(
                                        Utility.getReadableDateString(transactionDate)
                                );

                            }, year, month, date);

            datePickerDialog.setTitle(R.string.date);
            datePickerDialog.show();

        Animation moveDown = AnimationUtils.loadAnimation(CustomerActivity.this,
                R.anim.move_down);
        keyboardView.startAnimation(moveDown);
        keyboardView.setVisibility(View.GONE);
        keyboardView.hideCursor();
    }

    // on selected customer
    private void onSelectedCustomer(Contact contact) {
        final String cstName = contact.getName();
        final String cstMobile = contact.getMobile();
        oldCustomerId = contact.getCustomerId();
        if (cstName == null || cstMobile == null) return;

            if (!cstMobile.isEmpty()) {
                activityCustomerDataBinding.sendCustomerSms.setVisibility(View.VISIBLE);
                activityCustomerDataBinding.sendCustomerSms.setChecked(false);
                sendSms = false;
            } else {
                activityCustomerDataBinding.sendCustomerSms.setVisibility(View.GONE);
            }


        if (!Utility.isBlank(oldCustomerId)) {
            customerEntity = CustomerRepository.getInstance(this).getCustomerById(oldCustomerId);
            newReportId = customerEntity.altCustomerId;
//            contactType = AnalyticsConst.CUSTOMER_EXISTING;
        } else {
            customerEntity = null;
//            contactType = !Utility.isBlank(cstMobile) ? AnalyticsConst.CUSTOMER_PHONEBOOK :
//                    !Utility.isBlank(cstName) ? AnalyticsConst.CUSTOMER_CREATE_MANUAL : null;
        }

        getCustomer().mobile = !Utility.isBlank(contact.getMobile())
                ? Utility.cleanPhonenumber(contact.getMobile()) : "";
        getCustomer().name = contact.getName();

        customer.mobile = !Utility.isBlank(contact.getMobile())
                ? Utility.cleanPhonenumber(contact.getMobile()) : "";

        customer.name = contact.getName();

            activityCustomerDataBinding.name.setText(cstName);
            activityCustomerDataBinding.scrollView.setVisibility(View.VISIBLE);
            activityCustomerDataBinding.bottomLayout.setVisibility(View.VISIBLE);

        refreshSettlementCheckbox();

        updateReceiveAmountTitle();

        if (!RemoteConfigUtils.INSTANCE.shouldSendSmsUtang()) {
            sendSms = false;
                activityCustomerDataBinding.sendCustomerSms.setVisibility(View.GONE);
        }

    }

    private void refreshSettlementCheckbox() {
            if (Utility.isBlank(oldCustomerId)) {
                activityCustomerDataBinding.customerBalance.setText(Utility.formatCurrency(0.0));
                activityCustomerDataBinding.paymentCheckbox.setVisibility(View.GONE);
                activityCustomerDataBinding.setIsDebit(false);
                activityCustomerDataBinding.customerBalanceLayout.setVisibility(View.GONE);
            } else {
                activityCustomerDataBinding.customerBalanceLayout.setVisibility(View.VISIBLE);
                double customerBalance = TransactionRepository.getInstance(this).getCustomerBalance(oldCustomerId);
                activityCustomerDataBinding.customerBalance.setText(Utility.formatCurrency(Math.abs(customerBalance)));
                if (Double.compare(customerBalance, AppConst.ZERO) >= 0) {
                    activityCustomerDataBinding.underpaidAmount.setText(getString(R.string.you_owe_total));
                    activityCustomerDataBinding.setIsDebit(false);
                    if (transactionType == AppConst.DEBIT) {
                        activityCustomerDataBinding.paymentCheckbox.setVisibility(View.VISIBLE);
                    } else {
                        activityCustomerDataBinding.paymentCheckbox.setVisibility(View.GONE);
                    }
                } else if (Double.compare(customerBalance, AppConst.ZERO) < 0) {
                    activityCustomerDataBinding.underpaidAmount.setText(getString(R.string.remain_debt) + " " + activityCustomerDataBinding.name.getText());
                    activityCustomerDataBinding.setIsDebit(true);
                    if (transactionType != AppConst.DEBIT) {
                        activityCustomerDataBinding.paymentCheckbox.setVisibility(View.VISIBLE);
                    } else {
                        activityCustomerDataBinding.paymentCheckbox.setVisibility(View.GONE);
                    }
                }
                if (customerBalance == 0) {
                    activityCustomerDataBinding.customerBalanceLayout.setVisibility(View.GONE);
                    activityCustomerDataBinding.paymentCheckbox.setVisibility(View.GONE);
                }
            }

    }

    private void updateReceiveAmountTitle() {
            if (transactionType == AppConst.DEBIT) {
                activityCustomerDataBinding.paidReceiveAmountTitle.setText(getString(R.string.you_paid));
                activityCustomerDataBinding.contactLayoutTitle.setText(getString(R.string.give_to));
            } else {
                activityCustomerDataBinding.paidReceiveAmountTitle.setText(getString(R.string.i_accept));
                activityCustomerDataBinding.contactLayoutTitle.setText(getString(R.string.Receive_from));
            }
    }

    // Customer instance
    public CustomerForm getCustomer() {

            this.customer = new CustomerForm("", activityCustomerDataBinding.name.getText().toString(), "", SessionManager.getInstance().getCountryCode().substring(1), "", 0,
                    false);
        return this.customer;
    }

    // request read write permission handling
    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        if (i == PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION && Build.VERSION.SDK_INT >= 23) {
            if (userContactFragment != null) {
                userContactFragment.allowedContactPermission();
            }
            //Toast.makeText(this, this.getString(R.string.alert_deny_contact_permission), Toast.LENGTH_LONG).show();
        }
    }

    private void loadUserContactFragment() {

        if (transactionType == AppConst.CREDIT) {
            userContactFragment = UserContactFragment.Companion.getInstance(UserContactFragment.getTRANSACTION_TYPE_CREDIT(), customer == null ? "" : customer.getName() == null ? "" : customer.getName(), true);
        } else {
            userContactFragment = UserContactFragment.Companion.getInstance(UserContactFragment.getTRANSACTION_TYPE_DEBIT(), customer == null ? "" : customer.getName() == null ? "" : customer.getName(), true);
        }

        getSupportFragmentManager().beginTransaction().add(R.id.contact_fragment_container, userContactFragment).commit();
        //hideContactForm();
    }

    // region Transaction Methods
    private void saveCustomerTransaction() {
        final Activity activity = this;
        if (customer != null && Utility.isBlank(customer.name)) {
            NotificationUtils.alertToast("Anda belum menambahkan pelanggan");
            return;
        }
        String balanceInputAmount = keyboardView.getInputAmount();
            if (activityCustomerDataBinding.paymentCheckbox.isChecked()) {
                double customerBalance = TransactionRepository.getInstance(this).getCustomerBalance(oldCustomerId);
                balanceInputAmount = String.valueOf(Math.abs(customerBalance));//activityCustomerDataBinding.customerBalance.getText().toString().replace(",", "");
            }

        boolean isNewTransaction = false;
        double balanceAmount = !Utility.isBlank(balanceInputAmount) ? Double.parseDouble(balanceInputAmount) : 0.0d;
            if (activityCustomerDataBinding.paymentCheckbox.isChecked()) {
                balanceAmount = -customerEntity.balance;
            } else if (transactionType == AppConst.DEBIT) {
                balanceAmount = 0 - balanceAmount;
            }

        double balance = balanceAmount;
        try {
            AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.DATE, new Date())
                    .put(AnalyticsConst.FEATURE, AnalyticsConst.UTANG);
            builder.put(AnalyticsConst.AMOUNT, Math.abs(balanceAmount));
            if (transactionType == AppConst.DEBIT) {
                builder.put(AnalyticsConst.TYPE, AnalyticsConst.DEBIT);
            } else {
                builder.put(AnalyticsConst.TYPE, AnalyticsConst.CREDIT);
            }
        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        if (!SessionManager.getInstance().hasCreatedUtangRecord()) {
            SessionManager.getInstance().setHasCreatedUtangRecord(true);
        }
        Utility.trackTransactionCount();
        boolean noteAdded = false;
        String savedTransactionId = null;
        if (activity != null && Utility.isBlank(oldCustomerId)) {
            isNewTransaction = true;
            CustomerActivity customerActivity = this;
            if (customer != null) {
                    CustomerActivity.SaveCustomerHandler
                            .SaveCustomerAsyncTask saveCustomerAsyncTask = new CustomerActivity.SaveCustomerHandler.
                            SaveCustomerAsyncTask(customerActivity, balance,
                            activityCustomerDataBinding.note.getText().toString(), this.transactionDate, this.newReportId, customer.mobile, sendSms);
                    saveCustomerAsyncTask.execute(new CustomerForm[]{customer});


                // TODO : This is the old analytics as we using in past
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put("source", "phone_book");
                propBuilder.put("transaction_type", Utility.getTransactionTypeStr(balanceAmount));
                propBuilder.put("transaction_amount", balanceInputAmount);
                propBuilder.put(AnalyticsConst.FULLY_PAID, fullyPaid);
                propBuilder.put(AnalyticsConst.DESCRIPTION_FILLED, noteAdded);
                propBuilder.put(AnalyticsConst.CUSTOMER_SOURCE, contactType);
                    propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);
                propBuilder.put(AnalyticsConst.SEND_SMS_REMINDER, sendSms);
                if (!TextUtils.isEmpty(from)) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE);
                }
                propBuilder.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsUtang());
                propBuilder.put(NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewUtangInvoice());
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CREATED_NEW_CUSTOMER, propBuilder);
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_CREATED_NEW_CUSTOMER, this);

                // disabled by product team request
                // Utility.showSmsSentToast(balance < 0 ? -1 : 1, this, sendSms);
            }
        } else {
                savedTransactionId = TransactionRepository.getInstance(Application.getAppContext()).saveNewTransaction(User.getBusinessId(), oldCustomerId, balanceAmount, transactionDate, activityCustomerDataBinding.note.getText().toString(), 1);

            // TODO : This is the old analytics as we using in past
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("transaction_type", InputUtils.getTransactionTypeStr(balanceAmount));
            propBuilder.put("transaction_amount", String.valueOf(balanceAmount));
            propBuilder.put("cst_trans_seq", SessionManager.getInstance().getTransSeq());
            propBuilder.put("cst_seq", SessionManager.getInstance().getCstSeq());
            propBuilder.put("form", "new_utang_form");
            propBuilder.put("transaction_id", savedTransactionId);
            // not sure about this event
            propBuilder.put(AnalyticsConst.FULLY_PAID, fullyPaid);
            propBuilder.put(AnalyticsConst.FULLY_SETTLED, fullySettled);
            propBuilder.put(AnalyticsConst.DESCRIPTION_FILLED, noteAdded);
            propBuilder.put(AnalyticsConst.CUSTOMER_SOURCE, contactType);
                propBuilder.put(AnalyticsConst.UTANG_UI_VARIATION, AnalyticsConst.PRE_JUNE);

            propBuilder.put(AnalyticsConst.SEND_SMS_REMINDER, sendSms);

            if (isFromEdit) {
                String actionBy = AnalyticsConst.ACCOUNTING;
                TransactionEntityType type = TransactionRepository.getInstance(this).getTransactionById(transactionId).transactionType;
                if (type == TransactionEntityType.PAYMENT) {
                    actionBy = AnalyticsConst.PAYMENTS;
                }
                AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
                prop.put(AnalyticsConst.UPDATE_TYPE, AnalyticsConst.EDIT_NEW);
                prop.put(AnalyticsConst.ACTION_BY, actionBy);
                prop.put(AnalyticsConst.TRANSACTION_ID, transactionId);
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_TRANSACTION_UPDATE, prop);
            } else {
                propBuilder.put(AnalyticsConst.CUST_FAV_PIN_ENABLED, isFavoriteCustomerPinEnabled);
                if (!TextUtils.isEmpty(from)) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE);
                }
                propBuilder.put(SMS_CHECKBOX_ENABLED, RemoteConfigUtils.INSTANCE.shouldSendSmsUtang());
                propBuilder.put(NOTA_STANDARD_ENABLED, RemoteConfigUtils.INSTANCE.shouldShowNewUtangInvoice());

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_DETAIL_TAP_SAVED, propBuilder);

                SurvicateAnalytics.invokeEventTracker(EVENT_CUSTOMER_DETAIL_TAP_SAVED, this);
            }

            String receiverPhone = this.getCustomer().getCountryCode() + "" + this.getCustomer().getMobile();
            try {
                CustomerEntity customerEntity = CustomerRepository.getInstance(this).getCustomerById(oldCustomerId);
                double finalBalance = customerEntity.balance;
                String reportId = customerEntity.altCustomerId;
                boolean sendMessage = customerEntity != null && sendSms && !Utility.isBlank(customerEntity.phone) && customerEntity.phone.length() > 6;
                if (sendMessage) {
                    Utility.sendSms(receiverPhone, balance, 0, this, reportId, -1, finalBalance);
                }
                // disabled by product team request
                // Utility.showSmsSentToast(balance < 0 ? -1 : 1, this, sendMessage);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (AppConfigManager.getInstance().useReferral()
                && getCurrentBook() != null && Math.abs(balance) > AppConfigManager.getInstance().getReferralTxnVal()) {
            ReferralRepository.getInstance().addTransactionPoints(getCurrentBook(), false);
        }

        if (!isNewTransaction) {
            final boolean fromCollecting = getIntent().getBooleanExtra(PARAM_FROM_COLLECTING, false);
            Intent intentCollecting = new Intent(this, CollectingCalendarActivity.class);
            intentCollecting.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);

            // lottie animation
            if (RemoteConfigUtils.INSTANCE.shouldShowUtangSuccessAnimation()) {
                    activityCustomerDataBinding.animationLayout.tvTrxSuccess.setText(RemoteConfigUtils.INSTANCE.getTrxSuccessMessage());
                    String finalSavedTransactionId = savedTransactionId;
                    AnimationExtensionKt.showForOnce(activityCustomerDataBinding.animationLayout.lavSuccess, activityCustomerDataBinding.animationLayout.successView, 75, () -> {
                        if (!fromCollecting) {
                            if (txnCount < notaVisibleThreshold) {
                                MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
                            } else {
                                Intent intent = new Intent(this, CustomerTransactionDetailActivity.class);
                                intent.putExtra(CustomerTransactionDetailActivity.TRX_ID_PARAM, finalSavedTransactionId);
                                intent.putExtra(CustomerTransactionDetailActivity.CST_ID_PARAM, oldCustomerId);
                                intent.putExtra("OPERATION_TYPE", "ADD_TRANSACTION");
                                intent.putExtra("from", from);
                                startActivity(intent);
                            }
                            finish();
                        } else {
                            startActivity(intentCollecting);
                            if (!Utility.isBlank(oldCustomerId)) finish();
                        }
                        return null;
                    });
            } else {
                if (!fromCollecting) {
                    if (txnCount < notaVisibleThreshold) {
                        MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
                    } else {
                        Intent intent = new Intent(this, CustomerTransactionDetailActivity.class);
                        intent.putExtra(CustomerTransactionDetailActivity.TRX_ID_PARAM, savedTransactionId);
                        intent.putExtra(CustomerTransactionDetailActivity.CST_ID_PARAM, oldCustomerId);
                        intent.putExtra("OPERATION_TYPE", "ADD_TRANSACTION");
                        intent.putExtra("from", from);
                        startActivity(intent);
                    }
                    finish();
                } else {
                    startActivity(intentCollecting);
                    if (!Utility.isBlank(oldCustomerId)) finish();
                }
            }
        }
        InputUtils.hideKeyBoardWithCheck(this);
    }

    // open customer transaction by customer id.
    private void openCustomerTransaction(String customerId) {
        Intent intent = new Intent(this, CustomerTransactionActivity.class);
        intent.putExtra("customerId", customerId);
        intent.putExtra("firstTrx", true);
        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);

        if (RemoteConfigUtils.INSTANCE.shouldShowUtangSuccessAnimation()) {
                activityCustomerDataBinding.animationLayout.tvTrxSuccess.setText(RemoteConfigUtils.INSTANCE.getTrxSuccessMessage());
                AnimationExtensionKt.showForOnce(activityCustomerDataBinding.animationLayout.lavSuccess, activityCustomerDataBinding.animationLayout.successView, 75, () -> {
                    startActivity(intent);
                    finish();
                    return null;
                });
        } else {
            startActivity(intent);
            finish();
        }
    }

    @Override
    public void onOnboardingDismiss(@Nullable String id, @NotNull String body, boolean isFromButton, boolean isFromCloseButton, boolean isFromOutside) {

    }

    @Override
    public void onOnboardingButtonClicked(@Nullable String id, boolean isFromHighlight) {

    }

    // Save customer transaction using async task.
    public static final class SaveCustomerHandler {

        public static final class SaveCustomerAsyncTask extends AsyncTask<CustomerForm, Void, String> {

            private final CustomerActivity activity;
            private final double amount;
            private final String note;
            private final String date;
            private final String reportId;
            private final String phoneNumber;
            private final boolean sendSms;

            public SaveCustomerAsyncTask(CustomerActivity customerActivity, double amount, String note, String storableDate, String reportId, String phoneNumber, boolean sendSms) {
                this.activity = customerActivity;
                this.amount = amount;
                this.note = note;
                this.date = storableDate;
                this.reportId = reportId;
                this.sendSms = sendSms;
                this.phoneNumber = phoneNumber;
            }

            public String doInBackground(CustomerForm... customerUiArr) {
                CustomerForm customerUi = customerUiArr[0];
                customerUi.reportId = reportId;
                String customerId = CustomerRepository.getInstance(Application.getAppContext())
                        .saveCustomer(User.getBusinessId(), customerUi.getName(), customerUi.getAddress(),
                                customerUi.getMobile(), customerUi.getCountryCode(), customerUi.getBalance(),
                                reportId, customerUi.getSmsEnabled(), null);
                try {

                    String receiverPhone = customerUi.getCountryCode() + "" + phoneNumber;
                    int type = Utility.getTransactionTypeInt(this.amount);

                    if (sendSms && !Utility.isBlank(phoneNumber) && phoneNumber.length() > 6) {
                        Utility.sendSms(receiverPhone, this.amount, type,
                                activity, customerUi.reportId,
                                -1);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return customerId;
            }

            private String getMessage(double amount, String bookName) {
                return "Tercatat di " + bookName + " dengan hutang sebesar " + Math.abs(amount);
            }

            public void onPostExecute(String customerId) {
                CustomerForm customer = this.activity.getCustomer();
                customer.setId(customerId);
                int smsStatus = customer.getSmsEnabled() ? 1 : 0;
                CustomerActivity.SaveCustomerHandler.saveTransaction(
                        this.activity, this.amount, this.note, smsStatus, this.date, customerId);
            }
        }


        public static void saveTransaction(CustomerActivity customerActivity,
                                           double balance, String notes, int smsStatus, String date, String customerId) {
            CustomerSaveTransaction customerSaveFirstTransaction =
                    new CustomerSaveTransaction(customerActivity, balance, notes, smsStatus, date,customerId);
            new Thread(customerSaveFirstTransaction).start();
            customerActivity.openCustomerTransaction(customerId);
        }
    }

    // hide the calculator view.
    private void hideCalculator() {
        if (keyboardView.getVisibility() == View.VISIBLE) {
            keyboardView.setVisibility(View.GONE);
            keyboardView.hideCursor();
        }
    }

    // toggle keyboard
    private void toggleKeyboard() {
        InputUtils.hideKeyBoardWithCheck(this);

        if (keyboardView.getVisibility() == View.GONE) {
            Animation moveup = AnimationUtils.loadAnimation(this,
                    R.anim.move_up);
            keyboardView.startAnimation(moveup);
            keyboardView.setVisibility(View.VISIBLE);
            keyboardView.showCursor();

                activityCustomerDataBinding.note.clearFocus();
        }
    }

    // disable save button
    private void disableSaveButton() {
            activityCustomerDataBinding.saveButton.setEnabled(false);
            activityCustomerDataBinding.saveButton.setClickable(false);
    }

    private void enableSaveButton() {
            activityCustomerDataBinding.additionalDataLayout.setVisibility(View.VISIBLE);
            activityCustomerDataBinding.saveButton.setBackgroundColor(ContextCompat.getColor(this, R.color.new_yellow));
            activityCustomerDataBinding.saveButton.setTextColor(ContextCompat.getColor(this, R.color.black_80));
            activityCustomerDataBinding.saveButton.setEnabled(true);
            activityCustomerDataBinding.saveButton.setClickable(true);
    }

    // region Login, Verify Otp Methods
    @Override
    public void goToVerifyOtp(@NotNull String phone, @NotNull String countryCode, @NotNull String method) {
        VerifyOtpBottomSheetDialog verifyOtpBottomSheetDialog = VerifyOtpBottomSheetDialog.Companion.newInstance(phone, countryCode, currentEntryPoint, this);
        verifyOtpBottomSheetDialog.show(getSupportFragmentManager(), VerifyOtpBottomSheetDialog.TAG);
    }

    @Override
    public void callLoginBottomsheet(boolean openKeyboard, @NotNull String entryPoint) {
        currentEntryPoint = entryPoint;
        showLogin(openKeyboard);
    }

    private void showLogin(boolean openKeyboard) {
        LoginBottomSheetDialog loginBtSheet = LoginBottomSheetDialog.Companion.newInstance(currentEntryPoint, this);
        loginBtSheet.show(getSupportFragmentManager(), LoginBottomSheetDialog.TAG);
        loginBtSheet.keyboardState(openKeyboard);
    }

    @Override
    public void onFinishOtp() {
        BusinessRepository.getInstance(this).mergeGuestRecords();
        CashRepository.getInstance(this).mergeGuestRecords();
        ProductRepository.getInstance(this).mergeGuestRecords();
        SessionManager.getInstance().isGuestUser(false);
        Thread mThread = new Thread() {
            @Override
            public void run() {
                TransactionUtil.backupAllTransactions();
                MainActivity.startActivityAndClearTop(CustomerActivity.this);
            }
        };
        mThread.start();
    }

    @Override
    public void onBackPressed() {
        if (userContactFragment != null) {
            getSupportFragmentManager().beginTransaction().remove(userContactFragment).commit();
            userContactFragment = null;
        } else if (keyboardView.getVisibility() == View.VISIBLE) {
            keyboardView.setVisibility(View.GONE);
            showSaveButton();
        } else {
            moveToMainActivity();
        }
    }

    private void showSaveButton() {
        enableSaveButton();
        if (showTutorial && !OnboardingPrefManager.Companion.getInstance().getHasFinishedForId(OnboardingPrefManager.TUTOR_SAVE_UTANG_PIUTANG)) {
            OnboardingWidget.Companion.createInstance(this, this,
                    TUTOR_SAVE_UTANG_PIUTANG, activityCustomerDataBinding.saveOnboarding, R.drawable.onboarding_great, getString(R.string.done_exclmark),
                    getString(R.string.onboarding_save_debt_subtitle), "", FocusGravity.CENTER, ShapeType.RECTANGLE_FULL, 1, 1, true, false);
        }
    }

    private void moveToMainActivity(){
        if(!getIntent().hasExtra(SHOW_BUSINESS_DASHBOARD)&& RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage()){
        if (RemoteConfigUtils.NewHomePage.INSTANCE.shouldShowNewHomePage())  {
            MainActivity.startActivitySingleTopToTab(this, TabName.HOME);
        } else {
            MainActivity.startActivitySingleTopToTab(this, TabName.CUSTOMER);
        }}
        finish();
    }

    @Override
    public void onCustomerSelected(@Nullable Contact contact, @NotNull String contactSource) {
        handleOnCustomerSelected(contact, contactSource);
    }

    @Override
    public void onFavoriteCustomerSelected(@Nullable Contact contact) {
        handleOnCustomerSelected(contact, AnalyticsConst.FAV_CUST_PIN);
    }

    private void handleOnCustomerSelected(@Nullable Contact contact, String contactSource) {
        if (userContactFragment != null) {
            getSupportFragmentManager().beginTransaction().remove(userContactFragment).commit();
            userContactFragment = null;
        }

       try {
           // ui binding could be null
           activityCustomerDataBinding.customerBalanceLayout.setVisibility(View.VISIBLE);
       }catch (NullPointerException exception){
           ExtensionsKt.recordException(exception);
       }

        contactType = contactSource;
        onSelectedCustomer(contact);
            toggleKeyboard();
        enableSaveButton();
    }
    //endregion

    // TODO : where we are calling the collecting calender
    public static String PARAM_FROM_COLLECTING = "collectingcalendar";


}