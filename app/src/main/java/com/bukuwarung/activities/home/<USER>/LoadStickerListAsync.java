package com.bukuwarung.activities.home.data;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.util.Log;
import android.util.Pair;

import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.stickers.StickerPack;
import com.bukuwarung.activities.stickers.StickerPackDetailsActivity;
import com.bukuwarung.activities.stickers.StickerPackLoader;
import com.bukuwarung.activities.stickers.StickerPackValidator;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import static android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP;
import static android.content.Intent.FLAG_ACTIVITY_SINGLE_TOP;
import static com.bukuwarung.activities.stickers.StickerPackListActivity.EXTRA_STICKER_PACK_LIST_DATA;

public class LoadStickerListAsync extends AsyncTask<Void, Void, Pair<String, ArrayList<StickerPack>>> {
    private final WeakReference<MainActivity> contextWeakReference;


    public LoadStickerListAsync(MainActivity activity) {
        this.contextWeakReference = new WeakReference<>(activity);
    }

    @Override
    protected android.util.Pair<String, ArrayList<StickerPack>> doInBackground(Void... voids) {
        ArrayList<StickerPack> stickerPackList;
        try {
            final Context context = contextWeakReference.get();
            if (context != null) {
                stickerPackList = StickerPackLoader.fetchStickerPacks(context);
                if (stickerPackList.size() == 0) {
                    return new android.util.Pair<>("could not find any packs", null);
                }
                for (StickerPack stickerPack : stickerPackList) {
                    StickerPackValidator.verifyStickerPackValidity(context, stickerPack);
                }
                return new android.util.Pair<>(null, stickerPackList);
            } else {
                return new android.util.Pair<>("could not fetch sticker packs", null);
            }
        } catch (Exception e) {
            Log.e("EntryActivity", "error fetching sticker packs", e);
            return new android.util.Pair<>(e.getMessage(), null);
        }
    }

    @Override
    protected void onPostExecute(android.util.Pair<String, ArrayList<StickerPack>> stringListPair) {

        final MainActivity mainActivity = contextWeakReference.get();
        if (mainActivity != null && stringListPair!=null && stringListPair.second!=null) {
            Intent intent = new Intent(mainActivity, StickerPackDetailsActivity.class);
            intent.putExtra(StickerPackDetailsActivity.EXTRA_SHOW_UP_BUTTON, true);
            intent.putExtra(StickerPackDetailsActivity.EXTRA_TRANSACTION_TARGET, 0);
            intent.putParcelableArrayListExtra(EXTRA_STICKER_PACK_LIST_DATA, stringListPair.second);
            intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP|FLAG_ACTIVITY_SINGLE_TOP);

            mainActivity.startActivity(intent);
        }
    }

}
