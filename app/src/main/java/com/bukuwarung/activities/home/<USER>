package com.bukuwarung.activities.home;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ArrayAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.bukuwarung.R;

class AppLanguageClickHandler implements View.OnClickListener {

    final private MainActivity mActivity;
    final String title;

    public AppLanguageClickHandler(MainActivity activity,String title){
        mActivity = activity;
        this.title = title;
    }

    @Override
    public void onClick(View view) {
        showLanguageSelectionDialog(mActivity);
    }

    public final void showLanguageSelectionDialog(Activity activity) {
        try {
            Context context = activity;
            final Dialog dialog = new Dialog(context);
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            dialog.setContentView(R.layout.dialog_select_language);
            Window window = dialog.getWindow();
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
            dialog.setCancelable(false);
            TextView languageDialogTitle = dialog.findViewById(R.id.languageSelectDialogTitle);
            languageDialogTitle.setText(title);
            ListView listView = dialog.findViewById(R.id.cstLanguage);
            dialog.findViewById(R.id.closeDialog).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    dialog.dismiss();
                }
            });
            String[] languageList = activity.getResources().getStringArray(R.array.SupportedLanguage);
            listView.setAdapter(new ArrayAdapter(context, R.layout.langauge_list_item, R.id.language_item, languageList));
            dialog.show();
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}
