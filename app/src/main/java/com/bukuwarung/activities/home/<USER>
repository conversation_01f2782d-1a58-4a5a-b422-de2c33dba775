package com.bukuwarung.activities.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.HomeBlueTopItemBinding
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide

class HomeBlueTopAdapter(
    private val itemList: List<BodyBlock?>,
    private val category: String?,
    val getOnClickData: (BodyBlock, String?) -> Unit
) : RecyclerView.Adapter<HomeBlueTopAdapter.HomePaymentInOutViewHolder>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HomePaymentInOutViewHolder {
        val itemBinding =
            HomeBlueTopItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return HomePaymentInOutViewHolder(itemBinding).listen { position, _ ->
            getOnClickData(itemList[position]!!, category)
        }
    }

    override fun onBindViewHolder(holder: HomePaymentInOutViewHolder, position: Int) {
        holder.bind(itemList[position]!!)
    }

    override fun getItemCount(): Int {
        return itemList.size
    }

    class HomePaymentInOutViewHolder(private val binding: HomeBlueTopItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(body: BodyBlock) {
            with(binding) {
                tvTileName.text = body.display_name
                Glide.with(root.context).load(body.icon)
                    .centerCrop().into(ivTileImage)
                if (body.coming_soon) {
                    tvComingSoon.showView()
                    vwComingSoonBg.showView()
                    ivTileImage.alpha = 0.3f
                } else {
                    vwComingSoonBg.hideView()
                    tvComingSoon.hideView()
                    ivTileImage.alpha = 1f
                }
                val feature = when (body.display_name) {
                    PaymentConst.PAYMENT_IN_FEATURE_LABEL -> PaymentConst.Feature.PAYMENT_IN
                    PaymentConst.PAYMENT_OUT_FEATURE_LABEL -> PaymentConst.Feature.PAYMENT_OUT
                    else -> null
                }
                val remainingCountQuota = PaymentUtils.getRemainingCountQuota(feature).orNil
                if (
                    !PaymentUtils.shouldBeBlockedDueToQuota(feature.orEmpty())
                    && remainingCountQuota > 0
                ) {
                    tvQuotaCountLimit.text = root.context.getString(R.string.try_it)
                    tvQuotaCountLimit.showView()
                } else {
                    tvQuotaCountLimit.hideView()
                }

                /**
                 * Show red dot badge for QRIS
                 */
                if (RemoteConfigUtils.getPaymentConfigs().isQrisDiscontinued.isTrue
                    && body.analytics_name == PaymentConst.QRIS_FEATURE_ANALYTICS_NAME
                ) {
                    vwBadge.showView()
                } else {
                    vwBadge.hideView()
                }
            }
        }
    }
}