package com.bukuwarung.activities.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.BottomSheetOnboardingBinding
import com.bukuwarung.databinding.BottomSheetReferralSuccessBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.google.firebase.FirebaseException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.android.synthetic.main.bottom_sheet_referral_success.*
import java.lang.Exception
import java.util.*

class OnboardingBottomSheet : BaseBottomSheetDialogFragment() {


    companion object {
        const val TAG = "onboarding_bottom_sheet"
    }

    private var _binding: BottomSheetOnboardingBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetOnboardingBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        PaymentPrefManager.getInstance().setKybBottomSheetShownDate(Date())
        with(binding) {
            val businessEntity = BusinessRepository.getInstance(requireContext())
                .getBusinessByIdSync(User.getBusinessId())

            if (businessEntity == null) {
                FirebaseCrashlytics.getInstance()
                    .log("null business entity " + User.getBusinessId())
                dismiss()
            } else {
                tvTitle.text = tvTitle.text.toString().replace(
                    "[Store Name]",
                    businessEntity.businessName
                )
            }
        }
        btn_next.setOnClickListener {
            dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}
