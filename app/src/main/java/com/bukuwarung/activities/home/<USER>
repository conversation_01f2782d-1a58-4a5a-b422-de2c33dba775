package com.bukuwarung.activities.home;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import java.util.List;

public final class TabNavigationAdapter extends FragmentStatePagerAdapter {
    private List<Fragment> fragmentList;
    public TabNavigationAdapter(FragmentManager fragmentManager) {
        super(fragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
    }

    public void setFragmentList(List<Fragment> fragmentList) {
        this.fragmentList = fragmentList;
        notifyDataSetChanged();
    }
    @Override
    public Fragment getItem(int i) {
        return fragmentList.get(i);
    }

    public int getCount() {
        if (fragmentList != null)
            return fragmentList.size();
        return 0;
    }

    public final String getFragmentTag(int i) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.valueOf(i));
        sb.append("-tag");
        return sb.toString();
    }
}
