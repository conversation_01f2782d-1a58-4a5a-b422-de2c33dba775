package com.bukuwarung.activities.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.databinding.BottomSheetReferralSuccessBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.pref.PaymentPrefManager
import kotlinx.android.synthetic.main.bottom_sheet_referral_success.*
import java.util.*

class ReferralSuccessBottomSheet(val function: () -> Unit) : BaseBottomSheetDialogFragment() {


    companion object {
        const val TAG = "referral_success_bottom_sheet"
    }

    private var _binding: BottomSheetReferralSuccessBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetReferralSuccessBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        PaymentPrefManager.getInstance().setKybBottomSheetShownDate(Date())
        with(binding) {
//            tvTitle.text = RemoteConfigUtils.getAppText().kybVerificationDrawerTitle
//            tvBody.textHTML(DateTimeUtilsKt.getFormattedDateFromString(
//                RemoteConfigUtils.getPaymentConfigs().kybMandatoryFromDate,
//                DateTimeUtilsKt.YYYY_MM_DD,
//                DateTimeUtilsKt.DD_MMM_YYYY
//            )?.let {
//                RemoteConfigUtils.getAppText().kybVerificationDrawerMessage?.replace("{date}", it)
//            })
//            btnClose.setOnClickListener {
//                dismiss()
            }
            btn_next.setOnClickListener {
//                val url = "${PaymentUtils.getQrisWebUrl()}?onlyKyb=true"
//                startActivity(WebviewActivity.createIntent(context, url, AppConst.EMPTY_STRING))
                function()
                dismiss()
            }
        }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}
