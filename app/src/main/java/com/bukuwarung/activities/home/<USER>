package com.bukuwarung.activities.home

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.work.*
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.RemoteConfigUtils.DailyBusinessUpdateHighlightExperiment.getVariant
import java.util.*
import java.util.concurrent.TimeUnit

class HomeHelper {

    companion object{

        private val DAILY_UPDATE_ACTIVATION_WORK: String= "daily_update_activation_work"
        private val DAILY_UPDATE_STATUS_RESET_WORK: String= "daily_update_status_reset_work"

        fun dailyUpdateActivation(context: Context) {
            //Time for daily update activation
            val diff = getDiff(RemoteConfigUtils.getDailyUpdateActivationHour(),
                RemoteConfigUtils.getDailyUpdateActivationMinute())
            val request = OneTimeWorkRequest.Builder(DailyUpdateActivateWorker::class.java)
                    .setInitialDelay(diff, TimeUnit.MILLISECONDS)
                    .addTag(DAILY_UPDATE_ACTIVATION_WORK)
                    .build()
            WorkManager
                    .getInstance(context)
                    .enqueueUniqueWork(DAILY_UPDATE_ACTIVATION_WORK,ExistingWorkPolicy.KEEP,request)
        }

        fun dailyUpdateStatusReset(context: Context) {
            //Reset daily business update icon at midnight
            val diff = getDiff("00","01")
            // Request for testing which repeats in 5 mins
//            val request = PeriodicWorkRequest.Builder(DailyUpdateStatusResetWorker::class.java,5,TimeUnit.MINUTES)
//                    .build()

            val request = PeriodicWorkRequest.Builder(DailyUpdateStatusResetWorker::class.java,24,TimeUnit.HOURS)
                    .setInitialDelay(diff, TimeUnit.MILLISECONDS)
                    .build()
            WorkManager
                    .getInstance(context)
                    .enqueueUniquePeriodicWork(DAILY_UPDATE_STATUS_RESET_WORK,ExistingPeriodicWorkPolicy.REPLACE,request)
        }

        private fun getDiff(hour : String, minute : String): Long {
            val hourInt = hour.toInt()
            val minuteInt = minute.toInt()
            val calendar = Calendar.getInstance()
            val nowMillis = calendar.timeInMillis
            if (calendar[Calendar.HOUR_OF_DAY] > hourInt || calendar[Calendar.HOUR_OF_DAY] == hourInt && calendar[Calendar.MINUTE] + 1 >= minuteInt) {
                calendar.add(Calendar.DAY_OF_MONTH, 1)
            }
            calendar[Calendar.HOUR_OF_DAY] = hourInt
            calendar[Calendar.MINUTE] = minuteInt
            calendar[Calendar.SECOND] = 0
            calendar[Calendar.MILLISECOND] = 0
            val diff = calendar.timeInMillis - nowMillis
            return diff
        }
    }

}

class DailyUpdateActivateWorker(appContext: Context, workerParams: WorkerParameters):
        Worker(appContext, workerParams) {
    override fun doWork(): Result {

        if (getVariant()) {
            FeaturePrefManager.getInstance().setDailyBusinessRecap(true)
            FeaturePrefManager.getInstance().setDailyHighlightExplored(false)
            FeaturePrefManager.getInstance().isDailyHighlightSettingsVisible = true
        }
        FeaturePrefManager.getInstance().setDailyBusinessActivatedFlag(true)
        // Indicate whether the work finished successfully with the Result
        return Result.success()
    }
}

class DailyUpdateStatusResetWorker(appContext: Context, workerParams: WorkerParameters):
        Worker(appContext, workerParams) {
    override fun doWork(): Result {

        if(getVariant()){
            val businessList = BusinessRepository.getInstance(applicationContext).getBusinessListRaw(User.getUserId())
            for(business in businessList){
                business.isDailyBusinessUpdateSeen = 0
                BusinessRepository.getInstance(applicationContext).insertBookSync(business)
            }
        }

        // Indicate whether the work finished successfully with the Result
        return Result.success()
    }
}