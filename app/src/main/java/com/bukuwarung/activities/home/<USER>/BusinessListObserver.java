package com.bukuwarung.activities.home.data;

import android.os.Handler;
import android.view.View;

import androidx.core.widget.NestedScrollView;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.LayoutManager;

import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.database.entity.BookEntity;
import com.google.android.material.button.MaterialButton;

import java.util.Collection;
import java.util.List;

public final class BusinessListObserver<T> implements Observer<List<BookEntity>> {

    final MaterialButton btnCreateNewBusiness;
    final MainActivity activity;

    public BusinessListObserver(MainActivity mainActivity,MaterialButton btnCreateBusiness) {
        this.activity = mainActivity;
        this.btnCreateNewBusiness = btnCreateBusiness;
    }

    public final void onChanged(List<BookEntity> list) {
        MainActivity.getSideNavigationAdapter(this.activity).setBusinessHolderList(list);
        Collection collection = list;
        if (collection == null || collection.isEmpty()) {
            this.btnCreateNewBusiness.setVisibility(View.GONE);
            return;
        }
        this.btnCreateNewBusiness.setVisibility(View.VISIBLE);

        new Handler().postDelayed(new Runnable() {

            public final void run() {
                MainActivity mainActivity = activity;
                RecyclerView bookRv = MainActivity.getSideNavigationRV(mainActivity);
                LayoutManager layoutManager = bookRv.getLayoutManager();
                if (layoutManager.getChildAt(MainActivity.getSideNavigationAdapter(mainActivity).getSelectedBusinessPosition()) != null) {
                    try {
                        NestedScrollView nestedScrollView = (NestedScrollView) mainActivity.findViewById(R.id.scrollView);
                        View childAt = layoutManager.getChildAt(MainActivity.getSideNavigationAdapter(mainActivity).getSelectedBusinessPosition());
                        nestedScrollView.smoothScrollTo(0, (int) childAt.getY());
                    } catch (Exception e) {
                    }
                }
            }
        }, 200);
    }
}
