package com.bukuwarung.activities.homepage.view

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.kycupgrade.KycUpgradeActivity
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.HomepageNudgeLayoutBinding
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView

class HomepageNudgeActivity : BaseActivity() {

    private lateinit var binding: HomepageNudgeLayoutBinding
    private var from: String? = null

    companion object {

        private const val FROM = "from"
        private const val STATUS = "string"
        private const val NON_KYC = "NON_KYC"
        private const val ADVANCED = "ADVANCED"
        private const val SUPREME = "SUPREME"
        private const val TAGIH = "Tagih"
        private const val BAYAR = "Bayar"
        private const val QRIS = "QRIS"

        fun createIntent(origin: Context?, from: String, status: String): Intent {
            val intent = Intent(origin, HomepageNudgeActivity::class.java)
            intent.putExtra(FROM, from)
            intent.putExtra(STATUS, status)
            return intent
        }
    }

    override fun setViewBinding() {
        binding = HomepageNudgeLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
      // No imp
    }

    fun updateUI(status: String, from: String?) {
        with(binding) {
            nudgeBottomLayout.btnLater.setOnClickListener {
                finish()
            }
            if (status == NON_KYC) {
                nudgeBottomLayout.layoutKyb.root.hideView()
                nudgeBottomLayout.btnVerify.setOnClickListener {
                    startActivity(
                        WebviewActivity.createIntent(
                            this@HomepageNudgeActivity, RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl,
                            getString(R.string.qris_ktp_rejected)
                        )
                    )
                    val propBuilder = AppAnalytics.PropBuilder().apply {
                        put(AnalyticsConst.ENTRY_POINT2, "landing_page")
                        put("currently_kyc_or_kyb_step", "KYC")
                        put("kyc_tier", PaymentPrefManager.getInstance()
                            .getKycTier().toString())
                    }
                    AppAnalytics.trackEvent("kyc_verification_start", propBuilder)
                }

                val prop = AppAnalytics.PropBuilder().apply {
                    put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.HOME_PAGE)
                    put(
                        "benefit",
                        if (from == BAYAR) "payment_out" else if (from == TAGIH) "payment_in" else "qris"
                    )
                    put("current_kyc_or_kybstep", "KYC")
                    put(
                        "kyc_tier", PaymentPrefManager.getInstance()
                            .getKycTier().toString()
                    )
                }
                AppAnalytics.trackEvent("kyc_landing_page_view", prop)
            } else if (status == ADVANCED) {
                with(binding.nudgeBottomLayout) {
                    layoutKyc.root.showView()
                    layoutKyb.root.showView()
                    layoutKyc.root.background = layoutKyc.root.context?.let {
                        ContextCompat.getDrawable(it, R.drawable.bg_rounded_rectangle_green_5)
                    }
                    layoutKyc.ivPosition.setImageDrawable(
                        AppCompatResources.getDrawable(
                            layoutKyc.ivPosition.context,
                            R.drawable.ic_number_one_green
                        )
                    )
                    layoutKyc.ivTick.setImageDrawable(
                        AppCompatResources.getDrawable(
                            layoutKyc.ivPosition.context,
                            R.drawable.ic_tick_green
                        )
                    )
                    layoutKyc.tvSubtitle.hideView()
                    layoutKyb.ivPosition.setImageDrawable(
                        AppCompatResources.getDrawable(
                            layoutKyc.ivPosition.context,
                            R.drawable.ic_number_two_blue
                        ))
                    layoutKyb.ivAccountPremium.setImageDrawable(
                        AppCompatResources.getDrawable(
                            layoutKyb.ivPosition.context,
                            R.drawable.ic_kyc_prioritas
                        )
                    )
                    layoutKyb.tvTitle.text = "Upgrade Akun Prioritas"
                    layoutKyb.tvSubtitle.text = "Belum upgrade"
                    btnVerify.setOnClickListener {
                        startActivity(
                            WebviewActivity.createIntent(
                                this@HomepageNudgeActivity, RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl,
                                getString(R.string.qris_ktp_rejected)
                            )
                        )
                        val propBuilder = AppAnalytics.PropBuilder().apply {
                            put(AnalyticsConst.ENTRY_POINT2, "landing_page")
                            put("currently_kyc_or_kyb_step", "KYB")
                            put("kyc_tier", PaymentPrefManager.getInstance()
                                .getKycTier().toString())
                        }
                        AppAnalytics.trackEvent("kyc_verification_start", propBuilder)
                    }
                }

                val prop = AppAnalytics.PropBuilder().apply {
                    put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.HOME_PAGE)
                    put(
                        "benefit",
                        if (from == BAYAR) "payment_out" else if (from == TAGIH) "payment_in" else "qris"
                    )
                    put("currently_kyc_or_kyb_step", "KYB")
                    put(
                        "kyc_tier", PaymentPrefManager.getInstance()
                            .getKycTier().toString()
                    )
                }
                AppAnalytics.trackEvent("kyc_landing_page_view", prop)
            }
            if (from == BAYAR) {
                ivBanner.setImageDrawable(
                    AppCompatResources.getDrawable(
                        ivBanner.context, R.drawable.bayar_nudge_image
                    )
                )
                tvSubtitle.text = "Upgrade Akun, Nikmati Fiturnya"
                tvBodyText.text = "Atur komisimu tiap bantu pelanggan bayar ke bank manapun dengan biaya admin rendah dan limit hingga Rp1 Miliar/hari."
            } else if (from == TAGIH) {
                ivBanner.setImageDrawable(
                    AppCompatResources.getDrawable(
                        ivBanner.context, R.drawable.ic_tagih_nudge
                    )
                )
                tvSubtitle.text = "Tagih pembayaran secara digital"
                tvBodyText.text = "Kirim link tagihan dengan Virtual Account ke pelanggan supaya pelanggan bayar dari mana saja."
            } else {
                ivBanner.setImageDrawable(
                    AppCompatResources.getDrawable(
                        ivBanner.context, R.drawable.ic_qris_nudge
                    )
                )
                tvSubtitle.text = "Terima pembayaran pakai QRIS"
                tvBodyText.text = "Tanpa perlu uang tunai, pelanggan bayarnya tinggal scan aja."
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        var status = intent?.getStringExtra(STATUS)
        from = intent?.getStringExtra(FROM)
        if (status == NON_KYC) {
            status = NON_KYC
        } else if (status == ADVANCED) {
            status = ADVANCED
        } else {
            status = SUPREME
        }
        updateUI(status, from)
        with(binding) {
            tvLearnAboutBenefits.setOnClickListener {
                if (status == NON_KYC) {
                    val intent = KycUpgradeActivity.createIntent(
                        this@HomepageNudgeActivity,
                        "HomepageNudgeActivity"
                    )
                    startActivity(intent)
                    val propBuilder = AppAnalytics.PropBuilder().apply {
                        put("current_step", "KYC")
                        put("kyc_tier", PaymentPrefManager.getInstance()
                            .getKycTier().toString())
                    }
                    AppAnalytics.trackEvent("kyc:learn_kyc_benefits_clicked", propBuilder)
                } else if (status == ADVANCED) {
                    PaymentUtils.showKycKybStatusBottomSheet(supportFragmentManager, AnalyticsConst.HOME_PAGE)
                    val propBuilder = AppAnalytics.PropBuilder().apply {
                        put("current_step", "KYB")
                        put("kyc_tier", PaymentPrefManager.getInstance()
                            .getKycTier().toString())
                    }
                    AppAnalytics.trackEvent("kyc:learn_kyc_benefits_clicked", propBuilder)
                }
            }
        }

    }

    override fun subscribeState() {
        // No impl
    }

}