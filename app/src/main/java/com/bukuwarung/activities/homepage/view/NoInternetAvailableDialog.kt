package com.bukuwarung.activities.homepage.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.LayoutNoInternetHomeBinding
import com.bukuwarung.dialogs.base.BaseDialogFragment
import com.bukuwarung.session.User
import com.bukuwarung.utils.setSingleClickListener

class NoInternetAvailableDialog: BaseDialogFragment() {

    private lateinit var binding: LayoutNoInternetHomeBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding =  LayoutNoInternetHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    companion object {
        fun show(manager: FragmentManager) {
            NoInternetAvailableDialog().show(manager, "no-internet")
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val bookName = BusinessRepository.getInstance(requireContext())
            .getBusinessByIdSync(User.getBusinessId()).businessName
        with(binding) {
            tvToolbarTitle.text = bookName
            btTryAgain.setSingleClickListener {
                dismiss()
            }
            tbError.setNavigationOnClickListener {
                dismiss()
            }
        }
    }

}