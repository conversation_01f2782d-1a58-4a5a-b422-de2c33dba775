package com.bukuwarung.activities.homepage.view
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.experiments.CustomWebviewActivity
import com.bukuwarung.activities.profile.LoyaltyViewModel
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyAccount
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTier
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ViewLoyaltyBinding
import com.bukuwarung.payments.data.model.ReferralDataResponse
import com.bukuwarung.payments.data.model.SaldoResponse
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.subscribeSingleLiveEvent
import javax.inject.Inject

class HomeLoyaltyFragment: BaseFragment() {

    lateinit var homeLoyaltyBinding: ViewLoyaltyBinding

    @Inject
    lateinit var viewModel: LoyaltyViewModel

    companion object {

        fun createIntent(): HomeLoyaltyFragment {
            return HomeLoyaltyFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        homeLoyaltyBinding = ViewLoyaltyBinding.inflate(layoutInflater, container, false)
        return homeLoyaltyBinding.root
    }

    override fun onResume() {
        super.onResume()
        viewModel.onEventReceived(LoyaltyViewModel.Event.OnCreateView)
    }

    override fun setupView(view: View) {
        if(RemoteConfigUtils.shouldShowSaldoBonus()) {
            homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus.visibility = View.VISIBLE
            homeLoyaltyBinding.loyaltyWidget.visibility = View.GONE
            homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus.setTierClickListener {
                AppAnalytics.trackEvent("saldo_button_click",
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                val webViewIntent = WebviewActivity.createIntent(activity,
                    RemoteConfigUtils.getLoyaltyMembershipUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                startActivity(webViewIntent)
            }

            homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus.setOnPointClickListener {
                AppAnalytics.trackEvent("redeem_screen_open",
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                val webViewIntent = WebviewActivity.createIntent(activity,
                    RemoteConfigUtils.getLoyaltyPointHistoryUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                startActivity(webViewIntent)
            }

            homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus.setOnWidgetClickListener {
                if(Utility.hasInternet()){
                    AppAnalytics.trackEvent("loyalty_main_button_click",
                        AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                    val webViewIntent = WebviewActivity.createIntent(activity,
                        RemoteConfigUtils.getLoyaltyLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                    startActivity(webViewIntent)
                } else {
                    CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                        requireContext(),
                        requireActivity().supportFragmentManager
                    )
                }
            }
            homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus.setOnSaldoBonusClickListener {
                if (FeaturePrefManager.getInstance().showKomisiAgenDialog) {
                    val komisiAgenInfoDialog = KomisiAgenInfoDialog(requireContext())
                    Utilities.showDialogIfActivityAlive(requireActivity(), komisiAgenInfoDialog)
                } else {
                    AppAnalytics.trackEvent("redeem_screen_open",
                        AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                    val webViewIntent = WebviewActivity.createIntent(activity,
                        RemoteConfigUtils.getLoyaltyLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                    startActivity(webViewIntent)
                }
            }
            homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus.setOnReferralClickListerner {
                AppAnalytics.trackEvent("referral_main_button_click",
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.HomePage.HOMEPAGE))
                val webViewIntent = WebviewActivity.createIntent(activity,
                    RemoteConfigUtils.getReferralLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                startActivity(webViewIntent)
            }
        } else {
            homeLoyaltyBinding.loyaltyWidget.setTierClickListener {
                AppAnalytics.trackEvent("loyalty_button_click",
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                val webViewIntent = WebviewActivity.createIntent(activity,
                    RemoteConfigUtils.getLoyaltyMembershipUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                startActivity(webViewIntent)
            }

            homeLoyaltyBinding.loyaltyWidget.setOnPointClickListener {
                AppAnalytics.trackEvent("redeem_screen_open",
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                val webViewIntent = WebviewActivity.createIntent(activity,
                    RemoteConfigUtils.getLoyaltyPointHistoryUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                startActivity(webViewIntent)
            }

            homeLoyaltyBinding.loyaltyWidget.setOnWidgetClickListener {
                AppAnalytics.trackEvent("loyalty_main_button_click",
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                val webViewIntent = WebviewActivity.createIntent(activity,
                    RemoteConfigUtils.getLoyaltyLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                startActivity(webViewIntent)
            }
        }
    }

    override fun subscribeState() {

        subscribeSingleLiveEvent(viewModel.state) {
            when(it) {
                is LoyaltyViewModel.State.OnProfileTierLoaded -> {
                    handleLoyaltyData(it.loyaltyAccount, it.tier, it.isWhitelister)
                }

                is LoyaltyViewModel.State.OnSaldoBonusLoaded -> {
                    handleSaldoBonusData(it.saldoBonusResponse,it.loyaltyAccount, it.tier, it.isWhitelister)
                }
                is LoyaltyViewModel.State.OnReferralDataLoaded -> {
                    handleReferralData(it.referralDataResponse)
                }
            }
        }
    }

    private fun handleReferralData(referralDataResponse: ReferralDataResponse) {
        with(homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus) {
            updateReferralData(referralDataResponse)
        }
    }

    private fun handleLoyaltyData(loyaltyAccount: LoyaltyAccount, tier: LoyaltyTier, isWhiteListed: Boolean) {

        homeLoyaltyBinding.loyaltyWidget.apply {
            setPoint(loyaltyAccount)
            setTier(tier)
            visibility = isWhiteListed.asVisibility()
            AppAnalytics.setUserProperty("current_quarter_tier", tier.tierName)
//            AppAnalytics.setUserProperty("loyalty_points_earned", loyaltyAccount.activePoints.toString())
        }
    }

    private fun handleSaldoBonusData(saldoBonusResponse: SaldoResponse,loyaltyAccount: LoyaltyAccount, tier: LoyaltyTier, isWhiteListed: Boolean) {

        homeLoyaltyBinding.loyaltyWidgetWithSaldoBonus.apply {
            setPoint(saldoBonusResponse)
            if (RemoteConfigUtils.getLoyaltyWidgetType() == 0 || RemoteConfigUtils.getLoyaltyWidgetType() == 1 ||
                (RemoteConfigUtils.getLoyaltyWidgetType() == 2 && !RemoteConfigUtils.showReferralEntryPointHome())
            ) {
                setTier(tier)
            }
//            visibility = isWhiteListed.asVisibility()
//            AppAnalytics.setUserProperty("loyalty_enable", isWhiteListed.toString())
//            AppAnalytics.setUserProperty("current_quarter_tier", tier.tierName)
//            AppAnalytics.setUserProperty("loyalty_points_earned", loyaltyAccount.activePoints.toString())
        }
    }
}