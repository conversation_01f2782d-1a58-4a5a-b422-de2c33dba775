package com.bukuwarung.activities.homepage.view

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.experiments.CustomWebviewActivity
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.homepage.data.FragmentBlock
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.commonview.view.BukuTileView
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.BANNER_CLICK
import com.bukuwarung.constants.AnalyticsConst.BANNER_LOCATION
import com.bukuwarung.constants.AnalyticsConst.BANNER_NAME
import com.bukuwarung.constants.AnalyticsConst.EVENT_HOMEPAGE_BANNER_CLICK
import com.bukuwarung.constants.AnalyticsConst.HomePage.BANNER_ORDER
import com.bukuwarung.constants.AnalyticsConst.HomePage.BANNER_TYPE
import com.bukuwarung.constants.AnalyticsConst.HomePage.HOMEPAGE
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.databinding.LayoutHomePaymentsBannerBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.DateTimeUtilsKt.getTimestampFromDateString
import com.google.android.material.tabs.TabLayoutMediator
import java.util.*
import javax.inject.Inject

class HomePaymentsBannerFragment : BaseFragment(), Navigator {

    private lateinit var binding: LayoutHomePaymentsBannerBinding
    val handler = Handler()
    var runnable: Runnable? = null

    private var scrollStarted = false
    private var checkDirection: Boolean = false
    private val thresholdOffset = 0.5f

    private var fragmentData: FragmentBodyBlock? = null
    private var homePaymentsBannerAdapter: HomePaymentsBannerAdapter? = null
    private var hideReminderIcon = true
    private val versionCode = BuildConfig.VERSION_CODE
    private var isManualClick: Boolean = false

    @Inject
    lateinit var neuro: Neuro

    companion object {
        private const val HOME_PAYMENTS_BANNER_FRAGMENT = "home_payments_banner"
        private const val FRAGMENT_BLOCK = "fragment_block"

        fun createIntent(bukuTileContent: FragmentBodyBlock, fragmentBlock: FragmentBlock): HomePaymentsBannerFragment {
            val fragment = HomePaymentsBannerFragment()
            fragment.arguments = Bundle().apply {
                putParcelable(HOME_PAYMENTS_BANNER_FRAGMENT, bukuTileContent)
                putParcelable(FRAGMENT_BLOCK, fragmentBlock)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding =
            LayoutHomePaymentsBannerBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        fragmentData = arguments?.getParcelable(HOME_PAYMENTS_BANNER_FRAGMENT)
        val fragmentBlock = arguments?.getParcelable<FragmentBlock>(FRAGMENT_BLOCK)
        var finalItemList: List<BodyBlock?>
        fragmentData?.let {
            with(binding) {
                finalItemList = filterList(it)
                val shouldShowLendingBanner = AppConfigManager.getInstance().showLendingBanner()
                if (shouldShowLendingBanner == AppConst.TRUE) {
                    finalItemList = finalItemList.filter { !it!!.analytics_name!!.contains("lending", true) }.sortedBy { it?.rank }
                }
                if (!finalItemList.isNullOrEmpty()) {
                    vpPaymentBanner.isNestedScrollingEnabled = false
                    vpPaymentBanner.apply {
                        homePaymentsBannerAdapter = HomePaymentsBannerAdapter(
                            finalItemList,
                            fragmentBlock?.category!!,
                            requireContext()
                        ) { fragmentBody, category, position ->
                            run {
                                processRedirectAndAnalytics(fragmentBody, category, position)
                            }
                        }
                        vpPaymentBanner.adapter = homePaymentsBannerAdapter
                    }
                    TabLayoutMediator(
                        binding.tbPaymentBanner,
                        binding.vpPaymentBanner
                    ) { tab, position ->
                    }.attach()
                } else {
                    tbPaymentBanner.hideView()
                    vpPaymentBanner.hideView()
                }
            }
            binding.vpPaymentBanner.registerOnPageChangeCallback(object :
                ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    runnable = Runnable { binding.vpPaymentBanner.currentItem = (position + 1) % homePaymentsBannerAdapter!!.itemCount }
                    if (RemoteConfigUtils.NewHomePage.isAutoRotateHomepageBanner()) {
                        runnable?.let {
                            handler.postDelayed(it, RemoteConfigUtils.NewHomePage.autoRotateHomepageBannerDuration())
                        }
                    }
                }

                override fun onPageScrollStateChanged(state: Int) {
                    super.onPageScrollStateChanged(state)
                    if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                        handler.removeMessages(0)

                    }
                    if (!scrollStarted && state == ViewPager.SCROLL_STATE_DRAGGING) {
                        scrollStarted = true
                        checkDirection = true
                    } else {
                        scrollStarted = false
                    }
                }

                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                    if (checkDirection) {
                        isManualClick = true
                        checkDirection = false;
                    } else {
                        isManualClick = false
                    }
                }
            })
        }
        binding.vpPaymentBanner.offscreenPageLimit = 1
        val nextItemVisiblePx = resources.getDimension(R.dimen._8dp)
        val currentItemHorizontalMarginPx = resources.getDimension(R.dimen._26dp)
        val pageTranslationX = nextItemVisiblePx + currentItemHorizontalMarginPx
        val pageTransformer = ViewPager2.PageTransformer { page, position ->
            page.translationX = -pageTranslationX * position
        }
        binding.vpPaymentBanner.setPageTransformer(pageTransformer)
        context?.let {
            val itemDecoration = HorizontalMarginItemDecoration(
                it,
                R.dimen._6dp
            )
            binding.vpPaymentBanner.addItemDecoration(itemDecoration)
        }
        binding.vpPaymentBanner.apply {
            layoutParams.height = ((activity?.let { getScreenWidth(it) } ?: 0) * 0.22).toInt()
        }

    }

    fun filterList(fragmentData: FragmentBodyBlock): List<BodyBlock?> {
        val versionFilteredList =
            fragmentData.data!!.filter { it?.start_version!! <= versionCode && (it.end_version >= versionCode || it.end_version == -1) }
                .sortedBy { it?.rank }
        return if (hideReminderIcon) {
            versionFilteredList.filter {
                !it!!.analytics_name!!.contains(
                    BukuTileView.REMINDER,
                    true
                )
            }
            versionFilteredList.filter {
                if (it?.start_time.isNotNullOrEmpty() && it?.end_time.isNotNullOrEmpty()) {
                    System.currentTimeMillis() > getTimestampFromDateString(
                        it?.start_time.orEmpty(),
                        DateTimeUtilsKt.YYYY_MM_DD_T_HH_MM_SS
                    ) && System.currentTimeMillis() < getTimestampFromDateString(
                        it?.end_time.orEmpty(), DateTimeUtilsKt.YYYY_MM_DD_T_HH_MM_SS
                    )
                } else
                    true
            }
        } else {
            versionFilteredList
        }
    }

    private fun processRedirectAndAnalytics(fragmentBody: BodyBlock?, category: String, position: Int) {
        // Redirection/ deeplink/ flow
        val newRedirection = fragmentBody?.deeplinkAppNeuro.orEmpty()
        val redirection = fragmentBody?.deeplink_app.orEmpty()
        val deeplink = fragmentBody?.deeplink_web.orEmpty()
        when {
            newRedirection.isNotBlank() -> {
                redirect(newRedirection)
            }
            newRedirection.isBlank() && redirection.isNotBlank() -> {
                redirect(redirection)
            }
            deeplink.isNotBlank() -> {
                if (Utility.hasInternet()) {
                    if (fragmentBody?.analytics_name?.contains("reward")!!) {
                        val webViewIntent = CustomWebviewActivity.createIntent(
                            activity,
                            deeplink, "Integrasi Institusi Keuangan", false,
                            "loyalty_account", "homepage"
                        )
                        startActivity(webViewIntent)
                    } else {
                        val intent = WebviewActivity.createIntent(
                            requireActivity(), deeplink,
                            fragmentBody.display_name
                        )
                        intent.putExtra("from", "homepage")
                        startActivity(intent)
                    }
                } else {
                    NoInternetAvailableDialog.show(childFragmentManager)
                }
            }
        }
        if (fragmentBody != null) {
            handleAnalytics(fragmentBody, category, position)
        }
    }

    private fun redirect(redirection: String) {
        val sourceLink = SourceLink(context = requireContext(), link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = { redirectWithLegacyLink(redirection) },
        )
    }

    private fun redirectWithLegacyLink(redirection: String) {
        if (!redirection.startsWith("com.")) return

        val link = "$DEEPLINK_INTERNAL_URL?type=act&data=$redirection&from=home&launch=2"
        val sourceLink = SourceLink(context = requireContext(), link)

        neuro.route(sourceLink, navigator = this, onSuccess = {}, onFailure = {})
    }

    private fun handleAnalytics(fragmentBody: BodyBlock, category: String, position: Int) {
        val prop = AppAnalytics.PropBuilder()
        prop.put(BANNER_NAME, fragmentBody.display_name)
        prop.put(BANNER_TYPE, category)
        prop.put(BANNER_ORDER, (position + 1).toString())
        prop.put("banner_shown_automatically", isManualClick)
        AppAnalytics.trackEvent(EVENT_HOMEPAGE_BANNER_CLICK, prop)
    }

    override fun setupView(view: View) {
        // NOT REQUIRED
    }

    override fun subscribeState() {
        PaymentPrefManager.getInstance().reminderFlag.observe(viewLifecycleOwner) { showReminder ->
            if (showReminder) {
                hideReminderIcon = false
                fragmentData?.let {
                    val fragData = filterList(it)
                    homePaymentsBannerAdapter?.refreshData(fragData)
                }
            }
        }
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    override fun onDestroyView() {
        runnable?.let(handler::removeCallbacks)
        runnable = null
        super.onDestroyView()
    }
}