package com.bukuwarung.activities.homepage.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.profile.LoyaltyViewModel
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTierBenefit
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.databinding.FragmentSubscriptionBinding
import com.bukuwarung.game.model.GameStatus
import com.bukuwarung.game.viewmodel.GameViewModel
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.subscribeSingleLiveEvent
import javax.inject.Inject

class SubscriptionEntryPointFragment: BaseFragment() {

    lateinit var binding: FragmentSubscriptionBinding

    @Inject
    lateinit var viewModel: LoyaltyViewModel

    @Inject
    lateinit var gameViewModel: GameViewModel

    private var tierQuota = 0

    companion object {
        fun createIntent(): SubscriptionEntryPointFragment {
            return SubscriptionEntryPointFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentSubscriptionBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        viewModel.onEventReceived(LoyaltyViewModel.Event.OnCreateView)

        binding.clHomeSubscription.setSingleClickListener {
            startActivity(WebviewActivity.createIntent(requireActivity(), RemoteConfigUtils.getSubscriptionEntryPoint(), "Subscription"))
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is LoyaltyViewModel.State.OnSaldoBonusLoaded -> {
                    handleSaldoBonusData(it.loyaltyTierBenefit, it.isWhitelister)
                }
                else -> {}
            }
        }

        gameViewModel.gameData.observe(viewLifecycleOwner) {
            it?.let { gameData ->
                if (gameData.gameProgress == null || gameData.gameProgress.isEmpty()) {
                    binding.apply {
                        clSubscribed.hideView()
                        clSubscriptionTry.showView()
                    }
                } else {
                    binding.apply {
                        clSubscribed.showView()
                        clSubscriptionTry.hideView()
                    }
                    when (gameData.gameProgress[0]?.status) {
                        GameStatus.COMPLETED.status -> {
                            binding.tvSubscriptionDescription.text = "Kuota diskon fitur Bayar sudah terpakai. Yuk, berlangganan lagi!"
                            binding.pbSubscription.progress = 100
                        }

                        GameStatus.EXPIRED.status -> {
                            binding.tvSubscriptionDescription.text = "Langganan sudah kedaluwarsa. Yuk, langganan lagi"
                            gameData.gameProgress[0]?.apply {
                                binding.pbSubscription.progress =
                                    (tierQuota - remainingQuotaCount) * 100 / tierQuota
                            }
                            binding.pbSubscription.progressTintList = ContextCompat.getColorStateList(requireContext(), R.color.black40)
                        }

                        GameStatus.ACTIVE.status -> {
                            gameData.gameProgress[0]?.apply {
                                binding.tvSubscriptionDescription.text =
                                    "" + (tierQuota - remainingQuotaCount) + " dari " + tierQuota + " kuota diskon fitur Bayar sudah terpakai. Yuk, pakai terus!"
                            }

                            gameData.gameProgress[0]?.apply {
                                binding.pbSubscription.progress =
                                    (tierQuota - remainingQuotaCount) * 100 / tierQuota
                            }
                        }
                        else -> {}
                    }
                }
            }
        }
    }

    private fun handleSaldoBonusData(tierBenefit: List<LoyaltyTierBenefit>?, isWhiteListed: Boolean) {
        if (tierBenefit?.isNotEmpty() == true) {
            val gameName = tierBenefit[0].gameRuleName
            tierQuota = tierBenefit[0].quota

            if (gameName.isNotBlank()) {
                gameViewModel.getGameProgressData(gameName)
            }
        }
    }
}