package com.bukuwarung.activities.homepage.view

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.experiments.CustomWebviewActivity
import com.bukuwarung.activities.homepage.data.*
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.commonview.view.BukuTileView
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.HomePage.BANNER_TYPE
import com.bukuwarung.constants.AnalyticsConst.HomePage.HOMEPAGE
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.databinding.HomeLendingBannerBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.utils.Utility

import com.bukuwarung.utils.isNotNullOrEmpty
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable
import javax.inject.Inject

class HomeLendingBannerFragment : BaseFragment(), Navigator {
    @Inject
    lateinit var neuro: Neuro

    private lateinit var binding: HomeLendingBannerBinding

    companion object {
        private const val HOME_LENDING_BANNER_FRAGMENT = "home_lending_banner"
        private const val FRAGMENT_BLOCK = "fragment_block"

        fun createIntent(bukuTileContent: FragmentBodyBlock, fragmentBlock: FragmentBlock): HomeLendingBannerFragment {
            val fragment = HomeLendingBannerFragment()
            fragment.arguments = Bundle().apply {
                putParcelable(HOME_LENDING_BANNER_FRAGMENT, bukuTileContent)
                putParcelable(FRAGMENT_BLOCK, fragmentBlock)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = HomeLendingBannerBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val fragmentData = arguments?.getParcelable<FragmentBodyBlock>(HOME_LENDING_BANNER_FRAGMENT)
        val fragmentBlock = arguments?.getParcelable<FragmentBlock>(FRAGMENT_BLOCK)
        val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()

        val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }
        fragmentData?.let {
            with(binding) {
                tvGetLoan.text = it.title
                tvCooperate.text = it.subtitle
                context?.let { it1 ->
                    Glide.with(it1).load(it.data!!.getOrNull(0)?.icon).placeholder(shimmerDrawable).into(ivLendingBanner)
                }
                clListingItem.setOnClickListener { view ->

                    processRedirectAndAnalytics(it.data!!.getOrNull(0), fragmentBlock?.category!!)
                }
            }
        }
    }

    private fun processRedirectAndAnalytics(fragmentBody: BodyBlock?, category: String) {
        // Redirection/ deeplink/ flow

        val redirection = fragmentBody?.deeplink_app
        val deeplink = fragmentBody?.deeplink_web

        if (redirection.isNotNullOrEmpty()) {
            redirection?.let(::redirect)
        }
        if (deeplink.isNotNullOrEmpty())
        {
            if (Utility.hasInternet()) {
                deeplink?.let {
                    if (fragmentBody?.analytics_name?.contains("reward")!!) {
                        val webViewIntent = CustomWebviewActivity.createIntent(activity,
                                deeplink, "Integrasi Institusi Keuangan",false,
                                "loyalty_account","homepage")
                        startActivity(webViewIntent)
                    } else {
                        startActivity(
                                WebviewActivity.createIntent(
                                        requireActivity(), deeplink,
                                        fragmentBody.display_name
                                )
                        )
                    }
                }
            } else {
                NoInternetAvailableDialog.show(childFragmentManager)
            }
        }
        if (fragmentBody != null) {
            handleAnalytics(fragmentBody, category)
        }

    }

    private fun redirect(redirection: String) {
        val sourceLink = SourceLink(context = requireContext(), link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = { redirectWithLegacyLink(redirection) },
        )
    }

    private fun redirectWithLegacyLink(redirection: String) {
        if (!redirection.startsWith("com.")) return

        val link = "$DEEPLINK_INTERNAL_URL?type=act&data=$redirection&from=home"
        val sourceLink = SourceLink(context = requireContext(), link)

        neuro.route(sourceLink, navigator = this, onSuccess = {}, onFailure = {})
    }

    private fun handleAnalytics(fragmentBody: BodyBlock, category: String) {
        val prop = AppAnalytics.PropBuilder()
        prop.put(AnalyticsConst.BANNER_NAME, fragmentBody.display_name)
        prop.put(BANNER_TYPE, category)
        prop.put(AnalyticsConst.BANNER_LOCATION, HOMEPAGE)
        AppAnalytics.trackEvent(AnalyticsConst.BANNER_CLICK, prop)
    }

    override fun setupView(view: View) {
        //
    }

    override fun subscribeState() {
        //
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }
}