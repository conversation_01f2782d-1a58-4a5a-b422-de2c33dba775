package com.bukuwarung.activities.homepage.di

import com.bukuwarung.activities.homepage.view.*
import com.bukuwarung.commonview.view.BukuTileView
import dagger.Module
import dagger.android.ContributesAndroidInjector

@Module
abstract class HomePageDI {

    @ContributesAndroidInjector
    abstract fun contributeHomePage(): HomeFragment

    @ContributesAndroidInjector
    abstract fun contributeBukTileView(): BukuTileView

    @ContributesAndroidInjector
    abstract fun contributeHomeTopBlueFragment(): HomeTopBlueFragment

    @ContributesAndroidInjector
    abstract fun contributeSaldoFragment(): HomeSaldoFragment

    @ContributesAndroidInjector
    abstract fun contributeHomeLoyaltyFragment(): HomeLoyaltyFragment

    @ContributesAndroidInjector
    abstract fun contributeHomeBnplFragment(): HomeBnplFragment

    @ContributesAndroidInjector
    abstract fun contributeHomeTickerFragment(): HomeTickerFragment

    @ContributesAndroidInjector
    abstract fun contributeHomeOnBoardingTitleCampaignFragment():HomeOnBoardingCampaignWidgetFragment

    @ContributesAndroidInjector
    abstract fun contributeHomepageNudgeActivity(): HomepageNudgeActivity
}