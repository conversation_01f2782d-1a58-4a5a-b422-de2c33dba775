package com.bukuwarung.activities.homepage.data

import com.google.gson.annotations.SerializedName

data class TickerFragmentData(

	@SerializedName("start_time")
	val startTime: String? = null,

	@SerializedName("end_time")
	val endTime: String? = null,

	@SerializedName("ticker_header")
	val tickerHeader: String? = null,

	@SerializedName("ticker_body")
	val tickerBody: String? = null,

	@SerializedName("frequency")
	val frequency: Double? = null
)
