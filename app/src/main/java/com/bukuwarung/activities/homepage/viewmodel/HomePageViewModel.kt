package com.bukuwarung.activities.homepage.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.wrapper.EventWrapper
import javax.inject.Inject

class HomePageViewModel: BaseViewModel() {

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state

    @Inject
    lateinit var viewModel: HomeTopFragmentViewModel


    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }

    sealed class State {
        data class ShowNextCoachmark(val nextSection: Int?) : State()
        data class MoveToSection(val nextSection: Int?): State()
        object HideFloatingButton: State()
        data class OnFloatingButtonClick(val redirectionUrl: String): State()
    }

    sealed class Event {
        data class OnNextCoachmarkClick(val nextSection: Int?): Event()
        object OnFloatingButtonHide: Event()
        data class OnFloatingButtonClick(val redirectionUrl: String): Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnNextCoachmarkClick -> resolveNextCoachmarkSection(event.nextSection)
            Event.OnFloatingButtonHide -> hideFloatingButton()
            is Event.OnFloatingButtonClick -> onFloatingButtonClick(event.redirectionUrl)
        }
    }

    private fun resolveNextCoachmarkSection(nextSection: Int?) {
        setState(State.ShowNextCoachmark(nextSection))
        setState(State.MoveToSection(nextSection))
    }

    private fun hideFloatingButton() {
        setState(State.HideFloatingButton)
    }

    private fun onFloatingButtonClick(redirectionUrl: String) {
        setState(State.OnFloatingButtonClick(redirectionUrl))
    }
}