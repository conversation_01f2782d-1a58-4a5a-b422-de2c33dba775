package com.bukuwarung.activities.homepage.data

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class BodyBlock(
    val analytics_name: String?,
    val coming_soon: Boolean,
    val deeplink_app: String?,
    val deeplink_web: String?,
    val display_name: String?,
    val end_version: Int,
    val icon: String?,
    val is_new: Boolean?,
    val start_time: String?,
    val end_time: String?,
    val is_new_end_time: String?,
    val is_promo: Boolean?,
    val rank: Int?,
    val start_version: Int,
    val redirection_handler: String?,
    val deeplinkAppNeuro: String?,
    val ppobCategoryName: String?,
    val is_available: Boolean,
    val check_kyc: Boolean?
): Parcelable