package com.bukuwarung.activities.homepage.view

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.BottomsheetPpobPulsaBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.session.SessionManager

class PpobPulsaBottomSheet: BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "loginSheet"
        private const val ENTRY_POINT = "ENTRY_POINT"
        private const val MERCHANT_TYPE = "MERCHANT_TYPE"
        fun newInstance(entryPoint: String?, merchantType: String?): PpobPulsaBottomSheet {
            val dialog = PpobPulsaBottomSheet()
            val bundle = Bundle()
            bundle.putString(ENTRY_POINT, entryPoint)
            bundle.putString(MERCHANT_TYPE, merchantType)
            dialog.arguments = bundle
            return dialog
        }
    }

    lateinit var binding: BottomsheetPpobPulsaBinding

    private var merchantType: String? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = BottomsheetPpobPulsaBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)

        if (!merchantType.equals("PPOB", true)) {
            SessionManager.getInstance().setPpobPulsaBottomSheetSeen()
        } else {
            SessionManager.getInstance().setPpobListrikBottomSheetSeen()
        }

        val prop =  AppAnalytics.PropBuilder()
        prop.put(AnalyticsConst.CROSS_SELL_CHOICE, "try")
        AppAnalytics.trackEvent(AnalyticsConst.CROSS_SELL_CTA_CLICK, prop)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val title = view.findViewById<TextView>(R.id.title)
        val desc1 = view.findViewById<TextView>(R.id.txt_desc_1)
        val content1 = view.findViewById<TextView>(R.id.tv_layani_b)
        val desc2 = view.findViewById<TextView>(R.id.txt_desc_2)
        val content2 = view.findViewById<TextView>(R.id.tv_dapatkan_b)
        val desc3 = view.findViewById<TextView>(R.id.txt_desc_3)
        val content3 = view.findViewById<TextView>(R.id.tv_maksimalin_b)
        val desc4 = view.findViewById<TextView>(R.id.txt_desc_4)
        val content4 = view.findViewById<TextView>(R.id.tv_otomatis_b)

        merchantType = arguments?.getString(MERCHANT_TYPE)

        if (merchantType.equals("PPOB", true)) {
            title.text = getString(R.string.ppob_listrik_bottomsheet_title)
            desc1.text = getString(R.string.ppob_listrik_bottomsheet_layani_h)
            desc2.text = getString(R.string.ppob_listrik_bottomsheet_dapatkan_h)
            desc3.text = getString(R.string.ppob_listrik_maksimalin_h)
            content1.text = getString(R.string.ppob_listrik_bottomsheet_layani_b)
            content2.text = getString(R.string.ppob_listrik_bottomsheet_dapatkan_b)
            content3.text = getString(R.string.ppob_listrik_maksimalin_b)
            desc4.text = getString(R.string.ppob_listrik_otomatis_h)
            content4.text = getString(R.string.ppob_listrik_otomatis_b)

        } else {
            title.text = getString(R.string.ppob_pulsa_bottomsheet_title)
            desc1.text = getString(R.string.ppob_pulsa_bottomsheet_layani_h)
            desc2.text = getString(R.string.ppob_pulsa_bottomsheet_dapatkan_h)
            desc3.text = getString(R.string.ppob_pulsa_maksimalin_h)
            content1.text = getString(R.string.ppob_pulsa_bottomsheet_layani_b)
            content2.text = getString(R.string.ppob_pulsa_bottomsheet_dapatkan_b)
            content3.text = getString(R.string.ppob_pulsa_maksimalin_b)
            desc4.text = getString(R.string.ppob_pulsa_otomatis_h)
            content4.text = getString(R.string.ppob_pulsa_otomatis_b)
        }

        view.findViewById<View>(R.id.btn_allow).setOnClickListener {
            var link = "https://bukuwarung.com/fitur-pembayaran/jual-pulsa/"
            var webTitle = "Pulsa Tutorial"

            if (merchantType.equals("PAYMENT_AGENT", true)) {
                link = "https://bukuwarung.com/yuk-jualan-pulsa-1/"
            } else if (merchantType.equals("FMCG", true)) {
                link = "https://bukuwarung.com/yuk-jualan-pulsa-3/"
            } else if (merchantType.equals("PPOB", true)) {
                link = "https://bukuwarung.com/yuk-jualan-token-listrik/"
            }

            if (merchantType.equals("PPOB", true)) {
                webTitle = "Listrik Tutorial"
                SessionManager.getInstance().setPpobListrikBottomSheetSeenCount()
            } else {
                SessionManager.getInstance().setPpobPulsaBottomSheetSeenCount()
            }

            activity?.startActivity(Intent(WebviewActivity.createIntent(activity, link, webTitle)))
            dismiss()
        }

        view.findViewById<View>(R.id.btn_reject).setOnClickListener {
            val p: AppAnalytics.PropBuilder
            if (!merchantType.equals("PPOB", true)) {
                p =  AppAnalytics.PropBuilder()
                p.put(AnalyticsConst.CROSS_SELL_PITCH, "ppob_pulsa")
                AppAnalytics.trackEvent(AnalyticsConst.CROSS_SELL_TUTORIAL_CLICK, p)
                PpobUtils.getPpobCategoryActivityIntent(
                    fragmentManager = childFragmentManager,
                    context = requireContext(),
                    category = PpobConst.CATEGORY_PULSA
                )?.let { context?.startActivity(it) }
                SessionManager.getInstance().setPpobPulsaBottomSheetSeenCount()
            } else {
                p =  AppAnalytics.PropBuilder()
                p.put(AnalyticsConst.CROSS_SELL_PITCH, "ppob_listrik")
                AppAnalytics.trackEvent(AnalyticsConst.CROSS_SELL_TUTORIAL_CLICK, p)
                PpobUtils.getPpobCategoryActivityIntent(
                    fragmentManager = childFragmentManager,
                    context = requireContext(),
                    category = PpobConst.CATEGORY_LISTRIK,
                    from = AnalyticsConst.HOME_PAGE
                )?.let { context?.startActivity(it) }
                SessionManager.getInstance().setPpobListrikBottomSheetSeenCount()
            }

            val prop =  AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.CROSS_SELL_CHOICE, "try")
            AppAnalytics.trackEvent(AnalyticsConst.CROSS_SELL_CTA_CLICK, prop)
            dismiss()
        }
    }
}