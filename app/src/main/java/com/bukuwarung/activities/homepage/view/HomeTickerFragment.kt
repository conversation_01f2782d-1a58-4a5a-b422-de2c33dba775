package com.bukuwarung.activities.homepage.view

import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.activities.homepage.data.TickerFragmentData
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.databinding.FragmentHomeTickerBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.*
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import java.text.SimpleDateFormat
import java.util.*

class HomeTickerFragment: BaseFragment() {

    private lateinit var binding: FragmentHomeTickerBinding

    companion object {
        fun createIntent(): HomeTickerFragment {
            return HomeTickerFragment()
        }
    }

    override fun onResume() {
        super.onResume()
//        CleverTapAPI.getDefaultInstance(activity)?.setDisplayUnitListener(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentHomeTickerBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {

        val tickerDataBlock = RemoteConfigUtils.getTickerFragmentData()
        val type = object : TypeToken<TickerFragmentData>() {}.type
        val gson: Gson = GsonBuilder().create()

        val tickerData: TickerFragmentData = gson.fromJson(tickerDataBlock, type)

        val hours: Double = 24/tickerData.frequency.orNil

        val tickerDataInterval: Long = hours.times(60).times(60).times(1000).toLong()


        if ((System.currentTimeMillis() - FeaturePrefManager.getInstance().tickerCloseTime > tickerDataInterval) && System.currentTimeMillis() > getTime(tickerData.startTime) && System.currentTimeMillis() < getTime(tickerData.endTime)) {
            binding.tvTickerHeader.apply {
                setOnTouchListener { view, motionEvent ->
                    if (motionEvent.action == MotionEvent.ACTION_UP) {
                        if (motionEvent.rawX >= right - totalPaddingRight) {
                            FeaturePrefManager.getInstance().setTickerCloseTime()
                            removeFragment()
                        }
                        true
                    }
                    true;
                }
            }

            val header = tickerData.tickerHeader
            val bodyContent = tickerData.tickerBody

            if (header.isNullOrEmpty()) {
                removeFragment()
                return
            }

            binding.apply {
                tvTickerHeader.textHTML(header)
                tvTickerBody.textHTML(bodyContent)
                tvTickerBody.movementMethod = LinkMovementMethod.getInstance()
            }
        } else {
            removeFragment()
        }

    }

    override fun subscribeState() {

    }

    private fun removeFragment() {
        val manager = requireActivity().supportFragmentManager
        manager.beginTransaction().remove(this@HomeTickerFragment).commit()
    }

    private fun getTime(date: String?) : Long {
        if (date == null) {
            return 0
        }
        val utcFormat = SimpleDateFormat(DateTimeUtilsKt.YYYY_MM_DD_T_HH_MM_SS, Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone("UTC")

        return utcFormat.parse(date).time
    }

}