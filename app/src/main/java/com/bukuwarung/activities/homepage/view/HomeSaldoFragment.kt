package com.bukuwarung.activities.homepage.view

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.payment.PaymentTabViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.ViewSaldoBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.bottomsheet.SaldoTutorialBottomSheet
import com.bukuwarung.payments.deeplink.handler.SaldoSignalHandler
import com.bukuwarung.payments.history.OrderHistoryActivity
import com.bukuwarung.payments.saldo.TopupSaldoActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import com.google.firebase.crashlytics.FirebaseCrashlytics
import javax.inject.Inject


class HomeSaldoFragment : BaseFragment(), Navigator,
    SaldoTutorialBottomSheet.SaldoTutorialListener {

    lateinit var homeSaldoBinding: ViewSaldoBinding

    @Inject
    lateinit var viewModel: PaymentTabViewModel

    @Inject
    lateinit var neuro: Neuro

    companion object {
        private const val HOME_SALDO_FRAGMENT = "home_saldo"
        private const val FRAGMENT_BLOCK = "fragment_block"

        fun createIntent(): HomeSaldoFragment {
            return HomeSaldoFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        homeSaldoBinding = ViewSaldoBinding.inflate(layoutInflater, container, false)
        return homeSaldoBinding.root
    }

    override fun setupView(view: View) {
        viewModel.checkSaldoBalance()

        with(homeSaldoBinding) {
            btnSaldoTopup.setOnClickListener {
                openSaldoTopup()
            }

            ivSaldoInfo.setOnClickListener {
                val props = AppAnalytics.PropBuilder()
                props.put(AnalyticsConst.ENTRY_POINT_NEW, AnalyticsConst.HomePage.HOMEPAGE)
                props.put(AnalyticsConst.HomePage.INFO, AnalyticsConst.SALDO)
                props.put(AnalyticsConst.SOURCE, AnalyticsConst.HomePage.HOMEPAGE)
                AppAnalytics.trackEvent(AnalyticsConst.HomePage.EVENT_INFO_CLICK, props)
                SaldoTutorialBottomSheet.createInstance()
                    .show(childFragmentManager, SaldoTutorialBottomSheet.TAG)
            }

            ivSaldoWallet.setOnClickListener {
                openSaldoHistory()
            }
            tvAmount.setOnClickListener {
                openSaldoHistory()
            }
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.saldoState) { state ->
            when (state) {
                is PaymentTabViewModel.State.SaldoState -> {
                    state.saldoData?.let {
                        val balance = it.subBalance?.saldo.ifNull { it.amount }
                        showSaldoAmount(balance)

                        if (balance.orNil == 0.0 && it.subBalance?.cashback.orNil == 0.0) {
                            homeSaldoBinding.tvSubheading.text =
                                getString(R.string.make_credit_and_sales)
                            homeSaldoBinding.tvSubheading.setOnClickListener { openSaldoTopup() }
                        } else {
                            homeSaldoBinding.tvSubheading.text = getString(
                                R.string.saldo_bonus_x, Utility.formatAmount(it.subBalance?.cashback)
                            )
                            homeSaldoBinding.tvSubheading.setOnClickListener { openCashbackHistory() }
                        }
                    }
                }
                else -> {}
            }
        }
    }

    private fun openSaldoTopup() {
        val prop = AppAnalytics.PropBuilder()
        prop.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
        prop.put(AnalyticsConst.WALLET, AnalyticsConst.SALDO)
        prop.put(AnalyticsConst.SOURCE, AnalyticsConst.HomePage.HOMEPAGE_INFO_BOTTOMSHEET)

        AppAnalytics.trackEvent(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, prop)
        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, "wallet_top_up_home_saldo")

        val sourceLink = SourceLink(
            context = requireContext(),
            "${SaldoSignalHandler.saldoLink}?${AnalyticsConst.ENTRY_POINT}=${AnalyticsConst.HOME_PAGE}"
        )
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = {
                if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_SALDO_IN)) {
                    PaymentUtils.showKycKybStatusBottomSheet(childFragmentManager, AnalyticsConst.HOME_PAGE)
                } else {
                    val intent = Intent(requireContext(), TopupSaldoActivity::class.java)
                    startActivity(intent)
                }
                FirebaseCrashlytics.getInstance().recordException(it)
            },
        )
    }

    private fun openCashbackHistory() {
        startActivity(
            OrderHistoryActivity.createIntent(
                requireContext(),
                SessionManager.getInstance().businessId,
                PaymentConst.HISTORY_TABS.SALDOBONUS
            )
        )
    }

    private fun openSaldoHistory() {
        startActivity(
            OrderHistoryActivity.createIntent(
                requireContext(),
                SessionManager.getInstance().businessId,
                PaymentConst.HISTORY_TABS.SALDO
            )
        )
    }

    private fun showSaldoAmount(amount: Double?) {
        homeSaldoBinding.tvAmount.text = Utility.formatAmount(amount)
    }

    override fun tutorialTopupSaldoClicked() {
        openSaldoTopup()
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }
}