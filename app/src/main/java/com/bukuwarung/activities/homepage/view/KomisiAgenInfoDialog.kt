package com.bukuwarung.activities.homepage.view

import android.content.Context
import android.os.Bundle
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.databinding.DialogKomisiAgenInfoBinding
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.openActivity
import com.bukuwarung.utils.singleClick

class KomisiAgenInfoDialog(
    context: Context
): BaseDialog(context, BaseDialogType.POPUP_ROUND_CORNERED) {

    private val binding by lazy {
        DialogKomisiAgenInfoBinding.inflate(layoutInflater).also { setupViewBinding(it.root) }
    }

    init {
        setUseFullWidth(false)
        setCancellable(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)

        FeaturePrefManager.getInstance().showKomisiAgenDialog = false
        binding.tvTermsAndConditions.singleClick{
            dismiss()
            openWebView(RemoteConfigUtils.getKomisiAgenTermsAndConditionsUrl())
        }
        binding.btnUnderstand.singleClick{
            dismiss()
            openWebView(RemoteConfigUtils.getKomisiAgenDashboardUrl()+"?entryPoint=BUKUWARUNG")
        }
    }

    override fun getResId(): Int {
        return 0
    }

    private fun openWebView(redirectionUrl: String){
        context.openActivity(WebviewActivity::class.java) {
            putString(BaseWebviewActivity.LINK, redirectionUrl)
        }
    }
}