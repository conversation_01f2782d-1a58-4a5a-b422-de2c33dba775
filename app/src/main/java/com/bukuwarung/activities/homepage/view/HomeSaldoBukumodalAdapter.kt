package com.bukuwarung.activities.homepage.view

import android.animation.ValueAnimator
import android.os.Handler
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.core.animation.doOnPause
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.ItemBukumodalEntryPointBinding
import com.bukuwarung.utils.*

class HomeSaldoBukumodalAdapter(
    val itemHeight: Int,
    val getOnClickItem: (Int) -> Unit

): RecyclerView.Adapter<HomeSaldoBukumodalAdapter.HomeSaldoBukumodalViewHolder>() {

    private var isSaldoDataFetched = false
    private var isBnplRegistered = false
    private var isBnplNotRegistered = false
    private var isSaldoErrorState = false
    private var saldoAmount: Double? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): HomeSaldoBukumodalViewHolder {
        return HomeSaldoBukumodalViewHolder(
            ItemBukumodalEntryPointBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        ).listen { position, type ->
            getOnClickItem(position)
        }
    }

    override fun onBindViewHolder(holder: HomeSaldoBukumodalViewHolder, position: Int) {
        holder.bind(position)
    }

    override fun getItemCount(): Int {
        return if (RemoteConfigUtils.getPaymentConfigs().showBnplEntrypoint) 2 else 1
    }

    fun updateUi() {
        isSaldoDataFetched = true
        notifyDataSetChanged()
    }

    fun updateIsBnplRegistered(amount: Double) {
        isBnplRegistered = true
        saldoAmount = amount
        notifyDataSetChanged()
    }

    fun updateIsBnplNotRegistered() {
        isBnplNotRegistered = true
        notifyDataSetChanged()
    }

    fun updateSaldoErrorState() {
        isSaldoErrorState = true
        notifyDataSetChanged()
    }

    inner class HomeSaldoBukumodalViewHolder(private val binding: ItemBukumodalEntryPointBinding): RecyclerView.ViewHolder(binding.root) {
        val context = binding.root.context
        fun bind(position: Int) {
            with(binding) {
                clCard.layoutParams.height = itemHeight
                if (position == 0) {
                    ivIsiSaldo.hideView()
                    slLayout.stopShimmer()
                    shimmerLayout.clBody.hideView()
                    clCard.showView()
                    ivIcon.setImageResource(R.drawable.ic_bnpl_entry)
                    tvItemTitle.text = context.getString(R.string.buku_modal_entry)
                    tvBlueFont.text = context.getString(R.string.learn_and_apply)
                } else {
                    ivIsiSaldo.hideView()
                    slLayout.startShimmer()
                    clCard.hideView()
                    if (isBnplRegistered) {
                        slLayout.stopShimmer()
                        shimmerLayout.clBody.hideView()
                        clCard.showView()
                        tvItemTitle.text = context.getString(R.string.saldo_talangin_dulu)
                        ivIcon.setImageResource(R.drawable.ic_bnpl)
                        tvBlackFont.showView()
                        tvBlackFont.text = Utility.formatAmount(saldoAmount)
                        tvBlueFont.hideView()
                        tvFailureText.hideView()
                        ivIsiSaldo.hideView()
                    }
                    if (isBnplNotRegistered) {
                        slLayout.stopShimmer()
                        shimmerLayout.clBody.hideView()
                        clCard.showView()
                        val animatorBnpl = ValueAnimator.ofFloat(0.0f, 1.0f)
                        tvItemTitle.text = context.getString(R.string.saldo_talangin_dulu)
                        ivIcon.setImageResource(R.drawable.ic_bnpl)
                        ivIsiSaldo.hideView()
                        animatorBnpl.repeatMode = ValueAnimator.REVERSE
                        animatorBnpl.repeatCount = ValueAnimator.INFINITE
                        animatorBnpl.interpolator = LinearInterpolator()
                        animatorBnpl.duration = AppConst.ANIMATION_TIME
                        tvBlueFont.hideView()
                        tvBlackFont.text = context.getString(R.string.want_capital)
                        animatorBnpl.addUpdateListener { animation ->
                            val progress = (animation.animatedValue as Float)
                            val height = tvBlackFont.height
                            val translationY = height * progress
                            if (animation.currentPlayTime % animation.duration / 12 == 0L) {
                                animation.pause()
                            }
                            tvBlackFont.translationY = translationY
                            tvBlueFont.translationY = translationY - height
                        }
                        animatorBnpl.doOnPause {
                            Handler().postDelayed({
                                animatorBnpl.resume()
                                tvBlueFont.showView()
                            }, AppConst.ANIMATION_STOP_TIME)
                        }
                        tvBlueFont.text = context.getString(R.string.daftar_sekarang)
                        animatorBnpl.start()
                    }
                    if (isSaldoErrorState) {
                        slLayout.stopShimmer()
                        shimmerLayout.clBody.hideView()
                        clCard.showView()
                        tvItemTitle.text = context.getString(R.string.saldo_talangin_dulu)
                        ivIcon.setImageResource(R.drawable.ic_bnpl)
                        tvBlackFont.hideView()
                        tvBlueFont.hideView()
                        tvFailureText.showView()
                        ivIsiSaldo.hideView()
                    }
                }
            }
        }
    }

}