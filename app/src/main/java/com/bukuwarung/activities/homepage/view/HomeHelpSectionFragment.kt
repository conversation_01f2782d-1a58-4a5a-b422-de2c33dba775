package com.bukuwarung.activities.homepage.view

import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.homepage.data.FragmentBlock
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT
import com.bukuwarung.constants.AnalyticsConst.EVENT_CUSTOMER_SUPPORT_REQUESTED
import com.bukuwarung.constants.AnalyticsConst.HomePage.HOMEPAGE
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.databinding.HomeHelpSectionLayoutBinding
import com.bukuwarung.dialogs.HelpDialog
import com.bukuwarung.utils.PermissonUtil
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.WhatsAppUtils

class HomeHelpSectionFragment: BaseFragment() {

    private lateinit var binding: HomeHelpSectionLayoutBinding

    companion object {
        private const val HOME_HELP_SECTION_FRAGMENT = "home_help_section"
        private const val FRAGMENT_BLOCK = "fragment_block"

        fun createIntent(bukuTileContent: FragmentBodyBlock, fragmentBlock: FragmentBlock): HomeHelpSectionFragment {
            val fragment = HomeHelpSectionFragment()
            fragment.arguments = Bundle().apply {
                putParcelable(HOME_HELP_SECTION_FRAGMENT, bukuTileContent)
                putParcelable(FRAGMENT_BLOCK, fragmentBlock)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = HomeHelpSectionLayoutBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val fragmentData = arguments?.getParcelable<FragmentBodyBlock>(HOME_HELP_SECTION_FRAGMENT)
        val fragmentBlock = arguments?.getParcelable<FragmentBlock>(FRAGMENT_BLOCK)
        fragmentData?.let {
            with(binding) {
                tvHeadingFirst.text = it.title
                tvHeadingHelp.text = it.subtitle
                tvHeadingHelp.setOnClickListener { _ ->
                    handleAnanlytics()
//                    WhatsAppUtils.openWABotWithoutProp(context)
                    HelpDialog(requireContext()).show()
                }
                ivHelp.setOnClickListener { _ ->
                    handleAnanlytics()
//                    WhatsAppUtils.openWABotWithoutProp(context)
                    HelpDialog(requireContext()).show()
                }
            }
        }
    }

    private fun handleAnanlytics() {
      val prop = AppAnalytics.PropBuilder()
      prop.put(ENTRY_POINT, HOMEPAGE)
      AppAnalytics.trackEvent(EVENT_CUSTOMER_SUPPORT_REQUESTED, prop)
    }

    override fun setupView(view: View) {
        // NO IMP
    }

    override fun subscribeState() {
        // NO IMP
    }

}