package com.bukuwarung.activities.homepage.data

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FragmentData(
    @SerializedName("title") val title : String?,
    @SerializedName("startImage") val startImage : String?,
    @SerializedName("subtitle") val subtitle : String?,
    @SerializedName("endImage") val endImage : String?,
    @SerializedName("type") val type : Int?,
    @SerializedName("position") val position : Int?,
    @SerializedName("isActive") val isActive : Boolean?,
    @SerializedName("isEnabled") val isEnabled : Boolean?,
    @SerializedName("isPaymentTile") val isPaymentTile: Boolean?,
    @SerializedName("category") val category: String?,
    @SerializedName("body") val body : List<FragmentBody>?,
    @SerializedName("coachmarkNext") val nextCoachMark: Int?,
    @SerializedName("step") val stepNumber: Int?,
    @SerializedName("maxSteps") val maxSteps: Int?,
    @SerializedName("coachMarkHead") val coachmarkHead: String?,
    @SerializedName("coachmarkBody") val coachmarkbody: String?,
    @SerializedName("startVersion") val startVersion: Int,
    @SerializedName("endVersion") val endVersion: Int
): Parcelable
