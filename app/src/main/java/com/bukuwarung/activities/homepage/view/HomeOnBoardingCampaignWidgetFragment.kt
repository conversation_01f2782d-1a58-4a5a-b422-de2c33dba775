package com.bukuwarung.activities.homepage.view

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.FragmentHomeOnboardingCampaignTileBinding
import com.bukuwarung.utils.DateTimeUtils

class HomeOnBoardingCampaignWidgetFragment : BaseFragment() {

    companion object {
        private const val FRAGMENT_BLOCK = "fragment_block"
        fun createIntent(bodyContent: FragmentBodyBlock?): HomeOnBoardingCampaignWidgetFragment {
            val fragment = HomeOnBoardingCampaignWidgetFragment()
            fragment.arguments = Bundle().apply {
                putParcelable(FRAGMENT_BLOCK, bodyContent)
            }
            return fragment
        }
    }

    private lateinit var binding: FragmentHomeOnboardingCampaignTileBinding
    private var campaignStatus = ""
    private var timer: CountDownTimer? = null
    private var fragmentBlock: FragmentBodyBlock? = null

    override fun setupView(view: View) {
        //not required
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding =
            FragmentHomeOnboardingCampaignTileBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        fragmentBlock = arguments?.getParcelable<FragmentBodyBlock>(FRAGMENT_BLOCK)
        with(binding) {
            tvOnboardingCampaignTitle.text = fragmentBlock?.title.orEmpty()
            tvOnboardingCampaignBody.text = fragmentBlock?.subtitle.orEmpty()
            btJoinCampaign.setOnClickListener {
                val prop = AppAnalytics.PropBuilder()
                prop.put(AnalyticsConst.CAMPAIGN_NAME, fragmentBlock?.data?.get(0)?.analytics_name)
                prop.put(AnalyticsConst.STATUS, campaignStatus)
                AppAnalytics.trackEvent(AnalyticsConst.HOMEPAGE_CAMPAIGN_CLICK, prop)
                startActivity(
                    WebviewActivity.createIntent(
                        requireActivity(),
                        fragmentBlock?.data?.get(0)?.deeplink_web,
                        ""
                    )
                )
            }
        }
    }

    fun getTimerAndGameStatus(endTime: String, gameStatus: String) {
        campaignStatus = gameStatus
        with(binding) {
            if (gameStatus == GameStatus.IN_PROGRESS.name || gameStatus == GameStatus.QUALIFIED.name) {
                root.visibility = View.VISIBLE
                tvTimer.setText(R.string.berakhir_dalam)
                setExpiryTime(endTime)
                btJoinCampaign.setText(fragmentBlock?.subtitle?.split(":")?.get(0).orEmpty())
                tvTimer.setBackgroundResource(R.drawable.bg_solid_red5_corner_8dp)
                tvTimer.setTextColor(ContextCompat.getColor(requireContext(), R.color.red80))
                tvTimer.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    R.drawable.ic_timer_red,
                    0,
                    0,
                    0
                )
            } else if (gameStatus == GameStatus.CLAIMED.name || gameStatus == GameStatus.REWARD_FAILED.name) {
                root.visibility = View.VISIBLE
                tvTimer.setText(R.string.mission_accomplished)
                btJoinCampaign.setText(fragmentBlock?.subtitle?.split(":")?.get(1).orEmpty())
                tvTimer.setBackgroundResource(R.drawable.bg_rounded_rectangle_green_5)
                tvTimer.setTextColor(ContextCompat.getColor(requireContext(), R.color.green_80))
                tvTimer.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    R.drawable.ic_completed_tick_green,
                    0,
                    0,
                    0
                )
            } else {
                root.visibility = View.GONE
            }
        }
    }

    private fun setExpiryTime(expiredAt: String) {
        timer = object :
            CountDownTimer(DateTimeUtils.getInvoiceExpiryTime(expiredAt), AppConst.ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                binding.tvTimer.text =
                    DateTimeUtils.millisToFormattedHourAndMins(millisUntilFinished)
            }

            override fun onFinish() {
                timer?.cancel()
                binding.root.visibility = View.GONE
            }
        }
        timer?.start()
    }


    override fun subscribeState() {
        //not required
    }

    override fun onDestroy() {
        timer?.cancel()
        super.onDestroy()
    }
}

enum class GameStatus {
    IN_PROGRESS,
    QUALIFIED,
    CLAIMED,
    REWARDED,
    REWARD_FAILED
}