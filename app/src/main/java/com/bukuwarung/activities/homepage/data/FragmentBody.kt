package com.bukuwarung.activities.homepage.data

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FragmentBody(
    @SerializedName("name") val name : String,
    @SerializedName("icon") val icon : String,
    @SerializedName("rank") val rank : Int,
    @SerializedName("redirection") val redirection : String,
    @SerializedName("deeplink") val deeplink : String,
    @SerializedName("isActive") val isActive : Bo<PERSON>an,
    @SerializedName("isEnabled") val isEnabled : <PERSON><PERSON>an,
    @SerializedName("isNew") val isNew : Boolean,
    @SerializedName("button_name") val buttonName: String,
    @SerializedName("startVersion") val startVersion: Int,
    @SerializedName("endVersion") val endVersion: Int
): Parcelable
