package com.bukuwarung.activities.homepage.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.BuildConfig
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.homepage.data.RefreeEntryPointData
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.LayoutEntryPointRefreeBinding
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.returnObject
import com.google.gson.reflect.TypeToken
import javax.inject.Inject


class HomeRefereeEntryPointFragment: BaseFragment() {

    private lateinit var binding: LayoutEntryPointRefreeBinding

    companion object {
        fun createIntent(): HomeRefereeEntryPointFragment {
            return HomeRefereeEntryPointFragment()
        }
    }

    @Inject
    lateinit var neuro: Neuro

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = LayoutEntryPointRefreeBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        val jsonType = object : TypeToken<RefreeEntryPointData?>() {}.type
        val refereeEntryPointData: RefreeEntryPointData? =
            jsonType.returnObject(RemoteConfigUtils.getRefereePointEntryData())
        if (refereeEntryPointData != null) {
            binding.tvInfo.text = refereeEntryPointData.title
        }
        binding.root.setOnClickListener {
            if (refereeEntryPointData != null) {
                context?.let {
                    val intent = WebviewActivity.createIntent(
                        it,
                        BuildConfig.API_BASE_URL_LOYALTY.plus(refereeEntryPointData.redirection),
                        AppConst.EMPTY_STRING
                    )
                    startActivity(intent)
                }
            }
        }
    }

    override fun subscribeState() {
        // No impl
    }

}