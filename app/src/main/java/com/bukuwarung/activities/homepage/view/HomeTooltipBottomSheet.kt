package com.bukuwarung.activities.homepage.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.databinding.HomeFeatureNudgeBottomsheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment


class HomeTooltipBottomSheet: BaseBottomSheetDialogFragment() {

    private var homeFeatureNudgeBottomsheetBinding: HomeFeatureNudgeBottomsheetBinding? = null

    private val binding get() = homeFeatureNudgeBottomsheetBinding!!

    companion object {
        private const val TITLE_TEXT = "TITLE_TEXT"
        private const val BODY_TEXT = "BODY_TEXT"
        fun newInstance(title: String, bodyText: String?): HomeTooltipBottomSheet = HomeTooltipBottomSheet().apply {
            val bundle = Bundle()
            bundle.putString(TITLE_TEXT, title)
            bundle.putString(BODY_TEXT, bodyText)
            arguments = bundle
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        homeFeatureNudgeBottomsheetBinding = HomeFeatureNudgeBottomsheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            tvTitle.text = arguments?.getString(TITLE_TEXT)
            tvBody.text = arguments?.getString(BODY_TEXT)
            divider.setOnClickListener {
                dismiss()
            }
            btnOk.setOnClickListener {
                dismiss()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        homeFeatureNudgeBottomsheetBinding = null
    }
}