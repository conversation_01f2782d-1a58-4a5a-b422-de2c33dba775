package com.bukuwarung.activities.homepage.view

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

class HomePaymentInOutDecorator(
    private val startEndMargin: Int,
    private val intermediateMargin: Int
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val dataSize = state.itemCount
        when (parent.getChildAdapterPosition(view)) {
            0 -> {
                outRect.right = intermediateMargin / 2
                outRect.left = startEndMargin
            }
            dataSize - 1 -> {
                outRect.right = startEndMargin
                outRect.left = intermediateMargin / 2
            }
            else -> {
                outRect.right = intermediateMargin / 2
                outRect.left = intermediateMargin / 2
            }
        }
    }
}