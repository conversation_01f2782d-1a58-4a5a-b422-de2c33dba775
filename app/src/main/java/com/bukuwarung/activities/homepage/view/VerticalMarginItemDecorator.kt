package com.bukuwarung.activities.homepage.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import androidx.annotation.DimenRes
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity.Companion.context
import com.bukuwarung.utils.orNil

class VerticalMarginItemDecorator(context: Context, @DimenRes verticalMarginInDp: Int, @DimenRes firstLastMarginInDp: Int) :
    RecyclerView.ItemDecoration() {

    private val verticalMarginInPx: Int =
        context.resources.getDimension(verticalMarginInDp).toInt()
    private val firstLastMarginInDp: Int =
        context.resources.getDimension(firstLastMarginInDp).toInt()

    override fun getItemOffsets(
        outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
    ) {
        when (parent.getChildAdapterPosition(view)) {
            0 -> {
                outRect.top = firstLastMarginInDp
                outRect.bottom = verticalMarginInPx
            }
            state.itemCount - 1 -> {
                outRect.top = verticalMarginInPx
                outRect.bottom = firstLastMarginInDp

            }
            else -> {
                outRect.top = verticalMarginInPx
                outRect.bottom = verticalMarginInPx
            }

        }
    }

}