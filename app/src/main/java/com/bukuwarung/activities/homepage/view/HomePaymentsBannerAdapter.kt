package com.bukuwarung.activities.homepage.view

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.databinding.PpobBannerItemBinding
import com.bukuwarung.utils.listen
import com.bumptech.glide.Glide
import com.facebook.shimmer.Shimmer
import com.facebook.shimmer.ShimmerDrawable


class HomePaymentsBannerAdapter(
    private var itemList: List<BodyBlock?>,
    private val category: String,
    private val context: Context,
    val getOnClickData: (BodyBlock?, String, Int) -> Unit
) : RecyclerView.Adapter<HomePaymentsBannerAdapter.HomePaymentsBannerViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): HomePaymentsBannerViewHolder {
        return HomePaymentsBannerViewHolder(
            PpobBannerItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        ).listen { position, type ->
            getOnClickData(itemList[position], category, position)
        }
    }

    override fun onBindViewHolder(holder: HomePaymentsBannerViewHolder, position: Int) {
        holder.bind(itemList[position])
    }

    override fun getItemCount(): Int {
        return itemList.size
    }

    fun refreshData(newContents: List<BodyBlock?>) {
        itemList = newContents
        notifyDataSetChanged()
    }

    inner class HomePaymentsBannerViewHolder(private val binding: PpobBannerItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val shimmer = Shimmer.AlphaHighlightBuilder()
            .setDuration(1000)
            .setBaseAlpha(0.6f)
            .setHighlightAlpha(0.5f)
            .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
            .setAutoStart(true)
            .build()

        val shimmerDrawable = ShimmerDrawable().apply {
            setShimmer(shimmer)
        }

        fun bind(body: BodyBlock?) {
            with(binding) {
                if (isValidContextForGlide(context))
                    Glide.with(context).load(body?.icon).placeholder(shimmerDrawable)
                        .into(ivBanner)
            }
        }
    }

    private fun isValidContextForGlide(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        if (context is Activity) {
            if (context.isDestroyed || context.isFinishing) {
                return false
            }
        }
        return true
    }
}