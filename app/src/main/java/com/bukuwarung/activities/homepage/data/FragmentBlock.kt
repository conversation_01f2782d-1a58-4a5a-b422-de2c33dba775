package com.bukuwarung.activities.homepage.data

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class FragmentBlock(
    val analytics_block_name: String?,
    val body_block_name: String?,
    val category: String?,
    val coachmark_next: Int?,
    val coachmark_body: String?,
    val coachmark_header: String?,
    val coachmark_max_steps: Int?,
    val coachmark_step: Int?,
    val enabled: Boolean?,
    val end_version: Int?,
    val id: String?,
    var layout_type: Int?,
    val rank: Int?,
    val start_version: Int?
): Parcelable