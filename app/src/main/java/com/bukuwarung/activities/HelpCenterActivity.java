package com.bukuwarung.activities;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.VerticalFadeSwipeAnim;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.baseui.DefaultWebViewClient;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.session.SessionManager;


import java.util.Objects;

public final class HelpCenterActivity extends AppActivity {

    private WebView webView;
    private ImageView backImage;
    private String webUrl = AppConst.TUTORIAL_URL;
    private String title = "";

    public HelpCenterActivity() {
        super(new VerticalFadeSwipeAnim());
    }

    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if(getIntent().hasExtra("url"))
            webUrl = getIntent().getStringExtra("url");
        if(getIntent().hasExtra("title"))
            title = getIntent().getStringExtra("title");
        try {
            setContentView(R.layout.activity_help_center);
        } catch (Exception ex) {
            setContentView(R.layout.activity_help_center_without_webview);

            backImage = findViewById(R.id.backBtn);
            backImage.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    onBackPressed();
                }
            });

            Intent browserIntent = new Intent(
                    Intent.ACTION_VIEW, Uri.parse(webUrl));
            startActivity(browserIntent);
            return;
        }

        webView = (WebView) findViewById(R.id.webView);
        backImage = findViewById(R.id.backBtn);
        TextView titleTextView = (TextView)findViewById(R.id.guideTitle);
        if(!title.equals(""))
            titleTextView.setText(title);
        webView.setWebChromeClient(new WebChromeClient() {
            @Nullable
            @Override
            public Bitmap getDefaultVideoPoster() {
                if (super.getDefaultVideoPoster() == null) {
                    try {
                        // returns Launcher icon in case of default video poster is null
                        return BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher);
                    } catch (Exception ex) {
                        // If that too, didn't work as hoped, we'll show blank bitmap
                        return Bitmap.createBitmap(50, 50, Bitmap.Config.ARGB_8888);
                    }
                } else {
                    return super.getDefaultVideoPoster();
                }
            }
        });
        webView.setWebViewClient(new DefaultWebViewClient(this));
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setDomStorageEnabled(true);
        webView.loadUrl(webUrl);
        backImage.setOnClickListener(view -> onBackPressed());
    }

    @Override
    public void onBackPressed() {
        int txnCount = SessionManager.getInstance().isGuestUser()?TransactionRepository.getInstance(Application.getAppContext()).getGuestTransactionCount():TransactionRepository.getInstance(Application.getAppContext()).getTransactionCount();
        if(txnCount == AppConst.ZERO_TRANSACTIONS) {
            setResult(Activity.RESULT_OK);
        }
        super.onBackPressed();
    }

}
