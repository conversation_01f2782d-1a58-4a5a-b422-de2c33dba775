package com.bukuwarung.activities.settings

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.ArrayAdapter
import android.widget.ListView
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.superclasses.VerticalSwipeAnim
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.base.udf.api.screen.UdfActivity
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivitySettingsBinding
import com.bukuwarung.datasync.restore.ManualDataRestoreDialog
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.edc.payments.ui.core.PinChangeStatus
import com.bukuwarung.enums.Language
import com.bukuwarung.feature.login.createPassword.screen.ChangePasswordActivity
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.payments.constants.KycTier
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.payments.pin.NewPaymentPinActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.setup.LanguageSetupDialog
import com.bukuwarung.setup.Setup
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.JWTUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.RemoteConfigUtils.getAuthVariant
import com.bukuwarung.utils.Utilities.logOut
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isFalse
import com.bukuwarung.utils.openActivity
import com.bukuwarung.utils.openActivityForResult
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.singleClick
import dagger.hilt.android.AndroidEntryPoint
import java.util.*

@AndroidEntryPoint
class SettingsActivity : UdfActivity() {

    private val binding: ActivitySettingsBinding by viewBinding()
    private val viewModel: SettingsViewModel by viewModels()
    private lateinit var sessionManager: SessionManager
    private lateinit var featurePref: FeaturePrefManager
    private lateinit var progressDialog: Dialog
    private var hasPin = false

    init {
        VerticalSwipeAnim()
    }


    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        binding.clPin.hideView()
        viewModel.checkPinLength()
        subscribeState()
        binding.clPin.singleClick {
            val kycTier = JWTUtils.getKycTierFromToken()
            if (kycTier == KycTier.NON_KYC) {
                PaymentUtils.showKycKybStatusBottomSheet(supportFragmentManager, AnalyticsConst.PAYMENTS)
            } else {
                when{
                    hasPin.isFalse -> {
                        openNewPaymentPinScreen(PinType.PIN_CREATE.toString())
                    }
                    else -> {
                        viewModel.checkPinChangeRequest()
                    }
                }
            }
        }

        sessionManager = SessionManager.getInstance()
        featurePref = FeaturePrefManager.getInstance()

        if (getAuthVariant() == 0 || !sessionManager.userLoggedInWithPinAtleastOnce) {
            binding.passwordChangeContainer.hideView()
        }
        if (BuildConfig.MULTI_LANGUAGE_ENABLED) {
            binding.appLanguageLayout.showView()
        }

        if (SessionManager.getInstance().isGuestUser) {
            binding.clPin.hideView()
        }

        if (featurePref.useMilitaryTime()) {
            binding.timeFormat.isChecked = true
            binding.timeSample.text = "14:00"
        } else {
            binding.timeFormat.isChecked = false
            binding.timeSample.text = "02:00 PM"
        }

        if (!featurePref.isDailyHighlightSettingsVisible) {
            binding.dailyUpdateLayout.hideView()
        }
        binding.dailyBusinessRecapSwitch.isChecked = featurePref.useDailyBusinessRecap()


        binding.paymentFeature.isChecked = FeaturePrefManager.getInstance().paymentTabEnabled()
        //stockFeature.setChecked(FeaturePrefManager.getInstance().stockTabEnabled());
        //stockFeature.setChecked(FeaturePrefManager.getInstance().stockTabEnabled());
        binding.cashFeature.isChecked = FeaturePrefManager.getInstance().cashModuleEnabled()
        binding.backBtn.setOnClickListener {
            onBackPressed()
        }

        progressDialog = Dialog(this).apply {
            setCancelable(false)
            setContentView(R.layout.layout_progress_dialog)
            findViewById<TextView>(R.id.loading_msg).text = getString(R.string.please_wait)
        }

        binding.timeFormat.setOnTouchListener { view, motionEvent ->
            if(Utility.hasInternet()){
                binding.timeFormat.isClickable = true
            }else{
                binding.timeFormat.isClickable = false
                CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    this,
                    supportFragmentManager
                )
            }
            false
        }

        binding.dailyBusinessRecapSwitch.setOnTouchListener { view, motionEvent ->
            if(Utility.hasInternet()){
                binding.dailyBusinessRecapSwitch.isClickable = true
            }else{
                binding.dailyBusinessRecapSwitch.isClickable = false
                CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    this,
                    supportFragmentManager
                )
            }
            false
        }

        binding.cashFeature.setOnTouchListener { view, motionEvent ->
            if(Utility.hasInternet()){
                binding.cashFeature.isClickable = true
            }else{
                binding.cashFeature.isClickable = false
                CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    this,
                    supportFragmentManager
                )
            }
            false
        }

        binding.paymentFeature.setOnTouchListener { view, motionEvent ->
            if(Utility.hasInternet()){
                binding.paymentFeature.isClickable = true
            }else{
                binding.paymentFeature.isClickable = false
                CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    this,
                    supportFragmentManager
                )
            }
            false
        }

        binding.timeFormat.setOnCheckedChangeListener { buttonView, isChecked ->
                if (isChecked) {
                    FeaturePrefManager.getInstance().setUseMilitaryTime(true)
                    binding.timeSample.text = "14:00"
                    binding.lastBackup.text =
                        DateTimeUtils.getReadableDateTime(FeaturePrefManager.getInstance().backupTimestamp)
                } else {
                    FeaturePrefManager.getInstance().setUseMilitaryTime(false)
                    binding.timeSample.text = "02:00 PM"
                    binding.lastBackup.text =
                        DateTimeUtils.getReadableDateTime(FeaturePrefManager.getInstance().backupTimestamp)
                }
        }


        binding.dailyBusinessRecapSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                FeaturePrefManager.getInstance().setDailyBusinessRecap(true)
            } else {
                FeaturePrefManager.getInstance().setDailyBusinessRecap(false)
            }
        }

        binding.cashFeature.setOnCheckedChangeListener { buttonView, isChecked ->
            FeaturePrefManager.getInstance().setCashModuleEnabled(isChecked)
            MainActivity.startActivityAndClearTop(this)
            try {
                val propBuilder = PropBuilder()
                propBuilder.put("enabled", isChecked)
                propBuilder.put("op", "manual")
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_USE_CASH_FEATURE, propBuilder)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        binding.paymentFeature.setOnClickListener {
            if (!binding.paymentFeature.isChecked) {
                showPaymentTabConfirmationDialog()
            } else {
                FeaturePrefManager.getInstance().setPaymentTabEnabled(true)
                MainActivity.startActivityAndClearTop(this)
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.ID, true)
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_SHOWN,
                    propBuilder
                )
            }
        }

        binding.lastBackup.text =
            DateTimeUtils.getReadableDateTime(FeaturePrefManager.getInstance().backupTimestamp)



        binding.btnSync.setOnClickListener {
            if (Utility.hasInternet()) {
                binding.btnSync.isEnabled = false
                binding.btnSync.isClickable = false
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_BACKUP_NOW)
                val manualDataRestoreDialog = ManualDataRestoreDialog(this, this)
                manualDataRestoreDialog.show()
                FeaturePrefManager.getInstance().isRestoreInProgress(true)
            } else {
                showBottomSheet(
                    R.string.no_internet_title,
                    R.string.no_internet_desc,
                    R.drawable.ic_data_restore_no_internet,
                    R.string.understand, null, { bottomsheet ->
                        bottomsheet.dismiss()
                    }, {
                        //No Implementation required
                    }
                )
            }
        }

        val langString = this.resources.getString(R.string.dialog_choose_language)
        val appLanguageBtn = findViewById<TextView>(R.id.app_language)
        appLanguageBtn.text =
            if (sessionManager.appLanguage == Language.ENGLISH.langCd) getString(R.string.english) else getString(
                R.string.indonesian
            )
        appLanguageBtn.setOnClickListener {
            if (Utility.hasInternet()) {
                showLanguageDialog(
                    this,
                    langString
                )
            } else {
                CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    this,
                    supportFragmentManager
                )
            }
        }

        binding.logoutContainer.setOnClickListener {
            checkConditionForLogout()
        }

        binding.passwordChangeContainer.setOnClickListener {
            startActivity(Intent(this, ChangePasswordActivity::class.java))
        }
        observeState(source = viewModel.stateFlow, action = ::observeViewModelState)

        viewModel.event.observe(this) {
            when (it) {
                is SettingsViewModel.Event.SetLogoutButtonVisibility -> {
                    binding.logoutContainer.apply {
                        if (it.hideButton || SessionManager.getInstance().isGuestUser) {
                            hideView()
                        } else {
                            showView()
                        }
                    }
                }
            }
        }
        viewModel.getPaymentsData()
    }

    private fun checkConditionForLogout() {
        if (Utility.hasInternet()) {
            if (checkIfDataToRestore()) {
                showOnlineDataSyncInProgressLogoutBottomSheet()
            } else {
                showOnlineAndNoDataToSyncBottomSheet()
            }
        } else {
            showOfflineAndDataToSyncBottomSheet()
        }
    }

    private fun showOnlineDataSyncInProgressLogoutBottomSheet() {
        showBottomSheet(
            R.string.want_to_quit_app,
            R.string.sync_inprogress_logout_would_delete_data,
            R.drawable.ic_offline_online_logout,
            R.string.batal,
            getString(R.string.sign_out),
            { bottomSheet ->
                bottomSheet.closeDialog()
            }, { bottomSheet ->
                bottomSheet.dismiss()

                logOut(this@SettingsActivity, this)
            }
        )
    }

    private fun showOnlineAndNoDataToSyncBottomSheet() {
        showBottomSheet(
            R.string.want_to_quit_app,
            R.string.data_is_backed_up_can_logout,
            R.drawable.ic_offline_online_logout,
            R.string.batal,
            getString(R.string.sign_out),
            { bottomSheet ->
                bottomSheet.dismiss()
            },
            { bottomSheet ->
                bottomSheet.dismiss()

                logOut(this@SettingsActivity, this)
            })
    }

    private fun showOfflineAndDataToSyncBottomSheet() {
        showBottomSheet(
            R.string.cannot_exit_application,
            R.string.makes_sure_you_are_connected_to_save_records,
            R.drawable.ic_data_sync_offline,
            R.string.understand,
            null,
            { bottomSheet ->
                bottomSheet.dismiss()
            }, {
                //No Implementation required.
            }
        )
    }

    private fun checkIfDataToRestore(): Boolean {
        return FeaturePrefManager.getInstance().dataRestoreProgressStatus
    }

    private fun observeViewModelState(state: SettingsFormState) {
        when (state.restoreState) {
            DataRestoreState.STATE_NOT_YET_INITIATED -> {
                binding.llRestoreProgress.hideView()
            }
            DataRestoreState.STATE_INITIATED -> {
                binding.llRestoreProgress.showView()
                binding.btnSync.isEnabled = false
                binding.btnSync.isClickable = false
            }
            DataRestoreState.STATE_ERROR -> {
                binding.llRestoreProgress.hideView()
                binding.btnSync.isEnabled = true
                binding.btnSync.isClickable = true
                showBottomSheet(
                    R.string.data_restore_error_title,
                    R.string.data_restore_error_desc,
                    R.drawable.ic_data_restore_cancelled,
                    R.string.understand, null,
                    { bottomSheet ->
                        bottomSheet.dismiss()
                    }, {
                        //No Implementation required
                    }
                )
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_MANUAL_BACKUP_FAIL)
            }
            DataRestoreState.STATE_SUCCESS -> {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_MANUAL_BACKUP_SUCCESS)
                binding.llRestoreProgress.hideView()
                binding.btnSync.isEnabled = true
                binding.btnSync.isClickable = true
                FeaturePrefManager.getInstance().backupTimestamp = System.currentTimeMillis()
                binding.lastBackup.text =
                    DateTimeUtils.getReadableDateTime(FeaturePrefManager.getInstance().backupTimestamp)
                FeaturePrefManager.getInstance().isRestoreInProgress(false)
                showBottomSheet(
                    R.string.data_restore_success_title,
                    R.string.records_are_updated,
                    R.drawable.data_restore_success,
                    R.string.restore_complete_btn, null,
                    { bottomSheet ->
                        bottomSheet.dismiss()

                    }, {
                        //No Implementation required
                    }
                )
            }
        }
    }


    private fun showLanguageDialog(activity: Activity, titleStr: String?) {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_OPEN_LANGUAGE_DIALOG,
            "current_lang_cd",
            SessionManager.getInstance().appLanguage.toString() + ""
        )
        val dialog = Dialog(activity)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(R.layout.dialog_select_language)
        val window = dialog.window
        window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        dialog.setCancelable(false)
        val titleTv = dialog.findViewById<TextView>(R.id.languageSelectDialogTitle)
        val langListView = dialog.findViewById<ListView>(R.id.cstLanguage)
        dialog.findViewById<View>(R.id.closeDialog).setOnClickListener {
            dialog.dismiss()
        }

        titleTv.text = titleStr
        val languageList = activity.resources.getStringArray(R.array.SupportedLanguage)
        val dataList: ArrayList<String> = ArrayList<String>()
        Collections.addAll(dataList, *languageList)
        langListView.adapter =
            ArrayAdapter(activity, R.layout.langauge_list_item, R.id.language_item, dataList)
        langListView.onItemClickListener = LanguageSetupDialog(Setup(), activity, dialog)
        dialog.show()
    }

    private fun showBottomSheet(
        title: Int,
        subTitle: Int,
        image: Int,
        positiveBtnText: Int,
        negativeBtnText: String? = null,
        positiveButtonClick: (CommonConfirmationBottomSheet) -> Unit,
        negativeButtonClick: (CommonConfirmationBottomSheet) -> Unit
    ) {
        val bottomSheet = CommonConfirmationBottomSheet(this)
        bottomSheet.setTitle(title)
        bottomSheet.setDescription(subTitle)
        bottomSheet.setImage(image)
        bottomSheet.setCancellableFlag(false)
        bottomSheet.setPositiveButtonText(positiveBtnText)
        bottomSheet.setPositiveButtonClickListener {
            positiveButtonClick.invoke(bottomSheet)
        }
        bottomSheet.setNegativeButtonText(negativeBtnText)
        bottomSheet.setNegativeButtonClickListener {
            negativeButtonClick.invoke(bottomSheet)
        }
        bottomSheet.show(supportFragmentManager, "SYNC_DATA")
    }


    private fun showPaymentTabConfirmationDialog() {
        val dialog = GenericConfirmationDialog(this,
            R.layout.layout_generic_confirmation_dialog,
            R.string.payment_tab_confirmation_title,
            R.string.payment_tab_confirmation_subtitle,
            R.string.tut_next_btn, R.string.cancel, null,
            {
                FeaturePrefManager.getInstance().setPaymentTabEnabled(false)
                MainActivity.startActivityAndClearTop(this)
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.ID, false)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_SHOWN, propBuilder)
            }
        ) {
            binding.paymentFeature.toggle()
        }
        dialog.show()
    }

    private fun subscribeState() {
        viewModel.pinChangeResponse.observe(this){
            when(val status = it?.content?.firstOrNull()?.status.orEmpty()){
                PinChangeStatus.PENDING_OTP_VERIFICATION.name -> {
                    openNewPaymentPinScreen(PinType.PIN_FORGOT_ENTER_OTP_STEP.toString())
                }
                PinChangeStatus.PENDING_PIN_INPUT.name -> {
                    openNewPaymentPinScreen(PinType.PIN_FORGOT_CREATE_PIN_STEP.toString())
                }
                PinChangeStatus.PENDING_MANUAL_VERIFICATION.name, PinChangeStatus.VERIFIED.name, PinChangeStatus.REJECTED.name -> {
                    if (status.equals(PinChangeStatus.VERIFIED.name, true)){
                        if (SessionManager.getInstance().isPinVerified){
                            openNewPaymentPinScreen(PinType.PIN_UPDATE.toString())
                        } else {
                            SessionManager.getInstance().isPinVerified = true
                            openActivity(WebviewActivity::class.java){
                                putString(BaseWebviewActivity.LINK,RemoteConfigUtils.getPaymentConfigs().forgotPinUrl+"?landing=BUKUWARUNG")
                            }
                        }
                    } else {
                        openActivity(WebviewActivity::class.java){
                            putString(BaseWebviewActivity.LINK,RemoteConfigUtils.getPaymentConfigs().forgotPinUrl+"?landing=BUKUWARUNG")
                        }
                    }
                }
                PinChangeStatus.VOID.name, "" -> {
                    openNewPaymentPinScreen(PinType.PIN_UPDATE.toString())
                }
                else -> {
                    openNewPaymentPinScreen(PinType.PIN_UPDATE.toString())
                }

            }
        }
        viewModel.hasPin.observe(this) { hasPin ->
            <EMAIL> = hasPin
            binding.apply {
                clPin.showView()
                tvPinChange.text = getString(if (hasPin) R.string.edit else R.string.atur)
                tvPinInfo.text = getString(if (hasPin) R.string.change_transaksi_pin else R.string.set_transaksi_pin)
            }
        }
    }

    private fun openNewPaymentPinScreen(useCase: String){
        openActivityForResult(NewPaymentPinActivity::class.java, startNewPaymentPinActivityForResult) {
            putString(NewPaymentPinActivity.USECASE, useCase)
        }
    }

    private val startNewPaymentPinActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                hasPin = true
                binding.tvPinInfo.text = getString(R.string.change_transaksi_pin)
                binding.tvPinChange.text = getString(R.string.edit)
            }
        }
}