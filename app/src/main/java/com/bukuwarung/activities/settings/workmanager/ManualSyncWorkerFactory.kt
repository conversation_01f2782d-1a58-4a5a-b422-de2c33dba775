package com.bukuwarung.activities.settings.workmanager

import android.content.Context
import androidx.work.ListenableWorker
import androidx.work.WorkerFactory
import androidx.work.WorkerParameters
import com.bukuwarung.datasync.usecase.ManualSyncUseCase
import javax.inject.Inject

class ManualSyncWorkerFactory @Inject constructor(
    private val manualSyncUseCase: ManualSyncUseCase
) : WorkerFactory() {
    override fun createWorker(
        appContext: Context,
        workerClassName: String,
        workerParameters: WorkerParameters
    ): ListenableWorker? {
        return when (workerClassName) {
            ManualSyncDataWorker::class.java.name ->
                ManualSyncDataWorker(appContext, workerParameters, manualSyncUseCase)
            else -> null
        }
    }
}