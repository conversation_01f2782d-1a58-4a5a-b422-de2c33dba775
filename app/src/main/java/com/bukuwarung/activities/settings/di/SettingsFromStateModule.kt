package com.bukuwarung.activities.settings.di

import androidx.lifecycle.SavedStateHandle
import com.bukuwarung.activities.settings.DataRestoreState
import com.bukuwarung.activities.settings.SettingsFormState
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.base.udf.implementation.state.DefaultStateManager
import com.bukuwarung.feature.onboarding.form.screen.OnBoardingFormState
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent

@Module
@InstallIn(ViewModelComponent::class)
object SettingsFromStateModule {

    @Provides
    fun provideSettingsFromState(): SettingsFormState {
        return SettingsFormState(
            restoreState = DataRestoreState.STATE_NOT_YET_INITIATED
        )
    }

    @Provides
    fun provideStateManager(
        initState: SettingsFormState,
        savedStateHandle: SavedStateHandle,
    ): StateManager<SettingsFormState> {
        return DefaultStateManager(initState, savedStateHandle)
    }
}