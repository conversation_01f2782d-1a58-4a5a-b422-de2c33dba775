package com.bukuwarung.activities.settings

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.bukuwarung.R
import com.bukuwarung.databinding.BottomsheetManualSyncBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView

class CommonConfirmationBottomSheet() : BaseBottomSheetDialogFragment() {

    private var title: String? = null
    private var description: String? = null
    private var positiveButtonText: String? = null
    private var negativeButtonText: String? = null
    private var positiveButtonClickListener: (() -> Unit)? = null
    private var negativeButtonClickListener: (() -> Unit)? = null
    private var cancelableFlag: Boolean = false
    private var imageId: Int? = null

    private lateinit var instance: CommonConfirmationBottomSheet
    private lateinit var mContext: Context

    constructor(context: Context) : this() {
        mContext = context
        instance = this
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        instance = this
        setStyle(STYLE_NORMAL, R.style.CommonBottomSheetDialogStyle)
        savedInstanceState?.let {
            dismissAllowingStateLoss()
        }
    }

    private var _binding: BottomsheetManualSyncBinding? = null
    private val binding get() = _binding!!
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BottomsheetManualSyncBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = cancelableFlag
        title?.let {
            binding.tvHeading.text = it
        } ?: run {
            binding.tvHeading.hideView()
        }
        description?.let {
            binding.tvBody.text = it
        } ?: run {
            binding.tvBody.hideView()
        }

        imageId?.let {
            binding.ivImage.setImageResource(it)
        } ?: run {
            binding.ivImage.hideView()
        }
        positiveButtonText?.let {
            binding.btnPositive.text = it
            binding.btnPositive.showView()
        } ?: run {
            binding.btnPositive.hideView()
        }

        negativeButtonText?.let {
            binding.btnNegative.text = it
            binding.btnNegative.showView()
        } ?: run {
            binding.btnNegative.hideView()
        }

        binding.btnNegative.setOnClickListener { negativeButtonClickListener?.invoke() }
        binding.btnPositive.setOnClickListener { positiveButtonClickListener?.invoke() }

    }

    fun setTitle(title: String) {
        this.title = title
    }

    fun setTitle(title: Int) {
        this.title = mContext.getString(title)
    }

    fun setDescription(description: String?) {
        this.description = description
    }

    fun setDescription(description: Int) {
        this.description = mContext.getString(description)
    }

    fun setPositiveButtonText(pbText: String?) {
        this.positiveButtonText = pbText
    }

    fun setPositiveButtonText(pbText: Int) {
        this.positiveButtonText = mContext.getString(pbText)
    }

    fun setNegativeButtonText(nbText: String?) {
        this.negativeButtonText = nbText
    }

    fun setNegativeButtonText(nbText: Int) {
        this.negativeButtonText = mContext.getString(nbText)
    }

    fun setImage(imageId: Int) {
        this.imageId = imageId
    }

    fun setPositiveButtonClickListener(positiveButtonClickListener: () -> Unit) {
        this.positiveButtonClickListener = positiveButtonClickListener

    }

    fun setNegativeButtonClickListener(negativeButtonClickListener: () -> Unit) {
        this.negativeButtonClickListener = negativeButtonClickListener
    }

    fun setCancellableFlag(setCancellable: Boolean) {
        this.cancelableFlag = setCancellable
    }

    fun closeDialog() {
        dismiss()
    }

    /**
     * common method for no internet bottomSheet
     */
    fun showNoInternetBottomSheet(context: Context,fragmentManager: FragmentManager) {
        val bottomSheet = CommonConfirmationBottomSheet(context)
        bottomSheet.setTitle(R.string.feature_requires_internet_connection)
        bottomSheet.setDescription(R.string.make_sure_you_are_connected)
        bottomSheet.setImage(R.drawable.ic_data_restore_no_internet)
        bottomSheet.setCancellableFlag(false)
        bottomSheet.setPositiveButtonText(R.string.understand)
        bottomSheet.setPositiveButtonClickListener {
            bottomSheet.closeDialog()
        }
        bottomSheet.show(fragmentManager, "NO_INTERNET_DIALOG")
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }
}