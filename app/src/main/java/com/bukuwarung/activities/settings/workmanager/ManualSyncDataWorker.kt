package com.bukuwarung.activities.settings.workmanager

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.bukuwarung.Application
import com.bukuwarung.activities.settings.SettingsViewModel
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.datasync.*
import com.bukuwarung.datasync.model.*
import com.bukuwarung.datasync.usecase.ManualSyncUseCase
import com.bukuwarung.utils.*
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject

@HiltWorker
class ManualSyncDataWorker @AssistedInject constructor(
    @Assisted private val context: Context,
    @Assisted private val params: WorkerParameters,
    val manualSyncUseCase: ManualSyncUseCase
) : CoroutineWorker(context, params) {
    override suspend fun doWork(): Result {

        val tableName = inputData.getString(TABLE_NAME) ?: return Result.failure()
        val lastVisible = inputData.getInt(LAST_VISIBLE, 0)
        val batchSize = inputData.getInt(BATCH_SIZE, 0)
        return syncData(
            tableName,
            if (lastVisible == 0) null else lastVisible,
            if (batchSize == 0) null else batchSize
        )

    }

    private suspend fun syncData(tableName: String, lastVisible: Int?, batchSize: Int?): Result {
        val response = manualSyncUseCase.getManualSyncData(tableName, lastVisible, batchSize)
        when (response) {
            is ApiSuccessResponse -> {
                try {
                    val dataString = Gson().toJson(response.body?.data)
                    var isNotEmptyOrNullData = false
                    var outputData: Data? = null
                    var lastVisibleId: Int? = null
                    when (tableName) {
                        SettingsViewModel.TABLE_BOOK_STORE -> {
                            val jsonType =
                                object : TypeToken<ManualSyncData<BookResponseListItem>>() {}.type
                            val data: ManualSyncData<BookResponseListItem> =
                                Gson().fromJson(dataString, jsonType)
                            val entityData = data.responseList?.map { it?.toBookEntity() }
                            if (entityData != null) {
                                for (bookEntity in entityData) {
                                    BusinessRepository.getInstance(Application.getAppContext())
                                        .insertBookSync(bookEntity)
                                }
                            }
                            outputData = workDataOf(
                                LAST_VISIBLE to data.lastVisibleId,
                                TOTAL_ELEMENTS to data.totalRecords.toString(),
                                ELEMENTS_PRESENT to data.responseList?.size.toString()
                            )
                            isNotEmptyOrNullData = (data.responseList?.isNullOrEmpty() == false)
                            lastVisibleId = data.lastVisibleId

                            if (data.lastVisibleId != null) {
                                AppBookSyncManager.getInstance().bookRestoreTime =
                                    System.currentTimeMillis()
                            }
                        }
                        SettingsViewModel.TABLE_CUSTOMER_STORE -> {
                            val jsonType =
                                object :
                                    TypeToken<ManualSyncData<CustomerResponseListItem>>() {}.type
                            val data: ManualSyncData<CustomerResponseListItem> =
                                Gson().fromJson(dataString, jsonType)
                            val entityData = data.responseList?.map { it?.toCategoryEntity() }
                            if (entityData != null) {
                                for (categoryEntity in entityData) {
                                    CustomerRepository.getInstance(Application.getAppContext())
                                        .insertCustomerSync(categoryEntity)
                                }
                            }
                            outputData = workDataOf(
                                LAST_VISIBLE to data.lastVisibleId,
                                TOTAL_ELEMENTS to data.totalRecords.toString(),
                                ELEMENTS_PRESENT to data.responseList?.size.toString()
                            )
                            isNotEmptyOrNullData = (data.responseList?.isNullOrEmpty() == false)
                            lastVisibleId = data.lastVisibleId
                            if (data.lastVisibleId != null) {

                                AppCustomerSyncManager.getInstance()
                                    .customerRestoreTime(System.currentTimeMillis())
                            }
                        }
                        SettingsViewModel.TABLE_TRANSACTION_STORE -> {
                            val jsonType =
                                object :
                                    TypeToken<ManualSyncData<TransactionResponseListItem>>() {}.type
                            val data: ManualSyncData<TransactionResponseListItem> =
                                Gson().fromJson(dataString, jsonType)
                            val entityData = data.responseList?.map { it?.toTransactionEntity() }
                            if (entityData != null) {
                                for (transactionEntity in entityData) {
                                    TransactionRepository.getInstance(Application.getAppContext())
                                        .insertTransactionSync(transactionEntity)

                                }
                            }
                            outputData = workDataOf(
                                LAST_VISIBLE to data.lastVisibleId,
                                TOTAL_ELEMENTS to data.totalRecords.toString(),
                                ELEMENTS_PRESENT to data.responseList?.size.toString()
                            )
                            isNotEmptyOrNullData = (data.responseList?.isNullOrEmpty() == false)
                            lastVisibleId = data.lastVisibleId
                            if (data.lastVisibleId != null) {
                                AppTransSyncManager.getInstance().transSyncTime =
                                    System.currentTimeMillis()
                            }
                        }
                        SettingsViewModel.TABLE_APP_EXPENSE_TRANSACTION_STORE -> {
                            val jsonType =
                                object :
                                    TypeToken<ManualSyncData<AppExpenseTransactionResponseListItem>>() {}.type
                            val data: ManualSyncData<AppExpenseTransactionResponseListItem> =
                                Gson().fromJson(dataString, jsonType)
                            val entityData =
                                data.responseList?.map { it?.toCashTransactionEntity() }
                            if (entityData != null) {
                                for (cashTransactionEntity in entityData) {
                                    CashRepository.getInstance(Application.getAppContext())
                                        .insertCashTransaction(cashTransactionEntity)
                                }
                            }
                            outputData = workDataOf(
                                LAST_VISIBLE to data.lastVisibleId,
                                TOTAL_ELEMENTS to data.totalRecords.toString(),
                                ELEMENTS_PRESENT to data.responseList?.size.toString()
                            )
                            isNotEmptyOrNullData = (data.responseList?.isNullOrEmpty() == false)
                            lastVisibleId = data.lastVisibleId
                            if (data.lastVisibleId != null) {
                                AppExpenseTransSyncManager.getInstance().expenseRestoreTime =
                                    System.currentTimeMillis()
                            }
                        }
                        SettingsViewModel.TABLE_APP_EXPENSE_CATEGORY_STORE -> {
                            val jsonType =
                                object :
                                    TypeToken<ManualSyncData<AppExpenseCategoryResponseListItem>>() {}.type
                            val data: ManualSyncData<AppExpenseCategoryResponseListItem> =
                                Gson().fromJson(dataString, jsonType)
                            val entityData = data.responseList?.map { it?.toCashCategoryEntity() }
                            if (entityData != null) {
                                for (CashCategoryEntity in entityData) {
                                    CashRepository.getInstance(Application.getAppContext())
                                        .insertCashCategorySync(CashCategoryEntity)
                                }
                            }
                            outputData = workDataOf(
                                LAST_VISIBLE to data.lastVisibleId,
                                TOTAL_ELEMENTS to data.totalRecords.toString(),
                                ELEMENTS_PRESENT to data.responseList?.size.toString()
                            )
                            isNotEmptyOrNullData = (data.responseList?.isNullOrEmpty() == false)
                            lastVisibleId = data.lastVisibleId
                            if (data.lastVisibleId != null) {
                                AppCustomerCategorySyncManager.getInstance().customerRestoreTime =
                                    System.currentTimeMillis()
                            }

                        }
                        SettingsViewModel.TABLE_APP_TRANSACTION_ITEM_STORE -> {
                            val jsonType =
                                object :
                                    TypeToken<ManualSyncData<AppTransactionItemResponseListItem>>() {}.type
                            val data: ManualSyncData<AppTransactionItemResponseListItem> =
                                Gson().fromJson(dataString, jsonType)
                            val entityData =
                                data.responseList?.map { it?.toTransactionItemsEntity() }
                            if (entityData != null) {
                                for (transactionItemEntity in entityData) {
                                    TransactionRepository.getInstance(Application.getAppContext())
                                        .insertTransactionItems(transactionItemEntity)
                                }
                            }
                            outputData = workDataOf(
                                LAST_VISIBLE to data.lastVisibleId,
                                TOTAL_ELEMENTS to data.totalRecords.toString(),
                                ELEMENTS_PRESENT to data.responseList?.size.toString()
                            )
                            /**
                             * for this table Api returns all rows at once.
                             */
                            isNotEmptyOrNullData = false
                            lastVisibleId = data.lastVisibleId

                        }
                    }
                    if (isNotEmptyOrNullData) {
                        return syncData(tableName, lastVisibleId, batchSize)
                    } else {
                        return Result.success(outputData!!)
                    }
                } catch (e: Exception) {
                    FirebaseCrashlytics.getInstance().recordException(
                        Exception("ManualSyncException: , message: ${e.message.toString()}")
                    )
                    return Result.failure(workDataOf(ERROR to e.message.toString()))
                }
            }
            is ApiErrorResponse -> {
                FirebaseCrashlytics.getInstance().recordException(
                    Exception("APIException: code: ${response.code}, message: ${response.errorMessage}")
                )
                return Result.failure(workDataOf(ERROR to "APIException"))
            }
            else -> {
                return Result.failure(workDataOf(ERROR to "Unknown error occurred"))
            }
        }
    }

    companion object {
        const val TABLE_NAME = "table_name"
        const val LAST_VISIBLE = "last_visible"
        const val BATCH_SIZE = "batch_size"
        const val TOTAL_ELEMENTS = "total_elements"
        const val ELEMENTS_PRESENT = "element_present"
        const val TAG = "manual_sync_data_worker"
        const val ERROR = "error_msg"
    }

    @AssistedFactory
    interface Factory {
        fun create(appContext: Context, params: WorkerParameters): ManualSyncDataWorker
    }
}