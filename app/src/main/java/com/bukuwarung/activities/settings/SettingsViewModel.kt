package com.bukuwarung.activities.settings

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkInfo
import androidx.work.WorkManager
import androidx.work.workDataOf
import com.bukuwarung.activities.settings.workmanager.ManualSyncDataWorker
import com.bukuwarung.base.udf.api.screen.UdfViewModel
import com.bukuwarung.base.udf.api.state.StateManager
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.domain.payments.PaymentTwoFAUseCase
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.data.model.PinChangeResponse
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.isZero
import com.bukuwarung.utils.orNil
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit
import javax.inject.Inject


@HiltViewModel
class SettingsViewModel @Inject constructor(
    val stateManager: StateManager<SettingsFormState>,
    private val paymentUseCase: PaymentUseCase,
    private val finproUseCase: FinproUseCase,
    private var paymentTwoFAUseCase: PaymentTwoFAUseCase,
    @ApplicationContext private val context: Context
) : UdfViewModel<SettingsFormState>(stateManager) {
    private val workManager = WorkManager.getInstance(context)

    fun manualDataSync(lastVisible: Int? = null) {
        produce { state ->
            state.copy(restoreState = DataRestoreState.STATE_INITIATED)
        }
        val listOfTableNames = listOf(
            TABLE_BOOK_STORE,
            TABLE_CUSTOMER_STORE,
            TABLE_TRANSACTION_STORE,
            TABLE_APP_EXPENSE_TRANSACTION_STORE,
            TABLE_APP_EXPENSE_CATEGORY_STORE,
            TABLE_APP_TRANSACTION_ITEM_STORE
        )

        val continuation = workManager
            .beginUniqueWork(
                MANUAL_SYNC_DATA_WORKER,
                ExistingWorkPolicy.KEEP,
                returnWorker(listOfTableNames[0], lastVisible, DEFAULT_BATCH_SIZE)
            )
            .then(returnWorker(listOfTableNames[1], lastVisible, DEFAULT_BATCH_SIZE))
            .then(returnWorker(listOfTableNames[2], lastVisible, DEFAULT_BATCH_SIZE))
            .then(returnWorker(listOfTableNames[3], lastVisible, DEFAULT_BATCH_SIZE))
            .then(returnWorker(listOfTableNames[4], lastVisible, DEFAULT_BATCH_SIZE))
            .then(returnWorker(listOfTableNames[5], lastVisible, null))

        continuation.enqueue()
        continuation.workInfosLiveData.observeForever { workInfos ->
            val isError = workInfos.filter {
                it.state == WorkInfo.State.FAILED
            }.isNotEmpty()

            val isSuccess = workInfos.filter {
                it.state == WorkInfo.State.SUCCEEDED
            }.size == listOfTableNames.size

            if (isError) {
                produce { state ->
                    state.copy(restoreState = DataRestoreState.STATE_ERROR)
                }
            }
            if (isSuccess) {
                produce { state ->
                    state.copy(restoreState = DataRestoreState.STATE_SUCCESS)
                }
            }

        }
    }

    private val _event = MutableLiveData<Event>()
    val event: LiveData<Event> = _event

    sealed class Event {
        data class SetLogoutButtonVisibility(var hideButton: Boolean): Event()
    }

    fun getPaymentsData() = viewModelScope.launch(Dispatchers.IO) {
        val bookId = SessionManager.getInstance().businessId
        val summary = finproUseCase.getPaymentSummary(bookId)
        val bankAccounts = paymentUseCase.getMerchantBankAccounts(bookId)
        if (summary is ApiSuccessResponse && bankAccounts is ApiSuccessResponse) {
            // Hide logout button if there is no bank account added and there are no trxs
            if (bankAccounts.body.isEmpty() && summary.body.countAll.orNil.isZero()) {
                _event.postValue(Event.SetLogoutButtonVisibility(true))
            } else {
                _event.postValue(Event.SetLogoutButtonVisibility(false))
            }
        } else {
            _event.postValue(Event.SetLogoutButtonVisibility(false))
        }
    }

    private fun returnWorker(
        tableName: String,
        lastVisible: Int?,
        batch_size: Int?
    ): OneTimeWorkRequest {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val worker = OneTimeWorkRequest.Builder(ManualSyncDataWorker::class.java)
            .setInputData(
                workDataOf(
                    ManualSyncDataWorker.TABLE_NAME to tableName,
                    ManualSyncDataWorker.LAST_VISIBLE to lastVisible,
                    ManualSyncDataWorker.BATCH_SIZE to batch_size
                )
            )
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                2 * 60 * 1000L, // 2 minute.
                TimeUnit.MILLISECONDS
            )
            .setConstraints(constraints)
            .addTag(ManualSyncDataWorker.TAG + tableName)
            .build()
        return worker
    }

    companion object {
        const val MANUAL_SYNC_DATA_WORKER = "manual_sync_data_worker"
        const val TABLE_BOOK_STORE = "ac_business"
        const val TABLE_CUSTOMER_STORE = "ac_customers"
        const val TABLE_TRANSACTION_STORE = "ac_customer_transaction"
        const val TABLE_APP_EXPENSE_TRANSACTION_STORE = "ac_business_transaction"
        const val TABLE_APP_EXPENSE_CATEGORY_STORE = "ac_transaction_category"
        const val TABLE_APP_TRANSACTION_ITEM_STORE = "ac_transaction_items"
        const val DEFAULT_BATCH_SIZE = 50
    }

    private val _pinChangeResponse = MutableLiveData<PinChangeResponse?>()
    val pinChangeResponse: LiveData<PinChangeResponse?>
        get() = _pinChangeResponse

    private val _hasPin = MutableLiveData<Boolean>()
    val hasPin: LiveData<Boolean>
        get() = _hasPin

    fun checkPinLength() = viewModelScope.launch(Dispatchers.IO) {
        when(val result = paymentTwoFAUseCase.checkPinLength()){
            is ApiSuccessResponse -> {
                _hasPin.postValue(result.body.success)
            }
            else -> {
                _hasPin.postValue(false)
            }
        }
    }

    fun checkPinChangeRequest() = viewModelScope.launch(Dispatchers.IO) {
        when(val result = paymentTwoFAUseCase.checkPinChangeRequest()){
            is ApiSuccessResponse -> {
                _pinChangeResponse.postValue(result.body)
            }
            is ApiErrorResponse -> {
                _pinChangeResponse.postValue(null)
            }
            else -> _pinChangeResponse.postValue(null)
        }
    }
}