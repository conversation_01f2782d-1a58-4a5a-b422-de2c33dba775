package com.bukuwarung.activities.inventory.detail

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.bukuwarung.R
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.InventoryOperationType
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.InventoryRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.databinding.BottomSheetDialogManageStockBinding
import com.bukuwarung.utils.DigitsInputFilter
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.Utility
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.skydoves.powerspinner.OnSpinnerItemSelectedListener
import kotlinx.android.synthetic.main.bottom_sheet_dialog_manage_stock.*
import kotlinx.android.synthetic.main.layout_customer_number_stepper.view.*
import java.text.DecimalFormat
import kotlin.math.floor
import kotlin.math.roundToInt


class ManageStockBottomSheetFragment : BottomSheetDialogFragment() {

    internal lateinit var viewModel: InventoryHistoryDetailViewModel
    private lateinit var productId: String
    private lateinit var mBinding: BottomSheetDialogManageStockBinding
    private lateinit var productEntity: ProductEntity
    private lateinit var entryPoint: String

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (arguments != null) {
            if (requireArguments().containsKey("product_id")) {
                productId = requireArguments().getString("product_id").toString()

            }

            if (requireArguments().containsKey("entry_point")) {
                entryPoint = requireArguments().getString("entry_point").toString()

            }
        }
        val factory = InventoryHistoryDetailViewModelFactory(this, InventoryRepository.getInstance(activity),
                ProductRepository.getInstance(activity),
                productId)

        this.viewModel = ViewModelProviders.of(this, factory)
                .get(InventoryHistoryDetailViewModel::class.java)

        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialogThemeRound)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        mBinding = BottomSheetDialogManageStockBinding.inflate(inflater, container, false)
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        mBinding.ivClose.setOnClickListener { v: View? ->
            InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
            if (dialog != null) dialog!!.dismiss()
        }

        mBinding.spinner.setOnSpinnerItemSelectedListener(object : OnSpinnerItemSelectedListener<String?> {
            override fun onItemSelected(position: Int, item: String?) {
                spinner.text = item
                updateTotalStockValue()
            }
        })
        mBinding.numberStepperLayout.btnIncrease.setOnClickListener { v: View? ->
            try {
                InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
                mBinding.numberStepperLayout.etNumber.clearFocus()
                var olderValue: Double
                if (mBinding.numberStepperLayout.etNumber.text.toString().isEmpty()) {
                    olderValue = 0.0
                } else {
                    olderValue = Utility.extractAmountFromText(mBinding.numberStepperLayout.etNumber.text.toString()).toDouble()
                }

                olderValue++
                mBinding.numberStepperLayout.etNumber.setText(getRoundedOffStockValue(olderValue))
                mBinding.numberStepperLayout.etNumber.setSelection(mBinding.numberStepperLayout.etNumber.text.length)
            } catch (e: Exception) {
                // raise the exception here.
                Log.v("exception here", e.message.toString())
            }
        }
        mBinding.numberStepperLayout.btnDecrease.setOnClickListener { v: View? ->
            try {
                InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
                mBinding.numberStepperLayout.etNumber.clearFocus()
                var olderValue: Double
                if (mBinding.numberStepperLayout.etNumber.text.toString().isEmpty()) {
                    olderValue = 0.0
                } else {
                    olderValue = Utility.extractAmountFromText(mBinding.numberStepperLayout.etNumber.text.toString()).toDouble()
                }
                olderValue--
                if (olderValue >= 0) {
                    mBinding.numberStepperLayout.etNumber.setText(getRoundedOffStockValue(olderValue))
                    mBinding.numberStepperLayout.etNumber.setSelection(mBinding.numberStepperLayout.etNumber.text.length)
                }
            } catch (e: Exception) {
                // raise the exception here.
            }
        }

        mBinding.numberStepperLayout.etNumber.addTextChangedListener(textWatcher)

        mBinding.numberStepperLayout.etNumber.filters = arrayOf<InputFilter>(
            DigitsInputFilter(
                5,
                3,
                ','
            )
        )

        return mBinding.root;
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        observeData()
    }

    private fun observeData() {
        viewModel?.stateData?.observe(this, Observer { state ->
            showBasedOnState(state)
        })
    }

    /**
     * Show data based on state.
     */
    private fun showBasedOnState(state: StockDetailState) {
        when (state.currentProductState) {
            ProductDetailStateType.Loading -> {

            }
            ProductDetailStateType.NotFound -> {


                //TODO - we can add conition here.

                /*   handler.postDelayed({
                       finish()
                   }, 1000)*/
            }
            ProductDetailStateType.Loaded -> {

                state.productEntity?.let {
                    mBinding.item = it
                    productEntity = it
                    mBinding.stockInitialValue.text = getRoundedOffStockValue(it.stock)
                    mBinding.numberStepperLayout.etNumber.isEnabled = true
                    val measurementUnit: String
                    if (productEntity.measurementName != null)
                        measurementUnit = productEntity.measurementName
                    else
                        measurementUnit = ""

                    val totalStock = getRoundedOffStockValue(productEntity.stock)
                    total_stock.text = "Total stok " + totalStock + " " + measurementUnit
                }

                btn_confirm.setOnClickListener { v: View? ->
                    try {
                        var qty = 0.0
                        InputUtils.hideKeyboardFrom(context, mBinding.numberStepperLayout.etNumber)
                        if (!numberStepperLayout.etNumber.text.toString().isNullOrEmpty())
                            qty = Utility.extractAmountFromText(numberStepperLayout.etNumber.text.toString())

                        if (qty != 0.0) {
                            val propBuilder = AppAnalytics.PropBuilder()
                            propBuilder.put(AnalyticsConst.ENTRY_POINT, entryPoint)
                            propBuilder.put(AnalyticsConst.OLD_STOCK, state.productEntity?.stock)
                            propBuilder.put(AnalyticsConst.STOCK_CHANGE, mBinding.numberStepperLayout.etNumber.text.toString())
                            propBuilder.put(AnalyticsConst.MINIMUM_STOCK, state.productEntity?.minimumStock)
                            propBuilder.put(AnalyticsConst.MEASUREMENT_UNIT, state.productEntity?.measurementName)
                            propBuilder.put(AnalyticsConst.PRODUCT_ID, state.productEntity?.productId)
                            if (spinner.text.toString().equals(getString(R.string.add_stock))) {
                                propBuilder.put(AnalyticsConst.OPERATION, AnalyticsConst.ADD_STOCK)
                            } else if (spinner.text.toString().equals(getString(R.string.remove_stock))) {
                                propBuilder.put(AnalyticsConst.OPERATION, AnalyticsConst.REMOVE_STOCK)
                            }
                            propBuilder.put(AnalyticsConst.QUANTITY_TYPE, Utility.getQuantityTypeFromTotalStock(qty))
                            AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_EDIT_STOCK, propBuilder)
                            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_INVENTORY_EDIT_STOCK, requireActivity())
                            // update product only if it's tracking is enabled
//                            if (state.productEntity?.trackInventory == AppConst.INVENTORY_TRACKING_ENABLED) {
//
//                            }
                            ProductRepository.getInstance(activity).updateStockQuantity(qty, state.productEntity, getOperationType(), null, 0.0, 0.0, "")
                            if (dialog != null) dialog!!.dismiss()
                        } else
                            NotificationUtils.alertError(getString(R.string.alert_error_message_on_empty_stock))

                    } catch (e: Exception) {
                        e.printStackTrace()
                        if (dialog != null) dialog!!.dismiss()
                    }


                }

            }
        }
    }


    private fun getOperationType(): InventoryOperationType? {
        var operationType: InventoryOperationType? = null
        if (spinner.text.toString().equals(getString(R.string.add_stock)))
            operationType = InventoryOperationType.ADD_STOCK
        else if (spinner.text.toString().equals(getString(R.string.remove_stock)))
            operationType = InventoryOperationType.REMOVE_STOCK

        return operationType;
    }


    private val textWatcher = object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {

        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            updateTotalStockValue()
        }
    }

    private fun updateTotalStockValue() {

        try {
            val measurementUnit: String
            val numberOfUnit: Double
            val stockValue = mBinding.numberStepperLayout.etNumber.text.toString()

            measurementUnit = if (productEntity.measurementName != null)
                productEntity.measurementName
            else
                ""

            numberOfUnit = if (stockValue.isEmpty())
                0.0
            else {
                Utility.extractAmountFromText(stockValue)
            }

            if (spinner.text.toString() == getString(R.string.add_stock)) {
                val totalStock = productEntity.stock + numberOfUnit
                total_stock.text = "Total stok " + getRoundedOffStockValue(totalStock) + " " + measurementUnit
            } else if (spinner.text.toString() == getString(R.string.remove_stock)) {
                val totalStock = productEntity.stock - numberOfUnit
                total_stock.text = "Total stok " + getRoundedOffStockValue(totalStock) + " " + measurementUnit
            }
        } catch (e: Exception) {
            Log.v("error message", e.message.toString())
        }


    }

    private fun getRoundedOffStockValue(totalStock: Double): String {
        val df = DecimalFormat("#####.###")
        return if (totalStock == floor(totalStock) && !totalStock.isInfinite()) {
            totalStock.roundToInt().toString().replace(".", ",")
        } else {
            df.format(totalStock).replace(".", ",")
        }
    }

}