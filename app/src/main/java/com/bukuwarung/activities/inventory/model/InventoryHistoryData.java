package com.bukuwarung.activities.inventory.model;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.entity.InventoryOperationType;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.Utility;

public class InventoryHistoryData extends DataHolder {
    private String name;
    private String stok;
    private Double currentStock;
    private int minStock;

    public int tag = Tag.INVENTORY_HISTORY_ITEM;
    private Double updateStock;
    private String operationType;
    private long dateTime;
    private String transactionId;
    private String hargaValue;

    public InventoryHistoryData() {
        setTag(Tag.INVENTORY_HISTORY_ITEM);
    }

    public String getName() {
        if (operationType.equalsIgnoreCase(InventoryOperationType.ADD_STOCK.name()))
            return "Penambahan Stok";
        else if (operationType.equalsIgnoreCase(InventoryOperationType.REMOVE_STOCK.name()))
            return "Pengurangan Stok";
        else if (operationType.equalsIgnoreCase(InventoryOperationType.SALE_TRANSACTION.name()))
            return "Penjualan";
        else if (operationType.equalsIgnoreCase(InventoryOperationType.EXPENSE_TRANSACTION.name()))
            return "Pembelian";
        else if (operationType.equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_BELI.name()))
            return "Perubahan Harga Beli";
        else if (operationType.equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_JUAL.name()))
            return "Perubahan Harga Jual";
        return "";
    }

    public String getStok() {
        if (operationType.equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_BELI.name()))
            return "Harga Beli";
        else if (operationType.equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_JUAL.name()))
            return "Harga Jual";
        else
            {return "Stok";}
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setStok(String stok) {
        this.stok = stok;
    }
     public void setHargaValue(String hargaValue) {
        this.hargaValue = hargaValue;
    }

    public String getHargaValue() {
        return hargaValue;
    }

    public Double getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(Double currentStock) {
        this.currentStock = currentStock;
    }

    public int getMinStock() {
        return minStock;
    }

    public void setMinStock(int minStock) {
        this.minStock = minStock;
    }

    public Double getUpdateStock() {
        return updateStock;
    }

    public void setUpdateStock(Double updateStock) {
        this.updateStock = updateStock;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public long getDateTime() {
        return dateTime;
    }

    public void setDateTime(long dateTime) {
        this.dateTime = dateTime;
    }

    public String getReadableTimeStr() {
        return DateTimeUtils.timestampToDatetime(dateTime);
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}
