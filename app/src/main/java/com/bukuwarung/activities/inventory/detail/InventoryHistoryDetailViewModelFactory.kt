package com.bukuwarung.activities.inventory.detail

import android.os.Bundle
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.savedstate.SavedStateRegistryOwner
import com.bukuwarung.database.repository.InventoryRepository
import com.bukuwarung.database.repository.ProductRepository

class InventoryHistoryDetailViewModelFactory(
        private val owner: SavedStateRegistryOwner,
        private val inventoryRepository: InventoryRepository,
        private val productRepository: ProductRepository,
        private val productId: String,
        defaultArgs: Bundle? = null
) : AbstractSavedStateViewModelFactory(owner, defaultArgs) {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(
            key: String,
            modelClass: Class<T>,
            handle: SavedStateHandle
    ): T {
        return InventoryHistoryDetailViewModel(
                inventoryRepository, productRepository,
                productId
        ) as T
    }
}