package com.bukuwarung.activities.inventory.detail

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.inventory.adapter.InventoryHistoryAdapter
import com.bukuwarung.activities.inventory.dialog.DeleteProductDialog
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.moe.trackEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.repository.InventoryRepository
import com.bukuwarung.database.repository.ProductRepository
import com.bukuwarung.databinding.ActivityInventoryHistoryDetailsBinding
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.tutor.view.OnboardingWidget.Companion.createInstance
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import kotlinx.android.synthetic.main.activity_cash_transaction_detail.closeBtn
import kotlinx.android.synthetic.main.activity_inventory_history_details.*

class InventoryHistoryDetailActivity : AppActivity(), OnboardingWidget.OnboardingWidgetListener {

    internal lateinit var viewModel: InventoryHistoryDetailViewModel
        private set
    private var binding: ActivityInventoryHistoryDetailsBinding? = null

    internal lateinit var handler: Handler
    lateinit var productId: String
    private var inventoryHistoryAdapter: InventoryHistoryAdapter? = null
    private var inventoryHistoryList: ArrayList<DataHolder>? = ArrayList()

    /**
     * Remote config
     */
    private var isStockToggleEnabled = RemoteConfigUtils.isStockToggleEnabled()

    private var oldStockActiveStatus = AppConst.INVENTORY_TRACKING_ENABLED

    private var editStockRequestCode = 111

    private fun initViewModel() {
        productId = intent.getStringExtra(PRODUCT_ID_PARAM) ?: "-"

        val factory = InventoryHistoryDetailViewModelFactory(
            this, InventoryRepository.getInstance(this),
            ProductRepository.getInstance(this),
            productId
        )

        this.viewModel = ViewModelProviders.of(this, factory)
            .get(InventoryHistoryDetailViewModel::class.java)
    }

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        initViewModel()
        handler = Handler()
        setupView()
    }

    private fun setupView() {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_inventory_history_details)
        customerRecyclerView.setHasFixedSize(true)
        closeBtn.setOnClickListener { onBackPressed() }
        inventoryHistoryAdapter = InventoryHistoryAdapter(inventoryHistoryList)
        customerRecyclerView.adapter = inventoryHistoryAdapter
        val mLayoutManager: RecyclerView.LayoutManager = LinearLayoutManager(this)
        customerRecyclerView.layoutManager = mLayoutManager

        if (!OnboardingPrefManager.getInstance()
                .getHasFinishedForId(OnboardingPrefManager.TOUR_STOCK_TAB_ADD_BTN)
        ) {
            createInstance(
                this,
                this,
                OnboardingPrefManager.TOUR_STOCK_TAB_ADD_BTN,
                manageStockBtn,
                R.drawable.onboarding_attention,
                "",
                getString(R.string.body_add_stock),
                getString(R.string.understand),
                FocusGravity.CENTER,
                ShapeType.ROUND_RECT,
                2,
                2,
                true
            )
        }

        btn_manage_price.setOnClickListener {
            // this button only visible if the product doesn't have unit price (0product)
            val evenProp = AppAnalytics.PropBuilder().put(AnalyticsConst.EDIT_PRODUCT_PRICE_SOURCE, AnalyticsConst._0PRODUCT)
            AppAnalytics.trackEvent(AnalyticsConst.INVENTORY_EDIT_PRICE_CLICK, evenProp)

            val editIntent = EditStockActivity.getNewIntent(this, productId, bookId = "", editSource = AnalyticsConst._0PRODUCT)
            startActivity(editIntent)
        }
        observeData()
    }

    private fun observeData() {
        viewModel.stateData.observe(this, Observer { state ->
            showBasedOnState(state)
        })
    }

    /**
     * Show data based on state.
     */
    private fun showBasedOnState(state: StockDetailState) {
        when (state.currentProductState) {
            ProductDetailStateType.Loading -> {
                loadingContainer.visibility = View.VISIBLE
                notFoundContainer.visibility = View.GONE
                mainContainer.visibility = View.GONE
                editStockBtn.visibility = View.GONE
                deleteStockBtn.visibility = View.GONE
                manageStockBtn.visibility = View.GONE
            }
            ProductDetailStateType.NotFound -> {
                loadingContainer.visibility = View.GONE
                notFoundContainer.visibility = View.VISIBLE
                mainContainer.visibility = View.GONE
                editStockBtn.visibility = View.GONE
                deleteStockBtn.visibility = View.GONE
                manageStockBtn.visibility = View.GONE
            }
            ProductDetailStateType.Loaded -> {
                loadingContainer.visibility = View.GONE
                notFoundContainer.visibility = View.GONE
                mainContainer.visibility = View.GONE
                editStockBtn.visibility = View.VISIBLE
                deleteStockBtn.visibility = View.VISIBLE
                manageStockBtn.visibility = View.VISIBLE
                state.productEntity?.let {
                    setTextData(it)
//                    if (it.trackInventory == AppConst.INVENTORY_TRACKING_ENABLED) {
                        loadInventoryData(state)
//                    }
                }

                editStockBtn.setOnClickListener { _ ->
                    if (isStockToggleEnabled) {
                        val intent = EditStockActivity.getNewIntent(
                            this, productId, "", isInventoryEditFlow = true)
                        startActivityForResult(intent, editStockRequestCode)
                    } else {
                        goToEditPage()
                    }
                }

                label_favourite.setOnClickListener {
                    trackFavoriteButtonClick(state.productEntity?.favourite ?: false)
                    state.productEntity?.favourite = state.productEntity?.favourite?.xor(true)
                    ProductRepository.getInstance(this).updateFavourite(state.productEntity)
                }

                deleteStockBtn.setOnClickListener { _ -> showDeleteDialog() }
                manageStockBtn.setOnClickListener { _ -> goToManageBottomSheet() }

            }
        }
    }

    private fun trackFavoriteButtonClick(isFav : Boolean) {
        trackEvent(AnalyticsConst.MARK_FAVOURITE_PRODUCT){
            addEntryPointProperty(AnalyticsConst.CASHIER_MODE)
            val action = if (isFav) AnalyticsConst.REMOVE else AnalyticsConst.MARK
            addProperty(AnalyticsConst.ACTION to action)
        }
    }


    private fun loadInventoryData(state: StockDetailState) {
        when (state.inventoryDetailStateType) {
            InventoryDetailStateType.Loading -> {
                loadingContainer.visibility = View.VISIBLE
                notFoundContainer.visibility = View.GONE
                mainContainer.visibility = View.GONE
            }
            InventoryDetailStateType.NotFound -> {
                loadingContainer.visibility = View.GONE
                notFoundContainer.visibility = View.VISIBLE
                mainContainer.visibility = View.GONE
            }
            InventoryDetailStateType.Loaded -> {
                loadingContainer.visibility = View.GONE
                if (state.inventoryHistoryData?.size == 0) {
                    notFoundContainer.visibility = View.VISIBLE;
                    customerRecyclerView.visibility = View.INVISIBLE;
                } else {
                    notFoundContainer.visibility = View.GONE;
                    customerRecyclerView.visibility = View.VISIBLE;
                }
                mainContainer.visibility = View.VISIBLE

                if (state.productEntity?.unitPrice == 0.0){
                    emptyUnitPriceContaienr.visibility = View.VISIBLE
                    manageStockBtn.visibility = View.GONE
                }

                if ((isStockToggleEnabled && oldStockActiveStatus == AppConst.INVENTORY_TRACKING_ENABLED)
                    || !isStockToggleEnabled) {
                    inventoryHistoryAdapter?.updateData(state.inventoryHistoryData)
                }
            }
        }
    }


    private fun setTextData(productEntityValue: ProductEntity) {
        binding?.item = productEntityValue
        oldStockActiveStatus = productEntityValue.trackInventory
        binding?.stockInitialValue?.text =
            "Stok: " + Utility.getRoundedOffPrice(productEntityValue.stock)
        if (productEntityValue.stock >= 0) {
            binding?.stockInitialValue?.setTextColor(ContextCompat.getColor(this, R.color.black_80))
        } else {
            binding?.stockInitialValue?.setTextColor(ContextCompat.getColor(this, R.color.out_red))
        }
        if (productEntityValue.favourite) {
            binding?.labelFavourite?.background = binding?.labelFavourite?.context?.let {
                ContextCompat.getDrawable(
                    it, R.drawable
                        .bg_yellow_stroke_f7b500
                )
            }
            binding?.labelFavourite?.setCompoundDrawablesRelativeWithIntrinsicBounds(
                R.drawable.ic_yellow_fav,
                0,
                0,
                0
            )
        } else {
            binding?.labelFavourite?.background = binding?.labelFavourite?.context?.let {
                ContextCompat.getDrawable(
                    it, R.drawable
                        .button_round_cornerd_edit_stroke_pop_up
                )
            }
            binding?.labelFavourite?.setCompoundDrawablesRelativeWithIntrinsicBounds(
                R.drawable.ic_fav_grey,
                0,
                0,
                0
            )
        }
    }

    private fun goToManageBottomSheet() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.STOCK_DETAIL)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_INVENTORY_MANAGE_STOCK, propBuilder)
        val bottomSheetFragment = ManageStockBottomSheetFragment()

        val bundle = Bundle()
        bundle.putString("product_id", productId)
        bundle.putString("entry_point", AnalyticsConst.STOCK_DETAIL)
        bottomSheetFragment.arguments = bundle
        bottomSheetFragment.show(supportFragmentManager, bottomSheetFragment.tag)
    }

    /**
     * GO to edit page using product id.
     */
    private fun goToEditPage() {
        // events moved to BottomSheet
        val bottomSheetFragment = EditStockBottomSheetFragment()
        val bundle = Bundle()
        bundle.putString(EditStockBottomSheetFragment.PRODUCT_ID, productId)
        bundle.putString(
            EditStockBottomSheetFragment.ENTRY_POINT,
            AnalyticsConst.VIA_STOCK_MENU_VIEW_PRODUCT
        )
        bottomSheetFragment.arguments = bundle
        bottomSheetFragment.show(supportFragmentManager, bottomSheetFragment.tag)
    }

    /**
     * Show delete product.
     */
    private fun showDeleteDialog() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.START)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_DELETE_PRODUCT, propBuilder)
        val dialog = DeleteProductDialog(this) {
            if (it) {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CONFIRM)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_DELETE_PRODUCT, propBuilder)
                viewModel.stateData.removeObservers(this)
                ProductRepository.getInstance(this).deleteProductByProductId(productId)
                finish()
            } else {
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.CANCEL)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_DELETE_PRODUCT, propBuilder)
            }
        }
        dialog.show()
    }


    companion object {
        const val PRODUCT_ID_PARAM = "ProductId"

        fun getNewIntent(origin: Context, productId: String): Intent {
            val intent = Intent(origin, InventoryHistoryDetailActivity::class.java)
            intent.putExtra(PRODUCT_ID_PARAM, productId)
            return intent
        }
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        OnboardingPrefManager.getInstance().setHasFinishedForId(id)
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        // callback triggered when onboarding coachmark is clicked
    }

}

