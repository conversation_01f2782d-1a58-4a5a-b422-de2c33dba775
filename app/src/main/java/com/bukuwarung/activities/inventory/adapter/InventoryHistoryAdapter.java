package com.bukuwarung.activities.inventory.adapter;


import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity;
import com.bukuwarung.activities.inventory.model.InventoryHistoryData;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.entity.InventoryOperationType;
import com.bukuwarung.databinding.RowItemInventoryDetailBinding;
import com.bukuwarung.utils.Utilities;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;

public class InventoryHistoryAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private ArrayList<? extends DataHolder> inventoryHistoryDataArrayList;

    public InventoryHistoryAdapter(ArrayList<? extends DataHolder> inventoryHistoryData) {
        this.inventoryHistoryDataArrayList = inventoryHistoryData;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == Tag.INVENTORY_HISTORY_ITEM) {
            RowItemInventoryDetailBinding binding = DataBindingUtil
                    .inflate(LayoutInflater.from(parent.getContext()), R.layout.row_item_inventory_detail,
                            parent, false);
            return new ViewHolder(binding);
        } else {
            View inflate = LayoutInflater.from(parent.getContext()).inflate(R.layout.activity_main_view_customer_end, parent, false);
            return new LastViewHolder(inflate);
        }
    }

    public int getItemViewType(int i) {
        return ((DataHolder) this.inventoryHistoryDataArrayList.get(i)).getTag();
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (holder.getItemViewType() == Tag.INVENTORY_HISTORY_ITEM) {

            InventoryHistoryData data = (InventoryHistoryData) inventoryHistoryDataArrayList.get(position);
            String transactionId = data.getTransactionId();

            ((ViewHolder) holder).binding.setItem(data);
            if (data.getOperationType().equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_JUAL.name())||
                    data.getOperationType().equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_BELI.name()))
            {
                ((ViewHolder) holder).binding.stockValue.setText(data.getHargaValue());

            }else {
                ((ViewHolder) holder).binding.stockValue.setText(Utility.getRoundedOffPrice(data.getCurrentStock()));

            }

            if (data.getOperationType().equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_JUAL.name())||
                    data.getOperationType().equalsIgnoreCase(InventoryOperationType.PERUBAHAN_HARGA_BELI.name()))

            {
                ((ViewHolder) holder).binding.amount.setText("");

            }else if (data.getOperationType().equalsIgnoreCase("ADD_STOCK") ||
                    data.getOperationType().equalsIgnoreCase("EXPENSE_TRANSACTION")) {
                ((ViewHolder) holder).binding.amount.setText("+" + Utility.getRoundedOffPrice(data.getUpdateStock()));
                ((ViewHolder) holder).binding.amount.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.in_green));
            } else {
                ((ViewHolder) holder).binding.amount.setText("-" + Utility.getRoundedOffPrice(data.getUpdateStock()));
                ((ViewHolder) holder).binding.amount.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.out_red));
            }

            if (!Utility.isBlank(transactionId)) {
                ((ViewHolder) holder).binding.tvTransactionId
                        .setText(Utilities.INSTANCE.getFormattedInvoiceId(transactionId)+" ");
            }

            ((ViewHolder) holder).binding.executePendingBindings();
        }
    }

    @Override
    public int getItemCount() {
        return inventoryHistoryDataArrayList.size();
    }

    public void updateData(ArrayList<? extends DataHolder> list) {
        inventoryHistoryDataArrayList = list;
        notifyDataSetChanged();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        final RowItemInventoryDetailBinding binding;

        public ViewHolder(RowItemInventoryDetailBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            binding.getRoot().setOnClickListener(view -> {
                InventoryHistoryData data = (InventoryHistoryData) inventoryHistoryDataArrayList.get(getAdapterPosition());
                String transactionId = data.getTransactionId();
                if (transactionId != null) {
                    Intent intent = CashTransactionDetailActivity.Companion.getNewIntent(view.getContext(), transactionId, false);
                    view.getContext().startActivity(intent);
                }
            });
        }
    }

    public final class LastViewHolder extends RecyclerView.ViewHolder {

        public LastViewHolder(final View view) {
            super(view);
        }
    }


}