package com.bukuwarung.activities.geolocation.view

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.databinding.ItemAddressLayoutBinding

class AddressAdapter(private val callback: (Address) -> Unit) :
    RecyclerView.Adapter<AddressViewHolder>() {
    private val data = mutableListOf<Address>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AddressViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemAddressLayoutBinding.inflate(inflater, parent, false)
        return AddressViewHolder(binding)
    }

    override fun onBindViewHolder(holder: AddressViewHolder, position: Int) {
        holder.bind(data[position], callback)
    }

    override fun getItemCount(): Int = data.size

    fun updateData(_data: List<Address>) {
        data.apply {
            clear()
            addAll(_data)
        }

        notifyDataSetChanged()
    }
}

class AddressViewHolder(private val binding: ItemAddressLayoutBinding) :
    RecyclerView.ViewHolder(binding.root) {
    fun bind(address: Address, callback: (Address) -> Unit) {
        binding.apply {
            tvAddress.text = address.name
            root.setOnClickListener { callback(address) }
        }
    }
}