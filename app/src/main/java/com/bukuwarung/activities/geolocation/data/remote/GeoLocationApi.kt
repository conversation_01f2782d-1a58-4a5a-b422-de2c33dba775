package com.bukuwarung.activities.geolocation.data.remote

import com.bukuwarung.activities.geolocation.data.model.AddressSuggestionResponse
import com.bukuwarung.data.restclient.ApiResponse
import retrofit2.http.GET
import retrofit2.http.Query

interface GeoLocationApi {
    @GET("/ac/api/v2/tkk/shipper/maps/areas")
    suspend fun getAddressSuggestion(
        @Query("countryCode") countryCode: String = "ID",
        @Query("input") input: String
    ): ApiResponse<AddressSuggestionResponse>
}