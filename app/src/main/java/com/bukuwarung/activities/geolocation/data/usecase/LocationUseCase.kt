package com.bukuwarung.activities.geolocation.data.usecase

import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.data.model.AddressSuggestionResponse
import com.bukuwarung.activities.geolocation.data.remote.GeoLocationApi
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.session.SessionManager
import javax.inject.Inject

class LocationUseCase @Inject constructor(
    private val api: GeoLocationApi
) {

    @Throws(Exception::class)
    suspend fun searchLocation(keyword: String): List<Address> {

        return when (val response = api.getAddressSuggestion(input = keyword)) {
            is ApiSuccessResponse -> {
                response.body.data
            }
            is ApiEmptyResponse -> emptyList()
            is ApiErrorResponse -> throw Exception(response.errorMessage)
        }
    }
}