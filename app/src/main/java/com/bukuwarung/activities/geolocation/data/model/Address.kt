package com.bukuwarung.activities.geolocation.data.model


import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize


@Parcelize
@Keep
data class Address(
    @SerializedName("city") var city: String,
    @SerializedName("district") var district: String,
    @SerializedName("id") val id: String,
    @SerializedName("name") var name: String,
    @SerializedName("postalCode") var postalCode: String,
    @SerializedName("province") var province: String,
    @SerializedName("subdistrict") var subDistrict: String,
    @SerializedName("full_address") var fullAddress: String? = "",
    @SerializedName("latitude") var latitude: Double? = null,
    @SerializedName("longitude") var longitude: Double? = null
)  : Parcelable

data class AddressSuggestionResponse(
    @SerializedName("result") val result: <PERSON><PERSON><PERSON>,
    @SerializedName("data") var data: List<Address>
)