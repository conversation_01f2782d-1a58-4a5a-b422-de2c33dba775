package com.bukuwarung.activities.geolocation.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModelEventState
import com.bukuwarung.activities.ViewModelEvent
import com.bukuwarung.activities.ViewModelState
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.data.usecase.LocationUseCase
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.location.UserLocationDto
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isEditingAddressForFirstTime
import kotlinx.coroutines.launch
import javax.inject.Inject

class GeoLocationViewModelFactory @Inject constructor(
    private val useCase: LocationUseCase,
    private val businessUseCase: BusinessUseCase,
    private val sessionManager: SessionManager
) :
    ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T =
        GeoLocationViewModel(useCase, businessUseCase, sessionManager) as T

}

class GeoLocationViewModel @Inject constructor(
    private val useCase: LocationUseCase,
    private val businessUseCase: BusinessUseCase,
    private val sessionManager: SessionManager
) : BaseViewModelEventState<GeoLocationViewModel.State, GeoLocationViewModel.Event>() {
    private var shouldSaveOnFullFlow: Boolean = false
    private var entryPoint : String = ""

    private var hasInternet: Boolean = true
        set(value) {
            field = value
            setState(State.OnNetworkStateChange(value))
        }

    private var address: Address? = null
        set(value) {
            field = value

            value?.let {
                setState(State.OnLocationSelected(it))
            }
        }

    sealed class State : ViewModelState {
        data class OnLocationResult(val locations: List<Address>) : State()
        data class OnLocationSelected(val address: Address) : State()
        object OnChangeAddressRequest : State()
        data class OnBusinessCompleted(val address: Address) : State()
        data class OnNetworkStateChange(val hasInternet: Boolean) : State()
    }

    init {
        hasInternet = Utility.hasInternet()
    }

    sealed class Event : ViewModelEvent {
        data class UseCurrentLocation(val currentLocation: UserLocationDto?) : Event()
        data class SearchLocation(val keyword: String) : Event()
        data class OnLocationSelected(val address: Address) : Event()
        object OnChangeAddressRequest : Event()
        object OnSaveButtonClick : Event()
        data class OnWardChanged(val ward: String) : Event()
        data class OnPostalCodeChanged(val postalCode: String) : Event()
        data class OnFullAddressEdited(val fullAddress: String) : Event()
        data class SetShouldSaveOnFullFlow(val shouldSave: Boolean) : Event()
        data class SetEntryPoint(val entryPoint: String) : Event()
    }

    override fun onEventReceipt(event: Event) {
        when (event) {
            is Event.SearchLocation -> searchLocation(event.keyword)
            is Event.OnLocationSelected -> address = event.address
            is Event.OnSaveButtonClick -> updateBusinessAdministrativeData()
            is Event.OnFullAddressEdited -> address?.fullAddress = event.fullAddress.trim()
            is Event.OnPostalCodeChanged -> address?.postalCode = event.postalCode
            is Event.OnWardChanged -> address?.subDistrict = event.ward
            is Event.UseCurrentLocation -> useCustomLocation(event.currentLocation)
            Event.OnChangeAddressRequest -> setState(State.OnChangeAddressRequest)
            is Event.SetShouldSaveOnFullFlow -> shouldSaveOnFullFlow = event.shouldSave
            is Event.SetEntryPoint -> entryPoint = event.entryPoint
        }
    }

    private fun useCustomLocation(currentLocation: UserLocationDto?) {
        if (currentLocation == null) {
            setState(State.OnLocationResult(emptyList()))
            return
        }

        val addressName = StringBuilder().apply {
            if (currentLocation.district.toString().trim().isNotEmpty()) {
                append(currentLocation.district).append(COMA_SEPARATOR)
            }

            if (currentLocation.city.toString().trim().isNotEmpty()) {
                append(currentLocation.city).append(COMA_SEPARATOR)
            }

            if (currentLocation.province.toString().trim().isNotEmpty()) {
                append(currentLocation.province).append(COMA_SEPARATOR)
            }

            if (currentLocation.postalCode.toString().trim().isNotEmpty()) {
                append(currentLocation.postalCode).append(DOT_CHAR)
            } else {
                append(DOT_CHAR)
            }

        }
            .toString()
            .takeIf { it.isNotEmpty() } ?: ""


        val fullAddress = StringBuilder().apply {
            if (currentLocation.streetName.toString().trim().isNotEmpty()) {
                append(currentLocation.streetName).append(COMA_SEPARATOR)
            }

            if (currentLocation.streetNumber.toString().trim().isNotEmpty()) {
                append(currentLocation.streetNumber).append(COMA_SEPARATOR)
            }

            if (currentLocation.subDistrict.toString().trim().isNotEmpty()) {
                append(currentLocation.subDistrict). append(DOT_CHAR)
            }else{
                append(DOT_CHAR)
            }


        }
            .toString()
            .takeIf {it.isNotEmpty() } ?: currentLocation.addressLine ?: ""


        address = Address(
            id = "",
            name = addressName,
            province = currentLocation.province ?: "",
            city = currentLocation.city ?: "",
            district = currentLocation.district ?: "",
            subDistrict = currentLocation.subDistrict ?: "",
            postalCode = currentLocation.postalCode ?: "",
            fullAddress = fullAddress
        )
    }

    private fun searchLocation(keyword: String) = viewModelScope.launch {
        hasInternet = Utility.hasInternet()
        if (!hasInternet) return@launch

        try {
            val location = useCase.searchLocation(keyword)
            setState(State.OnLocationResult(location))
        } catch (ex: Exception) {
            Log.d("LOCATIONS", ex.message.toString())
        }
    }

    private fun updateBusinessAdministrativeData() = viewModelScope.launch {
        address?.let {
            val bookId = sessionManager.businessId

            if (shouldSaveOnFullFlow) {
                businessUseCase.updateBusinessAdministrativeData(bookId, it)
            }
            setState(State.OnBusinessCompleted(it))
        }
    }

    private fun chooseLocationInBackground(subDistrict: String) = viewModelScope.launch {
        hasInternet = Utility.hasInternet()
        if (!hasInternet) return@launch

        try {
            val location = useCase.searchLocation(subDistrict)
            if (location.isNotEmpty()) {
                onEventReceipt(Event.OnLocationSelected(location.first()))
            } else {
                setState(State.OnLocationResult(emptyList()))
            }
        } catch (ex: Exception) {
            Log.d("LOCATIONS", ex.message.toString())
        }
    }

    fun getEntryPoint() = entryPoint

    fun isEditingAddressForFirstTime() =
        businessUseCase.getCurrentBusiness()?.isEditingAddressForFirstTime()

    companion object {
        private const val DOT_CHAR = "."
        private const val COMA_SEPARATOR = ", "
    }
}