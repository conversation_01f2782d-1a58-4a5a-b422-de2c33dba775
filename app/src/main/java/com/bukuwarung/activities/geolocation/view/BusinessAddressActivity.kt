package com.bukuwarung.activities.geolocation.view

import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.viewmodel.GeoLocationViewModel
import com.bukuwarung.activities.geolocation.viewmodel.GeoLocationViewModel.State.*
import com.bukuwarung.activities.geolocation.viewmodel.GeoLocationViewModelFactory
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityBusinessAddressBinding
import com.bukuwarung.utils.emptyLambda
import javax.inject.Inject

class BusinessAddressActivity : BaseActivity() {

    @Inject
    lateinit var vmFactory: GeoLocationViewModelFactory
    private val vm: GeoLocationViewModel by viewModels { vmFactory }

    private val binding: ActivityBusinessAddressBinding by lazy {
        ActivityBusinessAddressBinding.inflate(layoutInflater)
    }

    override fun setViewBinding() {
        setContentView(binding.root)
        setUpToolbarWithHomeUp(binding.tbAddress)
    }

    override fun setupView() {
        val shouldSaveOnFullFlow = intent.getBooleanExtra(SHOULD_SAVE_ON_FULL_FLOW, false)
        vm.onEventReceipt(GeoLocationViewModel.Event.SetShouldSaveOnFullFlow(shouldSaveOnFullFlow))

        changeFragment(GeoLocationFragment(), GeoLocationFragment.TAG)

        val entryPoint = intent.getStringExtra(ENTRY_POINT)
        entryPoint?.let {
            vm.onEventReceipt(GeoLocationViewModel.Event.SetEntryPoint(it))
        }
    }

    override fun subscribeState() {
        vm.state.observe(this, Observer { eventWrapper ->
            when (val state = eventWrapper.peekContent()) {
                is OnLocationSelected -> {
                    val isForAddress = intent.getBooleanExtra(IS_FOR_ADDRESS, false)
                    if (isForAddress) {
                        returnResultForAddress(state.address)
                    } else {
                        changeFragment(BusinessAddressFragment(), BusinessAddressFragment.TAG)
                    }
                }
                OnChangeAddressRequest -> changeFragment(
                    GeoLocationFragment(), GeoLocationFragment.TAG
                )
                is OnLocationResult -> emptyLambda
                is OnBusinessCompleted -> returnResultForAddress(state.address)
                else -> {}
            }
        })
    }

    private fun returnResultForAddress(address: Address) {
        val resultIntent = Intent().apply {
            putExtra(ADDRESS, address)
        }
        setResult(RESULT_OK, resultIntent)
        finish()
    }

    private fun changeFragment(fr: Fragment, tag: String) {
        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainer.id, fr, tag)
            .addToBackStack(tag)
            .commit()
    }

    override fun onBackPressed() {
        // finish activity if only 1 fragment left in the stack
        if (supportFragmentManager.backStackEntryCount == 1) {
            finish()
            return
        }

        supportFragmentManager.popBackStack()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    companion object {
        const val ADDRESS = "address"
        const val ADDRESS_REQUEST_CODE = 1133
        private const val ENTRY_POINT = "entry_point"
        private const val IS_FOR_ADDRESS = "is_for_address"
        private const val SHOULD_SAVE_ON_FULL_FLOW = "should_save_on_full_flow"

        /**
         * call this intent with [startActivityForResult]
         * will return a parcelable [Address] object with key [ADDRESS]
         */
        fun createIntent(context: Context, isForAddress: Boolean = false, saveOnFullFlow: Boolean = false, entryPoint: String = AnalyticsConst.NOTA): Intent {
            return Intent(context, BusinessAddressActivity::class.java).apply {
                putExtra(IS_FOR_ADDRESS, isForAddress)
                putExtra(SHOULD_SAVE_ON_FULL_FLOW, saveOnFullFlow)
                putExtra(ENTRY_POINT, entryPoint)
            }
        }
    }
}