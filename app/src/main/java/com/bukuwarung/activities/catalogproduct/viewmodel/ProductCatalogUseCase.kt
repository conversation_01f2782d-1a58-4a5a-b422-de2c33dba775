package com.bukuwarung.activities.catalogproduct.viewmodel

import com.bukuwarung.activities.catalogproduct.data.CatalogService
import com.bukuwarung.activities.catalogproduct.data.model.CatalogCategory
import com.bukuwarung.utils.RemoteConfigUtils
import javax.inject.Inject

class ProductCatalogUseCase @Inject constructor(val catalogService: CatalogService) {

    fun getCatalogCategories(): List<CatalogCategory> {
        return RemoteConfigUtils.ProductCatalog.getData()
    }

    fun getCatalogCategoryByName(name: String): CatalogCategory? {
        return RemoteConfigUtils.ProductCatalog.getData().find { it.name == name }
    }
}