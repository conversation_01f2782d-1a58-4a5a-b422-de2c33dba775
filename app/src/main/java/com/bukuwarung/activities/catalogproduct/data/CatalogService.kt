package com.bukuwarung.activities.catalogproduct.data

import com.bukuwarung.activities.catalogproduct.data.model.CatalogProductRequest
import com.bukuwarung.activities.catalogproduct.data.model.CatalogProductResponse
import com.bukuwarung.data.restclient.ApiResponse
import retrofit2.http.Body
import retrofit2.http.POST

interface CatalogService {
    @POST("/ac/api/v2/tkk/catalogue/products/filter")
    suspend fun getProduct(@Body body: CatalogProductRequest): ApiResponse<CatalogProductResponse>
}