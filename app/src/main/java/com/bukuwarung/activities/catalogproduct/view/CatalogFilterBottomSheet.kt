package com.bukuwarung.activities.catalogproduct.view

import android.content.Context
import android.view.LayoutInflater
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.bukuwarung.R
import com.bukuwarung.activities.catalogproduct.adapter.CatalogFilterAdapter
import com.bukuwarung.activities.catalogproduct.data.model.CatalogCategory
import com.bukuwarung.activities.catalogproduct.data.model.CatalogFilterType
import com.bukuwarung.activities.catalogproduct.data.model.CatalogSubCategory
import com.bukuwarung.databinding.CatalogFilterBSLayoutBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialog

class CatalogFilterBottomSheet<T : CatalogFilterType>(
    _context: Context,
    private val data: List<T>,
    private val selectedItem: String? = null,
    private val onItemClick: (T) -> Unit
) : BaseBottomSheetDialog(_context) {
    private lateinit var binding: CatalogFilterBSLayoutBinding

    override fun getResId(): Int {
        return 0
    }

    override fun getBinding(): ViewBinding {
        binding = CatalogFilterBSLayoutBinding.inflate(LayoutInflater.from(context))
        setupView()
        return binding
    }


    private fun setupView() {
        if (data.isEmpty()) return

        binding.apply {
            ivClose.setOnClickListener { dismiss() }

            val titleId = when (data[0]) {
                is CatalogCategory -> R.string.import_catalog
                else -> R.string.product_category
            }
            tvTitle.text = context.getString(titleId)

            rvCatalogFilter.apply {
                itemAnimator = DefaultItemAnimator()
                layoutManager = LinearLayoutManager(context)

                adapter = CatalogFilterAdapter<T>(selectedItem) {
                    dismiss()
                    onItemClick(it as T)
                }.apply {
                    setData(data)
                }

                if (data[0] is CatalogSubCategory) {
                    addItemDecoration(
                        DividerItemDecoration(
                            context,
                            RecyclerView.VERTICAL
                        )
                    )
                }

            }
        }

    }
}