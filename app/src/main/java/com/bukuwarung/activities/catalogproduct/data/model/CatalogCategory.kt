package com.bukuwarung.activities.catalogproduct.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.*


@Keep
abstract class CatalogFilterType() : Parcelable

@Keep
@Parcelize
data class CatalogCategory(
    var name: String,
    var image: String,
    val subcategory: ArrayList<CatalogSubCategory>
) : CatalogFilterType()


@Keep
@Parcelize
data class CatalogSubCategory(
    var name: String,
    var count: String,
    var isSelected: Boolean = false
) : CatalogFilterType()