package com.bukuwarung.activities.catalogproduct.viewmodel

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.catalogproduct.data.CatalogPagingSource
import com.bukuwarung.activities.catalogproduct.data.model.CatalogProduct
import com.bukuwarung.activities.catalogproduct.data.model.CatalogSubCategory
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.inventory.generateId
import com.bukuwarung.inventory.generateProductCode
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.wrapper.EventWrapper
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

class CatalogViewModel @Inject constructor(
    sessionManager: SessionManager,
    private val catalogUseCase: ProductCatalogUseCase,
    private val inventoryUseCase: ProductInventory
) : BaseViewModel() {
    private val currentBookId = sessionManager.businessId

    private val _state = MutableLiveData<EventWrapper<State>>()
    val state: LiveData<EventWrapper<State>> = _state

    private var categoryName: String = ""
    private var queryText: String = ""
    private var selectedSubCategory: CatalogSubCategory? = null

    private val _productData = MutableLiveData<PagingData<CatalogProduct>>()
    val productData: LiveData<PagingData<CatalogProduct>> get() = _productData

    private val selectedProducts = mutableListOf<CatalogProduct>()

    private val _pagingStatus = MutableLiveData<PagingStatus>()
    val pagingStatus : LiveData<PagingStatus> = _pagingStatus


    sealed class State {
        data class SetSelectedSubCategory(val subCategory: CatalogSubCategory) : State()
        data class SetSubCategories(val subCategories: ArrayList<CatalogSubCategory>) : State()
        data class SetSelectedProducts(val products: List<CatalogProduct>) : State()
        object ProductsAddedToInventory : State()
    }

    sealed class Event {
        data class OnCategoryNameSet(val categoryName: String) : Event()
        data class OnSubCategoryChanged(val newSubCategory: CatalogSubCategory) : Event()
        data class OnQueryTextChanged(val query: String) : Event()
        data class OnSelectedProductsChanged(val products: HashMap<String, CatalogProduct>) :
            Event()

        object OnSubmitProduct : Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnCategoryNameSet -> {
                setCategory(event.categoryName)
            }
            is Event.OnSubCategoryChanged -> changeSubCategory(event.newSubCategory)
            is Event.OnQueryTextChanged -> changeQueryText(event.query)
            is Event.OnSelectedProductsChanged -> setSelectedProducts(event.products)
            Event.OnSubmitProduct -> addProductsToInventory()
        }
    }

    private fun setCategory(name: String) {
        categoryName = name
        val subCategories =
            catalogUseCase.getCatalogCategoryByName(name)?.subcategory ?: return

        selectedSubCategory = subCategories.first()
        setState(State.SetSubCategories(subCategories))
        providePagingSource()
    }

    private fun changeSubCategory(newSubCategory: CatalogSubCategory) {
        selectedSubCategory = newSubCategory
        setState(State.SetSelectedSubCategory(selectedSubCategory!!))
        invalidateDataSource()
    }

    private fun changeQueryText(newQuery: String) {
        queryText = newQuery
        invalidateDataSource()
    }

    fun invalidateDataSource() {
        providePagingSource()
    }

    private fun providePagingSource() {
        val pager = Pager(
            config = PagingConfig(
                pageSize = RemoteConfigUtils.ProductCatalog.getPageSize(),
                prefetchDistance = 2
            ),
            pagingSourceFactory = {
                CatalogPagingSource(
                    service =  catalogUseCase.catalogService,
                    category = categoryName,
                    subCategory = selectedSubCategory?.name ?: "",
                    query = queryText,
                    productNames = inventoryUseCase.getAllProductNames(currentBookId),
                    pagingStatusLiveData = _pagingStatus
                )
            }
        )
        viewModelScope.launch {
            pager.flow.cachedIn(viewModelScope).collectLatest {
                _productData.value = it
            }
        }
    }

    private fun setSelectedProducts(products: HashMap<String, CatalogProduct>) {
        selectedProducts.apply {
            clear()
            addAll(products.map { it.value })
        }

        setState(State.SetSelectedProducts(selectedProducts))
    }

    fun getSelectedCategory() = selectedSubCategory

    private fun addProductsToInventory() = viewModelScope.launch {
        if (selectedProducts.isEmpty()) return@launch

        Log.d("products", selectedProducts.toString())
        val productEntities = selectedProducts.map { catalog->
            val product = ProductEntity().also {
                it.bookId = currentBookId
                it.productId = it.generateId()
                it.name = catalog.productName
                it.unitPrice = catalog.price.toDouble()
                it.trackInventory = AppConst.INVENTORY_TRACKING_ENABLED
                it.isImportedFromCatalog = true
                it.hasUpdatedPrice = false
                it.code = it.generateProductCode()
            }

            EntityHelper.fillProductMetadata(product)
        }

        inventoryUseCase.addProductsToInventory(productEntities)

        val eventProp = AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.ProductCatalog.NO_IMPORTED_PRODUCT, productEntities.size)
            put(AnalyticsConst.ProductCatalog.PRODUCT_CATEGORY, categoryName)
            put(AnalyticsConst.ProductCatalog.PRODUCT_SUB_CATEGORY, selectedSubCategory?.name ?: "")
        }
        AppAnalytics.trackEvent(AnalyticsConst.ProductCatalog.IMPORT_PRODUCT_CATALOG_SAVE, eventProp)
        setState(State.ProductsAddedToInventory)
    }

    private fun setState(state: State) {
        _state.value = EventWrapper(state)
    }
}