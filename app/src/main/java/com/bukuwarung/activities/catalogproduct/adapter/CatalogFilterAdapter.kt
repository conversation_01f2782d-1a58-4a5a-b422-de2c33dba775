package com.bukuwarung.activities.catalogproduct.adapter

import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.catalogproduct.data.model.CatalogCategory
import com.bukuwarung.activities.catalogproduct.data.model.CatalogFilterType
import com.bukuwarung.activities.catalogproduct.data.model.CatalogSubCategory
import com.bukuwarung.databinding.CatalogCategoryFilterItemBinding
import com.bukuwarung.databinding.CatalogSubCategoryFilterItemBinding
import com.bukuwarung.utils.getColorCompat
import com.bumptech.glide.Glide

class CatalogFilterAdapter<T : CatalogFilterType>(
    private val selectedItem: String? = null,
    private val onCLick: (CatalogFilterType) -> Unit
) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val data = mutableListOf<T>()

    override fun getItemCount(): Int = data.size

    override fun getItemViewType(position: Int): Int {
        return when (data[position]) {
            is CatalogCategory -> CATEGORY_FILTER
            else -> SUB_CATEGORY_FILTER
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            CATEGORY_FILTER -> CatalogCategoryViewHolder(
                CatalogCategoryFilterItemBinding.inflate(
                    layoutInflater,
                    parent,
                    false
                )
            )
            else -> CatalogSubCategoryViewHolder(
                CatalogSubCategoryFilterItemBinding.inflate(
                    layoutInflater,
                    parent,
                    false
                )
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val datum = data[position]) {
            is CatalogCategory -> {
                (holder as CatalogFilterAdapter<*>.CatalogCategoryViewHolder).bind(datum, onCLick)
            }
            is CatalogSubCategory -> (holder as CatalogFilterAdapter<*>.CatalogSubCategoryViewHolder).bind(
                datum,
                selectedItem,
                onCLick
            )
        }
    }

    fun setData(_data: List<T>) {
        data.apply {
            clear()
            addAll(_data)
        }

        notifyDataSetChanged()
    }

    inner class CatalogCategoryViewHolder(private val binding: CatalogCategoryFilterItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(category: CatalogCategory, onCLick: (CatalogCategory) -> Unit) {
            binding.apply {
                Glide.with(root.context)
                    .load(category.image)
                    .into(ivCategory)

                tvName.text = category.name

                root.setOnClickListener {
                    onCLick(category)
                }
            }
        }

    }

    inner class CatalogSubCategoryViewHolder(private val binding: CatalogSubCategoryFilterItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(subCategory: CatalogSubCategory, selectedItem: String?, onCLick: (CatalogSubCategory) -> Unit) {
            binding.apply {
                tvName.apply {
                    text = subCategory.name

                    selectedItem?.let {name->
                        if (name == subCategory.name){
                            typeface = Typeface.DEFAULT_BOLD
                            setTextColor(context.getColorCompat(R.color.colorPrimary))
                        }
                    }
                }
                tvBadgeCount.text = subCategory.count

                root.setOnClickListener {
                    onCLick(subCategory)
                }
            }
        }
    }

    companion object {
        private const val CATEGORY_FILTER = 1111
        private const val SUB_CATEGORY_FILTER = 33
    }
}
