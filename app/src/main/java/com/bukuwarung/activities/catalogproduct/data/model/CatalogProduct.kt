package com.bukuwarung.activities.catalogproduct.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class CatalogProductRequest(
    @SerializedName("category") val category: String,
    @SerializedName("limit") val limit: Int = 20,
    @SerializedName("offset") val offset: Int = 0,
    @SerializedName("subcategory") val subcategory: String,
    @SerializedName("text") val text: String
)

@Keep
data class CatalogProductResponse(
    @SerializedName("result") val result: Boolean,
    @SerializedName("data") val data : ArrayList<CatalogProduct>
)

@Keep
data class CatalogProduct(
    @SerializedName("productCode") val productCode: String,
    @SerializedName("productId") val productId: String,
    @SerializedName("category") val category: String,
    @SerializedName("subCategory") val subCategory: String,
    @SerializedName("productName") val productName: String,
    @SerializedName("price") val price: Long,
    @SerializedName("productBrand") val productBrand: String,
    @SerializedName("productDescription") val productDescription: String,
    @SerializedName("longDescription") val longDescription: String,
    @SerializedName("images") val images: ArrayList<ImageData>?
)

@Keep
data class ImageData(
    @SerializedName("imageUrl") val imageUrl: String
)