package com.bukuwarung.activities.catalogproduct.adapter

import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.catalogproduct.data.model.CatalogProduct
import com.bukuwarung.databinding.CatalogProductItemBinding
import com.bukuwarung.databinding.CatalogProductItemPlaceholderBinding
import com.bukuwarung.utils.asFormattedCurrency
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.boldText
import com.bumptech.glide.Glide

class CatalogProductAdapter(
    private val onProductSelectionChanged: (HashMap<String, CatalogProduct>) -> Unit
) : PagingDataAdapter<CatalogProduct, RecyclerView.ViewHolder>(diffCallback) {

    // HashMap<ProductId, ProductObject>
    private val selectedProducts = HashMap<String, CatalogProduct>()
    private var query: String = ""

    companion object {
        private const val DATA_HOLDER = 1111
        private const val PLACEHOLDER = 33

        private val diffCallback = object : DiffUtil.ItemCallback<CatalogProduct>() {
            override fun areItemsTheSame(oldItem: CatalogProduct, newItem: CatalogProduct): Boolean =
                oldItem.productId == newItem.productId

            override fun areContentsTheSame(oldItem: CatalogProduct, newItem: CatalogProduct): Boolean =
                oldItem == newItem
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)

        return when (viewType) {
            DATA_HOLDER -> CatalogProductViewHolder(
                CatalogProductItemBinding.inflate(inflater, parent, false)
            )
            else -> CatalogProductPlaceHolderViewHolder(
                CatalogProductItemPlaceholderBinding.inflate(inflater, parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val product = getItem(position)

        when (holder) {
            is CatalogProductViewHolder -> holder.bind(product, query)
            is CatalogProductPlaceHolderViewHolder -> holder.bind()
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position) == null) PLACEHOLDER else DATA_HOLDER
    }

    fun clearSelectedProduct() {
        selectedProducts.clear()
        broadcastSelectedProducts()
        notifyDataSetChanged()
    }

    fun updateQuery(_query: String) {
        query = _query
        notifyDataSetChanged()
    }

    private fun broadcastSelectedProducts() {
        onProductSelectionChanged(selectedProducts)
    }

    inner class CatalogProductViewHolder(private val binding: CatalogProductItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(product: CatalogProduct?, query: String) {
            binding.apply {
                shimmerLayout.visibility = (product == null).asVisibility()
                contentLayout.visibility = (product != null).asVisibility()

                product ?: return@apply

                tvProductName.text = SpannableStringBuilder(product.productName).boldText(query, ignoreCase = true)
                tvProductPrice.text = product.price.toDouble().asFormattedCurrency()

                product.images?.let { imageUrls ->
                    if (imageUrls.isNotEmpty()) {
                        Glide.with(root.context)
                            .load(imageUrls[0].imageUrl)
                            .into(imageProduct)
                    }
                }

                checkbox.apply {
                    isChecked = selectedProducts.containsKey(product.productId)

                    setOnCheckedChangeListener { cb, _ ->
                        if (cb.isPressed) {
                            isChecked =
                                if (selectedProducts.containsKey(product.productId)) {
                                    selectedProducts.remove(product.productId)
                                    false
                                } else {
                                    selectedProducts[product.productId] = product
                                    true
                                }
                            broadcastSelectedProducts()
                        }
                    }
                }
            }
        }
    }

    inner class CatalogProductPlaceHolderViewHolder(private val binding: CatalogProductItemPlaceholderBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.root.startShimmer()
        }
    }
}
