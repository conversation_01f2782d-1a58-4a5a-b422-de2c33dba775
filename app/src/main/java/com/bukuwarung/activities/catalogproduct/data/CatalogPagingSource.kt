package com.bukuwarung.activities.catalogproduct.data

import androidx.lifecycle.MutableLiveData
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.bukuwarung.activities.catalogproduct.data.model.CatalogProduct
import com.bukuwarung.activities.catalogproduct.data.model.CatalogProductRequest
import com.bukuwarung.activities.catalogproduct.data.model.CatalogProductResponse
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.payments.ppob.base.model.PagingStatus
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility

class CatalogPagingSource(
    private val service: CatalogService,
    private val category: String,
    private val subCategory: String,
    private val query: String,
    private val productNames: List<String>,
    private val pagingStatusLiveData: MutableLiveData<PagingStatus>
) : PagingSource<Int, CatalogProduct>() {

    private val itemsPerPage = RemoteConfigUtils.ProductCatalog.getPageSize()

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, CatalogProduct> {
        val page = params.key ?: 0

        // Create the request body
        val request = createRequestBody(page)

        pagingStatusLiveData.postValue(if (page == 0) PagingStatus.Loading else PagingStatus.LoadingNextPage)

        if (!Utility.hasInternet()) {
            pagingStatusLiveData.postValue(PagingStatus.NoInternet)
            return LoadResult.Error(Exception("No Internet Connection"))
        }

        return when (val response = service.getProduct(request)) {
            is ApiSuccessResponse -> {
                val data = filterData(response)

                if (data.isNotEmpty()) {
                    LoadResult.Page(
                        data = data,
                        prevKey = if (page == 0) null else page - 1,
                        nextKey = if (data.size < itemsPerPage) null else page + 1
                    )
                } else {
                    pagingStatusLiveData.postValue(if (page == 0) PagingStatus.Empty else PagingStatus.EmptyNextPage)
                    LoadResult.Page(
                        emptyList(),
                        prevKey = if (page == 0) null else page - 1,
                        nextKey = null
                    )
                }
            }
            is ApiEmptyResponse -> {
                pagingStatusLiveData.postValue(if (page == 0) PagingStatus.Empty else PagingStatus.EmptyNextPage)
                LoadResult.Page(
                    emptyList(),
                    prevKey = if (page == 0) null else page - 1,
                    nextKey = null
                )
            }
            is ApiErrorResponse -> {
                pagingStatusLiveData.postValue(PagingStatus.Error(response.errorMessage))
                LoadResult.Error(Exception(response.errorMessage))
            }
        }
    }

    override fun getRefreshKey(state: PagingState<Int, CatalogProduct>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }

    private fun filterData(
        response: ApiSuccessResponse<CatalogProductResponse>,
        postEmptyState: Boolean = true
    ): List<CatalogProduct> {
        return response.body.data.filter {
            it.productName !in productNames
        }.also {
            if (postEmptyState) {
                pagingStatusLiveData.postValue(if (it.isNotEmpty()) PagingStatus.Loaded(it.count()) else PagingStatus.Empty)
            }
        }
    }

    private fun createRequestBody(offsetKey: Int): CatalogProductRequest {
        return CatalogProductRequest(
            category = category.toLowerCase(),
            subcategory = subCategory.toLowerCase(),
            text = query,
            offset = itemsPerPage * offsetKey,
            limit = itemsPerPage
        )
    }
}
