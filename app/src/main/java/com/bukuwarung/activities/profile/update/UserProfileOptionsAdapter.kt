package com.bukuwarung.activities.profile.update

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.profile.ProfilePins
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.listen
import kotlinx.android.synthetic.main.user_profile_option_item.view.*


class UserProfileOptionsAdapter(val pins:List<ProfilePins>, val getSubMenuItem: (Int) -> Unit) : RecyclerView.Adapter<UserProfileOptionsAdapter.PinViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PinViewHolder {

        return PinViewHolder(parent).listen { pos, type ->
                getSubMenuItem(pos)
        }
    }


    override fun onBindViewHolder(holder: PinViewHolder, position: Int) {
        holder.bind(pins[position])
    }

    override fun getItemCount(): Int = pins.size

    class PinViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {

        constructor(parent: ViewGroup) : this(
                LayoutInflater.from(parent.context).inflate(
                        R.layout.user_profile_option_item,
                        parent, false
                )
        )

        fun bind(pin: ProfilePins) {
            with(itemView) {
                if(pin.readabaleName.equals("business_profile")){
                    if(FeaturePrefManager.getInstance().hasCompleteBusinessProfile()){
                        iv_completion_check.visibility = View.VISIBLE
                        AppAnalytics.setUserProperty("business_profile_status","complete")
                    }else{
                        iv_completion_check.visibility = View.GONE
                        AppAnalytics.setUserProperty("business_profile_status","partial")
                    }
                }else if(pin.readabaleName.equals("user_profile")){
                    if(FeaturePrefManager.getInstance().hasCompleteUserProfile()){
                        iv_completion_check.visibility = View.VISIBLE
                    }else{
                        iv_completion_check.visibility = View.GONE
                    }

                }else if(pin.readabaleName.equals("bank_profile")){
                    if(FeaturePrefManager.getInstance().hasBankAccount(SessionManager.getInstance().businessId)){
                        iv_completion_check.visibility = View.VISIBLE
                        AppAnalytics.setUserProperty("bank_account_saved","true")
                    }else{
                        iv_completion_check.visibility = View.GONE
                        AppAnalytics.setUserProperty("bank_account_saved","false")
                    }
                }else if(pin.readabaleName.equals("user_kyc")){
                    if(FeaturePrefManager.getInstance().hasCompletedKYC()){
                        iv_completion_check.visibility = View.VISIBLE
                        AppAnalytics.setUserProperty("kyc_status","VERIFIED")
                    }else{
                        iv_completion_check.visibility = View.GONE
                    }

                }else if (pin.readabaleName.equals("brick_integration")){
                    if(FeaturePrefManager.getInstance().isBrickIntegrated() && FeaturePrefManager.getInstance().isAutoRecordEnabled()){
                        iv_completion_check.visibility = View.VISIBLE
                    }else{
                        iv_completion_check.visibility = View.GONE
                    }
                }
                tv_user_profile_option_item.text = pin.name
            }
        }
    }
}