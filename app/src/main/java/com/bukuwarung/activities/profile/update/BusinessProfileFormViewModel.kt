package com.bukuwarung.activities.profile.update

import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.domain.config.AutoRecordUseCase
import com.bukuwarung.domain.config.StockConfigUseCase
import com.bukuwarung.payments.data.repository.PaymentsRepository
import com.bukuwarung.session.User
import kotlinx.coroutines.launch
import javax.inject.Inject

class BusinessProfileFormViewModel @Inject constructor(
    private val paymentsRepository: PaymentsRepository,
    private val businessRepository: BusinessRepository,
    private val stockConfigUseCase: StockConfigUseCase,
    private val autoRecordConfigUseCase: AutoRecordUseCase
) : BaseViewModel() {

    fun updateBusinessName(businessName: String) = viewModelScope.launch {
        paymentsRepository.updateBusinessName(businessName)
    }

    fun updateBusiness(businessName: String, bookType: Int, bookTypeName: String) = viewModelScope.launch {
        businessRepository.updateBusinessProfile(
            User.getUserId(),
            User.getDeviceId(),
            User.getBusinessId(),
            businessName,
            bookType,
            bookTypeName,
            ""
        )
    }

    fun setStockTabEnabled(isEnabled: Boolean){
        stockConfigUseCase.setStockTabEnabledFromSettings(stockConfigUseCase.STOCK_SELECTED)
    }

    fun setAutoRecordTabEnabled(isEnabled: Boolean){
        autoRecordConfigUseCase.setAutoRecordTabEnabledFromSettings(autoRecordConfigUseCase.AUTO_RECORD_SELECTED)
    }

}