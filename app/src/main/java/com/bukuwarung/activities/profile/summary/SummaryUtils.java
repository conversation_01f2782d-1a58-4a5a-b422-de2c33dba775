package com.bukuwarung.activities.profile.summary;

import android.content.Context;

import com.bukuwarung.R;
import com.bukuwarung.utils.Utility;

public class SummaryUtils {

    public static String composeDebitSummary(SummaryViewModel summaryData, String currency){
        StringBuilder sb = new StringBuilder();
        if (Double.valueOf(summaryData.getTotalDebit()).equals(Integer.valueOf(0))) {
            sb.append(currency);
            sb.append(" 0");
        } else {
            sb.append(currency);
            sb.append(' ');
            sb.append(Utility.formatCurrency(Double.valueOf(Math.abs(summaryData.getTotalDebit()))));
        }
        return sb.toString();
    }

    public static String composeCreditSummary(SummaryViewModel summaryData, String currency) {
        StringBuilder sb = new StringBuilder();
        if (Double.valueOf(summaryData.getTotalCredit()).equals(Integer.valueOf(0))) {
            sb.append(currency);
            sb.append(" 0");
        } else {
            sb.append(currency);
            sb.append(' ');
            sb.append(Utility.formatCurrency(Double.valueOf(Math.abs(summaryData.getTotalCredit()))));
        }
        return sb.toString();
    }

    public static String composeNetBalanceSummary(SummaryViewModel summaryData, String currency) {
        double totalDebit = summaryData.getTotalDebit() + summaryData.getTotalCredit();
        StringBuilder sb = new StringBuilder();
        sb.append(currency);
        sb.append(' ');
        sb.append(Utility.formatCurrency(Double.valueOf(Math.abs(totalDebit))));
        return sb.toString();
    }

    public static String composeBalanceExplainSummary(SummaryViewModel summaryData, String currency, Context context) {
        double totalDebit = summaryData.getTotalDebit() + summaryData.getTotalCredit();
        StringBuilder sb = new StringBuilder();
        if (totalDebit >= ((double) 0)) {
            sb.append("(");
            sb.append(context.getString(R.string.summary_explain_net_credit));
            sb.append(")");
        } else {
            sb.append("(");
            sb.append(context.getString(R.string.summary_explain_net_debit));
            sb.append(")");
        }
        return sb.toString();
    }
}
