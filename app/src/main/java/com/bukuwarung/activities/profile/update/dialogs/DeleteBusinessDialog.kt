package com.bukuwarung.activities.profile.update.dialogs

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BasePromptDialog

class DeleteBusinessDialog(
        context: Context,
        val businessName: String?,
        onPromptClicked: ((Boolean) -> Unit)
): BasePromptDialog(context, onPromptClicked) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setTitle(context.resources.getString(R.string.business_deletion_dialog_title))
        setContent(context.resources.getString(R.string.activity_book_deletion_dialog_body))
        setPositiveText(context.resources.getString(R.string.delete))
        setNegativeText(context.resources.getString(R.string.cancel))
    }

}