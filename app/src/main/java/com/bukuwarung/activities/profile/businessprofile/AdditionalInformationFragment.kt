package com.bukuwarung.activities.profile.businessprofile

import android.app.DatePickerDialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.DatePicker
import android.widget.RadioButton
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ACTION
import com.bukuwarung.constants.AnalyticsConst.ADDITIONAL_SECTION
import com.bukuwarung.constants.AnalyticsConst.EDIT
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.constants.AnalyticsConst.HomePage.SECTION
import com.bukuwarung.constants.AnalyticsConst.NEW
import com.bukuwarung.constants.AnalyticsConst.SECTION_STATUS
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.LayoutAdditionalInformationBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isNotNullOrEmpty
import dagger.android.support.AndroidSupportInjection
import java.util.*
import javax.inject.Inject

class AdditionalInformationFragment : BaseFragment(), DatePickerDialog.OnDateSetListener {
    private lateinit var binding: LayoutAdditionalInformationBinding
    private var selectSelfProduction = false
    private var selectbuyResell = false
    private var isFirstSectionSelected = false
    private var isSecondSectionSelected = false
    private var selectSellFromManufactures = false
    private var isMonthlyTurnOverSelected = false
    private var isNumberOfBranchSelected = false
    private var isNumberOfEmployessSelected = false
    private var isEstablishedYearSelected = false
    private var selectOtherSeller = false
    private var selectDirectBuyers = false
    private var bookEntity: BookEntity? = null
    private var bookId: String = ""
    private var isFromProfileTab = false
    private var isFromNotesMission = false

    @Inject
    lateinit var vmFactory: DetailedBusinessFormViewModelFactory
    private val viewModel: DetailedBusinessFormViewModel by activityViewModels { vmFactory }

    companion object {
        const val BOOK_ID = "BOOK_ID"
        const val IS_FROM_PROFILE_TAB = "IS_FROM_PROFILE_TAB"
        const val IS_FROM_NOTES_MISSION = "IS_FROM_NOTES_MISSION"
        fun instance(targetBookId: String,isFromProfileTab: Boolean = false,isFromNotesMission:Boolean=false): AdditionalInformationFragment {
            val fragment = AdditionalInformationFragment()
            val bundle = Bundle()
            bundle.putString(BOOK_ID, targetBookId)
            bundle.putBoolean(IS_FROM_PROFILE_TAB, isFromProfileTab)
            bundle.putBoolean(IS_FROM_NOTES_MISSION, isFromNotesMission)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onAttach(context: Context) {
        AndroidSupportInjection.inject(this)
        super.onAttach(context)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        arguments?.let {
            if (it.containsKey(BOOK_ID)) {
                bookId = requireArguments().getString(BOOK_ID).toString()
            }
            if (it.containsKey(IS_FROM_PROFILE_TAB)) {
                isFromProfileTab = requireArguments().getBoolean(
                    IS_FROM_PROFILE_TAB
                )
            }
            if (it.containsKey(IS_FROM_NOTES_MISSION)) {
                isFromNotesMission = requireArguments().getBoolean(
                    IS_FROM_NOTES_MISSION
                )
            }
        }

        bookEntity = viewModel.bookData
        binding = LayoutAdditionalInformationBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        //No imp
    }

    override fun subscribeState() {
        //No imp
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initRadioMechanism()
        initClickListeners()
        initSave()
        initBackButton()
    }

    private fun initBackButton()
    {
        binding.backBtn.setOnClickListener {
            showBackPromptDialog()
        }
    }

    private fun showBackPromptDialog() {
        val dialog = context?.let { context ->
            BackConfirmationDialog(
                context,
                "Keluar dari isi profil usaha?",
                "Tenang, data akan tersimpan saat keluar dan kamu bisa lanjutkan kembali nanti.",
                "Keluar",
                "Lanjut Mengisi"
            ) {
                val prop = AppAnalytics.PropBuilder()
                prop.put(
                    AnalyticsConst.ENTRY_POINT2,
                    if (isFromProfileTab) "progress_bar_lainnya" else "user_profile"
                )
                prop.put(AnalyticsConst.HomePage.SECTION, "additional")
                if (it) {
                    prop.put(AnalyticsConst.HomePage.BUTTON_CLICK_EVENT, "yes")
                    binding.monthlyTurnOverDropdown.spinner.dismiss()
                    binding.noOfEmployeesDropdown.spinner.dismiss()
                    binding.noOfBranchDropdown.spinner.dismiss()
                    activity?.onBackPressed()
                } else {
                    prop.put(AnalyticsConst.HomePage.BUTTON_CLICK_EVENT, "no")
                }
                AppAnalytics.trackEvent("click_back_button_business_profile", prop)
            }
        }
        dialog?.show()
    }

    override fun onPause() {
        super.onPause()
        binding.monthlyTurnOverDropdown.spinner.dismiss()
        binding.noOfEmployeesDropdown.spinner.dismiss()
        binding.noOfBranchDropdown.spinner.dismiss()
    }

    private fun sendCompleteBusinessProfileEvent() {
        val prop = AppAnalytics.PropBuilder()
        prop.put(ENTRY_POINT2,if (isFromProfileTab) "progress_bar_lainnya" else "user_profile")
        prop.put(
            ACTION,
            if (Utility.isSectionEmpty(bookEntity, Utility.profileAdditionalInfoFields)) NEW else EDIT
        )
        prop.put("business_category", bookEntity?.bookTypeName)
        prop.put("business_province", bookEntity?.province.toString())
        prop.put("business_city", bookEntity?.city.toString())
        prop.put("business_operating_days", bookEntity?.operatingDays?.toString())
        prop.put("product_production",if (selectSelfProduction) "self_made" else if (selectbuyResell) "buy_sell" else "self_made_and_buy_sell")
        prop.put("business_buyer", if (selectDirectBuyers) "direct_customer" else "reseller")
        prop.put("monthly_turn_over", binding.monthlyTurnOverDropdown.spinner.text)
        prop.put("business_creation_year",binding.layoutSinceWhenDropdown.spinner.text.toString())
        prop.put("no_of_employees", binding.noOfEmployeesDropdown.spinner.text.toString())
        prop.put("no_of_outlets", binding.noOfBranchDropdown.spinner.text.toString())
        prop.put(AnalyticsConst.ENTRY_POINT2,AnalyticsConst.MISSION_PAGE)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_COMPLETE_BUSINESS_PROFILE, prop)
        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_COMPLETE_BUSINESS_PROFILE, "profile_tab")
        FeaturePrefManager.getInstance().hasCompleteBusinessProfile(true)
    }

    private fun initSave() {
        with(binding) {
            btnSave.setOnClickListener {
                monthlyTurnOverDropdown.spinner.dismiss()
                noOfEmployeesDropdown.spinner.dismiss()
                noOfBranchDropdown.spinner.dismiss()
                updateBookEntity()
                sendSaveClickEvent()
                bookEntity?.updatedAt = Utility.getCurrentTime()
                bookEntity?.dirty = 1
                BusinessRepository.getInstance(Application.getAppContext())
                    .createBusinessWithDetails(bookEntity)
                if (Utility.isSectionFilled(
                        bookEntity,
                        Utility.profileBasicFields
                    ) && Utility.isSectionFilled(
                        bookEntity,
                        Utility.profileInfoFields
                    ) && isMonthlyTurnOverSelected && isNumberOfBranchSelected && isNumberOfEmployessSelected && isEstablishedYearSelected && isFirstSectionSelected && isSecondSectionSelected
                ) {
                    sendCompleteBusinessProfileEvent()
                    requireActivity().supportFragmentManager.beginTransaction().replace(
                        R.id.fragment_container,
                        BusinessProfileSuccessScreenFragment.instance(bookId, isFromProfileTab,isFromNotesMission)
                    ).commit()
                }
                else {
                    requireActivity().supportFragmentManager.beginTransaction().replace(
                        R.id.main_container,
                        NgBusinessProfileFragment.instance(bookId)
                    ).commit()
                    requireActivity().supportFragmentManager.popBackStack()
                }
            }
        }
    }

    private fun sendSaveClickEvent() {
        val prop = AppAnalytics.PropBuilder()
        prop.put(
            AnalyticsConst.ENTRY_POINT2,
            if (isFromProfileTab) "progress_bar_lainnya" else "user_profile"
        )
        prop.put(SECTION, ADDITIONAL_SECTION)
        prop.put(
            SECTION_STATUS,
            Utility.sectionCompletionStatus(bookEntity, Utility.profileAdditionalInfoFields)
        )
        prop.put(
            ACTION,
            if (Utility.isSectionEmpty(
                    bookEntity,
                    Utility.profileAdditionalInfoFields
                )
            ) NEW else EDIT
        )
        prop.put(
            "product_production",
            if (selectSelfProduction) "self_made" else if (selectbuyResell) "buy_sell" else "self_made_and_buy_sell"
        )
        prop.put("business_buyer", if (selectDirectBuyers) "direct_customer" else "reseller")
        prop.put(
            "monthly_turn_over",
            if (!binding.monthlyTurnOverDropdown.spinner.text.equals(getText(R.string.choose_monthly_turn_over))) {
                binding.monthlyTurnOverDropdown.spinner.text
            } else ""
        )
        prop.put(
            "business_creation_year",
            if (!binding.layoutSinceWhenDropdown.spinner.text.equals("Pilih tahun berdiri toko/usaha")) {
                binding.layoutSinceWhenDropdown.spinner.text.toString()

            } else ""
        )
        prop.put(
            "no_of_employees",
            if (!binding.noOfEmployeesDropdown.spinner.text.equals(getText(R.string.choose_no_of_employees))) {
                binding.noOfEmployeesDropdown.spinner.text
            } else ""
        )
        prop.put(
            "no_of_outlets",
            if (!binding.noOfBranchDropdown.spinner.text.equals(getText(R.string.select_no_of_branch))) {
                binding.noOfBranchDropdown.spinner.text
            } else ""
        )
        AppAnalytics.trackEvent("business_profile_section_saved", prop)
        Utilities.sendEventsToBackendWithBureau("save_business_profile_section", "profile_save")
    }

    private fun updateBookEntity() {
        with(binding) {
            if (!monthlyTurnOverDropdown.spinner.text.equals(getText(R.string.choose_monthly_turn_over))) {
                bookEntity?.monthlyTurnover = monthlyTurnOverDropdown.spinner.selectedIndex
                isMonthlyTurnOverSelected = true
            }
            if (!noOfBranchDropdown.spinner.text.equals(getText(R.string.select_no_of_branch))) {
                bookEntity?.outletCount = noOfBranchDropdown.spinner.selectedIndex
                isNumberOfBranchSelected = true
            }
            if (!noOfEmployeesDropdown.spinner.text.equals(getText(R.string.choose_no_of_employees))) {
                bookEntity?.empCount = noOfEmployeesDropdown.spinner.selectedIndex
                isNumberOfEmployessSelected = true
            }
            if(!layoutSinceWhenDropdown.spinner.text.equals("Pilih tahun berdiri toko/usaha")){
                bookEntity?.establishmentYear = layoutSinceWhenDropdown.spinner.text.toString()
                isEstablishedYearSelected = true
            }
            if (selectOtherSeller) {
                bookEntity?.productBuyer = otherSellers.rbValue.text.toString()
            } else if (selectDirectBuyers) {
                bookEntity?.productBuyer = directBuyers.rbValue.text.toString()
            }
            if (selectSelfProduction) {
                bookEntity?.production = selfProduction.rbValue.text.toString()
            } else if (selectbuyResell) {
                bookEntity?.production = buyResell.rbValue.text.toString()
            } else if (selectSellFromManufactures) {
                bookEntity?.production = sellFromManufactures.rbValue.text.toString()
            }
        }
    }

    private fun initClickListeners() {
        with(binding) {
            layoutSinceWhenDropdown.spinner.setOnClickListener {
                showTransactionDatePicker()
            }
        }
    }

    private fun initRadioMechanism() {
        with(binding) {
            if (bookEntity?.production?.isNotNullOrEmpty()?.or(false) == true) {
                isFirstSectionSelected = true
            }
            if (bookEntity?.productBuyer?.isNotNullOrEmpty()?.or(false) == true) {
                isSecondSectionSelected = true
            }
            selfProduction.rbValue.setOnClickListener {
                setSelectedColor(selfProduction.rbValue)
                setUnselectedColors(buyResell.rbValue, sellFromManufactures.rbValue)
                selectSelfProduction = true
                selectbuyResell = false
                selectSellFromManufactures = false
                isFirstSectionSelected = true
            }
            buyResell.rbValue.setOnClickListener {
                setSelectedColor(buyResell.rbValue)
                setUnselectedColors(selfProduction.rbValue, sellFromManufactures.rbValue)
                selectSelfProduction = false
                selectbuyResell = true
                selectSellFromManufactures = false
                isFirstSectionSelected = true
            }
            sellFromManufactures.rbValue.setOnClickListener {
                setSelectedColor(sellFromManufactures.rbValue)
                setUnselectedColors(selfProduction.rbValue, buyResell.rbValue)
                selectSelfProduction = false
                selectbuyResell = false
                selectSellFromManufactures = true
                isFirstSectionSelected = true
            }
            otherSellers.rbValue.setOnClickListener {
                setSelectedColor(otherSellers.rbValue)
                setUnselectedColors(directBuyers.rbValue)
                selectOtherSeller = true
                selectDirectBuyers = false
                isSecondSectionSelected = true
            }
            directBuyers.rbValue.setOnClickListener {
                setSelectedColor(directBuyers.rbValue)
                setUnselectedColors(otherSellers.rbValue)
                selectOtherSeller = false
                selectDirectBuyers = true
                isSecondSectionSelected = true
            }
        }
    }

    private fun setUnselectedColors(
        unselectFirst: RadioButton,
        unselectSecond: RadioButton? = null
    ) {
        unselectFirst.background = ContextCompat.getDrawable(
            unselectFirst.context,
            R.drawable.rounded_black_10_4dp
        )
        unselectFirst.setTextColor(
            ContextCompat.getColor(
                unselectFirst.context,
                R.color.black_40
            )
        )
        unselectSecond?.let {
            unselectSecond.background = ContextCompat.getDrawable(
                unselectSecond.context,
                R.drawable.rounded_black_10_4dp
            )
            unselectSecond.setTextColor(
                ContextCompat.getColor(
                    unselectSecond.context,
                    R.color.black_40
                )
            )
        }
    }

    private fun showTransactionDatePicker() {
        val instance = Calendar.getInstance()
        val yearPicker = YearPickerDialog.createInstance(instance.time)
        yearPicker.show(childFragmentManager, "YearPickerDialog")
    }

    private fun setSelectedColor(radioButton: RadioButton) {
        radioButton.background = ContextCompat.getDrawable(
            radioButton.context,
            R.drawable.bg_blue_outline_without_padding
        )
        radioButton.setTextColor(
            ContextCompat.getColor(
                radioButton.context,
                R.color.blue_60
            )
        )
    }

    private fun initView() {
        with(binding) {
            tvMakeYourOwnProduct.tvHeading.text = getString(R.string.make_your_own_product)
            selfProduction.rbValue.setText(getString(R.string.yes_self_produced))
            buyResell.rbValue.setText(getString(R.string.buy_and_resell))
            sellFromManufactures.rbValue.setText(getString(R.string.sell_products_yourself_and_manufacturers))
            tvWhoBuys.tvHeading.text = getString(R.string.who_buys_your_product)
            otherSellers.rbValue.setText(getString(R.string.other_sellers))
            directBuyers.rbValue.setText(getString(R.string.direct_buyer))
            monthlyTurnOver.tvHeading.text = getString(R.string.monthly_turn_over)
            monthlyTurnOver.tvLabel.hideView()
            monthlyTurnOverDropdown.spinner.setText(R.string.choose_monthly_turn_over)
            layoutSinceWhen.tvHeading.text = getString(R.string.since_when)
            layoutSinceWhen.tvLabel.hideView()
            if(bookEntity?.establishmentYear.isNotNullOrEmpty()) {
                layoutSinceWhenDropdown.spinner.setText(bookEntity?.establishmentYear)
            }else{
                layoutSinceWhenDropdown.spinner.setText(R.string.choose_year_of_establishment)
            }
            noOfBranch.tvHeading.text = getString(R.string.no_of_branch_stores)
            noOfBranch.tvLabel.hideView()
            noOfBranchDropdown.spinner.setText(getString(R.string.select_no_of_branch))
            noOfEmployees.tvHeading.text = getString(R.string.no_of_employees)
            noOfEmployees.tvLabel.hideView()
            noOfEmployeesDropdown.spinner.setText(getText(R.string.choose_no_of_employees))
            bookEntity?.monthlyTurnover?.let {
                if (it in 0..5) {
                    monthlyTurnOverDropdown.spinner.selectItemByIndex(it)
                }
            }
            bookEntity?.empCount?.let {
                if (it in 0..5) {
                    noOfEmployeesDropdown.spinner.selectItemByIndex(it)
                }
            }
            bookEntity?.outletCount?.let {
                if (it in 0..5) {
                    noOfBranchDropdown.spinner.selectItemByIndex(it)
                }
            }
        }
    }

    override fun onDateSet(view: DatePicker?, year: Int, month: Int, dayOfMonth: Int) {
        binding.layoutSinceWhenDropdown.spinner.text = year.toString()
    }
}