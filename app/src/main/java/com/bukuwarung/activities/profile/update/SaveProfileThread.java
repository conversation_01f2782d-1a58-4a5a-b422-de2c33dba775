package com.bukuwarung.activities.profile.update;

import com.bukuwarung.database.entity.UserProfileEntity;

final class SaveProfileThread implements Runnable {
    final EditUserProfileFragment editUserProfileFragment;
    final UserProfileEntity userProfileEntity;

    public SaveProfileThread(EditUserProfileFragment editUserProfileFragment, UserProfileEntity userProfileTemp) {
        this.editUserProfileFragment = editUserProfileFragment;
        this.userProfileEntity = userProfileTemp;
    }

    public final void run() {
        editUserProfileFragment.viewModel.saveUserProfileToRemote(userProfileEntity);
    }


}
