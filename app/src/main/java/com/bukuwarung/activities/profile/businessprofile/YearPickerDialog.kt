package com.bukuwarung.activities.profile.businessprofile

import android.app.AlertDialog
import android.app.DatePickerDialog
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import androidx.fragment.app.DialogFragment
import com.bukuwarung.databinding.DialogMonthYearPickerBinding
import java.util.*

class YearPickerDialog : DialogFragment() {

    companion object {
        private const val MAX_YEAR = 2099
        private const val MIN_YEAR = 1400
        private const val DATE = "date"

        fun createInstance(date: Date) =
            YearPickerDialog().apply {
                arguments = Bundle().apply { putLong(DATE, date.time) }
            }
    }

    private lateinit var binding: DialogMonthYearPickerBinding

    private var listener: DatePickerDialog.OnDateSetListener? = null
    private var date = Date()

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { listener = it as? DatePickerDialog.OnDateSetListener }
        if (context is DatePickerDialog.OnDateSetListener) listener = context
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        binding = DialogMonthYearPickerBinding.inflate(requireActivity().layoutInflater)
        val cal: Calendar = Calendar.getInstance().apply { time = date }

        arguments?.getLong(DATE)?.let { date = Date(it) }

        binding.pickerYear.run {
            val year = cal.get(Calendar.YEAR)
            minValue = MIN_YEAR
            maxValue = year
            value = year - 1
        }

        return AlertDialog.Builder(requireContext())
            .setView(binding.root)
            .setPositiveButton("Pilih") { _, _ -> listener?.onDateSet(null, binding.pickerYear.value, 1, 1) }
            .setNegativeButton("Cancel") { _, _ -> dialog?.cancel() }
            .create()
    }
}