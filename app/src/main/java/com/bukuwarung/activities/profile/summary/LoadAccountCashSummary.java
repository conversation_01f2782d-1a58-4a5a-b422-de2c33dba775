package com.bukuwarung.activities.profile.summary;

import android.content.Context;
import android.os.AsyncTask;

import com.bukuwarung.Application;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.session.User;

public final class LoadAccountCashSummary extends AsyncTask<Void, Void, SummaryViewModel> {

    private final Context mContext;
    final CashSummaryFragment parent;

    public LoadAccountCashSummary(CashSummaryFragment fragment, Context context) {
        this.parent = fragment;
        this.mContext = context;
    }

    public SummaryViewModel doInBackground(Void... voidArr) {
        SummaryViewModel summaryData = BusinessRepository.getInstance(Application.getAppContext()).getCreditSummary(User.getBusinessId());
        return summaryData;
    }


    public void onPostExecute(SummaryViewModel summaryData) {
        this.parent.displaySummary(summaryData, this.mContext);
    }
}
