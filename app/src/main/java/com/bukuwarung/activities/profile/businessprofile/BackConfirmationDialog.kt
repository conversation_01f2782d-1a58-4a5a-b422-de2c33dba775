package com.bukuwarung.activities.profile.businessprofile

import android.content.Context
import android.os.Bundle
import com.bukuwarung.dialogs.base.BasePromptDialog

class BackConfirmationDialog(
        context: Context,
        private val textTitle: String,
        private val content: String,
        private val positiveText: String,
        private val negativeText: String,
        onPromptClicked: ((Boolean) -> Unit)
): BasePromptDialog(context, onPromptClicked) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setTitle(textTitle)
        setContent(content)
        setNegativeText(negativeText)
        setPositiveText(positiveText)
    }

}