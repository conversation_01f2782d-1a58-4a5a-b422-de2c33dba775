package com.bukuwarung.activities.profile.deeplink.handler

import android.content.Intent
import com.bukuwarung.activities.profile.update.dialogs.UserProfileActivity
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.neuro.api.Signal
import javax.inject.Inject

class EditUserProfileSignalHandler @Inject constructor() : BukuWarungSignalHandler() {
    override val paths: Set<String> = setOf("/edit-profile")

    override fun handle(signal: Signal) {
        val context = signal.context
        val navigator = signal.navigator

        checkIsLogin(context) {
            val intent = Intent(context, UserProfileActivity::class.java).apply {
                putExtra("show_edit_profile", true)
            }
            navigator.navigate(intent)
        }
    }
}
