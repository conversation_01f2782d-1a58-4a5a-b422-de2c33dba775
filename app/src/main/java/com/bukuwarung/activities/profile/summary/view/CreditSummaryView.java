package com.bukuwarung.activities.profile.summary.view;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.activities.profile.summary.SummaryViewModel;

public class CreditSummaryView {

    private TextView netBalanceTV;
    private TextView totalCreditAmountTV;
    private TextView totalDebitAmountTV;

    public CreditSummaryView(View view) {
        this.netBalanceTV = view.findViewById(R.id.netBalanceValue);
        this.totalCreditAmountTV = view.findViewById(R.id.totalCreditsValue);
        this.totalDebitAmountTV = view.findViewById(R.id.totalDebitsValue);
    }

    public void refreshValues(String netBalanceStr, String netExplainStr, String totalCreditAmountStr, String totalDebitAmountStr, SummaryViewModel summaryData, Context ctx){
        double totalDebit = summaryData.getTotalDebit() + summaryData.getTotalCredit();
        this.netBalanceTV.setText(netBalanceStr);
        this.totalCreditAmountTV.setText(totalCreditAmountStr);
        this.totalDebitAmountTV.setText(totalDebitAmountStr);
        if (totalDebit >= ((double) 0)) {
            this.netBalanceTV.setTextColor(ctx.getResources().getColor(R.color.color_credit));
        }else{
            this.netBalanceTV.setTextColor(ctx.getResources().getColor(R.color.color_debit));
        }
    }
}
