package com.bukuwarung.activities.profile.deeplink.handler

import android.content.Intent
import com.bukuwarung.activities.profile.businessprofile.NgBusinessProfileActivity
import com.bukuwarung.activities.profile.businessprofile.NgBusinessProfileFragment.Companion.BOOK_ID
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.neuro.api.Signal
import javax.inject.Inject

class NgBusinessProfileSignalHandler @Inject constructor() : BukuWarungSignalHandler() {
    override val paths: Set<String> = setOf("/business/profile")

    override fun handle(signal: Signal) {
        val context = signal.context
        val query = signal.query
        val navigator = signal.navigator
        val businessId = extractQuery(query, name = "business_id") ?: ""

        checkIsLogin(context) {
            val intent = Intent(context, NgBusinessProfileActivity::class.java).apply {
                putExtra(BOOK_ID, businessId)
            }
            navigator.navigate(intent)
        }
    }
}
