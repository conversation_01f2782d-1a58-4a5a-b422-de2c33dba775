package com.bukuwarung.activities.profile.summary;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

public class SummaryPageAdapter extends FragmentStatePagerAdapter {
    public SummaryPageAdapter(FragmentManager paramFragmentManager) {
        super(paramFragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
    }

    public int getCount() {
        return 2;
    }

    public void reload(){
        notifyDataSetChanged();
    }

    public Fragment getItem(int paramInt) {
        if(paramInt == 0) {
            return new CreditSummaryFragment();
        }else{
            return new CashSummaryFragment();
        }
    }
}
