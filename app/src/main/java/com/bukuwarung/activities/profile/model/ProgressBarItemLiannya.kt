package com.bukuwarung.activities.profile.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class ProgressBarItemLiannya(

//	@field:SerializedName("itemTitle")
	val itemTitle: String? = null,

//	@field:SerializedName("failedTitle")
	val failedTitle: String? = null,

//	@field:SerializedName("successSubtitle")
	val successSubtitle: String? = null,

//	@field:SerializedName("showOrHide")
	val showOrHide: Boolean? = null,

//	@field:SerializedName("rank")
	val rank: Int? = null,

//	@field:SerializedName("emptySubtitle")
	val emptySubtitle: String? = null,

//	@field:SerializedName("inProgessSubtitle")
	val inProgessSubtitle: String? = null,

//	@field:SerializedName("type")
	val type: String? = null,

//	@field:SerializedName("sucessChipText")
	val successChipText: String? = null,

	val deeplinkUrl: String? = null,

	val imageUrl: String? = null
): Parcelable
