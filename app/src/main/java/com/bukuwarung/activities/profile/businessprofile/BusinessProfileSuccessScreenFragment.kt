package com.bukuwarung.activities.profile.businessprofile

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.card.BusinessCardActivity
import com.bukuwarung.activities.card.newcard.NewBusinessCardActivity
import com.bukuwarung.activities.card.preview.LayoutToImageHandler
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.SOURCE
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.BusinessProfileSuccessScreenBinding
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.google.android.gms.tasks.TaskExecutors

class BusinessProfileSuccessScreenFragment: BaseFragment() {

    private lateinit var binding: BusinessProfileSuccessScreenBinding
    private var isFromProfileTab = false
    private var isFromNotesMission = false
    private var bookId: String = ""

    companion object {
        const val BOOK_ID = "BOOK_ID"
        const val IS_FROM_PROFILE_TAB = "IS_FROM_PROFILE_TAB"
        const val IS_FROM_NOTES_MISSION = "IS_FROM_NOTES_MISSION"
        fun instance(
            targetBookId: String,
            isFromProfileTab: Boolean = false,
            isFromNotesMission: Boolean
        ): BusinessProfileSuccessScreenFragment {
            val fragment = BusinessProfileSuccessScreenFragment()
            val bundle = Bundle()
            bundle.putString(BOOK_ID, targetBookId)
            bundle.putBoolean(IS_FROM_PROFILE_TAB, isFromProfileTab)
            bundle.putBoolean(IS_FROM_NOTES_MISSION, isFromNotesMission)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        arguments?.let {
            if (it.containsKey(BOOK_ID)) {
                bookId = requireArguments().getString(BOOK_ID).toString()
            }
            if (it.containsKey(IS_FROM_PROFILE_TAB)) {
                isFromProfileTab = requireArguments().getBoolean(
                    IS_FROM_PROFILE_TAB
                )
            }
            if (it.containsKey(IS_FROM_NOTES_MISSION)) {
                isFromNotesMission = requireArguments().getBoolean(
                    IS_FROM_NOTES_MISSION
                )
            }
        }
        binding = BusinessProfileSuccessScreenBinding.inflate(inflater, container, false)
        var bookEntity = BusinessRepository.getInstance(context).getBusinessByIdSync(User.getBusinessId());
        binding.newBusinessCardPreviewShare.setData(bookEntity, RemoteConfigUtils.NewBusinessCard.getLockedDesigns().get(0));
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            editBusinessCard.setOnClickListener {
                val isNewBusinessCardEnabled = RemoteConfigUtils.NewBusinessCard.isEnabled()
                val clazz = if (isNewBusinessCardEnabled) NewBusinessCardActivity::class.java else BusinessCardActivity::class.java
                startActivity(Intent(context, clazz))
                if (activity?.isFinishing == false) {
                    activity?.finish()
                }
                val prop = AppAnalytics.PropBuilder()
                prop.put(AnalyticsConst.ENTRY_POINT2, "business_profile")
                AppAnalytics.trackEvent("edit_business_card", prop)
            }
            shareNormal.setOnClickListener {
                val prop = AppAnalytics.PropBuilder()
                prop.put(AnalyticsConst.ENTRY_POINT2, "business_profile")
                AppAnalytics.trackEvent("share_business_card", prop)
                ImageUtils.saveLayoutConvertedImage(newBusinessCardPreviewShare, false).continueWith(
                    TaskExecutors.MAIN_THREAD,
                    LayoutToImageHandler(activity, "com.whatsapp", null, true, false)
                )
            }
            btnOkay.setOnClickListener {
               if (isFromProfileTab) {
                   if (activity?.isFinishing == false)
                       activity?.finish()
               } else {
                   requireActivity().supportFragmentManager.beginTransaction().replace(
                       R.id.main_container,
                       NgBusinessProfileFragment.instance(bookId)
                   ).commit()

                   requireActivity().supportFragmentManager.popBackStack()
               }
            }
        }
    }

    override fun setupView(view: View) {
        // no imp
    }

    override fun subscribeState() {
        // no imp
    }
}