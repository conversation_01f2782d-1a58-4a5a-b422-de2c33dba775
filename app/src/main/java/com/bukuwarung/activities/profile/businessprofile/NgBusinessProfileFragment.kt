package com.bukuwarung.activities.profile.businessprofile

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.observe
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModelFactory
import com.bukuwarung.activities.profile.summary.view.BusinessOperationalInformationFragment
import com.bukuwarung.activities.profile.update.dialogs.DeleteBusinessDialog
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ACTION
import com.bukuwarung.constants.AnalyticsConst.ADDITIONAL_SECTION
import com.bukuwarung.constants.AnalyticsConst.BASIC_SECTION
import com.bukuwarung.constants.AnalyticsConst.EDIT
import com.bukuwarung.constants.AnalyticsConst.HomePage.SECTION
import com.bukuwarung.constants.AnalyticsConst.NEW
import com.bukuwarung.constants.AnalyticsConst.OPERATIONAL_SECTION
import com.bukuwarung.constants.AnalyticsConst.SECTION_STATUS
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.NgBusinessProfileActivityBinding
import com.bukuwarung.dialogs.PaymentAccountDialogPrompt
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog2
import com.bukuwarung.payments.constants.NameMatchingStatus
import com.bukuwarung.payments.constants.QrisAndKycStatus
import com.bukuwarung.payments.constants.VerificationStatus
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.io.File
import javax.inject.Inject

class NgBusinessProfileFragment : BaseFragment() {

    private var fromMission: Boolean = false
    private lateinit var binding: NgBusinessProfileActivityBinding
    @Inject
    lateinit var vmFactory: DetailedBusinessFormViewModelFactory
    private val businessFormViewModel: DetailedBusinessFormViewModel by activityViewModels { vmFactory }
    private var bookEntity: BookEntity? = null
    private var bookId: String = ""
    private var profilePicUri: Uri? = null
    private var profilePicFile: File? = null

    private var bitmap: Bitmap? = null


    companion object {
        const val BOOK_ID = "BOOK_ID"
        const val FROM_MISSION = "FROM_MISSION"
        fun instance(targetBookId: String,fromMission : Boolean= false): NgBusinessProfileFragment {
            val fragment = NgBusinessProfileFragment()
            val bundle = Bundle()
            bundle.putString(BOOK_ID, targetBookId)
            bundle.putBoolean(FROM_MISSION, fromMission)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun setupView(view: View) {
        initView()
        with(binding) {
            layoutBasicInfo.layoutHeading.tvChange.setOnClickListener {
                if (Utility.hasInternet()) {
                    val intent2 = CreateBusinessProfileActivity.createIntent(context, bookId, true)
                    startActivity(intent2)
                    val prop = AppAnalytics.PropBuilder()
                    prop.put(SECTION, BASIC_SECTION)
                    prop.put(ACTION, EDIT)
                    prop.put(
                        SECTION_STATUS,
                        Utility.sectionCompletionStatus(bookEntity, Utility.profileBasicFields)
                    )
                    AppAnalytics.trackEvent("edit_business_profile_section", prop)
                } else {
                    CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                        requireContext(),
                        requireActivity().supportFragmentManager
                    )
                }
            }
            backBtn.setOnClickListener {
                activity?.onBackPressed()
            }
            deleteBtn.setSingleClickListener {
                context?.let { it1 -> showDeletionDialog(it1) }
            }
            layoutOperationInfo.layoutHeading.tvChange.setOnClickListener {
                if (Utility.hasInternet()) {
                    activity?.supportFragmentManager?.beginTransaction()?.add(
                        R.id.fragment_container_test,
                        BusinessOperationalInformationFragment.instance(bookId),
                        "BusinessOperationalInformationFragment"
                    )?.addToBackStack("BusinessOperationalInformationFragment")?.commit()
                    val prop = AppAnalytics.PropBuilder()
                    prop.put(SECTION, OPERATIONAL_SECTION)
                    prop.put(
                        ACTION,
                        if (Utility.isSectionEmpty(
                                bookEntity,
                                Utility.profileInfoFields
                            )
                        ) NEW else EDIT
                    )
                    prop.put(
                        SECTION_STATUS,
                        Utility.sectionCompletionStatus(bookEntity, Utility.profileInfoFields)
                    )
                    AppAnalytics.trackEvent("edit_business_profile_section", prop)
                } else {
                    CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                        requireContext(),
                        requireActivity().supportFragmentManager
                    )
                }
            }
            layoutAdditionalInfo.layoutHeading.tvChange.setOnClickListener {
                if (Utility.hasInternet()) {
                    activity?.supportFragmentManager?.beginTransaction()?.add(
                        R.id.fragment_container_test,
                        AdditionalInformationFragment.instance(bookId),
                        "AdditionalInformationFragmen"
                    )?.addToBackStack("AdditionalInformationFragmen")?.commit()
                    val prop = AppAnalytics.PropBuilder()
                    prop.put(SECTION, ADDITIONAL_SECTION)
                    prop.put(
                        ACTION,
                        if (Utility.isSectionEmpty(
                                bookEntity,
                                Utility.profileAdditionalInfoFields
                            )
                        ) NEW else EDIT
                    )
                    prop.put(
                        SECTION_STATUS,
                        Utility.sectionCompletionStatus(
                            bookEntity,
                            Utility.profileAdditionalInfoFields
                        )
                    )
                    AppAnalytics.trackEvent("edit_business_profile_section", prop)
                } else {
                    CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                        requireContext(),
                        requireActivity().supportFragmentManager
                    )
                }
            }
        }

        PaymentPrefManager.getInstance().qrisData.observe(viewLifecycleOwner) {
            val bookId = SessionManager.getInstance().businessId
            val qrisBookId = it.qrisBookId
            /**
             * Allow book deletion only if final status is rejected due to QRIS or KYC being rejected
             * In case of Name matching or KYB failure, we still need to restrict book deletion
             */
            if (PaymentUtils.areChangesAllowedInQrisBook(it) && bookId == qrisBookId) {
                PaymentPrefManager.getInstance().setDeleteBookEnabled(true)
            }
        }
        businessFormViewModel.setBusinessData(bookEntity ?: BookEntity())
        bindProfilePicLayoutComponents()
    }

    override fun subscribeState() {
        BusinessRepository.getInstance(requireContext()).getBusinessById(bookId)
            .observe(viewLifecycleOwner, Observer { syncedBook ->
                bookEntity = syncedBook
                initView()
            })
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (arguments != null) {
            if (requireArguments().containsKey(BOOK_ID)) {
                bookId = requireArguments().getString(BOOK_ID).toString()
            }
            if(requireArguments().containsKey(FROM_MISSION)) {
                fromMission = requireArguments().getBoolean(FROM_MISSION,false)
            }
        }
        if (bookId.isNotNullOrEmpty()) {
            bookEntity = BusinessRepository.getInstance(context)?.getBusinessByIdSync(bookId)
            bookEntity?.profileCompletionProgress =
                Utility.calculateCompletionPercentage(bookEntity)
        } else {
            bookEntity = BookEntity()
        }
        binding = NgBusinessProfileActivityBinding.inflate(layoutInflater, container, false)
        if (bitmap == null && bookEntity?.businessImage.isNotNullOrEmpty()) {
            setProfilePic(bookEntity?.businessImage)
        } else {
            binding.profileSummaryLayout.profilePic.setImageBitmap(bitmap)
        }
        return binding.root
    }

    private fun initView() {
        with(binding) {

            setBasicInfoLayout()
            setOperationInfoLayout()
            setAdditionalInfoLayout()
            val profileCompletionProgress= Utility.calculateCompletionPercentage(bookEntity)
                if (profileCompletionProgress >= 100) {
                    profileSummaryLayout.progressProfile.hideView()
                    profileSummaryLayout.tvProfilePercentage.hideView()
                    profileSummaryLayout.completionBadge.showView()
                } else {
                    profileSummaryLayout.progressProfile.showView()
                    profileSummaryLayout.tvProfilePercentage.showView()
                    profileSummaryLayout.completionBadge.hideView()
                    profileSummaryLayout.progressProfile.progress = profileCompletionProgress
                    profileSummaryLayout.tvProfilePercentage.text =
                        "Info usaha baru " + profileCompletionProgress + "%. Yuk, lengkapi!"
                }

            profileSummaryLayout.tvSuccessShop.text = bookEntity?.businessName
        }
    }

    private fun setBasicInfoLayout() {
        binding.layoutBasicInfo.layoutNumber.vwDivider.hideView()
        binding.layoutBasicInfo.layoutCategory.tvItemKey.text =
            getString(R.string.business_category)
        binding.layoutBasicInfo.layoutNumber.tvItemKey.text = getString(R.string.mobile_phone_label)
        binding.layoutBasicInfo.layoutName.tvItemValue.text = bookEntity?.bookName.orEmpty()
        binding.layoutBasicInfo.layoutCategory.tvItemValue.text = bookEntity?.bookTypeName
        binding.layoutBasicInfo.layoutNumber.tvItemValue.text = bookEntity?.businessPhone ?: "-"
        if (bookEntity?.bookName.isNotNullOrEmpty() && bookEntity?.bookTypeName.isNotNullOrBlank() && bookEntity?.businessPhone.isNotNullOrBlank()) {
            binding.layoutBasicInfo.layoutHeading.ivCompletionCheck.showView()
        } else {
            binding.layoutBasicInfo.layoutHeading.ivCompletionCheck.hideView()
        }
        if((bookEntity?.bookName.isNotNullOrEmpty() && bookEntity?.bookName?.contains("EDC") == true)
            ||(bookEntity?.businessName.isNotNullOrEmpty() && bookEntity?.businessName?.contains("EDC") == true)){
            binding.deleteBtn.hideView()
        }
    }

    private fun setOperationInfoLayout() {
        binding.layoutOperationInfo.layoutHeading.tvBasicInfo.text =
            getString(R.string.operation_info_header)
        if (Utility.isSectionEmpty(bookEntity, Utility.profileInfoFields)) {
            binding.layoutOperationInfo.tvIncentiveInfo.showView()
            binding.layoutOperationInfo.formArea.hideView()
            binding.layoutOperationInfo.layoutHeading.tvChange.text = "Lengkapi"
            binding.layoutOperationInfo.layoutHeading.ivCompletionCheck.hideView()
        } else {
            binding.layoutOperationInfo.tvIncentiveInfo.hideView()
            binding.layoutOperationInfo.formArea.showView()
            binding.layoutOperationInfo.layoutOperationalHours.vwDivider.hideView()
            binding.layoutOperationInfo.layoutBusinessAddress.tvItemKey.text =
                getString(R.string.business_address_hint)
            binding.layoutOperationInfo.layoutOperationalHours.tvItemKey.text =
                getString(R.string.business_hour_title)

            if (bookEntity?.operatingDays.isNotNullOrEmpty() && !bookEntity?.operatingDays.equals("0")) {
                val businessTiming =
                    bookEntity?.operatingDays + ", " + bookEntity?.operatingHourStart + " - " + bookEntity?.operatingHourEnd
                binding.layoutOperationInfo.layoutOperationalHours.tvItemValue.text = businessTiming
            } else {
                binding.layoutOperationInfo.layoutOperationalHours.tvItemValue.text = "-"
            }
            if(bookEntity?.businessAddress.isNotNullOrBlank()){
                binding.layoutOperationInfo.layoutBusinessAddress.tvItemValue.text = Utility.getCompleteAddress(bookEntity)
            }else{
                binding.layoutOperationInfo.layoutBusinessAddress.tvItemValue.text = "-"
            }
            if (Utility.isSectionFilled(bookEntity, Utility.profileInfoFields)) {
                binding.layoutOperationInfo.layoutHeading.ivCompletionCheck.showView()
            } else {
                binding.layoutOperationInfo.layoutHeading.ivCompletionCheck.hideView()
            }
        }
    }

    private fun setAdditionalInfoLayout() {
        binding.layoutAdditionalInfo.apply {
            layoutHeading.tvBasicInfo.text =
                getString(R.string.additional_info_header)
            if (Utility.isSectionEmpty(bookEntity, Utility.profileAdditionalInfoFields)) {
                tvIncentiveInfo.showView()
                formArea.hideView()
                layoutHeading.tvChange.text = "Lengkapi"
                layoutHeading.ivCompletionCheck.hideView()
            } else {
                tvIncentiveInfo.hideView()
                formArea.showView()
                layoutProductionInfo.tvItemKey.text =
                    getString(R.string.production_type)
                layoutBuyerInfo.tvItemKey.text =
                    getString(R.string.buyer_type)
                layoutItemSoldInfo.tvItemKey.text =
                    getString(R.string.sell_qty)
                layoutEstablishment.tvItemKey.text =
                    getString(R.string.since_when)
                layoutOutletCount.tvItemKey.text =
                    getString(R.string.no_of_branch_stores)
                layoutEmpCount.tvItemKey.text =
                    getString(R.string.emp_count_hint)


                layoutProductionInfo.tvItemValue.text =
                    if (bookEntity?.production.isNullOrBlank()) "-" else bookEntity?.production
                layoutBuyerInfo.tvItemValue.text =
                    if (bookEntity?.productBuyer.isNullOrBlank()) "-" else bookEntity?.productBuyer
                layoutItemSoldInfo.tvItemValue.text =
                    if (bookEntity?.monthlyTurnover ?: 0 == 0) "-" else resources.getStringArray(R.array.choose_monthly_turn_over)[bookEntity?.monthlyTurnover!!]
                layoutEstablishment.tvItemValue.text =
                    if (bookEntity?.establishmentYear.isNullOrBlank()) "-" else bookEntity?.establishmentYear
                layoutOutletCount.tvItemValue.text =
                    if (bookEntity?.outletCount ?: 0 == 0) "-" else resources.getStringArray(R.array.number_of_branch_stores)[bookEntity?.outletCount!!]
                try {
                    var employeeCountId = if (bookEntity?.empCount ?: 0 < 6) {
                        bookEntity?.empCount ?: 0
                    } else {
                        if (bookEntity?.empCount ?: 0 > 10) 5 else 4
                    }
                    layoutEmpCount.tvItemValue.text =
                        if (bookEntity?.empCount == 0) "-" else resources.getStringArray(R.array.number_of_employees)[employeeCountId]
                }catch (ex:Exception){
                    FirebaseCrashlytics.getInstance().log(ex.message?:"incorrect employee count id")
                }

                if (Utility.isSectionFilled(bookEntity, Utility.profileAdditionalInfoFields)) {
                    layoutHeading.ivCompletionCheck.showView()
                } else {
                    layoutHeading.ivCompletionCheck.hideView()
                }
            }
        }
    }

    private fun setProfilePic(imageUri: String?) {
        Glide.with(this).load(imageUri)
            .apply(RequestOptions().circleCrop())
            .placeholder(R.drawable.ic_business_big)
            .error(R.drawable.ic_business_big)
            .into(binding.profileSummaryLayout.profilePic)
    }

    private fun bindProfilePicLayoutComponents() {
        binding.profileSummaryLayout.editImageIcon.setOnClickListener { showImageSelectorDialog() }
        binding.profileSummaryLayout.profilePic.setOnClickListener { showImageSelectorDialog() }
    }

    private fun showImageSelectorDialog() {
        if (context == null) return
        val imageSelectorDialog = ImageSelectorDialog2(requireContext(), {
            setImageSourceFromCamera()
        }, {
            getImageFromGallery()
        })
        imageSelectorDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_PROFILE_PIC)
        imageSelectorDialog.show()
    }

    private fun getImageFromGallery() {
        if (Build.VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(
                PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                PermissionConst.REQ_PICK_IMAGE_PERMISSON
            )
            return
        }

        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        startActivityForResult(
            Intent.createChooser(
                intent,
                getString(R.string.select_image_instruction)
            ), PermissionConst.REQ_PICK_IMAGE_PERMISSON
        )
    }

    private fun setImageSourceFromCamera() {
        try {
            profilePicFile = ImageUtils.createTempFile()
            profilePicUri = ImageUtils.getUriFromFile(profilePicFile)
            val intent = Intent("android.media.action.IMAGE_CAPTURE")
            intent.putExtra("output", profilePicUri)
            intent.addFlags(Intent.FLAG_EXCLUDE_STOPPED_PACKAGES)
            if (Build.VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    PermissionConst.CAMER_AND_STORAGE,
                    PermissionConst.REQ_TAKE_PICTURE_PERMISSON
                )
            }
            if (intent.resolveActivity(requireActivity().packageManager) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.REQ_TAKE_PICTURE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    setImageSourceFromCamera()
                }
            }
            PermissionConst.REQ_PICK_IMAGE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    getImageFromGallery()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        try {
            var uri: Uri? = null
            if (resultCode == Activity.RESULT_OK && requestCode == PermissionConst.TAKE_PHOTO) {
                uri = profilePicUri
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_USER_PROFILE_PIC_CAMERA)
            } else if (requestCode == PermissionConst.REQ_PICK_IMAGE_PERMISSON && resultCode == Activity.RESULT_OK && intent != null && intent.data != null) {
                uri = intent.data
            }

            uri?.let {
                bitmap = MediaStore.Images.Media.getBitmap(
                    requireActivity().contentResolver,
                    uri
                )

                bitmap = Utility.fixImageRotation(requireContext(), bitmap, profilePicFile, it)
                //write logic to set uri in binding.profileSummary.profilePic
//                binding.profileSummaryLayout.profilePic.setImageBitmap(bitmap)
                bookEntity?.businessImage = uri.toString()
                setProfilePic(bookEntity?.businessImage)
                businessFormViewModel.uploadToFirebase(bookEntity?.bookId ?: "", bitmap, true)
                profilePicFile = null

            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun showDeletionDialog(context: Context) {
        // book entity can be null some times
        var businessName: String? = "-"
        if (bookEntity != null) businessName = bookEntity!!.businessName
        if (bookEntity != null
            && bookEntity!!.bookId.isNotNullOrEmpty()
            && PaymentPrefManager.getInstance().getQrisInfo().qrisBookId.equals(bookEntity!!.bookId)
            && !PaymentPrefManager.getInstance().getDeleteBookEnabled()
        ) {
            BookDeletionBottomSheet().show(childFragmentManager, "qris-book-deletion")
        } else {
            // check if it is a payment account
            businessFormViewModel.fetchEDCDeviceDetails()
        }

        businessFormViewModel.edcPaymentBook.observe(this) {
            if (it.isNotNullOrEmpty() && it?.equals(SessionManager.getInstance().businessId)!!) {
                PaymentAccountDialogPrompt(requireActivity()).show()
            } else {
                val dialog = DeleteBusinessDialog(context, businessName) { promptResult: Boolean ->
                    if (promptResult) { deleteBook() }
                }
                dialog.show()
            }
        }
    }

    private fun deleteBook() {
        BusinessRepository.getInstance(activity)?.updateDeleteFlag(User.getUserId(), User.getDeviceId(), User.getBusinessId(), Integer.valueOf(1))

        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_DELETE_BUSINESS_PROFILE, "business_profile")
        PaymentPrefManager.getInstance().setDeleteBookEnabled(false)
        MainActivity.startActivityAndClearTop(activity, "open_side_menu", true)
    }

}