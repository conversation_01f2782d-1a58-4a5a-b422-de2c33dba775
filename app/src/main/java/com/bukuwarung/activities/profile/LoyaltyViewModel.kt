package com.bukuwarung.activities.profile

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyAccount
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTier
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTierBenefit
import com.bukuwarung.domain.profile.ProfileUseCase
import com.bukuwarung.payments.data.model.ReferralDataResponse
import com.bukuwarung.payments.data.model.SaldoResponse
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.recordException
import com.bukuwarung.wrapper.EventWrapper
import kotlinx.coroutines.launch
import javax.inject.Inject


class LoyaltyViewModel @Inject constructor(private val profileUseCase: ProfileUseCase
) : BaseViewModel() {

    sealed class State {
        data class OnProfileTierLoaded(val loyaltyAccount: LoyaltyAccount, val tier: LoyaltyTier, val isWhitelister:Boolean): State()
        data class OnSaldoBonusLoaded(val saldoBonusResponse: SaldoResponse,val loyaltyAccount: LoyaltyAccount, val tier: LoyaltyTier, val isWhitelister:Boolean, val loyaltyTierBenefit: List<LoyaltyTierBenefit>?): State()
        data class OnReferralDataLoaded(val referralDataResponse: ReferralDataResponse): State()
    }

    private val _state = MutableLiveData<EventWrapper<LoyaltyViewModel.State>>()
    val state: LiveData<EventWrapper<LoyaltyViewModel.State>> = _state


    sealed class Event {
        object OnCreateView : Event()

    }

    fun onEventReceived(event: Event) {
        when (event) {
            Event.OnCreateView -> fetchLoyaltyData()
        }
    }

    private fun fetchLoyaltyData() = launch {
        if(Utility.hasInternet() && RemoteConfigUtils.shouldShowLoyaltyLayout() && !RemoteConfigUtils.shouldShowSaldoBonus() ) {
            getProfileTierInfo()
        }
        if(Utility.hasInternet() && RemoteConfigUtils.shouldShowLoyaltyLayout() && RemoteConfigUtils.shouldShowSaldoBonus()) {
            getSaldoBonusInfo()
        }
        if (Utility.hasInternet() && RemoteConfigUtils.getLoyaltyWidgetType() == 2) {
            getReferralData()
        }
    }

    private fun setState(state: LoyaltyViewModel.State) {
        _state.value = EventWrapper(state)
    }

    private fun getProfileTierInfo() = viewModelScope.launch {
        Log.d("TIER", "fetching the tier info...")

        try {
            val tier = profileUseCase.getProfileTier()
            if (tier != null) {
                tier.let {
                    setState(
                        State.OnProfileTierLoaded(
                            it.loyaltyAccount,
                            it.loyaltyAccountTier.loyaltyTier,
                            it.isWhitelisted
                        )
                    )
                }
            }
        } catch(ex: Exception){
            ex.recordException()
        }
    }

    private fun getSaldoBonusInfo() = viewModelScope.launch {
        Log.d("SALDO", "fetching the Komisi Agen info...")

        try {
            val tier = profileUseCase.getProfileTier()
            if (tier != null) {
                profileUseCase.getSaldoBonus()?.let {
                    setState(
                        State.OnSaldoBonusLoaded(
                            it,
                            tier.loyaltyAccount,
                            tier.loyaltyAccountTier.loyaltyTier,
                            tier.isWhitelisted,
                            tier.loyaltyAccountTier.loyaltyTierBenefits
                        )
                    )
                }
            }

        } catch(ex: Exception){
            ex.recordException()
        }
    }

    private fun getReferralData() = viewModelScope.launch {
        try {
            val referralData = profileUseCase.getReferralData()
            referralData?.let {
                setState(
                    State.OnReferralDataLoaded(it)
                )
            } ?: kotlin.run {
                State.OnReferralDataLoaded(ReferralDataResponse(null,null))
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

}
