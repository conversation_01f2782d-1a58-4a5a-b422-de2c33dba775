package com.bukuwarung.activities.profile.businessprofile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.databinding.BookDeletionBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class BookDeletionBottomSheet : BottomSheetDialogFragment() {

    private var _binding: BookDeletionBottomSheetBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BookDeletionBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.btnClose.setOnClickListener {
            dismiss()
        }
        binding.btnReturn.setOnClickListener {
            dismiss()
        }
    }
}