package com.bukuwarung.activities.profile.summary.view;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.activities.profile.summary.SummaryViewModel;

public class CashSummaryView {

    private TextView netCashBalanceTV;
    private TextView totalCashInAmountTV;
    private TextView totalCashOutAmountTV;

    public CashSummaryView(View view) {
        this.netCashBalanceTV = view.findViewById(R.id.netCashBalanceTv);
        this.totalCashInAmountTV = view.findViewById(R.id.totalCashInValueTv);
        this.totalCashOutAmountTV = view.findViewById(R.id.totalCashOutValueTv);
    }

    public void refreshValues(String netBalanceStr, String netExplainStr, String totalCreditAmountStr, String totalDebitAmountStr, SummaryViewModel summaryData, Context ctx){
        double totalDebit = summaryData.getTotalDebit() + summaryData.getTotalCredit();
        this.netCashBalanceTV.setText(netBalanceStr);
        this.totalCashInAmountTV.setText(totalCreditAmountStr);
        this.totalCashOutAmountTV.setText(totalDebitAmountStr);
        if (totalDebit >= ((double) 0)) {
            this.netCashBalanceTV.setTextColor(ctx.getResources().getColor(R.color.color_credit));
        }else{
            this.netCashBalanceTV.setTextColor(ctx.getResources().getColor(R.color.color_debit));
        }
    }
}
