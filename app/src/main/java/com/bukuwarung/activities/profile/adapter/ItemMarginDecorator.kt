package com.bukuwarung.activities.profile.adapter

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

class ItemMarginDecorator(
    private val startEndMargin: Int, private val intermediateMargin: Int
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val dataSize = state.itemCount
        when (parent.getChildAdapterPosition(view)) {
            0 -> {
                outRect.right = intermediateMargin / 2
                outRect.left = startEndMargin
            }
            dataSize - 1 -> {
                outRect.right = startEndMargin
                outRect.left = intermediateMargin / 2
            }
            else -> {
                outRect.right = intermediateMargin / 2
                outRect.left = intermediateMargin / 2
            }
        }
    }
}