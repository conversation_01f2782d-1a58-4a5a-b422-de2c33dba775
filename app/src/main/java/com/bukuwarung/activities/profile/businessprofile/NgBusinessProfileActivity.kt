package com.bukuwarung.activities.profile.businessprofile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.view.BusinessAddressActivity
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModelFactory
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.ActivityUserBusinessProfileBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isNotNullOrEmpty
import javax.inject.Inject

class NgBusinessProfileActivity : BaseActivity() {

    private var fromMission: Boolean = false

    @Inject
    lateinit var vmFactory: DetailedBusinessFormViewModelFactory
    private val viewModel: DetailedBusinessFormViewModel by viewModels { vmFactory }
    private lateinit var binding: ActivityUserBusinessProfileBinding
    private var bookId : String = ""
    private var bookEntity: BookEntity? = null

    companion object {

        private val BOOK_ID: String = "book_id"
        private val FROM_MISSON: String = "from_mission"

        fun createIntent(origin: Context?, bookId: String, fromMission: Boolean= false): Intent {
            val intent = Intent(origin, NgBusinessProfileActivity::class.java)
            intent.putExtra(BOOK_ID, bookId)
            intent.putExtra(FROM_MISSON, fromMission)
            return intent
        }
    }

    override fun setViewBinding() {
        binding = ActivityUserBusinessProfileBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
       //No Imp
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(intent.hasExtra(BOOK_ID)) {
            bookId = intent.getStringExtra(BOOK_ID).orEmpty()
        }
        else{
            bookId = User.getBusinessId()
        }
        if (bookId.isNotNullOrEmpty()) {
            bookEntity = BusinessRepository.getInstance(this)?.getBusinessByIdSync(bookId)
        }
        if(intent.hasExtra(FROM_MISSON)){
            fromMission = intent.getBooleanExtra(FROM_MISSON,false)
        }
        supportFragmentManager.beginTransaction().add(
            R.id.main_container,
            NgBusinessProfileFragment.instance(bookId,true)).commit()

        supportFragmentManager.addOnBackStackChangedListener {
            if (bookId.isNotNullOrEmpty()) {
                bookEntity = BusinessRepository.getInstance(this)?.getBusinessByIdSync(bookId)
                bookEntity?.profileCompletionProgress =
                    Utility.calculateCompletionPercentage(bookEntity)
            } else {
                bookEntity = BookEntity()
            }

            if(fromMission && Utility.calculateCompletionPercentage(bookEntity)==100){
                this.finish()
            }
        }

    }

    override fun onResume() {
        super.onResume()
        bookEntity?.let {
            if (Utility.isSectionFilled(
                    it,
                    Utility.profileBasicFields
                ) && Utility.isSectionFilled(
                    it,
                    Utility.profileInfoFields
                ) && Utility.calculateCompletionPercentage(bookEntity) == 100
            ) {
                FeaturePrefManager.getInstance().hasCompleteBusinessProfile(true)
            }
        }
    }

    override fun subscribeState() {
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                DetailedBusinessFormViewModel.State.OpenAddressFlow -> {
                    val intent = BusinessAddressActivity.createIntent(this,  saveOnFullFlow = false, entryPoint = AnalyticsConst.BUSINESS_PROFILE)
                    startActivityForResult(intent, BusinessAddressActivity.ADDRESS_REQUEST_CODE)
                }
            }

        })
    }

    override fun onBackPressed() {
        val count = supportFragmentManager.backStackEntryCount

        if (count == 0) {
            super.onBackPressed()
            //additional code
        } else {
            supportFragmentManager.popBackStack()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode == Activity.RESULT_OK){
            when(requestCode){
                BusinessAddressActivity.ADDRESS_REQUEST_CODE->{
                    val address = data?.getParcelableExtra<Address>(BusinessAddressActivity.ADDRESS) ?: return
                    viewModel.setBusinessAddress(address)
                }
            }
        }
    }


}