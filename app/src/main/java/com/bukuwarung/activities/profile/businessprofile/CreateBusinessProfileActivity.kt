package com.bukuwarung.activities.profile.businessprofile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.observe
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.form.CategorySelectorFragment
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModelFactory
import com.bukuwarung.activities.onboarding.form.FormType
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ACTION
import com.bukuwarung.constants.AnalyticsConst.BASIC_SECTION
import com.bukuwarung.constants.AnalyticsConst.BOOK_NAME
import com.bukuwarung.constants.AnalyticsConst.BUSINESS_CATEGORY
import com.bukuwarung.constants.AnalyticsConst.BUSINESS_COUNT
import com.bukuwarung.constants.AnalyticsConst.EDIT
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.constants.AnalyticsConst.HomePage.BUTTON_CLICK_EVENT
import com.bukuwarung.constants.AnalyticsConst.HomePage.SECTION
import com.bukuwarung.constants.AnalyticsConst.NEW_BOOK
import com.bukuwarung.constants.AnalyticsConst.SECTION_STATUS
import com.bukuwarung.constants.AnalyticsConst.TYPE
import com.bukuwarung.constants.AnalyticsConst.USER_PROFILE
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.LayoutCreateBookBinding
import com.bukuwarung.payments.constants.QrisAndKycStatus
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import java.util.*
import javax.inject.Inject

class CreateBusinessProfileActivity : BaseActivity(),
    CategorySelectorFragment.ICategoryCommunicator {

    @Inject
    lateinit var vmFactory: DetailedBusinessFormViewModelFactory
    private val businessFormViewModel: DetailedBusinessFormViewModel by viewModels { vmFactory }
    private lateinit var binding: LayoutCreateBookBinding
    private var bookEntity: BookEntity? = null
    private var bookId: String = ""
    private var isSaveEnabled = false
    private var businessCategory: String? = null
    private var businessCategoryId: String? = null
    private var isEdit = false
    private val useCase by lazy { (intent.getSerializableExtra(USE_CASE) ?: UseCase.DEFAULT) as UseCase }

    enum class UseCase {
        DEFAULT, QRIS, PAYMENT_CHECKOUT
    }

    companion object {

        private val BOOK_ID: String = "book_id"
        private val IS_EDIT: String = "is_edit"
        private val USE_CASE = "use_case"

        fun createIntent(
            origin: Context?, bookId: String, isEdit: Boolean = false,
            useCase: UseCase = UseCase.DEFAULT
        ): Intent {
            val intent = Intent(origin, CreateBusinessProfileActivity::class.java)
            intent.putExtra(BOOK_ID, bookId)
            intent.putExtra(IS_EDIT, isEdit)
            intent.putExtra(USE_CASE, useCase)
            return intent
        }
    }

    override fun setViewBinding() {
        binding = LayoutCreateBookBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (intent.hasExtra(BOOK_ID)) {
            bookId = intent.getStringExtra(BOOK_ID).orEmpty()
        }
        if (intent.hasExtra(IS_EDIT)) {
            isEdit = intent.getBooleanExtra(IS_EDIT, false)
        }
        if (bookId.isNotNullOrEmpty()) {
            bookEntity = BusinessRepository.getInstance(this)?.getBusinessByIdSync(bookId)
        } else {
            bookEntity = BookEntity()
            //binding.tvDeleteBtn.visibility = View.INVISIBLE
        }
        if (isEdit) {
            binding.titleText.text = getString(R.string.basic_info)
        } else {
            binding.titleText.text = getString(R.string.new_business_title)
        }
        binding.etName.onFocusChangeListener =
            View.OnFocusChangeListener { _: View?, hasFocus: Boolean ->
                if (hasFocus) {
                    binding.tvBusinessNameError.hideView()
                    binding.tilName.boxStrokeColor = getColorCompat(R.color.colorPrimary)
                }
            }
        binding.backBtn.setOnClickListener { showBackPromptDialog() }
        binding.tvHeadingName.tvHeading.text = getString(R.string.business_name)
        binding.tvHeadingName.tvLabel.text = "Wajib"
        binding.tvHeadingCategory.tvHeading.text = getString(R.string.business_category_hint)
        binding.tvHeadingCategory.tvLabel.text = "Wajib"
        binding.tvHeadingPhone.tvHeading.text = getString(R.string.mobile_phone_label)
        binding.tvHeadingPhone.tvLabel.text = "Wajib"

        binding.etName.setText(bookEntity?.bookName)
        binding.etCategory.setText(bookEntity?.bookTypeName)
        if (!Utility.isBlank(bookEntity?.businessPhone) && bookEntity?.businessPhone?.isValidPhoneNumber() == true) {
            binding.etPhone.setText(bookEntity?.businessPhone)
        } else {
            binding.etPhone.setText("0" + User.getUserId())
        }
        binding.etName.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (Utility.isValidName(s.toString())) {
                    binding.tvBusinessNameError.visibility = false.asVisibility()
                }else{
                    binding.tvBusinessNameError.visibility = true.asVisibility()
                    binding.tvBusinessNameError.text = "Mohon tidak menggunakan karakter spesial dan emoji \n" +
                            "(^\$*.[]{}()?-\"!#%&/\\,><':;|_~`) \uD83D\uDE03"
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
        binding.etCategory.setOnClickListener {
            val categoryList =
                RemoteConfigUtils.OnBoarding.getBusinessCategories().toMutableList()
            when (useCase) {
                UseCase.QRIS -> categoryList.removeIf { it.name == "Lainnya" }
                else -> {}
            }
            CategorySelectorFragment.getInstance(
                data = categoryList,
                showHeader = true,
                showActionButton = true,
                formType = FormType.BusinessCategory
            ).show(supportFragmentManager, "jhv")
            InputUtils.hideKeyBoardWithCheck(this)
        }

        binding.btnSave.setOnClickListener {
            if(binding.tvBusinessNameError.visibility == View.VISIBLE){
                Toast.makeText(this, "Mohon tidak menggunakan karakter spesial dan emoji \n" +
                        "(^\$*.[]{}()?-\"!#%&/\\,><':;|_~`) \uD83D\uDE03", Toast.LENGTH_SHORT).show()
            }else {
                if (Utility.hasInternet()) {
                    businessFormViewModel.checkBusinessNameValidation(binding.etName.text.toString())
                } else {
                    Toast.makeText(this, getString(R.string.no_internet_error), Toast.LENGTH_SHORT)
                        .show()
                }
            }
        }
    }

    override fun setupView() {
        businessFormViewModel.setBusinessData(bookEntity ?: BookEntity())
        with(binding) {
            etName.afterTextChanged {
                checkIfButtonIsEnabled()
                tvBusinessNameError.hideView()
                binding.tilName.boxStrokeColor = getColorCompat(R.color.colorPrimary)
            }
            etPhone.afterTextChanged {
                checkIfButtonIsEnabled()
            }
            etCategory.afterTextChanged {
                checkIfButtonIsEnabled()
            }
            binding.etName.onFocusChangeListener =
                View.OnFocusChangeListener { _: View?, hasFocus: Boolean ->
                    if (hasFocus) {
                        binding.tvBusinessNameError.hideView()
                    }
                }
        }

        //no need to check in book creating flow
        if(intent.hasExtra(BOOK_ID) && intent.getStringExtra(BOOK_ID).orEmpty().isNotNullOrEmpty()) {
            val bookId = SessionManager.getInstance().businessId
            val book = BusinessRepository.getInstance(this)?.getBusinessByIdSync(bookId)
            PaymentPrefManager.getInstance().qrisData.observe(this) {
                val qrisBookId = it.qrisBookId
                if (!it.finalStatus.equals(QrisAndKycStatus.REJECTED.name) && bookId == qrisBookId && !Utility.isBlank(
                        it.qrisBookName
                    )
                ) {
                    if (it.finalStatus.equals(QrisAndKycStatus.VERIFIED.name) && !Utility.isEqual(it.qrisBookName, book?.bookName)) {
                        binding.profileNameEditInfoLayout.showView()
                        binding.tvInfo.text = getString(R.string.qris_wrong_name, it.qrisBookName)
                        binding.etName.isEnabled = true
                        binding.etName.isFocusable = true
                    }
                    else if (!Utility.isEqual(it.qrisBookName, book?.bookName)) {
                        BusinessRepository.getInstance(this)
                            .updateBusinessProfileName(bookId, it.qrisBookName)
                        binding.etName.setText(it.qrisBookName)
                        binding.etName.isEnabled = false
                        binding.etName.isFocusable = false
                        binding.profileNameEditInfoLayout.showView()
                        binding.tvInfo.text = getString(R.string.qris_info)
                    }
                } else {
                    binding.etName.isEnabled = true
                    binding.etName.isFocusable = true
                    binding.profileNameEditInfoLayout.hideView()
                }
            }

            val qrisData = PaymentPrefManager.getInstance().getQrisInfo()

            if (qrisData.finalStatus.equals(QrisAndKycStatus.VERIFIED.name) && !Utility.isEqual(qrisData.qrisBookName, book?.bookName)) {
                binding.profileNameEditInfoLayout.showView()
                binding.tvInfo.text = getString(R.string.qris_wrong_name, qrisData.qrisBookName)
                binding.etName.isEnabled = true
                binding.etName.isFocusable = true
            }
        }
    }

    private fun checkIfButtonIsEnabled() {
        with(binding) {
            if (!etName.text.isNullOrEmpty() && !etCategory.text.isNullOrEmpty() && !etPhone.text.isNullOrEmpty()) {
                btnSave.setTextColor(ContextCompat.getColor(btnSave.context, R.color.black_80))
                btnSave.backgroundTintList =
                    ContextCompat.getColorStateList(btnSave.context, R.color.new_yellow)
                isSaveEnabled = true
            } else {
                btnSave.setTextColor(ContextCompat.getColor(btnSave.context, R.color.white))
                btnSave.backgroundTintList =
                    ContextCompat.getColorStateList(btnSave.context, R.color.black_40)
                isSaveEnabled = false
            }
        }
    }

    override fun subscribeState() {
        businessFormViewModel.observeEvent.observe(this) {
            when (it) {
                is DetailedBusinessFormViewModel.Event.BookValidationError -> {
                    binding.tvBusinessNameError.showView()
                    binding.tvBusinessNameError.text =
                        getString(
                            R.string.change_name_of_business_msg_2,
                            it.bookName?.uppercase(Locale.getDefault())?.trim()
                        )
                    binding.tilName.boxStrokeColor = getColorCompat(R.color.red_80)
                }
                is DetailedBusinessFormViewModel.Event.BookValidationSuccess -> {
                    setResult(Activity.RESULT_OK)
                    if (isSaveEnabled) {
                        bookEntity?.businessName = binding.etName.text.toString()
                        bookEntity?.bookName = binding.etName.text.toString()
                        bookEntity?.businessPhone = binding.etPhone.text.toString()
                        bookEntity?.bookTypeName = binding.etCategory.text.toString()
                        businessCategoryId?.let { categoryId ->
                            bookEntity?.bookType = categoryId.toInt()
                        }
                        if (bookEntity?.bookId.isNullOrEmpty()) {
                            bookEntity?.bookId = AppIdGenerator.resourceUUID()
                            bookEntity?.ownerId = User.getUserId()
                            EntityHelper.fillBusinessMetadata(bookEntity)
                            SessionManager.getInstance().businessId = bookEntity!!.bookId

                            SessionManager.getInstance().selectedBookName = bookEntity!!.bookName

                            SessionManager.getInstance().appState = 1
                            InputUtils.hideKeyBoardWithCheck(this)
                            MainActivity.startActivityAndClearTop(this)
                            BusinessRepository.getInstance(Application.getAppContext())
                                .createBusinessWithDetails(bookEntity)
                        } else {
                            InputUtils.hideKeyBoardWithCheck(this)
                            bookEntity?.updatedAt = Utility.getCurrentTime()
                            bookEntity?.dirty = 1
                            BusinessRepository.getInstance(Application.getAppContext())
                                .createBusinessWithDetails(bookEntity)
                            bookEntity?.let { it1 ->
                                businessFormViewModel.updateQrisBookName(it1.bookId, it1.bookName)
                            }
                            finish()
                        }
                        if (!isEdit) {
                            val prop = AppAnalytics.PropBuilder()
                            prop.put(TYPE, NEW_BOOK)
                            prop.put("business_category", binding.etCategory.text.toString())
                            prop.put(
                                BUSINESS_COUNT,
                                (BusinessRepository.getInstance(Application.getAppContext())
                                    .getBusinessListRaw(SessionManager.getInstance().userId).size).toString()
                            )
                            AppAnalytics.trackEvent("business_profile_create", prop)
                            Utilities.sendEventsToBackendWithBureau("business_profile_create", "create_profile")
                        } else {
                            val prop = AppAnalytics.PropBuilder()
                            prop.put(ENTRY_POINT2, USER_PROFILE)
                            prop.put(SECTION, BASIC_SECTION)
                            prop.put(
                                SECTION_STATUS,
                                Utility.sectionCompletionStatus(bookEntity, Utility.profileBasicFields)
                            )
                            prop.put(ACTION, EDIT)
                            prop.put(TYPE, NEW_BOOK)
                            prop.put(BOOK_NAME, binding.etName.text.toString())
                            prop.put(BUSINESS_CATEGORY, binding.etCategory.text.toString())
                            AppAnalytics.trackEvent("business_profile_section_saved", prop)
                            Utilities.sendEventsToBackendWithBureau("save_business_profile_section", "create_business")
                        }
                    }
                }
                else -> {}
            }
        }
    }

    private fun showBackPromptDialog() {
        val dialog = BackConfirmationDialog(
            this,
            "Batal mengisi informasi toko?",
            "Informasi yg sudah kamu isi akan terhapus setelah dibatalkan.",
            "Ya, Batal",
            "Lanjut Mengisi"
        ) {
            val prop = AppAnalytics.PropBuilder()
            prop.put(ENTRY_POINT2, "user_profile")
            prop.put(SECTION, "basic")
            if (it) {
                prop.put(BUTTON_CLICK_EVENT, "yes")
                onBackPressed()
            } else {
                prop.put(BUTTON_CLICK_EVENT, "no")
            }
            AppAnalytics.trackEvent("click_back_button_business_profile", prop)
        }
        dialog.show()
    }

    override fun setCategory(category: String, categoryId: String) {
        businessCategory = category
        businessCategoryId = categoryId
        binding.etCategory.setText(category)
    }

}