package com.bukuwarung.activities.profile.summary;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;

import com.bukuwarung.R;
import com.bukuwarung.activities.profile.summary.view.CreditSummaryView;
import com.bukuwarung.locale.Countries;
import com.bukuwarung.locale.Country;

public class CreditSummaryFragment extends Fragment {

    private CreditSummaryView creditSummaryView;

    public void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
    }

    public View onCreateView(LayoutInflater paramLayoutInflater, ViewGroup paramViewGroup, Bundle paramBundle) {
        View view = paramLayoutInflater.inflate(R.layout.fragment_credit_summary, paramViewGroup, false);
        creditSummaryView = new CreditSummaryView(view);
        initSummaryView(this);
        return view;
    }
    private void initSummaryView(CreditSummaryFragment creditSummaryFragment){
        new LoadAccountCreditSummary(creditSummaryFragment, getContext()).execute(new Void[0]);
    }

    public final void displaySummary(SummaryViewModel summaryData, Context context) {

        String currency = ((Country) new Countries().getCountryList().get(Countries.getDefaultCountryIndex())).getCurrency();

        String totalDebitAmountStr = SummaryUtils.composeDebitSummary(summaryData,currency);
        String totalCreditAmountStr = SummaryUtils.composeCreditSummary(summaryData,currency);
        String netBalanceStr = SummaryUtils.composeNetBalanceSummary(summaryData,currency);
        String netExplainStr = SummaryUtils.composeBalanceExplainSummary(summaryData,currency,context);

        creditSummaryView.refreshValues(netBalanceStr, netExplainStr, totalCreditAmountStr, totalDebitAmountStr,summaryData,context);

    }
}
