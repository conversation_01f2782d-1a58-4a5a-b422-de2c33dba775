package com.bukuwarung.activities.profile.update

import android.Manifest
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.work.WorkInfo
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.form.CategorySelectorFragment
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.onboarding.form.FormType
import com.bukuwarung.activities.onboarding.form.OperationHourSelectorFragment
import com.bukuwarung.activities.profile.update.dialogs.DeleteBusinessDialog
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.FragmentUserBusinessProfileEditBinding
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog2
import com.bukuwarung.location.LocationUtil.getLocation
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.io.File

class EditUserBusinessProfileFragment : BaseFragment() {

    private val businessFormViewModel: DetailedBusinessFormViewModel by activityViewModels()

    lateinit var binding: FragmentUserBusinessProfileEditBinding
    private var bookEntity: BookEntity? = null
    var dialog: BusinessSelectorDialog? = null
    private var profilePicUri: Uri? = null
    private var profilePicFile: File? = null
    private var successView: View? = null
    private var bookId:String = ""

    companion object {

        val TAG: String = "edit_user_business_profile_fragment"
        private const val BOOK_ID = "BOOK_ID"

        fun instance(targetBookId:String): EditUserBusinessProfileFragment {
            val fragment = EditUserBusinessProfileFragment()
            val bundle = Bundle()
            bundle.putString(BOOK_ID, targetBookId)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View? {
        binding = FragmentUserBusinessProfileEditBinding.inflate(layoutInflater, viewGroup, false)
        if (arguments != null) {
            if (requireArguments().containsKey(BOOK_ID)) {
                bookId = requireArguments().getString(BOOK_ID).toString()
            }
        }
        if(bookId.isNotNullOrEmpty()) {
            bookEntity = BusinessRepository.getInstance(context)?.getBusinessByIdSync(bookId)
        } else {
            bookEntity = BookEntity()
            binding.tvDeleteBtn.visibility = View.INVISIBLE
        }
        return binding.root
    }

    override fun setupView(view: View) {

        businessFormViewModel.setBusinessData(bookEntity?: BookEntity())

        binding.apply {

            etBusinessAddress.setOnFocusChangeListener(OnFocusChangeListener { view: View, b: Boolean ->
                if (view.isFocused) {
                    if (!PermissonUtil.hasLocationPermission()) {
                        PermissonUtil.requestLocationPermission(activity)
                    } else {
                        storeLocation()
                    }
                }
            })

            etBusinessAddress.afterTextChanged { txt ->
                bookEntity?.businessAddress = txt
                calculatePercentage()
            }
            userNameTv.afterTextChanged { txt ->
                bookEntity?.bookName = txt
                bookEntity?.businessName = txt
                calculatePercentage()
            }
            etBusinessEstablishmentYear.afterTextChanged { txt ->
                bookEntity?.establishmentYear = txt
                calculatePercentage()
            }
            etEmpCount.afterTextChanged { txt ->
                bookEntity?.empCount = txt.toInt(0)
                calculatePercentage()
            }
            etOutletCount.afterTextChanged { txt ->
                bookEntity?.outletCount = txt.toInt(0)
                calculatePercentage()
            }
            userPhoneTv.afterTextChanged { txt ->
                bookEntity?.businessPhone = txt
                calculatePercentage()
            }
        }

        businessFormViewModel.bookDataLive.observe(this, Observer { data ->
            binding.userNameTv.setText(bookEntity?.bookName)
            binding.userPhoneTv.setText(bookEntity?.businessPhone?:("0" + User.getUserId()))

            if(bookEntity?.businessAddress.isNotNullOrEmpty()) {
                binding.etBusinessAddress.setText(bookEntity?.businessAddress)
            }

            if(bookEntity?.businessAddress.isNotNullOrEmpty()) {
                binding.etBusinessAddress.setText(bookEntity?.businessAddress)
            }

            if(data?.operatingDays.isNotNullOrEmpty()) {
                bookEntity?.operatingDays = data.operatingDays
                bookEntity?.operatingHourEnd = data.operatingHourEnd
                bookEntity?.operatingHourStart = data.operatingHourStart
            }

            if(bookEntity?.operatingDays.isNotNullOrEmpty() && !bookEntity?.operatingDays.equals("0")) {
                var businessTiming = bookEntity?.operatingDays+ ", " + bookEntity?.operatingHourStart+" - "+bookEntity?.operatingHourEnd
                binding.etBusinessHour.setText(businessTiming)
            }

            if(bookEntity?.empCount!! > 0) {
                binding.etEmpCount.setText(bookEntity?.empCount.toString() ?: "")
            }else{
                binding.etEmpCount.setText("")
            }
            if(bookEntity?.outletCount!! > 0) {
                binding.etOutletCount.setText(bookEntity?.outletCount.toString() ?: "")
            }else{
                binding.etOutletCount.setText("")
            }
            binding.etBusinessEstablishmentYear.setText(bookEntity?.establishmentYear)
            if(data.businessImage.isNotNullOrEmpty()) {
                setProfilePic(data.businessImage)
            }
            if(data.bookTypeName.isNotNullOrEmpty()) {
                bookEntity?.bookTypeName = data.bookTypeName
                bookEntity?.bookType = data.bookType
                binding.etBusinessCategory.setText(bookEntity?.bookTypeName)
            }
            calculatePercentage()
        })

//        lavSuccessView = view.findViewById(R.id.lav_success)
        successView = view.findViewById(R.id.success_view)

        bindLayoutComponents()
        binding.backBtn.setOnClickListener {
            activity?.onBackPressed()
        }
        binding.etBusinessCategory.setOnClickListener{
            val categoryForm = CategorySelectorFragment.getInstance(
                data = RemoteConfigUtils.OnBoarding.getBusinessCategories(),
                showHeader = true,
                showActionButton = true,
                formType = FormType.BusinessCategory
            )
            InputUtils.hideKeyBoardWithCheck(activity)
            activity?.supportFragmentManager?.beginTransaction()?.add(R.id.fragment_container_contact, categoryForm!!)?.commit()
        }
        binding.etBusinessHour.setOnClickListener{
            val categoryForm = OperationHourSelectorFragment.getInstance(
                data = RemoteConfigUtils.OnBoarding.getBusinessCategories(),
                showHeader = true,
                showActionButton = true,
                formType = FormType.BusinessTiming,
                targetBookId = ""
            )
            InputUtils.hideKeyBoardWithCheck(activity)
            activity?.supportFragmentManager?.beginTransaction()?.add(R.id.fragment_container_contact, categoryForm!!)?.commit()
        }
    }

    private fun storeLocation() {
        try {
            val workInfoLiveData = getLocation(requireContext(), "business card")
            workInfoLiveData?.observe(this,
                Observer { workInfo: WorkInfo? ->
                    if (workInfo != null && workInfo.state == WorkInfo.State.SUCCEEDED) {
                        val workInfoOutputData = workInfo.outputData
                        val streetName = workInfoOutputData.getString("streetName")
                        binding.etBusinessAddress.setText(streetName)
                        binding.etBusinessAddress.getText()?.length?.let {
                            binding.etBusinessAddress.setSelection(
                                it
                            )
                        }
                    }
                })
        } catch (ex: java.lang.Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }


    private fun setProfilePic(imageUri: String?) {
        Glide.with(this).load(imageUri)
            .apply(RequestOptions().circleCrop())
            .placeholder(R.drawable.ic_business_big)
            .error(R.drawable.ic_business_big)
            .into(binding.profilePic)
    }

    private fun calculatePercentage(){
        var filledFields = 0;
        if(!binding.userNameTv.text.isNullOrEmpty() && binding.etBusinessHour.text.toString().length>0) filledFields++
        if(!binding.etBusinessHour.text.isNullOrEmpty() && binding.etBusinessHour.text.toString().length>0) filledFields++
        if(!binding.etBusinessCategory.text.isNullOrEmpty() && binding.etBusinessCategory.text.toString().length>0) filledFields++
        if(!binding.etBusinessEstablishmentYear.text.isNullOrEmpty() && binding.etBusinessEstablishmentYear.text.toString().length>0) filledFields++
        if(!binding.etBusinessAddress.text.isNullOrEmpty() && binding.etBusinessAddress.text.toString().length>0) filledFields++
        if(!binding.userPhoneTv.text.isNullOrEmpty() && binding.etBusinessAddress.text.toString().length>0) filledFields++
        if(!binding.etEmpCount.text.isNullOrEmpty() && binding.etBusinessAddress.text.toString().length>0) filledFields++
        if(!binding.etOutletCount.text.isNullOrEmpty() && binding.etBusinessAddress.text.toString().length>0) filledFields++
        binding.progressText.setText("Lengkapi profilmu, isi "+(8-filledFields)+" pertanyaan lagi")

        if(filledFields == 0){
            binding.progressPercentage.setText("0%")
            binding.progressProfile.setProgress(0)
            bookEntity?.profileCompletionProgress = 0
        }else {
            var percentage: Int = filledFields * 100 / 8
            binding.progressPercentage.setText(percentage.toString()+"%")
            binding.progressProfile.setProgress(percentage)
            if(percentage == 100) {
                binding.progressText.setText("\uD83C\uDF8A Keren, profil kamu sudah lengkap!")
            }
            bookEntity?.profileCompletionProgress = percentage
        }

    }

    fun onClickSave() {

        if(binding.userNameTv.text.isNullOrEmpty()){
            Toast.makeText(context,getString(R.string.enter_business_name),Toast.LENGTH_SHORT).show()
            return
        }
        if(binding.etBusinessCategory.text.isNullOrEmpty()){
            Toast.makeText(context,getString(R.string.enter_category_name),Toast.LENGTH_SHORT).show()
            return
        }

        bookEntity?.bookName = binding.userNameTv.text.toString()
        bookEntity?.businessName = binding.userNameTv.text.toString()
        bookEntity?.businessPhone = binding.userPhoneTv.text.toString()
        bookEntity?.businessAddress = binding.etBusinessAddress.text.toString()
        bookEntity?.empCount = binding.etEmpCount.text.toString().toInt(0)
        bookEntity?.outletCount = binding.etOutletCount.text.toString().toInt(0)
        bookEntity?.establishmentYear = binding.etBusinessEstablishmentYear.text.toString()
        if(bookEntity?.bookId.isNullOrEmpty()){
            bookEntity?.bookId = AppIdGenerator.resourceUUID()
            bookEntity?.ownerId = User.getUserId()
            EntityHelper.fillBusinessMetadata(bookEntity)
            SessionManager.getInstance().setBusinessId(bookEntity!!.bookId)
            SessionManager.getInstance().selectedBookName = bookEntity!!.bookName

            SessionManager.getInstance().setAppState(1)
            MainActivity.startActivityAndClearTop(activity)
        }else{
            bookEntity?.updatedAt = Utility.getCurrentTime()
            bookEntity?.dirty = 1
        }
        BusinessRepository.getInstance(Application.getAppContext()).createBusinessWithDetails(bookEntity)
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT, "edit_profile")
        propBuilder.put("business_contact", !Utility.isBlank(bookEntity?.businessPhone?:""))
        propBuilder.put(AnalyticsConst.BUSINESS_TYPE, bookEntity?.bookType)
        propBuilder.put(AnalyticsConst.BUSINESS_TYPE_NAME, bookEntity?.bookTypeName)
        propBuilder.put("business_location", bookEntity?.businessAddress)
        propBuilder.put("business_creation_date", bookEntity?.establishmentYear)
        propBuilder.put("no_of_employee", bookEntity?.empCount)
        propBuilder.put("no_of_outlet", bookEntity?.outletCount)
        propBuilder.put("business_operational_hours", bookEntity?.operatingDays+" "+bookEntity?.operatingHourStart+"-"+bookEntity?.operatingHourEnd)
        AppAnalytics.trackEvent(AnalyticsConst.BUSINESS_PROFILE_SAVE,propBuilder)
        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.BUSINESS_PROFILE_SAVE, "edit_business_fragment")
        activity?.onBackPressed()
    }

    private fun bindLayoutComponents() {
        binding.tvDeleteBtn.setOnClickListener({
            context?.let { it1 -> showDeletionDialog(it1) }
        })

        binding.editImageIcon.setOnClickListener { showImageSelectorDialog() }
        binding.profilePic.setOnClickListener { showImageSelectorDialog() }
        binding.btnSave.setOnClickListener({
            onClickSave()
        })
    }

    private fun showImageSelectorDialog() {
        if (context == null) return
        val imageSelectorDialog = ImageSelectorDialog2(requireContext(), {
            setImageSourceFromCamera()
        }, {
            getImageFromGallery()
        })
        imageSelectorDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_PROFILE_PIC)
        imageSelectorDialog.show()
    }

    private fun getImageFromGallery() {
        if (Build.VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(
                PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                PermissionConst.REQ_PICK_IMAGE_PERMISSON
            )
            return
        }

        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        startActivityForResult(
            Intent.createChooser(
                intent,
                getString(R.string.select_image_instruction)
            ), PermissionConst.REQ_PICK_IMAGE_PERMISSON
        )
    }

    private fun setImageSourceFromCamera() {
        try {
            profilePicFile = ImageUtils.createTempFile()
            profilePicUri = ImageUtils.getUriFromFile(profilePicFile)
            val intent = Intent("android.media.action.IMAGE_CAPTURE")
            intent.putExtra("output", profilePicUri)
            intent.addFlags(Intent.FLAG_EXCLUDE_STOPPED_PACKAGES)
            if (Build.VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    PermissionConst.CAMER_AND_STORAGE,
                    PermissionConst.REQ_TAKE_PICTURE_PERMISSON
                )
            }
            if (intent.resolveActivity(requireActivity().packageManager) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.REQ_TAKE_PICTURE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    setImageSourceFromCamera()
                }
            }
            PermissionConst.REQ_PICK_IMAGE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    getImageFromGallery()
                }
            }

            PermissionConst.ACCESS_LOCATION -> {
                val propBuilder = PropBuilder()
                if (grantResults.size > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_ALLOW)
                } else {
                    propBuilder.put(AnalyticsConst.STATUS, AnalyticsConst.STATUS_DENY)
                }
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_LOCATION_PERMISSION_REQUEST,
                    propBuilder
                )
                if (grantResults.size > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    storeLocation()
                }
            }

        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        try {
            var uri: Uri? = null
            if (resultCode == RESULT_OK && requestCode == PermissionConst.TAKE_PHOTO) {
                uri = profilePicUri
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_USER_PROFILE_PIC_CAMERA)
            } else if (requestCode == PermissionConst.REQ_PICK_IMAGE_PERMISSON && resultCode == RESULT_OK && intent != null && intent.data != null) {
                uri = intent.data
            }

            uri?.let {
                var bitmap = MediaStore.Images.Media.getBitmap(
                    requireActivity().contentResolver,
                    uri
                )

                bitmap = Utility.fixImageRotation(requireContext(), bitmap, profilePicFile, it)
                profilePicFile = null
                businessFormViewModel.uploadToFirebase(bookEntity?.bookId?:"",bitmap)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun showDeletionDialog(context: Context) {
        // book entity can be null some times
        var businessName: String? = "-"
        if (bookEntity != null) businessName = bookEntity!!.businessName
        val dialog = DeleteBusinessDialog(
            context,
            businessName) { promptResult: Boolean ->
            if (promptResult) {
                deleteBook()
            }
        }
        dialog.show()
    }

    private fun deleteBook() {
        BusinessRepository.getInstance(activity)?.updateDeleteFlag(User.getUserId(), User.getDeviceId(), User.getBusinessId(), Integer.valueOf(1))

        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_DELETE_BUSINESS_PROFILE, "edit_business_fragment")
        MainActivity.startActivityAndClearTop(activity, "open_side_menu", true)
    }

    override fun subscribeState() {
    }
}