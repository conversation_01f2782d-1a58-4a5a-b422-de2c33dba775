package com.bukuwarung.activities.profile.businessprofile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.geolocation.data.model.Address
import com.bukuwarung.activities.geolocation.view.BusinessAddressActivity
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModel
import com.bukuwarung.activities.onboarding.form.DetailedBusinessFormViewModelFactory
import com.bukuwarung.activities.profile.summary.view.BusinessOperationalInformationFragment
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.databinding.ActivityBusinessOperationInformationBinding
import com.bukuwarung.session.User
import javax.inject.Inject

class BusinessOperationalInformationActivity: BaseActivity() {

    @Inject
    lateinit var vmFactory: DetailedBusinessFormViewModelFactory
    private val viewModel: DetailedBusinessFormViewModel by viewModels { vmFactory }
    private lateinit var binding: ActivityBusinessOperationInformationBinding
    private var bookId: String = ""
    private var bookEntity: BookEntity? = null

    companion object {

        private val BOOK_ID: String = "book_id"

        fun createIntent(origin: Context?, bookId: String): Intent {
            val intent = Intent(origin, BusinessOperationalInformationActivity::class.java)
            intent.putExtra(BOOK_ID, bookId)
            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(intent.hasExtra(BOOK_ID)) {
            bookId = intent.getStringExtra(BOOK_ID).orEmpty()
        } else{
            bookId = User.getBusinessId()
        }
        supportFragmentManager.beginTransaction().add(
            R.id.main_container,
            BusinessOperationalInformationFragment.instance(bookId, true)).commit()
    }

    override fun setViewBinding() {
        binding = ActivityBusinessOperationInformationBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        // no imp
    }

    override fun subscribeState() {
        // observe with traditional way as the state LiveData has multiple observers
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                DetailedBusinessFormViewModel.State.OpenAddressFlow -> {
                    val intent = BusinessAddressActivity.createIntent(this,  saveOnFullFlow = false, entryPoint = AnalyticsConst.BUSINESS_PROFILE)
                    startActivityForResult(intent, BusinessAddressActivity.ADDRESS_REQUEST_CODE)
                }
            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode == Activity.RESULT_OK){
            when(requestCode){
                BusinessAddressActivity.ADDRESS_REQUEST_CODE->{
                    val address = data?.getParcelableExtra<Address>(BusinessAddressActivity.ADDRESS) ?: return
                    viewModel.setBusinessAddress(address)
                }
            }
        }
    }

}