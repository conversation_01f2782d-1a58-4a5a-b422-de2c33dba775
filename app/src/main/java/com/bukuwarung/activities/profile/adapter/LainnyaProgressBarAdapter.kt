package com.bukuwarung.activities.profile.adapter

import android.content.res.ColorStateList
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.profile.ProfileTabFragment
import com.bukuwarung.activities.profile.model.ProgressBarItemLiannya
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.databinding.ItemProgressBarLainnyaTabBinding
import com.bukuwarung.payments.constants.KybStatus
import com.bukuwarung.payments.constants.KycStatus
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide

class LainnyaProgressBarAdapter(
    private val itemList: List<ProgressBarItemLiannya>,
    val userProfileTemp: UserProfileEntity,
    private val bookEntity: BookEntity?,
    val kycStatus: KycStatus?,
    val kybStatus: KybStatus?,
    val handleCarouselItemClicks: (Int) -> Unit
) :
    RecyclerView.Adapter<LainnyaProgressBarAdapter.LainnyaProgressBarViewHolder>() {

    inner class LainnyaProgressBarViewHolder constructor(private val binding: ItemProgressBarLainnyaTabBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(item: ProgressBarItemLiannya) {
            when (item.type) {
                ProfileTabFragment.USER_PROFILE -> {
                    handleProfileCarousel(item)
                }
                ProfileTabFragment.USER_BUSINESS_PROFILE -> {
                    handleBusinessProfile(item)
                }
                ProfileTabFragment.USER_KYC_COMPLETE -> {
                    handleKycCarouselItem(item)
                }
                ProfileTabFragment.BANK_ACCOUNT -> {
                    handleBankAccountCarousel(item)
                }
                ProfileTabFragment.BRICK_ACCOUNT -> {
                    handleBrickIntegrationCarousel(item)
                }
            }
        }

        private fun handleKycCarouselItem(item: ProgressBarItemLiannya) {
            with(binding) {
                tvItemHeading.text = item.itemTitle
                Glide.with(root.context).load(item.imageUrl)
                    .into(ivIcon)
                labelSuccess.setTypeface(Typeface.DEFAULT_BOLD)
                when (kycStatus) {
                    KycStatus.PENDING_MANUAL_VERIFICATION, KycStatus.PENDING_VERIFICATION -> {
                        tvItemHeading.text = RemoteConfigUtils.getAppText().kycProcessedBannerTitle
                        tvItemText.text = RemoteConfigUtils.getAppText().kycProcessedBannerMessage
                        labelSuccess.apply {
                            text = context.getString(R.string.in_process)
                            background =
                                context.getDrawableCompat(R.drawable.bg_solid_yellow5_corner_4dp)
                            setDrawable(right = R.drawable.ic_progress_pending)
                            showView()
                        }
                        pbBp.hideView()
                    }
                    KycStatus.REJECTED -> {
                        tvItemHeading.text = RemoteConfigUtils.getAppText().kycRejectedBannerTitle
                        tvItemText.text = RemoteConfigUtils.getAppText().kycRejectedBannerMessage
                        labelSuccess.apply {
                            text = context.getString(R.string.failed_status)
                            background =
                                context.getDrawableCompat(R.drawable.bg_solid_red5_corner_3dp)
                            setDrawable(right = R.drawable.ic_progress_failed)
                            showView()
                        }
                        pbBp.hideView()
                    }
                    KycStatus.MANUALLY_VERIFIED, KycStatus.VERIFIED -> {
                        handleKybStatus()
                    }
                    else -> {
                        tvItemHeading.text = RemoteConfigUtils.getAppText().kycRequiredBannerTitle
                        tvItemText.text = RemoteConfigUtils.getAppText().kycRequiredBannerMessage
                        labelSuccess.hideView()
                        pbBp.progress = 10
                        pbBp.showView()
                    }
                }
            }
        }

        private fun handleKybStatus() {
            with(binding) {
                when (kybStatus) {
                    KybStatus.PENDING_MANUAL_VERIFICATION, KybStatus.PENDING_VERIFICATION -> {
                        tvItemHeading.text = RemoteConfigUtils.getAppText().kybProcessedBannerTitle
                        tvItemText.text = RemoteConfigUtils.getAppText().kybProcessedBannerMessage
                        labelSuccess.apply {
                            text = context.getString(R.string.in_process)
                            background =
                                context.getDrawableCompat(R.drawable.bg_solid_yellow5_corner_4dp)
                            setDrawable(right = R.drawable.ic_progress_pending)
                            showView()
                        }
                        pbBp.hideView()
                    }
                    KybStatus.REJECTED, KybStatus.MANUALLY_REJECTED -> {
                        tvItemHeading.text = RemoteConfigUtils.getAppText().kybRejectedBannerTitle
                        tvItemText.text = RemoteConfigUtils.getAppText().kybRejectedBannerMessage
                        labelSuccess.apply {
                            text = context.getString(R.string.failed_status)
                            background =
                                context.getDrawableCompat(R.drawable.bg_solid_red5_corner_3dp)
                            setDrawable(right = R.drawable.ic_progress_failed)
                            showView()
                        }
                        pbBp.hideView()
                    }
                    KybStatus.MANUALLY_VERIFIED, KybStatus.VERIFIED -> {
                        tvItemHeading.text =
                            RemoteConfigUtils.getAppText().kycKybApprovedBannerTitle
                        tvItemText.text = RemoteConfigUtils.getAppText().kycKybApprovedBannerMessage
                        labelSuccess.apply {
                            text = context.getString(R.string.succeed)
                            background =
                                context.getDrawableCompat(R.drawable.bg_solid_green5_corner_4dp)
                            setDrawable(right = R.drawable.ic_progress_completed)
                            showView()
                        }
                        pbBp.hideView()
                    }
                    else -> {
                        tvItemHeading.text = RemoteConfigUtils.getAppText().kybRequiredBannerTitle
                        tvItemText.text = RemoteConfigUtils.getAppText().kybRequiredBannerMessage
                        labelSuccess.hideView()
                        pbBp.progress = 50
                        pbBp.showView()
                    }
                }
            }
        }

        private fun handleBusinessProfile(item: ProgressBarItemLiannya) {
            with(binding) {
                val userBusinessProfileCompletionStatus =
                    Utility.calculateCompletionPercentage(bookEntity)
                Glide.with(root.context).load(item.imageUrl)
                    .into(ivIcon)
                if (userBusinessProfileCompletionStatus == 100) {
                    tvItemHeading.text = item.itemTitle
                    tvItemText.text = item.successSubtitle
                    pbBp.hideView()
                    labelSuccess.text = item.successChipText
                    labelSuccess.showView()
                } else {
                    tvItemHeading.text =
                        item.itemTitle.plus(" baru ").plus(userBusinessProfileCompletionStatus)
                            .plus("%")
                    tvItemText.text = item.inProgessSubtitle
                    pbBp.progress = userBusinessProfileCompletionStatus
                    pbBp.progressTintList = ColorStateList.valueOf(
                        ContextCompat.getColor(
                            binding.pbBp.context,
                            R.color.buku_CTA
                        )
                    )
                    pbBp.showView()
                    labelSuccess.hideView()
                }
            }
        }

        private fun handleBankAccountCarousel(item: ProgressBarItemLiannya) {
            with(binding) {
                Glide.with(root.context).load(item.imageUrl)
                    .into(ivIcon)
                tvItemHeading.text = item.itemTitle
                if (FeaturePrefManager.getInstance().getFeatureCompletionById(item.type)) {
                    tvItemText.text = item.successSubtitle
                    labelSuccess.text = item.successChipText
                    labelSuccess.showView()
                    pbBp.hideView()
                } else {
                    tvItemText.text = item.emptySubtitle
                    labelSuccess.hideView()
                    pbBp.progress = 5
                    pbBp.progressTintList = ColorStateList.valueOf(
                        ContextCompat.getColor(
                            binding.pbBp.context,
                            R.color.buku_CTA
                        )
                    )
                    pbBp.showView()
                }
            }
        }

        private fun handleBrickIntegrationCarousel(item: ProgressBarItemLiannya) {
            with(binding) {
                tvItemHeading.text = item.itemTitle
                Glide.with(root.context).load(item.imageUrl)
                    .into(ivIcon)
                if (FeaturePrefManager.getInstance().getFeatureCompletionById(item.type)) {
                    tvItemText.text = item.successSubtitle
                    labelSuccess.text = item.successChipText
                    labelSuccess.showView()
                    pbBp.hideView()
                } else {
                    tvItemText.text = item.emptySubtitle
                    labelSuccess.hideView()
                    pbBp.progress = 5
                    pbBp.progressTintList = ColorStateList.valueOf(
                        ContextCompat.getColor(
                            binding.pbBp.context,
                            R.color.buku_CTA
                        )
                    )
                    pbBp.showView()
                }
            }
        }

        private fun handleProfileCarousel(item: ProgressBarItemLiannya) {
            with(binding) {
                val userProfileCompletionStatus =
                    Utility.calculateUserProfileCompletionPercentage(userProfileTemp)
                Glide.with(root.context).load(item.imageUrl)
                    .into(ivIcon)
                if (userProfileCompletionStatus == 100) {
                    tvItemHeading.text = item.itemTitle
                    tvItemText.text = item.successSubtitle
                    pbBp.hideView()
                    labelSuccess.text = item.successChipText
                    labelSuccess.showView()
                } else {
                    tvItemHeading.text =
                        item.itemTitle.plus(" baru ").plus(userProfileCompletionStatus).plus("%")
                    tvItemText.text = item.inProgessSubtitle
                    pbBp.progress = userProfileCompletionStatus
                    pbBp.progressTintList = ColorStateList.valueOf(
                        ContextCompat.getColor(
                            binding.pbBp.context,
                            R.color.buku_CTA
                        )
                    )
                    pbBp.showView()
                    labelSuccess.hideView()
                }
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): LainnyaProgressBarViewHolder {
        return LainnyaProgressBarViewHolder(
            ItemProgressBarLainnyaTabBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        ).listen { pos, type ->
            handleCarouselItemClicks(pos)
        }
    }

    override fun onBindViewHolder(holder: LainnyaProgressBarViewHolder, position: Int) {
        holder.bind(itemList[position])
    }

    override fun getItemCount(): Int = itemList.size

}