package com.bukuwarung.activities.profile.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.databinding.PpobBannerItemBinding
import com.bukuwarung.model.BannerInfo
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.setSingleClickListener
import com.bumptech.glide.Glide

class ProfileBannerAdapter(private val bannerClick: (String, String?) -> Unit) : RecyclerView.Adapter<ProfileBannerAdapter.BannerInfoViewHolder>() {
    private var bannerInfoList: List<BannerInfo> = listOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProfileBannerAdapter.BannerInfoViewHolder {
        return BannerInfoViewHolder(PpobBannerItemBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun getItemCount(): Int = bannerInfoList.size


    override fun onBindViewHolder(holder: ProfileBannerAdapter.BannerInfoViewHolder, position: Int) {
        holder.bind(bannerInfoList[position])
    }

    fun setItem(list: List<BannerInfo>) {
        this.bannerInfoList = list
        notifyDataSetChanged()
    }

    inner class BannerInfoViewHolder constructor(private val binding: PpobBannerItemBinding) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setSingleClickListener {
                if (adapterPosition != -1) {
                    val redirectionUrl = bannerInfoList[adapterPosition].redirectionUrl
                    if (redirectionUrl.isNotNullOrBlank()) {
                        bannerClick(redirectionUrl.orEmpty(), bannerInfoList[adapterPosition].type)
                    }
                }
            }
        }

        fun bind(bannerInfo: BannerInfo) {
            with(binding) {
                bannerInfo.bannerUrl?.let { bannerUrl ->
                    Glide.with(root.context).load(bannerUrl).into(ivBanner)
                }
            }
        }
    }
}
