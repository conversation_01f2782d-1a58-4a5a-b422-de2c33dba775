package com.bukuwarung.activities.profile.summary;

public class SummaryViewModel {

    private final int credit;
    private final int debit;
    private final double totalCredit;
    private final double totalDebit;

    public SummaryViewModel(int credit, int debit, double totalCredit, double totalDebit) {
        this.credit = credit;
        this.debit = debit;
        this.totalCredit = totalCredit;
        this.totalDebit = totalDebit;
    }

    public boolean equals(Object obj) {
        if (this != obj) {
            if (obj instanceof SummaryViewModel) {
                SummaryViewModel summaryViewModel = (SummaryViewModel) obj;
                if (this.credit == summaryViewModel.credit) {
                    if (this.debit == summaryViewModel.debit) {
                        if (!(Double.compare(this.totalCredit, summaryViewModel.totalCredit) == 0 && Double.compare(this.totalDebit, summaryViewModel.totalDebit) == 0)) {
                            return false;
                        }
                    }
                }
            }
            return false;
        }
        return true;
    }

    public int hashCode() {
        int i = ((((this.credit * 31) + this.debit) * 31)) * 31;
        long doubleToLongBits = Double.doubleToLongBits(this.totalCredit);
        int i2 = (i + ((int) (doubleToLongBits ^ (doubleToLongBits >>> 32)))) * 31;
        long doubleToLongBits2 = Double.doubleToLongBits(this.totalDebit);
        return i2 + ((int) (doubleToLongBits2 ^ (doubleToLongBits2 >>> 32)));
    }

    public double getTotalCredit() {
        return totalCredit;
    }

    public double getTotalDebit() {
        return totalDebit;
    }

}
