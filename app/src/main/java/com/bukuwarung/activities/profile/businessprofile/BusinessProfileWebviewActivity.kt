package com.bukuwarung.activities.profile.businessprofile

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.provider.MediaStore
import android.view.KeyEvent
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.webkit.WebSettings.RenderPriority
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatDelegate
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.GeneralLoadingDialog
import com.bukuwarung.activities.profile.update.dialogs.DeleteBusinessDialog
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.utils.CoroutineHelper
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog2
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.utils.AppIdGenerator
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.recordException
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File

class BusinessProfileWebviewActivity : Activity() {
    public var fromMission: Boolean = false
    private lateinit var mContext: Context
    internal var mLoaded = false

    private var loadingDialog: GeneralLoadingDialog? = null

    internal var URL = RemoteConfigUtils.getBusinessProfileUrl()

    internal var doubleBackToExitPressedOnce = false

    lateinit var bookId: String

    //AdView adView;
    private lateinit var mWebView: WebView
    private lateinit var prgs: ProgressBar
    private lateinit var toolbar: androidx.appcompat.widget.Toolbar
    private lateinit var deleteBtn: TextView
    private lateinit var backBtn: ImageView
    private lateinit var layoutWebview: RelativeLayout

    private var profilePicUri: Uri? = null
    private var profilePicFile: File? = null
    private val coroutineHandler = CoroutineHelper()

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_webview_business_profile)
        loadingDialog = GeneralLoadingDialog.createInstance()
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true)
        mContext = this

        mWebView = findViewById<View>(R.id.webview) as WebView
        prgs = findViewById<View>(R.id.progressBar) as ProgressBar
        layoutWebview = findViewById<View>(R.id.layout_webview) as RelativeLayout
        toolbar = findViewById<View>(R.id.toolbar) as androidx.appcompat.widget.Toolbar
        deleteBtn = findViewById<View>(R.id.delete_business_btn) as TextView
        if (intent.hasExtra(IS_CREATE_BOOK)) {
            deleteBtn.visibility = View.INVISIBLE
            bookId = ""
        } else {
            bookId = intent.getStringExtra(BOOKID) ?: User.getBusinessId()
            fromMission = intent.getBooleanExtra(FROM_MISSION, false)
            deleteBtn.setOnClickListener { showDeletionDialog(this) }
        }
        backBtn = findViewById<View>(R.id.close) as ImageView
        backBtn.setOnClickListener { onBackPressed() }
        requestForWebview()
    }

    override fun onDestroy() {
        super.onDestroy()
        coroutineHandler.cancel()
    }

    private fun showDeletionDialog(activity: Activity) {
        // book entity can be null some times
        var businessName: String? = "-"
        var bookEntity = BusinessRepository.getInstance(this).getBusinessByIdSync(User.getBusinessId())
        if (bookEntity != null) businessName = bookEntity!!.businessName
        val dialog = DeleteBusinessDialog(
            activity,
            businessName
        ) { promptResult: Boolean ->
            if (promptResult) {
                deleteBook()
            }
        }
        dialog.show()
    }

    private fun deleteBook() {
        BusinessRepository.getInstance(this)?.updateDeleteFlag(User.getUserId(), User.getDeviceId(), User.getBusinessId(), Integer.valueOf(1))
        MainActivity.startActivityAndClearTop(this, "open_side_menu", true)
    }

    private fun requestForWebview() {

        if (!mLoaded) {
            requestWebView()
            prgs.visibility = View.VISIBLE
            mWebView.visibility = View.VISIBLE

        } else {
            mWebView.visibility = View.VISIBLE
            prgs.visibility = View.GONE
        }

    }

    @SuppressLint("SetJavaScriptEnabled")
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private fun requestWebView() {
        /** Layout of webview screen View  */
        if (internetCheck(mContext)) {
            mWebView.visibility = View.VISIBLE
            mWebView.loadUrl(URL)
        } else {
            prgs.visibility = View.GONE
            mWebView.visibility = View.GONE

            return
        }
        mWebView.isFocusable = true
        mWebView.isFocusableInTouchMode = true
        mWebView.settings.javaScriptEnabled = true
        mWebView.settings.setRenderPriority(RenderPriority.HIGH)
        mWebView.settings.cacheMode = WebSettings.LOAD_DEFAULT
        mWebView.clearHistory()
        mWebView.settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

        mWebView.settings.domStorageEnabled = true
        if (RemoteConfigUtils.doWebviewForceReload()) {
            mWebView.clearCache(true)
        }
        mWebView.settings.databaseEnabled = true
        mWebView.settings.setSupportMultipleWindows(false)
        mWebView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView, url: String?): Boolean {

                if (internetCheck(mContext)) {
                    url?.let {
                        view.loadUrl(it)
                    }
                } else {
                    prgs.visibility = View.GONE
                    mWebView.visibility = View.GONE
                }

                return true
            }

            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                if (prgs.visibility == View.GONE) {
//                    prgs.visibility = View.VISIBLE
                }
            }

            override fun onLoadResource(view: WebView, url: String) {
                super.onLoadResource(view, url)
            }

            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
                mLoaded = true
                val sessionManager = SessionManager.getInstance()
                val token = sessionManager.bukuwarungToken
                val userId = sessionManager.userId

                val script = StringBuilder("javascript:supplyToken(")
                script.append("'").append(token).append("'").append(",")
                script.append("'").append(userId).append("'").append(",")
                if (!Utility.isBlank(bookId)) {
                    script.append("'").append(bookId).append("'").append(",")
                }
                script.append(")")
                view.evaluateJavascript(script.toString(), null)
                if (prgs.visibility == View.VISIBLE)
                    prgs.visibility = View.GONE
                toolbar.visibility = View.VISIBLE
            }
        }

        mWebView.addJavascriptInterface(JsObject(this, loadingDialog, fromMission), "BusinessProfileWebView")

    }

    internal class JsObject(
        private val parentActivity: BusinessProfileWebviewActivity?,
        private val loadingDialog: GeneralLoadingDialog?,
        val fromMission: Boolean
    ) {

        @JavascriptInterface
        fun onClickSave(businessId: String, profileCompletionProgress: String) {
            SetupManager.getInstance().setSyncedBookData(false)
            var completionPercentage = "40";
            if (!Utility.isBlank(profileCompletionProgress)) {
                completionPercentage = profileCompletionProgress;
            }
            if (Utility.isEqual(businessId, User.getBusinessId())) {
                //AsyncBookDataSync(parentActivity, businessId, completionPercentage, false).execute()
                parentActivity?.coroutineHandler?.syncBookData(parentActivity, businessId, completionPercentage.toInt(), false)
                parentActivity?.finish()
            } else {
                //AsyncBookDataSync(parentActivity, businessId, completionPercentage, true).execute()
                parentActivity?.coroutineHandler?.syncBookData(parentActivity, businessId, completionPercentage.toInt(), true)
            }
            if (fromMission && profileCompletionProgress == "100") {
                parentActivity?.finish()
            }
        }

        @JavascriptInterface
        fun uploadBusinessImage() {
            parentActivity?.showImageSelectorDialog()
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && mWebView.canGoBack()) {
            mWebView.goBack()
            return true
        }

        if (doubleBackToExitPressedOnce) {
            return super.onKeyDown(keyCode, event)
        }

        this.doubleBackToExitPressedOnce = true

        Handler().postDelayed({ doubleBackToExitPressedOnce = false }, 2000)
        return true
    }

    companion object {

        private val BOOKID: String = "BOOKID"
        private val FROM_MISSION: String = "fromMission"
        private val IS_CREATE_BOOK: String = "IS_CREATE_BOOK"

        @kotlin.jvm.JvmField
        var WEBVIEW_PARAM_IS_DAILY_UPDATE = "webview_param_is_daily_update"

        fun createIntent(origin: Context?, link: String?, title: String?, bookId: String, isCreateBook: Boolean): Intent {
            val intent = Intent(origin, BusinessProfileWebviewActivity::class.java)
            intent.putExtra(BaseWebviewActivity.LINK, link)
            intent.putExtra(BaseWebviewActivity.TITLE, title)
            intent.putExtra(BOOKID, bookId)
            intent.putExtra(IS_CREATE_BOOK, isCreateBook)
            return intent
        }

        fun createIntent(origin: Context?, fromMission: Boolean = false): Intent {
            val intent = Intent(origin, BusinessProfileWebviewActivity::class.java)
            intent.putExtra(BOOKID, User.getBusinessId())
            intent.putExtra(FROM_MISSION, fromMission)
            return intent
        }

        fun internetCheck(context: Context): Boolean {
            var available = false
            val connectivity = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            if (connectivity != null) {
                val networkInfo = connectivity.allNetworkInfo
                if (networkInfo != null) {
                    for (i in networkInfo.indices) {
                        if (networkInfo[i].state == NetworkInfo.State.CONNECTED) {
                            available = true
                            break
                        }
                    }
                }
            }
            return available
        }
    }

    private fun showImageSelectorDialog() {

        val imageSelectorDialog = ImageSelectorDialog2(this, {
            setImageSourceFromCamera()
        }, {
            getImageFromGallery()
        })
        imageSelectorDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_PROFILE_PIC)
        imageSelectorDialog.show()
    }

    private fun getImageFromGallery() {
        if (Build.VERSION.SDK_INT >= 23 && checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(
                PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                PermissionConst.REQ_PICK_IMAGE_PERMISSON
            )
            return
        }

        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        startActivityForResult(
            Intent.createChooser(
                intent,
                getString(R.string.select_image_instruction)
            ), PermissionConst.REQ_PICK_IMAGE_PERMISSON
        )
    }

    private fun setImageSourceFromCamera() {
        try {
            profilePicFile = ImageUtils.createTempFile()
            profilePicUri = ImageUtils.getUriFromFile(profilePicFile)
            val intent = Intent("android.media.action.IMAGE_CAPTURE")
            intent.putExtra("output", profilePicUri)
            intent.addFlags(Intent.FLAG_EXCLUDE_STOPPED_PACKAGES)
            if (Build.VERSION.SDK_INT >= 23 && checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    PermissionConst.CAMER_AND_STORAGE,
                    PermissionConst.REQ_TAKE_PICTURE_PERMISSON
                )
            }
            if (intent.resolveActivity(packageManager) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.REQ_TAKE_PICTURE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    setImageSourceFromCamera()
                }
            }
            PermissionConst.REQ_PICK_IMAGE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    getImageFromGallery()
                }
            }


        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        try {
            var uri: Uri? = null
            if (resultCode == RESULT_OK && requestCode == PermissionConst.TAKE_PHOTO) {
                uri = profilePicUri
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_USER_PROFILE_PIC_CAMERA)
            } else if (requestCode == PermissionConst.REQ_PICK_IMAGE_PERMISSON && resultCode == RESULT_OK && intent != null && intent.data != null) {
                uri = intent.data
            }

            uri?.let {
                var bitmap = MediaStore.Images.Media.getBitmap(
                    contentResolver,
                    uri
                )

                bitmap = Utility.fixImageRotation(this, bitmap, profilePicFile, it)
                profilePicFile = null

                bitmap?.let {
                    uploadBusinessImageWithTask(it, User.getUserId()) { imageUrl ->
                        val script = StringBuilder("javascript:setBusinessProfileImage(")
                        script.append("'").append(imageUrl).append("'")
                        script.append(")")
                        mWebView.evaluateJavascript(script.toString(), null)
                    }
                }

//                viewModel.onEventReceived(ProfileTabViewModel.Event.OnUploadUserProfileImage(bitmap, uri))
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    fun uploadBusinessImageWithTask(image: Bitmap?, userId: String, callback: (String?) -> Unit) {
        val resourceId: String = AppIdGenerator.resourceUUID()
        CoroutineScope(Dispatchers.IO).launch {
            try {
                if (image == null) {
                    callback(null)
                    return@launch
                }

                val storage = FirebaseStorage.getInstance().reference.child("customer_profile/$userId/$resourceId.jpg")

                storage.putBytes(compressAsByteArray(image)).continueWithTask { task ->
                    if (!task.isSuccessful) {
                        task.exception?.let {
                            it.recordException()
                            callback(null)
                        }
                    }
                    storage.downloadUrl
                }.addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        callback(task.result.toString())
                    } else {
                        callback(null)
                    }
                }

            } catch (ex: Exception) {
                ex.recordException()
            }
        }
    }

    private suspend fun compressAsByteArray(image: Bitmap): ByteArray {
        return withContext(Dispatchers.IO) {
            val stream = ByteArrayOutputStream()
            image.compress(Bitmap.CompressFormat.JPEG, 45, stream)
            stream.toByteArray()
        }
    }

}