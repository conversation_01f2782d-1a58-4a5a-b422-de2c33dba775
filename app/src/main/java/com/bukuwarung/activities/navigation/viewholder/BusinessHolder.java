package com.bukuwarung.activities.navigation.viewholder;

import com.bukuwarung.activities.business.CreateBusinessActivity;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.enums.RowComponent;


public abstract class BusinessHolder {

    private int tag;

    public static final class BusinessRowHolder extends BusinessHolder {

        private final BookEntity bookEntity;

        public BusinessRowHolder(BookEntity entity) {

            this.bookEntity = entity;
            setTag(CreateBusinessActivity.MORE_BUSINESS);
        }

        public final BookEntity getBookEntity() {
            return this.bookEntity;
        }

    }

    public static final class SelectedBusinessRowHolder extends BusinessHolder {
        private final BookEntity bookEntity;

        public SelectedBusinessRowHolder(BookEntity entity) {
            this.bookEntity = entity;
            setTag(RowComponent.SELECTED_BUSINESS.ordinal());
        }

        public final BookEntity getBookEntity() {
            return this.bookEntity;
        }
    }

    public static final class EmptyBusinessHolder extends BusinessHolder {
        public EmptyBusinessHolder() {
            setTag(RowComponent.EMPTY.ordinal());
        }
    }

    public final int getTag() {
        return this.tag;
    }

    public final void setTag(int i) {
        this.tag = i;
    }
}
