package com.bukuwarung.activities.navigation.adapter;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.View.OnClickListener;

import androidx.core.view.GravityCompat;

import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.navigation.SideNavActivity;
import com.bukuwarung.activities.navigation.viewholder.BusinessHolder;
import com.bukuwarung.activities.navigation.viewholder.BusinessHolder.BusinessRowHolder;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.session.SessionManager;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

final class NavigationItemClickedListener implements OnClickListener {

    final NavigationAdapter adapter;

    NavigationItemClickedListener(NavigationAdapter businessAdapter) {
        this.adapter = businessAdapter;
    }

    public final void onClick(View view) {

        int childAdapterPosition = this.adapter.getRecyclerView().getChildAdapterPosition(view);

        try {

            BusinessHolder businessHolder = this.adapter.businessHolderList.get(childAdapterPosition);

            if (businessHolder instanceof BusinessRowHolder) {
                SessionManager.getInstance().setBusinessId(((BusinessRowHolder) businessHolder).getBookEntity().bookId);
                SessionManager.getInstance().setAppState(AppConst.APP_STATE_ALL_DELETED);

                MainActivity.startActivityAndClearTop(this.adapter.getContext());

                Context context = this.adapter.getContext();

                if (context != null) {
                    try {
                        ((MainActivity) context).finish();
                    }catch (Exception e){
                        ((SideNavActivity) context).finish();
                    }
                    return;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
