package com.bukuwarung.activities.navigation.viewholder;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.bukuwarung.R;
import com.bukuwarung.activities.navigation.adapter.NavigationAdapter;

public final class SelectedBusinessViewHolder extends ViewHolder {
    private ImageView businessIcon;
    private TextView name;
    final NavigationAdapter adapter;

    public SelectedBusinessViewHolder(NavigationAdapter businessAdapter, View view) {
        super(view);
        this.adapter = businessAdapter;
        this.businessIcon = view.findViewById(R.id.nav_icon);
        this.name = view.findViewById(R.id.name);
    }

    public final ImageView getBusinessIcon() {
        return this.businessIcon;
    }

    public final TextView getName() {
        return this.name;
    }
}
