package com.bukuwarung.activities.navigation;

import android.content.Intent;
import android.content.pm.PackageInfo;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.TextView;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProviders;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.business.CreateBusinessActivity;
import com.bukuwarung.activities.navigation.adapter.NavigationAdapter;
import com.bukuwarung.activities.navigation.viewmodel.BusinessViewModel;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.utils.Utility;
import com.google.android.material.button.MaterialButton;

import java.util.List;

public final class SideNavActivity extends AppActivity implements OnClickListener {

    private NavigationAdapter adapter;
    private BusinessViewModel businessViewModel;
    private RecyclerView businessList;

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView((int) R.layout.activity_side_nav);

        final MaterialButton createNewTextBtn = findViewById(R.id.add_new_business_btn);
        createNewTextBtn.setOnClickListener(this);

        try {
            TextView versionTv = findViewById(R.id.appVersion);
            PackageInfo pInfo = getApplicationContext().getPackageManager().getPackageInfo(getPackageName(), 0);
            versionTv.setText("version "+pInfo.versionName+"("+pInfo.versionCode+")");
        }catch (Exception e){
            e.printStackTrace();
        }


        this.businessList = findViewById(R.id.business_list);

        this.adapter = new NavigationAdapter(this.businessList, this);
        this.businessList.setAdapter(this.adapter);
        this.businessList.setLayoutManager(new LinearLayoutManager(this));

        this.businessViewModel = ViewModelProviders.of(this).get(BusinessViewModel.class);

        LiveData businessList = this.businessViewModel.getBusinessList();

        businessList.observe(this, new Observer<List<BookEntity>>() {
            @Override
            public final void onChanged(List<BookEntity> list) {
            adapter.setBusinessHolderList(list);
            if (list == null || list.size() == 0) {
                createNewTextBtn.setVisibility(View.GONE);
                return;
            }
            createNewTextBtn.setVisibility(View.VISIBLE);
            }
        });

    }

    @Override
    public void onBackPressed() {
        return;
    }

    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.add_first_business :
                Intent intent = new Intent(this, CreateBusinessActivity.class);
                intent.putExtra(CreateBusinessActivity.EVENT_TYPE, CreateBusinessActivity.FIRST_BUSINESS);
                startActivity(intent);
                return;
            case R.id.add_new_business_btn:
                Intent moreBizintent = new Intent(this, CreateBusinessActivity.class);
                moreBizintent.putExtra(CreateBusinessActivity.EVENT_TYPE, CreateBusinessActivity.MORE_BUSINESS);
                startActivity(moreBizintent);
                AppAnalytics.trackEvent("customer_add_new_business");
                return;
            default:
                return;
        }
    }
}
