package com.bukuwarung.activities.navigation.viewholder;

import android.view.View;

import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.bukuwarung.R;
import com.bukuwarung.activities.navigation.adapter.NavigationAdapter;
import com.google.android.material.button.MaterialButton;

public final class EmptyViewHolder extends ViewHolder {

    private MaterialButton createNew;
    final NavigationAdapter adapter;

    public EmptyViewHolder(NavigationAdapter businessAdapter, View view) {
        super(view);
        this.adapter = businessAdapter;
        this.createNew = view.findViewById(R.id.add_first_business);
    }

    public final MaterialButton getCreateNew() {
        return this.createNew;
    }

}


