package com.bukuwarung.activities.navigation.adapter;

import static com.bukuwarung.constants.AnalyticsConst.LEFT_DRAWER_HAMBURGER_MENU;
import static com.bukuwarung.preference.OnboardingPrefManager.TUTOR_DAILY_BUSINESS_UPDATE;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.view.GravityCompat;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.bukuwarung.R;
import com.bukuwarung.activities.dailyupdatestates.DailyUpdateNoTxnActivity;
import com.bukuwarung.activities.dailyupdatestates.DailyUpdateWebviewActivity;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.navigation.viewholder.BusinessHolder;
import com.bukuwarung.activities.navigation.viewholder.BusinessHolder.BusinessRowHolder;
import com.bukuwarung.activities.navigation.viewholder.BusinessHolder.EmptyBusinessHolder;
import com.bukuwarung.activities.navigation.viewholder.BusinessViewHolder;
import com.bukuwarung.activities.navigation.viewholder.EmptyViewHolder;
import com.bukuwarung.activities.navigation.viewholder.SelectedBusinessViewHolder;
import com.bukuwarung.activities.profile.businessprofile.BusinessProfileWebviewActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.enums.RowComponent;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.TooltipBuilder;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.List;

public final class NavigationAdapter extends Adapter<ViewHolder> {

    private final Context context;

    public ArrayList<BusinessHolder> businessHolderList = new ArrayList<>();
    private final OnClickListener mClickedListener = new NavigationItemClickedListener(this);
    private final RecyclerView mRecyclerView;
    private int selectedBusinessPosition;
    FeaturePrefManager featurePref = FeaturePrefManager.getInstance();

    public final Context getContext() {
        return this.context;
    }

    public final RecyclerView getRecyclerView() {
        return this.mRecyclerView;
    }

    public NavigationAdapter(RecyclerView recyclerView, Context ctx) {
        this.mRecyclerView = recyclerView;
        this.context = ctx;
    }

    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {

        //Show tooltip for first time when introducing daily business update
        if (i == 0 && featurePref.isDailyHighlightSettingsVisible() && !featurePref.isDailyHighlightExplored()) {

            TooltipBuilder.Companion.builder(context)
                    .setGravity(Gravity.BOTTOM)
                    .setText(context.getString(R.string.always_check_transaction_recap))
                    .setAnchor(viewHolder.itemView.findViewById(R.id.iv_daily_update_icon))
                    .build()
                    .show();
        }

        BusinessHolder businessHolder = this.businessHolderList.get(i);
        if (viewHolder.getItemViewType() == RowComponent.EMPTY.ordinal()) {
            emptyViewBindHolder((EmptyViewHolder) viewHolder);
            return;
        }
        if (viewHolder.getItemViewType() == RowComponent.SELECTED_BUSINESS.ordinal()) {
            SelectedBusinessViewHolder businessViewHolder = (SelectedBusinessViewHolder) viewHolder;
            if (businessHolder != null) {
                BusinessHolder.SelectedBusinessRowHolder businessRowHolder = (BusinessHolder.SelectedBusinessRowHolder) businessHolder;
                SelectedBusinessBindViewHolder(businessViewHolder, businessRowHolder);
                setupSelectedView(viewHolder, (BusinessHolder.SelectedBusinessRowHolder) businessHolder, businessRowHolder);
            }
            return;
        }
        BusinessViewHolder businessViewHolder = (BusinessViewHolder) viewHolder;
        if (businessHolder != null) {
            BusinessRowHolder businessRowHolder = (BusinessRowHolder) businessHolder;
            BusinessBindViewHolder(businessViewHolder, businessRowHolder);
            setupView(viewHolder, (BusinessRowHolder) businessHolder, businessRowHolder);
        }
    }

    private void setupView(@NonNull ViewHolder viewHolder, BusinessRowHolder businessHolder, BusinessRowHolder businessRowHolder) {
        if(businessHolder.getBookEntity().isDailyBusinessUpdateSeen==1){
            ((ImageView)viewHolder.itemView.findViewById(R.id.iv_daily_update_icon)).setImageResource(R.drawable.ic_daily_update_icon_greyed);
        }
        //handle daily update click
        viewHolder.itemView.findViewById(R.id.iv_daily_update_icon).setOnClickListener(v -> openDailyUpdate(businessHolder.getBookEntity(),businessHolder.getBookEntity().bookId));
        if (featurePref.useDailyBusinessRecap()) {
            viewHolder.itemView.findViewById(R.id.iv_daily_update_icon).setVisibility(View.VISIBLE);
        } else {
            viewHolder.itemView.findViewById(R.id.iv_daily_update_icon).setVisibility(View.GONE);
        }

        if (Utility.isEqual(businessRowHolder.getBookEntity().bookId, User.getBusinessId())) {
            viewHolder.itemView.setBackgroundColor(this.context.getResources().getColor(R.color.colorPrimary));
            View view = viewHolder.itemView;
//                ((ImageView) view.findViewById(R.id.forward)).setImageResource(R.drawable.ic_check);
            ((ImageView) view.findViewById(R.id.forward)).setColorFilter(this.context.getResources().getColor(R.color.white));
//                ((ImageView) view.findViewById(R.id.forward)).setBackgroundDrawable(this.context.getResources().getDrawable(R.drawable.blue_circle));
            viewHolder.itemView.getBackground().setAlpha(40);
            return;
        }

        View view = viewHolder.itemView;
        view.setBackgroundColor(this.context.getResources().getColor(R.color.white));
//            ((ImageView) view.findViewById(R.id.forward)).setImageResource(R.drawable.ic__nav_next);
        ((ImageView)view.findViewById(R.id.forward)).setColorFilter(Color.GRAY);
        view.findViewById(R.id.forward).setBackgroundDrawable(null);
        view.getBackground().setAlpha(100);
    }

    private void setupSelectedView(@NonNull ViewHolder viewHolder, BusinessHolder.SelectedBusinessRowHolder businessHolder, BusinessHolder.SelectedBusinessRowHolder businessRowHolder) {
        //handle daily update click
        if(businessHolder.getBookEntity().isDailyBusinessUpdateSeen==1){
            ((ImageView)viewHolder.itemView.findViewById(R.id.iv_daily_update_icon)).setImageResource(R.drawable.ic_daily_update_icon_greyed);
        }
        viewHolder.itemView.findViewById(R.id.iv_daily_update_icon).setOnClickListener(v -> openDailyUpdate(businessHolder.getBookEntity(), businessHolder.getBookEntity().bookId));
        if (featurePref.useDailyBusinessRecap()) {
            viewHolder.itemView.findViewById(R.id.iv_daily_update_icon).setVisibility(View.VISIBLE);
        } else {
            viewHolder.itemView.findViewById(R.id.iv_daily_update_icon).setVisibility(View.GONE);
        }

        // TODO set progress from API
        businessHolder.getBookEntity().profileCompletionProgress = Utility.calculateCompletionPercentage(businessHolder.getBookEntity());
        if(businessHolder.getBookEntity().profileCompletionProgress!=100)
        {
            ((TextView) (viewHolder.itemView.findViewById(R.id.tv_profile_progress))).setText(businessHolder.getBookEntity().profileCompletionProgress+"%");
            ((TextView) (viewHolder.itemView.findViewById(R.id.tv_business_profile_label))).setVisibility(View.VISIBLE);
        }else {
            ((TextView) (viewHolder.itemView.findViewById(R.id.tv_profile_progress))).setVisibility(View.GONE);
            ((TextView) (viewHolder.itemView.findViewById(R.id.tv_business_profile_label))).setVisibility(View.INVISIBLE);
        }


        ((TextView)(viewHolder.itemView.findViewById(R.id.tv_profile_progress))).getBackground().setColorFilter(context.getResources().getColor(R.color.green),
                PorterDuff.Mode.SRC_ATOP);


        if (Utility.isEqual(businessRowHolder.getBookEntity().bookId, User.getBusinessId())) {
            viewHolder.itemView.setBackgroundColor(this.context.getResources().getColor(R.color.colorPrimary));
            View view = viewHolder.itemView;
//                ((ImageView) view.findViewById(R.id.forward)).setImageResource(R.drawable.ic_check);
            ((ImageView) view.findViewById(R.id.forward)).setColorFilter(this.context.getResources().getColor(R.color.white));
//                ((ImageView) view.findViewById(R.id.forward)).setBackgroundDrawable(this.context.getResources().getDrawable(R.drawable.blue_circle));
            viewHolder.itemView.getBackground().setAlpha(40);
            return;
        }

        View view = viewHolder.itemView;
        view.setBackgroundColor(this.context.getResources().getColor(R.color.white));
//            ((ImageView) view.findViewById(R.id.forward)).setImageResource(R.drawable.ic__nav_next);
        ((ImageView)view.findViewById(R.id.forward)).setColorFilter(Color.GRAY);
        view.findViewById(R.id.forward).setBackgroundDrawable(null);
        view.getBackground().setAlpha(100);
    }

    private void openDailyUpdate(BookEntity bookEntity, String bookId) {

        if(bookEntity.isDailyBusinessUpdateSeen == 0){
            bookEntity.isDailyBusinessUpdateSeen = 1;
            ((MainActivity)context).saveUpdatedBookEntity(bookEntity);
        }

        AppAnalytics.PropBuilder builder = new AppAnalytics.PropBuilder();

        FeaturePrefManager.getInstance().setDailyHighlightExplored(true);
        if(TransactionRepository.getInstance(context).getYesterdayTransactionCountWithBookIdAndDate(bookId, DateTimeUtils.getYesterdayDate(context.getString(R.string.yyyy_MM_dd))) > 0)
        {
            builder.put(AnalyticsConst.DAILY_BUSINESS_DATA_AVAILABLE, true);
            String url = RemoteConfigUtils.INSTANCE.getDailyUpdateUrl();
            Intent webViewIntent = DailyUpdateWebviewActivity.Companion.createIntent(context, url, "",bookId);
            webViewIntent.putExtra(DailyUpdateWebviewActivity.WEBVIEW_PARAM_IS_DAILY_UPDATE, true);
            context.startActivity(webViewIntent);
        }
        else {
            builder.put(AnalyticsConst.DAILY_BUSINESS_DATA_AVAILABLE, false);
            ((MainActivity)context).homelayout.closeDrawer(GravityCompat.START);
            Intent intent = new Intent(context, DailyUpdateNoTxnActivity.class);
            intent.putExtra(DailyUpdateNoTxnActivity.BOOK_ID,bookId);
            context.startActivity(intent);
        }

//        BookEntity bookEntity = BusinessRepository.getInstance(context).getBusinessByIdSync(bookId);
        builder.put(AnalyticsConst.ENTRY_POINT, LEFT_DRAWER_HAMBURGER_MENU);
        builder.put(AnalyticsConst.BUSINESS_NAME, bookEntity.businessName);
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_BW_STORY, builder);
    }

    private void BusinessBindViewHolder(BusinessViewHolder businessViewHolder, BusinessRowHolder businessRowHolder) {
        BookEntity bookEntity = businessRowHolder.getBookEntity();
        businessViewHolder.getName().setText(bookEntity.businessName);
        businessViewHolder.getBusinessIcon().setImageResource(R.drawable.ic_business_big);
    }

    private void SelectedBusinessBindViewHolder(SelectedBusinessViewHolder businessViewHolder, BusinessHolder.SelectedBusinessRowHolder businessRowHolder) {
        BookEntity bookEntity = businessRowHolder.getBookEntity();
        businessViewHolder.getName().setText(bookEntity.businessName);
        businessViewHolder.getBusinessIcon().setImageResource(R.drawable.ic_business_big);
    }

    private void emptyViewBindHolder(EmptyViewHolder emptyViewHolder) {
        emptyViewHolder.getCreateNew().setOnClickListener(new CreateBusinessClickHandler(this));
    }

    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

        if (i == RowComponent.EMPTY.ordinal()) {
            View inflateTarget = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.navigation_item_empty, viewGroup, false);
            return new EmptyViewHolder(this, inflateTarget);
        }

        if (i == RowComponent.SELECTED_BUSINESS.ordinal()) {
            View inflateTarget = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.navigation_item_selected, viewGroup, false);
            inflateTarget.setOnClickListener(this.mClickedListener);

            return new SelectedBusinessViewHolder(this, inflateTarget);
        }

        View inflateTarget = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.navigation_item, viewGroup, false);
        inflateTarget.setOnClickListener(this.mClickedListener);

        return new BusinessViewHolder(this, inflateTarget);
    }

    public int getItemViewType(int i) {
        return this.businessHolderList.get(i).getTag();
    }

    public int getItemCount() {
        return this.businessHolderList.size();
    }

    public final void setBusinessHolderList(List<BookEntity> businessList) {
        this.businessHolderList = new ArrayList<>();
        if (businessList == null || businessList.size() == 0) {
            this.businessHolderList.add(new EmptyBusinessHolder());
        } else {
            ArrayList<BusinessHolder> holderList = this.businessHolderList;
            List<BusinessHolder> entityToHolderList = new ArrayList();
            for (BookEntity bookEntity : businessList) {
//                entityToHolderList.add(new BusinessRowHolder(bookEntity));
                if (Utility.isEqual(bookEntity.bookId, SessionManager.getInstance().getBusinessId())) {
                    entityToHolderList.add(new BusinessHolder.SelectedBusinessRowHolder(bookEntity));
                }else{
                    entityToHolderList.add(new BusinessRowHolder(bookEntity));
                }
            }
            holderList.addAll(entityToHolderList);
            for (BookEntity bookEntity : businessList) {
                if (Utility.isEqual(bookEntity.bookId, SessionManager.getInstance().getBusinessId())) {
                    this.selectedBusinessPosition = businessList.indexOf(bookEntity);
                }
            }
        }
        notifyDataSetChanged();
    }

    public final int getSelectedBusinessPosition() {
        return this.selectedBusinessPosition;
    }
}