package com.bukuwarung.activities.edc.cardhistory.data.repository

import com.bukuwarung.activities.edc.cardhistory.data.api.CardTransactionHistoryApi
import javax.inject.Inject

class CardHistoryRepository @Inject constructor(private val cardTransactionHistoryApi: CardTransactionHistoryApi) {

    suspend fun getCardHistory(
        accountId: String,
        pageNumber: Int,
        pageSize: Int,
        order: String?,
        startDate: String?,
        endDate: String?,
        type: String?
    ) = cardTransactionHistoryApi.getTransactionDetails(
        accountId,
        type,
        pageNumber,
        pageSize,
        order,
        startDate,
        endDate
    )

}