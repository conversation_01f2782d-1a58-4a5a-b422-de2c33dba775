package com.bukuwarung.activities.edc.orderdetail.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.R
import com.bukuwarung.activities.HelpCenterActivity
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.edc.orderdetail.model.Beneficiary
import com.bukuwarung.activities.edc.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.activities.edc.orderdetail.model.RefundRequest
import com.bukuwarung.activities.edc.orderdetail.viewmodel.EdcOrderDetailViewModel
import com.bukuwarung.activities.edc.orderhistory.enums.EdcOrderStatus
import com.bukuwarung.activities.edc.orderhistory.enums.EdcOrderType
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.databinding.ActivityEdcOrderDetailsBinding
import com.bukuwarung.dialogs.HelpDialog
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.bottomsheet.BankAccountListBottomSheetFragment
import com.bukuwarung.payments.constants.isPending
import com.bukuwarung.payments.constants.isRejected
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.colorText
import com.bukuwarung.utils.getClassTag
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.getDrawableCompat
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isFalse
import com.bukuwarung.utils.isFalseOrNull
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.openActivity
import com.bukuwarung.utils.orDash
import com.bukuwarung.utils.setDrawable
import com.bukuwarung.utils.setDrawableRightListener
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.singleClick
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EdcOrderDetailsActivity : AppCompatActivity(),
    BankAccountListBottomSheetFragment.BtSheetBankAccountListener {

    private val binding: ActivityEdcOrderDetailsBinding by viewBinding()
    private val viewModel: EdcOrderDetailViewModel by viewModels()
    private val orderId by lazy { intent.getStringExtra(ORDER_ID) }
    private var orderStatus: OrderStatus? = null
    private var edcOrderDetailResponse: EdcOrderDetailResponse? = null
    private var isBillingDetailsExpanded = true
    private var isOrderRejected = false
    private var merchantBankAccountsList: List<BankAccount>? = null
    private var hideBottomView = true
    private var showError = false
    private var isBukuWarungOrder = true
    private var isTikTokOrder = false
    private var isEZAOrder = false

    companion object {
        const val ORDER_ID = "ORDER_ID"
        private const val CANCELLED = "Cancelled"
        private const val UNASSIGNED = "Unassigned"
        private const val STATUS_PAID = "PAID"
        const val STATUS_REFUNDING = "REFUNDING"
        const val STATUS_REFUNDED = "REFUNDED"
        const val STATUS_REFUNDING_FAILED = "REFUNDING_FAILED"
        private const val RENTAL = "RENTAL"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setToolbar()
    }

    private fun setToolbar() {
        binding.btnBack.singleClick { onBackPressed() }
        binding.tvHelp.singleClick {
            val intent = Intent(this, HelpCenterActivity::class.java)
            intent.putExtra(
                AppConst.URL,
                RemoteConfigUtils.getPaymentConfigs().supportUrls.payments
            )
            intent.putExtra(AppConst.TITLE, getString(R.string.help))
            startActivity(intent)
        }
    }

    override fun onResume() {
        super.onResume()
        getOrderDetailsByOrderId(orderId)
        observer()
    }

    override fun onBackPressed() {
        finishAffinity()
        MainActivity.startActivityAndClearTop(this@EdcOrderDetailsActivity)
    }

    private fun getOrderDetailsByOrderId(orderId: String?) {
        orderId?.let {
            viewModel.getEdcOrderDetailsByOrderId(it)
        }
        viewModel.fetchMerchantBankAccounts()
    }

    private fun observer() {
        viewModel.refundResponse.observe(this) {
            if (it?.success.isFalseOrNull) {
                NotificationUtils.alertError("Adding Refund Bank Account Failed.")
            }
        }
        viewModel.merchantBankAccounts.observe(this) {
            merchantBankAccountsList = it
        }
        viewModel.cancelOrder.observe(this) {
            if (it.not()) {
                NotificationUtils.alertError("Cancelling Order Failed.")
            }
        }
        viewModel.regeneratePaymentLink.observe(this) {
            if (it?.result.isTrue) {
                startWebview(it?.data?.paymentUrl.orEmpty())
            } else {
                NotificationUtils.alertError("Regenerating Payment Link Failed.")
            }
        }
        viewModel.showLoader.observe(this) {
            binding.pbLoader.visibility = it.asVisibility()
            binding.clBottom.visibility = (it?.not().isTrue && hideBottomView.not()).asVisibility()
            binding.nsvDetails.visibility = (it?.not().isTrue && showError.isFalse).asVisibility()
        }
        viewModel.orderDetails.observe(this) { response ->
            if (response?.result.isTrue) {
                edcOrderDetailResponse = response
                showError = false
                isBukuWarungOrder = response?.data?.deviceMappingDetails?.type?.equals(EdcOrderType.BUKUWARUNG.name, true).isTrue
                isTikTokOrder = response?.data?.deviceMappingDetails?.type?.equals(EdcOrderType.TIKTOK.name, true).isTrue
                isEZAOrder = response?.data?.deviceMappingDetails?.type?.equals(EdcOrderType.EZA.name, true).isTrue
                if(isBukuWarungOrder.isFalse){
                    binding.tvDeviceTitle.text = response?.data?.deviceMappingDetails?.description.orEmpty()
                }
                //cache the orderId so that it can be used for opening this screen after completing the kyc.
                FeaturePrefManager.getInstance().edcOrderId = edcOrderDetailResponse?.data?.orderId.orEmpty()
                orderStatus = when {
                    response?.data?.status?.equals(CANCELLED, true).isTrue -> OrderStatus.CANCELLED
                    response?.data?.status?.equals(
                        UNASSIGNED,
                        true
                    ).isTrue && response?.data?.paymentDetails?.paymentLink?.isNotNullOrBlank().isTrue -> OrderStatus.PENDING
                    response?.data?.status?.equals(
                        UNASSIGNED,
                        true
                    ).isTrue && response?.data?.paymentDetails?.paymentLink.isNullOrBlank().isTrue -> OrderStatus.EXPIRED
                    response?.data?.paymentDetails?.status?.equals(
                        STATUS_PAID,
                        true
                    ).isTrue -> OrderStatus.COMPLETED
                    response?.data?.paymentDetails?.status?.equals(
                        STATUS_REFUNDING,
                        true
                    ).isTrue -> OrderStatus.REFUNDING_IN_PROGRESS
                    response?.data?.paymentDetails?.status?.equals(
                        STATUS_REFUNDED,
                        true
                    ).isTrue -> OrderStatus.REFUNDING_SUCCESSFUL
                    response?.data?.paymentDetails?.status?.equals(
                        STATUS_REFUNDING_FAILED,
                        true
                    ).isTrue -> OrderStatus.REFUNDING_FAILED
                    else -> OrderStatus.PENDING
                }
                if (edcOrderDetailResponse?.data?.status.equals(EdcOrderStatus.WAITING_FOR_OPS.name, true)){
                    binding.clActivateEdc.showView()
                    binding.btnActivateEdc.singleClick {
                        EdcActivationBottomSheet(edcOrderDetailResponse?.data?.orderId.orEmpty()).show(supportFragmentManager, getClassTag())
                    }
                }
                isOrderRejected = edcOrderDetailResponse?.data?.status.equals(EdcOrderStatus.REJECTED.name, true)
                loadTopDetails()
                loadRefundInfo()
                loadRefundView()
                loadEdcOrderSteps()
                loadTrackOrder()
                loadMyOrders()
                loadBillingDetails()
                loadBottomView()
            } else {
                showErrorView()
            }
        }
        viewModel.productDetails.observe(this) {
            val purchaseType =
                if (it?.data?.products?.firstOrNull()?.plans?.firstOrNull()?.planType?.equals(RENTAL).isTrue) "Sewa" else getString(
                    R.string.buy
                )
            val productName = it?.data?.products?.firstOrNull()?.description.orEmpty()
            binding.tvDeviceTitle.text = "$purchaseType $productName"
        }
    }

    private fun loadTrackOrder(){
        edcOrderDetailResponse?.data?.status?.let {
            if (it.equals(EdcOrderStatus.COMPLETED.name, true) || it.equals(EdcOrderStatus.WAITING_FOR_USER.name, true) || it.equals(EdcOrderStatus.WAITING_FOR_OPS.name, true)){
                binding.clTrackOrders.showView()
                val awb = edcOrderDetailResponse?.data?.deliveryDetails?.awb
                if (awb.isNotNullOrBlank()){
                    binding.apply {
                        tvReceiptNumberValue.setDrawable(right = R.drawable.ic_copy)
                        tvTrackOrderValue.showView()
                        tvTrackOrderValue.singleClick {
                            openActivity(WebviewActivity::class.java) {
                                putString(BaseWebviewActivity.LINK,"https://jne.co.id/tracking-package")
                            }
                        }
                        tvReceiptNumberValue.singleClick {
                            Utility.copyToClipboard(awb, this@EdcOrderDetailsActivity, "")
                        }
                        tvReceiptNumberValue.text = awb
                    }
                }
                else {
                    binding.apply {
                        tvTrackOrderValue.hideView()
                        tvReceiptNumberValue.text = getString(R.string.in_the_process)
                        tvReceiptNumberValue.setDrawable(right = 0)
                    }
                }
            } else if(it.equals(EdcOrderStatus.WAITING_FOR_ADDITIONAL_DOC.name, true)) {
                binding.apply {
                    binding.clTrackOrders.showView()
                    tvTrackOrderValue.hideView()
                    tvReceiptNumberValue.text = getString(R.string.complete_the_verification_process)
                    tvReceiptNumberValue.setDrawable(right = 0)
                }
            } else {
                binding.clTrackOrders.hideView()
            }
        }
    }

    private fun showErrorView() = with(binding) {
        hideBottomView = true
        showError = true
        clBottom.hideView()
        nsvDetails.hideView()
        bukuErrorView.showView()
        bukuErrorView.setErrorType(
            type = BaseErrorView.Companion.ErrorType.SERVER_UNREACHABLE,
            message = getString(R.string.loading_error_message)
        )
        bukuErrorView.addCallback(serverErrorViewCallBack)
    }

    private var serverErrorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            finishAffinity()
            MainActivity.startActivityAndClearTop(this@EdcOrderDetailsActivity)
        }

        override fun messageClicked() {}
    }

    private fun loadTopDetails() = with(binding) {
        tvDateValue.text = DateTimeUtils.getUTCTimeToLocalDateTime(
            edcOrderDetailResponse?.data?.paymentDetails?.paymentDate,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )
        tvOrderTypeValue.text = when{
            isBukuWarungOrder -> getString(R.string.bukuwarung_order_type)
            isTikTokOrder -> getString(R.string.tiktok_order_type)
            isEZAOrder -> getString(R.string.eza_order_type)
            else -> getString(R.string.bukuwarung_order_type)
        }
        tvInvoiceValue.text = edcOrderDetailResponse?.data?.invoiceNumber.orEmpty()
        ivInvoiceValue.singleClick {
            Utility.copyToClipboard(
                edcOrderDetailResponse?.data?.invoiceNumber.orEmpty(),
                this@EdcOrderDetailsActivity,
                getString(R.string.invoice_number_copied)
            )
        }
        when (orderStatus) {
            OrderStatus.PENDING -> {
                tvStatusValue.text = getString(R.string.in_process)
                tvStatusValue.setTextColor(getColorCompat(R.color.dark_yellow))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_yellow5_corner_4dp)
                tvStatusValue.setDrawable(0, 0, 0, 0)
            }
            OrderStatus.COMPLETED -> {
                tvStatusValue.text = getString(R.string.paid_label)
                tvStatusValue.setTextColor(getColorCompat(R.color.green_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_green5_corner_4dp)
                tvStatusValue.setDrawable(R.drawable.ic_tick_green_bg, 0, 0, 0)
            }
            OrderStatus.EXPIRED -> {
                tvStatusValue.text = getString(R.string.expired_status)
                tvStatusValue.setTextColor(getColorCompat(R.color.red_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_red5_corner_4dp)
                tvStatusValue.setDrawable(0, 0, 0, 0)
            }
            OrderStatus.CANCELLED -> {
                tvStatusValue.text = getString(R.string.cancelled_label)
                tvStatusValue.setTextColor(getColorCompat(R.color.red_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_red5_corner_4dp)
                tvStatusValue.setDrawable(0, 0, 0, 0)
            }
            OrderStatus.REFUNDING_IN_PROGRESS -> {
                tvStatusValue.text = getString(R.string.returns_processing)
                tvStatusValue.setTextColor(getColorCompat(R.color.dark_yellow))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_yellow5_corner_4dp)
                tvStatusValue.setDrawable(R.drawable.ic_clock_in_progress, 0, 0, 0)
            }
            OrderStatus.REFUNDING_FAILED -> {
                tvStatusValue.text = getString(R.string.refund_failed_1)
                tvStatusValue.setTextColor(getColorCompat(R.color.red_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_red5_corner_4dp)
                tvStatusValue.setDrawable(R.drawable.ic_cross_filled_circle, 0, 0, 0)
            }
            OrderStatus.REFUNDING_SUCCESSFUL -> {
                tvStatusValue.text = getString(R.string.refund_successful)
                tvStatusValue.setTextColor(getColorCompat(R.color.green_60))
                tvStatusValue.background = getDrawableCompat(R.drawable.bg_solid_green5_corner_4dp)
                tvStatusValue.setDrawable(R.drawable.ic_tick_green_bg, 0, 0, 0)
            }
            else -> {}
        }
    }

    private fun loadRefundInfo() = with(binding) {
        refundInfo.tvSetRefund.text = getString(R.string.money_will_be_returned)
        refundInfo.tvSetReceiver.hideView()
        refundInfo.btnChooseRefundMethod.hideView()
        refundInfo.includeBankLayout.root.showView()
        Glide.with(this@EdcOrderDetailsActivity)
            .load(edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.logoUrl)
            .placeholder(R.drawable.ic_bank)
            .error(R.drawable.ic_bank)
            .into(refundInfo.includeBankLayout.ivBank)
        val bankName = edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.bankName.orEmpty()
        val beneficiaryName =
            edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.beneficiaryName.orEmpty()
        refundInfo.includeBankLayout.tvBankName.text = "$bankName - $beneficiaryName"
        refundInfo.includeBankLayout.tvAccountNumber.text =
            Utilities.maskSensitiveInfo(edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.accountNumber.orEmpty())
        refundInfo.includeBankLayout.tvEdit.hideView()
        when {
            edcOrderDetailResponse?.data?.beneficiaryAccountDetails == null -> {
                refundInfo.root.hideView()
            }
            orderStatus == OrderStatus.REFUNDING_IN_PROGRESS -> {
                refundInfo.root.showView()
                refundInfo.clRefundLayout.background =
                    getDrawableCompat(R.drawable.bg_rounded_rectangle_blue_5)
            }
            orderStatus == OrderStatus.REFUNDING_SUCCESSFUL -> {
                refundInfo.root.showView()
                refundInfo.clRefundLayout.background =
                    getDrawableCompat(R.drawable.bg_rounded_rectangle_green_5)
                refundInfo.tvSetRefund.setTextColor(getColorCompat(R.color.green_60))
            }
            else -> {
                refundInfo.root.hideView()
            }
        }
    }

    private val startAddBankAccountActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.getParcelableExtra<BankAccount>(AddBankAccountActivity.BANK_ACCOUNT)
                    ?.let { addRefundBankAccount(it) }
            }
        }

    private fun addRefundBankAccount(bankAccount: BankAccount?) {
        viewModel.refundPayment(
            orderId.orEmpty(),
            RefundRequest(
                Beneficiary(
                    type = "BANK",
                    code = bankAccount?.bankCode.orEmpty(),
                    number = bankAccount?.accountNumber.orEmpty(),
                    name = bankAccount?.accountHolderName.orEmpty()
                )
            )
        )
    }

    private fun loadRefundView() = with(binding) {
        val checks = edcOrderDetailResponse?.data?.checks
        when {
            isOrderRejected && orderStatus == OrderStatus.COMPLETED -> {
                clRefund.showView()
                if (isBukuWarungOrder){
                    tvRefundTitle.text = getString(R.string.verification_period_completed)
                    tvRefundDesc.text = getString(R.string.select_refund_method_for_edc_purchase)
                    btnSelect.showView()
                    btnSelect.singleClick {
                        if (merchantBankAccountsList?.isEmpty().isTrue) {
                            addNewRefundBankAccount(false)
                        } else {
                            val bankListBtSheet =
                                BankAccountListBottomSheetFragment.createBankAccountInstance(
                                    AnalyticsConst.EDC_ORDER_DETAIL, false
                                )
                            bankListBtSheet.show(supportFragmentManager, "bankListBtSheet")
                        }
                    }
                    btnHelp.hideView()
                } else {
                    tvRefundTitle.text = getString(R.string.verification_period_completed)
                    if (isTikTokOrder) {
                        tvRefundDesc.text = getString(R.string.tiktok_order_cancelled_message)
                    } else if (isEZAOrder){
                        tvRefundDesc.text = getString(R.string.eza_order_cancelled_message)
                    }
                    btnSelect.hideView()
                    btnHelp.hideView()
                }
            }
            orderStatus == OrderStatus.COMPLETED && (checks?.kycStatus.isRejected() || checks?.kybStatus.isRejected()) -> {
                clRefund.showView()
                tvRefundTitle.text = getString(R.string.account_verification_failed)
                tvRefundDesc.text = getString(R.string.reverify_account)
                btnSelect.showView()
                btnHelp.hideView()
                btnSelect.singleClick { startWebview(RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}") }
            }
            orderStatus == OrderStatus.REFUNDING_FAILED && isBukuWarungOrder -> {
                clRefund.showView()
                tvRefundTitle.text = getString(R.string.refund_failed)
                tvRefundDesc.text = getString(R.string.refund_failed_message)
                btnSelect.hideView()
                btnHelp.showView()
                btnHelp.singleClick {
                    HelpDialog(this@EdcOrderDetailsActivity).show()
                }
            }
            else -> {
                clRefund.hideView()
            }
        }
    }

    private fun loadEdcOrderSteps() = with(binding) {
        stepOne.setStepTitle(getString(R.string.type_of_payment))
        stepOne.setStepNumber("1")
        stepTwo.setStepTitle(getString(R.string.identity_verification))
        stepTwo.setStepNumber("2")
        stepThree.setStepTitle(getString(R.string.priority_account_verification))
        stepThree.setStepNumber("3")
        stepFour.setStepTitle(getString(R.string.enter_bank_account))
        stepFour.setStepNumber("4")
        when (orderStatus) {
            OrderStatus.CANCELLED, OrderStatus.EXPIRED -> binding.clOrderSteps.hideView()
            OrderStatus.PENDING -> {
                clOrderSteps.showView()
                tvWarning.hideView()
                stepOne.setCurrentStep()
                stepOne.singleClick { startWebview(edcOrderDetailResponse?.data?.paymentDetails?.paymentLink.orEmpty()) }
                if (edcOrderDetailResponse?.data?.checks?.kycDone.isTrue) stepTwo.setDone() else stepTwo.setDisabledStep()
                if (edcOrderDetailResponse?.data?.checks?.kybDone.isTrue) stepThree.setDone() else stepThree.setDisabledStep()
                if (edcOrderDetailResponse?.data?.checks?.bankAccountAdded.isTrue) stepFour.setDone() else stepFour.setDisabledStep()
                stepTwo.singleClick { }//empty click listener
                stepTwo.singleClick { }//empty click listener
                stepFour.singleClick { }//empty click listener
            }
            else -> {
                binding.clOrderSteps.showView()
                val checks = edcOrderDetailResponse?.data?.checks
                when {
                    checks?.kycDone.isTrue && checks?.kybDone.isTrue && checks?.bankAccountAdded.isTrue -> {
                        tvWarning.hideView()
                        tvEdcStepTitle.text = getString(R.string.edc_is_ready_title)
                        tvEdcStepDesc.text = getString(R.string.edc_is_ready_desc)
                    }
                    orderStatus == OrderStatus.COMPLETED && isOrderRejected.isFalse -> {
                        tvWarning.visibility = isBukuWarungOrder.asVisibility()
                        val time = DateTimeUtils.getUTCTimeToLocalDateTime(
                            edcOrderDetailResponse?.data?.checks?.completionExpireDate,
                            DateTimeUtils.DD_MMM_YYYY_HH_MM
                        )
                        tvWarning.text = getString(R.string.complete_details_by, time)
                        tvWarning.setTextColor(getColorCompat(R.color.grey_91))
                    }
                    orderStatus == OrderStatus.COMPLETED && checks?.kycStatus.isPending() && checks?.kybStatus.isPending() && checks?.bankAccountAdded.isTrue -> {
                        tvWarning.hideView()
                    }
                    orderStatus == OrderStatus.COMPLETED && (checks?.kycStatus.isRejected() || checks?.kybStatus.isRejected()) -> {
                        val time = DateTimeUtils.getUTCTimeToLocalDateTime(
                            edcOrderDetailResponse?.data?.checks?.completionExpireDate,
                            DateTimeUtils.DD_MMM_YYYY_HH_MM
                        )
                        tvWarning.showView()
                        tvWarning.setTextColor(getColorCompat(R.color.red))
                        val completeText = getString(R.string.edc_verification_failed) + "\n\n" + getString(R.string.complete_details_by,time)
                        tvWarning.text = SpannableStringBuilder(completeText).colorText(
                            getString(R.string.complete_details_by,time),
                            getColorCompat(R.color.grey_91),
                            true
                        )
                    }
                    else -> {
                        tvWarning.showView()
                        tvWarning.text = when{
                            isBukuWarungOrder -> getString(R.string.edc_purchase_expired)
                            isTikTokOrder -> getString(R.string.tiktok_refund_error_message)
                            isEZAOrder -> getString(R.string.eza_refund_error_message)
                            else -> getString(R.string.edc_purchase_expired)
                        }
                        tvWarning.setTextColor(getColorCompat(R.color.red))
                    }
                }
                stepOne.setDone()
                stepOne.singleClick { }//empty click listener
                if (edcOrderDetailResponse?.data?.checks?.kycDone.isTrue) {
                    stepTwo.setDone()
                    stepTwo.singleClick { }//empty click listener
                } else {
                    when{
                        isOrderRejected -> {
                            stepTwo.setDisabledStep()
                            stepTwo.singleClick {  }//empty click listener
                        }
                        checks?.kycStatus.isPending() -> {
                            stepTwo.singleClick {  }
                            stepTwo.setPendingStep()
                        }
                        checks?.kycStatus.isRejected() -> {
                            stepTwo.singleClick { startWebview(RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}") }
                            stepTwo.setRejectedStep()
                        }
                        orderStatus == OrderStatus.COMPLETED && isOrderRejected.not() -> {
                            stepTwo.setCurrentStep()
                            stepTwo.singleClick { startWebview(RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}") }
                        }
                        else -> {
                            stepTwo.setDisabledStep()
                            stepTwo.singleClick {  }//empty click listener
                        }
                    }
                }
                if (edcOrderDetailResponse?.data?.checks?.kybDone.isTrue) {
                    stepThree.setDone()
                    stepThree.singleClick { }//empty click listener
                } else {
                    when{
                        isOrderRejected -> {
                            stepThree.setDisabledStep()
                            stepThree.singleClick {  }//empty click listener
                        }
                        checks?.kybStatus.isPending() -> {
                            stepThree.singleClick {  }
                            stepThree.setPendingStep()
                        }
                        checks?.kybStatus.isRejected() -> {
                            stepThree.singleClick { startWebview(RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}") }
                            stepThree.setRejectedStep()
                        }
                        orderStatus == OrderStatus.COMPLETED && isOrderRejected.not() -> {
                            stepThree.setCurrentStep()
                            stepThree.singleClick { startWebview(RemoteConfigUtils.getPaymentConfigs().accountVerificationUrl + "?source=EDC_ORDER_DETAIL" + "&orderId=${edcOrderDetailResponse?.data?.orderId.orEmpty()}") }
                        }
                        else -> {
                            stepThree.setDisabledStep()
                            stepThree.singleClick {  }//empty click listener
                        }
                    }
                }
                if (edcOrderDetailResponse?.data?.checks?.bankAccountAdded.isTrue) {
                    stepFour.setDone()
                    stepFour.singleClick { }//empty click listener
                } else {
                    if (orderStatus == OrderStatus.COMPLETED && isOrderRejected.not() && edcOrderDetailResponse?.data?.checks?.kycDone.isTrue){
                        stepFour.setCurrentStep()
                        stepFour.singleClick { startWebview(RemoteConfigUtils.getPaymentConfigs().merchantBanksUrl) }
                    } else {
                        stepFour.setDisabledStep()
                        stepFour.singleClick {  }//empty click listener
                    }
                }
            }
        }
    }

    private fun loadMyOrders() = with(binding) {
        tvOrderValue.singleClick { startWebview(RemoteConfigUtils.getPaymentConfigs().edcRegistrationUrl) }
        tvDeviceValue.visibility = isBukuWarungOrder.asVisibility()
        tvDeviceValue.text =
            Utility.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.totalPayableAmount)
    }

    private fun loadBillingDetails() = with(binding) {
        if (isBukuWarungOrder){
            clBillingDetails.showView()
            tvBillTitle.setDrawableRightListener {
                tvBillTitle.setDrawable(right = if (isBillingDetailsExpanded) R.drawable.ic_chevron_down else R.drawable.ic_chevron_up)
                isBillingDetailsExpanded = !isBillingDetailsExpanded
                grpBillDetails.visibility = isBillingDetailsExpanded.asVisibility()
            }
            tvTotalPurValue.text =
                Utility.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.productPrice)
            val edcDiscounts = edcOrderDetailResponse?.data?.orderAmountDetails?.discount
            llDiscounts.removeAllViews()
            edcDiscounts?.map {
                val llDiscountsView = LayoutInflater.from(this@EdcOrderDetailsActivity)
                    .inflate(R.layout.layout_card_item, llDiscounts, false)
                val tvTitle = llDiscountsView.findViewById<TextView>(R.id.tvTitle)
                val tvValue = llDiscountsView.findViewById<TextView>(R.id.tvValue)
                tvTitle.text = it.key
                tvValue.text = "-${Utility.formatAmount(it.value)}"
                llDiscounts.addView(llDiscountsView)
            }
            tvTotalPaymentValue.text =
                Utility.formatAmount(edcOrderDetailResponse?.data?.orderAmountDetails?.totalPayableAmount)
            tvPaymentModeValue.text =
                edcOrderDetailResponse?.data?.paymentDetails?.paymentMethod.orDash
        } else {
            clBillingDetails.hideView()
        }

    }

    private fun loadBottomView() = with(binding) {
        when (orderStatus) {
            OrderStatus.PENDING -> {
                hideBottomView = false
                clBottom.showView()
                btnCancelOrder.hideView()
                btnPayBill.showView()
                btnPayBill.text = getString(R.string.pay_bill)
                btnPayBill.singleClick { startWebview(edcOrderDetailResponse?.data?.paymentDetails?.paymentLink.orEmpty()) }
            }
            OrderStatus.EXPIRED -> {
                hideBottomView = false
                clBottom.showView()
                btnCancelOrder.showView()
                btnCancelOrder.singleClick {
                    showCancelOrderBS()
                }
                btnPayBill.text = getString(R.string.repay_bill)
                btnPayBill.singleClick {
                    viewModel.regeneratePaymentLink(edcOrderDetailResponse?.data?.orderId.orEmpty())
                }
            }
            else -> {
                hideBottomView = true
                clBottom.hideView()
            }
        }
    }

    private fun showCancelOrderBS() {
        EdcCancelOrderBS {
            viewModel.cancelOrder(edcOrderDetailResponse?.data?.orderId.orEmpty())
        }.show(supportFragmentManager, EdcCancelOrderBS.TAG)
    }

    private fun startWebview(url: String) {
        startActivity(
            WebviewActivity.createIntent(
                this@EdcOrderDetailsActivity,
                url,
                ""
            )
        )
    }

    private fun addNewRefundBankAccount(hasBankAccount: Boolean) {
        val bookId = SessionManager.getInstance().businessId
        startAddBankAccountActivityForResult.launch(
            AddBankAccountActivity.createIntent(
                this@EdcOrderDetailsActivity,
                PaymentConst.TYPE_PAYMENT_IN.toString(),
                bookId,
                "",
                hasBankAccount = hasBankAccount.toString(),
                paymentIn = true
            )
        )
    }

    private enum class OrderStatus {
        PENDING, COMPLETED, EXPIRED, CANCELLED, REFUNDING_IN_PROGRESS, REFUNDING_FAILED, REFUNDING_SUCCESSFUL
    }

    override fun onBankAccountSelected(bankAccount: BankAccount?) {
        addRefundBankAccount(bankAccount)
    }

    override fun addNewBankAccount() {
        addNewRefundBankAccount(true)
    }

}