package com.bukuwarung.activities.edc.cardhistory.ui

import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import androidx.fragment.app.FragmentManager
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.NEWEST_TO_OLDEST
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.OLDEST_TO_NEWEST
import com.bukuwarung.databinding.BottomsheetCardTransactionSortingBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.utils.getClassTag
import com.bukuwarung.utils.singleClick
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CardTransactionSortingBottomSheet(
    private val previousOrDefaultSelected: Pair<Int, String>,
    private val sortSelectedCallback: (Pair<Int, String>) -> Unit
) :
    BaseBottomSheetDialogFragment() {

    companion object {
        fun createInstance(
            fr: FragmentManager,
            previousOrDefaultSelected: Pair<Int, String>,
            sortSelectedCallback: (Pair<Int, String>) -> Unit
        ) =
            CardTransactionSortingBottomSheet(previousOrDefaultSelected, sortSelectedCallback).show(
                fr,
                getClassTag()
            )
    }

    private lateinit var binding: BottomsheetCardTransactionSortingBinding
    private var sortType: Pair<Int, String> = previousOrDefaultSelected

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        binding = BottomsheetCardTransactionSortingBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
        }

        binding.ivClose.setOnClickListener { dismiss() }
        binding.rgSort.check(binding.rgSort.getChildAt(previousOrDefaultSelected.first).id)
        binding.rgSort.setOnCheckedChangeListener { group, checkedId ->
            val selectedButton = group.findViewById<RadioButton>(checkedId)
            val position = group.indexOfChild(selectedButton)
            sortType = when (position) {
                0 -> {
                    Pair(position, NEWEST_TO_OLDEST)
                }

                else -> {
                    Pair(position, OLDEST_TO_NEWEST)
                }
            }

        }
        binding.btnConfirm.singleClick {
            sortSelectedCallback.invoke(sortType)
            dismiss()
        }
    }

}