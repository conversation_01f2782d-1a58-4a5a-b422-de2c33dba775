package com.bukuwarung.activities.edc.cardhistory.ui

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.paging.CombinedLoadStates
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.edc.cardhistory.adapter.TransactionHistoryAdapter
import com.bukuwarung.activities.edc.cardhistory.adapter.TransactionItemLoadStateAdapter
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.BALANCE_INQUIRY
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.NEWEST_TO_OLDEST
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.SORT_PROPERTY_VALUE_NEW_TO_OLD
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.SORT_PROPERTY_VALUE_OLD_TO_NEW
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.TRANSFER_POSTING
import com.bukuwarung.activities.edc.cardhistory.enums.HistoryType
import com.bukuwarung.activities.edc.cardhistory.model.HistoryItem
import com.bukuwarung.activities.edc.cardhistory.utils.PaymentAuxilliary
import com.bukuwarung.activities.edc.cardhistory.viewmodel.CardTransactionHistoryViewModel
import com.bukuwarung.activities.edc.orderdetail.ui.EdcOrderDetailsActivity
import com.bukuwarung.activities.edc.orderhistory.enums.EdcDevicePlanType
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.ActivityCardHistoryBinding
import com.bukuwarung.payments.data.model.DateFilter
import com.bukuwarung.payments.history.DateFilterBottomSheet
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.openActivity
import com.bukuwarung.utils.singleClick
import com.google.android.material.tabs.TabLayout
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.Calendar
import javax.inject.Inject

@AndroidEntryPoint
class CardTransactionHistoryActivity : AppCompatActivity() {

    companion object {
        const val HISTORY_TYPE = "history_type"
    }

    @Inject
    lateinit var viewModel: CardTransactionHistoryViewModel

    private lateinit var binding: ActivityCardHistoryBinding
    private lateinit var adapter: TransactionHistoryAdapter
    private val edcOrderHistory by lazy {
        intent?.getStringExtra(HISTORY_TYPE) ?: HistoryType.transaction.name
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCardHistoryBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setUpToolBar()
        trackHistoryEvent()
        binding.rvHistory.layoutManager = LinearLayoutManager(this)
        adapter = TransactionHistoryAdapter(this, edcOrderHistory) {
            redirectToTrxHistoryDetailPage(it)
        }
        binding.rvHistory.adapter = adapter.withLoadStateHeaderAndFooter(
            header = TransactionItemLoadStateAdapter(),
            footer = TransactionItemLoadStateAdapter()
        )
        adapter.addLoadStateListener { loadState ->
            binding.rvHistory.isVisible = loadState.source.refresh is LoadState.NotLoading
            binding.pbLoading.isVisible = loadState.source.refresh is LoadState.Loading
            binding.layoutEmptyView.root.isVisible =
                loadState.source.refresh is LoadState.Error || adapter.snapshot().items.isEmpty()
            binding.layoutEmptyView.btnEmptyCta.text = getString(R.string.retry)
            handleError(loadState)
        }
        binding.layoutEmptyView.btnEmptyCta.singleClick {
            adapter.retry()
            if (adapter.snapshot().items.isEmpty()) {
                adapter.refresh()
            }
        }
        loadInitialData()

        with(binding) {
            if (edcOrderHistory == HistoryType.transaction.name) {

                tlHistory.addTab(
                    tlHistory.newTab().setText(getString(R.string.filter_history_all))
                )

                tlHistory.addTab(
                    tlHistory.newTab().setText(getString(R.string.filter_history_check_balance))
                )
                tlHistory.addTab(
                    tlHistory.newTab().setText(getString(R.string.filter_history_transfer))
                )
            } else {
                tlHistory.hideView()
            }

            subscribeState()
            tvFilterDate.singleClick {
                DateFilterBottomSheet.createInstance(
                    supportFragmentManager,
                    true,
                    viewModel.defaultOrSelectedDateFilter
                ) { dateFilter ->
                    dateFilter?.let {
                        viewModel.defaultOrSelectedDateFilter = it
                        tvFilterDate.text = calculateLabel(it)
                        val startDate = Calendar.getInstance()
                        val (startTimeInMillis, endDateInMillis) = PaymentAuxilliary.getDates(it)
                        val endDateInYYYYMMDDFormat = DateTimeUtils.getFormattedDateTime(
                            endDateInMillis ?: startDate.timeInMillis,
                            DateTimeUtils.YYYY_MM_DD
                        )
                        val startDateInYYYYMMDDFormat = DateTimeUtils.getFormattedDateTime(
                            startTimeInMillis ?: startDate.timeInMillis,
                            DateTimeUtils.YYYY_MM_DD
                        )
                        viewModel.pageNumber = 0
                        viewModel.startDate = startDateInYYYYMMDDFormat
                        viewModel.endDate = endDateInYYYYMMDDFormat

                        viewModel.getTransactionHistoryData(
                            pageNumber = viewModel.pageNumber,
                            type = if (edcOrderHistory == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
                            startDate = viewModel.startDate,
                            endDate = viewModel.endDate,
                            historyType = edcOrderHistory
                        )
                        tvFilterDate.isSelected = true
                        updateFilter()
                    }
                }
            }
            tvSort.singleClick {
                CardTransactionSortingBottomSheet.createInstance(
                    supportFragmentManager,
                    viewModel.sort
                ) {
                    Log.d("--->filter", "sortType = $it")
                    viewModel.sort = it
                    tvSort.text =
                        if (it.second == NEWEST_TO_OLDEST) getString(R.string.newest_to_oldest) else getString(
                            R.string.oldest_to_newest
                        )
                    viewModel.pageNumber = 0
                    viewModel.getTransactionHistoryData(
                        pageNumber = viewModel.pageNumber,
                        type = if (edcOrderHistory == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
                        order = viewModel.sort.second,
                        historyType = edcOrderHistory
                    )
                    tvSort.isSelected = true
                    updateFilter()
                }
            }

            tlHistory.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    viewModel.pageNumber = 0
                    when (tab?.position) {
                        0 -> {
                            viewModel.type = ""
                        }

                        1 -> {
                            viewModel.type = BALANCE_INQUIRY
                        }

                        2 -> {
                            viewModel.type = TRANSFER_POSTING
                        }
                    }
                    viewModel.getTransactionHistoryData(
                        pageNumber = viewModel.pageNumber,
                        type = if (edcOrderHistory == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
                        startDate = viewModel.startDate,
                        endDate = viewModel.endDate,
                        historyType = edcOrderHistory
                    )
                    trackHistoryEvent()
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    // no implementation
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                    // no implementation
                }

            })

            ivClearFilter.singleClick {
                ivClearFilter.hideView()
                binding.tvSort.isSelected = false
                binding.tvSort.text = getString(R.string.sort)
                binding.tvFilterDate.isSelected = false
                binding.tvFilterDate.text = getString(R.string.select_date)
                viewModel.type = ""
                viewModel.pageNumber = 0
                viewModel.startDate = null
                viewModel.endDate = null
                viewModel.sort = Pair(0, NEWEST_TO_OLDEST)
                viewModel.defaultOrSelectedDateFilter = DateFilter(
                    label = "Hari ini",
                    presetValue = PaymentConst.DATE_PRESET.TODAY,
                    endDate = null,
                    startDate = null,
                    isChecked = true,
                    endDays = null,
                    startDays = null
                )
                loadInitialData()
            }
        }


    }

    private fun trackHistoryEvent() {
        val tabSelected = when (viewModel.type) {
            BALANCE_INQUIRY -> CardHistoryAnalyticsConstants.TRANSACTION_TYPE_VALUE_TAB_BALANCE_CHECK
            TRANSFER_POSTING -> CardHistoryAnalyticsConstants.TRANSACTION_TYPE_VALUE_TAB_TRANSFER
            else -> CardHistoryAnalyticsConstants.TRANSACTION_TYPE_VALUE_TAB_ALL
        }
        val sortApplied =
            if (viewModel.sort.second == NEWEST_TO_OLDEST) SORT_PROPERTY_VALUE_NEW_TO_OLD else SORT_PROPERTY_VALUE_OLD_TO_NEW
        val filterApplied = viewModel.defaultOrSelectedDateFilter.presetValue?.name.orEmpty()
        val propBuilder = AppAnalytics.PropBuilder().apply {
            put(CardHistoryAnalyticsConstants.EVENT_PROPERTY_TRANSACTION_TAB_SELECTED, tabSelected)
            put(CardHistoryAnalyticsConstants.EVENT_PROPERTY_SORT, sortApplied)
            put(CardHistoryAnalyticsConstants.EVENT_PROPERTY_TIME_FILTER, filterApplied)
        }
        AppAnalytics.trackEvent(CardHistoryAnalyticsConstants.EVENT_VISIT_HISTORY, propBuilder)
    }

    private fun loadInitialData() {
        viewModel.getTransactionHistoryData(
            pageNumber = viewModel.pageNumber,
            type = if (edcOrderHistory == HistoryType.order.name) EdcDevicePlanType.OWNED.name else viewModel.type,
            startDate = viewModel.startDate,
            endDate = viewModel.endDate,
            historyType = edcOrderHistory
        )
    }

    private fun updateFilter() {
        with(binding) {
            ivClearFilter.isVisible = tvFilterDate.isSelected || tvSort.isSelected
        }
        trackHistoryEvent()
    }

    private fun handleError(loadState: CombinedLoadStates) {
        val errorState = loadState.source.append as? LoadState.Error
            ?: loadState.source.prepend as? LoadState.Error
        errorState?.let {
            Toast.makeText(this, "error occurred", Toast.LENGTH_SHORT).show()
        }
    }

    private fun subscribeState() {
        viewModel.transactionData.observe(this@CardTransactionHistoryActivity) {
            lifecycleScope.launch {
                if (it != null) {
                    adapter.submitData(it)
                }
            }
        }
    }

    private fun setUpToolBar() {
        with(binding.toolbar) {
            txtToolbarTitle.text =
                if (edcOrderHistory == HistoryType.order.name) getString(R.string.edc_order_history_title) else getString(
                    R.string.card_history
                )
            backBtn.singleClick { onBackPressed() }
        }
    }

    private fun calculateLabel(dateFilter: DateFilter): String {
        return if (dateFilter.presetValue?.name?.uppercase() == "CUSTOM_RANGE") {
            val startDate =
                DateTimeUtils.getFormattedDateTime(
                    dateFilter.startDate ?: 0,
                    DateTimeUtils.DD_MMM_YY
                )
            val endDate =
                DateTimeUtils.getFormattedDateTime(dateFilter.endDate ?: 0, DateTimeUtils.DD_MMM_YY)
            return startDate.plus("-").plus(endDate)
        } else dateFilter.label
    }

    private fun calculateStartDateEndDate(
        startDate: Calendar,
        dateFilter: DateFilter
    ): Pair<Long?, Long?> {
        val endDate = startDate.clone() as Calendar
        when (dateFilter.presetValue?.name?.uppercase()) {
            "TODAY" -> {
                endDate.add(Calendar.DAY_OF_MONTH, 1)
            }

            "LAST_SEVEN_DAYS" -> {
                endDate.add(Calendar.DAY_OF_MONTH, 7)
            }

            "THIS_MONTH" -> {
                endDate.add(Calendar.MONTH, 1)
            }

            "CUSTOM_RANGE" -> return Pair(dateFilter.startDate, dateFilter.endDate)

            else -> throw IllegalArgumentException("not supported")
        }
        return Pair(startDate.timeInMillis, endDate.timeInMillis)
    }

    private fun redirectToTrxHistoryDetailPage(data: HistoryItem) {
        // No implementation
        if (HistoryType.order.name == edcOrderHistory) {
            openActivity(EdcOrderDetailsActivity::class.java) {
                putString(EdcOrderDetailsActivity.ORDER_ID, data.id)
            }

        }
    }
}