package com.bukuwarung.activities.edc.orderdetail.usecase

import com.bukuwarung.activities.edc.orderdetail.model.RefundRequest
import com.bukuwarung.activities.edc.orderdetail.repo.EdcOrderDetailRepo
import javax.inject.Inject

class EdcOrderDetailUseCase @Inject constructor(private val edcOrderDetailRepo: EdcOrderDetailRepo) {

    suspend fun getEdcOrderDetailsByOrderId(orderId: String) =
        edcOrderDetailRepo.getEdcOrderDetailsByOrderId(orderId)

    suspend fun getEdcOrderDetailByPhoneNumber() =
        edcOrderDetailRepo.getEdcOrderDetailByPhoneNumber()

    suspend fun getEdcProductDetails(orderId: String) =
        edcOrderDetailRepo.getEdcProductDetails(orderId)
    suspend fun cancelEdcOrder(orderId: String) =
        edcOrderDetailRepo.cancelEdcOrder(orderId)

    suspend fun regeneratePaymentLink(orderId: String) =
        edcOrderDetailRepo.regeneratePaymentLink(orderId)

    suspend fun refundPayment(orderId: String, refundRequest: RefundRequest) =
        edcOrderDetailRepo.refundPayment(orderId, refundRequest)
}