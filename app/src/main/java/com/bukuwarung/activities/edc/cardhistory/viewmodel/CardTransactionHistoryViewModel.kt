package com.bukuwarung.activities.edc.cardhistory.viewmodel

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.map
import com.bukuwarung.activities.edc.cardhistory.constants.CardHistoryAnalyticsConstants.NEWEST_TO_OLDEST
import com.bukuwarung.activities.edc.cardhistory.enums.HistoryType
import com.bukuwarung.activities.edc.cardhistory.model.HistoryItem
import com.bukuwarung.activities.edc.cardhistory.pagingdatasource.TransactionHistoryPagingSource
import com.bukuwarung.activities.edc.cardhistory.usecase.CardTransactionHistoryUseCase
import com.bukuwarung.activities.edc.orderhistory.usecase.EdcOrderHistoryUseCase
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.payments.data.model.DateFilter
import com.bukuwarung.session.User
import com.bukuwarung.utils.DateTimeUtils
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class HistoryListItem {
    data class HistoryItem(val historyItem: com.bukuwarung.activities.edc.cardhistory.model.HistoryItem) :
        HistoryListItem()

    data class HistoryItemSeparator(val data: String) : HistoryListItem()
}

class CardTransactionHistoryViewModel @Inject constructor(
    private val cardTransactionHistoryUseCase: CardTransactionHistoryUseCase,
    private val edcOrderHistoryUseCase: EdcOrderHistoryUseCase
) : ViewModel() {

    private var pager: Pager<Int, HistoryItem> = createPager()

    var startDate: String? = null
    var endDate: String? = null
    var type = ""
    var pageNumber = 0
    var sort: Pair<Int, String> = Pair(0, NEWEST_TO_OLDEST)
    var defaultOrSelectedDateFilter: DateFilter = DateFilter(
        label = "Hari ini",
        presetValue = PaymentConst.DATE_PRESET.TODAY,
        endDate = null,
        startDate = null,
        isChecked = true,
        endDays = null,
        startDays = null
    )

    private var _transactionData = MutableLiveData<PagingData<HistoryListItem>>()
    val transactionData: LiveData<PagingData<HistoryListItem>> get() = _transactionData


    private fun createPager(
        pageNumber: Int = 0,
        pageSize: Int = 10,
        order: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: String? = null,
        accountId: String = User.getBusinessId(),
        historyType: String = HistoryType.transaction.name,
    ): Pager<Int, HistoryItem> {

        return Pager(
            config = PagingConfig(pageSize = 10, prefetchDistance = 2),
            pagingSourceFactory = {
                TransactionHistoryPagingSource(
                    cardTransactionHistoryUseCase,
                    edcOrderHistoryUseCase,
                    accountId = accountId,
                    pageNumber = pageNumber,
                    pageSize = pageSize,
                    order = order,
                    startDate = startDate,
                    endDate = endDate,
                    type = type,
                    isOrderHistory = historyType == HistoryType.order.name
                )
            })
    }

    fun getTransactionHistoryData(
        pageNumber: Int,
        pageSize: Int = 10,
        order: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: String? = null,
        historyType: String
    ) {
        viewModelScope.launch {
                Log.d("--->","book id = "+ User.getBusinessId())
            val newPager =
                createPager(
                    pageNumber,
                    pageSize,
                    order,
                    startDate,
                    endDate,
                    type,
                    historyType = historyType
                )
            pager = newPager
            newPager.flow.map { pagingData ->
                pagingData.map {
                    HistoryListItem.HistoryItem(it)
                }
            }.map {
                it.insertSeparators { before, after ->

                    if (after == null) {
                        return@insertSeparators null
                    }
                    val nameOfAfterItem = DateTimeUtils.getUTCTimeToLocalDateTime(
                        if (historyType == HistoryType.order.name) after.historyItem.paymentDate else after.historyItem.date,
                        DateTimeUtils.DD_MMM_YYYY
                    )

                    if (before == null) {
                        return@insertSeparators HistoryListItem.HistoryItemSeparator(
                            nameOfAfterItem.orEmpty()
                        )
                    }
                    val nameBeforeItem = DateTimeUtils.getUTCTimeToLocalDateTime(
                        if (historyType == HistoryType.order.name) after.historyItem.paymentDate else before.historyItem.date,
                        DateTimeUtils.DD_MMM_YYYY
                    )
                    if (nameBeforeItem != nameOfAfterItem) {
                        return@insertSeparators HistoryListItem.HistoryItemSeparator(
                            nameOfAfterItem.orEmpty()
                        )
                    } else {
                        null
                    }
                }
            }.cachedIn(viewModelScope).collectLatest {
                _transactionData.value = it
            }
        }

    }


}

