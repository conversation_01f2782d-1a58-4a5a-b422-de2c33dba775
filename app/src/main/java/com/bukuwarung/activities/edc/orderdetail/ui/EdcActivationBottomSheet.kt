package com.bukuwarung.activities.edc.orderdetail.ui

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.bukuwarung.BuildConfig
import com.bukuwarung.databinding.EdcActivationBottomSheetBinding
import com.bukuwarung.dialogs.base.BaseBottomSheetDialogFragment
import com.bukuwarung.session.User
import com.bukuwarung.utils.singleClick

class EdcActivationBottomSheet(private val orderId: String) : BaseBottomSheetDialogFragment() {

    private var _binding: EdcActivationBottomSheetBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = EdcActivationBottomSheetBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        with(binding) {
            ivClose.singleClick { dismiss() }
            btnLater.singleClick { dismiss() }
            btnActivate.singleClick {
                if (isAppInstalled()) redirectToBukuAgen()
                else redirectToPlayStore()
            }
        }
    }

    private fun redirectToBukuAgen() {
        val bukuagenPackageName = BuildConfig.BUKUAGEN_PACKAGE_NAME
        val uri =
            Uri.parse("bukuagen://launch/edcOrderDetail?phone_number=${User.getUserId()}&order_id=${orderId}")
        val intent = Intent(Intent.ACTION_VIEW, uri).apply {
            `package` = bukuagenPackageName
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        try {
            requireContext().startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "Error opening BukuAgen App", Toast.LENGTH_SHORT)
                .show()
        }
    }

    private fun redirectToPlayStore() {
        var bukuagenPackageName = "com.bukuwarung.bukuagen"
        val playStoreUrl = "https://play.google.com/store/apps/details?id=$bukuagenPackageName"
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(playStoreUrl)).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        requireContext().startActivity(intent)
    }

    private fun isAppInstalled(): Boolean {
        val bukuagenPackageName = BuildConfig.BUKUAGEN_PACKAGE_NAME
        return try {
            requireContext().packageManager.getPackageInfo(bukuagenPackageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
}