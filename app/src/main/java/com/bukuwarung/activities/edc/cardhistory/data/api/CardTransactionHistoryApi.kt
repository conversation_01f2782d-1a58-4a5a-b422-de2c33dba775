package com.bukuwarung.activities.edc.cardhistory.data.api

import com.bukuwarung.activities.edc.cardhistory.model.TransactionHistoryResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface CardTransactionHistoryApi {

    @GET("edc-adapter/transaction/history/{account_id}")
    suspend fun getTransactionDetails(
        @Path("account_id") accountId: String,
        @Query("type") type: String?,
        @Query("page_number") pageNumber: Int,
        @Query("page_size") pageSize: Int,
        @Query("order") order: String?,
        @Query("start_date") startDate: String?,
        @Query("end_date") endDate: String?
    ): Response<TransactionHistoryResponse>
}