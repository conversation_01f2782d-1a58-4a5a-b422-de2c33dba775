package com.bukuwarung.activities.edc.orderdetail.di

import com.bukuwarung.activities.edc.orderdetail.api.EdcOrderDetailApi
import com.bukuwarung.activities.edc.orderdetail.repo.EdcOrderDetailRepo
import com.bukuwarung.activities.edc.orderdetail.usecase.EdcOrderDetailUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named


@Module
@InstallIn(SingletonComponent::class)
class EdcOrderDetailModule {

    @Provides
    fun provideEdcOrderDetailDataSource(@Named("edc") retrofit: Retrofit): EdcOrderDetailApi {
        return retrofit.create(EdcOrderDetailApi::class.java)
    }

    @Provides
    fun provideEdcOrderDetailRepository(api: EdcOrderDetailApi) = EdcOrderDetailRepo(api)

    @Provides
    fun provideEdcOrderDetailUseCase(repository: EdcOrderDetailRepo) =
        EdcOrderDetailUseCase(repository)

}