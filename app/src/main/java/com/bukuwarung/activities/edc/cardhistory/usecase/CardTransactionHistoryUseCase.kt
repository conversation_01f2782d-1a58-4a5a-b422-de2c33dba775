package com.bukuwarung.activities.edc.cardhistory.usecase

import com.bukuwarung.activities.edc.cardhistory.data.repository.CardHistoryRepository
import com.bukuwarung.activities.edc.cardhistory.model.HistoryItem
import javax.inject.Inject

class CardTransactionHistoryUseCase @Inject constructor(private val transactionHistoryRepository: CardHistoryRepository) {

    suspend fun getTransactionHistory(
        accountId: String,
        pageNumber: Int,
        pageSize: Int,
        order: String?,
        startDate: String?,
        endDate: String?,
        type: String?
    ): ArrayList<HistoryItem> {
        return try {
            val response = transactionHistoryRepository.getCardHistory(
                accountId,
                pageNumber,
                pageSize,
                order,
                startDate,
                endDate,
                type
            )
            if (response.isSuccessful) response.body()?.history!! else throw Exception()
        } catch (e: Exception) {
            throw Exception()
        }
    }
}