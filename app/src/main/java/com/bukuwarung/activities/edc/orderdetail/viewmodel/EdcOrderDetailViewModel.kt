package com.bukuwarung.activities.edc.orderdetail.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.edc.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.activities.edc.orderdetail.model.EdcProductDetails
import com.bukuwarung.activities.edc.orderdetail.model.RefundRequest
import com.bukuwarung.activities.edc.orderdetail.model.RefundResponse
import com.bukuwarung.activities.edc.orderdetail.model.RegeneratePaymentLinkResponse
import com.bukuwarung.activities.edc.orderdetail.usecase.EdcOrderDetailUseCase
import com.bukuwarung.activities.edc.orderhistory.enums.EdcOrderType
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.domain.payments.PaymentUseCase
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.isNotNullOrBlank
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class EdcOrderDetailViewModel @Inject constructor(
    private val edcOrderDetailUseCase: EdcOrderDetailUseCase,
    private val paymentUseCase: PaymentUseCase
) : ViewModel() {

    private var _showLoader = MutableLiveData<Boolean?>()
    val showLoader: LiveData<Boolean?> = _showLoader

    private var _orderDetails = MutableLiveData<EdcOrderDetailResponse?>()
    val orderDetails: LiveData<EdcOrderDetailResponse?> = _orderDetails

    private var _productDetails = MutableLiveData<EdcProductDetails?>()
    val productDetails: LiveData<EdcProductDetails?> = _productDetails

    private var _cancelOrder = MutableLiveData<Boolean>()
    val cancelOrder: LiveData<Boolean> = _cancelOrder

    private var _regeneratePaymentLink = MutableLiveData<RegeneratePaymentLinkResponse?>()
    val regeneratePaymentLink: LiveData<RegeneratePaymentLinkResponse?> = _regeneratePaymentLink

    private var _refundResponse = MutableLiveData<RefundResponse?>()
    val refundResponse: LiveData<RefundResponse?> = _refundResponse

    private var _merchantBankAccounts = MutableLiveData<List<BankAccount>?>()
    val merchantBankAccounts: LiveData<List<BankAccount>?> = _merchantBankAccounts

    private var edcOrderDetailResponse: EdcOrderDetailResponse? = null

    fun fetchMerchantBankAccounts() = viewModelScope.launch{
        val bookId = SessionManager.getInstance().businessId
        if (!PaymentPrefManager.getInstance().getShowSelectedBankFromLocal()) {
            when (val result = paymentUseCase.getMerchantBankAccounts(bookId)) {
                is ApiSuccessResponse -> {
                    val size = result.body.size.orNil
                    FeaturePrefManager.getInstance().setHasBankAccount(size > 0, bookId)
                    PaymentPrefManager.getInstance().setShowSelectedBankFromLocal(true)
                    _merchantBankAccounts.value = result.body
                }
                else -> {
                    _merchantBankAccounts.value = paymentUseCase.getLocalMerchantBankAccounts(bookId)
                }
            }
        } else {
            _merchantBankAccounts.value = paymentUseCase.getLocalMerchantBankAccounts(bookId)
        }
    }

    fun getEdcOrderDetailByPhoneNumber() = viewModelScope.launch {
        when (val response = edcOrderDetailUseCase.getEdcOrderDetailByPhoneNumber()) {
            is ApiSuccessResponse -> {
                _orderDetails.value = response.body
            }
            else -> {
                _orderDetails.value = EdcOrderDetailResponse(result = false, data = null)
            }
        }
    }

    private fun fetchBankNameAndLogo(bankCode: String) = viewModelScope.launch{
        when(val response = paymentUseCase.getBanks(null)){
            is ApiSuccessResponse -> {
                response.body.forEach {
                    if (it.bankCode == bankCode){
                        edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.bankName = it.bankName
                        edcOrderDetailResponse?.data?.beneficiaryAccountDetails?.logoUrl = it.logo
                        _orderDetails.value = edcOrderDetailResponse
                    }
                }
            }
            else -> {

            }
        }
    }

    fun getEdcOrderDetailsByOrderId(orderId: String) = viewModelScope.launch {
        _showLoader.value = true
        when(val response = edcOrderDetailUseCase.getEdcOrderDetailsByOrderId(orderId)){
            is ApiSuccessResponse -> {
                _showLoader.value = false
                edcOrderDetailResponse = response.body
                _orderDetails.value = edcOrderDetailResponse
                if(response.body.data?.beneficiaryAccountDetails?.bankCode.isNotNullOrBlank()){
                    fetchBankNameAndLogo(response.body.data?.beneficiaryAccountDetails?.bankCode.orEmpty())
                }
                if (response.body?.data?.deviceMappingDetails?.type?.equals(
                        EdcOrderType.BUKUWARUNG.name,
                        true
                    ).isTrue
                ) fetchProductName(response.body.data?.orderId.orEmpty())
            }
            else -> {
                _orderDetails.value = EdcOrderDetailResponse(result = false, data = null)
                _showLoader.value = false
            }
        }
    }

    private fun fetchProductName(orderId: String) = viewModelScope.launch {
        when(val response = edcOrderDetailUseCase.getEdcProductDetails(orderId)){
            is ApiSuccessResponse -> {
                _productDetails.value = response.body
                _showLoader.value = false
            }
            else -> {
                _productDetails.value = null
                _showLoader.value = false
            }
        }
    }

    fun cancelOrder(orderId: String) = viewModelScope.launch {
        _showLoader.value = true
        when(val response = edcOrderDetailUseCase.cancelEdcOrder(orderId)){
            is ApiSuccessResponse -> {
                _cancelOrder.value = response.body.result.isTrue
                getEdcOrderDetailsByOrderId(orderId)//calling this fn to refresh the screen.
            }
            else -> {
                _showLoader.value = false
                _cancelOrder.value = false
            }
        }
    }

    fun regeneratePaymentLink(orderId: String) = viewModelScope.launch {
        _showLoader.value = true
        when(val response = edcOrderDetailUseCase.regeneratePaymentLink(orderId)){
            is ApiSuccessResponse -> {
                _regeneratePaymentLink.value = response.body
                _showLoader.value = false
            } else -> {
                _regeneratePaymentLink.value = RegeneratePaymentLinkResponse(result = false, data = null)
                _showLoader.value = false
            }
        }
    }

    fun refundPayment(orderId: String, refundRequest: RefundRequest) = viewModelScope.launch {
        when(val response = edcOrderDetailUseCase.refundPayment(orderId, refundRequest)){
            is ApiSuccessResponse -> {
                getEdcOrderDetailsByOrderId(orderId)//refreshing screen on successfully adding refund bank account
            }
            else -> {
                _refundResponse.value = RefundResponse(success = false)
            }
        }

    }
}