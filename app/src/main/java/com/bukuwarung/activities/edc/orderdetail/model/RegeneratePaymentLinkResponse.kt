package com.bukuwarung.activities.edc.orderdetail.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName


@Keep
data class RegeneratePaymentLinkResponse(

	@field:SerializedName("result")
	val result: Boolean? = null,

	@field:SerializedName("data")
	val data: NewPaymentLink? = null
)

data class NewPaymentLink(

	@field:SerializedName("amount")
	val amount: Any? = null,

	@field:SerializedName("payment_url")
	val paymentUrl: String? = null,

	@field:SerializedName("updated_at")
	val updatedAt: String? = null,

	@field:SerializedName("created_at")
	val createdAt: String? = null,

	@field:SerializedName("external_id")
	val externalId: String? = null,

	@field:SerializedName("expired_at")
	val expiredAt: String? = null,

	@field:SerializedName("order_id")
	val orderId: String? = null,

	@field:SerializedName("status")
	val status: String? = null
)

