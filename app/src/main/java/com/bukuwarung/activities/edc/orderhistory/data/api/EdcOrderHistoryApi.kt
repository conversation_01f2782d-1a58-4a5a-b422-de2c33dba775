package com.bukuwarung.activities.edc.orderhistory.data.api

import com.bukuwarung.activities.edc.orderhistory.model.EdcOrderHistoryResponse
import com.bukuwarung.data.restclient.ApiResponse
import retrofit2.http.GET
import retrofit2.http.Query

interface EdcOrderHistoryApi {

    @GET("ac/api/v2/edc/order/history")
    suspend fun getEdcOrderHistory(
        @Query("plan_type") type: String?,
        @Query("page_number") pageNumber: Int,
        @Query("page_size") pageSize: Int,
        @Query("sort") order: String?,
        @Query("start_date") startDate: String?,
        @Query("end_date") endDate: String?
    ): ApiResponse<EdcOrderHistoryResponse>
}