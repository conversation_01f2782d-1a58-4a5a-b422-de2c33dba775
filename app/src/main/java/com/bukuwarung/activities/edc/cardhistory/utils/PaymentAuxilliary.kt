package com.bukuwarung.activities.edc.cardhistory.utils


import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.payments.data.model.DateFilter
import java.util.*


object PaymentAuxilliary {
    fun getDates(dateFilterDay: DateFilter): Pair<Long?, Long?> {
        val presetVal = dateFilterDay.presetValue
        var cal = Calendar.getInstance()
        presetVal?.let {
            return when (presetVal) {
                PaymentConst.DATE_PRESET.TODAY -> {
                    Pair(cal.timeInMillis, cal.timeInMillis)
                }
                PaymentConst.DATE_PRESET.YESTERDAY -> {
                    cal.add(Calendar.DATE, -1)
                    Pair(cal.timeInMillis, cal.timeInMillis)
                }
                PaymentConst.DATE_PRESET.THIS_WEEK -> {
                    val endTime = cal.timeInMillis
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    //get Monday for this week, DAY_OF_WEEK is 2 for Monday
                    val minus = if (dayOfWeek == 1) -6 else 2 - dayOfWeek
                    cal.add(Calendar.DATE, minus)
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_WEEK -> {
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    val minus = if (dayOfWeek == 1) -6 else 2 - dayOfWeek
                    val lastWeekMonday = minus - 7
                    cal.add(Calendar.DATE, lastWeekMonday)
                    val sTime = cal.timeInMillis
                    cal.add(Calendar.DATE, 6)
                    Pair(sTime, cal.timeInMillis)
                }
                PaymentConst.DATE_PRESET.THIS_MONTH -> {
                    val endTime = cal.timeInMillis
                    cal[Calendar.DAY_OF_MONTH] = 1
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_MONTH -> {
                    val aCalendar = Calendar.getInstance()
                    aCalendar.add(Calendar.MONTH, -1)
                    aCalendar[Calendar.DATE] = 1
                    val sTime = aCalendar.timeInMillis
                    aCalendar[Calendar.DATE] = aCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = aCalendar.timeInMillis
                    Pair(sTime, endTime)
                }
                PaymentConst.DATE_PRESET.THIS_YEAR -> {
                    val endTime = cal.timeInMillis
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    val sTime = cal.timeInMillis
                    Pair(sTime, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_YEAR -> {
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    cal.add(Calendar.YEAR, -1)
                    val sTime = cal.timeInMillis
                    cal.set(Calendar.MONTH, 11)
                    cal[Calendar.DATE] = cal.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = cal.timeInMillis
                    Pair(sTime, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS -> {
                    val endTime = cal.timeInMillis
                    cal.add(Calendar.DATE, -6)
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_TEN_DAYS -> {
                    val endTime = cal.timeInMillis
                    cal.add(Calendar.DATE, -9)
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.CUSTOM_RANGE -> {
                    Pair(dateFilterDay.startDate, dateFilterDay.endDate)
                }
                PaymentConst.DATE_PRESET.ALL -> {
                    Pair(null, null)
                }
            }
        } ?: run {
            val startDate = dateFilterDay.startDays
            val endDate = dateFilterDay.endDays
            return if (startDate != null && endDate != null) {
                cal.add(Calendar.DATE, startDate)
                val sTime = cal.timeInMillis
                cal = Calendar.getInstance()
                cal.add(Calendar.DATE, endDate)
                val endTime = cal.timeInMillis
                Pair(sTime, endTime)
            } else Pair(null, null)
        }
    }
}