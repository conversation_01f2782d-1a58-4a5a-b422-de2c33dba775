package com.bukuwarung.activities.edc.orderhistory.usecase

import com.bukuwarung.activities.edc.cardhistory.model.HistoryItem
import com.bukuwarung.activities.edc.orderhistory.data.repository.EdcOrderHistoryRepo
import com.bukuwarung.activities.edc.orderhistory.model.EdcOrderHistoryResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import javax.inject.Inject

class EdcOrderHistoryUseCase @Inject constructor(private val repo: EdcOrderHistoryRepo) {

    suspend fun getTransactionHistory(
        pageNumber: Int,
        pageSize: Int,
        order: String?,
        startDate: String?,
        endDate: String?,
        type: String?
    ): ArrayList<HistoryItem> {
        try {
            when (val response = repo.getEdcOrderHistory(
                pageNumber,
                pageSize,
                order,
                startDate,
                endDate,
                type
            )) {
                is ApiSuccessResponse -> {
                    return response.body.edcOrderData.history
                }
                else -> {
                    throw Exception()
                }
            }
        } catch (e: Exception) {
            throw Exception()
        }
    }
}

