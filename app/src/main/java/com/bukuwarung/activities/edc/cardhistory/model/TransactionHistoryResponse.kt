package com.bukuwarung.activities.edc.cardhistory.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class TransactionHistoryResponse(

    @field:SerializedName("pagination_details")
    val paginationDetails: PaginationDetails? = null,

    @field:SerializedName("history")
    val history: ArrayList<HistoryItem> = arrayListOf()
)
@Keep
data class HistoryItem(

    @field:SerializedName("date")
    val date: String? = null,

    @field:SerializedName("id")
    val id: String? = null,

    @field:SerializedName("type")
    val type: String? = null,

    @field:SerializedName("reference_number")
    val referenceNumber: String? = null,

    @field:SerializedName("status")
    val status: String? = null,

    @field:SerializedName("amount")
    val amount: Double? = null,

    @field:SerializedName("created_at")
    val createdAt: String? = null,

    @field:SerializedName("payment_date")
    val paymentDate: String? = null,


    @field:SerializedName("payment_status")
    val paymentStatus: String? = null,

    @SerializedName("payment_link_expired")
    val paymentLinkExpired: Boolean? = null,

    @field:SerializedName("plan_type")
    val planType: String? = null,

    @field:SerializedName("order_amount")
    val orderAmount: String? = null,

    @field:SerializedName("name")
    val name: String? = null,

    @field:SerializedName("order_id")
    val orderId: String? = null
)
@Keep
data class PaginationDetails(

    @field:SerializedName("page_number")
    val pageNumber: Int? = null,

    @field:SerializedName("total_count")
    val totalCount: Int? = null,

    @field:SerializedName("page_size")
    val pageSize: Int? = null
)
