package com.bukuwarung.activities.edc.orderdetail.api

import com.bukuwarung.activities.edc.orderdetail.model.*
import com.bukuwarung.data.restclient.ApiResponse
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface EdcOrderDetailApi {

    @GET("ac/api/v2/edc/order/v2")
    suspend fun getEdcOrderDetailByPhoneNumber(): ApiResponse<EdcOrderDetailResponse>

    @GET("ac/api/v2/edc/order/{order_id}")
    suspend fun getEdcOrderDetailByOrderId(@Path("order_id") orderId: String): ApiResponse<EdcOrderDetailResponse>

    @GET("ac/api/v2/cart/view/{order_id}")
    suspend fun getEdcProductDetails(@Path("order_id") orderId: String): ApiResponse<EdcProductDetails>

    @POST("ac/api/v2/edc/order/{order_id}/cancel")
    suspend fun cancelEdcOrder(@Path("order_id") orderId: String): ApiResponse<OrderCancelResponse>

    @POST("ac/api/v2/edc/payment/regenerate-link/order/{orderId}")
    suspend fun regeneratePaymentLink(@Path("orderId") orderId: String): ApiResponse<RegeneratePaymentLinkResponse>

    @POST("ac/api/v2/edc/payment/refund/{orderId}")
    suspend fun refundPayment(
        @Path("orderId") orderId: String,
        @Body request: RefundRequest
    ): ApiResponse<RefundResponse>
}