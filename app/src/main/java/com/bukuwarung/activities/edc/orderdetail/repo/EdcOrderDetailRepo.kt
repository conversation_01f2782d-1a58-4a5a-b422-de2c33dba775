package com.bukuwarung.activities.edc.orderdetail.repo

import com.bukuwarung.activities.edc.orderdetail.api.EdcOrderDetailApi
import com.bukuwarung.activities.edc.orderdetail.model.RefundRequest
import javax.inject.Inject

class EdcOrderDetailRepo @Inject constructor(private val edcOrderDetailApi: EdcOrderDetailApi) {

    suspend fun getEdcOrderDetailByPhoneNumber() =
        edcOrderDetailApi.getEdcOrderDetailByPhoneNumber()

    suspend fun getEdcOrderDetailsByOrderId(orderId: String) = edcOrderDetailApi.getEdcOrderDetailByOrderId(orderId)

    suspend fun getEdcProductDetails(orderId: String) = edcOrderDetailApi.getEdcProductDetails(orderId)

    suspend fun cancelEdcOrder(orderId: String) = edcOrderDetailApi.cancelEdcOrder(orderId)

    suspend fun regeneratePaymentLink(orderId: String) = edcOrderDetailApi.regeneratePaymentLink(orderId)

    suspend fun refundPayment(orderId: String, refundRequest: RefundRequest) = edcOrderDetailApi.refundPayment(orderId, refundRequest)

}