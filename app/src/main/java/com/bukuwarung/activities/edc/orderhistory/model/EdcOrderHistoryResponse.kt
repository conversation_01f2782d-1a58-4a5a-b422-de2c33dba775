package com.bukuwarung.activities.edc.orderhistory.model

import androidx.annotation.Keep
import com.bukuwarung.activities.edc.cardhistory.model.HistoryItem
import com.bukuwarung.activities.edc.cardhistory.model.PaginationDetails
import com.google.gson.annotations.SerializedName

@Keep
data class EdcOrderHistoryResponse(
    val result: Boolean,
    @field:SerializedName("data")
    val edcOrderData: EdcOrderData,


    )

data class EdcOrderData(
    val phone: String,
    @field:SerializedName("order_history")
    val history: ArrayList<HistoryItem> = arrayListOf(),
    val paginationDetails: PaginationDetails? = null,
)
