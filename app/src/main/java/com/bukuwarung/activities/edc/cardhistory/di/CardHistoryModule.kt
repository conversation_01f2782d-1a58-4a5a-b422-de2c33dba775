package com.bukuwarung.activities.edc.cardhistory.di

import com.bukuwarung.activities.edc.cardhistory.data.api.CardTransactionHistoryApi
import com.bukuwarung.activities.edc.cardhistory.data.repository.CardHistoryRepository
import com.bukuwarung.activities.edc.cardhistory.usecase.CardTransactionHistoryUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
class CardHistoryModule {

    @Provides
    fun provideCardHistoryDataSource(@Named("edc")retrofit: Retrofit): CardTransactionHistoryApi =
        retrofit.create(CardTransactionHistoryApi::class.java)

    @Provides
    fun provideCardHistoryRepository(api: CardTransactionHistoryApi) = CardHistoryRepository(api)

    @Provides

    fun provideCardHistoryUseCase(repository: CardHistoryRepository) =
        CardTransactionHistoryUseCase(repository)
}