package com.bukuwarung.activities.business;

import android.view.View;

import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog;
import com.bukuwarung.dialogs.businessselector.BusinessType;
import com.bukuwarung.dialogs.selectableobjectdialog.SelectableObject;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.utils.Utility;

import java.util.List;

class BusinessCategoryClickHandler implements View.OnClickListener, BusinessSelectorDialog.BusinessSelectedListener {

    private CreateBusinessActivity mActivity;
    BusinessSelectorDialog dialog;
    public BusinessCategoryClickHandler(CreateBusinessActivity activity){
        mActivity = activity;
    }

    @Override
    public void onClick(View view) {
        showBusinessTypeDlg();
    }

    public final void showBusinessTypeDlg() {
        List<BusinessType> businessTypes = AppConfigManager.getInstance().getBusinessTypes();
        dialog = null;
        dialog = BusinessSelectorDialog.getInstance(
                mActivity,
                businessTypes,
                this
        );
        if (mActivity.businessType != -1) dialog.setSelectedId(mActivity.businessType);
        dialog.show();

    }

    @Override
    public void onBusinessSelected(SelectableObject datum) {
        mActivity.businessType = datum.getId();
        mActivity.businessTypeName = datum.getName();
        mActivity.businessTypeTv.setText(datum.getName());
        if(Utility.isEqual(datum.getName(),"Lainnya")){
            mActivity.businessType = 36;
        }
        dialog.dismiss();
    }

    @Override
    public void onNewBusinessAdded(SelectableObject datum) {
        mActivity.businessType = datum.getId();
        mActivity.businessTypeTv.setText(datum.getName());
        if (dialog != null) dialog.dismiss();
    }
}
