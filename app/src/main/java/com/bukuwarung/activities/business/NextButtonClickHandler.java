package com.bukuwarung.activities.business;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;

import com.bukuwarung.R;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.dialogs.businessselector.BusinessType;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.InputUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.Utility;

import java.util.List;


final class NextButtonClickHandler implements OnClickListener {
    final  CreateBusinessActivity mActivity;
    Context context;

    NextButtonClickHandler(CreateBusinessActivity activity) {
        this.mActivity = activity;
        this.context = activity.getBaseContext();
    }

    public final void onClick(View view) {

        if(!validate(this.mActivity.ownerNameEt,context.getString(R.string.business_card_owner_hint)) ||
        !validate(this.mActivity.businessNameEt,context.getString(R.string.empty_business_name))) return;

        List<BusinessType> businessTypes = AppConfigManager.getInstance().getBusinessTypes();
        BusinessType selectedBusinessType = new BusinessType(this.mActivity.businessType);

//        if(!businessTypes.contains(selectedBusinessType)) {
//            NotificationUtils.alertToast(context.getString(R.string.select_business));
//            return;
//        }

        String businessName = this.mActivity.businessNameEt.getText().toString();
        if (businessName != null) {
            if (businessName != null) {
                String name = this.mActivity.ownerNameEt.getText().toString();
                if (mActivity.ownerLayout.getVisibility() == View.GONE) {
                    name = BusinessRepository.getInstance(mActivity).getUserName(User.getUserId());
                }
                if (name != null) {
                    this.mActivity.crateBusiness(this.mActivity.businessType, businessName, businessName, name, this.mActivity.businessTypeName);
                    InputUtils.hideKeyBoardWithCheck(mActivity);
                    return;
                }
            }
        }else if(businessName == null || businessName.length() == 0){
            NotificationUtils.alertError(context.getString(R.string.empty_business_name));
        }
    }

    private boolean validate(EditText target, String message){
        if (target.isShown()) {
            String businessName = target.getText().toString().trim();
            if (businessName == null || businessName.length() == 0) {
                NotificationUtils.alertError(message);
                return false;
            }
        }
        return true;
    }
}
