package com.bukuwarung.activities.business;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DefaultAnim;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.Utilities;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;

import java.util.HashMap;
import java.util.Map;


public final class CreateBusinessActivity extends AppActivity {

    public static String EVENT_TYPE = "BUSINESS_TYPE";

    public static int MORE_BUSINESS = 1;
    public static int FIRST_BUSINESS = 2;
    public static int DEF_EVENT = 2;


    protected RelativeLayout ownerLayout;
    protected TextInputEditText ownerNameEt;

    protected RelativeLayout businessLayout;
    protected TextInputEditText businessNameEt;

    protected RelativeLayout businessTypeLayout;
    protected TextView businessTypeTv;

    protected RelativeLayout refLayout;
    protected EditText refCodeEt;

    private MaterialButton btnNext;

    public int defaultBusinessType=0;
    public int businessType=-1;
    public String businessTypeName="";
    private int eventType=DEF_EVENT;

    public CreateBusinessActivity(){
        super(new DefaultAnim());
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView((int) R.layout.activity_create_business);
        this.eventType =  getIntent().getIntExtra(EVENT_TYPE, DEF_EVENT);
        this.defaultBusinessType = 0;

        this.ownerLayout = findViewById(R.id.ownerLayout);
        this.businessLayout = findViewById(R.id.businessLayout);
        this.refLayout = findViewById(R.id.refLayout);

        this.ownerNameEt = findViewById(R.id.ownerNameEt);
        this.refCodeEt = findViewById(R.id.refCode);
        this.businessNameEt = findViewById(R.id.businessNameEt);
        this.businessTypeTv = findViewById(R.id.businessTypeTv);

        businessTypeTv.setOnClickListener(new BusinessCategoryClickHandler(this));

        Toolbar toolbar = findViewById(R.id.toolbar);
        ImageView backBtn = toolbar.findViewById(R.id.backBtn);
        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });

        btnNext = findViewById(R.id.next);

        if (this.eventType == MORE_BUSINESS) {
            this.refLayout.setVisibility(View.GONE);
        } else if (Utility.isBlank(BusinessRepository.getInstance(this).getUserName(User.getUserId()))) {
            if (this.ownerLayout == null) {
                return;
            }
            this.ownerLayout.setVisibility(View.VISIBLE);
        } else {
            this.refLayout.setVisibility(View.GONE);
            this.ownerLayout.setVisibility(View.GONE);
        }

        if (this.businessLayout == null) {
            return;
        }
        btnNext.setOnClickListener(new NextButtonClickHandler(this));
    }


    public final void crateBusiness(int businessType, String sideNavLabel, String businessNm, String ownerNm, String businessTypeName) {

        BusinessRepository businessRepository = BusinessRepository.getInstance(Application.getAppContext());

        BookEntity bookEntity = businessRepository.createBusiness(User.getUserId(), User.getDeviceId(),ownerNm,businessNm,businessType,businessTypeName);
        changeAppState(bookEntity);
        int businessCount = businessRepository.businessCount(User.getUserId());

        if (businessCount>1) {
             AppAnalytics.trackEvent("customer_add_new_business","businessCount",String.valueOf(businessCount));
        }
        final AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        propBuilder.put("business_type", businessType);
        propBuilder.put("business_type_name", businessTypeName);
        propBuilder.put("business_name", businessNm);
        AppAnalytics.trackEvent("business_profile_create", propBuilder);
        Utilities.INSTANCE.sendEventsToBackendWithBureau("business_profile_create", "create_profile",
                SessionManager.getInstance().getBukuwarungToken(), SessionManager.getInstance().getUUID());

        saveReferralCode(getRefCode());

        MainActivity.startActivityAndClearTop(this);
    }

    private void changeAppState(BookEntity bookEntity){
        sessionManager.setBusinessId(bookEntity.bookId);
        sessionManager.setAppState(1);
    }

    private String getRefCode() {
        try {
            if (this.refCodeEt != null) {
                return this.refCodeEt.getText().toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
//             AppAnalytics.trackEvent("error_event", "detail", e.getMessage());

        }
        return "";
    }

    public void saveReferralCode(String code) {

        try {
            if (Utility.isBlank(code))
                return;
//            FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            Map<String, String> referralMap = new HashMap<>();
            AppAnalytics.trackEvent("input_referral_code", "code", code);
//            AppAnalytics.setUserProperty("referral_code", code);
            referralMap.put("userId", User.getUserId());
            referralMap.put("code", code);
            referralMap.put("createdAt", String.valueOf(getTimestamp()));
//            mFirestore.collection("user_referral")
//                    .document().set(referralMap);

        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
//             AppAnalytics.trackEvent("error_event", "detail", e.getMessage());;

        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        MainActivity.startActivityAndClearTop(this);
    }
}
