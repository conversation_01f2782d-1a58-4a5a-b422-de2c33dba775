package com.bukuwarung.activities.business;

import android.app.Dialog;
import android.view.View;
import android.widget.AdapterView;


final class BusinessCategorySelector implements AdapterView.OnItemClickListener {


    CreateBusinessActivity mActivity;
    Dialog mDialog;
    String[] categoryList;

    BusinessCategorySelector(CreateBusinessActivity activity, Dialog dialog, String[] list) {
        this.mActivity = activity;
        this.mDialog = dialog;
        categoryList = list;
    }
    @Override
    public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        this.mActivity.businessType = i;
        this.mActivity.businessTypeTv.setText(categoryList[i]);
        this.mDialog.dismiss();

    }
}
