package com.bukuwarung.activities

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.utils.isNotNullOrEmpty
import com.google.firebase.crashlytics.FirebaseCrashlytics
import org.json.JSONException
import org.json.JSONObject

class EventLoggingReceiver : BroadcastReceiver() {

    companion object {
        const val TAG = "EventLoggingReceiver"
        const val ACTION_LOG_EVENT = "com.bukuwarung.action.ACTION_LOG_EVENT"
        const val KEY = "key"
        const val PROPS = "props"
    }

    override fun onReceive(context: Context, intent: Intent?) {
        /**
         * It is expected that event will come in "key" string
         * Rest of the data is expted to come in JSON string in "props" key
         */
        intent?.let {
            val event = intent.getStringExtra(KEY)
            val props = intent.getStringExtra(PROPS)
            val propBuilder = AppAnalytics.PropBuilder()

            try {
                props?.let {
                    val json = JSONObject(it)
                    json.keys().forEachRemaining { key ->
                        propBuilder.put(key, json[key])
                    }
                }
            } catch (ex: JSONException) {
                FirebaseCrashlytics.getInstance().recordException(ex)
            }
            if (event.isNotNullOrEmpty()) {
                AppAnalytics.trackEvent(event, propBuilder)
            }
        }
    }
}