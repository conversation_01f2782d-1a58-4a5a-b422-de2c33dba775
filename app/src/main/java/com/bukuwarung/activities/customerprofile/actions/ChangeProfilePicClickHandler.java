package com.bukuwarung.activities.customerprofile.actions;

import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.R;
import com.bukuwarung.activities.customerprofile.CustomerDetailActivity;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog;

public class ChangeProfilePicClickHandler implements OnClickListener {


    final CustomerDetailActivity customerDetailActivity;
    final CustomerEntity customer;

    public ChangeProfilePicClickHandler(CustomerDetailActivity customerDetailActivity, CustomerEntity customerEntity) {
        this.customerDetailActivity = customerDetailActivity;
        this.customer = customerEntity;
    }

    public final void onClick(View view) {
        ImageSelectorDialog imageSelectorDialog = new ImageSelectorDialog(customerDetailActivity, customerDetailActivity, "");
        imageSelectorDialog.show();
    }
}
