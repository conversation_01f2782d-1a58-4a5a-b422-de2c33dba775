package com.bukuwarung.activities.customerprofile;

import com.bukuwarung.session.SessionManager;
import com.hbb20.CountryCodePicker.OnCountryChangeListener;

final class PhoneCountryChangeListener implements OnCountryChangeListener {

    CustomerDetailActivity activity;

    PhoneCountryChangeListener(CustomerDetailActivity activity) {
        this.activity = activity;
    }

    public final void onCountrySelected() {
        String selectedCountryCodeWithPlus = activity.countrySelector.getSelectedCountryCodeWithPlus();

        activity.phoneCode = selectedCountryCodeWithPlus;
        SessionManager sessionManager = SessionManager.getInstance();

        if (!sessionManager.isLoggedIn()) {
            sessionManager.setCountryCode(this.activity.phoneCode);
        }
    }
}
