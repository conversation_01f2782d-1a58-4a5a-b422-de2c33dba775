package com.bukuwarung.activities.customerprofile.actions;

import android.view.View;
import android.view.View.OnClickListener;

import com.bukuwarung.activities.customerprofile.CustomerDetailActivity;
import com.bukuwarung.activities.customerprofile.tasks.DeleteCustomerAsyncTask;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.transaction.customer.reminder.DeleteCstDialog;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.database.entity.CustomerEntity;

public class DeleteCustomerClickHandler implements OnClickListener {


    final CustomerDetailActivity customerDetailActivity;
    final CustomerEntity customer;

    public DeleteCustomerClickHandler(CustomerDetailActivity customerDetailActivity, CustomerEntity customerEntity) {
        this.customerDetailActivity = customerDetailActivity;
        this.customer = customerDetailActivity.customer;
    }

    public final void onClick(View view) {
        showDeletionDialog(customerDetailActivity);
    }

    private final void showDeletionDialog(final AppActivity baseActivity) {
        CustomerEntity customerEntity = CustomerDetailActivity.getCustomer(customerDetailActivity);
        final DeleteCstDialog dialog =
                new DeleteCstDialog(
                        baseActivity,
                        promptResult -> {
                            if (promptResult) {
                                new DeleteCustomerAsyncTask(baseActivity).execute(new CustomerEntity[]{customerEntity});
                                AppAnalytics.trackEvent("delete_customer");
                                MainActivity.startActivitySingleTopToTab(baseActivity, TabName.CUSTOMER);
                            }
                            return null; // java to kotlin unit conversion
                        }
                );
        dialog.show();
    }
}
