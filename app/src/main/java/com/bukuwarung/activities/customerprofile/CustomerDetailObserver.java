package com.bukuwarung.activities.customerprofile;


import androidx.lifecycle.Observer;

import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.ProfileIconHelper;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.hbb20.CountryCodePicker;

final class CustomerDetailObserver<T> implements Observer<CustomerEntity> {
    final CustomerDetailActivity customerDetailActivity;

    CustomerDetailObserver(CustomerDetailActivity customerDetailActivity) {
        this.customerDetailActivity = customerDetailActivity;
    }

    public final void onChanged(CustomerEntity customerEntity) {
        if (customerEntity != null) {
            try {
                this.customerDetailActivity.customer = customerEntity;

                this.customerDetailActivity.smsSwitch.setChecked(customerEntity.enableSmsAlerts == 1);
                String customerNm = Utility.isBlank(customerEntity.name)?"O":customerEntity.name;
                if (!Utility.isBlank(customerNm)) {
                    CustomerDetailActivity.getName(this.customerDetailActivity).setText(customerEntity.name);
                    CustomerDetailActivity.getName(this.customerDetailActivity).setSelection(customerEntity.name.length());
                    customerNm = customerEntity.name;
                }
                this.customerDetailActivity.setProfileSrc(customerEntity.image);
                ProfileIconHelper.setProfilePic(this.customerDetailActivity, CustomerDetailActivity.getProfilePic(this.customerDetailActivity), CustomerDetailActivity.getNameInitial(this.customerDetailActivity), customerNm, customerEntity.image);
                if (!Utility.isBlank(customerEntity.phone)) {
                    String purifyMobileNumber = Utility.cleanPhonenumber(customerEntity.phone);
                    customerDetailActivity.phoneNumber.setText(purifyMobileNumber);
                    CustomerDetailActivity.getPhone(this.customerDetailActivity).setSelection(purifyMobileNumber.length());
                }
                if (!Utility.isBlank(customerEntity.address)) {
                    this.customerDetailActivity.address.setText(customerEntity.address);
                    this.customerDetailActivity.address.setSelection(customerEntity.address.length());
                }
                String countryCd = CustomerDetailActivity.getCustomer(this.customerDetailActivity).countryCode;
                if (Utility.isBlank(countryCd)) {
                    CountryCodePicker countryPicker = CustomerDetailActivity.getCountryPicker(this.customerDetailActivity);
                    String countryCode = SessionManager.getInstance().getCountryCode();

                    if (countryCode != null) {
                        String cdWithoutStr = countryCode.substring(1);
                        countryPicker.setDefaultCountryUsingPhoneCode(Integer.parseInt(cdWithoutStr));
                        customerDetailActivity.phoneCode = CustomerDetailActivity.getCustomer(this.customerDetailActivity).countryCode;
                    }
                } else {

                    CustomerDetailActivity.getCountryPicker(this.customerDetailActivity).setCountryForPhoneCode(Integer.parseInt(countryCd));
                    this.customerDetailActivity.phoneCode = CustomerDetailActivity.getCustomer(this.customerDetailActivity).countryCode;
                }
                if(customerEntity.language == null || customerEntity.language.intValue() == 12 || customerEntity.language == -1 ){
                    customerDetailActivity.getCustomerLangTv(customerDetailActivity).setText("Bahasa Indonesia");
                }else{
                    customerDetailActivity.getCustomerLangTv(customerDetailActivity).setText("English");
                }

            } catch (Exception e) {
                e.printStackTrace();
                FirebaseCrashlytics.getInstance().recordException(e);
            }
        }
    }
}
