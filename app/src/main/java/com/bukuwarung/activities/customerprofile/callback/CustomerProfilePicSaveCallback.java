package com.bukuwarung.activities.customerprofile.callback;

import android.net.Uri;

import com.bukuwarung.activities.customerprofile.CustomerDetailActivity;
import com.bukuwarung.utils.ProfileIconHelper;
import com.bukuwarung.utils.SaveUploadedImage;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

public final class CustomerProfilePicSaveCallback implements SaveUploadedImage.OnScaleResultCallback {
    final CustomerDetailActivity customerDetailActivity;

    public CustomerProfilePicSaveCallback(CustomerDetailActivity customerDetailActivity) {
        this.customerDetailActivity = customerDetailActivity;
    }

    public final void onComplete(Uri uri) {
        try {
            if (uri != null) {
                this.customerDetailActivity.setProfileSrc(uri.toString());
                String name = CustomerDetailActivity.getName(this.customerDetailActivity).getText().toString();
                String profileSrc = this.customerDetailActivity.getProfileSrc();
                ProfileIconHelper.setProfilePic(this.customerDetailActivity, CustomerDetailActivity.getProfilePic(this.customerDetailActivity), CustomerDetailActivity.getNameInitial(this.customerDetailActivity), name, profileSrc);
            }
        }catch(Exception e){
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
