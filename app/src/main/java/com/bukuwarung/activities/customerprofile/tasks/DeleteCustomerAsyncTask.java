package com.bukuwarung.activities.customerprofile.tasks;

import android.os.AsyncTask;

import com.bukuwarung.Application;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.session.User;

public class DeleteCustomerAsyncTask extends AsyncTask<CustomerEntity, Void, Void> {

    private final AppActivity activity;

    public DeleteCustomerAsyncTask(AppActivity baseActivity) {

        this.activity = baseActivity;
    }


    public Void doInBackground(CustomerEntity... customerEntityArr) {

        CustomerEntity customerEntity = customerEntityArr[0];
        CustomerRepository customerRepository = CustomerRepository.getInstance(Application.getAppContext());
        String userId = User.getUserId();
        String deviceId = User.getDeviceId();
        customerRepository.updateExistingCustomer(userId, deviceId, customerEntity.customerId, customerEntity.name, customerEntity.address, customerEntity.phone, customerEntity.countryCode, 1);
        return null;
    }


    public void onPostExecute(Void voidR) {
        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        propBuilder.put(AnalyticsConst.UPDATE_TYPE, AnalyticsConst.DELETE);
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_DETAILS_UPDATE, propBuilder);
        if (this.activity != null) {
            this.activity.finish();
        }
    }
}
