package com.bukuwarung.activities.customerprofile;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.ViewModelProviders;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.customerprofile.actions.ChangeProfilePicClickHandler;
import com.bukuwarung.activities.customerprofile.actions.DeleteCustomerClickHandler;
import com.bukuwarung.activities.customerprofile.callback.CustomerProfilePicSaveCallback;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DefaultAnim;
import com.bukuwarung.activities.transaction.customer.TransactionListViewModel;
import com.bukuwarung.activities.transaction.customer.TransactionViewModelFactory;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.database.entity.CustomerEntity;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.datasync.AppCustomerSyncManager;
import com.bukuwarung.enums.Language;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ImageUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.SaveUploadedImage;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.material.button.MaterialButton;
import com.hbb20.CountryCodePicker;

import java.util.List;


public final class CustomerDetailActivity extends AppActivity implements OnClickListener {

    public CustomerEntity customer;

    public Switch smsSwitch;

    private AppCompatImageView profilePic;
    private ImageView camera;
    protected CountryCodePicker countrySelector;
    private TextView smsLanguageTv;
    protected EditText phoneNumber;
    private TextView nameInitial;
    protected EditText address;

    private String profileSrc;
    private Uri tempImageSrc;

    private EditText name;
    public String phoneCode;
    private String customerId;


    private LinearLayout deleteLayout;
    private MaterialButton edit;
    private MaterialButton save;
    private CustomerRepository customerRepository;

    private TransactionListViewModel viewModel;

    public CustomerDetailActivity() {
        super(new DefaultAnim());
        this.phoneCode = sessionManager.getInstance().getCountryCode();
    }

    public static final TextView getCustomerLangTv(CustomerDetailActivity customerDetailActivity) {
        return customerDetailActivity.smsLanguageTv;
    }

    public static final CustomerEntity getCustomer(CustomerDetailActivity customerDetailActivity) {
        return customerDetailActivity.customer;
    }

    public static final CountryCodePicker getCountryPicker(CustomerDetailActivity customerDetailActivity) {
        return customerDetailActivity.countrySelector;
    }

    public static final EditText getName(CustomerDetailActivity customerDetailActivity) {
        return customerDetailActivity.name;
    }

    public static final TextView getNameInitial(CustomerDetailActivity customerDetailActivity) {
        return customerDetailActivity.nameInitial;
    }

    public static final EditText getPhone(CustomerDetailActivity customerDetailActivity) {
        return customerDetailActivity.phoneNumber;
    }

    public static final AppCompatImageView getProfilePic(CustomerDetailActivity customerDetailActivity) {
        return customerDetailActivity.profilePic;
    }

    public final String getProfileSrc() {
        return this.profileSrc;
    }

    public final void setProfileSrc(String str) {
        this.profileSrc = str;
    }

    public static final void showLanguageDialog(final Activity activity) {


        Context context = activity;
        final Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_select_language);
        Window window = dialog.getWindow();
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        dialog.setCancelable(false);

        try {
            dialog.findViewById(R.id.closeDialog).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    dialog.dismiss();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

        ListView listView = (ListView) dialog.findViewById(R.id.cstLanguage);
        String[] languageList = activity.getResources().getStringArray(R.array.SupportedLanguage);

        listView.setAdapter(new ArrayAdapter(context, R.layout.langauge_list_item, R.id.language_item, languageList));
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {

            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                try {
                    CustomerEntity customer = getCustomer((CustomerDetailActivity) activity);
                    MaterialButton materialButton = (MaterialButton) view.findViewById(R.id.language_item);
                    String langName = materialButton.getText().toString();
                    Integer oldLang = customer.language;
                    if (Utility.areEqual(langName, "English") || Utility.areEqual(langName, "Inggris")) {
                        customer.language = Language.ENGLISH.getLangCd();
                    } else {
                        customer.language = Language.INDONESIAN.getLangCd();
                    }
                    if (customer.language == 1) {
                        getCustomerLangTv((CustomerDetailActivity) activity).setText("English");
                    } else {
                        getCustomerLangTv((CustomerDetailActivity) activity).setText("Bahasa Indonesia");
                    }
                    if (oldLang == null || oldLang.intValue() != customer.language.intValue()) {

                        try {
                            CustomerRepository.getInstance(activity.getBaseContext()).updateCustomerLanguage(User.getUserId(), User.getDeviceId(), customer.customerId, customer.language);
                            CustomerEntity cst = CustomerRepository.getInstance(Application.getAppContext()).getCustomerById(customer.customerId);
                            AppCustomerSyncManager.getInstance().updateCustomer(cst);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                dialog.dismiss();

            }
        });
        dialog.show();

    }

    public void onCreate(Bundle bundle) {

        super.onCreate(bundle);

        setContentView((int) R.layout.activity_customer_detail);
        Toolbar toolbarView = findViewById(R.id.toolbar);
        setSupportActionBar(toolbarView);
        toolbarView.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        this.customerId = getIntent().getStringExtra("customerId");

        this.deleteLayout = findViewById(R.id.deleteContainer);
        this.save = findViewById(R.id.save);
        this.edit = findViewById(R.id.edit);
        this.profilePic = findViewById(R.id.profilePic);
        this.nameInitial = findViewById(R.id.nameInitial);
        this.camera = findViewById(R.id.cameraIcon);
        this.camera.setVisibility(View.INVISIBLE);
        this.name = findViewById(R.id.customerName);
        this.phoneNumber = findViewById(R.id.mobile);
        this.countrySelector = findViewById(R.id.countryPicker);
        this.address = findViewById(R.id.address);
        smsSwitch = findViewById(R.id.switch_sms);
        customerRepository = CustomerRepository.getInstance(getBaseContext());

        try {
            smsSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public final void onCheckedChanged(CompoundButton compoundButton, boolean isChecked) {
                    if (customer == null) {
                        NotificationUtils.alertError("Terjadi kesalahan, silakan restart aplikasi atau perangkat Anda");
                        return;
                    }
                    if (isChecked) {
                        customer.enableSmsAlerts = Integer.valueOf(1);
                        customerRepository.updateEnableSmsAlertsForCustomer(User.getUserId(), User.getDeviceId(), customer.customerId, Integer.valueOf(1));
                    } else if (!isChecked) {
                        customer.enableSmsAlerts = Integer.valueOf(0);
                        customerRepository.updateEnableSmsAlertsForCustomer(User.getUserId(), User.getDeviceId(), customer.customerId, Integer.valueOf(0));
                        try {
                            CustomerEntity cst = customerRepository.getCustomerById(customer.customerId);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }
                }
            });

            try {
                this.smsLanguageTv = findViewById(R.id.sms_language);
                final Activity activity = this;
                smsLanguageTv.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        showLanguageDialog(activity);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }

            this.save.setOnClickListener(this);
            this.edit.setOnClickListener(this);

            TextView toolBarLabel = findViewById(R.id.toolBarLabel);
            toolBarLabel.setText(getString(R.string.customerProfileTitle));

            TextView customerLabelTv = findViewById(R.id.customerLabel);
            customerLabelTv.setText(getString(R.string.input_customer_name));

            this.name.setHint(getString(R.string.input_customer_name_hint));

            ActionBar supportActionBar = getSupportActionBar();
            supportActionBar.setDisplayHomeAsUpEnabled(true);

            FragmentActivity fragmentActivity = this;
            Application application = new Application();

            this.viewModel = ViewModelProviders.of(fragmentActivity, new TransactionViewModelFactory(application, this.customerId)).get(TransactionListViewModel.class);

            TransactionListViewModel transactionListViewModel = this.viewModel;
            LiveData customerEntity = transactionListViewModel.getCustomerLiveData();

            customerEntity.observe(this, new CustomerDetailObserver(this));

            this.countrySelector.setOnCountryChangeListener(new PhoneCountryChangeListener(this));
            changeViewMode(getIntent().getBooleanExtra("isEditable", false));

            this.camera.setOnClickListener(new ChangeProfilePicClickHandler(this, customer));
            this.deleteLayout.setOnClickListener(new DeleteCustomerClickHandler(this, this.customer));

        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i2 == -1 && i == PermissionConst.TAKE_PHOTO) {
            handleCameraImage();
        } else if (i == PermissionConst.REQ_PICK_IMAGE_PERMISSON && i2 == -1 && intent != null && intent.getData() != null) {
            handlerSelectImage(intent);
        }
    }

    private void handlerSelectImage(Intent srcIntent) {
        Uri src = srcIntent.getData();
        if (src != null) {
            new SaveUploadedImage().scaleAndSaveImage(src, new CustomerProfilePicSaveCallback(this));
        }
    }

    private void handleCameraImage() {
        if (this.tempImageSrc != null) {
            new SaveUploadedImage().scaleAndSaveImage(this.tempImageSrc, new CustomerProfilePicSaveCallback(this));
        }
    }

    public final void setGalleryImageUri() {
        try {
            Intent intent = new Intent();
            intent.setType("image/*");
            intent.setAction("android.intent.action.GET_CONTENT");
            startActivityForResult(Intent.createChooser(intent, getString(R.string.select_image_instruction)), PermissionConst.REQ_PICK_IMAGE_PERMISSON);
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public final void setImageSourceFromCamera() {
        try {
            this.tempImageSrc = ImageUtils.getImageSource();
            Intent intent = ImageUtils.getCameraIntent(tempImageSrc);
            if (VERSION.SDK_INT >= 23 && !(this.checkSelfPermission("android.permission.CAMERA") == PackageManager.PERMISSION_GRANTED)) {
                requestPermissions(PermissionConst.CAMER_AND_STORAGE, PermissionConst.REQ_TAKE_PICTURE_PERMISSON);
            }
            if (intent.resolveActivity(getPackageManager()) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onClick(View view) {
        try {
            switch (view.getId()) {
                case R.id.edit:
                    changeViewMode(true);
                    break;
                case R.id.save:
                    String customerNm = this.name.getText().toString();
                    if (!(customerNm == null || customerNm.length() == 0)) {
                        String phoneNumber = this.phoneNumber.getText().toString();
                        if (phoneNumber.length() > 0 && !Utility.areEqual(this.customer.phone, phoneNumber)) {
                            String bookId = User.getBusinessId();
                            List customersWithPhone = customerRepository.getCustomersWithPhone(bookId, SessionManager.getInstance().getCountryCode(), this.phoneNumber.getText().toString());
                            if (customersWithPhone != null && customersWithPhone.size() > 0) {
                                NotificationUtils.alertToast("Customer with same number already exists");
                                return;
                            }
                        }
                        this.customer.image = this.profileSrc;
                        this.customer.name = this.name.getText().toString();
                        this.customer.phone = Utility.cleanPhonenumber(this.phoneNumber.getText().toString());
                        this.customer.address = this.address.getText().toString();
                        this.customer.countryCode = this.phoneCode;
                        changeViewMode(false);

                        Bitmap bitmap = null;
                        try {
                            if (this.profileSrc != null) {
                                bitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), Uri.parse(this.profileSrc));
                                bitmap = Utility.fixImageRotation(this, bitmap, null, null);
                            }
                        } catch (Exception ex) {
                            FirebaseCrashlytics.getInstance().recordException(ex);
                        }

                        AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
                        prop.put(AnalyticsConst.UPDATE_TYPE, AnalyticsConst.EDIT_NEW);
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CUSTOMER_DETAILS_UPDATE, prop);
                        viewModel.updateExistingCustomer(customer, bitmap);

                    } else {
                        NotificationUtils.alertOnTop(getString(R.string.customer_name_hint));
                        return;
                    }
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {

        if (i == PermissionConst.REQ_TAKE_PICTURE_PERMISSON) {
            if (VERSION.SDK_INT >= 23) {
                if (!(ContextCompat.checkSelfPermission(this, "android.permission.CAMERA") == 0)) {
                    NotificationUtils.alertToast(getString(R.string.camera_permission_denied_message));
                    return;
                } else {
                    setImageSourceFromCamera();
                }
                AppAnalytics.trackEvent("granted_storage_permission");
            }
        }
    }

    private final void changeViewMode(boolean isEditing) {
        if (isEditing) {
            this.save.setVisibility(View.VISIBLE);
            this.camera.setVisibility(View.VISIBLE);
            this.edit.setVisibility(View.GONE);
            this.name.setEnabled(true);
            this.address.setEnabled(true);
            phoneNumber.setEnabled(true);
            countrySelector.setCcpClickable(true);
            return;
        }
        this.camera.setVisibility(View.GONE);
        this.save.setVisibility(View.GONE);
        this.edit.setVisibility(View.VISIBLE);
        this.name.setEnabled(false);
        this.address.setEnabled(false);
        this.phoneNumber.setEnabled(false);
        this.countrySelector.setCcpClickable(false);
    }

}
