package com.bukuwarung.activities.transactionreport;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.home.TabName;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.superclasses.DefaultAnim;
import com.bukuwarung.activities.transactionreport.adapter.ReportAdapter;
import com.bukuwarung.activities.transactionreport.download.DownloadReportHandler;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.analytics.SurvicateAnalytics;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.enums.ReportFileType;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.NotificationUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.Utility;
import com.google.android.material.button.MaterialButton;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public final class TransactionReportActivity extends AppActivity implements UnduhLaporanBottomSheet.DownloadReportListener{


    public ReportAdapter transactionListAdapter;

    public MaterialButton downloadPdfBtn;
    public ImageView menuStackIcon;
    public RecyclerView recyclerView;
    public LinearLayout endDate;
    public RelativeLayout specificDate;
    public LinearLayout startDate;
    public TextView tvEndDate;
    public TextView tvStartDate;
    public TextView specificDateTv;
    public ReportFileType fileType = ReportFileType.PDF;

    private DatePicker startDatePicker;
    private DatePicker endDatePicker;
    private Toolbar toolbar;
    private DownloadReportHandler downloadReportHandler = null;

    public TextView creditTotal;
    public TextView debitTotal;
    public TextView debitCount;
    public TextView creditCount;
    public TextView totalCount;
    public TextView creditTotalLabel;
    public TextView debitTotalLabel;
    public TextView netBalance;
    public TextView netBalanceDesc;

    private String cashTransactionType = "";
    public static int REPORT_TARGET = 0;
    public static int REPORT_TARGET_CASH = 2;
    public static int REPORT_TARGET_CREDIT = 1;
    private String minDate = "";
    private String maxDate = "";

    public static String EXTRA_START_DATE = "startDate";
    public static String EXTRA_END_DATE = "endDate";
    public static String EXTRA_FILTER = "filter";

    public final String TODAY = "today";
    public final String ALL = "all";
    public final String LAST_WEEK = "lastweek";
    public final String LAST_MONTH = "lastmonth";
    public static String TAG = "TransactionReportException";

    private int defaultSelection = -1;
    private int currentSelected = 0;
    private boolean handlingPN = false;

    public TextView spinnerTv;
    private static final String[] pathsEn = {"All","Today","Last 7 days", "Last 30 days", "Custom Date","Select date Range"};
    private static final String[] pathsId = {"Semua","Hari ini","7 hari terakhir", "30 hari terakhir", "Pilih tanggal","Pilih rentang tanggal"};

    private int lastSelected = 0;

    public List<? extends DataHolder> transactionList = new ArrayList<>();

    public TransactionReportActivity() {
        super(new DefaultAnim());
    }

    public final void setStartDatePicker(DatePicker datePicker) {
        this.startDatePicker = datePicker;
    }

    public final void setEndDatePicker(DatePicker datePicker) {
        this.endDatePicker = datePicker;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_transaction_report);

        this.toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(false);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
        toolbar.setNavigationOnClickListener(v -> finish());
        int filterId = RemoteConfigUtils.PdfDefaultFilterExperiment.INSTANCE.getVariant();
        initToolBar();
        initDateSelector();
        if(getIntent().hasExtra("targetId")){
            try {
                String targetString = getIntent().getExtras().getString("targetId", "0");
                REPORT_TARGET = Integer.parseInt(targetString);
            } catch (Exception e) {
                FirebaseCrashlytics.getInstance().log("Sending targetId that aren't an integer");
            }
            updateReportType(REPORT_TARGET);
            TextView title = toolbar.findViewById(R.id.screen_title);

            if(REPORT_TARGET == REPORT_TARGET_CASH){
                cashTransactionType = "transaksi";
                title.setText(this.getString(R.string.cash_transaction_report));
                TextView outHeader = findViewById(R.id.out);
                TextView inHeader = findViewById(R.id.in);
                outHeader.setText(this.getString(R.string.expense_label));
                inHeader.setText(this.getString(R.string.income_label));
                minDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMinDate(true);
                maxDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMaxDate(true);
            }else{
                cashTransactionType = "utang";
                title.setText(this.getString(R.string.credit_debit_report_title));
                minDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMinDate(false);
                maxDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMaxDate(false);
            }
            if(getIntent().hasExtra(EXTRA_START_DATE) && getIntent().hasExtra(EXTRA_END_DATE)){
                tvStartDate.setText(getIntent().getExtras().getString(EXTRA_START_DATE, minDate));
                tvEndDate.setText(getIntent().getExtras().getString(EXTRA_END_DATE, maxDate));
                //since start and end dates are provided by PN filter should be set to CUSTOMRANGE
                filterId = DateFilter.CUSTOMRANGE;
                //handlingPN flag is required to handle back button
                handlingPN=true;
            }
        }else{
            //handlingPN flag is not required, keep using existing logic
            handlingPN = false;
        }
        this.recyclerView = findViewById(R.id.transactionReportRecyclerView);
        this.recyclerView.setLayoutManager(new LinearLayoutManager(this));
        this.transactionListAdapter = new ReportAdapter(this.transactionList,REPORT_TARGET == REPORT_TARGET_CASH);
        this.recyclerView.setAdapter(this.transactionListAdapter);

        defaultSelection = -1;
        if(getIntent().hasExtra(EXTRA_FILTER)) {
            filterId = Integer.parseInt(getIntent().getExtras().getString(EXTRA_FILTER, "0"));
            filterSelection = filterId;
            setDateFilter(filterSelection);
            if(filterSelection == DateFilter.CUSTOMRANGE){
                //if custom date range PN is sent from MoE start date calendar needs to be triggered
                startDate.performClick();
            }
            //handlingPN flag is required to handle back button
            handlingPN=true;
        }else{
            filterSelection = filterId;
            setDateFilter(filterSelection);
        }
        downloadReportHandler = new DownloadReportHandler(this);

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NotNull String[] permissions,
                                           @NotNull int[] grantResults) {
        if (requestCode == PermissionConst.WRITE_EXTERNAL_STORAGE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                AppAnalytics.trackEvent("granted_storage_permission");
                //TODO: fix it properly. commented out for fixing crash
                //https://console.firebase.google.com/u/0/project/ledger-fcc1e/crashlytics/app/android:com.bukuwarung/issues/f4fa4a72015ec1616ca86b19d5951113?time=last-seven-days&versions=3.5.0%20(3399);3.5.0%20(3419)&sessionEventKey=601BF6F40314000104D5921836446619_1503846456110563869
                downloadPdfBtn.performClick();
            }
        }
    }

    private String[] paths(){
        return SessionManager.getInstance().getAppLanguage() == 1?pathsEn:pathsId;
    }

    private int filterSelection = RemoteConfigUtils.PdfDefaultFilterExperiment.INSTANCE.getVariant();

    private void openDateSelectionPopup(){

        Context wrapper = new ContextThemeWrapper(this, R.style.DatePopupMenu);
        final Activity activity = this;

        final PopupMenu sortDialog = new PopupMenu(wrapper, this.spinnerTv);
        if (Build.VERSION.SDK_INT >= 23) {
            sortDialog.setGravity(Gravity.END);
        }
        sortDialog.getMenuInflater().inflate(R.menu.date_selection_menu, sortDialog.getMenu());
        String string="";
        switch (filterSelection) {
            case DateFilter.TODAY:
//                AppAnalytics.trackEvent("date_filter","filter","today");
                string = this.getString(R.string.today);
                highlightMenu(sortDialog, string, R.id.today);
                break;
            case DateFilter.ALL:
//                AppAnalytics.trackEvent("date_filter","filter","all");
                if(handlingPN){
                    if(REPORT_TARGET == REPORT_TARGET_CASH){
                        minDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMinDate(true);
                        maxDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMaxDate(true);
                    }else{
                        minDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMinDate(false);
                        maxDate = TransactionRepository.getInstance(getBaseContext()).getTransactionMaxDate(false);
                    }
                }
                string = this.getString(R.string.filter_all);
                highlightMenu(sortDialog, string, R.id.all);
                break;
            case DateFilter.LASTWEEK:
//                AppAnalytics.trackEvent("date_filter","filter","last_week");
                string = this.getString(R.string.lastweek);
                highlightMenu(sortDialog, string, R.id.lastweek);
                break;
            case DateFilter.LASTMONTH:
//                AppAnalytics.trackEvent("date_filter","filter","last_month");
                string = this.getString(R.string.lastmonth);
                highlightMenu(sortDialog, string, R.id.lastmonth);
                break;
            case DateFilter.SINGLEDAY:
//                AppAnalytics.trackEvent("date_filter","filter","single_date");
                string = this.getString(R.string.singleday);
                highlightMenu(sortDialog, string, R.id.singleday);
                break;
            case DateFilter.CUSTOMRANGE:
//                AppAnalytics.trackEvent("date_filter","filter","range");
                string = this.getString(R.string.daterange);
                highlightMenu(sortDialog, string, R.id.daterange);
                break;

            default:
//                AppAnalytics.trackEvent("date_filter","filter","lastmonth");
                string = this.getString(R.string.lastmonth);
                highlightMenu(sortDialog, string, R.id.lastmonth);
                break;
        }
        sortDialog.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem menuItem) {
                String string = "";
                int filterId = 0;
                switch (menuItem.getItemId()) {
                    case R.id.today:
                        setDateFilter(DateFilter.TODAY);
                        filterId = DateFilter.TODAY;
                        //AppAnalytics.trackEvent("date_filter","filter","today");
                        string = activity.getString(R.string.today);
                        highlightMenu(sortDialog, string, R.id.today);
                        break;
                    case R.id.all:
                        setDateFilter(DateFilter.ALL);
                        filterId = DateFilter.ALL;

                        //AppAnalytics.trackEvent("date_filter","filter","yesterday");
                        string = activity.getString(R.string.filter_all);
                        highlightMenu(sortDialog, string, R.id.all);
                        break;
                    case R.id.lastweek:
                        setDateFilter(DateFilter.LASTWEEK);
                        filterId = DateFilter.LASTWEEK;

                        //AppAnalytics.trackEvent("date_filter","filter","last_week");
                        string = activity.getString(R.string.lastweek);
                        highlightMenu(sortDialog, string, R.id.lastweek);
                        break;
                    case R.id.lastmonth:
                        setDateFilter(DateFilter.LASTMONTH);
                        filterId = DateFilter.LASTMONTH;

                        //AppAnalytics.trackEvent("date_filter","filter","last_month");
                        string = activity.getString(R.string.lastmonth);
                        highlightMenu(sortDialog, string, R.id.lastmonth);
                        break;
                    case R.id.singleday:
                        setDateFilter(DateFilter.SINGLEDAY);
                        filterId = DateFilter.SINGLEDAY;

                        //AppAnalytics.trackEvent("date_filter","filter","single_date");
                        string = activity.getString(R.string.singleday);
                        highlightMenu(sortDialog, string, R.id.singleday);
                        break;
                    case R.id.daterange:
                        setDateFilter(DateFilter.CUSTOMRANGE);
                        filterId = DateFilter.CUSTOMRANGE;

                        //AppAnalytics.trackEvent("date_filter","filter","range");
                        string = activity.getString(R.string.daterange);
                        highlightMenu(sortDialog, string, R.id.daterange);
                        startDate.performClick();
                        break;

                    default:
                        setDateFilter(DateFilter.LASTMONTH);
                        filterId = DateFilter.LASTMONTH;

                        //AppAnalytics.trackEvent("date_filter","filter","lastmonth");
                        string = activity.getString(R.string.lastmonth);
                        highlightMenu(sortDialog, string, R.id.lastmonth);
                        break;
                }

                trackReportDateFilter(filterId);
                return true;
            }
        });
        sortDialog.show();
    }

    private final void highlightMenu(PopupMenu popupMenu, String str, int i) {
        SpannableString spannableString = new SpannableString(str);
        spannableString.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.colorPrimaryDark)), 0, spannableString.length(), 0);
        popupMenu.getMenu().findItem(i).setTitle(spannableString);
    }

    public void setDateFilter(int filterId) {

        filterSelection = filterId;

        switch (filterId) {
            case 0:
                lastSelected=1;
                spinnerTv.setText(paths()[0]);
//                AppAnalytics.trackEvent("report_all");
                setDateDayAndLoadTransactions(ALL);
                break;
            case 1:
                lastSelected=0;
                spinnerTv.setText(paths()[1]);
                setDateDayAndLoadTransactions(TODAY);
                break;
            case 2:
                lastSelected=2;
                spinnerTv.setText(paths()[2]);
//                AppAnalytics.trackEvent("report_last_week");
                setDateDayAndLoadTransactions(LAST_WEEK);
                break;
            case 3:
                lastSelected=3;
                spinnerTv.setText(paths()[3]);
//                AppAnalytics.trackEvent("report_last_month");
                setDateDayAndLoadTransactions(LAST_MONTH);
                break;
            case 4:
                spinnerTv.setText(paths()[4]);
//                AppAnalytics.trackEvent("report_select_single_date");
                selectFilterDate(AppConst.SPECIFIC_DATE);
                break;
            case 5:
                spinnerTv.setText(paths()[5]);
//                AppAnalytics.trackEvent("report_select_date_range");
                reloadMatchingTransactionList();
                break;
        }
        triggerNextStep = true;
    }

    private void trackReportDateFilter(int filterId) {
        AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
        if(REPORT_TARGET == REPORT_TARGET_CREDIT){
            propBuilder.put(AnalyticsConst.SOURCE, AnalyticsConst.UTANG);
        }else {
            propBuilder.put(AnalyticsConst.SOURCE, AnalyticsConst.TRANSAKSI);
        }
        propBuilder.put(AnalyticsConst.DATE_FILTER_NAME, DateFilter.Companion.getFilterString(this, filterId));
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REPORT_DATE_FILTER, propBuilder);
    }

    public void setCustomRange(boolean triggerEndDate) {
        if(filterSelection!=5){
            triggerEndDate = false;
        }
        this.spinnerTv.setText(paths()[5]);
        filterSelection = 5;
        triggerNextStep = false;
        if(triggerEndDate) {
            this.endDate.performClick();
        }
    }
    public boolean triggerNextStep = true;

    private void initDateSelector(){

        this.creditTotal = findViewById(R.id.creditTotal);
        this.creditCount = findViewById(R.id.creditCount);
        this.debitCount = findViewById(R.id.debitCount);
        this.totalCount = findViewById(R.id.transCount);
        this.debitTotal = findViewById(R.id.debitTotal);
        this.creditTotalLabel = findViewById(R.id.credit_caption);
        this.debitTotalLabel = findViewById(R.id.debit_caption);
        this.netBalance = findViewById(R.id.net_total);
        this.netBalanceDesc = findViewById(R.id.net_total_label);

        this.startDate = findViewById(R.id.startDate);
        this.endDate = findViewById(R.id.endDate);
        this.specificDate = findViewById(R.id.singleDayLayout);
        this.tvStartDate = findViewById(R.id.tvStartDate);
        this.tvEndDate = findViewById(R.id.tvEndDate);
        this.specificDateTv = findViewById(R.id.specificDate);
        this.spinnerTv = findViewById(R.id.singleDateDummy);
        ArrayAdapter<String> adapter = new ArrayAdapter<String>(this,android.R.layout.simple_spinner_item,paths());
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        initFilterInput();

        this.specificDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openDateSelectionPopup();
            }
        });

        this.startDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectFilterDate(AppConst.START_DATE);
            }
        });
        this.endDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectFilterDate(AppConst.END_DATE);
            }
        });
    }


    public final void initToolBar() {
        this.menuStackIcon = findViewById(R.id.back_btn);
        this.downloadPdfBtn = findViewById(R.id.downloadPdf);
        this.menuStackIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        this.downloadPdfBtn.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                showUnduhLaporanBottomSheet();
            }
        });
    }

    @Override
    public void onBackPressed() {
        if (handlingPN) {
            MainActivity.startActivitySingleTopToTab(this, REPORT_TARGET == REPORT_TARGET_CASH ? TabName.TRANSACTION : TabName.CUSTOMER);
            finish();
        } else {
            super.onBackPressed();
        }
    }

    public void onResume() {
        super.onResume();
        setDateFilter(filterSelection);
        Intent intent = getIntent();

        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_OPEN_TRANSACTION_REPORT,this);
    }


    public final void selectFilterDate(String targetType) {
        Calendar calendar = Calendar.getInstance();
        DatePickerDialog datePickerDialog = new DatePickerDialog(this, android.R.style.Theme_DeviceDefault_Light_Dialog,new ReportDatePicker(this, targetType), calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH));
        if(Utility.areEqual(targetType, AppConst.START_DATE)){
            datePickerDialog.setTitle(R.string.start_date);
        }else if(Utility.areEqual(targetType, AppConst.END_DATE)){
            datePickerDialog.setTitle(R.string.end_date);
        }else{
            datePickerDialog.setTitle(R.string.date);
        }
        if (Utility.areEqual(targetType, AppConst.START_DATE) && this.startDatePicker != null) {
            datePickerDialog.updateDate(this.startDatePicker.getYear(), this.startDatePicker.getMonth(), this.startDatePicker.getDayOfMonth());
        } else if (Utility.areEqual(targetType, AppConst.END_DATE) && this.endDatePicker != null) {
            datePickerDialog.updateDate(this.endDatePicker.getYear(), this.endDatePicker.getMonth(), this.endDatePicker.getDayOfMonth());
        }
        if(Utility.areEqual(targetType, AppConst.START_DATE)){
            datePickerDialog.setTitle(R.string.start_date);
        }else if(Utility.areEqual(targetType, AppConst.END_DATE)){
            datePickerDialog.setTitle(R.string.end_date);
        }else{
            datePickerDialog.setTitle(R.string.date);
        }
        datePickerDialog.show();
    }

    public final void initFilterInput() {
        String presentableDate =  Utility.getRegularDateString(DateTimeUtils.convertToDateDDMMYY(Calendar.getInstance().getTime()));
        this.tvStartDate.setText(presentableDate);
        this.tvEndDate.setText(presentableDate);
        this.specificDateTv.setText(presentableDate);
        reloadMatchingTransactionList();
    }


    public void reloadMatchingTransactionList() {

        new AsyncLoadTransactionData(this, this, singleSelected).execute(new Void[0]);
    }

    public DateRange getDateRange() {
        return new DateRange(tvStartDate.getText().toString(), tvEndDate.getText().toString());
    }

    public boolean hasSelectedDateRange(){
        return !(ComponentUtil.matchingTextView(tvStartDate,getString(R.string.start_date)) && ComponentUtil.matchingTextView(tvEndDate,getString(R.string.end_date)));
    }

    public final ReportAdapter getTransactionListAdapter() {
        return this.transactionListAdapter;
    }

    public void updateReportType(int type) {
        switch (type) {
            case 0:
                REPORT_TARGET = REPORT_TARGET_CREDIT;
                displayTransactionList();
                break;
            case 1:
                REPORT_TARGET = REPORT_TARGET_CASH;
                displayTransactionList();
                break;
        }
    }

    public void displayTransactionList() {

        RelativeLayout relativeLayout = this.specificDate;
        new AsyncLoadTransactionData(this, this, relativeLayout.getVisibility() == View.VISIBLE).execute(new Void[0]);
    }

    public final void setDateDayAndLoadTransactions(String str) {

        String endDateStr=null,startDateStr="";
        boolean setDate = false;
        Calendar calendar = Calendar.getInstance();
        singleSelected = false;
        if (Utility.areEqual(str, this.TODAY)) {
            endDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
            startDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
        } else if (Utility.areEqual(str, this.ALL)) {
            if(Utility.isBlank(minDate) || Utility.isBlank(maxDate)){
                endDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
                startDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
            }else{
                endDateStr = Utility.getRegularDateString(DateTimeUtils.convertToDateYYYYMMDD(maxDate));
                startDateStr = Utility.getRegularDateString(DateTimeUtils.convertToDateYYYYMMDD(minDate));
            }
        } else if (Utility.areEqual(str, this.LAST_WEEK)) {
            endDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_MONTH, -6);
            startDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
            singleSelected = false;
        } else if (Utility.areEqual(str, this.LAST_MONTH)) {
            endDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_MONTH, -29);
            startDateStr = Utility.getRegularDateString(getComparableDate(calendar.getTime()));
            singleSelected = false;
        } else {
            endDateStr = null;
            setDate=true;
        }

        String dateStr = Utility.isBlank(startDateStr)?endDateStr:startDateStr;
        this.tvStartDate.setText(startDateStr);
        this.tvEndDate.setText(endDateStr);
        if(setDate) {
            spinnerTv.setText(dateStr);
        }
        displayTransactionList();
    }

    public final Date getComparableDate(Date date) {
        String regularDateString = Utility.getRegularDateString(date);
        return stringToDate(regularDateString);
    }

    public final Date stringToDate(String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MM-yyyy", Locale.US);
        Date date = new Date();
        try {
            return simpleDateFormat.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
            return date;
        }
    }

    private boolean singleSelected;

    private void showUnduhLaporanBottomSheet(){
        if (Utility.hasInternet()) {
            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23 && Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put("entry_point","download_report");
                propBuilder.put("requested_permission","storage");
                AppAnalytics.trackEvent("permission_request",propBuilder);
                requestPermissions(PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR, PermissionConst.WRITE_EXTERNAL_STORAGE);
            } else {
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put(AnalyticsConst.SOURCE,cashTransactionType);

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_DOWNLOAD_REPORT,propBuilder);
                UnduhLaporanBottomSheet unduhLaporanBottomSheet = UnduhLaporanBottomSheet.Companion.newInstance(tvStartDate.getText().toString(),
                        tvEndDate.getText().toString(),
                        cashTransactionType,
                        DateFilter.Companion.getFilterString(this, filterSelection),
                        filterSelection == RemoteConfigUtils.PdfDefaultFilterExperiment.INSTANCE.getVariant(),
                        this);
                unduhLaporanBottomSheet.show(getSupportFragmentManager(), "unduh-laporan-dialog");
            }
        } else{
            NotificationUtils.alertError(getString(R.string.no_internet_error));
        }
    }

    @Override
    public void downloadReport(@NotNull String startDate, @NotNull String endDate, @NotNull ReportFileType fileType) {
        try {
            this.fileType = fileType;
//        trackReportEvent(false);
            downloadReportHandler.processReport(false, DateFilter.Companion.getFilterString(this, filterSelection),
                    filterSelection == RemoteConfigUtils.PdfDefaultFilterExperiment.INSTANCE.getVariant());

        } catch (Exception exception) {
            FirebaseCrashlytics.getInstance().recordException(exception);
        }
    }

    private String dateStrFromTextView(TextView tv){
        String inputText = tv.getText().toString();
        return Utility.getStorableDateString(DateTimeUtils.convertToDateDDMMYYYY(inputText));
    }

    @Override
    public void shareReport(@NotNull String startDate, @NotNull String endDate, @NotNull ReportFileType fileType) {
        try {
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_DOWNLOAD_BOOK_REPORT, this);
            this.fileType = fileType;
            downloadReportHandler.processReport(true, DateFilter.Companion.getFilterString(this, filterSelection),
                    filterSelection == RemoteConfigUtils.PdfDefaultFilterExperiment.INSTANCE.getVariant());
        } catch (Exception exception) {
            Log.d(TAG, exception.getMessage());
        }
    }
}
