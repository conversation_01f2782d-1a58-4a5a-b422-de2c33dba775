package com.bukuwarung.activities.transactionreport;

import android.content.Context;
import android.os.AsyncTask;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transactionreport.adapter.dataholder.TotalDataHolder;
import com.bukuwarung.activities.transactionreport.adapter.dataholder.TransactionDataHolder;
import com.bukuwarung.database.dto.ReportTranasctionModel;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.List;

public final class AsyncLoadTransactionData extends AsyncTask<Void, Void, List<ReportTranasctionModel>> {

    private DateRange dateRange;

    private final boolean hasSpecificDate;
    private final Context mContext;
    private final String specificDate;

    final TransactionReportActivity reportTab;

    public AsyncLoadTransactionData(TransactionReportActivity reportTab, Context context, boolean hasSpecificDate) {
        this.reportTab = reportTab;
        this.mContext = context;
        this.hasSpecificDate = false;
        dateRange = reportTab.getDateRange();
        this.specificDate = reportTab.specificDateTv.getText().toString();
    }


    @Override
    public List<ReportTranasctionModel> doInBackground(Void... voidArr) {
        dateRange.setStartDate(DateTimeUtils.formatDateStringToYYYYMMDD(dateRange.getStartDate()));
        dateRange.setEndDate(DateTimeUtils.formatDateStringToYYYYMMDD(dateRange.getEndDate()));
        if(reportTab.REPORT_TARGET == reportTab.REPORT_TARGET_CASH) {
            return TransactionRepository.getInstance(Application.getAppContext()).getCashTransactionsForDateRange(User.getBusinessId(), dateRange);
        }else{
            return TransactionRepository.getInstance(Application.getAppContext()).getTransactionsForDateRange(User.getBusinessId(), dateRange);
        }
    }


    @Override
    public void onPostExecute(List<ReportTranasctionModel> list) {
        if (dateRange.isRangeSpecified(mContext)) {
            double creditAmount = 0;
            double debitAmount = 0;
            int creditCount = 0,debitCount=0;
            ArrayList dataHolderList = new ArrayList();

            for (ReportTranasctionModel tranasctionModel : list) {
                if (tranasctionModel.getAmount() < ((double) 0)) {
                    debitAmount += tranasctionModel.getAmount();
                    if(!Utility.isBlank(tranasctionModel.date))
                        debitCount++;
                } else {
                    creditAmount += tranasctionModel.getAmount();
                    debitAmount += -1*tranasctionModel.getBuyingPrice();
                    if(!Utility.isBlank(tranasctionModel.date))
                        creditCount++;
                }
                dataHolderList.add(new TransactionDataHolder(tranasctionModel));
            }
            if(!ListUtils.isEmpty(list)){
                dataHolderList.add(new DataHolder.LastRowHolder());
            }
            this.reportTab.transactionList = dataHolderList;
            String formattedCreditTotal =Utility.formatAmount(creditAmount);
            String formattedDebitTotal = Utility.formatAmount(Math.abs(debitAmount));
            String netFormattedTotal = Utility.formatAmount(creditAmount-Math.abs(debitAmount));
            double netbalance = creditAmount - Math.abs(debitAmount);

            this.reportTab.getTransactionListAdapter().setReportTransactionList(this.reportTab.transactionList);
            setTotalAmount(formattedDebitTotal,formattedCreditTotal,netbalance,creditCount,debitCount);
        }
    }

    private void setTotalAmount(String totalDebits, String totalCredits,Double netBalanceAmt,int creditCount,int debitCount) {
        this.reportTab.creditTotal.setText(totalCredits);
        this.reportTab.debitTotal.setText(totalDebits);
        String netBalanceStr = "";
        if(reportTab.REPORT_TARGET == reportTab.REPORT_TARGET_CREDIT) {
            this.reportTab.creditTotalLabel.setText(getString(R.string.credits));
            this.reportTab.debitTotalLabel.setText(getString(R.string.debits));
            if (netBalanceAmt < 0) {
                netBalanceStr = getString(R.string.cash_out_nett_summary_text);
            } else {
                netBalanceStr = getString(R.string.cash_in_nett_summary_text);
            }

        }else{
            this.reportTab.creditTotalLabel.setText(getString(R.string.sales));
            this.reportTab.debitTotalLabel.setText(getString(R.string.cash_out_summary_text));
            if (netBalanceAmt < 0) {
                netBalanceStr = getString(R.string.loss_text);
            } else {
                netBalanceStr = getString(R.string.profit_text);
            }

        }
        if(netBalanceAmt<0){
            this.reportTab.netBalanceDesc.setTextColor(getColor(R.color.out_red));
            this.reportTab.netBalance.setTextColor(getColor(R.color.out_red));
        }else{
            this.reportTab.netBalanceDesc.setTextColor(getColor(R.color.in_green));
            this.reportTab.netBalance.setTextColor(getColor(R.color.in_green));
        }
        StringBuilder sb2 = new StringBuilder();
        sb2.append(Utility.getCurrency());
        sb2.append(" ");
        sb2.append(Utility.formatCurrency(Double.valueOf(Math.abs(netBalanceAmt))));
        this.reportTab.netBalance.setText(sb2.toString());
        this.reportTab.netBalanceDesc.setText(netBalanceStr);
        this.reportTab.creditCount.setText(creditCount+" "+ getString(R.string.trans_header_label));
        this.reportTab.debitCount.setText(debitCount+" "+ getString(R.string.trans_header_label));
        this.reportTab.totalCount.setText((debitCount+creditCount) +" "+ this.getString(R.string.trans_header_label));

    }

    private String getString(int resId){
        return this.reportTab.getString(resId);
    }

    private int getColor(int id){
        return this.reportTab.getResources().getColor(id);
    }
}
