package com.bukuwarung.activities.transactionreport.adapter.viewholder;

import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;

public final class ReportTransactionViewHolder extends RecyclerView.ViewHolder {
    public TextView tvCredit;
    public TextView date;
    public TextView tvDebit;
    public TextView tvNote;

    public ReportTransactionViewHolder(View view) {
        super(view);
        this.date = view.findViewById(R.id.date);
        this.tvDebit = view.findViewById(R.id.debit);
        this.tvCredit = view.findViewById(R.id.credit);
        this.tvNote = view.findViewById(R.id.note);
    }
}

