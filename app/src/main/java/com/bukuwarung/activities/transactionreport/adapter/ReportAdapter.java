package com.bukuwarung.activities.transactionreport.adapter;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.bukuwarung.R;
import com.bukuwarung.activities.customer.transactiondetail.CustomerTransactionDetailActivity;
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transactionreport.adapter.dataholder.TotalDataHolder;
import com.bukuwarung.activities.transactionreport.adapter.dataholder.TransactionDataHolder;
import com.bukuwarung.activities.transactionreport.adapter.viewholder.ReportHeaderViewHolder;
import com.bukuwarung.activities.transactionreport.adapter.viewholder.ReportTransactionViewHolder;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.Utility;

import java.util.List;

public final class ReportAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private List<? extends DataHolder> reportTransactionList;
    private final boolean isExpenseReport;

    public ReportAdapter(List<? extends DataHolder> matchingTransactionList,boolean isExpenseReport) {
        this.reportTransactionList = matchingTransactionList;
        this.isExpenseReport = isExpenseReport;
    }

    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int tag) {
        LayoutInflater from = LayoutInflater.from(viewGroup.getContext());
        if (tag == Tag.REPORT_TAB_TRANSACTION_VIEW) {
            View transactionView = from.inflate(R.layout.report_transaction_view_item, viewGroup, false);
            return new ReportTransactionViewHolder(transactionView);
        } else if (tag == Tag.LAST_ROW) {
            View inflate = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.activity_main_view_customer_end, viewGroup, false);
            return new LastViewHolder(inflate);
        }else {
            View headerView = from.inflate(R.layout.report_header_view_item, viewGroup, false);
            return new ReportHeaderViewHolder(headerView);
        }
    }

    public final class LastViewHolder extends RecyclerView.ViewHolder {

        public LastViewHolder(final View view) {
            super(view);
        }
    }

    public int getItemCount() {
        return this.reportTransactionList.size();
    }

    public int getItemViewType(int i) {
        return (this.reportTransactionList.get(i)).getTag();
    }

    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int i) {
        int itemViewType = viewHolder.getItemViewType();
        if (itemViewType == Tag.REPORT_TAB_HEADER_VIEW) {
            ReportHeaderViewHolder headerViewHolder = (ReportHeaderViewHolder) viewHolder;
            ComponentUtil.setVisible(headerViewHolder.headerLayout,this.reportTransactionList.size() > 1);
        } else if (itemViewType == Tag.REPORT_TAB_TRANSACTION_VIEW) {
            ReportTransactionViewHolder reportTransactionHolder = (ReportTransactionViewHolder) viewHolder;
            final TransactionDataHolder transaction = (TransactionDataHolder) this.reportTransactionList.get(i);
            double transactionAmount = transaction.getTransactionAmount();
            if (transactionAmount < ((double) 0)) {
                reportTransactionHolder.tvCredit.setText("-");
                reportTransactionHolder.tvDebit.setText(Utility.formatAmount(Double.valueOf(Math.abs(transactionAmount))));
            } else {
                double buyingPrice = transaction.getReportTranasctionModel().buyingPrice;
                reportTransactionHolder.tvDebit.setText(buyingPrice == 0 ?"-":Utility.formatAmount(Double.valueOf(buyingPrice)));
                reportTransactionHolder.tvCredit.setText(Utility.formatAmount(Double.valueOf(transactionAmount)));
            }
            reportTransactionHolder.tvNote.setText(transaction.getCustomerName());
            reportTransactionHolder.date.setText(DateTimeUtils.getRegularDateStrWithName(DateTimeUtils.convertToDateYYYYMMDD(transaction.getTransactionDate())));

            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String transId = transaction.getTransactionId();
                    if (!Utility.isBlank(transId)) {
                        Intent intent = new Intent(view.getContext(), CustomerTransactionDetailActivity.class);
                        Bundle bundle = new Bundle();
                        bundle.putString("transactionId", transId);
                        bundle.putString("customerId", transaction.getCustomerId());
                        bundle.putString("OPERATION_TYPE", "VIEW_TRANSACTION");
                        intent.putExtras(bundle);
                        view.getContext().startActivity(intent);
                    } else if (!Utility.isBlank(transaction.getCashTransactionId())) {
                        Intent intent = CashTransactionDetailActivity.Companion.getNewIntent(
                                view.getContext(), transaction.getCashTransactionId(), false
                        );
                        view.getContext().startActivity(intent);
                    }
                }
            });
        }
    }

    public final void setReportTransactionList(List<? extends DataHolder> list) {
        this.reportTransactionList = list;
        notifyDataSetChanged();
    }
}
