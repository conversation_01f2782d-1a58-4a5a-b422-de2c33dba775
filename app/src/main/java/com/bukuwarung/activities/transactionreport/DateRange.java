package com.bukuwarung.activities.transactionreport;

import android.content.Context;

import com.bukuwarung.R;
import com.bukuwarung.utils.Utility;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DateRange {

    private String startDate;
    private String endDate;
    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

    public DateRange(String startDate, String endDate){
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Date getStartDateAsDate(){
        try {
            return simpleDateFormat.parse(this.startDate);
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }
    }

    public Date getEndDateAsDate(){
        try {
            return simpleDateFormat.parse(this.endDate);
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }
    }

    public boolean isRangeSpecified(Context ctx) {
        return !Utility.areEqual(this.startDate, ctx.getString(R.string.start_date)) && !Utility.areEqual(this.endDate, ctx.getString(R.string.end_date));
    }
}
