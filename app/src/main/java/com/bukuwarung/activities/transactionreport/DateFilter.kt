package com.bukuwarung.activities.transactionreport

import android.content.Context
import com.bukuwarung.R


class DateFilter {

    companion object{
        const val ALL = 0
        const val TODAY = 1
        const val LASTWEEK = 2
        const val THISWEEK = 6
        const val THISMONTH = 7
        const val LASTMONTH = 3
        const val SINGLEDAY = 4
        const val CUSTOMRANGE = 5

        fun getFilterString(context : Context,filterSelection: Int): String {
            when (filterSelection) {

                TODAY -> return context.getString(R.string.today_en)

                ALL -> return context.getString(R.string.all_en)

                LASTWEEK -> return context.getString(R.string.last_week_en)

                LASTMONTH -> return context.getString(R.string.last_month_en)

                SINGLEDAY -> return context.getString(R.string.single_day_en)

                CUSTOMRANGE -> return context.getString(R.string.custom_range_en)

                else -> {
                    return ""
                }
            }
        }
    }
}