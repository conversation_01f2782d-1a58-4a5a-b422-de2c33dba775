package com.bukuwarung.activities.transactionreport.download;

import android.net.Uri;

import androidx.lifecycle.LifecycleOwner;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transactionreport.TransactionReportActivity;
import com.bukuwarung.enums.ReportFileType;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskCompletionSource;

import java.util.List;

public final class PrepareBookTransactionsReportPayLoad {
    public int reportTarget;
    public ReportFileType reportFileType;

    public PrepareBookTransactionsReportPayLoad(int reportTarget,ReportFileType reportFileType){
        this.reportTarget = reportTarget;
        this.reportFileType = reportFileType;
    }

    public static class ReportTaskResult {
        public Uri contentUri;
        public String error;

        public ReportTaskResult(Uri uri, String errorMessage) {
            this.contentUri = uri;
            this.error = errorMessage;
        }
    }


    public final Task<ReportTaskResult> getTask(LifecycleOwner lifecycle, String startDate, String endDate, List<? extends DataHolder> list, String dateFilterSelected, Boolean defaultFilter) throws Exception{
        TaskCompletionSource taskCompletionSource = new TaskCompletionSource();
        BookReportTaskExecutor bookReportTaskExecutor = new BookReportTaskExecutor(lifecycle,this, startDate, endDate, list, taskCompletionSource,reportTarget == TransactionReportActivity.REPORT_TARGET_CASH,reportFileType,dateFilterSelected,defaultFilter);
        new Thread(bookReportTaskExecutor).start();
        return taskCompletionSource.getTask();
    }
}
