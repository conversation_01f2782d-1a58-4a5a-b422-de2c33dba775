package com.bukuwarung.activities.transactionreport;

import android.app.DatePickerDialog.OnDateSetListener;
import android.widget.DatePicker;

import com.bukuwarung.constants.AppConst;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.Utility;

import java.util.Calendar;
import java.util.Date;

final class ReportDatePicker implements OnDateSetListener {
    
    final String dateInputTarget;
    final TransactionReportActivity reportTab;

    ReportDatePicker(TransactionReportActivity reportTab, String dateInputTarget) {
        this.reportTab = reportTab;
        this.dateInputTarget = dateInputTarget;
    }

    public final void onDateSet(DatePicker datePicker, int year, int month, int date) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month, date);
        Date time = calendar.getTime();
        Date selectedDate = DateTimeUtils.convertToDateDDMMYY(time);
        String dateForView = DateTimeUtils.getPresentableDateStr(selectedDate);

        if (Utility.areEqual(this.dateInputTarget, AppConst.SPECIFIC_DATE)) {
            this.reportTab.specificDateTv.setText(dateForView);
            this.reportTab.tvEndDate.setText(dateForView);
            this.reportTab.tvStartDate.setText(dateForView);
            this.reportTab.spinnerTv.setText(dateForView);
        } else if (Utility.areEqual(this.dateInputTarget, AppConst.START_DATE)) {
            Date selectedEndDate = DateTimeUtils.convertToDateDDMMYYYY(this.reportTab.tvEndDate.getText().toString());
            if (reportTab.hasSelectedDateRange() && !DateTimeUtils.isValidDateRange(selectedDate,selectedEndDate)) {
                this.reportTab.tvEndDate.setText(dateForView);
            }
            this.reportTab.tvStartDate.setText(dateForView);
            this.reportTab.setCustomRange(true);
        } else if (Utility.areEqual(this.dateInputTarget, AppConst.END_DATE)) {
            Date selectedStartDate = DateTimeUtils.convertToDateDDMMYYYY(this.reportTab.tvStartDate.getText().toString());
            if (reportTab.hasSelectedDateRange() && !DateTimeUtils.isValidDateRange(selectedStartDate,selectedDate)) {
                this.reportTab.tvStartDate.setText(dateForView);
            }
            this.reportTab.tvEndDate.setText(dateForView);
            this.reportTab.setCustomRange(false);
        }
        this.reportTab.reloadMatchingTransactionList();
        initDatePicker(year, month, date);

    }

    private void initDatePicker(int year, int month, int date) {
        if (Utility.areEqual(this.dateInputTarget, AppConst.START_DATE)) {
            DatePicker startDatePicker = new DatePicker(reportTab);
            startDatePicker.init(year, month, date, null);
            this.reportTab.setStartDatePicker(startDatePicker);
        } else if (Utility.areEqual(this.dateInputTarget, AppConst.END_DATE)) {
            DatePicker endDatePicker = new DatePicker(reportTab);
            this.reportTab.setEndDatePicker(endDatePicker);
            endDatePicker.init(year, month, date, null);
        }
    }
}
