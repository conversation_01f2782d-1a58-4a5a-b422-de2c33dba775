package com.bukuwarung.activities.transactionreport.adapter.dataholder;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.dto.ReportTranasctionModel;

public class ReportHeaderDataHolder extends DataHolder {

    private final ReportTranasctionModel reportTranasctionModel;

    public ReportHeaderDataHolder() {
        this.reportTranasctionModel = new ReportTranasctionModel("", "", "", "", 0,0, "","","");
        setTag(Tag.REPORT_TAB_HEADER_VIEW);
    }

    @Override
    public String getName() {
        return reportTranasctionModel.notes;
    }
}