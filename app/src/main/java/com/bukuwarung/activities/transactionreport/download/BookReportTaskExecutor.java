package com.bukuwarung.activities.transactionreport.download;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;

import com.bukuwarung.Application;
import com.bukuwarung.activities.transactionreport.TransactionReportActivity;
import com.bukuwarung.activities.transactionreport.adapter.dataholder.TransactionDataHolder;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.api.ApiRepository;
import com.bukuwarung.api.model.DownloadReortRequest;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.database.dto.ReportTranasctionModel;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.entity.ProductEntity;
import com.bukuwarung.database.entity.TransactionItemsEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.enums.ReportFileType;
import com.bukuwarung.enums.ReportType;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.ReportUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.android.gms.tasks.TaskCompletionSource;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.List;
import java.util.Observer;

import kotlinx.coroutines.GlobalScope;
import retrofit2.Call;

final class BookReportTaskExecutor implements Runnable {

    final String endDate;
    final String startDate;
    final TaskCompletionSource tcs;
    final List transactions;
    final PrepareBookTransactionsReportPayLoad prepareBookTransactionsReportPayLoad;
    final boolean isExpense;
    final ReportFileType reportFileType;
    final String dateFilterSelected;
    final Boolean defaultFilter;
    final LifecycleOwner lifecycle;

    BookReportTaskExecutor(LifecycleOwner lifecycle, PrepareBookTransactionsReportPayLoad genBookTransactionsReport, String startDate, String endDate, List list, TaskCompletionSource taskCompletionSource, boolean downloadExpensePDF, ReportFileType reportFileType, String dateFilterSelected, Boolean defaultFilter) {
        this.prepareBookTransactionsReportPayLoad = genBookTransactionsReport;
        this.startDate = startDate;
        this.endDate = endDate;
        this.transactions = list;
        this.tcs = taskCompletionSource;
        this.isExpense = downloadExpensePDF;
        this.reportFileType = reportFileType;
        this.dateFilterSelected = dateFilterSelected;
        this.defaultFilter = defaultFilter;
        this.lifecycle = lifecycle;
    }

    public final void run() {
        try {
            JsonObject payload = reportPayload(this.startDate, this.endDate, this.transactions);
            AppAnalytics.PropBuilder prop = new AppAnalytics.PropBuilder();
            prop.put("source",isExpense?"transaksi":"utang");
            prop.put("start_date",startDate);
            prop.put("end_date",endDate);
            prop.put("file_type",reportFileType.name());
            prop.put(AnalyticsConst.DATE_FILTER_SELECTED,dateFilterSelected);
            prop.put(AnalyticsConst.DEFAULT_FILTER,defaultFilter);

            AppAnalytics.trackEvent(AnalyticsConst.EVENT_DOWNLOAD_BOOK_REPORT,prop);
            if(reportFileType == ReportFileType.EXCEL){
                DownloadReortRequest requestBody = excelReportPayload();
                if(AppConfigManager.getInstance().usePayloadFromApp()) {
                    requestBody.reportJsonPayloadDto = payload;
                }

                Call postAccountReport = ApiRepository.getExcelReportsApiService().postAccountReportExcelRequest(User.getBusinessId(), requestBody);

                ReportUtils.callReportApi(requestBody, postAccountReport,isExpense?"Cash_Transaksi":"BukuWarung_Laporan",true,reportFileType);
                new Handler(Looper.getMainLooper()).post(() -> ReportUtils.excelLiveData.observe(lifecycle,
                        s -> tcs.setResult(new PrepareBookTransactionsReportPayLoad.ReportTaskResult(Uri.parse(s), null))));
                return;
            }
            Call postAccountReport = isExpense?ApiRepository.getReportsApiService().postExpenseReportPdfRequest(payload):ApiRepository.getReportsApiService().postAccountReportPdfRequest(payload);
            Uri callApiAndDownloadPdf = ReportUtils.callReportApi(null, postAccountReport,isExpense?"Cash_Transaksi":"BukuWarung_Laporan",true);
            if (callApiAndDownloadPdf != null) {
                this.tcs.setResult(new PrepareBookTransactionsReportPayLoad.ReportTaskResult(callApiAndDownloadPdf, null));
                return;
            }
            throw new Exception("generatePdfBookTransactions result should not be null");
        } catch (Exception e) {
            this.tcs.setResult(new PrepareBookTransactionsReportPayLoad.ReportTaskResult(null, e.getMessage()));
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public final DownloadReortRequest excelReportPayload(){
        DownloadReortRequest request = new DownloadReortRequest();
        request.endDate= DateTimeUtils.formatDate(endDate,DateTimeUtils.YYYY_MM_DD, "dd-MM-yyyy");
        request.startDate=DateTimeUtils.formatDate(startDate,DateTimeUtils.YYYY_MM_DD,"dd-MM-yyyy");
        request.reportFileType= ReportFileType.EXCEL;
        request.reportType= isExpense? ReportType.CASH_TRANSACTION_REPORT:ReportType.UTANG_TRANSACTION_REPORT;
        request.sortBy="createdAt";
        request.sortDirection="DESC";
        return request;
    }

    public final JsonObject reportPayload(String startDate, String endDate, List<TransactionDataHolder> reportData) {

        String phoneWithCountryCode = SessionManager.getInstance().getCountryCode()+""+ User.getUserId();
        BookEntity bookSync = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());

        JsonObject reportObj = new JsonObject();
        reportObj.addProperty("language", SessionManager.getInstance().getAppLanguage());
        reportObj.addProperty("businessName", bookSync.businessName);
        reportObj.addProperty("businessPhone", phoneWithCountryCode);
        reportObj.addProperty("businessImage", bookSync.businessImage);
        reportObj.addProperty("businessCaption", bookSync.businessTagLine);
        reportObj.addProperty("startDate", startDate);
        reportObj.addProperty("target", isExpense? TransactionReportActivity.REPORT_TARGET_CREDIT: TransactionReportActivity.REPORT_TARGET_CREDIT);
        reportObj.addProperty("endDate", endDate);
        JsonArray jsonArray = new JsonArray();

        for (TransactionDataHolder reportDataHolder : reportData) {
            ReportTranasctionModel reportTranasctionModel = reportDataHolder.getReportTranasctionModel();
            JsonObject dataObjec = new JsonObject();
            dataObjec.addProperty("date", reportTranasctionModel.getDate());
            dataObjec.addProperty("amount", Double.valueOf(reportTranasctionModel.getAmount()));
            dataObjec.addProperty("name", reportTranasctionModel.getCustomerName());
            dataObjec.addProperty("description", reportTranasctionModel.getNotes());
            try {
                if (isExpense) {
                    dataObjec.addProperty("buyingPrice", Double.valueOf(reportTranasctionModel.getBuyingPrice()));
                    List<TransactionItemsEntity> itemsEntities = TransactionRepository.getInstance(Application.getAppContext()).getTransactionItems(reportDataHolder.getCashTransactionId());
                    JsonArray productArray = new JsonArray();
                    for (TransactionItemsEntity item : itemsEntities) {
                        ProductEntity productEntity = ProductRepository.getInstance(Application.getAppContext()).getProductsById(item.productId);
                        JsonObject productJson = new JsonObject();
                        productJson.addProperty("name", productEntity.name);
                        productJson.addProperty("quantity", item.quantity);
                        productArray.add(productJson);
                    }
                    dataObjec.add("products", productArray);
                    dataObjec.addProperty("txnId", reportTranasctionModel.cashTransactionId);
                }else{
                    dataObjec.addProperty("txnId", reportTranasctionModel.transactionId);
                }

            }catch (Exception e){
                e.printStackTrace();
            }

            jsonArray.add(dataObjec);
        }
        reportObj.add("transactions", jsonArray);
        reportObj.addProperty("includeDescription", Boolean.valueOf(true));
        return reportObj;
        //{"language":12,"businessName":"rest😎😎","businessPhone":"+6596430673","businessImage":"","startDate":"2020-01-01","endDate":"2020-02-08","transactions":[{"date":"2020-01-08","amount":-56.0,"name":"himan","description":""}],"includeDescription":true}
    }
}
