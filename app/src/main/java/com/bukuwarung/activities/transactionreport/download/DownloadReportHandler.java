package com.bukuwarung.activities.transactionreport.download;

import android.app.ProgressDialog;
import android.os.Build;
import android.widget.TextView;

import androidx.lifecycle.LifecycleOwner;

import com.bukuwarung.R;
import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.activities.transactionreport.TransactionReportActivity;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.constants.PermissionConst;
import com.bukuwarung.utils.ComponentUtil;
import com.bukuwarung.utils.DateTimeUtils;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.Utility;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskExecutors;

import java.util.List;

public final class DownloadReportHandler{
    final TransactionReportActivity reportTab;
    public DownloadReportHandler(TransactionReportActivity reportTab) {
        this.reportTab = reportTab;
    }

    public final void processReport(boolean share, String dateFilterSelected, boolean defaultFilter) throws Exception{
        if (Utility.hasInternet() && !ListUtils.isEmpty(this.reportTab.transactionList)) {
            if (!PermissonUtil.hasStoragePermission() && Build.VERSION.SDK_INT >= 23 && Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                this.reportTab.requestPermissions(PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR, PermissionConst.WRITE_EXTERNAL_STORAGE);
            } else{
                String sDateStr = dateStrFromTextView(this.reportTab.tvStartDate);
                String eDateStr = dateStrFromTextView(this.reportTab.tvEndDate);
                downloadReportPdf(sDateStr, eDateStr,this.reportTab.transactionList,share,dateFilterSelected,defaultFilter);
            }
        }
    }


    private String dateStrFromTextView(TextView tv){
        String inputText = tv.getText().toString();
        return Utility.getStorableDateString(DateTimeUtils.convertToDateDDMMYYYY(inputText));
    }

    public final void downloadReportPdf(String startDate, String endDate, List<? extends DataHolder> matchingTransactions, boolean share, String dateFilterSelected, boolean defaultFilter) throws Exception{
        if(startDate == null || endDate == null || matchingTransactions == null){
            return;
        }
        ProgressDialog progressDialog = ComponentUtil.showProgressDialog(reportTab,reportTab.getString(R.string.preparing_customer_pdf));
        AppAnalytics.trackEvent("download_report","target",String.valueOf(reportTab.REPORT_TARGET));
        List<? extends DataHolder> list = matchingTransactions;
        try {
            list.remove(reportTab.transactionList.size()-1);
        }catch (Exception e){
            e.printStackTrace();
        }
        Task<PrepareBookTransactionsReportPayLoad.ReportTaskResult> task = new PrepareBookTransactionsReportPayLoad(reportTab.REPORT_TARGET,reportTab.fileType).getTask((LifecycleOwner)reportTab, startDate, endDate, matchingTransactions,dateFilterSelected,defaultFilter);
        if(share) {
            task.continueWith(TaskExecutors.MAIN_THREAD, new ShareDownloadedReportPDF(reportTab, progressDialog));
        }else{
            task.continueWith(TaskExecutors.MAIN_THREAD, new DisplayDownloadedReportPDF(reportTab, progressDialog, reportTab.fileType));
        }

    }
}
