package com.bukuwarung.activities.transactionreport.download;

import android.app.ProgressDialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;

import com.bukuwarung.R;
import com.bukuwarung.activities.transactionreport.TransactionReportActivity;
import com.bukuwarung.enums.ReportFileType;
import com.bukuwarung.utils.NotificationUtils;
import com.google.android.gms.tasks.Continuation;
import com.google.android.gms.tasks.Task;

public final class DisplayDownloadedReportPDF implements Continuation<PrepareBookTransactionsReportPayLoad.ReportTaskResult, Object> {
    final ProgressDialog progressDialog;
    final TransactionReportActivity activity;
    final ReportFileType fileType;
    public DisplayDownloadedReportPDF(TransactionReportActivity activity, ProgressDialog pDialog, ReportFileType fileType) {
        this.activity = activity;
        this.progressDialog = pDialog;
        this.fileType = fileType;
    }

    public Object then(Task<PrepareBookTransactionsReportPayLoad.ReportTaskResult> task) {
        try {
            this.progressDialog.dismiss();
            PrepareBookTransactionsReportPayLoad.ReportTaskResult apiResult = task.getResult();
            if(apiResult == null || apiResult.contentUri == null){
                NotificationUtils.alertToast("Report service is not available. Please try later.");
            }else{
                Intent intent = new Intent("android.intent.action.VIEW", apiResult.contentUri);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                this.activity.startActivity(intent);
            }
        }catch (ActivityNotFoundException ne){
            if(fileType == ReportFileType.EXCEL){
                Intent share = new Intent();
                share.setAction(Intent.ACTION_SEND);
                share.setType("text/csv");
                share.putExtra(Intent.EXTRA_SUBJECT, activity.REPORT_TARGET == activity.REPORT_TARGET_CASH? activity.getString(R.string.transaksi_report_subject):activity.getString(R.string.utang_report_subject));
                share.putExtra(Intent.EXTRA_TEXT, activity.getString(R.string.report_sharing_message));
                share.putExtra(Intent.EXTRA_STREAM, task.getResult().contentUri);
                activity.startActivity(Intent.createChooser(share, "Bagikan Dengan"));
                return null;
            }
            NotificationUtils.alertToast("PDF viewer not found.");
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
