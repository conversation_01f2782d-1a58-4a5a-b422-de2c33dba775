package com.bukuwarung.activities.transactionreport.adapter.dataholder;

import com.bukuwarung.activities.superclasses.DataHolder;
import com.bukuwarung.constants.Tag;
import com.bukuwarung.database.dto.ReportTranasctionModel;

public class TransactionDataHolder extends DataHolder {

    public ReportTranasctionModel getReportTranasctionModel() {
        return reportTranasctionModel;
    }

    private final ReportTranasctionModel reportTranasctionModel;

    public TransactionDataHolder(ReportTranasctionModel reportTranasctionModel) {
        this.reportTranasctionModel = reportTranasctionModel;
        setTag(Tag.REPORT_TAB_TRANSACTION_VIEW);
    }

    @Override
    public String getName() {
        return reportTranasctionModel.notes;
    }

    public final String getTransactionId() {
        return reportTranasctionModel.transactionId;
    }

    public final String getCustomerId() {
        return reportTranasctionModel.customerId;
    }

    public final String getCashTransactionId() {
        return reportTranasctionModel.cashTransactionId;
    }

    public final String getCashCategoryId() {
        return reportTranasctionModel.cashCategoryId;
    }

    public final String getCustomerName() {
        return reportTranasctionModel.customerName;
    }

    public final String getTransactionDate() {
        return reportTranasctionModel.date;
    }

    public final double getTransactionAmount() {
        return reportTranasctionModel.amount;
    }

    public final String getTransactionDescription() {
        return reportTranasctionModel.notes;
    }
}