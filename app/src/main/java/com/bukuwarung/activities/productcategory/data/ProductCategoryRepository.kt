package com.bukuwarung.activities.productcategory.data

import androidx.lifecycle.LiveData
import com.bukuwarung.activities.productcategory.data.model.*
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utility
import javax.inject.Inject

class ProductCategoryRepository @Inject constructor(
    private val sessionManager: SessionManager,
    private val productCategoryDao: ProductCategoryDao,
    private val remoteDataSource: ProductCategoryRemoteDataSource
) {

    suspend fun createNewCategory(name: String): ProductCategoryEntity {
        val category = ProductCategoryEntity(
            Utility.uuid(),
            sessionManager.businessId,
            name,
            "",
            0
        )
        EntityHelper.fillMetaData(category)

        productCategoryDao.insert(category)
        remoteDataSource.createCategory(category)
        return category
    }

    suspend fun syncCategory(){
        val categories = remoteDataSource.getRemoteCategories(sessionManager.businessId)
        productCategoryDao.insertAll(categories)

        val associations = remoteDataSource.getRemoteProductCategoryAssociations(sessionManager.businessId)
        productCategoryDao.insertProductCategoryCrossRef(associations)
    }

    suspend fun getCategoryById(categoryId: String): ProductCategoryEntity? {
        return productCategoryDao.getCategoryById(categoryId, sessionManager.businessId)
    }

    suspend fun getCategoryByIdFromAllBooks(categoryId: String): ProductCategoryEntity? {
        return productCategoryDao.getCategoryByIdFromAllBooks(categoryId)
    }

    suspend fun updateCategory(newCategoryEntity: ProductCategoryEntity) {
        EntityHelper.updateMetadata(newCategoryEntity)
        productCategoryDao.updateCategory(newCategoryEntity)
        remoteDataSource.updateCategory(newCategoryEntity)
    }

    suspend fun deleteCategoryById(categoryId: String) {
        var productCategory: ProductCategoryEntity? = getCategoryByIdFromAllBooks(categoryId)
        productCategory?.deleted = 1;
        productCategoryDao.deleteCategoryById(categoryId, sessionManager.businessId)
        if(productCategory == null){
            remoteDataSource.deleteCategory(categoryId)
        }else {
            remoteDataSource.deleteCategoryEntity(productCategory)
        }
    }

    suspend fun getCategories(): List<ProductCategoryEntity> {
        return productCategoryDao.getAllCategories(sessionManager.businessId)
    }

    fun getCategoriesSync(): LiveData<List<ProductCategoryEntity>> {
        return productCategoryDao.getAllCategoriesSync(sessionManager.businessId)
    }

    suspend fun createProductCategoriesCrossRefs(crossRefs: List<ProductCategoryCrossRef>) {
        crossRefs.forEach { EntityHelper.fillMetaData(it) }
        productCategoryDao.insertProductCategoryCrossRef(crossRefs)
        remoteDataSource.createProductCategoryAssociations(crossRefs)
    }

    suspend fun deleteMultipleCategoryProductsCrossRef(productIds: List<String>, categoryId: String){
        productCategoryDao.deleteMultipleCategoryProductCrossRef(productIds,  categoryId)
        remoteDataSource.deleteMultipleCategoryProductAssociations(productIds,  categoryId)
    }

    suspend fun deleteMultipleProductCategoryCrossRef(productId: String, categoryIds: List<String>){
        productCategoryDao.deleteMultipleProductCategoryCrossRef(productId,  categoryIds)
        remoteDataSource.deleteMultipleProductCategoryAssociations(productId, categoryIds)
    }

    suspend fun getAllProductsWithCategories(): List<ProductWithCategories> {
        return productCategoryDao.getAllProductsWithCategories(sessionManager.businessId)
    }

    suspend fun getProductWithCategories(productId: String): ProductWithCategories? {
        return productCategoryDao.getProductWithCategories(sessionManager.businessId, productId)
    }

    suspend fun getCategoryWithProducts(categoryId: String?) =
        productCategoryDao.getCategoryWithProducts(sessionManager.businessId, categoryId)

    fun getCategoryWithProductsObservable(categoryId: String?) =
        productCategoryDao.getCategoryWithProductsSync(sessionManager.businessId, categoryId)

    suspend fun getSuggestion(query : String): List<String> {
       return remoteDataSource.getSuggestion(query)
    }

    suspend fun getCategoryByName(name: String) = productCategoryDao.getCategoryByName(name, sessionManager.businessId)

    suspend fun getCategoryCount() = productCategoryDao.getCount(sessionManager.businessId)
}