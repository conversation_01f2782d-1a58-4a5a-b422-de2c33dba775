package com.bukuwarung.activities.productcategory.data

import androidx.lifecycle.LiveData
import com.bukuwarung.activities.productcategory.data.model.ProductCategoryCrossRef
import com.bukuwarung.activities.productcategory.data.model.ProductWithCategories
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.database.helper.EntityHelper
import com.bukuwarung.session.SessionManager
import javax.inject.Inject

class ProductCategoryUseCase @Inject constructor(
    private val productCategoryRepository: ProductCategoryRepository,
    private val sessionManager: SessionManager
) {
    // create new category
    @Throws()
    suspend fun createNewCategory(name: String): ProductCategoryEntity {
        if (checkIfNameAlreadyExist(name)) throw Exception("Category name already exist!")
        return productCategoryRepository.createNewCategory(name)
    }

    // restore categories
    suspend fun syncCategories() {
        productCategoryRepository.syncCategory()
    }

    // delete a category
    suspend fun deleteCategory(categoryId: String) {
        productCategoryRepository.deleteCategoryById(categoryId)
    }

    suspend fun getCategoryById(categoryId: String) =
        productCategoryRepository.getCategoryById(categoryId)

    // update existing category
    @Throws
    suspend fun updateCategory(categoryId: String, newCategoryName: String) {
        if (checkIfNameAlreadyExist(newCategoryName)) throw Exception("Category name already exist!")
        val category = getCategoryById(categoryId) ?: return
        val newCategory = category.copy(name = newCategoryName)

        newCategory.apply {
            createdAt = category.createdAt
            createdByDevice = category.createdByDevice
            createdByUser = category.createdByUser
            serverSeq = category.serverSeq
            EntityHelper.updateMetadata(this)
        }

        productCategoryRepository.updateCategory(newCategory)
    }

    // get all categories
    suspend fun getCategories(): List<ProductCategoryEntity> = productCategoryRepository.getCategories()

    // get all categories (observable)
    fun getObservableCategories(): LiveData<List<ProductCategoryEntity>> =
        productCategoryRepository.getCategoriesSync()

    suspend fun createProductCategoriesAssociation(productId: String, categoryIds: List<String>) {
        // get all existing categories
        val previousCategoryIds =
            productCategoryRepository.getProductWithCategories(productId)?.categories?.map { it.id }

        previousCategoryIds?.let { productCategoryRepository.deleteMultipleProductCategoryCrossRef(productId, it) }

        // associate categories to this product
        val crossRefs = categoryIds.map { ProductCategoryCrossRef(productId, it, sessionManager.businessId, 0) }
        productCategoryRepository.createProductCategoriesCrossRefs(crossRefs)
    }

    suspend fun createCategoryProductsAssociation(categoryId: String, productIds: List<String>) {
        // get all existing products of this category
        val previousProductIdsOfThisCategory =
            productCategoryRepository.getCategoryWithProducts(categoryId)?.products?.map { it.productId }

        // dissociate all products to this category
        previousProductIdsOfThisCategory?.let {
            productCategoryRepository.deleteMultipleCategoryProductsCrossRef(it, categoryId)
        }

        // associate products to this category
        val crossRefs = productIds.map { ProductCategoryCrossRef(it, categoryId, sessionManager.businessId, 0) }
        productCategoryRepository.createProductCategoriesCrossRefs(crossRefs)
    }

    // get products with categories associated with it
    suspend fun getAllProductWithCategories(): List<ProductWithCategories> =
        productCategoryRepository.getAllProductsWithCategories()

    // get products with categories associated with it by product id
    suspend fun getProductWithCategories(productId: String): ProductWithCategories? =
        productCategoryRepository.getProductWithCategories(productId)

    // get a category with all products associated into it
    suspend fun getCategoryWithProducts(categoryId: String?) =
        productCategoryRepository.getCategoryWithProducts(categoryId)

    fun getCategoryWithProductsObservable(categoryId: String?) =
        productCategoryRepository.getCategoryWithProductsObservable(categoryId)

    suspend fun getSuggestion(query: String) = productCategoryRepository.getSuggestion(query)

    private suspend fun checkIfNameAlreadyExist(name: String): Boolean {
        return productCategoryRepository.getCategoryByName(name) != null
    }

    suspend fun getCategoryCount() = productCategoryRepository.getCategoryCount()
}