package com.bukuwarung.activities.productcategory.view

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.FrameLayout
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.core.widget.doOnTextChanged
import com.bukuwarung.R
import com.bukuwarung.activities.productcategory.view.CategoryAction.*
import com.bukuwarung.analytics.moe.trackEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.databinding.ProductCategoryFilterViewBinding
import com.bukuwarung.utils.*

enum class CategoryAction {
    ADD,
    ALL,
    NORMAL
}

data class CategoryDataHolder(
    val category: ProductCategoryEntity?,
    val action: CategoryAction
)

class CategoryAdapter(context: Context, private val layoutRes: Int = R.layout.custom_drop_down_item) :
    ArrayAdapter<CategoryDataHolder>(context, layoutRes) {

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val tempConvertView: View = convertView ?: LayoutInflater.from(context).inflate(layoutRes, parent, false)

        val item = getItem(position)
        val textView = tempConvertView.findViewById<TextView>(R.id.tv_item)

        textView.text = when (item?.action) {
            ADD -> context.getString(R.string.add_category_plus)
            ALL -> context.getString(R.string.all)
            NORMAL -> item.category?.name ?: ""
            null -> ""
        }

        val color = when (item?.action) {
            ADD -> R.color.colorPrimary
            else -> R.color.black_80
        }
        textView.setTextColor(context.getColorCompat(color))

        return tempConvertView
    }

    override fun getItem(position: Int): CategoryDataHolder? {
        return if (position < this.count) {
            super.getItem(position)
        } else {
            null
        }
    }
}

class ProductCategoryFilterView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {
    private val binding = ProductCategoryFilterViewBinding.inflate(LayoutInflater.from(context), this, true)

    private val categoryAdapter: CategoryAdapter by lazy {
        CategoryAdapter(context)
    }

    private val categories = mutableListOf<ProductCategoryEntity>()
    private var isSearching = false

    private lateinit var eventEntryPoint: String
    private var categoryListener: ((dataHolder: CategoryDataHolder?, showAdditionalCategoryAction: Boolean) -> Unit)? =
        null
    private var queryListener: ((String) -> Unit)? = null
    private var currentActivity: Activity? = null
    private var newCategoryEnabled = false
    private var lastValidPosition = 0
    private var previousSelectedItem = ""
    private var firstTimeInvoked = false

    init {
        setupView()
    }

    private fun setupView() {
        binding.apply {
            actvCategory.setAdapter(categoryAdapter)

            btnSearch.setOnClickListener {
                trackSearchButtonEvent()
                isSearching = true
                refreshLayout()
                binding.etSearch.requestFocus()
                InputUtils.showKeyboard(context)
            }

            btnCloseSearch.setOnClickListener {
                isSearching = false
                refreshLayout()
                currentActivity?.let {
                    Utility.hideKeyboard(it)
                }
            }

            actvCategory.setOnItemClickListener { _, _, position, _ ->
                handleItemClick(position)
            }
            actvCategory.setDropDownBackgroundResource(R.color.white)
            actvCategory.setOnClickListener { trackCategoryFilterClickEvent() }

            etSearch.doOnTextChanged { text, _, _, _ -> queryListener?.invoke(text.toString()) }
        }
    }

    private fun handleItemClick(position: Int) {
        val item = categoryAdapter.getItem(position)
        var newCategoryName = ""
        var showAdditionalCategoryAction = false

        when (item?.action) {
            ADD -> {
                context.getString(R.string.add_category_plus)
                val previousSelectedItem = categoryAdapter.getItem(lastValidPosition)
                showAdditionalCategoryAction = previousSelectedItem?.category?.name.isNotNullOrBlank()
                newCategoryName = previousSelectedItem?.category?.name ?: context.getString(R.string.all)
            }
            ALL -> {
                lastValidPosition = position
                newCategoryName = context.getString(R.string.all)
            }
            NORMAL -> {
                lastValidPosition = position
                newCategoryName = item.category?.name.toString()
                showAdditionalCategoryAction = true
            }
            null -> newCategoryName = ""
        }

        binding.actvCategory.setText(newCategoryName, false)
        categoryListener?.invoke(item, showAdditionalCategoryAction)

        previousSelectedItem = newCategoryName
        trackCategoryChangeEvent()
    }

    private fun refreshLayout() {
        binding.apply {
            mainLayout.visibility = (!isSearching).asVisibility()
            searchLayout.visibility = isSearching.asVisibility()

            if (!isSearching) {
                etSearch.setText("")
            }
        }
    }

    fun setCategories(_categories: List<ProductCategoryEntity>, setCreateCategoryEnabled: Boolean = false) {
        newCategoryEnabled = setCreateCategoryEnabled

        // find newly created category
        val newCategory = _categories.subtract(categories.toSet()).toList().firstOrNull()

        categories.apply {
            clear()
            addAll(_categories)
        }

        categoryAdapter.apply {
            clear()
            if (newCategoryEnabled) {
                add(CategoryDataHolder(null, ADD))
            }
            add(CategoryDataHolder(null, ALL))
            addAll(categories.map { CategoryDataHolder(it, NORMAL) })
        }

        if (!firstTimeInvoked) {
            binding.actvCategory.setText(context.getString(R.string.all), false)
            firstTimeInvoked = true
        }
    }

    fun setCategoryListener(callback: (CategoryDataHolder?, Boolean) -> Unit) {
        categoryListener = callback
        categoryListener?.invoke(CategoryDataHolder(null, ALL), false)
    }

    fun setSearchQueryListener(activity: Activity, callback: (String?) -> Unit) {
        currentActivity = activity
        queryListener = callback
    }

    fun setSecondaryButton(@DrawableRes iconRes: Int, onClick: (View) -> Unit) {
        binding.secondaryButton.apply {
            setImageResource(iconRes)
            setOnClickListener { onClick(it) }
        }
    }

    fun getSearchQuery(): String {
        return binding.etSearch.text.toString()
    }

    fun showSecondaryButton(show: Boolean) {
        binding.secondaryButton.visibility = show.asVisibility()
    }

    fun addEventEntryPoint(ep: String) {
        eventEntryPoint = ep
    }

    private fun trackSearchButtonEvent() {
        try {
            trackEvent(AnalyticsConst.CLICK_SEARCH_BUTTON) {
                addEntryPointProperty(eventEntryPoint)
                addProperty(AnalyticsConst.SEARCH_CRITERIA to AnalyticsConst.PRODUCT)
            }
        } catch (ex: Exception) {
            ex.recordException("Entry point hasn't set. Please invoke ProductCategoryFilterView.addEventEntryPoint(String)")
        }
    }


    private fun trackCategoryFilterClickEvent() {
        try {
            trackEvent(AnalyticsConst.CLICK_FILTER_FIELD) {
                addEntryPointProperty(eventEntryPoint)
                addProperty(AnalyticsConst.FILTER_CRITERIA to AnalyticsConst.ProductCatalog.PRODUCT_CATEGORY)
                addProperty(AnalyticsConst.CURRENT_FILTER_VALUE to previousSelectedItem)

                val isDefaultValue = binding.actvCategory.text.toString() == context.getString(R.string.all)
                addProperty(AnalyticsConst.DEFAULT_VALUE to isDefaultValue)
            }
        } catch (ex: Exception) {
            ex.recordException("Entry point hasn't set. Please invoke ProductCategoryFilterView.addEventEntryPoint(String)")
        }
    }

    private fun trackCategoryChangeEvent() {
        try {
            trackEvent(AnalyticsConst.SELECT_FILTER_FIELD, false, true, true) {
                addEntryPointProperty(eventEntryPoint)
                addProperty(AnalyticsConst.FILTER_CRITERIA to AnalyticsConst.ProductCatalog.PRODUCT_CATEGORY)

                addProperty(AnalyticsConst.NEW_FILTER_VALUE to binding.actvCategory.text.toString())
                addProperty(AnalyticsConst.OLD_FILTER_VALUE to previousSelectedItem)
            }
        } catch (ex: Exception) {
            ex.recordException("Entry point hasn't set. Please invoke ProductCategoryFilterView.addEventEntryPoint(String)")
        }
    }

}