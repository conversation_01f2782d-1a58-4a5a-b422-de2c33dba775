package com.bukuwarung.activities.productcategory.data.model


import com.google.gson.annotations.SerializedName
import androidx.annotation.Keep

@Keep
data class CategorySuggestionModelResponse(
    @SerializedName("result") val result : Bo<PERSON>an,
    @SerializedName("data") val data : List<CategorySuggestionModel>
)

@Keep
data class CategorySuggestionModel(
    @SerializedName("category") val category: String,
    @SerializedName("categoryId") val categoryId: Int,
    @SerializedName("parentCategory") val parentCategory: String,
    @SerializedName("parentCategoryId") val parentCategoryId: Int,
    @SerializedName("subCategory") val subCategory: String,
    @SerializedName("subCategoryId") val subCategoryId: Int
)