package com.bukuwarung.activities.productcategory.data.model

import com.bukuwarung.data.restclient.ApiResponse
import retrofit2.http.GET
import retrofit2.http.Query

interface ProductCategoryService {
    @GET("ac/api/v2/inventory/product/category/list")
    suspend fun getSuggestion(
        @Query("name") query : String,
        @Query("lang") language: String = "id"
    ) : ApiResponse<CategorySuggestionModelResponse>
}