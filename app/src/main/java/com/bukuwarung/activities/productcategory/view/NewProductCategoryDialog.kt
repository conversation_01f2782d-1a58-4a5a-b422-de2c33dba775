package com.bukuwarung.activities.productcategory.view

import android.content.Context
import android.os.Bundle
import androidx.core.widget.doOnTextChanged
import com.bukuwarung.activities.productcategory.adapter.CategorySuggestionAdapter
import com.bukuwarung.analytics.moe.trackEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.NewProductCategoryDialogBinding
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.utils.recordException
import com.bukuwarung.utils.setupForSearch
import kotlinx.coroutines.CoroutineScope

class NewProductCategoryDialog(
    context: Context,
    private val entryPoint: String,
    private val previousName: String = "",
    private val callback: (String) -> Unit
) : BaseDialog(context) {
    private val binding by lazy {
        NewProductCategoryDialogBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    override fun getResId(): Int = 0

    private val suggestionAdapter = CategorySuggestionAdapter(context)
    private var selectedNameFromSuggestion = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        setCancellable(false)
        setUseFullWidth(false)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)

        super.onCreate(savedInstanceState)
        binding.apply {
            if (previousName.isNotBlank()) etCategory.setText(previousName, false)

            suggestionAdapter.setupWithAutoCompleteTextView(etCategory)

            etCategory.setOnItemClickListener { _, _, position, _ ->
                suggestionAdapter.notifySelectItemFromSuggestion(true)
                selectedNameFromSuggestion = suggestionAdapter.getItem(position)
            }

            etCategory.doOnTextChanged { text, _, _, _ ->
                suggestionAdapter.notifySelectItemFromSuggestion(selectedNameFromSuggestion == text)
            }

            btnSave.setOnClickListener {
                val name = etCategory.text.toString()
                if (!validateCategoryName(name)) return@setOnClickListener

                if (previousName.isBlank()) {
                    trackNewCategorySaveEvent(name)
                } else {
                    trackCategoryNameUpdateEvent(name)
                }

                callback(name)
                dismiss()
            }

            btnCancel.setOnClickListener {
                dismiss()
            }
        }
    }

    fun submitSuggestion(_suggestions: List<String>) {
        try {
            suggestionAdapter.submitSuggestion(_suggestions)
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    fun setQueryChangeCallback(scope: CoroutineScope, callback: (String) -> Unit) {
        binding.etCategory.setupForSearch(scope, enableImeAction = false) { query ->
            suggestionAdapter.updateQuery(query)
            callback(query)
        }
    }

    private fun validateCategoryName(name: String): Boolean {
        return name.trim().isNotBlank()
    }

    private fun trackNewCategorySaveEvent(actualName: String) {
        try {
            trackEvent(AnalyticsConst.SAVE_NEW_PRODUCT_CATEGORY) {
                addEntryPointProperty(entryPoint)
                val nameSource = if (actualName == selectedNameFromSuggestion) AnalyticsConst.SEARCH else AnalyticsConst.CREATE_MANUAL
                addProperty(AnalyticsConst.PRODUCT_CATEGORY_SOURCE to nameSource)
                addProperty(AnalyticsConst.PRODUCT_CATEGORY_NAME to actualName)
            }
        } catch (ex: Exception) {
            ex.recordException("Entry point hasn't set. Please invoke ProductCategoryFilterView.addEventEntryPoint(String)")
        }
    }

    private fun trackCategoryNameUpdateEvent(actualName: String) {
        try {
            trackEvent(AnalyticsConst.EDIT_PRODUCT_CATEGORY){
                addEntryPointProperty(entryPoint)
                val nameSource = if (actualName == selectedNameFromSuggestion) AnalyticsConst.SEARCH else AnalyticsConst.CREATE_MANUAL
                addProperty(AnalyticsConst.PRODUCT_CATEGORY_SOURCE to nameSource)
                addProperty(AnalyticsConst.OLD_PRODUCT_CATEGORY_NAME to previousName)
                addProperty(AnalyticsConst.NEW_PRODUCT_CATEGORY_NAME to actualName)
            }
        }catch (ex: Exception){
            ex.recordException("Entry point hasn't set. Please invoke ProductCategoryFilterView.addEventEntryPoint(String)")
        }
    }
}