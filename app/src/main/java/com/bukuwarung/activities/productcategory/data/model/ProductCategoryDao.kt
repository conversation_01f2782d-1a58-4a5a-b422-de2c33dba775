package com.bukuwarung.activities.productcategory.data.model

import androidx.lifecycle.LiveData
import androidx.room.*
import com.bukuwarung.database.entity.ProductCategoryEntity

@Dao
interface ProductCategoryDao {
    /* ProductCategory queries */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(entity: ProductCategoryEntity)

    @Update(entity = ProductCategoryEntity::class)
    suspend fun updateCategory(categoryEntity: ProductCategoryEntity)

    // for restoring data
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(entities: List<ProductCategoryEntity>)

    @Query("SELECT * FROM product_category WHERE category_id=:id AND deleted=0 AND book_id=:bookId")
    suspend fun getCategoryById(id: String, bookId: String): ProductCategoryEntity?

    @Query("SELECT * FROM product_category WHERE category_id=:id AND deleted=0")
    suspend fun getCategoryByIdFromAllBooks(id: String): ProductCategoryEntity?

    @Query("SELECT * FROM product_category WHERE LOWER(name)=LOWER(:name) AND deleted=0 AND book_id=:bookId")
    suspend fun getCategoryByName(name: String, bookId: String): ProductCategoryEntity?

    @Query("UPDATE product_category SET deleted=1 WHERE category_id=:categoryId AND book_id=:bookId")
    suspend fun deleteCategoryById(categoryId: String, bookId: String)

    @Query("SELECT * FROM product_category WHERE book_id=:bookId AND deleted=0")
    suspend fun getAllCategories(bookId: String): List<ProductCategoryEntity>

    @Query("SELECT * FROM product_category WHERE book_id=:bookId AND deleted=0")
    fun getAllCategoriesSync(bookId: String): LiveData<List<ProductCategoryEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProductCategoryCrossRef(crossRefs: List<ProductCategoryCrossRef>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertProductCategoryCrossRefBlocking(crossRefs: List<ProductCategoryCrossRef>)

    @Query("DELETE FROM product_category_cross_ref WHERE product_id IN (:productIds) AND category_id=:categoryId")
    suspend fun deleteMultipleCategoryProductCrossRef(productIds: List<String>, categoryId: String)

    @Query("DELETE FROM product_category_cross_ref WHERE product_id=:productId AND category_id IN (:categoryIds)")
    suspend fun deleteMultipleProductCategoryCrossRef(productId: String, categoryIds: List<String>)

    @Transaction
    @Query("SELECT * FROM products WHERE book_id=:bookId AND deleted=0")
    suspend fun getAllProductsWithCategories(bookId: String): List<ProductWithCategories>

    @Transaction
    @Query("SELECT * FROM products WHERE book_id=:bookId AND deleted=0 AND product_id=:productId")
    suspend fun getProductWithCategories(bookId: String, productId: String): ProductWithCategories?

    @Transaction
    @Query("SELECT * FROM product_category WHERE book_id=:bookId AND deleted=0 AND category_id=:categoryId")
    suspend fun getCategoryWithProducts(bookId: String, categoryId:String?) : CategoryWithProducts?

    @Transaction
    @Query("SELECT * FROM product_category WHERE book_id=:bookId AND deleted=0")
    fun getAllCategoriesWithProductsSync(bookId: String): LiveData<List<CategoryWithProducts>>

    @Transaction
    @Query("SELECT * FROM product_category WHERE book_id=:bookId AND deleted=0 AND category_id=:categoryId")
    fun getCategoryWithProductsSync(bookId: String, categoryId: String?): LiveData<CategoryWithProducts?>

    @Query("SELECT COUNT(*) FROM product_category WHERE book_id=:bookId AND deleted=0")
    suspend fun getCount(bookId: String?): Int

}