package com.bukuwarung.activities.productcategory.data

import com.bukuwarung.Application
import com.bukuwarung.activities.productcategory.data.model.ProductCategoryCrossRef
import com.bukuwarung.activities.productcategory.data.model.ProductCategoryService
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.datasync.migration.SyncTableEnum
import com.bukuwarung.session.AccountingDataSyncHelper.Companion.updateEntity
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.await
import com.bukuwarung.utils.getLanguageString
import com.bukuwarung.utils.recordException
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import javax.inject.Inject

class ProductCategoryRemote @Inject constructor(
    private val service: ProductCategoryService,
    private val sessionManager: SessionManager
) : ProductCategoryRemoteDataSource {
    private val categoryCollection = Firebase.firestore.collection(CATEGORY_COLLECTION)
    private val associationCollection = Firebase.firestore.collection(ASSOCIATION_COLLECTION)

    companion object {
        const val CATEGORY_COLLECTION = "app_product_category_store"
        const val ASSOCIATION_COLLECTION = "app_product_category_association_store"
    }

    override suspend fun getSuggestion(query: String): List<String> {
        return try {
            if (query.isBlank()) return emptyList()
            val language = sessionManager.getLanguageString()

            when (val response = service.getSuggestion(query, language)) {
                is ApiSuccessResponse -> response.body.data.map { it.subCategory }
                is ApiEmptyResponse -> emptyList()
                is ApiErrorResponse -> emptyList()
            }
        } catch (ex: Exception) {
            ex.recordException()
            emptyList()
        }
    }

    override suspend fun createCategory(category: ProductCategoryEntity) {
        try {
            categoryCollection.document(category.id).set(category)
            updateEntity(
                Application.getAppContext(),
                category,
                SyncTableEnum.PRODUCT_CATEGORY,
                category.id
            )
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override suspend fun updateCategory(category: ProductCategoryEntity) {
        try {
            categoryCollection.document(category.id).set(category)
            updateEntity(
                Application.getAppContext(),
                category,
                SyncTableEnum.PRODUCT_CATEGORY,
                category.id
            )
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override suspend fun deleteCategory(categoryId: String) {
        try {
            categoryCollection.document(categoryId).update(mapOf("deleted" to 1))
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override suspend fun deleteCategoryEntity(categoryEntity: ProductCategoryEntity?) {
        try {
            categoryCollection.document(categoryEntity!!.id).update(mapOf("deleted" to 1))
            updateEntity(
                Application.getAppContext(),
                categoryEntity,
                SyncTableEnum.PRODUCT_CATEGORY,
                categoryEntity.id
            )
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override suspend fun createProductCategoryAssociations(associations: List<ProductCategoryCrossRef>) {
        try {
            Firebase.firestore.runBatch { batch ->
                associations.forEach {
                    batch.set(associationCollection.document(formatAssociationIdForFirebase(it)), it)
                }
            }
            associations.forEach {
                updateEntity(
                    Application.getAppContext(),
                    it,
                    SyncTableEnum.PRODUCT_CATEGORY_ASSOCIATION,
                    it.id()
                )
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override suspend fun deleteMultipleCategoryProductAssociations(productIds: List<String>, categoryId: String) {
        try {
            Firebase.firestore.runBatch { batch ->
                productIds.forEach {
                    val docRef = associationCollection.document(formatAssociationIdForFirebase(it, categoryId))
                    batch.update(docRef, mapOf("deleted" to 1))
                }
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override suspend fun deleteMultipleProductCategoryAssociations(productId: String, categoryIds: List<String>) {
        try {
            Firebase.firestore.runBatch { batch ->
                categoryIds.forEach {
                    val docRef = associationCollection.document(formatAssociationIdForFirebase(productId, it))
                    batch.update(docRef, mapOf("deleted" to 1))
                }
            }
        } catch (ex: Exception) {
            ex.recordException()
        }
    }

    override suspend fun getRemoteCategories(bookId: String): List<ProductCategoryEntity> {
        return try {
            val snapshot = categoryCollection.whereEqualTo("bookId", bookId)
                .whereEqualTo("deleted", 0)
                .get()
                .await()

            val categories = snapshot?.documents?.map { it.toObject(ProductCategoryEntity::class.java) }
            categories?.filterNotNull().orEmpty()
        } catch (ex: Exception) {
            ex.recordException()
            emptyList()
        }

    }

    override suspend fun getRemoteProductCategoryAssociations(bookId: String): List<ProductCategoryCrossRef> {
        return try {
            val snapshot = associationCollection.whereEqualTo("bookId", bookId).get().await()
            val associations = snapshot?.documents?.map { it.toObject(ProductCategoryCrossRef::class.java) }
            associations?.filterNotNull().orEmpty()
        } catch (ex: Exception) {
            ex.recordException()
            emptyList()
        }
    }

    private fun formatAssociationIdForFirebase(crossRef: ProductCategoryCrossRef): String {
        return crossRef.productId + "::" + crossRef.categoryId
    }

    private fun formatAssociationIdForFirebase(productId: String, categoryId: String): String {
        return "$productId::$categoryId"
    }

}

interface ProductCategoryRemoteDataSource {
    suspend fun getSuggestion(query: String): List<String>

    suspend fun createCategory(category: ProductCategoryEntity)
    suspend fun updateCategory(category: ProductCategoryEntity)
    suspend fun deleteCategory(categoryId: String)
    suspend fun deleteCategoryEntity(category: ProductCategoryEntity?)

    suspend fun createProductCategoryAssociations(associations: List<ProductCategoryCrossRef>)
    suspend fun deleteMultipleProductCategoryAssociations(productId: String, categoryIds: List<String>)
    suspend fun deleteMultipleCategoryProductAssociations(productIds: List<String>, categoryId: String)

    suspend fun getRemoteCategories(bookId: String): List<ProductCategoryEntity>
    suspend fun getRemoteProductCategoryAssociations(bookId: String): List<ProductCategoryCrossRef>
}