package com.bukuwarung.activities.productcategory.view

import android.text.SpannableStringBuilder
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.databinding.ItemProductSelectionInfoBinding
import com.bukuwarung.databinding.ItemSelectProductBinding
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.boldText
import java.util.*

data class DynamicProductUIModel(
    val type : Int = 0,
    val productEntity: ProductEntity?,
){
    companion object{
        const val PRODUCT = 11
        const val INFO = 33
    }
}

class ProductSelectionInfoViewHolder(private val binding: ItemProductSelectionInfoBinding) : RecyclerView.ViewHolder(binding.root){
    fun bind(categoryName: String){
        binding.apply {
            val formattedString = "Silakan pilih barang untuk ditambahkan ke dalam kategori $categoryName"
            val spannableString = SpannableStringBuilder(formattedString).boldText(categoryName)
            tvCategoryInfo.text = spannableString
        }
    }
}

class ProductSelectionViewHolder(private val binding : ItemSelectProductBinding) : RecyclerView.ViewHolder(binding.root) {
    fun bind(productEntity: ProductEntity?, isChecked: Boolean, callback: (String, Boolean) -> Unit){
        productEntity ?: return

        binding.apply {
            tvProductName.text = productEntity.name.replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
            tvInitial.text = productEntity.name.first().uppercase()
            tvSellingPrice.text = Utility.formatAmount(productEntity.unitPrice)
            tvUnit.text = "/${productEntity.measurementName}"

            checbox.isChecked = isChecked
            root.setOnClickListener {
                checbox.toggle()
                callback(productEntity.productId, checbox.isChecked)
            }
        }
    }
}