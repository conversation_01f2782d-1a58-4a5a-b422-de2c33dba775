package com.bukuwarung.activities.productcategory.view

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatAutoCompleteTextView

class CustomAutoCompleteTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : AppCompatAutoCompleteTextView(context, attrs) {

    override fun enoughToFilter(): Boolean {
        return text.toString().length >= 3
    }

    override fun onFocusChanged(focused: <PERSON>olean, direction: Int, previouslyFocusedRect: Rect?) {
        super.onFocusChanged(focused, direction, previouslyFocusedRect)
        if (focused && filter != null) {
            performFiltering(text, 0)
        }
    }

    fun performManualFiltering() {
        if (enoughToFilter() && filter != null) {
            performFiltering(text, 0)
        }
    }
}