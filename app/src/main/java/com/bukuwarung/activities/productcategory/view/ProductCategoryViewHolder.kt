package com.bukuwarung.activities.productcategory.view

import android.content.Context
import android.view.View
import androidx.appcompat.widget.PopupMenu
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.productcategory.adapter.ProductCategoryAdapter
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.databinding.ItemProductCategoryBinding

class ProductCategoryViewHolder(private val context: Context, private val binding: ItemProductCategoryBinding) :
    RecyclerView.ViewHolder(binding.root) {
    fun bind(
        category: ProductCategoryEntity,
        isChecked: Boolean,
        actionCallback: (ProductCategoryAdapter.Action) -> Unit
    ) {
        binding.apply {
            ivMore.setOnClickListener {
                showMenu(it, category, actionCallback)
            }

            cbCategory.text = category.name
            cbCategory.isChecked = isChecked
            root.setOnClickListener {
                cbCategory.toggle()
                actionCallback(ProductCategoryAdapter.Action.Check(category.id, cbCategory.isChecked))
            }
        }
    }

    private fun showMenu(
        view: View,
        category: ProductCategoryEntity,
        actionCallback: (ProductCategoryAdapter.Action) -> Unit
    ) {
        PopupMenu(context, view).also {
            it.inflate(R.menu.edit_delete_menu)
            it.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.menu_edit -> actionCallback(ProductCategoryAdapter.Action.Update(category.id, category.name))
                    R.id.menu_delete -> actionCallback(ProductCategoryAdapter.Action.Delete(category.id))
                }

                true
            }
        }.show()
    }
}