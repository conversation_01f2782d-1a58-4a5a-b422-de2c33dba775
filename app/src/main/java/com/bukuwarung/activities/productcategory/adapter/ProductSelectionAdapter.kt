package com.bukuwarung.activities.productcategory.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.productcategory.view.DynamicProductUIModel
import com.bukuwarung.activities.productcategory.view.ProductSelectionInfoViewHolder
import com.bukuwarung.activities.productcategory.view.ProductSelectionViewHolder
import com.bukuwarung.database.entity.ProductEntity
import com.bukuwarung.databinding.ItemProductSelectionInfoBinding
import com.bukuwarung.databinding.ItemSelectProductBinding

class ProductSelectionAdapter(private val callback: (String, Boolean) -> Unit) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val products = mutableListOf<DynamicProductUIModel>()
    private val selectedIds = mutableListOf<String>()
    private var categoryName: String = ""

    fun setCategoryName(_categoryName: String) {
        categoryName = _categoryName
        notifyItemChanged(0)
    }

    fun setData(_products: List<ProductEntity>) {
        products.apply {
            clear()
            add(DynamicProductUIModel(DynamicProductUIModel.INFO, null))
            addAll(_products.map { DynamicProductUIModel(DynamicProductUIModel.PRODUCT, it) })
        }

        notifyDataSetChanged()
    }

    fun setSelectedIds(_ids: List<String>) {
        selectedIds.apply {
            clear()
            addAll(_ids)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return products[position].type
    }

    override fun getItemCount(): Int = products.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            DynamicProductUIModel.PRODUCT -> ProductSelectionViewHolder(
                ItemSelectProductBinding.inflate(
                    layoutInflater,
                    parent,
                    false
                )
            )
            DynamicProductUIModel.INFO -> ProductSelectionInfoViewHolder(
                ItemProductSelectionInfoBinding.inflate(
                    layoutInflater,
                    parent,
                    false
                )
            )
            else -> throw Throwable("Invalid view holder type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            DynamicProductUIModel.PRODUCT -> {
                val isSelected = selectedIds.contains(products[position].productEntity?.productId)
                (holder as ProductSelectionViewHolder).bind(products[position].productEntity, isSelected, callback)
            }
            DynamicProductUIModel.INFO -> (holder as ProductSelectionInfoViewHolder).bind(categoryName)
        }
    }
}