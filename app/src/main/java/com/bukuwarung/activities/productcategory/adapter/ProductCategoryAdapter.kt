package com.bukuwarung.activities.productcategory.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.activities.productcategory.view.ProductCategoryViewHolder
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.databinding.ItemProductCategoryBinding

class ProductCategoryAdapter(private val actionCallback: (Action) -> Unit) :
    RecyclerView.Adapter<ProductCategoryViewHolder>() {
    private val categories = mutableListOf<ProductCategoryEntity>()
    private var checkedIds = mutableListOf<String>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductCategoryViewHolder {
        val layout = ItemProductCategoryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ProductCategoryViewHolder(parent.context, layout)
    }

    override fun onBindViewHolder(holder: ProductCategoryViewHolder, position: Int) {
        val isChecked = checkedIds.contains(categories[position].id)
        holder.bind(categories[position], isChecked, actionCallback)
    }

    override fun getItemCount(): Int = categories.size


    fun submitCategories(_categories: List<ProductCategoryEntity>) {
        categories.apply {
            clear()
            addAll(_categories)
        }
        notifyDataSetChanged()
    }

    fun setCheckedItems(_checkedIds: List<String>) {
        checkedIds.apply {
            clear()
            addAll(_checkedIds)
        }
        notifyDataSetChanged()
    }

    sealed class Action {
        data class Check(val categoryId: String, val checked: Boolean) : Action()
        data class Delete(val categoryId: String) : Action()
        data class Update(val categoryId: String, val previousCategoryName: String) : Action()
    }
}