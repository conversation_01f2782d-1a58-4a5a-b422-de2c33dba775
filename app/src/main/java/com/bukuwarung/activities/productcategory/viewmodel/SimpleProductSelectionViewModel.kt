package com.bukuwarung.activities.productcategory.viewmodel

import androidx.lifecycle.*
import com.bukuwarung.activities.BaseViewModelEventState
import com.bukuwarung.activities.ViewModelEvent
import com.bukuwarung.activities.ViewModelState
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.utils.isNotNullOrBlank
import javax.inject.Inject

class SimpleProductSelectionViewModel @Inject constructor(
    productInventory: ProductInventory
) : BaseViewModelEventState<SimpleProductSelectionViewModel.State, SimpleProductSelectionViewModel.Event>() {
    private val searchQuery = MutableLiveData<String>("")
    val productsObservable = searchQuery.switchMap { query ->
        val products = productInventory.getAllProducts()
        if (query.isNotNullOrBlank()){
            products.map { it.filter { p -> p.name.contains(query, true) } }
        }else{
            products
        }
    }

    private val selectedProductIds = MutableLiveData<List<String>>()
    val selectedProductIdsList: LiveData<List<String>> = selectedProductIds

    sealed class State : ViewModelState {

    }

    sealed class Event : ViewModelEvent {
        data class OnSelectionUpdated(val productId: String, val isSelected: Boolean) : Event()
        data class SelectMultipleProducts(val productIds: List<String>) : Event()
        data class DoSearch(val query: String) : Event()
    }

    override fun onEventReceipt(event: Event) {
        when (event) {
            is Event.OnSelectionUpdated -> updateSelection(event.productId, event.isSelected)
            is Event.SelectMultipleProducts -> {
                event.productIds.forEach { productId ->
                    updateSelection(productId, true)
                }
            }
            is Event.DoSearch -> {
                searchQuery.value = event.query
            }
        }
    }

    private fun updateSelection(productId: String, isSelected: Boolean) {
        val currentSelections = mutableListOf<String>().apply {
            selectedProductIds.value?.let { addAll(it) }
        }

        if (isSelected) {
            currentSelections.add(productId)
        } else {
            currentSelections.remove(productId)
        }

        selectedProductIds.value = currentSelections
    }

    fun getSelectedProductIds(): List<String> {
        return selectedProductIds.value ?: emptyList()
    }
}