package com.bukuwarung.activities.productcategory.adapter

import android.content.Context
import android.os.Build
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Filter
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.activities.productcategory.view.CustomAutoCompleteTextView
import com.bukuwarung.utils.boldText

class CategorySuggestionAdapter(context: Context, private val layoutRes: Int = R.layout.custom_drop_down_item) :
    ArrayAdapter<String>(context, layoutRes) {
    private var suggestions = mutableListOf<String?>()
    private var filtered = listOf<String?>()
    private var query = ""
    private var actv : CustomAutoCompleteTextView? = null
    private var useSuggestedItem = false

    fun notifySelectItemFromSuggestion(_useSuggestedItem : Boolean) {
        useSuggestedItem = _useSuggestedItem
    }


    fun submitSuggestion(s: List<String>) {
        val temp = suggestions union s
        suggestions.clear()
        suggestions.addAll(temp)
        notifyDataSetChanged()

        // this will trigger the popup to be shown without changing the textfield content
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            actv?.refreshAutoCompleteResults()
        }else{
            actv?.performManualFiltering()
        }
    }

    fun updateQuery(newQuery: String){
        query = newQuery
        notifyDataSetChanged()

        if (query.isBlank()) actv?.dismissDropDown()
    }

    fun setupWithAutoCompleteTextView(_actv : CustomAutoCompleteTextView){
        _actv.setAdapter(this)
        actv = _actv
    }

    override fun getItem(position: Int): String {
        return filtered[position].toString()
    }

    override fun getCount(): Int = filtered.size

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        return createView(position, convertView, parent)
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
        return createView(position, convertView, parent)
    }

    private fun createView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view: View = convertView ?: LayoutInflater.from(context).inflate(layoutRes, parent, false)

        // to give bold effect for query
        val formattedText = SpannableStringBuilder(getItem(position)).boldText(query)
        view.findViewById<TextView>(R.id.tv_item).text = formattedText
        return view
    }

    override fun getFilter(): Filter {
        return CustomFilter()
    }

    inner class CustomFilter : Filter() {
        override fun performFiltering(constraint: CharSequence?): FilterResults {
            val result = if (!constraint.isNullOrBlank()) {
                suggestions.filter { it?.contains(constraint, true) ?: false }
            }else{
                suggestions
            }

            val filterResult = FilterResults()
            filterResult.count = result.size
            filterResult.values = result

            return filterResult
        }

        override fun publishResults(constraint: CharSequence?, results: FilterResults) {
            if (results.count > 0) {
                filtered = results.values as List<String?>
                notifyDataSetChanged()
            } else {
                notifyDataSetInvalidated()
            }
        }
    }
}