package com.bukuwarung.activities

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent


class UrlReceiver : BroadcastReceiver() {

    companion object {
        const val TAG = "UrlReceiver"
        const val ACTION_OPEN_URL = "com.bukuwarung.action.ACTION_OPEN_URL"
        const val URL = "url"
    }

    override fun onReceive(context: Context, intent: Intent?) {
        intent?.getStringExtra(URL)?.let {
            context.startActivity(
                WebviewActivity.createIntent(
                    context, it, ""
                ).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                }
            )
        }
    }
}