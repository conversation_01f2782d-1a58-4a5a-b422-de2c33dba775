package com.bukuwarung.activities.referral.leaderboard.models

import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.base_android.utils.SingleLiveEvent
import com.bukuwarung.referral.model.ReferralDataResponsePayload
import com.bukuwarung.referral.usecase.ReferralUseCase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class LeaderboardWebViewModel @Inject constructor(
    private val referralUseCase: ReferralUseCase
) : BaseViewModel() {

    sealed class Event {
        data class ReferralData(val data: ReferralDataResponsePayload?) : Event()
    }

    private val _event = SingleLiveEvent<Event>()
    val event: LiveData<Event> = _event

    fun getReferralData() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val result = referralUseCase.getReferralData()
                withContext(Dispatchers.Main) {
                    _event.value = Event.ReferralData(result)
                }
            }
        }
    }
}