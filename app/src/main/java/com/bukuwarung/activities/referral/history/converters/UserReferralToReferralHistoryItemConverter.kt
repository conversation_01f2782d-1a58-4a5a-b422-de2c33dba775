package com.bukuwarung.activities.referral.history.converters

import com.bukuwarung.activities.referral.history.models.ReferralHistoryItem
import com.bukuwarung.database.dto.UserReferralModel
import com.bukuwarung.database.entity.referral.ReferralLink
import com.bukuwarung.utils.Converter

internal class UserReferralToReferralHistoryItemConverter: Converter<UserReferralModel, List<ReferralHistoryItem>>() {

    override fun convert(input: UserReferralModel?): List<ReferralHistoryItem> {
        input ?: return listOf()

        val res = mutableListOf<ReferralHistoryItem>()

        insertDataToList(res, input.received)
        insertDataToList(res, input.sent)

        return res
    }

    private fun insertDataToList(input: MutableList<ReferralHistoryItem>, data: List<ReferralLink>?) {
        data ?: return
        data.map {
            val list = mutableListOf<ReferralHistoryItem>()
            it.receiver.forEach { receiver ->
                list.add(
                    ReferralHistoryItem(
                            id = receiver.userId,
                            name = receiver.userId,
                            point = receiver.earnedPoints
                    )
                )
            }
        }
    }

}