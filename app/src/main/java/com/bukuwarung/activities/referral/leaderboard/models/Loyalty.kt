package com.bukuwarung.activities.referral.leaderboard.models


import androidx.annotation.DrawableRes
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/*sealed class LoyaltyTierInfo(val description: String, @DrawableRes val icon: Int) {
    data class Bronze(val desc: String, @DrawableRes val icn: Int) : LoyaltyTierInfo(desc, icn)
    data class Silver(val desc: String, @DrawableRes val icn: Int) : LoyaltyTierInfo(desc, icn)
    data class Gold(val desc: String, @DrawableRes val icn: Int) : LoyaltyTierInfo(desc, icn)
    data class Platinum(val desc: String, @DrawableRes val icn: Int) : LoyaltyTierInfo(desc, icn)
    data class Diamond(val desc: String, @DrawableRes val icn: Int) : LoyaltyTierInfo(desc, icn)
}*/

data class LoyaltyTierInfo(val description: String, @DrawableRes val icon: Int)

@Keep
data class LoyaltyResponse(
    @SerializedName("loyaltyAccount") val loyaltyAccount: LoyaltyAccount,
    @SerializedName("loyaltyAccountTier") val loyaltyAccountTier: LoyaltyAccountTier,
    @SerializedName("loyaltyTiers") val loyaltyTiers: List<LoyaltyTier>,
    @SerializedName("isWhitelisted") val isWhitelisted: Boolean
)

@Keep
data class LoyaltyAccount(
    @SerializedName("activePoints") val activePoints: Int = 0,
    @SerializedName("deleted") val deleted: Boolean = false,
    @SerializedName("expiredPoints") val expiredPoints: Int = 0,
    @SerializedName("id") val id: Int = 0,
    @SerializedName("levelScore") val levelScore: Int = 0,
    @SerializedName("tierId") val tierId: String = "",
    @SerializedName("usedPoints") val usedPoints: Int = 0,
    @SerializedName("userId") val userId: String = ""
)

@Keep
data class LoyaltySummary(
    @SerializedName("activePoints") val activePoints: Double = 0.0,
    @SerializedName("tierName") val tierName: String = "",
    @SerializedName("description") val description: String? = "",
    @SerializedName("isWhitelisted") val isWhitelisted: Boolean
)

@Keep
data class LoyaltyAccountTier(
    @SerializedName("loyaltyTier") val loyaltyTier : LoyaltyTier,
    @SerializedName("loyaltyTierBenefits") val loyaltyTierBenefits: List<LoyaltyTierBenefit>
)

@Keep
data class LoyaltyTier(
    @SerializedName("additionalDetails") val additionalDetails: String,
    @SerializedName("deleted") val deleted: Boolean,
    @SerializedName("description") val description: String,
    @SerializedName("id") val id: Int,
    @SerializedName("lastModifiedAt") val lastModifiedAt: String,
    @SerializedName("maxLevelScore") val maxLevelScore: Int,
    @SerializedName("minLevelScore") val minLevelScore: Int,
    @SerializedName("pointsMultiplier") val pointsMultiplier: Double,
    @SerializedName("tierId") val tierId: String,
    @SerializedName("tierName") val tierName: String
)

@Keep
data class LoyaltyTierBenefit(
    @SerializedName("allowedTxnTypes") val allowedTxnTypes: List<String>,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("discountedPrice") val discountedPrice: Int,
    @SerializedName("durationInMonths") val durationInMonths: Int,
    @SerializedName("gameRuleName") val gameRuleName: String,
    @SerializedName("id") val id: String,
    @SerializedName("maxDiscount") val maxDiscount: Int,
    @SerializedName("minTxnAmount") val minTxnAmount: Int,
    @SerializedName("price") val price: Int,
    @SerializedName("quota") val quota: Int,
    @SerializedName("tierId") val tierId: String
)