package com.bukuwarung.activities.referral.main_referral

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.Intent.EXTRA_CHOSEN_COMPONENT
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import java.lang.Exception

/**
 * Class to get what apps would be chosen to share referral link
 */
internal class ReferralSharingReceiver: BroadcastReceiver() {

    @RequiresApi(Build.VERSION_CODES.LOLLIPOP_MR1)
    override fun onReceive(context: Context?, intent: Intent?) {
        try {
            val pickedApps = intent?.extras?.get(EXTRA_CHOSEN_COMPONENT) as ComponentName
            val propBuilder = PropBuilder()
            propBuilder.put("shared_via", pickedApps?.packageName ?: "-")
            AppAnalytics.trackEvent("referral_shared_with_contact",propBuilder);
        } catch (ex: Exception) {
            Log.e("ReferralSharingReceiver", "Exception", ex)
        }
    }

}