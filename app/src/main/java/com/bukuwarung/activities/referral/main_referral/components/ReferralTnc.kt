package com.bukuwarung.activities.referral.main_referral.components

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.bukuwarung.R
import kotlinx.android.synthetic.main.referral_step_item.view.*

class ReferralTnc: LinearLayout {

    var index: Int = 0
        set(value) {
            field = value
            indexText?.text = value.toString()
        }

    var text: String? = resources.getString(R.string.default_placeholder)
        set(value) {
            field = value
            contentText?.text = value.toString()
        }

    constructor(context: Context) : this(context, null) {
        LayoutInflater.from(context)
                .inflate(R.layout.referral_tnc_item, this, true)
    }

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

}