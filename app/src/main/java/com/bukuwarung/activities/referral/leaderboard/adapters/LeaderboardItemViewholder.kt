package com.bukuwarung.activities.referral.leaderboard.adapters

import android.graphics.Color
import android.view.View
import kotlinx.android.synthetic.main.leaderboard_item.view.*
import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardItem
import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardItemType
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.utils.getFirstLetter
import com.bukuwarung.utils.setTextOrDefault
import com.bumptech.glide.Glide

internal class LeaderboardItemViewholder(view: View): DefaultRVViewHolder<LeaderboardItem>(view) {

    private val mainContainer = view.mainContainer
    private val txtRank = view.text_rank
    private val txtFirstLetter = view.firstLetter
    private val txtName = view.text_name
    private val txtPoint = view.text_point
    private val trophyImage = view.trophyImage

    override fun bind(item: LeaderboardItem) {
        setUpRankLayout(item.rank, item.name, item.point)
        if (item.type is LeaderboardItemType.Top) setupLeaderImage(item.type.badgeResId)
        if (item.isUsers)
            mainContainer.setBackgroundColor(Color.parseColor("#F1F1F1"))
        else
            mainContainer.setBackgroundColor(Color.WHITE)
    }

    private fun setUpRankLayout(rank: Int?, name: String?, point: Int?) {
        txtRank.setTextOrDefault(rank?.toString())
        txtFirstLetter.setTextOrDefault(name.getFirstLetter())
        txtName.setTextOrDefault(name)
        txtPoint.setTextOrDefault(point?.toString())
    }

    private fun setupLeaderImage(resId: Int) {
        trophyImage.visibility = View.VISIBLE

        Glide
                .with(itemView)
                .load(resId)
                .into(trophyImage)
    }

    override fun free() {
        Glide
                .with(itemView)
                .clear(trophyImage)
    }


}