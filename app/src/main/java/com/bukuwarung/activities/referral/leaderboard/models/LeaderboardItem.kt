package com.bukuwarung.activities.referral.leaderboard.models

import com.bukuwarung.baseui.DefaultRVAdapterModel

internal data class LeaderboardItem(
        val rank: Int,
        val type: LeaderboardItemType = LeaderboardItemType.Normal,
        val name: String,
        val point: Int,
        val isUsers: Boolean = false
): DefaultRVAdapterModel() {

    override fun getId(): Int = rank

}

internal sealed class LeaderboardItemType {
    object Normal: LeaderboardItemType()
    data class Top(val badgeResId: Int): LeaderboardItemType()
}