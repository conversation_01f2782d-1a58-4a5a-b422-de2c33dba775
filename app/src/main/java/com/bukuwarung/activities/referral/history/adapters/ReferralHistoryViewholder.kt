package com.bukuwarung.activities.referral.history.adapters

import android.view.View
import com.bukuwarung.R
import com.bukuwarung.activities.referral.history.models.ReferralHistoryItem
import kotlinx.android.synthetic.main.leaderboard_history_item.view.*
import com.bukuwarung.baseui.DefaultRVViewHolder
import com.bukuwarung.utils.getFirstLetter
import com.bukuwarung.utils.setTextOrDefault

internal class ReferralHistoryViewholder(val view: View): DefaultRVViewHolder<ReferralHistoryItem>(view) {

    private val txtFirstLetter = view.firstLetter
    private val txtName = view.text_name
    private val txtPoint = view.text_point

    override fun bind(item: ReferralHistoryItem) {
        setUpRankLayout(item.name, item.point)
    }

    private fun setUpRankLayout(name: String?, point: Int?) {
        txtFirstLetter.setTextOrDefault(name.getFirstLetter())
        txtName.setTextOrDefault(name)
        txtPoint.text = view.resources.getString(R.string.points_history_placeholder, point?.toString() ?: "-")
    }

    override fun free() {
        // DO NOTHING
    }


}