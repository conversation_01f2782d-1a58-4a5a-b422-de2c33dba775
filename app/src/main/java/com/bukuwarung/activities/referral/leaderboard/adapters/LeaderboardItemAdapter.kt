package com.bukuwarung.activities.referral.leaderboard.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardItem
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder

internal class LeaderboardItemAdapter: DefaultRVAdapter<LeaderboardItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<LeaderboardItem> {
        return LeaderboardItemViewholder(
                LayoutInflater
                        .from(parent.context)
                        .inflate(
                                R.layout.leaderboard_item,
                                parent,
                                false
                        )
        )
    }
}