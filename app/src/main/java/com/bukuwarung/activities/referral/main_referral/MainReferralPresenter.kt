package com.bukuwarung.activities.referral.main_referral

import android.util.Log
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.controllers.firebase.FirebaseDynamicLinksController
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.ReferralRepository
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers

internal class MainReferralPresenter(
        private val referralRepository: ReferralRepository,
        private val dynamicLinksController: FirebaseDynamicLinksController,
        private val contract: MainReferralContract
) {


    private var compositeDisposable: CompositeDisposable = CompositeDisposable()

    lateinit var flowableEmitter: FlowableEmitter<MainReferralActivityViewModel>
    var currentData: MainReferralActivityViewModel = MainReferralActivityViewModel()
    var data = Flowable.create<MainReferralActivityViewModel>(
            {
                emitter ->
                run {
                    flowableEmitter = emitter
                    initData()
                }
            }, BackpressureStrategy.BUFFER
    ).doOnNext {
        currentData = it
    }

    fun initData() {
        referralRepository.getUserRankPoints {
            val value = if (it == null) {
                currentData.copy(
                        state = MainReferralState.LOADED
                )
            } else {
                currentData.copy(
                        rank = it.rank,
                        name = it.name,
                        point = it.points.toInt(),
                        state = MainReferralState.LOADED
                )
            }
            flowableEmitter.onNext(value)
        }

        getReferralCode()
    }

    private fun getReferralCode() {
        referralRepository.getUserReferralCode {
            val value = currentData.copy(
                    referralCode = it
            )
            flowableEmitter.onNext(value)
        }
    }

    internal fun getReferralCodeWithCallback(callback: (String)->Unit) {
        referralRepository.getUserReferralCode {
            callback(it)
        }
    }

    fun openLeaderboard() {
        AppAnalytics.trackEvent("referral_leaderboard_clicked")
        contract.openLeaderboardActivity()
    }

    fun shareReferralCode(currentBook: BookEntity, referralCode: String) {
        try {
            val dialog = contract.openLoadingDialog()
            AppAnalytics.trackEvent("referral_share_link_clicked")
            val disposable = dynamicLinksController
                    .generateReferralDynamicLink(currentBook, referralCode)
                    .map {
                        val deeplink = it.toString()
                        val referralLink = referralRepository.getShareableLink(deeplink, referralCode)
                        referralRepository.enableSharedReferralLink(referralLink,
                                currentBook, referralCode)
                        referralLink
                    }
                    .doAfterSuccess {
                        getReferralCode()
                    }
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe({
                        val referralLink = it.sharedlink
                        Log.d(TAG, "Got Referral Link $referralLink")

                        val propBuilder = AppAnalytics.PropBuilder()
                        propBuilder.put("referral_link", referralLink)
                        AppAnalytics.trackEvent("referral_share_link_success")
                        contract.openShareChooser(referralLink)

                        dialog.dismiss()
                    }, {
                        dialog.dismiss()

                        if (it is FirebaseDynamicLinksController.BusinessOwnerNameEmptyException) {
                            AppAnalytics.trackEvent("referral_share_link_error_empty_data")
                            contract.alertOwnerNameNull()
                        } else {
                            AppAnalytics.trackEvent("referral_share_link_error_unknown")
                            contract.alertShareError()
                        }
                    })
            compositeDisposable.add(disposable)
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    fun destroy() {
        try {
            if (!compositeDisposable.isDisposed)
                compositeDisposable.clear()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    companion object {

        const val TAG = "MainReferralPresenter"

    }

}