package com.bukuwarung.activities.referral.main_referral.dialogs

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BasePromptDialog

class NullProfileReferralDialog(
        context: Context,
        private val contentRes: Int = R.string.null_profile_referral_content,
        private val hideBtn: Boolean = false,
        onPromptClicked: ((Boolean) -> Unit)
): BasePromptDialog(context, onPromptClicked, revertOption = true) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setTitle(context.resources.getString(R.string.null_profile_referral_title))
        setContent(context.resources.getString(contentRes))
        setPositiveText(context.resources.getString(R.string.setup_profile))
        setNegativeText(context.resources.getString(R.string.cancel))

        if (hideBtn) hideBtn()
    }

}
