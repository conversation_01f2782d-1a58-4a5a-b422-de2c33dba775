package com.bukuwarung.activities.referral.history.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bukuwarung.R
import com.bukuwarung.activities.referral.history.models.ReferralHistoryItem
import com.bukuwarung.baseui.DefaultRVAdapter
import com.bukuwarung.baseui.DefaultRVViewHolder

internal class ReferralHistoryAdapter: DefaultRVAdapter<ReferralHistoryItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefaultRVViewHolder<ReferralHistoryItem> {
        return ReferralHistoryViewholder(
                LayoutInflater
                        .from(parent.context)
                        .inflate(
                                R.layout.leaderboard_history_item,
                                parent,
                                false
                        )
        )
    }
}