package com.bukuwarung.activities.referral.main_referral

import android.app.Activity
import android.app.PendingIntent
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import com.bukuwarung.R
import com.bukuwarung.activities.notification.discover.InfoDiscoveryAdapter
import com.bukuwarung.activities.profile.update.BusinessProfileFormActivity
import com.bukuwarung.activities.referral.leaderboard.LeaderboardActivity
import com.bukuwarung.activities.referral.main_referral.components.ReferralPrize
import com.bukuwarung.activities.referral.main_referral.components.ReferralStep
import com.bukuwarung.activities.referral.main_referral.components.ReferralTnc
import com.bukuwarung.activities.referral.main_referral.dialogs.NullProfileReferralDialog
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.superclasses.MixSwipeAnim
import com.bukuwarung.controllers.firebase.FirebaseDynamicLinksController
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.loading.LoadingDialog
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.ShareIntentBuilder
import com.bukuwarung.utils.getFirstLetter
import com.bukuwarung.utils.setTextOrDefault
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import kotlinx.android.synthetic.main.activity_main_referral.*

class MainReferralActivity : AppActivity(MixSwipeAnim()), MainReferralContract {

    private lateinit var dynamicLinksController: FirebaseDynamicLinksController

    private lateinit var referralRepository: ReferralRepository
    private lateinit var presenter: MainReferralPresenter

    private var compositeDisposable = CompositeDisposable()

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)

        setupView()
        setupDynamicLinksController()
        setupRepository()
    }

    override fun onDestroy() {
        super.onDestroy()

        compositeDisposable.dispose()
        presenter.destroy()
    }

    private fun setupDynamicLinksController() {
        // TODO: MOVE TO DI
        dynamicLinksController = FirebaseDynamicLinksController()
    }

    private fun setupRepository() {
        referralRepository = ReferralRepository.getInstance()
        presenter = MainReferralPresenter(referralRepository, dynamicLinksController, this)

        compositeDisposable.add(
            presenter
                .data
                .subscribeOn(Schedulers.io())
                .subscribe(
                    {
                        setupViewsBasedOnModel(it)
                    },
                    {
                        Log.e(TAG_ACTIVITY, "Error", it)
                    }
                )
        )
    }

    private fun setupView() {
        setContentView(R.layout.activity_main_referral)

        backBtn.setOnClickListener { onBackPressed() }
        setupShare()
        setupBanners()
        setupStep()
        setupPrize()
        setupTnc()
    }

    private fun setupShare() {
        try {
            val sharingActive = AppConfigManager.getInstance().useReferralSharing()
            if (sharingActive) {
                referralShareContainer.visibility = View.VISIBLE
            } else {
                referralShareContainer.visibility = View.GONE
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    private fun setupStep() {
        val titleText = AppConfigManager.getInstance().referralTitle
        toolbar_title.text = titleText

        val steps: List<String> = AppConfigManager.getInstance().referralSteps
            ?: resources.getStringArray(R.array.main_referral_tutorials).toList()

        steps.forEachIndexed { index, s ->
            val view = ReferralStep(this)
            view.index = index + 1
            view.text = s

            referralStepContainer.addView(view)
        }
    }

    private fun setupPrize() {
        val prizes: List<String> = AppConfigManager.getInstance().referralPrizes
            ?: resources.getStringArray(R.array.main_referral_prizes).toList()

        prizes.forEachIndexed { index, s ->
            val view = ReferralPrize(this)
            view.index = index + 1
            view.contentText = s

            prizeContainer.addView(view)
        }
    }

    private fun setupTnc() {
        val tncs: List<String> = AppConfigManager.getInstance().referralTncs
            ?: resources.getStringArray(R.array.main_referral_tncs).toList()

        tncs.forEachIndexed { index, s ->
            val view = ReferralTnc(this)
            view.index = index + 1
            view.text = s

            tncContainer.addView(view)
        }
    }

    private fun setupBanners() {
        try {
            InfoDiscoveryAdapter.LoadImage(mainBanner, AppConfigManager.getInstance().referralBanner, false).execute()
            InfoDiscoveryAdapter.LoadImage(image_prize, AppConfigManager.getInstance().referralPrizeBanner, false).execute()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    private fun setupViewsBasedOnModel(viewModel: MainReferralActivityViewModel) {
        setUpRankLayout(viewModel)
        setupReferralCode(viewModel)
    }

    private fun setUpRankLayout(viewModel: MainReferralActivityViewModel) {
        if (viewModel.state != MainReferralState.LOADING) {
            myRankLoading.visibility = View.GONE

            if (viewModel.rank != null && viewModel.point != null && viewModel.name != null) {
                myRankContainer.visibility = View.VISIBLE
                rankTV.setTextOrDefault(viewModel.rank?.toString())
                firstLetter.setTextOrDefault(viewModel.name.getFirstLetter())
                nameTV.setTextOrDefault(viewModel.name)
                pointTV.setTextOrDefault(viewModel.point?.toString())

                leaderboardContainer.setOnClickListener { presenter.openLeaderboard() }
            } else {
                myRankEmpty.visibility = View.VISIBLE
                //hide rank layout if rank is null
                myRankContainer.visibility = View.GONE
                emptyLeaderboardContainer.setOnClickListener { presenter.openLeaderboard() }
            }
        }
    }

    override fun openLeaderboardActivity() {
        val intent = LeaderboardActivity.getIntent(this)
        startActivity(intent)
    }

    override fun openLoadingDialog(): BaseDialog {
        val dialog = LoadingDialog(this)
        dialog.show()
        return dialog
    }

    private fun setupReferralCode(viewModel: MainReferralActivityViewModel) {
        val referralCode = viewModel.referralCode ?: return
        referralCodeTV.text = referralCode
        shareReferralButton.setOnClickListener { shareReferralCode(referralCode) }
    }

    private fun shareReferralCode(referralCode: String) {
        try {
            presenter.shareReferralCode(currentBook, referralCode)
        } catch (ex: Exception) {
            NotificationUtils.alertError("Terjadi kesalahan, coba lagi tindakan ini dengan Buku/Bisnis lain")
        }
    }

    override fun openShareChooser(referralLink: String) {
        val referralMessage = AppConfigManager.getInstance()
            ?.referralMessage
            ?.replace("[]", referralLink) ?: "-"

        val receiverIntent = Intent(this, ReferralSharingReceiver::class.java)
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT)
        }
        val intent = ShareIntentBuilder()
            .setMessage(referralMessage)
            .setPendingIntent(pendingIntent)
            .build()
        startActivity(intent)
    }

    override fun alertShareError() {
        NotificationUtils.alertToast(resources.getString(R.string.sorry_share_referral))
    }

    override fun alertOwnerNameNull() {

        val dialog = NullProfileReferralDialog(
            this
        ) { promptResult ->
            run {
                if (promptResult) {
                    val intent = BusinessProfileFormActivity.getIntent(this)
                    startActivityForResult(intent, ACTIVITY_FILL_PROFILE_ID)
                }
            }
        }

        dialog.show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            ACTIVITY_FILL_PROFILE_ID -> {
                if (resultCode == Activity.RESULT_OK) {
                    presenter.getReferralCodeWithCallback(::shareReferralCode)
                }
            }
        }
    }

    companion object {

        const val ACTIVITY_FILL_PROFILE_ID = 11

        const val TAG_ACTIVITY = "MainReferralActivity"


        fun getIntent(origin: Activity): Intent = Intent(origin, MainReferralActivity::class.java)

    }

}