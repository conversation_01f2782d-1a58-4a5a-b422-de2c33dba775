package com.bukuwarung.activities.referral.share

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.utils.isNotNullOrEmpty

/**
 * Class to get what apps would be chosen to upload referral image
 */
internal class ReferralUploadReceiver : BroadcastReceiver() {

    @RequiresApi(Build.VERSION_CODES.LOLLIPOP_MR1)
    override fun onReceive(context: Context?, intent: Intent?) {
        try {
            val pickedApps = intent?.getParcelableExtra<ComponentName>(Intent.EXTRA_CHOSEN_COMPONENT)
            val entryPoint = intent?.extras?.get(AnalyticsConst.ENTRY_POINT)
            val from = intent?.extras?.getString("from")
            val propBuilder = AppAnalytics.PropBuilder()

            if (from.isNotNullOrEmpty()) {
                propBuilder.put(AnalyticsConst.ENTRY_POINT2, from)
            }
            propBuilder.put("uploaded_via", pickedApps?.packageName ?: "-")
            propBuilder.put(AnalyticsConst.ENTRY_POINT, entryPoint ?: "-")
            AppAnalytics.trackEvent("referral_upload_success", propBuilder);
        } catch (ex: Exception) {
            Log.e("ReferralUploadReceiver", "Exception", ex)
        }
    }

}