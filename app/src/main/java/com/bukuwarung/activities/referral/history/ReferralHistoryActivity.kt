package com.bukuwarung.activities.referral.history

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.referral.history.adapters.ReferralHistoryAdapter
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.activities.superclasses.VerticalFadeSwipeAnim
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.utils.asVisibility
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import kotlinx.android.synthetic.main.activity_referral_history.*

class ReferralHistoryActivity: AppActivity(VerticalFadeSwipeAnim()) {

    private lateinit var referralHistoryAdapter: ReferralHistoryAdapter

    private lateinit var referralRepository: ReferralRepository
    private lateinit var presenter: ReferralHistoryPresenter

    private val compositeDisposable = CompositeDisposable()

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)

        setupView()
        setupRepository()
    }

    override fun onDestroy() {
        super.onDestroy()

        compositeDisposable.dispose()
    }

    private fun setupRepository() {
        referralRepository = ReferralRepository.getInstance()
        presenter = ReferralHistoryPresenter(referralRepository)

        presenter.initData()
        compositeDisposable.add(
                presenter
                .data
                .subscribeOn(Schedulers.io())
                .subscribe(
                        {
                            setupViewBasedOnViewModel(it)
                        },
                        {
                            Log.e("ReferralHistoryActivity", "Error", it)
                        }
                )
        )
    }

    private fun setupView() {
        setContentView(R.layout.activity_referral_history)

        backBtn.setOnClickListener { onBackPressed() }
    }

    private fun setupViewBasedOnViewModel(viewModel: ReferralHistoryViewModel) {
        setupRV(viewModel)
    }

    private fun setupRV(viewModel: ReferralHistoryViewModel) {
        val items = viewModel.items ?: return

        referralHistoryAdapter = ReferralHistoryAdapter()
        referralHistoryAdapter.results = items
        historyRv.adapter = referralHistoryAdapter
        historyRv.layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)

        rvLoading.visibility = View.GONE
        historyRv.visibility = items.isNotEmpty().asVisibility()
        empty_view.visibility = items.isEmpty().asVisibility()
    }

    companion object {

        fun getIntent(origin: Activity): Intent = Intent(origin, ReferralHistoryActivity::class.java)

    }

}