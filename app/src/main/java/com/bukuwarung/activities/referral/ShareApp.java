package com.bukuwarung.activities.referral;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.appcompat.widget.Toolbar;

import com.bukuwarung.Application;
import com.bukuwarung.R;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.activities.superclasses.DefaultAnim;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.utils.NotificationUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
//import com.moe.pushlibrary.MoEHelper;

public class ShareApp extends AppActivity {

    public ShareApp() {
        super(new DefaultAnim());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_share_app);
        Toolbar mToolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        findViewById(R.id.share_app_button).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                shareApp();
            }
        });

    }

    private void shareApp() {
        try {
            StringBuilder sb = new StringBuilder();
            if (sessionManager.getAppLanguage() == 1) {
                sb.append("Hi,");
                sb.append("\nBukuWarung is a fast, simple and secure application that I use to manage my business and send payment payment reminders.");
                sb.append("\nGet it for free at http://bukuwarung.com/app");
            } else {
                sb.append("Bapak/Ibu,Saya pakai aplikasi pembukuan BukuWarung untuk:\n" +
                        "\n" +
                        "✅ Catat Utang Piutang" +
                        "\n" +
                        "✅ Catat Penjualan" +
                        "\n" +
                        "✅ Kirim dan Terima Pembayaran Tanpa Biaya\n" +
                        "\n" +
                        "Ukuran aplikasinya kecil banget, nggak bikin penuh memori. Coba deh, Gratis! \uD83D\uDC49 https://bukuwarung.id/unduh-gratis");
            }

            Intent share = new Intent(Intent.ACTION_SEND);
            share.setType("text/plain");
            share.addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET);
            share.putExtra(Intent.EXTRA_SUBJECT, "Share BukuWarung..");
            share.putExtra(Intent.EXTRA_TEXT, sb.toString());
            startActivity(Intent.createChooser(share, "Choose an App!"));
        } catch (ActivityNotFoundException e) {
            NotificationUtils.alertToast("WhatApp Not Installed");
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }
}
