package com.bukuwarung.activities.referral.leaderboard

import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardItem

internal data class LeaderboardViewModel(
        var rank: Int? = null,
        var point: Int? = null,
        var items: List<LeaderboardItem>? = listOf(),
        var state: LeaderboardViewState = LeaderboardViewState.LOADING
)

internal enum class LeaderboardViewState {
    LOADING,
    LOADED
}