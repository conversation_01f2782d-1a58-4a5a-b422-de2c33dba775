package com.bukuwarung.activities.referral.leaderboard.converters

import com.bukuwarung.R
import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardItem
import com.bukuwarung.activities.referral.leaderboard.models.LeaderboardItemType
import com.bukuwarung.database.entity.referral.UserRank
import com.bukuwarung.utils.Converter

internal class UserRankToLeaderboardItemsConverter: Converter<List<UserRank>, List<LeaderboardItem>>() {

    override fun convert(input: List<UserRank>?): List<LeaderboardItem> {
        if (input.isNullOrEmpty()) return listOf()

        return input
                .map {
                    LeaderboardItem(
                            rank = it.rank,
                            point = it.points.toInt(),
                            name = it.name,
                            type = convertRankToType(it.rank)
                    )
                }
    }

    fun convertWithAdditionalData(input: List<UserRank>?, currentUserRank: Int?): List<LeaderboardItem> {
        if (input.isNullOrEmpty()) return listOf()

        return input
                .map {
                    LeaderboardItem(
                            rank = it.rank,
                            point = it.points.toInt(),
                            name = it.name,
                            type = convertRankToType(it.rank),
                            isUsers = it.rank == currentUserRank
                    )
                }
    }

    private fun convertRankToType(rank: Int): LeaderboardItemType {
        return when (rank) {
            1 -> {
                LeaderboardItemType.Top(R.drawable.ic_trophy_first)
            }
            2 -> {
                LeaderboardItemType.Top(R.drawable.ic_trophy_second)
            }
            3 -> {
                LeaderboardItemType.Top(R.drawable.ic_trophy_third)
            }
            else -> {
                LeaderboardItemType.Normal
            }
        }
    }

}