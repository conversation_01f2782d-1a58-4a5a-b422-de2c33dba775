package com.bukuwarung.activities.referral.payment_referral

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlinx.android.synthetic.main.bottomsheet_referral_info.view.*
import com.bukuwarung.activities.experiments.CustomWebviewActivity



class TnCBottomSheet: BottomSheetDialogFragment() {

    companion object {
        const val TAG = "TnCBottomSheet"
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.bottomsheet_referral_info, container, false)
        val tncContent: TextView = view.findViewById(R.id.tv_bullet_smartphone)
        val tncHeader: TextView = view.findViewById(R.id.tv_header)

        val referralTncBullet: String = RemoteConfigUtils.getReferralTnCBulletPoint()
        val referralTncHeader: String = RemoteConfigUtils.getReferralTnCHeader()
        if(!Utility.isBlank(referralTncBullet)){
            tncContent.text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                Html.fromHtml(referralTncBullet, Html.FROM_HTML_MODE_COMPACT)
            } else {
                Html.fromHtml(referralTncBullet)
            }
        }
        tncContent.movementMethod = LinkMovementMethod.getInstance()
        if(!Utility.isBlank(referralTncHeader)){
            tncHeader.text = referralTncHeader
        }
        view.btn_tnc_referral.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_LEARN_MORE_PAGE)

            val intent = CustomWebviewActivity.createIntent(context, RemoteConfigUtils.getTncWebviewUrl.getUrl(), context?.getString(R.string.referral_tnc_title), false, "tnc", "referral")
            context?.startActivity(intent);
        }

        return view
    }

    override fun getTheme(): Int {
        return R.style.BottomSheetDialogTheme_RoundCorner
    }
}