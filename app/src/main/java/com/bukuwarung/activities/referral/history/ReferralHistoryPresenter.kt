package com.bukuwarung.activities.referral.history

import com.bukuwarung.activities.referral.history.converters.UserReferralToReferralHistoryItemConverter
import com.bukuwarung.database.repository.ReferralRepository
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter

internal class ReferralHistoryPresenter(
        private val referralRepository: ReferralRepository
) {

    val converter: UserReferralToReferralHistoryItemConverter = UserReferralToReferralHistoryItemConverter()

    lateinit var flowableEmitter: FlowableEmitter<ReferralHistoryViewModel>
    var currentData: ReferralHistoryViewModel = ReferralHistoryViewModel()
    var data = Flowable.create<ReferralHistoryViewModel>(
            {
                emitter -> flowableEmitter = emitter
            }, BackpressureStrategy.BUFFER
    ).doOnNext {
        currentData = it
    }

    fun initData() {
        referralRepository.getUserReferralDataWithSentLinks {
            val data = currentData
                    .copy(
                            items = converter.convert(it)
                    )
            flowableEmitter.onNext(data)
        }
    }

}