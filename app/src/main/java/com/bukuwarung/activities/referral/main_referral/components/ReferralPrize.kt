package com.bukuwarung.activities.referral.main_referral.components

import android.content.Context
import android.graphics.Typeface.BOLD
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.util.AttributeSet
import android.util.Log
import android.widget.LinearLayout
import android.widget.TextView
import com.bukuwarung.R

class ReferralPrize: TextView {

    var index: Int = 0

    var contentText: String? = resources.getString(R.string.default_placeholder)
        set(value) {
            field = value

            text = "$text$value"

            try {
                // TODO: OPTIMIZE
                val splitted = text.toString().split(".")
                val rank = splitted.first()
                val builder = SpannableStringBuilder(text.toString())
                val boldText = StyleSpan(BOLD)
                builder.setSpan(boldText, 0, rank.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)

                text = builder
            } catch (ex: Exception) {
                Log.e("ReferralPrize", "Something Happened", ex)
            }
        }

    constructor(context: Context) : this(context, null) {
       init()
    }

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    private fun init() {
        // style
        val lp = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT
        )
        lp.topMargin = 16

        layoutParams = lp
        setTextColor(resources.getColor(R.color.black))
        textSize = 14f
    }

}