package com.bukuwarung.activities.referral.payment_referral

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import kotlinx.android.synthetic.main.referral_confirmation_dialog.*

class ReferralConfirmationDialog(context: Context,private val action:() -> Unit,private val referralCode: String) : BaseDialog(context, BaseDialogType.POPUP) {
    init {
        setCancellable(false)
        setUseFullWidth(false)
    }

    override fun getResId(): Int {
        return R.layout.referral_confirmation_dialog
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        referralConfirmationMessage.text = context.getString(R.string.referral_confirmation_message,referralCode)
        buttonSkip.setOnClickListener{
            dismiss()
        }
        buttonSubmit.setOnClickListener{
            action()
            dismiss()
        }
    }


}