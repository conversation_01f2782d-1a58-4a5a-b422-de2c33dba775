package com.bukuwarung.activities.referral.leaderboard

import com.bukuwarung.activities.referral.leaderboard.converters.UserRankToLeaderboardItemsConverter
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.LeaderBoardSort
import com.bukuwarung.database.repository.ReferralRepository
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.FlowableEmitter

internal class LeaderboardPresenter(
        private val referralRepository: ReferralRepository,
        private val contract: LeaderboardContract
) {

    val converter: UserRankToLeaderboardItemsConverter = UserRankToLeaderboardItemsConverter()

    lateinit var flowableEmitter: FlowableEmitter<LeaderboardViewModel>
    var currentData: LeaderboardViewModel = LeaderboardViewModel()
    var data = Flowable.create<LeaderboardViewModel>(
            {
                emitter -> flowableEmitter = emitter
            }, BackpressureStrategy.BUFFER
    ).doOnNext {
        currentData = it
    }

    fun initData() {
        referralRepository.getUserRankPoints {
            val data = if (it == null) {
                currentData.copy(
                        state = LeaderboardViewState.LOADED
                )
            } else {
                currentData
                        .copy(
                                rank = it.rank,
                                point = it.points.toInt(),
                                state = LeaderboardViewState.LOADED,
                                // BECAUSE RANK DATA CAN ARRIVE AFTER LEADERBOARD DATA
                                items = if (!currentData.items.isNullOrEmpty()) {
                                    // TODO: OPTIMIZE THIS & REFACTOR IN THE FUTURE, STILL TOO COMPLEX
                                    currentData
                                            .items
                                            ?.map { item ->
                                                item.copy(
                                                        isUsers = item.rank == it.rank
                                                )
                                            }
                                } else {
                                    currentData
                                            .items
                                }
                        )
            }
            flowableEmitter.onNext(data)
        }

        referralRepository.getLeaderBoard(
                {
                    val data =
                            currentData
                                    .copy(
                                            items = if (currentData.rank != null) {
                                                converter
                                                        .convertWithAdditionalData(it, currentData.rank)
                                            } else {
                                                converter
                                                        .convert(it)
                                            }
                                    )
                    flowableEmitter.onNext(data)
                },
                LeaderBoardSort.RANK_ASC
        )
    }

    fun goToHistoryPage() {
        AppAnalytics.trackEvent("referral_history_clicked")
        contract.goToHistory()
    }

}