package com.bukuwarung.activities.categorydetail.dialog

import android.content.Context
import android.os.Bundle
import com.bukuwarung.R
import com.bukuwarung.dialogs.base.BasePromptDialog

class DeleteCatDialog(
        context: Context,
        private val trxCount: Int,
        onPromptClicked: ((Boolean) -> Unit)
): BasePromptDialog(context, onPromptClicked) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setTitle(context.resources.getString(R.string.transaction_deletion_dialog_title))
        setContent(context.resources.getString(R.string.category_delete_warning_txt, trxCount))
    }

}