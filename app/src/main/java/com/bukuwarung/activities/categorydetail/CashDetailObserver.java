package com.bukuwarung.activities.categorydetail;


import androidx.lifecycle.Observer;

import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.utils.Utility;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

final class CashDetailObserver<T> implements Observer<CashCategoryEntity> {
    final CashDetailActivity cashDetailActivity;

    CashDetailObserver(CashDetailActivity cashDetailActivity) {
        this.cashDetailActivity = cashDetailActivity;
    }

    public final void onChanged(CashCategoryEntity customerEntity) {
        if (customerEntity != null) {
            try {
                this.cashDetailActivity.customer = customerEntity;
                String customerNm = Utility.isBlank(customerEntity.name)?"O":customerEntity.name;
                if (!Utility.isBlank(customerNm)) {
                    CashDetailActivity.getName(this.cashDetailActivity).setText(customerEntity.name);
                    CashDetailActivity.getName(this.cashDetailActivity).setSelection(customerEntity.name.length());
                }
//                if(customerEntity.type == CategoryUtil.CATEGORY_TYPE_OUT_CST || customerEntity.type == CategoryUtil.CATEGORY_TYPE_IN_CST){
//                    CashDetailActivity.getName(this.cashDetailActivity).setEnabled(true);
//                }else{
//                    CashDetailActivity.getName(this.cashDetailActivity).setEnabled(false);
//                }
                CashDetailActivity.getDeleteLayout(cashDetailActivity).setOnClickListener(
                        new DeleteCategoryClickHandler(cashDetailActivity,cashDetailActivity.customer));

            } catch (Exception e) {
                e.printStackTrace();
                FirebaseCrashlytics.getInstance().recordException(e);
            }
        }
    }
}
