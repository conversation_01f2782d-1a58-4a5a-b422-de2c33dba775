package com.bukuwarung.activities.categorydetail;

import android.app.Dialog;
import android.content.Intent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bukuwarung.R;
import com.bukuwarung.activities.categorydetail.dialog.DeleteCatDialog;
import com.bukuwarung.activities.categorydetail.tasks.DeleteCashAsyncTask;
import com.bukuwarung.activities.expense.category.Category;
import com.bukuwarung.activities.home.MainActivity;
import com.bukuwarung.activities.superclasses.AppActivity;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.session.User;

public class DeleteCategoryClickHandler implements OnClickListener {


    final CashDetailActivity cashDetailActivity;
    final CashCategoryEntity customer;

    public DeleteCategoryClickHandler(CashDetailActivity cashDetailActivity, CashCategoryEntity customerEntity) {
        this.cashDetailActivity = cashDetailActivity;
        this.customer = customerEntity;
    }

    public final void onClick(View view) {
        showDeletionDialog(cashDetailActivity, customer);
    }

    private final void showDeletionDialog(final AppActivity baseActivity, final CashCategoryEntity customerEntity) {
        int trxWithThisCategory = CashRepository.getInstance(baseActivity)
                .countAllCategoryTransactions(customerEntity.cashCategoryId, User.getBusinessId());
        final DeleteCatDialog deleteCatDialog = new DeleteCatDialog(
                baseActivity,
                trxWithThisCategory,
                promptResult -> {
                    if (promptResult) {
                        new DeleteCashAsyncTask(baseActivity).execute(new CashCategoryEntity[]{customerEntity});

                        String oldCategoryId = customerEntity.cashCategoryId;
                        if (oldCategoryId.contains("::"))
                            oldCategoryId = oldCategoryId.split("::")[0];

                        Category oldCategory = new Category(
                                oldCategoryId,
                                customerEntity.name,
                                customerEntity.name,
                                customerEntity.type
                        );

//                        AppConfigManager.getInstance().deleteCashCategory(
//                                oldCategory,
//                                customerEntity.type
//                        );

                        MainActivity.startActivityAndClearTop(baseActivity);
                    }
                    return null; // java to kotlin unit conversion
                }
        );

        deleteCatDialog.show();
    }
}
