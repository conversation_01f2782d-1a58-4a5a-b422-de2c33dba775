package com.bukuwarung.activities.categorydetail.tasks;

import android.app.Activity;
import android.os.AsyncTask;

import com.bukuwarung.Application;
import com.bukuwarung.activities.expense.category.Category;
import com.bukuwarung.activities.expense.category.CategoryUtil;
import com.bukuwarung.analytics.AppAnalytics;
import com.bukuwarung.bulk.model.CashCategory;
import com.bukuwarung.data.repository.FirebaseStorageRepository;
import com.bukuwarung.data.repository.FirestoreRepository;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.entity.CashTransactionEntity;
import com.bukuwarung.database.entity.TransactionEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.database.repository.CustomerRepository;
import com.bukuwarung.database.repository.ProductRepository;
import com.bukuwarung.database.repository.TransactionRepository;
import com.bukuwarung.datasync.AppCustomerCategorySyncManager;
import com.bukuwarung.datasync.AppExpenseTransSyncManager;
import com.bukuwarung.domain.cash.CashUseCase;
import com.bukuwarung.domain.customer.CustomerUseCase;
import com.bukuwarung.domain.product.ProductUseCase;
import com.bukuwarung.domain.transaction.TransactionUseCase;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.TransactionConfigUseCase;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.utils.ListUtils;
import com.bukuwarung.utils.Utility;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import static com.bukuwarung.constants.AppConst.CATEGORY_CASH_IN;
import static com.bukuwarung.constants.AppConst.CATEGORY_CASH_OUT;
import static com.bukuwarung.constants.AppConst.CREDIT;

public class DeleteCashAsyncTask extends AsyncTask<CashCategoryEntity, Void, Void> {

    private final Activity activity;
    private boolean finishActivity;
    private boolean deleteValues;

    public DeleteCashAsyncTask(Activity baseActivity) {

        this.activity = baseActivity;
        this.finishActivity = true;
    }

    public DeleteCashAsyncTask(Activity baseActivity, boolean finishActivity, boolean deleteValues) {
        this.activity = baseActivity;
        this.finishActivity = finishActivity;
        this.deleteValues = deleteValues;
    }


    public Void doInBackground(CashCategoryEntity... cashCategoryEntityArr) {

        CashCategoryEntity originalCashCategoryEntity = cashCategoryEntityArr[0];
        Category cat = null;
        CashRepository cashRepository = CashRepository.getInstance(Application.getAppContext());
        try{
            originalCashCategoryEntity.deleted=1;
            List<CashTransactionEntity> transactionEntityList = CashRepository.getInstance(Application.getAppContext()).getAllCategoryTransactions(originalCashCategoryEntity.cashCategoryId, User.getBusinessId());
            if (!deleteValues) {
                for (CashTransactionEntity transactionEntity : transactionEntityList) {
                    transactionEntity.deleted = 1;
                    cashRepository.insertCashTransaction(transactionEntity);
                    AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(transactionEntity);
                }
                //delete cash category and all of it's transactions
                AppCustomerCategorySyncManager.getInstance().updateCashCategory(originalCashCategoryEntity);
                cashRepository.deleteExistingCash(originalCashCategoryEntity, !deleteValues, "0");
            }
            else if(!ListUtils.isEmpty(transactionEntityList)){
                if (originalCashCategoryEntity.type == 1) {
                    cat = getCategoryById(CATEGORY_CASH_IN, originalCashCategoryEntity.type);
                } else {
                    cat = getCategoryById(CATEGORY_CASH_OUT, originalCashCategoryEntity.type);
                }

                CashUseCase cashUseCase = new CashUseCase(new CashRepository(activity), new TransactionUseCase(new TransactionRepository(activity), new CustomerRepository(activity), new SessionManager(activity)),
                        new CustomerUseCase(new CustomerRepository(activity), new FirebaseStorageRepository(), new SessionManager(activity)), new ProductUseCase(new ProductRepository(activity),
                        new FirestoreRepository()), new TransactionConfigUseCase(new FeaturePrefManager(activity)),
                        new SessionManager(activity));

                //get category id for default category
                // if category exists in database it will fetch existing categoryId
                //if category doesnt exists in database, it will insert empty category with 0 as starting balance and return category id
                String newCashCategoryId = cashUseCase.ensureCategoryId(cat, 0.0, originalCashCategoryEntity.type);

                //move existing category transactions to new category
                //e.g user is deleting category renovation that falls under expense so all renovation transactions will move to cashOut category
                for (CashTransactionEntity transactionEntity : transactionEntityList) {
                    transactionEntity.cashCategoryId = newCashCategoryId;
                    cashRepository.insertCashTransaction(transactionEntity);
                    AppExpenseTransSyncManager.getInstance().updateExpenseTransaction(transactionEntity);
                }
                //recalculate category balance and insert into SQLite and Firestore
                CashRepository.getInstance(activity).updateCategoryBalanceWithSync(newCashCategoryId);
                //set deleted flag for old category
                originalCashCategoryEntity.deleted = 1;
                originalCashCategoryEntity.frequency -= 1;
                //insert deleted category in db so it won't be shown in category dialog
                cashRepository.insertCashCategorySync(originalCashCategoryEntity);
                //put updated category data in firestore
                AppCustomerCategorySyncManager.getInstance().updateCashCategory(originalCashCategoryEntity);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        AppAnalytics.trackEvent("delete_cash_category","category",originalCashCategoryEntity.name);
        return null;
    }

    private Category getCategoryById(String categoryId, int trxType) {

        List<Category> list = new ArrayList<>();
        if (trxType == CREDIT)
            list = CategoryUtil.getCashInCategories();
        else
            list = CategoryUtil.getCashOutCategories();

        for (Category cat : list) {
            if (Utility.areEqual(categoryId, cat.id)) {
                return cat;
            }
        }
        return null;
    }


    public void onPostExecute(Void voidR) {
        if (this.activity != null && finishActivity) {
            this.activity.finish();
        }
    }


}
