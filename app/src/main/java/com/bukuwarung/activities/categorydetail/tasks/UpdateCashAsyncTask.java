package com.bukuwarung.activities.categorydetail.tasks;

import android.os.AsyncTask;

import com.bukuwarung.Application;
import com.bukuwarung.database.entity.CashCategoryEntity;
import com.bukuwarung.database.repository.CashRepository;
import com.bukuwarung.session.User;

public final class UpdateCashAsyncTask extends AsyncTask<CashCategoryEntity, Void, Void> {

    public Void doInBackground(CashCategoryEntity... customerEntityArr) {

        CashCategoryEntity customerEntity = customerEntityArr[0];
        CashRepository cashRepository = CashRepository.getInstance(Application.getAppContext());
        String userId = User.getUserId();
        String deviceId = User.getDeviceId();

        String customerId = customerEntity.cashCategoryId;
        String name = customerEntity.name;
        Integer isDeleted = customerEntity.deleted;
        Integer type = customerEntity.type;

        CashRepository.getInstance(Application.getAppContext()).updateExistingCash(userId, deviceId, customerId,name, isDeleted.intValue(),type);
//        try{
//            bookRepository.updateCustomerImageInfo(customerId, customerEntity.image, 0);
//        }catch(Exception e){
//            e.printStackTrace();
//            FirebaseCrashlytics.getInstance().recordException(e);
//        }
        return null;
    }
}
