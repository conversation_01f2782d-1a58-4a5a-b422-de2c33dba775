package com.bukuwarung.activities

import androidx.annotation.CallSuper
import androidx.lifecycle.ViewModel
import com.bukuwarung.locale.Countries
import com.bukuwarung.locale.Country
import com.bukuwarung.session.SessionManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlin.coroutines.CoroutineContext

open class BaseViewModel : ViewModel(), CoroutineScope {
    private val job = Job()
    override val coroutineContext: CoroutineContext
        get() = Dispatchers.Main + job

    @CallSuper
    override fun onCleared() {
        super.onCleared()
        job.cancel()
    }

    internal fun getCurrency(): String? {
        var country: Country?
        val str = "Rp"
        val countryList: List<*> = Countries.getCountryList()
        var i = 0
        while (true) {
            if (i >= countryList.size) {
                country = null
                break
            }
            country = countryList[i] as Country?
            if (country!!.code == SessionManager.getInstance().countryCode) {
                break
            }
            i++
        }
        return if (country != null) country.currency else str
    }
}
