package com.bukuwarung.activities

import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.domain.payments.PaymentUseCase
import kotlinx.coroutines.runBlocking
import javax.inject.Inject

class WebviewViewModel @Inject constructor(private val paymentUseCase: PaymentUseCase) : BaseViewModel() {

    fun refreshToken():String? = runBlocking {
        paymentUseCase.refreshToken()
    }

    fun fetchBnplBookName(bookEntity: BookEntity?): String {
        var bnplBookId: String = "Not Found"
        bookEntity?.let {
            bnplBookId = bookEntity.bookId
        }
        return bnplBookId
    }
}
