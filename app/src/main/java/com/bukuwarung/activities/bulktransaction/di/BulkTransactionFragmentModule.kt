package com.bukuwarung.activities.bulktransaction.di

import com.bukuwarung.activities.bulktransaction.view.BulkTransactionActivity
import com.bukuwarung.activities.bulktransaction.view.BulkTransactionFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

@Module
abstract class BulkTransactionFragmentModule {

    @ContributesAndroidInjector
    abstract fun contributeBulkTransactionFragment(): BulkTransactionFragment

    @ContributesAndroidInjector
    abstract fun contributeBulkTransactionActivity(): BulkTransactionActivity
}