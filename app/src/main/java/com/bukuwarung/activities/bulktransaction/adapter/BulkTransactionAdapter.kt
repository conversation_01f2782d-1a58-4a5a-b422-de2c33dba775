package com.bukuwarung.activities.bulktransaction.adapter

import android.app.Dialog
import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.animation.AnimationUtils
import android.view.inputmethod.EditorInfo
import android.widget.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.bulktransaction.data.BulkTransactionData
import com.bukuwarung.activities.bulktransaction.view.BulkTransactionActivity
import com.bukuwarung.activities.bulktransaction.view.BulkTransactionFragment
import com.bukuwarung.keyboard.CustomKeyboardView
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.isAnimEnabled
import com.bukuwarung.utils.on

class BulkTransactionAdapter(val context: Context, var bulkTransactionData: MutableList<BulkTransactionData>,
                             val aggregates: updateAggregate, val databaseOps: operateOnDatabase, val softKeyboardHandler: (Boolean) -> Unit) :
        RecyclerView.Adapter<BulkTransactionAdapter.BulkViewHolder>() {
    private var keyboard: CustomKeyboardView? = null
    private var isTransactionDeleted = false
    private var selectedPosition = -1

    class BulkViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val creditSelected: RadioButton
        val debitSelected: RadioButton
        val deleteBulkTransaction: ImageView
        val totalPengeluaran: TextView
        val balance: TextView
        val harga: TextView
        val profit: TextView
        val notes: EditText
        val creditGroup: Group
        val debitGroup: Group
        val selectType: RadioGroup
        val balanceRp: TextView
        val hargaRp: TextView
        val salesRp: TextView
        val lResult: LinearLayout
        val tvExpr: TextView

        val hargaCursor: View
        val balanceCursor: View
        val pengeluaranCursor: View

        val clProfit: ConstraintLayout
        val tvProfit: TextView
        val tvProfitVal: TextView

        val balanceView: View
        val hargaView: View
        val pengeluaranView: View

        val clParent: ConstraintLayout

        init {
            view.apply {
                creditSelected = findViewById(R.id.rb_total_penjualan_select)
                debitSelected = findViewById(R.id.rb_total_pengeluaran_select)
                deleteBulkTransaction = findViewById(R.id.iv_bulk_delete)
                harga = findViewById(R.id.et_harga)
                balance = findViewById(R.id.et_balance)
                profit = findViewById(R.id.tv_profit_val)
                notes = findViewById(R.id.et_note)
                creditGroup = findViewById(R.id.gp_credit)
                totalPengeluaran = findViewById(R.id.et_pengeluaran_text)
                debitGroup = findViewById(R.id.gp_debit)
                selectType = findViewById(R.id.rg_status)
                balanceRp = findViewById(R.id.tv_penjualan_rp)
                hargaRp = findViewById(R.id.tv_pengeluaran_rp)
                salesRp = findViewById(R.id.tv_pengeluaran_r)
                lResult = findViewById(R.id.ll_result)
                tvExpr = findViewById(R.id.tv_expr)
                hargaCursor = findViewById(R.id.harga_cursor)
                balanceCursor = findViewById(R.id.balance_cursor)
                pengeluaranCursor = findViewById(R.id.pengeluaran_cursor)
                clProfit = findViewById(R.id.cl_profit)
                tvProfit = findViewById(R.id.tv_profit)
                tvProfitVal = findViewById(R.id.tv_profit_val)
                balanceView = findViewById(R.id.vw_balance)
                hargaView = findViewById(R.id.vw_harga)
                pengeluaranView = findViewById(R.id.vw_pengeluaran)
                clParent = findViewById(R.id.cl_parent)
            }
        }

    }

    fun updateDeletedList(pos: Int) {
        databaseOps.deleteValues(bulkTransactionData, pos)
        bulkTransactionData.removeAt(pos)
        isTransactionDeleted = true
        updateValues()
        hideKeyBoard()
        notifyItemRemoved(pos)
        notifyItemRangeChanged(pos, itemCount)
    }

    fun setKeyboard(keyboardView: CustomKeyboardView) {
        keyboard = keyboardView
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BulkViewHolder {
        val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.bulk_item, parent, false)
        return BulkViewHolder(view)
    }

    fun showExitDialog(view: View, position: Int) {
        val dialog = Dialog(view.context)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_exit_bulk_transaksi)
        val yesBtn = dialog.findViewById(R.id.btn_yes) as Button
        val noBtn = dialog.findViewById(R.id.btn_no) as Button
        yesBtn.setOnClickListener {
            dialog.dismiss()
        }
        noBtn.setOnClickListener {
            updateDeletedList(position)
            dialog.dismiss()
        }
        dialog.show()
    }

    override fun getItemCount(): Int {
        return bulkTransactionData.size
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun onBindViewHolder(holder: BulkViewHolder, position: Int) {
        if (bulkTransactionData.size <= position) {
            return
        }
        val bulkData = bulkTransactionData[position]

        holder.apply {
            try {
                if (bulkData.type == 1) {
                    creditGroup.visibility = View.VISIBLE
                    debitGroup.visibility = View.GONE
                    var bal = Utility.formatAmount(bulkData.credit)
                    if (bal.contains('₹')) {
                        bal = bal.replace("₹", "Rp")
                    }

                    var har = Utility.formatAmount(bulkData.harga)
                    if (har.contains('₹')) {
                        har = har.replace("₹", "Rp")
                    }
                    balance.setText(bal.replace("Rp", ""))
                    harga.setText(har.replace("Rp", ""))
                    selectType.check(R.id.rb_total_penjualan_select)
                    if (bulkData.credit == 0.0) {
                        balance.setText("")
                    }
                    if (bulkData.harga == 0.0) {
                        harga.setText("")
                    }
                    changeKeyboardFocus(this, FieldType.Balance, position)

                } else {
                    debitGroup.visibility = View.VISIBLE
                    creditGroup.visibility = View.GONE
                    selectType.check(R.id.rb_total_pengeluaran_select)
                    var tPen = Utility.formatAmount(bulkData.sales)
                    if (tPen.contains('₹')) {
                        tPen = tPen.replace("₹", "Rp")
                    }
                    totalPengeluaran.setText(tPen.replace("Rp", ""))
                    if (bulkData.sales == 0.0) {
                        totalPengeluaran.setText("")
                    }
                    changeKeyboardFocus(this, FieldType.Pengeluaran, position)
                }
                deleteBulkTransaction.setOnClickListener {
                    showExitDialog(deleteBulkTransaction, position)
                }

                updateProfitLoss(this)
                updateValues()

                notes.setText(bulkData.notes)

                notes.on(EditorInfo.IME_ACTION_DONE) {
                    notes.clearFocus()
                    InputUtils.hideKeyboardFrom(context, notes)
                    notes.isCursorVisible = false
                }

                notes.setOnFocusChangeListener { v, hasFocus ->
                    val note = notes.text.toString()
                    if (bulkTransactionData.size > position) {
                        bulkTransactionData[position].notes = note
                        if (!TextUtils.isEmpty(note)) {
                            databaseOps.updateValues(bulkTransactionData, position, bulkTransactionData[position].date)
                        }
                        if (hasFocus) {
                            notes.isCursorVisible = true
                            hideKeyBoard()
                        } else {
                            notes.clearFocus()
                            notes.isCursorVisible = false
                        }
                    }
                }

                notes.addTextChangedListener {
                    bulkTransactionData[position].notes = it.toString()
                }

                selectType.setOnCheckedChangeListener { group, checkedId ->
                    if (checkedId == R.id.rb_total_pengeluaran_select) {
                        debitGroup.visibility = View.VISIBLE
                        creditGroup.visibility = View.GONE
                        bulkTransactionData[position].type = 0
                        bulkTransactionData[position].sales = 0.0
                        totalPengeluaran.text = ""
                        changeKeyboardFocus(this, FieldType.Pengeluaran, position)

                    } else {
                        creditGroup.visibility = View.VISIBLE
                        debitGroup.visibility = View.GONE
                        bulkTransactionData[position].type = 1
                        bulkTransactionData[position].sales = 0.0
                        bulkTransactionData[position].harga = 0.0
                        balance.text = ""
                        harga.text = ""
                        changeKeyboardFocus(this, FieldType.Balance, position)
                    }
                }

                balanceView.setOnClickListener {
                    changeKeyboardFocus(this, FieldType.Balance, position)
                }

                hargaView.setOnClickListener {
                    changeKeyboardFocus(this, FieldType.Modal, position)
                }

                pengeluaranView.setOnClickListener {
                    changeKeyboardFocus(this, FieldType.Pengeluaran, position)
                }


                balance.addTextChangedListener {
                    bulkTransactionData[position].credit = Utility.extractAmountFromText(it.toString())
                    databaseOps.updateValues(bulkTransactionData, position, bulkTransactionData[position].date)
                    BulkTransactionActivity.isValueChanged = true
                    updateFromKeyBoard(this, position)
                    updateValues()
                }

                harga.addTextChangedListener {
                    bulkTransactionData[position].harga = Utility.extractAmountFromText(it.toString())
                    databaseOps.updateValues(bulkTransactionData, position, bulkTransactionData[position].date)
                    BulkTransactionActivity.isValueChanged = true
                    updateFromKeyBoard(this, position)
                    updateValues()
                }

                totalPengeluaran.addTextChangedListener {
                    bulkTransactionData[position].sales = Utility.extractAmountFromText(it.toString())
                    databaseOps.updateValues(bulkTransactionData, position, bulkTransactionData[position].date)
                    BulkTransactionActivity.isValueChanged = true
                    updateFromKeyBoard(this, position)
                    updateValues()
                }
            } catch (e: IndexOutOfBoundsException) {

            }

        }
    }

    interface updateAggregate {
        fun updateAggregates(bulkData: MutableList<BulkTransactionData>)
    }

    interface operateOnDatabase {
        fun addValues(bulkData: MutableList<BulkTransactionData>, position: Int)
        fun deleteValues(bulkData: MutableList<BulkTransactionData>, position: Int)
        fun updateValues(bulkData: MutableList<BulkTransactionData>, position: Int, date: String?)
    }

    fun updateValues() {
        aggregates.updateAggregates(bulkTransactionData)
    }

    fun addNewTransaction(newBulkTransactionData: BulkTransactionData) {
        bulkTransactionData.add(newBulkTransactionData)
        var size = bulkTransactionData.size
        if (size == 1 && isTransactionDeleted) {
            size += 1
        }

        selectedPosition = size

        databaseOps.addValues(bulkTransactionData, size - 1)
        updateValues()
        notifyItemChanged(bulkTransactionData.size)
    }

    fun refreshTransactionData(bulkData: MutableList<BulkTransactionData>, position: Int) {
        this.bulkTransactionData = bulkData
    }

    fun hideKeyBoard() {
        keyboard?.apply {
            if (this.isAttachedToWindow) {
                hideCursor()
                visibility = View.GONE
                BulkTransactionFragment.isKeyboardVisible = false
                submit()
            }
        }
    }

    private fun changeKeyboardFocus(view: BulkViewHolder, fieldType: FieldType, position: Int) {
        keyboard?.apply {
            setFromBulk()
            setExprTv(view.tvExpr)
            setResultLayout(view.lResult)
            view.lResult.visibility = View.GONE
            view.notes.clearFocus()
            view.notes.isCursorVisible = false
            try {
                hideCursor()
            } catch (ex: Exception) {
            }

            when (fieldType) {
                FieldType.Modal -> {
                    setResultTv(view.harga)
                    setCurrency(view.hargaRp)
                    cursor = view.hargaCursor
                    view.hargaView.requestFocus()
                    view.tvExpr.text = view.harga.text.toString()
                    view.hargaCursor.visibility = View.VISIBLE
                    view.balanceCursor.visibility = View.GONE
                    view.pengeluaranCursor.visibility = View.GONE
                }
                FieldType.Balance -> {
                    setResultTv(view.balance)
                    setCurrency(view.balanceRp)
                    cursor = view.balanceCursor
                    view.balanceView.requestFocus()
                    view.tvExpr.text = view.balance.text.toString()
                    bulkTransactionData[position].credit = Utility.extractAmountFromText(view.tvExpr.text.toString())
                    view.hargaCursor.visibility = View.GONE
                    view.balanceCursor.visibility = View.VISIBLE
                    view.pengeluaranCursor.visibility = View.GONE
                }
                FieldType.Pengeluaran -> {
                    setResultTv(view.totalPengeluaran)
                    setCurrency(view.totalPengeluaran)
                    cursor = view.pengeluaranCursor
                    view.pengeluaranView.requestFocus()
                    view.tvExpr.text = view.totalPengeluaran.text.toString()
                    bulkTransactionData[position].sales = Utility.extractAmountFromText(view.tvExpr.text.toString())
                    view.hargaCursor.visibility = View.GONE
                    view.balanceCursor.visibility = View.GONE
                    view.pengeluaranCursor.visibility = View.VISIBLE
                }
            }

            updateProfitLoss(view)
            updateValues()
            updateFromKeyBoard(view, position)
            softKeyboardHandler(true)
            val moveup = AnimationUtils.loadAnimation(context, R.anim.move_up)
            if (context.isAnimEnabled()) startAnimation(moveup)
            visibility = View.VISIBLE
            showCursor()
        }
    }

    fun updateFromKeyBoard(view: BulkViewHolder, position: Int) {
        if (bulkTransactionData.size < 1)
            return
        if (bulkTransactionData[position].type == 1) {
            bulkTransactionData[position].harga = Utility.extractAmountFromText(view.harga.text.toString())
            bulkTransactionData[position].credit = Utility.extractAmountFromText(view.balance.text.toString())
        } else {
            bulkTransactionData[position].sales = Utility.extractAmountFromText(view.totalPengeluaran.text.toString())
        }
        updateProfitLoss(view)
    }

    fun updateProfitLoss(holder: BulkViewHolder) {
        holder.apply {
            val cleanBalance: String
            val cleanModal: String
            var balVal = balance.text.toString()
            var hargaVal = harga.text.toString()
            if (Utility.isBlank(balVal)) balVal = "0"
            if (Utility.isBlank(hargaVal)) hargaVal = "0"
            if (SessionManager.getInstance().countryCode == "+62" || SessionManager.getInstance().countryCode == "62") {
                cleanBalance = balVal.replace(".", "")
                        .replace(",", ".")
                cleanModal = hargaVal.replace(".", "")
                        .replace(",", ".")
            } else {
                cleanBalance = balVal.replace(".", "")
                        .replace(",", "")
                cleanModal = hargaVal.replace(".", "")
                        .replace(",", "")
            }
            val currentBalance = cleanBalance.toDouble()
            val currentModal = cleanModal.toDouble()

            val color = if (currentBalance - currentModal >= 0) ContextCompat.getColor(itemView.context, R.color.green_5) else
                ContextCompat.getColor(itemView.context, R.color.red_5)

            clProfit.setBackgroundColor(color)
            tvProfitVal.text = Utility.formatAmount(currentBalance - currentModal)
            if (currentBalance - currentModal >= 0) {
                tvProfit.text = itemView.context.resources.getString(R.string.profit_kamu)
                tvProfit.setTextColor(ContextCompat.getColor(itemView.context, R.color.green_100))
                tvProfitVal.setTextColor(ContextCompat.getColor(itemView.context, R.color.green_100))
            } else {
                tvProfit.text = itemView.context.resources.getString(R.string.loss)
                tvProfit.setTextColor(ContextCompat.getColor(itemView.context, R.color.red_100))
                tvProfitVal.setTextColor(ContextCompat.getColor(itemView.context, R.color.red_100))
            }
        }
    }

    private enum class FieldType {
        Modal, Balance, Pengeluaran
    }
}