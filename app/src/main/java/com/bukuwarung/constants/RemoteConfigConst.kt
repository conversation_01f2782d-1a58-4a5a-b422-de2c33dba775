package com.bukuwarung.constants

import com.bukuwarung.BuildConfig
import com.bukuwarung.payments.data.model.PaymentHistory

object RemoteConfigConst {
    const val TNC_TAB_CONFIG: String =
        """["CUSTOMER",  "PAYMENT",  "TRANSACTION",  "STOCK",  "OTHERS"]"""
    const val REDIRECT_HELP_ICON_CUSTOMER_SUPPORT = "customer_support_chat"
    const val REDIRECT_HELP_ICON_TUTORIAL = "tutorial_page"
    const val USE_NEW_ONBOARDING_VARIANT = 1
    const val BUSINESS_PROFILE_VARIANT_OLD = 1
    const val BUSINESS_PROFILE_VARIANT_NEW_NATIVE = 2
    const val BUSINESS_PROFILE_VARIANT_NEW_WEB = 3
    const val BNPL_URL = "https://api-v4.bukuwarung.com/los-web/bnpl/bnpl-landing?productType=BNPL_PPOB"
    const val BUKU_MODAL_URL = "https://api-v3.bukuwarung.com/los-web/dashboard?source=buku_modal_icon"
    const val APP_TEXTS = """
        {
            "poweredByFooter": "Pembayaran didukung oleh DOKU, OY!, dan BI Fast yang berlisensi <b>Bank Indonesia.</b>",
            "poweredByFooterPlain": "Pembayaran didukung oleh DOKU, OY!, dan BI Fast yang berlisensi Bank Indonesia.",
            "paymentTutorialTitle": "Pembayaran digital didukung oleh DOKU, OY!, dan BI Fast yang berlisensi Bank Indonesia",
            "qrisSuccessMessage": "Data untuk pengajuan QRIS telah terkirim. Proses validasi data membutuhkan waktu 14 hari kerja.",
            "qrisMDRInfoTooltip": "Biaya QRIS atau MDR (Merchant Discount  Rate) adalah biaya yang dibebankan ke  pengguna selama transaksi dengan QRIS.",
            "qrisMDRLabel": "Biaya QRIS",
            "kycRequiredTitle": "Verifikasi Akun Premium dulu, yuk!",
            "kycRequiredMessage": "Yuk, Verifikasi Akun untuk nikmati: <br />✅ Tagih dan Bayar dengan limit yang lebih besar <br />✅ Akses QRIS lebih cepat <br />✅ Dapat pinjaman modal melalui BukuWarung <br />✅ Bisa simpan saldo hingga Rp10 Juta",
            "cashbackMessage":"Tagih dan Bayar dapat cashback saldo Rp2.000!",
            "cashbackTransactionMessage":"Cashback saldo Rp3.000 untuk {y}x Tagih atau Bayar",
            "manualVerificationTime":"Verifikasi rekening bank kamu sedang diproses maks. 7 hari kerja"
        }
    """

    val BUSINESS_CARD_LOCKED_DESIGN = """
        [          
          {
            "cardUID": "bizcard_17",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_17.webp?alt=media&token=97e9b601-b2e7-4b87-92e2-c111aedb0a19",
            "textColor": "#169F82",
            "tagLineTextColor": "#169F82",
            "topTextColor": "#169F82",
            "bottomTextColor": "#169F82",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#169F82"
          }
        ]
    """

    const val SOCIAL_MEDIA_POSTERS = """{
  "data": [
    {
      "story_id": 1,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/wa1.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#846441",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#846441",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 2,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/wa2.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#846441",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#846441",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#E8EDED",
      "store_link_text_color": "#000000"
    },
    {
      "story_id": 3,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/wa3.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#ffffff",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#ffffff",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#ffffff",
      "store_link_tint_color": "#2D318D",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 4,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/wa4.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#846441",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#846441",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 5,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/wa5.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#ffffff",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#ffffff",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#ffffff",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 6,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/wa6.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#000000",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#000000",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    }
  ]
}"""

    val NEW_BUSINESS_CARD_DESIGNS = """
        [
          {
            "cardUID": "bizcard_1",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_1.webp?alt=media&token=a6ce2295-b8f6-4c40-bc20-3e4a67772627",
            "textColor": "#FFFFFF",
            "tagLineTextColor": "#FFFFFF",
            "topTextColor": "#FFFFFF",
            "bottomTextColor": "#FFFFFF",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#EBAF0B"
          },
          {
            "cardUID": "bizcard_2",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_2.webp?alt=media&token=4063d5a2-351f-48ec-99f2-cef904437cbf",
            "textColor": "#007A60",
            "tagLineTextColor": "#007A60",
            "topTextColor": "#007A60",
            "bottomTextColor": "#007A60",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#EBAF0B"
          },
          {
            "cardUID": "bizcard_3",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_3.webp?alt=media&token=bfc67b9e-8445-49f4-8542-50c94abb7b1f",
            "textColor": "#391859",
            "tagLineTextColor": "#391859",
            "topTextColor": "#391859",
            "bottomTextColor": "#391859",
            "iconColor": "#EDE8E5",
            "iconBgColor": "#391859"
          },
          {
            "cardUID": "bizcard_4",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_4.webp?alt=media&token=057d29f1-362a-42d1-bbe2-58f33226cf89",
            "textColor": "#F44E6C",
            "tagLineTextColor": "#F44E6C",
            "topTextColor": "#F44E6C",
            "bottomTextColor": "#F44E6C",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#F44E6C"
          },
          {
            "cardUID": "bizcard_5",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_5.webp?alt=media&token=7d1b237d-870e-4e18-8cbb-2ccab7983f2d",
            "textColor": "#91265A",
            "tagLineTextColor": "#91265A",
            "topTextColor": "#91265A",
            "bottomTextColor": "#91265A",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#91265A"
          },
          {
            "cardUID": "bizcard_6",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_6.webp?alt=media&token=9906879a-461f-4b5a-a1ba-0553be0d42b1",
            "textColor": "#F6D6C1",
            "tagLineTextColor": "#F6D6C1",
            "topTextColor": "#F6D6C1",
            "bottomTextColor": "#F6D6C1",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#F6D6C1"
          },
          {
            "cardUID": "bizcard_7",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_7.webp?alt=media&token=c50cbc9a-53ac-4177-9b74-03049758b3ea",
            "textColor": "#0C5D75",
            "tagLineTextColor": "#0C5D75",
            "topTextColor": "#0C5D75",
            "bottomTextColor": "#0C5D75",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#0C5D75"
          },
          {
            "cardUID": "bizcard_8",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_8.webp?alt=media&token=5f24b6c2-efdd-4e0a-ac01-56df11100bd0",
            "textColor": "#222222",
            "tagLineTextColor": "#222222",
            "topTextColor": "#222222",
            "bottomTextColor": "#222222",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#222222"
          },
          {
            "cardUID": "bizcard_9",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_9.webp?alt=media&token=2f2d0e04-30ad-47a3-b929-c64af1bb6bea",
            "textColor": "#222222",
            "tagLineTextColor": "#222222",
            "topTextColor": "#222222",
            "bottomTextColor": "#222222",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#222222"
          },
          {
            "cardUID": "bizcard_10",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_10.webp?alt=media&token=ec71fc4e-0915-468e-b894-459dceb683bc",
            "textColor": "#FFFFFF",
            "tagLineTextColor": "#222222",
            "topTextColor": "#FFFFFF",
            "bottomTextColor": "#222222",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#222222"
          },
          {
            "cardUID": "bizcard_11",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_11.webp?alt=media&token=3142b4c4-9de0-441a-b1d7-4bfdfefefe83",
            "textColor": "#28486E",
            "tagLineTextColor": "#28486E",
            "topTextColor": "#28486E",
            "bottomTextColor": "#28486E",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#28486E"
          },
          {
            "cardUID": "bizcard_12",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_12.webp?alt=media&token=8a973029-f577-48fb-b55b-d4f45d3b88f2",
            "textColor": "#653C1C",
            "tagLineTextColor": "#653C1C",
            "topTextColor": "#653C1C",
            "bottomTextColor": "#653C1C",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#653C1C"
          },
          {
            "cardUID": "bizcard_13",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_13.webp?alt=media&token=0e1f2278-d539-4301-aba4-033956fa5843",
            "textColor": "#FFFFFF",
            "tagLineTextColor": "#FFFFFF",
            "topTextColor": "#FFFFFF",
            "bottomTextColor": "#FFFFFF",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#28486E"
          },
          {
            "cardUID": "bizcard_14",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_14.webp?alt=media&token=bbdf7055-8269-4c5c-b404-5b98db0c92d2",
            "textColor": "#181859",
            "tagLineTextColor": "#181859",
            "topTextColor": "#181859",
            "bottomTextColor": "#181859",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#181859"
          },
          {
            "cardUID": "bizcard_15",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_15.webp?alt=media&token=90e5c9ac-d45c-44e8-a064-b2c4636b961d",
            "textColor": "#181859",
            "tagLineTextColor": "#181859",
            "topTextColor": "#181859",
            "bottomTextColor": "#181859",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#181859"
          },
          {
            "cardUID": "bizcard_16",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_16.webp?alt=media&token=762fb0c4-6d53-4534-bca1-7a4caee768a1",
            "textColor": "#E8AF62",
            "tagLineTextColor": "#E8AF62",
            "topTextColor": "#E8AF62",
            "bottomTextColor": "#E8AF62",
            "iconColor": "#222222",
            "iconBgColor": "#E8AF62"
          },
          {
            "cardUID": "bizcard_17",
            "backgroundUrl": "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/business_card_template%2Fbizz_card_17.webp?alt=media&token=97e9b601-b2e7-4b87-92e2-c111aedb0a19",
            "textColor": "#169F82",
            "tagLineTextColor": "#169F82",
            "topTextColor": "#169F82",
            "bottomTextColor": "#169F82",
            "iconColor": "#FFFFFF",
            "iconBgColor": "#169F82"
          }
        ]
    """

    const val SOCIAL_MEDIA_STATUS = """
        {
  "data": [
    {
      "story_id": 1,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/second.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#846441",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#846441",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 2,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/stories_banner.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#846441",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#846441",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#E8EDED",
      "store_link_text_color": "#000000"
    },
    {
      "story_id": 3,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/design3.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#ffffff",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#ffffff",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#ffffff",
      "store_link_tint_color": "#2D318D",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 4,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/design4.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#846441",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#846441",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 5,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/design5.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#ffffff",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#ffffff",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#ffffff",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    },
    {
      "story_id": 6,
      "background_image": "https://tokoko-assets.s3-ap-southeast-1.amazonaws.com/marketing/design6.webp",
      "title_text": "Diskon Chinese \nNew Year 2021",
      "title_text_color": "#000000",
      "detail_text": "Nikmati diskon imlek dengan \n50% diskon untuk semua \nproduk yang ada di sini semua",
      "detail_text_color": "#000000",
      "footer_text": "Yuk belanja dimari saja semua",
      "footer_text_color": "#000000",
      "store_link_tint_color": "#4BA59C",
      "store_link_text_color": "#ffffff"
    }
  ]
}
    """

    const val USER_PROFILE_MENU = """
        [
  {
    "name": "Ubah Profil",
    "readabaleName": "Edit Profile",
    "rank": 1,
    "icon": "https://i.ibb.co/WnW51fW/icon-undang-1.png",
    "deeplink": 1,
    "deeplink_url": "",
    "destination": "Ubah Profil"
  },
  {
    "name": "Informasi Usaha",
    "readabaleName": "Edit Profile",
    "rank": 2,
    "icon": "https://i.ibb.co/WnW51fW/icon-undang-1.png",
    "deeplink": 4,
    "deeplink_url": "",
    "destination": "Ubah Informasi Usaha"
  },
  {
    "name": "Daftar Rekening Kamu",
    "readabaleName": "Register Your Account",
    "rank": 2,
    "icon": "https://i.ibb.co/WpZLnkJ/Frame-2908-1.png",
    "deeplink": 2,
    "deeplink_url": "",
    "destination": "Daftar Rekening Kamu"
  }
]"""

    const val NOTES_MISSION_STEPS = """
        [{
  "featureId":"user_profile_complete",
  "title":"Lengkapi Profil Kamu",
  "subText":"Nama, alamat email, jenis kelamin dan tanggal lahir .",
  "redirection": "ubah_profile"
},{
  "featureId":"user_business_complete",
  "title":"Lengkapi Info Usaha Kamu ",
  "subText":"Alamat usaha, jam buka, detail usaha dan omzet penjualan.",
  "redirection": "ubah_informasi_usaha"
}]"""

    const val TRANSACTION_CATEGORY_BREAK_UP_COLORS = """
        [   {     "color": "#FCDB9C"   },   {     "color": "#FDF0CC"   },   {     "color": "#A7C9F1"   },   {     "color": "#E6F7F3"   },   {     "color": "#FFC0C0"   },   {     "color": "#DBDBDB"   },   {     "color": "#FFEEEE"   },   {     "color": "#E5F4FF"   } ]"""

    const val PROFILE_PIN = """
        [
   {
      "name":"Kartu Nama",
      "readable_name":"Business Card",
      "rank":4,
      "icon":"https://bw-profile-pins.s3-ap-southeast-1.amazonaws.com/kartu_nama.webp",
      "deeplink":1,
      "deeplink_url":"",
      "destination":"Business Card"
   },
   {
      "name":"Jualan Pulsa",
      "readable_name":"Mobile Recharge",
      "rank":5,
      "icon":"https://bw-profile-pins.s3-ap-southeast-1.amazonaws.com/jualan_pulsa.webp",
      "deeplink":2,
      "deeplink_url":"",
      "destination":"Jualan Pulsa"
   },
   {
      "name":"Pengingat",
      "readable_name":"Self Reminder",
      "rank":3,
      "icon":"https://bw-profile-pins.s3-ap-southeast-1.amazonaws.com/pengingat.webp",
      "deeplink":3,
      "deeplink_url":"",
      "destination":"Self Reminder"
   },
   {
      "name":"Undang",
      "readable_name":"Referral Invite",
      "rank":1,
      "icon":"https://bw-profile-pins.s3-ap-southeast-1.amazonaws.com/undang.webp",
      "deeplink":4,
      "deeplink_url":"",
      "destination":"Share"
   },
   {
      "name":"Atur Nota",
      "readable_name":"Invoice Settings",
      "rank":2,
      "icon":"https://bw-profile-pins.s3-ap-southeast-1.amazonaws.com/atur_nota.png",
      "deeplink":5,
      "deeplink_url":"",
      "destination":"Notes"
   },
   {
      "name":"Stiker WA",
      "readable_name":"WhatsApp Stickers",
      "rank":6,
      "icon":"https://bw-profile-pins.s3-ap-southeast-1.amazonaws.com/sticker_wa.webp",
      "deeplink":6,
      "deeplink_url":"",
      "destination":"Whatsapp Stickers"
   }
]
    """

    const val WELCOME_SCREENS = """
        [
  {
    "screenName": "customer_testimonial",
    "backGroundImage": "https://mx-static.dev.bukuwarung.com/android/welcome-screen/welcome1.webp",
    "titleText": "Dipercaya 8 Juta++ UMKM",
    "subTitleText": "Satu Aplikasi Keuangan, Solusi Beragam Kebutuhan UMKM Indonesia"
  },
  {
    "screenName": "customer_payment",
    "backGroundImage": "https://mx-static.dev.bukuwarung.com/android/welcome-screen/welcome2.webp",
    "titleText": "Limit Pembayaran s.d 1 Miliar",
    "subTitleText": "Bayar & Tagih ke berbagai bank/e-Wallet, uang langsung terkirim"
  },
  {
    "screenName": "customer_debt",
    "backGroundImage": "https://mx-static.dev.bukuwarung.com/android/welcome-screen/welcome3.webp",
    "titleText": "Tambah Penghasilan hingga 10 Juta/Bulan",
    "subTitleText": "Dengan jualan beragam produk digital seperti pulsa, token listrik, dll"
  }
]
    """

    const val REFERRAL_WA_MESSAGE =
        "Yuk, gabung menjadi bagian dari BukuWarung untuk dapetin hadiah menarik!\n" +
                "BukuWarung adalah aplikasi pencatatan keuangan lengkap yang bisa kamu gunakan juga untuk bayar dan tagih transaksi usaha GRATIS bebas biaya admin. Nggak cuma itu, kamu juga bisa jualan pulsa, dan token listrik loh!\n" +
                "Download aplikasi BukuWarung sekarang dengan link berikut. []"


    /**
     * POS Related config constants
     */

    const val POS_PAYMENT_WALLETS = """
        [
   {
      "name":"Gopay",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/pos-images/pos-gopay.png",
      "order":1
   },
   {
      "name":"DANA",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/pos-images/pos-dana.png",
      "order":2
   },
   {
      "name":"OVO",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/pos-images/pos-ovo.png",
      "order":3
   },
   {
      "name":"ShopeePay",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/pos-images/pos-shopee.png",
      "order":4
   },
   {
      "name":"LinkAja",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/pos-images/pos-linkaja.png",
      "order":5
   },
   {
      "name":"QRIS",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/pos-images/pos-qris.png",
      "order":6
   }
]
    """

    /**
     * Onboarding related config constants
     */
    const val ON_BOARDING_CATEGORIES = """
        [
  {
    "id": 1,
    "resource_id": "3",
    "order": 1,
    "name": "Makanan atau minuman",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/fnb.png"
  },
  {
    "id": 2,
    "resource_id": "0",
    "order": 2,
    "name": "Pulsa, token listrik, dan tagihan",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/pulsa.png"
  },
  {
    "id": 3,
    "resource_id": "1",
    "order": 3,
    "name": "Pakaian, tas atau aksesoris",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/pakaian.png"
  },
  {
    "id": 4,
    "resource_id": "2",
    "order": 4,
    "name": "Warung sembako/rokok",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/sembako.png"
  },
  {
    "id": 5,
    "order": 5,
    "resource_id": "6",
    "name": "Produk kesehatan/kecantikan",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/kesehatan_kecantikan.png"
  },
  {
    "id": 6,
    "order": 6,
    "resource_id": "37",
    "name": "Agen pembayaran",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/bank.png"
  },
  {
    "id": 7,
    "resource_id": "38",
    "order": 7,
    "name": "Jasa (bengkel, laundry, salon, dan lainnya)",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/jasa_bengkel.png"
  },
  {
    "id": 8,
    "resource_id": "8",
    "order": 8,
    "name": "Pertanian atau kebutuhan tani",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/pertanian.png"
  },
  {
    "id": 9,
    "order": 9,
    "resource_id": "4",
    "name": "Galon, gas LPG atau bensin",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/galon_gas.png"
  },
  {
    "id": 10,
    "resource_id": "5",
    "order": 10,
    "name": "Buah, sayur, daging dan makanan segar",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/buah_sayur.png"
  },
  {
    "id": 11,
    "resource_id": "36",
    "order": 11,
    "name": "Lainnya",
    "image_url": "https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/business-category-images/lainnya.png"
  }
]
    """
    const val ON_BOARDING_USAGE_GOAL_OPTIONS = """
        [
   {
      "id":1,
      "order":1,
      "resource_id":"2",
      "name":"Catat Transaksi",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/in_out.png"
   },
   {
      "id":2,
      "order":2,
      "resource_id":"0",
      "name":"Catat Utang",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/contact.png"
   },
   {
      "id":3,
      "order":3,
      "resource_id":"4",
      "name":"Kelola Stok Barang",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/inventory.png"
   },
   {
      "id":4,
      "order":4,
      "resource_id":"1",
      "name":"Kirim & Tagih Uang",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/payment.png"
   },
   {
      "id":5,
      "order":5,
      "resource_id":"1",
      "name":"Jual Beli Pulsa",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/pulsa.png"
   }
]
    """
    const val ON_BOARDING_USAGE_PAST_OPTIONS = """
        [
   {
      "id":1,
      "order":1,
      "name":"Aplikasi HP",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/phone.png"
   },
   {
      "id":2,
      "order":2,
      "name":"Microsoft Excel",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/excel.png"
   },
   {
      "id":3,
      "order":3,
      "name":"Catat di buku",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/book.png"
   },
   {
      "id":4,
      "order":4,
      "name":"Tidak mencatat",
      "image_url":"https://bw-ac-assets.s3.ap-southeast-1.amazonaws.com/onboarding-assets/fingers.png"
   }
]
    """
    const val CATEGORY_CREDIT = """
            [{
                "categoryImage": "https://i.ibb.co/GJ32gQT/penjualan.webp",
                "categoryName": "Penjualan"
            },
                {
                    "categoryImage": "https://i.ibb.co/8XxfqR5/penambahan.webp",
                    "categoryName": "Penambahan Modal"
                },
                {
                    "categoryImage": "https://i.ibb.co/KFhw3nS/lain.webp",
                    "categoryName": "Pendapatan Lain"
                },
                {
                    "categoryImage": "https://i.ibb.co/TRtfGHD/pendapatan.webp",
                    "categoryName": "Pendapatan Jasa"
                },
                {
                    "categoryImage": "https://i.ibb.co/k0CRTtX/hibah.png",
                    "categoryName": "Hibah"
                },
                {
                    "categoryImage": "https://i.ibb.co/nwyWcW2/pinjaman.webp",
                    "categoryName": "Pinjaman"
                },
                {
                    "categoryImage": "https://i.ibb.co/0n7VY31/komisi.webp",
                    "categoryName": "Piutang"
                }
            ]
            """

    const val CATEGORY_CREDIT_NEW = """
           [ {
    "categoryImage": "https://i.ibb.co/8XxfqR5/penambahan.webp",
    "categoryName": "Penambahan Modal",
    "categoryId": "penambahan"
  },
  {
    "categoryImage": "https://i.ibb.co/KV0c1dG/pendapatan-Di-Luar-Usaha.png",
    "categoryName": "Pendapatan Di Luar Usaha",
    "categoryId": "pendapatanDiLuarUsaha"
  },
  {
    "categoryImage": "https://i.ibb.co/KFhw3nS/lain.webp",
    "categoryName": "Pendapatan Lain Lain",
    "categoryId": "pendapatan"
  },
  {
    "categoryImage": "https://i.ibb.co/TRtfGHD/pendapatan.webp",
    "categoryName": "Pendapatan Jasa/Komisi",
    "categoryId": "jasa"
  },
  {
    "categoryImage": "https://i.ibb.co/nwyWcW2/pinjaman.webp",
    "categoryName": "Terima Pinjaman",
    "categoryId": "lending"
  },
  {
    "categoryImage": "https://i.ibb.co/0n7VY31/komisi.webp",
    "categoryName": "Penagihan Utang/Cicilan",
    "categoryId": "piutang"
  } ]
            """

    const val CATEGORY_CREDIT_NEW_BACK_UP = """
             [ {
    "categoryImage": "https://i.ibb.co/8XxfqR5/penambahan.webp",
    "categoryName": "Penambahan Modal",
    "categoryId": "penambahan"
  },
  {
    "categoryImage": "https://i.ibb.co/KV0c1dG/pendapatan-Di-Luar-Usaha.png",
    "categoryName": "Pendapatan Di Luar Usaha",
    "categoryId": "pendapatanDiLuarUsaha"
  },
  {
    "categoryImage": "https://i.ibb.co/KFhw3nS/lain.webp",
    "categoryName": "Pendapatan Lain Lain",
    "categoryId": "pendapatan"
  },
  {
    "categoryImage": "https://i.ibb.co/TRtfGHD/pendapatan.webp",
    "categoryName": "Pendapatan Jasa/Komisi",
    "categoryId": "jasa"
  },
  {
    "categoryImage": "https://i.ibb.co/nwyWcW2/pinjaman.webp",
    "categoryName": "Terima Pinjaman",
    "categoryId": "lending"
  },
  {
    "categoryImage": "https://i.ibb.co/0n7VY31/komisi.webp",
    "categoryName": "Penagihan Utang/Cicilan",
    "categoryId": "piutang"
  } ]
            """

    const val CATEGORY_DEBIT = """
            [{
  "categoryImage": "https://i.ibb.co/x13sqPQ/pembelianstok.png",
    "categoryName": "Pembelian stok"
},
 {
  "categoryImage": "https://i.ibb.co/ZMztvmz/bahanbaku.png",
    "categoryName": "Pembelian bahan baku"
},
 {
  "categoryImage": "https://i.ibb.co/gdGTnPh/operasional.webp",
    "categoryName": "Biaya operasional"
},
 {
  "categoryImage": "https://i.ibb.co/nQgNMzw/pengeluaran-lain-lain.png",
    "categoryName": "Pengeluaran lain-lain"
},
 {
  "categoryImage": "https://i.ibb.co/3hDqHg7/utang.png",
    "categoryName": "Pembayaran utang"
},
 {
  "categoryImage": "https://i.ibb.co/nmhqFyr/pemberian-utang.webp",
    "categoryName": "Pemberian utang"
},
 {
  "categoryImage": "https://i.ibb.co/M9Hktt5/pribadi.webp",
    "categoryName": "Pribadi dan keluarga"
},
 {
  "categoryImage": "https://i.ibb.co/NYjZx8y/donasi.png",
    "categoryName": "Donasi atau sedekah"
},
{
  "categoryImage": "https://i.ibb.co/hsgxL7L/tabungan.webp",
    "categoryName": "Tabungan atau investasi"
}
]
            """

    const val CATEGORY_DEBIT_NEW = """
            [{ "categoryImage": "https://i.ibb.co/x13sqPQ/pembelianstok.png",     "categoryName": "Pembelian Stok",     "categoryId": "Stok"   },   {     "categoryImage": "https://i.ibb.co/KV0c1dG/pendapatan-Di-Luar-Usaha.png",     "categoryName": "Pengeluaran Di Luar Usaha",     "categoryId": "pengeluaranDiLuarUsaha"   },   {     "categoryImage": "https://i.ibb.co/ZMztvmz/bahanbaku.png",     "categoryName": "Pembelian bahan baku",     "categoryId": "Baku"   },   {     "categoryImage": "https://i.ibb.co/gdGTnPh/operasional.webp",     "categoryName": "Biaya operasional",     "categoryId": "biayaOperasional"   },   {     "categoryImage": "https://i.ibb.co/Gxwdzvx/Frame-2917gaji-Karyawab.png",     "categoryName": "Gaji/Bonus Karyawan",     "categoryId": "gajiKaryawan"   },   {     "categoryImage": "https://i.ibb.co/nmhqFyr/pemberian-utang.webp",     "categoryName": "Pemberian utang",     "categoryId": "Pemberian"   },   {     "categoryImage": "https://i.ibb.co/3hDqHg7/utang.png",     "categoryName": "Pembayaran Utang/Cicilan",     "categoryId": "Pembarayan"   },   {     "categoryImage": "https://i.ibb.co/nQgNMzw/pengeluaran-lain-lain.png",     "categoryName": "Pengeluaran lain-lain",     "categoryId": "lain"   }]
            """
    const val CATEGORY_DEBIT_NEW_BACK_UP = """
            [{ "categoryImage": "https://i.ibb.co/x13sqPQ/pembelianstok.png",     "categoryName": "Pembelian Stok",     "categoryId": "Stok"   },   {     "categoryImage": "https://i.ibb.co/KV0c1dG/pendapatan-Di-Luar-Usaha.png",     "categoryName": "Pengeluaran Di Luar Usaha",     "categoryId": "pengeluaranDiLuarUsaha"   },   {     "categoryImage": "https://i.ibb.co/ZMztvmz/bahanbaku.png",     "categoryName": "Pembelian bahan baku",     "categoryId": "Baku"   },   {     "categoryImage": "https://i.ibb.co/gdGTnPh/operasional.webp",     "categoryName": "Biaya operasional",     "categoryId": "biayaOperasional"   },   {     "categoryImage": "https://i.ibb.co/Gxwdzvx/Frame-2917gaji-Karyawab.png",     "categoryName": "Gaji/Bonus Karyawan",     "categoryId": "gajiKaryawan"   },   {     "categoryImage": "https://i.ibb.co/nmhqFyr/pemberian-utang.webp",     "categoryName": "Pemberian utang",     "categoryId": "Pemberian"   },   {     "categoryImage": "https://i.ibb.co/3hDqHg7/utang.png",     "categoryName": "Pembayaran Utang/Cicilan",     "categoryId": "Pembarayan"   },   {     "categoryImage": "https://i.ibb.co/nQgNMzw/pengeluaran-lain-lain.png",     "categoryName": "Pengeluaran lain-lain",     "categoryId": "lain"   }]
            """

    const val CATEGORY_INFO_CREDIT =
        """
            [
                {
                    "categoryName": "Penjualan",
                    "categoryDescription": "Uang yang masuk dari transaksi jual beli barang jadi. Contoh: penjualan barang di toko kamu."
                },
                {
                    "categoryName": "Penambahan Modal",
                    "categoryDescription": "Uang untuk pengembangan usaha yang didapatkan melalui uang pribadi, investor, atau pinjaman bank."
                },
                {
                    "categoryName": "Pendapatan Lain",
                    "categoryDescription": "Penghasilan tidak rutin yang berasal dari kegiatan di luar usaha utama. Contoh: penjualan aset usaha."
                },
                {
                    "categoryName": "Pendapatan Jasa",
                    "categoryDescription": "Uang yang dihasilkan berupa upah dari menjualkan barang atau menyediakan jasa. Contoh: Dropship, tarif cukur rambut, laundry."
                },
                {
                    "categoryName": "Hibah",
                    "categoryDescription": "Pendapatan atau hadiah yang dihasilkan dari pemberian orang lain atau institusi.."
                },
                {
                    "categoryName": "Pinjaman",
                    "categoryDescription": "Uang yang dipinjamkan oleh orang lain atau institusi keuangan kepada peminjam dan akan dikembalikan dengan tambahan bunga maupun tidak."
                },
                {
                    "categoryName": "Piutang",
                    "categoryDescription": "Harta atau uang yang sedang dipinjamkan ke orang lain."
                }
            ]
            """

    const val CATEGORY_INFO_CREDIT_NEW =
        """
            [
  {
    "categoryName": "Penjualan",
    "categoryDescription": "Pemasukan dari hasil penjualan usaha."
  },
  {
    "categoryName": "Pendapatan Jasa/Komisi",
    "categoryDescription": "Pemasukan dari komisi usaha atau jasa"
  },
  {
    "categoryName": "Penambahan Modal",
    "categoryDescription": "Pemasukan yang digunakan untuk modal tambahan usaha kamu."
  },
  {
    "categoryName": "Penagihan Utang/Cicilan",
    "categoryDescription": "Pemasukan dari pengembalian utang atau pembayaran cicilan."
  },
  {
    "categoryName": "Pendapatan Lain-lain",
    "categoryDescription": "Pengembalian uang ke pemberi pinjaman dalam jangka waktu tertentu."
  },
  {
    "categoryName": "Terima Pinjaman",
    "categoryDescription": "Pemasukan dari penerimaan uang pinjaman untuk usaha kamu."
  },
  {
    "categoryName": "Pendapatan Di Luar Usaha",
    "categoryDescription": "Pemasukan pribadi yang tidak berhubungan dengan kegiatan usaha. Contoh: hibah, hadiah, atau sedekah."
  },
  {
    "categoryName": "Pendapatan Lain-lain",
    "categoryDescription": "Pendapatan dari penjualan aset atau barang di luar usahamu."
  }
]
            """

    const val CATEGORY_INFO_DEBIT =
        """
            [
  {
  "categoryName": "Pembelian stok",
    "categoryDescription": "Pembelian barang yang akan dijual kembali ke pelanggan."
},
  {
  "categoryName": "Pembelian bahan baku",
    "categoryDescription": "Pembelian bahan dasar untuk diolah menjadi barang siap jual"
},
  {
  "categoryName": "Biaya operasional",
    "categoryDescription": "Biaya yang digunakan untuk menjalankan kegiatan usaha. Contoh: gaji karyawan, sewa tempat, listrik, hingga internet."
},
  {
  "categoryName": "Beban bisnis lain",
    "categoryDescription": "Pengeluaran tidak rutin yang berkaitan dengan kegiatan usaha. Contoh: bayar pajak tahunan."
},
  {
  "categoryName": "Pembayaran utang",
    "categoryDescription": "Pengembalian uang ke pemberi pinjaman dalam jangka waktu tertentu."
},
  {
  "categoryName": "Pemberian utang",
    "categoryDescription": "Pemberian uang ke orang lain yang dikembalikan pada waktu mendatang sesuai dengan kesepakatan."
},
 {
  "categoryName": "Pengeluaran pribadi dan keluarga",
    "categoryDescription": "Pengeluaran untuk kebutuhan pribadi yang tidak berhubungan dengan kegiatan usaha. Contoh: bayar berobat anak."
},
  {
  "categoryName": "Donasi atau sedekah",
    "categoryDescription": "Pengeluaran berupa sumbangan untuk membantu orang lain atau yayasan."
},
  {
  "categoryName": "Tabungan atau investasi",
    "categoryDescription": "Pengeluaran uang secara tunai untuk mendapatkan keuntungan di masa depan."
}  
]
            """

    const val CATEGORY_INFO_DEBIT_NEW =
        """
            [
  {
    "categoryName": "Pembelian stok",
    "categoryDescription": "Pengeluaran untuk pembelian barang yang akan dijual kembali."
  },
  {
    "categoryName": "Pembelian bahan baku",
    "categoryDescription": "Pembelian bahan dasar yang akan diolah menjadi barang siap jual."
  },
  {
    "categoryName": "Biaya operasional",
    "categoryDescription": "Biaya untuk menjalankan kegiatan usaha. Contoh: sewa tempat, listrik, dan internet."
  },
  {
    "categoryName": "Gaji/Bonus Karyawan",
    "categoryDescription": "Pembayaran upah, gaji, atau bonus karyawan."
  },
  {
    "categoryName": "Pemberian Utang",
    "categoryDescription": "Pengeluaran untuk memberikan pinjaman uang."
  },
  {
    "categoryName": "Pembayaran Utang/Cicilan",
    "categoryDescription": "Pengeluaran usaha untuk membayar utang/cicilan."
  },
  {
    "categoryName": "Pengeluaran Di Luar Usaha",
    "categoryDescription": "Pengeluaran untuk kebutuhan pribadi yang tidak berhubungan dengan kegiatan usaha. Contoh: bayar berobat anak."
  },
  {
    "categoryName": "Pengeluaran Lain-lain",
    "categoryDescription": "Pengeluaran untuk membeli aset atau barang di luar usahamu."
  }
]
            """


    const val NEW_HOME_PAGE_JSON =
        """
  [{
		"title": "Bayar dan Tagih Uang Antar Bank",
		"startImage": "HOMEPAGE_PAYMENT_INTRO",
		"subtitle": "dan hemat biaya admin!",
		"endImage": "",
		"type": 0,
		"position": 1,
		"isActive": true,
		"isEnabled": true,
		"category": "top",
		"isPaymentTile": true,
		"coachmarkNext": 8,
		"startVersion": 0,
		"endVersion": -1,
		"step": 1,
		"maxSteps": 4,
		"coachMarkHead": "\nBayar dan Tagih uang jadi secepat kilat!",
		"coachmarkBody": "Tinggal buka aplikasi, kamu bisa langsung bayar dan tagih pembayaran dengan mudah 😉",
		"body": [{
				"name": "Bayar",
				"icon": "https://i.ibb.co/8KszcpY/Group-162outttttt.png",
				"rank": 1,
				"redirection": "com.bukuwarung.payments.checkout.PaymentCheckoutActivity&paymentType=1&entryPoint=home",
				"deeplink": "",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"button_name": "kirim_uang"
			},
			{
				"name": "Tagih",
				"icon": "https://i.ibb.co/89RybfN/Group-163in.png",
				"rank": 2,
				"redirection": "com.bukuwarung.payments.checkout.PaymentCheckoutActivity&paymentType=0&entryPoint=home",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "tagih_uang"
			},
			{
				"name": "QRIS",
				"icon": "https://i.ibb.co/PrRdLVp/qr-code-1-1-QRIS.png",
				"rank": 3,
				"redirection": "",
				"isActive": true,
				"isEnabled": false,
				"startVersion": 0,
		    "endVersion": -1,
				"deeplink": "",
				"isNew": false,
				"button_name": "qris"
			}
		]
	},
	{
		"title": "Tambah Penghasilan Jual Pulsa & Tagihan 😎",
		"startImage": "HOMEPAGE_PPOB_INTRO",
		"subtitle": "",
		"endImage": "",
		"type": 2,
		"position": 2,
		"isActive": true,
		"isEnabled": true,
		"startVersion": 0,
		"endVersion": -1,
		"category": "ppob",
		"isPaymentTile": true,
		"coachmarkNext": 4,
		"step": 3,
		"maxSteps": 4,
		"coachMarkHead": "\nUntung maksimal jualan Pulsa & Tagihan",
		"coachmarkBody": "Selain itu, bisa jual paket data, token listrik, top up e-wallet, voucher game, serta tagihan listrik dan pulsa.",
		"body": [{
				"name": "Pulsa",
				"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
				"rank": 1,
				"redirection": "com.bukuwarung.payments.ppob.pulsa.view.PpobPulsaActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"deeplink": "",
				"isNew": false,
				"button_name": "pulsa"
			},
			{
				"name": "Token Listrik",
				"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
				"rank": 2,
				"redirection": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"deeplink": "",
				"isNew": false,
				"button_name": "token_listrik"
			},
			{
				"name": "E-Wallet",
				"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
				"rank": 3,
				"redirection": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=EMONEY",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"deeplink": "",
				"isNew": false,
				"button_name": "ewallet"
			},
			{
				"name": "Paket Data",
				"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
				"rank": 4,
				"redirection": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=PAKET_DATA",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"deeplink": "",
				"isNew": false,
				"button_name": "packet_data"
			},
			{
				"name": "Pulsa Pascabayar",
				"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
				"rank": 5,
				"redirection": "com.bukuwarung.payments.ppob.pulsa.view.PostpaidPulsaActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": 3211,
				"deeplink": "",
				"isNew": false,
				"button_name": "pascabayar"
			},
			{
				"name": "Tagihan Listrik",
				"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
				"rank": 6,
				"redirection": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity&selected_tab=1",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "tagihan_listrik"
			}
		]
	},
	{
		"title": "Fitur Andalan Kamu👌",
		"startImage": "HOMEPAGE_ACCOUNTING_INTRO",
		"subtitle": "",
		"endImage": "",
		"type": 2,
		"position": 4,
		"isActive": true,
		"isEnabled": true,
		"startVersion": 0,
		"endVersion": -1,
		"category": "bookkeeping",
		"isPaymentTile": false,
		"coachmarkNext": null,
		"step": 4,
		"maxSteps": 4,
		"coachMarkHead": "\nAda berbagai fitur pembukuan lengkap!",
		"coachmarkBody": "Fitur-fitur ini akan bantu kamu untuk atur pembukuan usaha jadi lebih rapi.",
		"body": [{
				"name": "Catat Utang",
				"icon": "https://i.ibb.co/8c2BnrL/icon-multi-color-utang.webp",
				"rank": 1,
				"redirection": "com.bukuwarung.activities.addcustomer.detail.CustomerActivity&transaction_type=-1",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "catat_utang"
			},
			{
				"name": "Catat Transaksi",
				"icon": "https://i.ibb.co/tPbzxrK/icon-multi-color-transaction.webp",
				"rank": 1,
				"redirection": "com.bukuwarung.activities.expense.NewCashTransactionActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "catat_transaxi"
			},
			{
				"name": "Mode Kasir",
				"icon": "https://i.ibb.co/PTCJqGg/icon-multi-color-cashier-mode.webp",
				"rank": 1,
				"redirection": "com.bukuwarung.activities.pos.PosActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "mode_kasir"
			},
			{
				"name": "Kelola Stok",
				"icon": "https://i.ibb.co/4YYzyr1/icon-multi-color-stock.webp",
				"rank": 1,
				"redirection": "com.bukuwarung.inventory.ui.InventoryActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "kelola_stok"
			}
		]
	},
	{
		"title": "Fitur Menarik Lainnya 😘",
		"startImage": "HOMEPAGE_ADDITIONAL_INTRO",
		"subtitle": "",
		"endImage": "",
		"type": 2,
		"position": 6,
		"isActive": true,
		"isEnabled": true,
		"startVersion": 0,
		"endVersion": -1,
		"category": "lainnya",
		"isPaymentTile": false,
		"coachmarkNext": 4,
		"step": 0,
		"maxSteps": 4,
		"coachMarkHead": "\nUntung maksimal jualan Pulsa & Tagihan",
		"coachmarkBody": "Selain itu, kamu juga bisa jual token listrik, top e-wallet, paket data, tagihan listrik dan pulsa.",
		"body": [{
				"name": "Undang Teman",
				"icon": "https://i.ibb.co/KF2LnjT/icon-multi-color-share.webp",
				"rank": 1,
				"redirection": "com.bukuwarung.activities.referral.payment_referral.ReferralActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "undang_teman"
			},
			{
				"name": "Atur Jadwal Pencatatan",
				"icon": "https://i.ibb.co/P4wtM5n/Frame-2997.png",
				"rank": 2,
				"redirection": "com.bukuwarung.activities.selfreminder.SelfReminderActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "atur_jadwal_pencatatan"
			},
			{
				"name": "Atur Info Nota",
				"icon": "https://i.ibb.co/SfT97Fg/icon-multi-color-receipt.webp",
				"rank": 3,
				"redirection": "com.bukuwarung.activities.invoice.InvoiceSettingActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "atur_info_nota"
			},
			{
				"name": "Buat Kartu Nama",
				"icon": "https://i.ibb.co/HV0dsny/icon-multi-color-biz-card.webp",
				"rank": 4,
				"redirection": "com.bukuwarung.activities.card.newcard.NewBusinessCardActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "buat_kartu_nama"
			},
			{
				"name": "Download Stiker WA",
				"icon": "https://i.ibb.co/d0mJqCF/icon-multi-color-sticker.webp",
				"rank": 5,
				"redirection": "com.bukuwarung.activities.stickers.StickerMainActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "download_sticker_wa"
			}
		]
	},
	{
		"title": "Kirim dan Tagih Uang Antar Bank",
		"startImage": "",
		"subtitle": "",
		"endImage": "",
		"type": 1,
		"position": 3,
		"isActive": true,
		"isEnabled": true,
		"startVersion": 0,
		"endVersion": -1,
		"category": "carousel",
		"isPaymentTile": true,
		"coachmarkNext": null,
		"step": 0,
		"maxSteps": 4,
		"coachMarkHead": "\nAkses fitur utama lengkap di bawah ini",
		"coachmarkBody": "Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
		"body": [{
				"name": "Payment Awareness",
				"icon": "https://i.ibb.co/cYfGPPN/1-Payment-awareness-5x.png",
				"rank": 1,
				"redirection": "",
				"deeplink": "https://bukuwarung.com/fitur-pembayaran/",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"button_name": "payment_awareness"
			},
			{
				"name": "Listrik",
				"icon": "https://i.ibb.co/PQPcmZX/2-Token-Listik-5x.png",
				"rank": 2,
				"redirection": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"deeplink": "",
				"isNew": false,
				"button_name": "listrik"
			},
			{
				"name": "Ewallet",
				"icon": "https://i.ibb.co/qgcn1nc/3-Ewallet-5x.png",
				"rank": 3,
				"redirection": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=EMONEY",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "ewallet"
			},
			{
				"name": "Referral",
				"icon": "https://i.ibb.co/7SFt4Pp/4-Referral-5x.png",
				"rank": 4,
				"redirection": "com.bukuwarung.activities.referral.payment_referral.ReferralActivity",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "",
				"button_name": "referral"
			},
			{
				"name": "Lending",
				"icon": "https://i.ibb.co/4174PcM/Lending-banner-5x.png",
				"rank": 5,
				"redirection": "",
				"isActive": true,
				"isEnabled": true,
				"startVersion": 0,
		    "endVersion": -1,
				"isNew": false,
				"deeplink": "${BuildConfig.LOS_WEB_LENDING_URL}",
				"button_name": "lending"
			}
		]
	},
	{
		"title": "Lakukan Verifikasi Akun Pakai KTP 👌",
		"startImage": "kyc_cta_clicked",
		"subtitle": "Cuma 2 langkah mudah untuk nikmati fitur lebih lengkap",
		"endImage": "kycBlock",
		"type": 3,
		"position": 5,
		"isActive": true,
		"isEnabled": true,
		"startVersion": 0,
		"endVersion": -1,
		"isPaymentTile": true,
		"category": "Fixed Banner",
		"coachmarkNext": null,
		"step": 0,
		"maxSteps": 4,
		"coachMarkHead": "\nAkses fitur utama lengkap di bawah ini",
		"coachmarkBody": "Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
		"body": [{
			"name": "Dapatkan pinjaman modal dengan bunga ringan 🤑",
			"icon": "https://i.ibb.co/wsTB3fG/KYC-banner-5x.png",
			"rank": 1,
			"redirection": "",
			"deeplink": "${BuildConfig.KYC_WEB_URL}",
			"isActive": true,
			"isEnabled": true,
			"startVersion": 0,
		  "endVersion": -1,
			"isNew": false,
			"button_name": "KYC"
		}]
	},
	{
		"title": "Butuh bantuan?",
		"startImage": "",
		"subtitle": "Hubungi CS",
		"endImage": "",
		"type": 4,
		"position": 7,
		"isActive": true,
		"isEnabled": true,
		"startVersion": 0,
		"endVersion": -1,
		"category": "help_section",
		"isPaymentTile": true,
		"coachmarkNext": null,
		"step": 3,
		"maxSteps": 4,
		"coachMarkHead": "\nAkses fitur utama lengkap di bawah ini",
		"coachmarkBody": "Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
		"body": [{
			"name": "",
			"icon": "",
			"rank": 1,
			"redirection": "",
			"deeplink": "",
			"isActive": true,
			"isEnabled": true,
			"startVersion": 0,
		  "endVersion": -1,
			"isNew": false,
			"button_name": ""
		}]
	},
	{
		"title": "",
		"startImage": "HOMEPAGE_BOTTOM_NAVIGATION",
		"subtitle": "",
		"endImage": "",
		"type": -1,
		"position": 8,
		"isActive": true,
		"isEnabled": true,
		"startVersion": 0,
		"endVersion": -1,
		"category": "none",
		"isPaymentTile": false,
		"coachmarkNext": 2,
		"step": 2,
		"maxSteps": 4,
		"coachMarkHead": "\nBisa akses fitur utama di bawah ini, lho!",
		"coachmarkBody": "Nikmati berbagai fitur utama mulai dari pembayaran, catat utang, dan catat transaksi secara lengkap dan cepat di sini.",
		"body": []
	}
]
        """

    const val PRODUCT_CATALOG_DATA = """
        [
            {
              name: "Makanan & Minuman",
              image: "https://assets.tokoko.id/app/groceries.webp",
              subcategory: [
                {
                  name: "Camilan",
                  count: "1564"
                },
                {
                  name: "Makanan Segar",
                  count: "488"
                },
                {
                  name: "Susu Cair & Es Krim",
                  count: "420"
                },
                {
                  name: "Minuman Ringan",
                  count: "381"
                },
                {
                  name: "Minuman Instan",
                  count: "376"
                },
                {
                  name: "Roti",
                  count: "360"
                },
                {
                  name: "Makanan Instan",
                  count: "312"
                },
                {
                  name: "Susu Bubuk",
                  count: "278"
                },
                {
                  name: "Makanan & Minuman Siap Saji",
                  count: "59"
                },
                {
                  name: "Abon & Makanan Tradisional",
                  count: "52"
                },
                {
                  name: "Makanan Anak",
                  count: "45"
                },
                {
                  name: "Makanan & Minuman Impor",
                  count: "24"
                },
                {
                  name: "Permen & Cokelat",
                  count: "2"
                }
              ]
            },
            {
              name: "Kecantikan & Kesehatan",
              image: "https://assets.tokoko.id/app/skincare.webp",
              subcategory: [
                {
                  name: "Riasan Wajah & Tubuh",
                  count: "1276"
                },
                {
                  name: "Perawatan Wajah",
                  count: "733"
                },
                {
                  name: "Perawatan Tubuh",
                  count: "723"
                },
                {
                  name: "Alat Kesehatan & Kecantikan",
                  count: "440"
                },
                {
                  name: "Perawatan Rambut",
                  count: "386"
                },
                {
                  name: "Obat-Obatan",
                  count: "382"
                },
                {
                  name: "Vitamin & Suplemen",
                  count: "369"
                },
                {
                  name: "Parfum",
                  count: "267"
                },
                {
                  name: "Perawatan Gigi & Mulut",
                  count: "151"
                },
                {
                  name: "Peralatan Rias & Tata Rambut",
                  count: "133"
                },
                {
                  name: "Pembalut & Popok Dewasa",
                  count: "127"
                },
                {
                  name: "Perawatan Pria",
                  count: "119"
                }
              ]
            },
            {
              name: "Kebutuhan sehari-hari",
              image: "https://assets.tokoko.id/app/electric-appliance.webp",
              subcategory: [
                {
                  name: "Bahan Masakan & Kue",
                  count: "778"
                },
                {
                  name: "Rokok & Korek",
                  count: "169"
                },
                {
                  name: "Anti Nyamuk",
                  count: "44"
                }
              ]
            }
          ]
    """

    const val QRIS_RETRY_CONFIG_DATA = """
        {
            "initialTime": 120,
            "incrementRate": 3,
            "maxAttempts": 5
        }
    """

    const val MX_MWEB_CONFIG_DATA = """
        {
            "voucherHistoryWebUrl": "${BuildConfig.VOUCHER_WEB_URL}"
        }
    """

    var PAYMENT_CONFIGS_DATA = """
    {
        "saldoBonusUrl": "https://bukuwarung.com/saldo-bonus",
        "showBnplEntrypoint": false,
        "disbursalMaxRetryAttempts": 2,
        "visibleCategoryCount": 3,
        "qrisBankChangeAllowedCount": 1,
        "qrisBankChangeAllowedDays": 1,
        "qrisProcessingTimeFAQ": "Pembayaran ke rekening kamu akan dilakukan pada jam 8 malam setiap harinya mulai Juli 2022.",
        "shouldShowQrisBankWarning": false,
        "customCalendarMaxRange": 31,
        "paymentOutPollingConfig": {
            "paymentOutStatusPollingIntervalSeconds": 5,
            "paymentOutStatusPollingTotalTimeSeconds": 30
        },
        "qrisCoachMarks": [
            {
                "title": "Transaksi pakai QRIS, dapet saldo Rp10.000!",
                "message": "Pelanggan wajib lakukan transaksi minimal Rp50.000 biar kamu dapat bonus <b>1.000 BukuPoin</b> yang setara dengan Rp10.000.",
                "index": 1,
                "id": "TUTORIAL_QRIS_INTRODUCTION_1"
            },
            {
                "title": "Yuk, cetak dan bagikan QRIS-mu sekarang!",
                "message": "Sebarkan QRIS tokomu ke media sosial dan cetak biar pelanggan bisa bayar pakai QRIS.",
                "index": 2,
                "id": "TUTORIAL_QRIS_INTRODUCTION_2"
            }
        ],
        "fileSizeLimits": {
            "KTP": 2097152,
            "SELFIE": 5242880,
            "VIDEO": 10485760,
            "MH_TICKET_PDF": 2097152,
            "MH_TICKET_IMAGE": 921600,
            "OTHERS": 2097152
        },
        "videoQuality": "sd",
        "compressionQuality": "medium",
        "historyFilters": [
            {
                "label": "Hari ini",
                "presetValue": 0
            },{
                "label": "Kemarin",
                "presetValue": 1
            },{
                "label": "Minggu ini",
                "presetValue": 2
            },{
                "label": "Bulan ini",
                "presetValue": 4
            },{
                "label": "Tahun ini",
                "presetValue": 6
            }
        ],
        "historyFiltersNew": {
            "all": {
                "status": [
                    {
                        "sectionLabel": "Pembayaran",
                        "filters": [
                            {
                                "key": "${PaymentHistory.STATUS_PENDING}",
                                "label": "Menunggu"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_PAID}",
                                "label": "Dalam Proses"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_COMPLETED}",
                                "label": "Berhasil"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_FAILED}",
                                "label": "Gagal"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_EXPIRED}",
                                "label": "Kedaluwarsa"
                            }
                        ]
                    },
                    {
                        "sectionLabel": "Refund",
                        "filters": [
                            {
                                "key": "${PaymentHistory.STATUS_REFUNDING}",
                                "label": "Proses Refund"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_REFUNDED}",
                                "label": "Refund Berhasil"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_REFUNDING_FAILED}",
                                "label": "Refund Gagal"
                            }
                        ]
                    }
                ],
                "products": [
                    {
                        "sectionLabel": "Pembayaran",
                        "filters": [
                            {
                                "key": "${PaymentConst.TYPE_PAY_IN}",
                                "label": "Tagih"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PAY_OUT}",
                                "label": "Bayar"
                            },
                            {
                                "key": "${PaymentConst.TYPE_QRIS}",
                                "label": "QRIS"
                            }
                        ]
                    },
                    {
                        "sectionLabel": "PPOB",
                        "filters": [
                            {
                                "key": "${PaymentConst.TYPE_PULSA}",
                                "label": "Pulsa"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PULSA}",
                                "label": "Pascabayar"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PAKET_DATA}",
                                "label": "Paket Data"
                            },
                            {
                                "key": "${PaymentConst.TYPE_LISTRIK}",
                                "label": "Tagihan & Token Listrik"
                            },
                            {
                                "key": "${PaymentConst.TYPE_EMONEY}",
                                "label": "Top Up E-Wallet"
                            },
                            {
                                "key": "${PaymentConst.TYPE_VOUCHER_GAME}",
                                "label": "Voucher Game"
                            },
                            {
                                "key": "${PaymentConst.TYPE_BPJS}",
                                "label": "BPJS"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PDAM}",
                                "label": "PDAM"
                            },
                            {
                                "key": "${PaymentConst.TYPE_ANGSURAN}",
                                "label": "Angsuran Kredit"
                            },
                            {
                                "key": "${PaymentConst.TYPE_INTERNET_DAN_TV_KABEL}",
                                "label": "Internet TV Kabel"
                            },
                            {
                                "key": "${PaymentConst.TYPE_VEHICLE_TAX}",
                                "label": "E-Samsat"
                            },
                            {
                                "key": "${PaymentConst.TYPE_TRAIN_TICKET}",
                                "label": "Kereta Api"
                            }
                        ]
                    },
                    {
                        "sectionLabel": "Saldo",
                        "filters": [
                            {
                                "key": "${PaymentHistory.TYPE_SALDO_IN}",
                                "label": "Saldo Masuk"
                            },
                            {
                                "key": "${PaymentHistory.TYPE_SALDO_OUT}",
                                "label": "Saldo Keluar"
                            },
                            {
                                "key": "${PaymentHistory.TYPE_SALDO_BNPL}",
                                "label": "Saldo Talangin Dulu"
                            }
                        ]
                    },
                    {
                        "sectionLabel": "Lainnya",
                        "filters": [
                            {
                                "key": "${PaymentHistory.TYPE_SALDO_REDEMPTION}",
                                "label": "Cashback"
                            }
                        ]
                    }
                ],
                "date": [
                    {
                        "label": "Hari ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.TODAY}"
                    },{
                        "label": "7 hari terakhir",
                        "presetValue": "${PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS}"
                    },{
                        "label": "Bulan ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.THIS_MONTH}"
                    },{
                        "label": "Atur tanggal",
                        "presetValue": "${PaymentConst.DATE_PRESET.CUSTOM_RANGE}"
                    }
                ]
            },
            "ppob": {
                "key": "PRODUK_DIGITAL",
                "status": [
                    {
                        "sectionLabel": "Pembayaran",
                        "filters": [
                            {
                                "key": "${PaymentHistory.STATUS_PENDING}",
                                "label": "Menunggu"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_PAID}",
                                "label": "Dalam Proses"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_COMPLETED}",
                                "label": "Berhasil"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_FAILED}",
                                "label": "Gagal"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_EXPIRED}",
                                "label": "Kedaluwarsa"
                            }
                        ]
                    },
                    {
                        "sectionLabel": "Refund",
                        "filters": [
                            {
                                "key": "${PaymentHistory.STATUS_REFUNDING}",
                                "label": "Proses Refund"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_REFUNDED}",
                                "label": "Refund Berhasil"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_REFUNDING_FAILED}",
                                "label": "Refund Gagal"
                            }
                        ]
                    }
                ],
                "products": [
                    {
                        "sectionLabel": "PPOB",
                        "filters": [
                            {
                                "key": "${PaymentConst.TYPE_PULSA}",
                                "label": "Pulsa"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PULSA_POSTPAID}",
                                "label": "Pascabayar"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PAKET_DATA}",
                                "label": "Paket Data"
                            },
                            {
                                "key": "${PaymentConst.TYPE_LISTRIK}",
                                "label": "Tagihan & Token Listrik"
                            },
                            {
                                "key": "${PaymentConst.TYPE_EMONEY}",
                                "label": "Top Up E-Wallet"
                            },
                            {
                                "key": "${PaymentConst.TYPE_VOUCHER_GAME}",
                                "label": "Voucher Game"
                            },
                            {
                                "key": "${PaymentConst.TYPE_BPJS}",
                                "label": "BPJS"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PDAM}",
                                "label": "PDAM"
                            },
                            {
                                "key": "${PaymentConst.TYPE_ANGSURAN}",
                                "label": "Angsuran Kredit"
                            },
                            {
                                "key": "${PaymentConst.TYPE_INTERNET_DAN_TV_KABEL}",
                                "label": "Internet TV Kabel"
                            },
                            {
                                "key": "${PaymentConst.TYPE_VEHICLE_TAX}",
                                "label": "E-Samsat"
                            }
                        ]
                    }
                ],
                "date": [
                    {
                        "label": "Hari ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.TODAY}"
                    },{
                        "label": "7 hari terakhir",
                        "presetValue": "${PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS}"
                    },{
                        "label": "Bulan ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.THIS_MONTH}"
                    },{
                        "label": "Atur tanggal",
                        "presetValue": "${PaymentConst.DATE_PRESET.CUSTOM_RANGE}"
                    }
                ]
            },
            "pembayaran": {
                "key": "PEMBAYARAN",
                "status": [
                    {
                        "sectionLabel": "Pembayaran",
                        "filters": [
                            {
                                "key": "${PaymentHistory.STATUS_PENDING}",
                                "label": "Menunggu"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_PAID}",
                                "label": "Dalam Proses"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_COMPLETED}",
                                "label": "Berhasil"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_FAILED}",
                                "label": "Gagal"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_EXPIRED}",
                                "label": "Kedaluwarsa"
                            }
                        ]
                    }
                ],
                "products": [
                    {
                        "sectionLabel": "Pembayaran",
                        "filters": [
                            {
                                "key": "${PaymentConst.TYPE_PAY_IN}",
                                "label": "Tagih"
                            },
                            {
                                "key": "${PaymentConst.TYPE_PAY_OUT}",
                                "label": "Bayar"
                            },
                            {
                                "key": "${PaymentConst.TYPE_QRIS}",
                                "label": "QRIS"
                            }
                        ]
                    }
                ],
                "date": [
                    {
                        "label": "Hari ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.TODAY}"
                    },{
                        "label": "7 hari terakhir",
                        "presetValue": "${PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS}"
                    },{
                        "label": "Bulan ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.THIS_MONTH}"
                    },{
                        "label": "Atur tanggal",
                        "presetValue": "${PaymentConst.DATE_PRESET.CUSTOM_RANGE}"
                    }
                ]
            },
            "saldo": {
                "key": "SALDO",
                "status": [
                    {
                        "sectionLabel": "Pembayaran",
                        "filters": [
                            {
                                "key": "${PaymentHistory.STATUS_PENDING}",
                                "label": "Menunggu"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_COMPLETED}",
                                "label": "Berhasil"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_FAILED}",
                                "label": "Gagal"
                            }
                        ]
                    }
                ],
                "products": [
                    {
                        "sectionLabel": "Saldo",
                        "filters": [
                            {
                                "key": "${PaymentHistory.TYPE_SALDO_IN}",
                                "label": "Saldo Masuk"
                            },
                            {
                                "key": "${PaymentHistory.TYPE_SALDO_OUT}",
                                "label": "Saldo Keluar"
                            },
                            {
                                "key": "${PaymentHistory.TYPE_SALDO_BNPL}",
                                "label": "Saldo Talangin Dulu"
                            }
                        ]
                    }
                ],
                "date": [
                    {
                        "label": "Hari ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.TODAY}"
                    },{
                        "label": "7 hari terakhir",
                        "presetValue": "${PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS}"
                    },{
                        "label": "Bulan ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.THIS_MONTH}"
                    },{
                        "label": "Atur tanggal",
                        "presetValue": "${PaymentConst.DATE_PRESET.CUSTOM_RANGE}"
                    }
                ]
            },
            "cashback": {
                "key": "CASHBACK",
                "status": [
                    {
                        "sectionLabel": "Pembayaran",
                        "filters": [
                            {
                                "key": "${PaymentHistory.STATUS_PAID}",
                                "label": "Dalam Proses"
                            },
                            {
                                "key": "${PaymentHistory.STATUS_COMPLETED}",
                                "label": "Berhasil"
                            }
                        ]
                    }
                ],
                "products": [
                    
                ],
                "date": [
                    {
                        "label": "Hari ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.TODAY}"
                    },{
                        "label": "7 hari terakhir",
                        "presetValue": "${PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS}"
                    },{
                        "label": "Bulan ini",
                        "presetValue": "${PaymentConst.DATE_PRESET.THIS_MONTH}"
                    },{
                        "label": "Atur tanggal",
                        "presetValue": "${PaymentConst.DATE_PRESET.CUSTOM_RANGE}"
                    }
                ],
                "sort": [
                    {
                        "key": "${PaymentConst.SORT_BY_EXPIRY_DSC}",
                        "label": "Tanggal Kedaluwarsa Terdekat"
                    },{
                        "key": "${PaymentConst.SORT_BY_CREDIT_TIME_DSC}",
                        "label": "Tanggal Masuk Terbaru"
                    }
                ]
            }
        }
    }
"""

    const val APP_UPDATE_VERSION_CODE_JSON = """
  {
    "hardUpdateVersionCode": 0,
    "softUpdateVersionCode": 0
  }
    """

    const val BUSINESS_DASHBOARD_JSON = """
        [
  {
    "cardId": "accounting_transaction",
    "order": 1
  },
  {
    "cardId": "modal",
    "order": 2
  },
  {
    "cardId": "inventory",
    "order": 3
  },
  {
    "cardId": "utang",
    "order": 4
  },
  {
    "cardId": "payment",
    "order": 5
  },
  {
    "cardId": "PPOB",
    "order": 6
  },
  {
    "cardId": "brick_autorecord",
    "order": 7
  }
]
    """
    const val LAINNYA_PROGRESS_BAR_JSON = """
        [
  {
    "type" : "user_profile",
    "rank": 0,
    "itemTitle": "Info profil kamu",
    "emptySubtitle" : "",
    "inProgessSubtitle" : "Lengkapi nama, alamat email, jenis kelamin dan tanggal lahir.",
    "successSubtitle" : "Semua info profil kamu sekarang udah lengkap. Mantap gan👍",
    "failedTitle" : "",
    "successChipText" : "Lengkap",
    "showOrHide" : true,
    "deeplinkUrl": "",
    "imageUrl" : "https://i.ibb.co/4tS94Lp/Group-7217ic-user-profile.png"
  },
  {
    "type" : "user_business_profile",
    "rank": 1,
    "itemTitle": "Info Usaha kamu",
    "emptySubtitle" : "",
    "inProgessSubtitle" : "Lengkapi alamat usaha, jam buka, dan detail usaha.",
    "successSubtitle" : "Yey! Kamu berhasil lengkapi semua info usaha kamu.",
    "failedTitle" : "",
    "successChipText" : "Lengkap",
    "showOrHide" : true,
    "deeplinkUrl": "",
    "imageUrl" : "https://i.ibb.co/DtJbtt7/resume-2ic-business-profile.png"
  },
  {
    "type" : "user_kyc_complete",
    "rank": 2,
    "itemTitle": "Verifikasi data diri",
    "emptySubtitle" : "Biar bayar & tagih jadi lebih cepat, mudah, dan aman.",
    "inProgessSubtitle" : "Submit verifikasi berhasil. Data kamu sedang di review",
    "successSubtitle" : "Hore! Sekarang udah bisa bayar & tagih dengan cepat dan aman.",
    "failedTitle" : "Proses verifikasi kamu tidak berhasil. Silahkan lakukan ulang",
    "successChipText" : "Verifikasi Berhasil",
    "showOrHide" : true,
    "deeplinkUrl": "${BuildConfig.KYC_WEB_URL}",
    "imageUrl" : "https://i.ibb.co/zZk3VY6/Group-7211ic-kyc.png"
  },
  {
    "type" : "bank_account",
    "rank": 3,
    "itemTitle": "Tambahkan rekening",
    "emptySubtitle" : "Biar transaksi usaha kamu jadi lebih nyaman.",
    "inProgessSubtitle" : "",
    "successSubtitle" : "Keren! Sekarang pembayaranmu akan lebih mudah pake rekening.",
    "failedTitle" : "",
    "successChipText" : "Tambah rekening berhasil",
    "showOrHide" : true,
    "deeplinkUrl": "",
    "imageUrl" : "https://i.ibb.co/88b5ffg/resume-2ic-ba.png"
  },
  { 
    "type" : "brick_account",
    "rank":4,
    "itemTitle": "Aktivasi catat otomatis",
    "emptySubtitle" : "Transaksi penjualan online yg masuk, tercatat otomatis",
    "inProgessSubtitle" : "",
    "successSubtitle" : "Cihuy! Kamu udah bisa pantau semua transaksi Catat Otomatis.",
    "failedTitle" : "",
    "successChipText" : "Aktivasi catat otomatis berhasil",
    "showOrHide" : true,
    "deeplinkUrl": "",
    "imageUrl" : "https://i.ibb.co/287xpdd/Group-7196bi.png"
  }
]
    """

    const val HOMEPAGE_JSON_SALDO = """
[    
    {
      "id":"top_block",
      "title":"Bayar dan Tagih Uang Antar Bank",
      "subtitle":"dan hemat biaya admin!",
      "category":"top",
      "layout_type":0,
      "rank":1,
      "analytics_block_name":"HOMEPAGE_PAYMENT_INTRO",
      "coachmark_header":"\nBayar dan Tagih uang jadi secepat kilat!",
      "coachmark_body":"Tinggal buka aplikasi, kamu bisa langsung bayar dan tagih pembayaran dengan mudah 😉",
      "coachmark_step":1,
      "coachmark_max_steps":4,
      "coachmark_next":10,
      "body_block_name":"top_body_new",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"ppob_block",
      "title":"Tambah Penghasilan Jual Pulsa & Tagihan 😎",
      "subtitle":"",
      "category":"ppob",
      "layout_type":2,
      "rank":3,
      "analytics_block_name":"HOMEPAGE_PPOB_INTRO",
      "coachmark_header":"\nUntung maksimal jualan Pulsa & Tagihan",
      "coachmark_body":"Selain itu, bisa jual paket data, token listrik, top up e-wallet, voucher game, serta tagihan listrik dan pulsa.",
      "coachmark_step":3,
      "coachmark_max_steps":4,
      "coachmark_next":6,
      "body_block_name":"ppob_body_new",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"bookkeeping_block",
      "title":"Fitur Andalan Kamu👌",
      "subtitle":"",
      "category":"bookkeeping",
      "layout_type":2,
      "rank":6,
      "analytics_block_name":"HOMEPAGE_ACCOUNTING_INTRO",
      "coachmark_header":"\nAda berbagai fitur pembukuan lengkap!",
      "coachmark_body":"Fitur-fitur ini akan bantu kamu untuk atur pembukuan usaha jadi lebih rapi.",
      "coachmark_step":4,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"bookkeeping_body_new",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"lainnya_block",
      "title":"Fitur Menarik Lainnya 😘",
      "subtitle":"",
      "category":"lainnya",
      "layout_type":2,
      "rank":8,
      "analytics_block_name":"HOMEPAGE_ADDITIONAL_INTRO",
      "coachmark_header":"\nUntung maksimal jualan Pulsa & Tagihan",
      "coachmark_body":"Selain itu, kamu juga bisa jual token listrik, top e-wallet, paket data, tagihan listrik dan pulsa.",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"lainnya_body_new",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"carousel_block",
      "title":"Kirim dan Tagih Uang Antar Bank",
      "subtitle":"",
      "category":"carousel",
      "layout_type":1,
      "rank":5,
      "analytics_block_name":"",
      "coachmark_header":"\nAkses fitur utama lengkap di bawah ini",
      "coachmark_body":"Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"carousel_body_new",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"fixed_banner_block",
      "title":"Lakukan Verifikasi Akun Pakai KTP 👌",
      "subtitle":"Cuma 2 langkah mudah untuk nikmati fitur lebih lengkap",
      "category":"Fixed Banner",
      "layout_type":3,
      "rank":7,
      "analytics_block_name":"kyc_cta_clicked",
      "coachmark_header":"\nAkses fitur utama lengkap di bawah ini",
      "coachmark_body":"Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"fixed_banner_body_new",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"help_section_block",
      "title":"Butuh bantuan?",
      "subtitle":"Hubungi CS",
      "category":"help_section",
      "layout_type":4,
      "rank":9,
      "analytics_block_name":"",
      "coachmark_header":"\nAkses fitur utama lengkap di bawah ini",
      "coachmark_body":"Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
      "coachmark_step":3,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"help_section_body_new",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"bottom_navigation_block",
      "title":"",
      "subtitle":"",
      "category":"none",
      "layout_type":-1,
      "rank":10,
      "analytics_block_name":"HOMEPAGE_BOTTOM_NAVIGATION",
      "coachmark_header":"\nBisa akses fitur utama di bawah ini, lho!",
      "coachmark_body":"Nikmati berbagai fitur utama mulai dari pembayaran, catat utang, dan catat transaksi secara lengkap dan cepat di sini.",
      "coachmark_step":2,
      "coachmark_max_steps":4,
      "coachmark_next":3,
      "body_block_name": null,
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"saldo_block",
      "title":"",
      "subtitle":"",
      "category":"none",
      "layout_type":5,
      "rank":2,
      "analytics_block_name":"HOMEPAGE_SALDO",
      "coachmark_header":"",
      "coachmark_body":"",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name": null,
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"loyalty_block",
      "title":"",
      "subtitle":"",
      "category":"none",
      "layout_type":6,
      "rank":4,
      "analytics_block_name":"HOMEPAGE_LOYALTY",
      "coachmark_header":"",
      "coachmark_body":"",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next": null,
      "body_block_name": null,
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"ticker_block",
      "title":"",
      "subtitle":"",
      "category":"ticker",
      "layout_type":-2,
      "rank":0,
      "analytics_block_name":"HOMEPAGE_TICKER",
      "coachmark_header":"",
      "coachmark_body":"",
      "coachmark_step": 0,
      "coachmark_max_steps":4,
      "coachmark_next": null,
      "body_block_name":null,
      "enabled":true,
      "start_version":0,
      "end_version":-1
   }
]
    """

    const val HOMEPAGE_JSON = """
        [    
    {
      "id":"top_block",
      "title":"Bayar dan Tagih Uang Antar Bank",
      "subtitle":"dan hemat biaya admin!",
      "category":"top",
      "layout_type":0,
      "rank":1,
      "analytics_block_name":"HOMEPAGE_PAYMENT_INTRO",
      "coachmark_header":"\nBayar dan Tagih uang jadi secepat kilat!",
      "coachmark_body":"Tinggal buka aplikasi, kamu bisa langsung bayar dan tagih pembayaran dengan mudah 😉",
      "coachmark_step":1,
      "coachmark_max_steps":4,
      "coachmark_next":8,
      "body_block_name":"top_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"ppob_block",
      "title":"Tambah Penghasilan Jual Pulsa & Tagihan 😎",
      "subtitle":"",
      "category":"ppob",
      "layout_type":2,
      "rank":2,
      "analytics_block_name":"HOMEPAGE_PPOB_INTRO",
      "coachmark_header":"\nUntung maksimal jualan Pulsa & Tagihan",
      "coachmark_body":"Selain itu, bisa jual paket data, token listrik, top up e-wallet, voucher game, serta tagihan listrik dan pulsa.",
      "coachmark_step":3,
      "coachmark_max_steps":4,
      "coachmark_next":4,
      "body_block_name":"ppob_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"bookkeeping_block",
      "title":"Fitur Andalan Kamu👌",
      "subtitle":"",
      "category":"bookkeeping",
      "layout_type":2,
      "rank":4,
      "analytics_block_name":"HOMEPAGE_ACCOUNTING_INTRO",
      "coachmark_header":"\nAda berbagai fitur pembukuan lengkap!",
      "coachmark_body":"Fitur-fitur ini akan bantu kamu untuk atur pembukuan usaha jadi lebih rapi.",
      "coachmark_step":4,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"bookkeeping_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"lainnya_block",
      "title":"Fitur Menarik Lainnya 😘",
      "subtitle":"",
      "category":"lainnya",
      "layout_type":2,
      "rank":6,
      "analytics_block_name":"HOMEPAGE_ADDITIONAL_INTRO",
      "coachmark_header":"\nUntung maksimal jualan Pulsa & Tagihan",
      "coachmark_body":"Selain itu, kamu juga bisa jual token listrik, top e-wallet, paket data, tagihan listrik dan pulsa.",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next":4,
      "body_block_name":"lainnya_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"carousel_block",
      "title":"Kirim dan Tagih Uang Antar Bank",
      "subtitle":"",
      "category":"carousel",
      "layout_type":1,
      "rank":3,
      "analytics_block_name":"",
      "coachmark_header":"\nAkses fitur utama lengkap di bawah ini",
      "coachmark_body":"Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"carousel_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"fixed_banner_block",
      "title":"Lakukan Verifikasi Akun Pakai KTP 👌",
      "subtitle":"Cuma 2 langkah mudah untuk nikmati fitur lebih lengkap",
      "category":"Fixed Banner",
      "layout_type":3,
      "rank":5,
      "analytics_block_name":"kyc_cta_clicked",
      "coachmark_header":"\nAkses fitur utama lengkap di bawah ini",
      "coachmark_body":"Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
      "coachmark_step":0,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"fixed_banner_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"help_section_block",
      "title":"Butuh bantuan?",
      "subtitle":"Hubungi CS",
      "category":"help_section",
      "layout_type":4,
      "rank":7,
      "analytics_block_name":"",
      "coachmark_header":"\nAkses fitur utama lengkap di bawah ini",
      "coachmark_body":"Kamu bisa lihat detail catatan utang, transaksi, dan riwayat pembayaran di sini. Data kamu dijamin tersimpan dengan aman 😘",
      "coachmark_step":3,
      "coachmark_max_steps":4,
      "coachmark_next":null,
      "body_block_name":"help_section_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
      "id":"bottom_navigation_block",
      "title":"",
      "subtitle":"",
      "category":"none",
      "layout_type":-1,
      "rank":8,
      "analytics_block_name":"HOMEPAGE_BOTTOM_NAVIGATION",
      "coachmark_header":"\nBisa akses fitur utama di bawah ini, lho!",
      "coachmark_body":"Nikmati berbagai fitur utama mulai dari pembayaran, catat utang, dan catat transaksi secara lengkap dan cepat di sini.",
      "coachmark_step":2,
      "coachmark_max_steps":4,
      "coachmark_next":2,
      "body_block_name":"dummy_data_body",
      "enabled":true,
      "start_version":0,
      "end_version":-1
   },
   {
    "id": "onboarding_campaign_block",
    "title": "",
    "subtitle": "",
    "category": "homepage_onbaording_campaign",
    "layout_type": 7,
    "rank": 5,
    "analytics_block_name": "HOMEPAGE_ONBOARDING",
    "coachmark_header": "",
    "coachmark_body": "",
    "coachmark_step": 0,
    "coachmark_max_steps": 4,
    "coachmark_next": null,
    "body_block_name": "onboarding_campaign_block",
    "enabled": true,
    "start_version": 0,
    "end_version": -1
  }
]
    """

    const val TOP_BLOCK_BODY = """
    {
"body_block_name": "top_body_new",
"title": "Bayar dan Tagih Uang Antar Bank",
"subtitle": "dan hemat biaya admin!",
"data": [
{
"display_name":"Bayar",
"analytics_name": ${PaymentConst.PAY_OUT_HANDLING_KEY},
"rank": 1,
"icon": "https://i.ibb.co/8KszcpY/Group-162outttttt.png",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.checkout.PaymentCheckoutActivity&paymentType=1&entryPoint=home",
"deeplinkAppNeuro":"bukuwarung://launch/payment_checkout?paymentType=OUT&entryPoint=home&from=home",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Tagih",
"analytics_name": ${PaymentConst.PAY_IN_HANDLING_KEY},
"rank": 2,
"icon": "https://i.ibb.co/89RybfN/Group-163in.png",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.checkout.PaymentCheckoutActivity&paymentType=0&entryPoint=home",
"deeplinkAppNeuro":"bukuwarung://launch/payment_checkout?paymentType=IN&entryPoint=home&from=home",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"QRIS",
"analytics_name": ${PaymentConst.QRIS_HANDLING_KEY},
"rank": 3,
"icon": "https://i.ibb.co/PrRdLVp/qr-code-1-1-QRIS.png",
"coming_soon": false,
"is_new": false,
"deeplink_app": "",
"deeplink_web": "",
"start_version": 0,
"end_version": -1,
"redirection_handler": ${PaymentConst.PAYMENT_HP_HANDLER}
}
]
}
    """

    const val PPOB_BLOCK_BODY = """
        {
"body_block_name": "ppob_body_new",
"title": "Tambah Penghasilan Jual Pulsa & Tagihan 😎",
"subtitle": “”,
"data": [
{
"display_name":"Pulsa",
"analytics_name": "pulsa",
"rank": 1,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PpobPulsaActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Token Listrik",
"analytics_name": "token_listrik",
"rank": 2,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"E-Wallet",
"analytics_name": "ewallet",
"rank": 3,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=EMONEY",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Paket Data",
"analytics_name": "packet_data",
"rank": 4,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=PAKET_DATA",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Pulsa Pascabayar",
"analytics_name": "pascabayar",
"rank": 5,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PostpaidPulsaActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Tagihan Listrik",
"analytics_name": "tagihan_listrik",
"rank": 6,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity&selected_tab=1",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"BPJS",
"analytics_name": "bpjs",
"rank": 7,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.bpjs.view.PpobBpjsActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"PDAM",
"analytics_name": "pdam",
"rank": 8,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.payments.ppob.waterbills.view.PpobWaterBillsActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
}
]
}
    """

    const val BOOKKEEPING_BLOCK_BODY = """
        {
"body_block_name": "bookkeeping_body_new",
"title": "Fitur Andalan Kamu👌",
"subtitle": "",
"data": [
{
"display_name":"Catat Utang",
"analytics_name": "catat_utang",
"rank": 1,
"icon": "https://i.ibb.co/8c2BnrL/icon-multi-color-utang.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.activities.addcustomer.detail.CustomerActivity&transaction_type=-1",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Catat Pembukuan",
"analytics_name": "catat_transaxi",
"rank": 2,
"icon": "https://i.ibb.co/tPbzxrK/icon-multi-color-transaction.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.activities.expense.NewCashTransactionActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Mode Kasir",
"analytics_name": "mode_kasir",
"rank": 3,
"icon": "https://i.ibb.co/PTCJqGg/icon-multi-color-cashier-mode.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.activities.pos.PosActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Kelola Stok",
"analytics_name": "kelola_stok",
"rank": 4,
"icon": "https://i.ibb.co/4YYzyr1/icon-multi-color-stock.webp",
"coming_soon": false,
"is_new": false,
"deeplink_app": "com.bukuwarung.inventory.ui.InventoryActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
}
]
}
        """

    const val FIXED_BANNER_BLOCK_BODY = """
        
        {
"body_block_name": "fixed_banner_body_new",
"title": "Lakukan Verifikasi Akun Pakai KTP 👌",
"subtitle": "Cuma 2 langkah mudah untuk nikmati fitur lebih lengkap",
"data": [
{
"display_name":"Dapatkan pinjaman modal dengan bunga ringan 🤑",
"analytics_name": "KYC",
"rank": 1,
"icon": "https://i.ibb.co/wsTB3fG/KYC-banner-5x.png",
"coming_soon": false,
"is_new": false,
"deeplink_app": "",
"deeplink_web": "${BuildConfig.KYC_WEB_URL}",
"start_version": 0,
"end_version": -1
}
]
}    
        """

    const val HELP_BLOCK_BODY = """
        {
"body_block_name":"help_section_body_new",
"title": "Butuh bantuan?",
"subtitle": "Hubungi CS",
"data": [
{
"display_name":"",
"analytics_name": "",
"rank": 1,
"icon": "",
"coming_soon": false,
"is_new": false,
"deeplink_app": "",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
}
]
}
        """

    const val TOP_BLOCK_BODY_NEW = """
    {
"body_block_name": "top_body_new",
"title": "Bayar dan Tagih Uang Antar Bank",
"subtitle": "dan hemat biaya admin!",
"data": [
{
"display_name":"Bayar",
"analytics_name": ${PaymentConst.PAY_OUT_HANDLING_KEY},
"rank": 1,
"icon": "https://i.ibb.co/8KszcpY/Group-162outttttt.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.checkout.PaymentCheckoutActivity&paymentType=1&entryPoint=home",
"deeplinkAppNeuro":"bukuwarung://launch/payment_checkout?paymentType=OUT&entryPoint=home&from=home",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Tagih",
"analytics_name": ${PaymentConst.PAY_IN_HANDLING_KEY},
"rank": 2,
"icon": "https://i.ibb.co/89RybfN/Group-163in.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.checkout.PaymentCheckoutActivity&paymentType=0&entryPoint=home",
"deeplinkAppNeuro":"bukuwarung://launch/payment_checkout?paymentType=IN&entryPoint=home&from=home",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"QRIS",
"analytics_name": ${PaymentConst.QRIS_HANDLING_KEY},
"rank": 3,
"icon": "https://i.ibb.co/PrRdLVp/qr-code-1-1-QRIS.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "",
"deeplink_web": "",
"start_version": 0,
"end_version": -1,
"redirection_handler": ${PaymentConst.PAYMENT_HP_HANDLER}
}
]
}
    """

    const val PPOB_BLOCK_BODY_NEW = """
        {
"body_block_name": "ppob_body_new",
"title": "Tambah Penghasilan Jual Pulsa & Tagihan 😎",
"subtitle": “”,
"data": [
{
"display_name":"Pulsa",
"analytics_name": "pulsa",
"rank": 1,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PpobPulsaActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Token Listrik",
"analytics_name": "token_listrik",
"rank": 2,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"E-Wallet",
"analytics_name": "ewallet",
"rank": 3,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
"coming_soon": false,
"is_new": false,
"is_promo": true,
"deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=EMONEY",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Paket Data",
"analytics_name": "packet_data",
"rank": 4,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=PAKET_DATA",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Pulsa Pascabayar",
"analytics_name": "pascabayar",
"rank": 5,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
"coming_soon": false,
"is_new": false,
"is_promo": true,
"deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PostpaidPulsaActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Tagihan Listrik",
"analytics_name": "tagihan_listrik",
"rank": 6,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listirk.webp",
"coming_soon": false,
"is_new": false,
"is_promo": true,
"deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity&selected_tab=1",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"BPJS",
"analytics_name": "bpjs",
"rank": 7,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.ppob.bpjs.view.PpobBpjsActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"PDAM",
"analytics_name": "pdam",
"rank": 8,
"icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.ppob.waterbills.view.PpobWaterBillsActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
}
]
}
    """

    const val BOOKKEEPING_BLOCK_BODY_NEW = """
        {
"body_block_name": "bookkeeping_body_new",
"title": "Fitur Andalan Kamu👌",
"subtitle": "",
"data": [
{
"display_name":"Catat Utang",
"analytics_name": "catat_utang",
"rank": 1,
"icon": "https://i.ibb.co/8c2BnrL/icon-multi-color-utang.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.activities.addcustomer.detail.CustomerActivity&transaction_type=-1",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Catat Pembukuan",
"analytics_name": "catat_transaxi",
"rank": 2,
"icon": "https://i.ibb.co/tPbzxrK/icon-multi-color-transaction.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.activities.expense.NewCashTransactionActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Mode Kasir",
"analytics_name": "mode_kasir",
"rank": 3,
"icon": "https://i.ibb.co/PTCJqGg/icon-multi-color-cashier-mode.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.activities.pos.PosActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Kelola Stok",
"analytics_name": "kelola_stok",
"rank": 4,
"icon": "https://i.ibb.co/4YYzyr1/icon-multi-color-stock.webp",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.inventory.ui.InventoryActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
}
]
}
        """

    const val LAINNYA_BLOCK_BODY_NEW = """
{
	"body_block_name": "lainnya_body_new",
	"title": "Fitur Menarik Lainnya 😘",
	"subtitle": "",
	"data": [{
			"display_name": "Undang Teman",
			"analytics_name": "undang_teman",
			"rank": 1,
			"icon": "https://i.ibb.co/KF2LnjT/icon-multi-color-share.webp",
			"coming_soon": false,
			"is_new": false,
            "is_promo": false,
			"deeplink_app": "com.bukuwarung.activities.referral.payment_referral.ReferralActivity",
			"deeplink_web": "",
			"start_version": 0,
			"end_version": -1
		},
		{
			"display_name": "Atur Jadwal Pencatatan",
			"analytics_name": "atur_jadwal_pencatatan",
			"rank": 2,
			"icon": "https://i.ibb.co/P4wtM5n/Frame-2997.png",
			"coming_soon": false,
			"is_new": false,
            "is_promo": false,
			"deeplink_app": "com.bukuwarung.activities.selfreminder.SelfReminderActivity",
			"deeplink_web": "",
			"start_version": 0,
			"end_version": -1
		},
		{
			"display_name": "Atur Info Nota",
			"analytics_name": "atur_info_nota",
			"rank": 3,
			"icon": "https://i.ibb.co/SfT97Fg/icon-multi-color-receipt.webp",
			"coming_soon": false,
			"is_new": false,
            "is_promo": false,
			"deeplink_app": "com.bukuwarung.activities.invoice.InvoiceSettingActivity",
			"deeplink_web": "",
			"start_version": 0,
			"end_version": -1
		},
		{
			"display_name": "Buat Kartu Nama",
			"analytics_name": "buat_kartu_nama",
			"rank": 4,
			"icon": "https://i.ibb.co/HV0dsny/icon-multi-color-biz-card.webp",
			"coming_soon": false,
			"is_new": false,
            "is_promo": false,
			"deeplink_app": "com.bukuwarung.activities.card.newcard.NewBusinessCardActivity",
			"deeplink_web": "",
			"start_version": 0,
			"end_version": -1
		},
		{
			"display_name": "Download Stiker WA",
			"analytics_name": "undang_teman",
			"rank": 5,
			"icon": "https://i.ibb.co/d0mJqCF/icon-multi-color-sticker.webp",
			"coming_soon": false,
			"is_new": false,
            "is_promo": false,
			"deeplink_app": "com.bukuwarung.activities.stickers.StickerMainActivity",
			"deeplink_web": "",
			"start_version": 0,
			"end_version": -1
		}]
}
        """

    const val CAROUSEL_BLOCK_BODY_NEW = """
        
        {
"body_block_name": "carousel_body_new",
"title": "Kirim dan Tagih Uang Antar Bank",
"subtitle": "",
"data": [
{
"display_name":"Payment Awareness",
"analytics_name": "payment_awareness",
"rank": 1,
"icon": "https://i.ibb.co/cYfGPPN/1-Payment-awareness-5x.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "",
"deeplink_web": "https://bukuwarung.com/fitur-pembayaran/",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Listrik",
"analytics_name": "listrik",
"rank": 2,
"icon": "https://i.ibb.co/PQPcmZX/2-Token-Listik-5x.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Ewallet",
"analytics_name": "ewallet",
"rank": 3,
"icon": "https://i.ibb.co/qgcn1nc/3-Ewallet-5x.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=EMONEY",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Referral",
"analytics_name": "referral",
"rank": 4,
"icon": "https://i.ibb.co/7SFt4Pp/4-Referral-5x.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "com.bukuwarung.activities.referral.payment_referral.ReferralActivity",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
},
{
"display_name":"Lending",
"analytics_name": "lending",
"rank": 5,
"icon": "https://i.ibb.co/4174PcM/Lending-banner-5x.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "",
"deeplink_web": "${BuildConfig.LOS_WEB_LENDING_URL}",
"start_version": 0,
"end_version": -1
},
    {
      "display_name": "Train ticket",
      "analytics_name": "ppob_train_tickets",
      "rank": 6,
      "icon": "https://bw-assets.s3.ap-southeast-1.amazonaws.com/payments/banner/train_ticket_banner.png",
      "coming_soon": false,
      "is_new": true,
      "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=TRAIN_TICKET",
      "deeplink_web": "",
      "start_version": 5000,
      "end_version": -1
    }
]
}       
        """

    const val FIXED_BANNER_BLOCK_BODY_NEW = """
        
        {
"body_block_name": "fixed_banner_body_new",
"title": "Lakukan Verifikasi Akun Pakai KTP 👌",
"subtitle": "Cuma 2 langkah mudah untuk nikmati fitur lebih lengkap",
"data": [
{
"display_name":"Dapatkan pinjaman modal dengan bunga ringan 🤑",
"analytics_name": "KYC",
"rank": 1,
"icon": "https://i.ibb.co/wsTB3fG/KYC-banner-5x.png",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "",
"deeplink_web": "${BuildConfig.KYC_WEB_URL}",
"start_version": 0,
"end_version": -1
}
]
}    
        """

    const val HELP_BLOCK_BODY_NEW = """
        {
"body_block_name":"help_section_body_new",
"title": "Butuh bantuan?",
"subtitle": "Hubungi CS",
"data": [
{
"display_name":"",
"analytics_name": "",
"rank": 1,
"icon": "",
"coming_soon": false,
"is_new": false,
"is_promo": false,
"deeplink_app": "",
"deeplink_web": "",
"start_version": 0,
"end_version": -1
}
]
}
        """
    const val ONBOARDING_CAMPAIGN_BLOCK="""
        {
  "body_block_name": "onboarding_campaign_block",
  "title": "Berburu Hadiah Komisi Agen Rp20.000!",
  "subtitle": "",
  "data": [
    {
      "display_name": "Berburu Hadiah Komisi Agen Rp20.000!",
      "analytics_name": "onboarding_campaign",
      "rank": 1,
      "icon": "",
      "coming_soon": false,
      "is_new": false,
      "is_promo": false,
      "deeplink_app": "",
      "deeplink_web": "https://api-v4.bukuwarung.com/mx-mweb/loyalty/v2?entryPoint=BUKUWARUNG",
      "start_version": 5900,
      "end_version": -1
    }
  ]
}
    """

    const val ONBOARDING_CAMPAIGN_GAME_TYPE="onboarding_campaign"

    const val NOTES_MISSION_BANNER_IMAGE = "https://firebasestorage.googleapis.com/v0/b/bukuwarung-app.appspot.com/o/banners%2FFrame%207372ic_mission_banner%404x.png?alt=media&token=fe75af2e-97a9-4428-bc7e-3bbe5d0a3e59"

    const val ADD_FAVOURITE_DIALOG_CONFIG = """{"titleText": "Ingin tambahkan pelanggan favorit?",
    "bodyText": "Sepertinya pelanggan ini langgananmu. Yuk, tambahkan pelanggan ini ke favorit biar transaksi selanjutnya jadi lebih cepat!",
    "yesButtonText": "Ya, Tambahkan",
    "noButtonText": "Tidak Perlu"}"""

    const val PPOB_ERROR_POPUP_CONTENT_CONFIG = """
        [
  {
    "ic_ppob_error": "",
    "tv_ppob_error_heading": "Yah, sedang ada gangguan",
    "tv_ppob_error_body": "Saat ini fitur beli {category} sedang dalam perbaikan, tunggu kira-kira {timer} jam lagi!",
    "tv_ppob_error_button": "Kembali",
    "ppob_category": [
      "PULSA",
      "LISTRIK",
      "PLN_PREPAID",
      "PLN_POSTPAID",
      "LISTRIK_POSTPAID",
      "EMONEY",
      "PAKET_DATA",
      "PASCABAYAR",
      "VOUCHER_GAME",
      "BPJS",
      "PDAM",
      "REMINDER",
      "ANGSURAN",
      "INTERNET_DAN_TV_KABEL",
      "VEHICLE_TAX",
      "SET_SELLING_PRICE",
      "PROMOTIONS"
    ],
    "text_after_timer_complete": "Tunggu ya, sedang diperbaiki. Aplikasi akan normal dan kamu bisa coba logi.",
    "start_time_millis": 1655177522236,
    "error_wait_time_hrs": 2
  }
]
    """

    const val PROMOTIONS_BODY = """
        {
            "body_block_name": "promotions_body",
            "title": "Atur promosi untuk jemput rezeki 💸",
            "subtitle": "",
            "data": [
                {
                  "display_name": "Pengingat Tagihan",
                  "analytics_name": "ppob_reminders",
                  "rank": 1,
                  "icon": "https://i.ibb.co/gjcY2ns/reminder.png",
                  "coming_soon": false,
                  "is_new": true,
                  "deeplink_app": "com.bukuwarung.payments.ppob.reminders.view.ReminderActivity",
                  "deeplink_web": "",
                  "start_version": 4280,
                  "end_version": -1
                },
                {
                  "display_name": "Atur Harga Jual",
                  "analytics_name": "ppob_change_selling_price",
                  "rank": 2,
                  "icon": "https://i.ibb.co/Lnq87FK/ic-set-selling-price.png",
                  "coming_soon": false,
                  "is_new": true,
                  "deeplink_app": "com.bukuwarung.payments.ppob.catalog.view.CatalogActivity",
                  "deeplink_web": "",
                  "start_version": 4470,
                  "end_version": -1
                },
                {
                  "display_name": "Poster Promosi",
                  "analytics_name": "ppob_promotions",
                  "rank": 3,
                  "icon": "https://i.ibb.co/mt2ZP0K/ic-promotions.png",
                  "coming_soon": false,
                  "is_new": true,
                  "deeplink_app": "",
                  "deeplink_web": "",
                  "start_version": 4470,
                  "end_version": -1
                }
            ]
        }
    """

    const val PPOB_BOTTOMSHEET_HOMEPAGE = """
        {
  "title": "Makin untung jadi Bos Konter 😎",
  "subTitle": "Tambah penghasilan dari jualan produk digital",
  "fragmentBodyBlockList": [
    {
      "body_block_name": "prepaid_ppob_body",
      "title": "Prabayar & Top Up",
      "subtitle": "",
      "data": [
        {
          "display_name": "Pulsa",
          "analytics_name": "pulsa",
          "rank": 1,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PpobPulsaActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=PULSA",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Paket Data",
          "analytics_name": "packet_data",
          "rank": 2,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=PAKET_DATA",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=PAKET_DATA",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Token Listrik",
          "analytics_name": "token_listrik",
          "rank": 3,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=LISTRIK",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "E-Wallet",
          "analytics_name": "ewallet",
          "rank": 4,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=EMONEY",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=EMONEY",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Gaming Voucher",
          "analytics_name": "gaming_voucher",
          "rank": 5,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/voucher-game.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "",
          "deeplink_web": "https://api-dev.bukuwarung.com/payments-mweb/vouchers/:businessId",
          "start_version": 4244,
          "end_version": -1
        }
      ]
    },
    {
      "body_block_name": "postpaid_ppob_body",
      "title": "Pembayaran Tagihan",
      "subtitle": "",
      "data": [
        {
          "display_name": "Tagihan Listrik",
          "analytics_name": "tagihan_listrik",
          "rank": 6,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
          "coming_soon": false,
          "is_new": false,
          "deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity&selected_tab=1",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=LISTRIK_POSTPAID",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Pulsa Pascabayar",
          "analytics_name": "pascabayar",
          "rank": 7,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
          "coming_soon": false,
          "is_new": false,
          "deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PostpaidPulsaActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=PASCABAYAR",
          "deeplink_web": "",
          "start_version": 4244,
          "end_version": -1
        },
        {
          "display_name": "BPJS",
          "analytics_name": "bpjs",
          "rank": 8,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
          "coming_soon": false,
          "is_new": false,
          "deeplink_app": "com.bukuwarung.payments.ppob.bpjs.view.PpobBpjsActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=BPJS",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "PDAM",
          "analytics_name": "pdam",
          "rank": 9,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
          "coming_soon": false,
          "is_new": false,
          "deeplink_app": "com.bukuwarung.payments.ppob.waterbills.view.PpobWaterBillsActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=PDAM",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Internet & TV Kabel",
          "analytics_name": "ppob_cable",
          "rank": 10,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/internet-tv-cable.webp",
          "coming_soon": false,
          "is_new": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.internetdantvcable.view.PpobInternetDanTvCableActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=INTERNET_DAN_TV_KABEL",
          "deeplink_web": "",
          "start_version": 4411,
          "end_version": -1
        },
        {
          "display_name": "Angsuran Kredit",
          "analytics_name": "multi_finance",
          "rank": 11,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/multifinance.webp",
          "coming_soon": false,
          "is_new": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.multifinance.view.PpobMultifinanceActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=ANGSURAN",
          "deeplink_web": "",
          "start_version": 4398,
          "end_version": -1
        },
        {
          "display_name": "E-Samsat",
          "analytics_name": "ppob_e_samsat",
          "rank": 12,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/vehicle-tax.webp",
          "coming_soon": false,
          "is_new": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.vehicletax.view.PpobVehicleTaxActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=VEHICLE_TAX",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        }
      ]
    },
    {
      "body_block_name": "promotions_ppob_body",
      "title": "Fitur untuk maksimalkan penjualan",
      "subtitle": "",
      "coachmarkInfo": {
        "analytics_block_name": "HOMEPAGE_PPOB_BOTTOMSHEET",
        "body_block_name": "TUTORIAL_PROMOTIONS_PPOB_CTA",
        "category": "ppob",
        "coachmark_next": null,
        "coachmark_body": "Kamu bisa atur pengingat tagihan ke pelanggan, atur harga jual, dan buat katalog harga untuk promosikan produk jualanmu!",
        "coachmark_header": "Ada banyak cara untuk promosi jualan 😍",
        "coachmark_max_steps": 1,
        "coachmark_step": 1,
        "enabled": true,
        "end_version": -1,
        "id": "TUTORIAL_PROMOTIONS_PPOB_CTA",
        "layout_type": -1,
        "rank": 1,
        "start_version": "4470"
      },
      "data": [
        {
          "display_name": "Pengingat Tagihan",
          "analytics_name": "ppob_reminders",
          "rank": 13,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/reminders.webp",
          "coming_soon": false,
          "is_new": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.reminders.view.ReminderActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=REMINDER",
          "deeplink_web": "",
          "start_version": 4280,
          "end_version": -1
        },
        {
          "display_name": "Atur Harga Jual",
          "analytics_name": "set_ppob_selling_price",
          "rank": 14,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/set-selling-price.webp",
          "coming_soon": false,
          "is_new": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.catalog.view.CatalogActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=SET_SELLING_PRICE",
          "deeplink_web": "",
          "start_version": 4500,
          "end_version": -1
        },
        {
          "display_name": "Poster Promosi",
          "analytics_name": "tools_promotion",
          "rank": 15,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/promotions.webp",
          "coming_soon": false,
          "is_new": true,
          "deeplink_app": "com.bukuwarung.payments.ppob.catalog.view.PromotionActivity",
          "deeplinkAppNeuro": "bukuwarung://launch/ppob/product?from=home&category=PROMOTIONS",
          "deeplink_web": "",
          "start_version": 4515,
          "end_version": -1
        }
      ]
    }
  ]
}
    """

    const val LAINNYA_BOTTOMSHEET_HOMEPAGE = """ 
            {
  "title": "",
  "subTitle": "",
  "fragmentBodyBlockList": [
    {
      "body_block_name": "lainnya_body",
      "title": "Fitur Menarik Lainnya 😘",
      "subtitle": "",
      "data": [
        {
          "display_name": "Undang Teman",
          "analytics_name": "undang_teman",
          "rank": 1,
          "icon": "https://i.ibb.co/KF2LnjT/icon-multi-color-share.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "com.bukuwarung.activities.referral.payment_referral.ReferralActivity",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Atur Jadwal Pencatatan",
          "analytics_name": "atur_jadwal_pencatatan",
          "rank": 2,
          "icon": "https://i.ibb.co/P4wtM5n/Frame-2997.png",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "com.bukuwarung.activities.selfreminder.SelfReminderActivity",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Atur Info Nota",
          "analytics_name": "atur_info_nota",
          "rank": 3,
          "icon": "https://i.ibb.co/SfT97Fg/icon-multi-color-receipt.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "com.bukuwarung.activities.invoice.InvoiceSettingActivity",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Buat Kartu Nama",
          "analytics_name": "buat_kartu_nama",
          "rank": 4,
          "icon": "https://i.ibb.co/HV0dsny/icon-multi-color-biz-card.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "com.bukuwarung.activities.card.newcard.NewBusinessCardActivity",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        },
        {
          "display_name": "Download Stiker WA",
          "analytics_name": "undang_teman",
          "rank": 5,
          "icon": "https://i.ibb.co/d0mJqCF/icon-multi-color-sticker.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "com.bukuwarung.activities.stickers.StickerMainActivity",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1
        }
      ]
    }
  ]
}
    """

    const val EDC_CONFIG = """
        {
          "edc_purchase_pending_details": {
            "show_snackbar": true,
            "snackbar_title": "Silakan verifikasi akun sebelum 20 Juli 2024",
            "snackbar_desc": "Supaya mesin EDC segera dikirim dan dapat digunakan untuk bertransaksi. Selengkapnya",
            "snackbar_desc_highlight": "Selengkapnya"
          }
        }
    """

    const val PPOB_CONFIG = """
        {
  "ppob_list": [
    {
      "display_name": "Pulsa",
      "analytics_name": "pulsa",
      "rank": 1,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/pulsa.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/pulsa.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "PULSA"
    },
    {
      "display_name": "Token Listrik",
      "analytics_name": "token_listrik",
      "rank": 2,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/listrik.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/listrik.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "LISTRIK"
    },
    {
      "display_name": "Top UP E-Wallet",
      "analytics_name": "ewallet",
      "rank": 3,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/ewallet.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/ewallet.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "EMONEY"
    },
    {
      "display_name": "Paket Data",
      "analytics_name": "packet_data",
      "rank": 4,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/paket-data.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/paket-data.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "PAKET_DATA"
    },
    {
      "display_name": "Tagihan Listrik",
      "analytics_name": "tagihan_listrik",
      "rank": 5,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/postpaid-listrik.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/postpaid-listrik.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "LISTRIK_POSTPAID"
    },
    {
      "display_name": "BPJS",
      "analytics_name": "bpjs",
      "rank": 6,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/bpjs.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/bpjs.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "BPJS"
    },
    {
      "display_name": "PDAM",
      "analytics_name": "pdam",
      "rank": 7,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/pdam.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/pdam.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "PDAM"
    },
    {
      "display_name": "Voucher Game",
      "analytics_name": "gaming_voucher",
      "rank": 8,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/voucher-game.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/voucher-game.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/voucher-game.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "VOUCHER_GAME"
    },
    {
      "display_name": "Pulsa Pascabayar",
      "analytics_name": "pascabayar",
      "rank": 9,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/postpaid-pulsa.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/postpaid-pulsa.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "PASCABAYAR"
    },
    {
      "display_name": "Angsuran Kredit",
      "analytics_name": "multi_finance",
      "rank": 10,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/internet-tv-cable.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/multifinance.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/internet-tv-cable.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "ANGSURAN"
    },
    {
      "display_name": "Internet & TV",
      "analytics_name": "ppob_cable",
      "rank": 11,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/internet-tv-cable.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/internet-tv-cable.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/internet-tv-cable.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "INTERNET_DAN_TV_KABEL"
    },
    {
      "display_name": "E-Samsat",
      "analytics_name": "e_samsat",
      "rank": 12,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/vehicle-tax.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/vehicle-tax.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/vehicle-tax.webp",
      "state": "AVAILABLE",
      "start_version": 4538,
      "end_version": -1,
      "category": "VEHICLE_TAX"
    },
    {
      "display_name": "Tiket Kereta Api",
      "analytics_name": "ppob_train_tickets",
      "rank": 13,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/not-selected-ppob/train.webp",
      "selected_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/train.webp",
      "coming_soon_icon": "https://mx-static.bukuwarung.com/android/ppob-icons/coming-soon-ppob/train.webp",
      "state": "AVAILABLE",
      "start_version": 5000,
      "end_version": -1,
      "category": "TRAIN_TICKET"
    }
  ]
}
    """

    const val PROFILE_TAB_FEEDBACK_INFO = """
        {
            "showOnMweb": false,
            "redirectionUrl": "https://buku.canny.io/saran",
            "showView": true
        }
    """

    const val IMAGE_ASSETS_URLS_JSON = """
        {
      "loyalty": {
        "bronze_tier_icon_url": "https://mx-static.dev.bukuwarung.com/loyalty-web/bw-loyalty-asssets/bronze-icon-set/tier_icon.webp",
        "silver_tier_icon_url": "https://mx-static.dev.bukuwarung.com/loyalty-web/bw-loyalty-asssets/silver-icon-set/tier_icon.webp",
        "gold_tier_icon_url": "https://mx-static.dev.bukuwarung.com/loyalty-web/bw-loyalty-asssets/gold-icon-set/tier_icon.webp",
        "platinum_tier_icon_url": "https://mx-static.dev.bukuwarung.com/loyalty-web/bw-loyalty-asssets/platinum-icon-set/tier_icon.webp",
        "diamond_tier_icon_url": "https://mx-static.dev.bukuwarung.com/loyalty-web/bw-loyalty-asssets/diamond-icon-set/tier_icon.webp"
      }
}
    """

    const val INVOICE_DATA_BLOCK_JSON = """
        {
  "body_block_name": "invoice_monetization",
  "id": "invoice_data",
  "data": [
    {
      "store_detail_data": [
        {
          "display_name": "store_detail_utang",
          "analytics_name": "store_detail_utang",
          "invoice_name": "utang",
          "visibility": true,
          "elements": [
            {
              "icon": {
                "visibility": false
              },
              "title": {
                "visibility": true
              },
              "address": {
                "visibility": true
              },
              "phone": {
                "visibility": true
              }
            }
          ]
        },
        {
          "display_name": "store_detail_pos",
          "analytics_name": "store_detail_pos",
          "invoice_name": "pos",
          "visibility": true,
          "elements": [
            {
              "icon": {
                "visibility": false
              },
              "title": {
                "visibility": true
              },
              "address": {
                "visibility": true
              },
              "phone": {
                "visibility": true
              }
            }
          ]
        },
        {
          "display_name": "store_detail_pembukan",
          "analytics_name": "store_detail_pembukan",
          "invoice_name": "pembukan",
          "visibility": true,
          "elements": [
            {
              "icon": {
                "visibility": false
              },
              "title": {
                "visibility": true
              },
              "address": {
                "visibility": true
              },
              "phone": {
                "visibility": true
              }
            }
          ]
        }
      ],
      "header_data": [
        {
          "display_name": "header_data_utang",
          "analytics_name": "header_data_utang",
          "invoice_name": "utang",
          "visibility": true,
          "elements": [
            {
              "date": {
                "key": "Tanggal",
                "visibility": true
              },
              "note_code": {
                "key": "Kode Nota",
                "visibility": false
              },
              "customer": {
                "key": "Pelanggan",
                "visibility": true
              }
            }
          ]
        },
        {
          "display_name": "header_data_pos",
          "analytics_name": "header_data_pos",
          "invoice_name": "pos",
          "visibility": true,
          "elements": [
            {
              "date": {
                "key": "Tanggal",
                "visibility": true
              },
              "note_code": {
                "key": "Kode Nota",
                "visibility": true
              },
              "cashier": {
                "key": "Kasir",
                "visibility": false
              },
              "table_number": {
                "key": "Nomor Meja",
                "visibility": false
              },
              "order": {
                "key": "Pesanan",
                "visibility": false
              },
              "type_of_payment": {
                "key": "Jenis Pembayaran",
                "visibility": true
              },
              "customer": {
                "key": "Pelanggan",
                "visibility": false
              }
            }
          ]
        },
        {
          "display_name": "header_data_pembukan",
          "analytics_name": "header_data_pembukan",
          "invoice_name": "pembukan",
          "visibility": true,
          "elements": [
            {
              "date": {
                "key": "Tanggal",
                "visibility": true
              },
              "note_code": {
                "key": "Kode Nota",
                "visibility": false
              },
              "cashier": {
                "key": "Kasir",
                "visibility": false
              },
              "table_number": {
                "key": "Nomor Meja",
                "visibility": false
              },
              "order": {
                "key": "Pesanan",
                "visibility": false
              },
              "type_of_payment": {
                "key": "Jenis Pembayaran",
                "visibility": false
              },
              "customer": {
                "key": "Pelanggan",
                "visibility": true
              }
            }
          ]
        }
      ],
      "transaction_status": [
        {
          "display_name": "transaction_status_utang",
          "analytics_name": "transaction_status_utang",
          "invoice_name": "utang",
          "visibility": true
        },
        {
          "display_name": "transaction_status_pos",
          "analytics_name": "transaction_status_pos",
          "invoice_name": "pos",
          "visibility": true
        },
        {
          "display_name": "transaction_status_pembukan",
          "analytics_name": "transaction_status_pembukan",
          "invoice_name": "pembukan",
          "visibility": true
        }
      ],
      "body_data": [
        {
          "display_name": "body_data_utang",
          "analytics_name": "body_data_utang",
          "invoice_name": "utang",
          "visibility": true,
          "elements": [
            {
              "amount_given": {
                "key": "Memberikan",
                "visibility": true
              },
              "total_debt": {
                "key": "Total Utang",
                "visibility": true
              },
              "total_paid": {
                "key": "Total Sudah Dibayar",
                "visibility": true
              },
              "insufficient_payment": {
                "key": "Kurang Bayar",
                "visibility": true
              }
            }
          ]
        },
        {
          "display_name": "body_data_pos",
          "analytics_name": "body_data_pos",
          "invoice_name": "pos",
          "visibility": true,
          "elements": [
            {
              "sub_total": {
                "key": "Subtotal",
                "visibility": false
              },
              "discount": {
                "key": "Diskon",
                "visibility": false
              },
              "tax": {
                "key": "Tax",
                "visibility": false
              },
              "total": {
                "key": "Total",
                "visibility": true
              },
              "cash": {
                "key": "Tunai",
                "visibility": true
              },
              "return": {
                "key": "Kembalian",
                "visibility": true
              }
            }
          ]
        },
        {
          "display_name": "body_data_pembukan",
          "analytics_name": "body_data_pembukan",
          "invoice_name": "pembukan",
          "visibility": true,
          "elements": [
            {
              "sub_total": {
                "key": "Subtotal",
                "visibility": false
              },
              "discount": {
                "key": "Diskon",
                "visibility": false
              },
              "tax": {
                "key": "Tax",
                "visibility": false
              },
              "total": {
                "key": "Total",
                "visibility": true
              },
              "cash": {
                "key": "Tunai",
                "visibility": true
              },
              "return": {
                "key": "Kembalian",
                "visibility": true
              }
            }
          ]
        }
      ],
      "footer_data": [
        {
          "display_name": "footer_data_utang",
          "analytics_name": "footer_data_utang",
          "invoice_name": "utang",
          "visibility": true,
          "elements": [
            {
              "notes": {
                "key": "Catatan",
                "visibility": true
              },
              "promo": {
                "key": "promo",
                "visibility": true
              },
              "buku_ad": {
                "key": "buku_ad",
                "visibility": true
              },
              "social_media": {
                "key": "social_media",
                "visibility": true
              }
            }
          ]
        },
        {
          "display_name": "footer_data_pos",
          "analytics_name": "footer_data_pos",
          "invoice_name": "pos",
          "visibility": true,
          "elements": [
            {
              "notes": {
                "key": "Catatan",
                "visibility": true
              },
              "promo": {
                "key": "promo",
                "visibility": true
              },
              "buku_ad": {
                "key": "buku_ad",
                "visibility": true
              },
              "social_media": {
                "key": "social_media",
                "visibility": true
              }
            }
          ]
        },
        {
          "display_name": "footer_data_pembukan",
          "analytics_name": "footer_data_pembukan",
          "invoice_name": "pembukan",
          "visibility": true,
          "elements": [
            {
              "notes": {
                "key": "Catatan",
                "visibility": true
              },
              "promo": {
                "key": "promo",
                "visibility": true
              },
              "buku_ad": {
                "key": "buku_ad",
                "visibility": true
              },
              "social_media": {
                "key": "social_media",
                "visibility": true
              }
            }
          ]
        }
      ]
    }
  ]
}
    """

    const val REFERRAL_CONTENT_DATA = """
        {
  "referral_page_content": {
    "title": "Undang Teman",
    "referral_image": "https://i.ibb.co/n6m2Pd5/referral-illustration.webp",
    "referral_text": "Kode Referral-mu:",
    "referral_subtext": "Kamu juga bisa naikan levelmu dan menangin grand prize tiap undang teman pakai BukuWarung.",
    "referral_bonus_text": "Cek cara dapatkan hadiah",
    "referral_share_text": "Undang Teman, bisa dapetin hadiah Komisi Agen Rp198.000/teman",
    "referral_share_button_text": "Bagikan Referral",
    "referral_instruction_text": "Teman yang Diundang",
    "referral_contact_image": "https://i.ibb.co/mykMbz3/ic-phonebook-new.webp",
    "referral_contact_info": "Undang Teman lebih cepat dari kontak HP-mu",
    "referral_contact_buttton_text": "Izinkan Akses Kontak"
  },
  "referral_permission_content": {
    "title": "Persetujuan untuk mengakses dan mengimpor Daftar Kontak Anda",
    "instruction_list": [
      {
        "index": 1,
        "data": "Sekali tap untuk menambah konsumen"
      },
      {
        "index": 2,
        "data": "Anda dapat mengirim pengingat untuk pembayaran hutang, <b>dapat mempercepat pembayaran hingga 3x lipat"
      },
      {
        "index": 3,
        "data": "Data dan privasi Anda dijamin aman."
      }
    ],
    "negative_button_text": "Tolak",
    "positive_button_text": "Izinkan"
  },
  "referral_share_content": {
    "referral_share_image": "https://images2.imgbox.com/01/37/awx1RWHr_o.jpg",
    "referral_share_text": "Halo, aku ngajakin kamu untuk pakai BukuWarung, nih. BukuWarung adalah aplikasi pencatatan keuangan usaha lengkap untuk:\n\n Catat transaksi keuangan praktis\n Laporan keuangan otomatis\n Catat dan tagih utang lewat chat otomatis\n Bayar dan tagih transaksi bisnis praktis\n Yuk, segera download aplikasi BukuWarung dengan link berikut. []\n *Jangan lupa* masukkan kode referral ini sebelum membuat catatan keuangan untuk dapetin hadiah menarik ${'$'}${'$'}",
    "referral_share_text_new": "Yuk, kembangin usahamu pakai aplikasi BukuWarung dengan download link berikut: \n[link]\n\nDan masukkan kode referral berikut [input_referral_code] di menu Undang Teman.\n\nSetelah terdaftar dan terverifikasi, kamu bisa bagikan kode referral milikmu ke teman untuk dapetin Komisi Agen setiap hari dan menangin hadiah liburan ke Bali!\n\nKenapa #MakinYakin pakai BukuWarung?\n\n💰 Pembayaran antar bank dengan biaya aplikasi lebih rendah mulai dari Rp900 dan selalu ada diskon tambahan.\n\n📱 Aplikasi praktis untuk jualan berbagai Produk Digital, Pembayaran yang cepat terkirim, dan Pembukuan usahamu.\n\n⏰ Jaminan Pembayaran Tepat Waktu. Ada kompensasi kalau pembayaranmu belum diproses lewat dari 10 menit.\n\n👩‍💻 Layanan CS dan Sales yang tanggap dan siap memberi solusi yang tepat.\n\n🎁 Berbagai Promo dan Misi dengan hadiah Komisi Agen, Diskon, sampai Uang Tunai jutaan rupiah!\n\nInfo selengkapnya klik https://bukuwarung.com/makin-yakin/"
  }
}
    """

    const val TICKER_FRAGMENT_DATA = """
{
"ticker_header" : "Info Seputar Pembayaran",
"ticker_body" : "Pembayaran dengan bank BCA akan mengalami keterlambatan sampai pukula. <font color='#0091FF'>Baca selengkapnya </font>",
"start_time" : "2023-02-02T12:15:00",
"end_time" : "2023-02-23T12:15:00",
"frequency" : 720
}     
    """



    const val PAYMENTS_FRAGMENT_DATA = """
        {
  "id": "ppob_block",
  "title": "Tambah Penghasilan Jual Pulsa & Tagihan 😎",
  "subtitle": "",
  "category": "ppob",
  "layout_type": 2,
  "rank": 3,
  "analytics_block_name": "PAYMENTS_PPOB_LAYOUT",
  "coachmark_header": "\nUntung maksimal jualan Pulsa & Tagihan",
  "coachmark_body": "Selain itu, bisa jual paket data, token listrik, top up e-wallet, voucher game, serta tagihan listrik dan pulsa.",
  "coachmark_step": 3,
  "coachmark_max_steps": 4,
  "coachmark_next": 6,
  "body_block_name": "ppob_body_payments",
  "enabled": true,
  "start_version": 0,
  "end_version": -1
}
    """

    const val SALDO_TOPUP_RESTRICT_INFO = """
        {
            "start_time": "2025-06-30T18:00:00",
            "end_time": "2025-07-31T18:00:00",
            "dialog_description": "Mulai 01 Agustus 2025, fitur top up saldo hanya bisa diakses oleh Juragan yang sudah verifikasi toko. Kalau belum, kamu masih bisa pakai saldo yang tersisa untuk beli Produk Digital."
        }
    """

    const val PPOB_PAYMENTS_BOTTOMSHEET = """
        {
  "title": "Makin untung jadi Bos Konter 😎",
  "subTitle": "Tambah penghasilan dari jualan produk digital",
  "fragmentBodyBlockList": [
    {
      "body_block_name": "prepaid_ppob_body",
      "title": "Prabayar & Top Up",
      "subtitle": "",
      "data": [
        {
          "display_name": "Pulsa",
          "analytics_name": "pulsa",
          "rank": 1,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=PULSA",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "PULSA",
          "is_available": false
        },
        {
          "display_name": "Paket Data",
          "analytics_name": "packet_data",
          "rank": 2,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=PAKET_DATA",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "PAKET_DATA",
          "is_available": true
        },
        {
          "display_name": "Token Listrik",
          "analytics_name": "token_listrik",
          "rank": 3,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=LISTRIK",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "LISTRIK",
          "is_available": true
        },
        {
          "display_name": "E-Wallet",
          "analytics_name": "ewallet",
          "rank": 4,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=EMONEY",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "EMONEY",
          "is_available": true
        },
        {
          "display_name": "Gaming Voucher",
          "analytics_name": "gaming_voucher",
          "rank": 5,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/voucher-game.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "",
          "deeplink_web": "https://api-dev.bukuwarung.com/payments-mweb/vouchers/:businessId",
          "start_version": 4244,
          "end_version": -1,
          "ppobCategoryName": "VOUCHER_GAME",
          "is_available": true
        },
        {
          "display_name": "Tiket Kereta Api",
          "analytics_name": "ppob_train_tickets",
          "rank": 6,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/train.webp",
          "coming_soon": false,
          "is_new": true,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=TRAIN_TICKET",
          "deeplink_web": "",
          "start_version": 5000,
          "end_version": -1,
          "ppobCategoryName": "TRAIN_TICKET",
          "is_available": true
        }
      ]
    },
    {
      "body_block_name": "postpaid_ppob_body",
      "title": "Pembayaran Tagihan",
      "subtitle": "",
      "data": [
        {
          "display_name": "Tagihan Listrik",
          "analytics_name": "tagihan_listrik",
          "rank": 7,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=LISTRIK_POSTPAID",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "PLN_POSTPAID",
          "is_available": false
        },
        {
          "display_name": "Pulsa Pascabayar",
          "analytics_name": "pascabayar",
          "rank": 8,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=PASCABAYAR",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "PASCABAYAR",
          "is_available": true
        },
        {
          "display_name": "BPJS",
          "analytics_name": "bpjs",
          "rank": 9,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=BPJS",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "BPJS",
          "is_available": true
        },
        {
          "display_name": "PDAM",
          "analytics_name": "pdam",
          "rank": 10,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=PDAM",
          "deeplink_web": "",
          "start_version": 0,
          "end_version": -1,
          "ppobCategoryName": "PDAM",
          "is_available": true
        },
        {
          "display_name": "Internet & TV Kabel",
          "analytics_name": "ppob_cable",
          "rank": 11,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/internet-tv-cable.webp",
          "coming_soon": false,
          "is_new": true,
          "is_promo": false,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=INTERNET_DAN_TV_KABEL",
          "deeplink_web": "",
          "start_version": 4280,
          "end_version": -1,
          "ppobCategoryName": "INTERNET_DAN_TV_KABEL",
          "is_available": true
        },
        {
          "display_name": "Angsuran Kredit",
          "analytics_name": "multi_finance",
          "rank": 12,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/multifinance.webp",
          "coming_soon": false,
          "is_new": true,
          "is_promo": false,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=ANGSURAN",
          "deeplink_web": "",
          "start_version": 4244,
          "end_version": -1,
          "ppobCategoryName": "ANGSURAN",
          "is_available": true
        },
        {
          "display_name": "E-Samsat",
          "analytics_name": "ppob_e_samsat",
          "rank": 13,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/vehicle-tax.webp",
          "coming_soon": false,
          "is_new": true,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=VEHICLE_TAX",
          "deeplink_web": "",
          "start_version": 4280,
          "end_version": -1,
          "ppobCategoryName": "VEHICLE_TAX",
          "is_available": true
        }
      ]
    },
    {
      "body_block_name": "promotions_ppob_body",
      "title": "Fitur untuk maksimalkan penjualan",
      "subtitle": "",
      "coachmarkInfo": {
        "analytics_block_name": "HOMEPAGE_PPOB_BOTTOMSHEET",
        "body_block_name": "TUTORIAL_PROMOTIONS_PPOB_CTA",
        "category": "ppob",
        "coachmark_next": null,
        "coachmark_body": "Kamu bisa atur pengingat tagihan ke pelanggan, atur harga jual, dan buat katalog harga untuk promosikan produk jualanmu!",
        "coachmark_header": "Ada banyak cara untuk promosi jualan 😍",
        "coachmark_max_steps": 1,
        "coachmark_step": 1,
        "enabled": true,
        "end_version": -1,
        "id": "TUTORIAL_PROMOTIONS_PPOB_CTA",
        "layout_type": -1,
        "rank": 1,
        "start_version": "4470"
      },
      "data": [
        {
          "display_name": "Pengingat Tagihan",
          "analytics_name": "ppob_reminders",
          "rank": 14,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/reminders.webp",
          "coming_soon": false,
          "is_new": true,
          "is_promo": false,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=REMINDER",
          "deeplink_web": "",
          "start_version": 4280,
          "end_version": -1,
          "ppobCategoryName": "REMINDER",
          "is_available": true
        },
        {
          "display_name": "Atur Harga Jual",
          "analytics_name": "set_ppob_selling_price",
          "rank": 15,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/set-selling-price.webp",
          "coming_soon": false,
          "is_new": true,
          "is_promo": true,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=SET_SELLING_PRICE",
          "deeplink_web": "",
          "start_version": 4470,
          "end_version": -1,
          "ppobCategoryName": "SET_SELLING_PRICE",
          "is_available": false
        },
        {
          "display_name": "Poster Promosi",
          "analytics_name": "tools_promotion",
          "rank": 16,
          "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/promotions.webp",
          "coming_soon": false,
          "is_new": false,
          "is_promo": false,
          "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=PROMOTIONS",
          "deeplink_web": "",
          "start_version": 4470,
          "end_version": -1,
          "ppobCategoryName": "PROMOTIONS",
          "is_available": false
        }
      ]
    }
  ]
}
    """

    const val PPOB_BODY_PAYMENTS = """
        {
  "body_block_name": "ppob_body",
  "title": "Tambah Penghasilan Jual Pulsa & Tagihan 😎",
  "subtitle": "",
  "data": [
    {
      "display_name": "Pulsa",
      "analytics_name": "pulsa",
      "rank": 1,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pulsa.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PpobPulsaActivity",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "PULSA",
      "is_available": false
    },
    {
      "display_name": "Token Listrik",
      "analytics_name": "token_listrik",
      "rank": 2,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/listrik.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "LISTRIK",
      "is_available": true
    },
    {
      "display_name": "E-Wallet",
      "analytics_name": "ewallet",
      "rank": 3,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/ewallet.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=EMONEY",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "EMONEY",
      "is_available": true
    },
    {
      "display_name": "Paket Data",
      "analytics_name": "packet_data",
      "rank": 4,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/paket-data.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.choose.ChooseProductActivity&productType=PAKET_DATA",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "PAKET_DATA",
      "is_available": true
    },
    {
      "display_name": "Pulsa Pascabayar",
      "analytics_name": "pascabayar",
      "rank": 5,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-pulsa.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.pulsa.view.PostpaidPulsaActivity",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "PASCABAYAR",
      "is_available": true
    },
    {
      "display_name": "Tagihan Listrik",
      "analytics_name": "tagihan_listrik",
      "rank": 6,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/postpaid-listrik.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity&selected_tab=1",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "PLN_POSTPAID",
      "is_available": false
    },
    {
      "display_name": "BPJS",
      "analytics_name": "bpjs",
      "rank": 7,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/bpjs.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.bpjs.view.PpobBpjsActivity",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "BPJS",
      "is_available": true
    },
    {
      "display_name": "PDAM",
      "analytics_name": "pdam",
      "rank": 8,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/pdam.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.waterbills.view.PpobWaterBillsActivity",
      "deeplink_web": "",
      "start_version": 0,
      "end_version": -1,
      "ppobCategoryName": "PDAM",
      "is_available": true
    },
    {
      "display_name": "Gaming Voucher",
      "analytics_name": "gaming_voucher",
      "rank": 9,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/voucher-game.webp",
      "coming_soon": false,
      "is_new": false,
      "is_promo": true,
      "deeplink_app": "",
      "deeplink_web": "https://api-dev.bukuwarung.com/payments-mweb/vouchers/:businessId",
      "start_version": 4244,
      "end_version": -1,
      "ppobCategoryName": "VOUCHER_GAME",
      "is_available": true
    },
    {
      "display_name": "Angsuran Kredit",
      "analytics_name": "multi_finance",
      "rank": 10,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/multifinance.webp",
      "coming_soon": false,
      "is_new": true,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.multifinance.view.PpobMultifinanceActivity",
      "deeplink_web": "",
      "start_version": 4244,
      "end_version": -1,
      "ppobCategoryName": "ANGSURAN",
      "is_available": true
    },
    {
      "display_name": "Internet & TV Kabel",
      "analytics_name": "ppob_cable",
      "rank": 11,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/internet-tv-cable.webp",
      "coming_soon": false,
      "is_new": true,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.internetdantvcable.view.PpobInternetDanTvCableActivity",
      "deeplink_web": "",
      "start_version": 4280,
      "end_version": -1,
      "ppobCategoryName": "INTERNET_DAN_TV_KABEL",
      "is_available": true
    },
    {
      "display_name": "E-Samsat",
      "analytics_name": "ppob_e_samsat",
      "rank": 12,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/vehicle-tax.webp",
      "coming_soon": false,
      "is_new": true,
      "is_promo": true,
      "deeplink_app": "com.bukuwarung.payments.ppob.vehicletax.view.PpobVehicleTaxActivity",
      "deeplink_web": "",
      "start_version": 4280,
      "end_version": -1,
      "ppobCategoryName": "VEHICLE_TAX",
      "is_available": true
    },
    {
      "display_name": "Tiket Kereta Api",
      "analytics_name": "ppob_train_tickets",
      "rank": 13,
      "icon": "https://mx-static.bukuwarung.com/android/ppob-icons/selected-ppob/train.webp",
      "coming_soon": false,
      "is_new": true,
      "is_promo": true,
      "deeplink_app": "bukuwarung://launch/ppob/product?from=home&category=TRAIN_TICKET",
      "deeplink_web": "",
      "start_version": 5000,
      "end_version": -1,
      "ppobCategoryName": "TRAIN_TICKET",
      "is_available": true
    }
  ]
}
    """

    const val HOME_FAB_CONFIG_JSON = """
        {
     "isEnabled": true,
     "title": "Referral LeaderBoard",
     "type": "IMAGE",
     "imageUrl": "https://mx-static.dev.bukuwarung.com/android/home-page/promo.webp",
     "deeplinkWeb": "https://develop.d3co3nb2lpfoig.amplifyapp.com/referrals/leaderboard"
  }
    """

    const val REFERRAL_CONTENT_WEB = """
        {
            "referral_share_image": "https://i.ibb.co/YkmXLxx/wa-share-referral.webp",
            "referral_share_text": "Hai kamu, \nYuk, cobain pakai BukuWarung! Satu aplikasi keuangan untuk solusi pengembangan usahamu.\n \n Caranya? Daftar dengan kode referral berikut *[input_referral_code]* \nAtau daftar langsung dengan klik link berikut: [link]\n\n*Kenapa #MakinYakin pakai BukuWarung?*\n\n⏰ *Jaminan Pembayaran Tepat Waktu*\nDapat kompensasi kalau pembayaranmu belum diproses lewat dari 10 menit.\n\n👩‍💻 *Jaminan CS Tanggap*\nLayanan Customer Service yang lebih tanggap dan tepat untuk Juragan.\n🎁 *Berbagai Promo Menarik*\nAda hadiah jutaan rupiah hingga liburan ke Bali dengan mengikuti berbagai kompetisi dan promo di BukuWarung.\n\nUntuk info selengkapnya, klik link *_di sini_*",
            "referral_share_sms" : "Dummy Message"
        }
    """

    const val APP_MAINTENANCE_DATA = """
        {
            "showAppMaintenance": false,
            "messageTitle": "Kami akan segera kembali",
            "messageBody": "Demi meningkatkan pelayanan, saat ini sistem dalam perbaikan. Kami akan infokan setelah akses fitur ini normal kembali, ya.",            
            "unblockedNumbers": ["118231","1160000011"]  
        }
    """

    const val REFEREE_ENTRY_POINT_DATA = """
           {
              "title": "Belum masukkan kode referral? Masukkan melalui halaman ini.",
              "redirection" : "/mx-mweb/referral/code?entryPoint=android_homepage"
           }
        """
}
