package com.bukuwarung.constants

object AnalyticsConst {
    const val NON_MONETARY_REWARD_ENABLE = "non_monetary_reward_enable"

    // user property
    const val KYC_STATUS = "kyc_status"
    const val KYC_TIER = "kyc_tier"
    const val KYC_ENABLED = "kyc_enabled"
    const val QRIS_STATUS = "qris_status"
    const val USER_QRIS_STATUS = "user_qris_status"
    const val BUSINESS_DASHBOARD_ENABLED = "business_dashboard_enabled"
    const val KYB_STATUS = "kyb_status"
    const val KYB = "kyb"
    const val PHONE = "phone"


    //event name
    const val EVENT_RECENTS_FAVOURITES_ENABLE = "recents_favourites_enable"
    const val EVENT_INITIATE_IN_APP_TICKET = "Initiate_in_app_ticket"
    const val EVENT_REGISTRATION_ASK_OTP = "registration_ask_otp"
    const val EVENT_SET_PASSWORD_PAGE_VISIT = "set_password_page_visit"
    const val EVENT_SET_PASSWORD_BOX_CLICK = "set_password_box_click"
    const val EVENT_BACK_BUTTON_PRESS = "back_button_press"
    const val EVENT_SET_PASSWORD_ALERT_APPEAR = "set_password_alert_appear"
    const val EVENT_SET_PASSWORD_SHOW_CLICK = "set_password_show_click"
    const val EVENT_SET_PASSWORD_SUMIT = "set_password_submit"
    const val EVENT_LOGIN_PAGE_VISIT = "login_page_visit"
    const val EVENT_LOGIN_PASSWORD_SUBMIT = "login_password_submit"
    const val EVENT_CHANGE_PASSWORD_CLICK = "change_password_click"
    const val EVENT_FORGOT_PASSWORD_CLICK = "forgot_password_click"
    const val INVETORY_DISABLED = "inventory_tab_disabled"

    const val PROFIT = "in_profit"
    const val LOSS = "in_loss"
    const val LANDING_POPUP = "landing_popup"
    const val INVENTORY_ENABLED = "inventory_tab_enabled"
    const val INVENTORY_USER_TYPE = "inventory_user_type"
    const val AUTORECORD_TRX_ENABLED = "autorecord_trx_enabled"
    const val PAYMENT_USER_TYPE = "payment_user_type"
    const val PAYMENT_ENABLED = "payment_tab_enabled"
    const val PAYMENT_DISABLED = "payment_tab_disabled"
    const val APP_VERSION_NAME = "app_version_name"
    const val EVENT_SUCCESS_REGISTRATION = "success_registration"
    const val EVENT_EXIT_WITHOUT_UTANG = "exit_app_without_utang"
    const val EVENT_EXIT_WITHOUT_TRANSAKSI = "exit_app_without_transaksi"
    const val EVENT_EXIT_WITHOUT_ADD_BANK_PAYMENT_OUT = "exit_app_without_add_bank_payment_out"
    const val EVENT_EXIT_WITHOUT_COMPLETING_PAYMENT_OUT = "exit_app_without_completing_payment_out"
    const val EVENT_EXIT_WITHOUT_COMPLETING_PAYMENT_IN = "exit_app_without_completing_payment_in"
    const val EVENT_NOTIFICATION_RECEIVED = "notification_received"
    const val EVENT_EXIT_WITHOUT_ADD_BANK_PAYMENT_IN = "exit_app_without_add_bank_payment_in"
    const val EVENT_GUEST_LOGIN_SUCCESS = "guest_login_success"
    const val EVENT_INPUT_PHONE_NUMBER_AUTO = "input_phone_number_auto"
    const val EVENT_PHONE_SELECTED_FROM_HINT = "phone_selected_from_hint"
    const val EVENT_BKWARUNG_LOGIN = "bkwarung_login"
    const val EVENT_AUTO_DETECT_OTP = "auto_detect_otp"
    const val EVENT_REGISTRATION_VERIFY_OTP = "registration_verify_otp"
    const val EVENT_FORGOT_PASSWORD_ASK_OTP = "forgot_password_ask_otp"
    const val EVENT_FORGOT_PASSWORD_VERIFY_OTP = "forgot_password_verify_otp"
    const val EVENT_REQUEST_WHATSAPP_LOGIN = "request_whatsapp_login"
    const val EVENT_REQUEST_WHATSAPP_VERIFY = "request_whatsapp_verify"
    const val EVENT_WHATSAPP_AUTO_LOGIN = "whatsapp_auto_login"
    const val EVENT_INPUT_INVALID_OTP = "input_invalid_otp"
    const val EVENT_DEVICE_FINGERPRINT_LOGIN = "device_fingerprint_login"
    const val EVENT_OPEN_REFERRAL_SCREEN = "open_referral_screen"
    const val EVENT_OPEN_STICKER_SCREEN = "open_sticker_screen"
    const val EVENT_OPEN_TUTORIAL_SCREEN = "open_tutorial_screen"
    const val EVENT_OPEN_TRANSACTION_LIST = "open_transaction_list"
    const val EVENT_OPEN_FEEDBACK = "open_feedback"
    const val EVENT_LAINNYA_UPDATE_APP = "lainnya_update_app"
    const val EVENT_OPEN_SHARE_APP_SCREEN = "open_share_app_screen"
    const val EVENT_SELF_REMINDER_OPEN = "self_reminder_open"
    const val EVENT_REFERRAL_OPEN = "open_payment_referral_screen"
    const val EVENT_CLICK_TOKOKO_DOWNLOAD = "open_tokoko_download_screen"
    const val EVENT_OPEN_IG_BW = "open_ig_bw"
    const val EVENT_OPEN_FB_BW = "open_fb_bw"
    const val EVENT_INPUT_REFERRAL_CODE = "referral_code_inputted"
    const val EVENT_REFERRAL_ACTIVATION_RESPONSE = "referral_activation_response"
    const val EVENT_REFEREE_ONBOARD_APPEARED = "referee_onboard_appeared"
    const val EVENT_REFEREE_ONBOARD_CONFIRMED = "referee_onboard_confirmed"
    const val EVENT_NOTIFICATION_CLICKED = "notification_clicked"
    const val EVENT_RESEND_OTP_CLICKED = "resend_otp_clicked"
    const val EVENT_CLICK_CONFIRM_POPUP = "click_confirmation_popup"
    const val EVENT_PAYMENT_SEND_REMINDER = "payment_send_reminder"
    const val EVENT_PAYMENT_LAINNYA_HISTORY = "payment_lainnya_history"
    const val EVENT_PAYMENT_LAINNYA_TAGIH_INWARDS = "payment_lainnya_tagih_inwards"
    const val EVENT_PAYMENT_LAINNYA_BAYAR_OUTWARDS = "payment_lainnya_bayar_outwards"
    const val EVENT_PAYMENT_PEMBAYARAN_TAGIH_INWARDS = "pembayaran_tagih_clicked"
    const val EVENT_CHANGE_RECEIVER_BANK_TYPE = "change_receiver_bank_type"
    const val EVENT_VISIT_FAVOURITE_TAB_PAYMENT_OUT = "payment_favourite_tab_visited"
    const val EVENT_VISIT_RECENT_TAB_PAYMENT_OUT = "payment_recent_tab_visited"
    const val EVENT_PAYMENT_SELECT_RECIPIENT_BANK = "payment_recipient_bank_selected"
    const val EVENT_VISIT_AMOUNT_SELECTION_PAGE = "payment_amount_selection_page_visited"
    const val EVENT_INSERT_PAYMENT_AMOUNT = "payment_amount_inputted"
    const val EVENT_CHANGE_FAVOURITE_STATUS = "change_favorite_status"
    const val EVENT_VISIT_PAYMENT_METHOD_SELECTION = "payment_method_selection_page_visited"
    const val EVENT_HAS_PAYOUT_NEW_UI = "hasPayoutNewUI"
    const val EVENT_PAYMENT_PEMBAYARAN_BAYAR_OUTWARDS = "pembayaran_bayar_clicked"
    const val EVENT_PROFILE_VISIT_SETTING = "profile_visit_setting"
    const val EVENT_OPEN_SETTINGS_SCREEN = "open_settings_screen"
    const val EVENT_OPEN_PRINTER_SETUP_ACTIVITY = "open_printer_setup_activity"
    const val EVENT_OPEN_BUSINESS_CARD_VIEW = "open_business_card_view"
    const val EVENT_SET_USER_PROFILE_PIC_CAMERA = "set_user_profile_pic_camera"
    const val EVENT_EDIT_PROFILE_PIC = "edit_profile_pic"
    const val EVENT_BUSINESS_CARD_INITIALIZE = "business_card_initialize"
    const val EVENT_OPEN_FB_GROUP = "lainnya_open_fb_group"
    const val EVENT_BUSINESS_CARD_SHARE_PREVIEW = "business_card_share_preview"
    const val EVENT_BULK_TRANSACTION_ADDED = "bulk_transaction_added_dev"
    const val EVENT_TRANSACTION_UPDATED = "transaction_updated"
    const val EVENT_SETTLE_OPEN = "settle_open"
    const val EVENT_SETTLE_CONFIRM = "settle_confirm"
    const val EVENT_BUSINESS_NAME_SAVE = "business_name_save"
    const val EVENT_BUSINESS_NAME_SKIP = "business_name_skip"
    const val EVENT_SETTLE_SHARE_INIT = "settle_share_init"
    const val EVENT_BANNER_APPEAR = "banner_appear"
    const val EVENT_BANNER_DISMISS = "banner_dismiss"
    const val EVENT_PRODUCT_DETAIL_OPEN = "product_details_open"
    const val EVENT_PRODUCT_DETAIL_CREATE = "product_details_create"
    const val EVENT_PRODUCT_DETAIL_ADD = "product_details_add"
    const val EVENT_PRODUCT_DETAILS_EDIT = "product_details_edit"
    const val EVENT_PRODUCT_DETAILS_DELETE = "product_details_delete"
    const val EVENT_PER_CATEGORY_DELETE_TRANSACTION_CLICK = "per_category_delete_transaction_click"
    const val EVENT_PER_CATEGORY_DELETE_CANCELED = "per_category_delete_canceled"
    const val CATEGORY_DELETE_CANCEL_CLICK = "category_delete_cancel_click"
    const val EVENT_PER_CATEGORY_DELETE_CONFIRMED = "per_category_delete_confirmed"
    const val EVENT_PER_CATEGORY_MOVE_TRANSACTION_CLICK = "category_move_transaction_click"
    const val EVENT_PER_CATEGORY_MOVE_CANCELED = "per_category_move_canceled"
    const val EVENT_PER_CATEGORY_MOVE_CONFIRMED = "per_category_move_confirmed"
    const val EVENT_PAYMENT_PEMBAYARAN_SHOWN = "payment_pembayaran_shown"
    const val EVENT_PAYMENT_PEMBAYARAN_HISTORY = "payment_pembayaran_history"
    const val EVENT_PEMBAYARAN_VISIT = "pembayaran_tab_visited"
    const val EVENT_PAYMENT_SET_USER_BANK = "payment_user_bank_set_clicked"
    const val EVENT_PAYMENT_CHECK_USER_BANK = "payment_user_bank_checked"
    const val EVENT_PAYMENT_CHECK_RECIPIENT_BANK = "payment_recipient_bank_checked"
    const val EVENT_PAYMENT_OPEN_USER_BANK_LIST = "payment_open_user_bank_list"
    const val EVENT_PAYMENT_PEMBAYARAN_SAVE_CUSTOMER_MANUAL = "payment_pembayaran_save_customer_manual"
    const val EVENT_PAYMENT_PEMBAYARAN_ADD_CUSTOMER_MANUAL = "payment_pembayaran_add_customer_manual"
    const val EVENT_PAYMENT_PEMBAYARAN_ADD_CUSTOMER = "pembayaran_customer_selected"
    const val EVENT_PAYMENT_PEMBAYARAN_SELECT_CUSTOMER = "pembayaran_customer_selected"
    const val EVENT_PAYMENT_PEMBAYARAN_ADD_CUSTOMER_PHONEBOOK = "payment_pembayaran_add_customer_phonebook"
    const val EVENT_PAYMENT_PEMBAYARAN_LOOKUP = "payment_pembayaran_lookup"
    const val EVENT_PAYMENT_FORM_AUTOCOMPLETE = "payment_form_autocomplete"
    const val EVENT_PAYMENT_DELETE_USER_BANK = "payment_delete_user_bank"
    const val EVENT_PAYMENT_DELETE_RECIPIENT_BANK = "payment_recipient_bank_deleted"
    const val EVENT_PAYMENT_SWITCH_USER_BANK = "payment_switch_user_bank"
    const val EVENT_PAYMENT_SWITCH_RECIPIENT_BANK = "payment_switch_recipient_bank"
    const val EVENT_PAYMENT_ADD_USER_BANK = "payment_user_bank_added"
    const val EVENT_PAYMENT_ADD_RECIPIENT_BANK = "payment_recipient_bank_added"
    const val EVENT_PAYMENT_SAVE_USER_BANK = "payment_user_bank_saved"
    const val EVENT_PAYMENT_SAVE_RECIPIENT_BANK = "payment_recipient_bank_saved"
    const val EVENT_PPOB_BUY_BUTTON_CLICKED = "ppob_buy_button_clicked"
    const val PPOB_BUY_BUTTON = "ppob_buy_button"
    const val EVENT_PPOB_PULSA_BUY_BUTTON = "EVENT_PPOB_PULSA_BUY_BUTTON"
    const val EVENT_PPOB_POSTPAID_PULSA_BUY_BUTTON = "EVENT_PPOB_POSTPAID_PULSA_BUY_BUTTON"
    const val EVENT_PPOB_LISTRIK_BUY_BUTTON = "EVENT_PPOB_LISTRIK_BUY_BUTTON"
    const val EVENT_PPOB_POSTPAID_LISTRIK_BUY_BUTTON = "EVENT_PPOB_POSTPAID_LISTRIK_BUY_BUTTON"
    const val EVENT_PPOB_EWALLET_BUY_BUTTON = "EVENT_PPOB_EWALLET_BUY_BUTTON"
    const val EVENT_PPOB_PAKET_DATA_BUY_BUTTON = "EVENT_PPOB_PAKET_DATA_BUY_BUTTON"
    const val EVENT_PPOB_GAMING_VOUCHER_BUY_BUTTON = "EVENT_PPOB_GAMING_VOUCHER_BUY_BUTTON"
    const val EVENT_PPOB_BPJS_BUY_BUTTON = "EVENT_PPOB_BPJS_BUY_BUTTON"
    const val EVENT_PPOB_REMINDER_BUTTON = "EVENT_PPOB_REMINDER_BUTTON"
    const val EVENT_PPOB_PDAM_BUY_BUTTON = "EVENT_PPOB_PDAM_BUY_BUTTON"
    const val EVENT_PPOB_MULTIFINANCE_BUY_BUTTON = "EVENT_PPOB_MULTIFINANCE_BUY_BUTTON"
    const val EVENT_PPOB_VEHICLE_TAX_BUY_BUTTON = "EVENT_PPOB_VEHICLE_TAX_BUY_BUTTON"
    const val EVENT_PPOB_INTERNET_DAN_TV_CABLE_BUY_BUTTON = "EVENT_PPOB_INTERNET_DAN_TV_CABLE_BUY_BUTTON"
    const val EVENT_PPOB_POPUP = "ppob_popup"
    const val EVENT_PPOB_BANNER = "ppob_banner"
    const val EVENT_PPOB_ADD_CUSTOMER_PHONEBOOK = "ppob_add_customer_phonebook"
    const val EVENT_PPOB_ADD_CUSTOMER_MANUAL = "ppob_add_customer_manual"
    const val EVENT_PPOB_SAVE_CUSTOMER_MANUAL = "ppob_save_customer_manual"
    const val EVENT_PPOB_SELECT_PACK = "ppob_pack_selected"
    const val EVENT_PPOB_SELECT_PAYMENT_METHOD = "ppob_payment_method_selected"
    const val EVENT_PPOB_CREATED = "ppob_created"
    const val EVENT_PAYMENT_PPOB_SUCCESS_PUSH = "payment_ppob_success_push"
    const val EVENT_OPEN_BUSINESS_DETAIL_DIALOG = "open_business_detail_dialog"
    const val EVENT_SUCCESSFULLY_INVITED_TAB_LOAD = "successfully_invited_tab_load"
    const val EVENT_REWARD_TAB_LOAD = "reward_tab_load"
    const val EVENT_REDIRECT_FROM_PN = "redirect_from_pn"
    const val EVENT_STOCK_HIDE_TAB = "change_inventory_tab_setting"
    const val EVENT_PRODUCT_SAVE = "product_details_save"
    const val EVENT_PAYMENT_EDIT_USER_BANK = "payment_edit_user_bank"
    const val EVENT_PAYMENT_EDIT_RECIPIENT_BANK = "payment_recipient_bank_edit_clicked\n"
    const val EVENT_APP_LAUNCH = "app_launch"
    const val EVENT_CUSTOMER_DETAIL_TAP_SAVED_CASH = "customer_detail_tap_saved_cash"
    const val EVENT_CUSTOMER_DETAIL_TAP_SAVED = "customer_detail_tap_saved"
    const val EVENT_CREATE_NEW_CASH_TRANSACTION = "create_new_cash_transaction"
    const val EVENT_CREATED_NEW_CUSTOMER = "created_new_customer"
    const val EVENT_CUSTOMER_DETAIL_TAP_DEBIT = "customer_detail_tap_debit"
    const val EVENT_CUSTOMER_DETAIL_TAP_CREDIT = "customer_detail_tap_credit"
    const val EVENT_SEND_REMINDER_VIA_SMS = "send_reminder_via_sms"
    const val EVENT_COLLECTING_CALENDAR_CREATE_NEW_COLLECTING_DATE = "collecting_calendar_create_new_collecting_date"
    const val EVENT_LOGOUT_CLICKED = "logout_clicked"
    const val EVENT_PAYMENT_REQUEST_CREATED = "payment_request_created"
    const val EVENT_PAYMENT_SEND_CREATED = "payment_send_created"
    const val EVENT_REFERRAL_SHARE_LINK_SUCCESS = "referral_share_link_success"
    const val EVENT_OPEN_TRANSACTION_REPORT = "open_transaction_report"
    const val EVENT_SAVE_BUSINESS_CARD = "save_business_card"
    const val EVENT_REFERRAL_CONTACT_PERMISSION_BUTTON_CLICK = "referral_contact_permission_button_click"
    const val EVENT_SEARCH_CONTACT_WITH_PHONEBOOK = "search_contact_with_phonebook"
    const val EVENT_INVITE_REFERRAL_WITH_FRIEND = "invite_referral_with_friend"
    const val EVENT_INVITE_REFERRAL_WITH_FRIEND_SUCCESS = "invite_referral_with_friend_success"
    const val EVENT_SHARE_REFERRAL_WITH_FRIEND = "share_referral_with_friend"
    const val EVENT_BUAT_PEMBAYARAN_CLICKED = "pembayaran_create_payment_clicked"
    const val EVENT_CLICK_PAYMENT_REFRESH_STATUS = "click_payment_refresh_status"
    const val EVENT_SHARE_PAYMENT_STATUS_TRACKING_LINK = "share_payment_status_tracking_link"
    const val EVENT_OPEN_WEBVIEW = "webview_opened"
    const val EVENT_CLICK_BW_STORY = "click_bw_story"
    const val EVENT_DISCONNECT_BRICK_INTEGRATION = "disconnect_brick_integration"
    const val EVENT_CHANGE_USER_PROFILE = "change_user_profile"
    const val EVENT_CLICK_PAYMENT_CATEGORY_FIELD = "click_payment_category_field"
    const val EVENT_CHOOSE_CATEGORY_PAYMENT_IN = "choose_category_payment_in"
    const val EVENT_CHOOSE_CATEGORY_PAYMENT_OUT = "choose_category_payment_out"
    const val USER_PROFILE_SAVE = "user_profile_save"

    const val EVENT_MISSION_CLICK = "mission_click"
    const val MISSION_COMPLETED_POPUP_CLICK = "mission_completed_popup_click"
    const val EVENT_PROFILE_MISSION_COMPLETED_POPUP = "profile_mission_completed_popup"
    const val EVENT_MISSION_COMPLETED_POPUP = "mission_completed_popup"
    const val EVENT_COMPLETE_BUSINESS_PROFILE = "business_profile_completed"
    const val EVENT_PROFILE_MISSION_SEE_INVOICE_CLICK = "profile_mission_see_invoice_click"
    const val EVENT_LOGO_REMOVAL_TOGGLE_SELECT = "logo_removal_toggle_select"
    const val EVENT_PAYMENT_SEND_SELECT_PAYMENT_METHOD = "payment_send_payment_method_selected"
    const val EVENT_KYC_VIDEO_SUBMIT_CLICKED = "kyc_video_submit_clicked"
    const val EVENT_PAYMENT_METHOD_NUDGE = "Payment_method_nudge"
    const val EVENT_DOWNLOAD_PPOB_RECEIPT = "download_ppob_receipt"
    const val EVENT_LOYALTY_CHECK_UNLOCKED_VCOUPON_CODE = "loyalty_check_unlocked_coupon_code"


    // event lending
    const val EVENT_LENDING_BANNER_LAINNYA_MENU_CLICKED = "LendingBanner_LainnyaMenu_clicked"
    const val EVENT_OPEN_MICROSITE_CAMPAIGN = "open_microsite_campaign"

    const val EVENT_DOWNLOAD_REPORT = "download_init_book_report"
    const val EVENT_DOWNLOAD_BOOK_REPORT = "download_book_report"
    const val EVENT_SHARE_BOOK_REPORT = "share_book_report"

    const val EVENT_CALENDAR_NOTIF_OPEN = "collecting_calendar_notification_open"
    const val BUSINESS_PROFILE_SAVE = "business_profile_save"
    const val EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED = "cash_tab_add_cash_btn_clicked"
    const val EVENT_PROFILE_MISSION_ENTRY_POINT_CLICK = "profile_mission_entry_point_click"
    const val EVENT_CLICK_ADD_CUSTOMER_BTN = "click_add_customer_btn"

    const val EVENT_PAYMENT_ZERO_STRING1 = "save200x_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING2 = "paymentMethod_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING3 = "shareInvoice_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING4 = "savings_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING5 = "digitalPayments_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING6 = "socialGathering_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING7 = "easyRefunds_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING8 = "ppob_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING9 = "listrik_cta_clicked"
    const val EVENT_PAYMENT_ZERO_STRING10 = "noCash_cta_clicked"

    const val EVENT_PROFILE_VISIT = "profile_visit"

    // printing feature
    const val EVENT_LOCATION_PERMISSION_REQUEST = "location_permission"
    const val EVENT_BT_ADVANCE_TO_ENABLE = "click_button_on_bluetooth_enable_request"
    const val EVENT_BT_ENABLE_REQUEST_ALLOWED = "click_button_on_bluetooth_enable_continue"
    const val EVENT_BT_ENABLE_REQUEST_RETRY = "click_button_on_bluetooth_enable_retry"
    const val EVENT_PRINT_CUSTOMER_TRANSACTION = "print_customer_transaction"
    const val EVENT_PRINT_CASH_TRANSACTION = "print_cash_transaction"
    const val EVENT_PRINT_PAYMENT_TRANSACTION = "invoice_print_clicked"
    const val EVENT_PRINT_PROCESS_DONE = "invoice_print_completed"
    const val EVENT_ADD_PRINTER = "printer_add_button_click"
    const val EVENT_CONNECT_PRINTER = "printer_connect_click"
    const val EVENT_PRINTER_PAIRING_COMPLETE = "printer_pairing_completed"
    const val EVENT_TEST_PRINTER_COMPLETED = "test_print_completed"
    const val EVENT_SETTING_PRINTER = "printer_setting_click"
    const val EVENT_SETTING_SAVE_PRINTER = "printer_setting_save"
    const val EVENT_DISCONNECT_PRINTER = "printer_disconnect"
    const val SEARCHING_PRINTER_START = "searching_BP_start"
    const val SEARCHING_PRINTER_END = "searching_BP_completed"

    const val EVENT_COLLECTING_DATE_SET_NEW_COLLECTION = "collecting_date_set_new_collecting_date"

    // connect cash unpaid to payment
    const val EVENT_PAYMENT_TRANSACTION_TAGIH_INWARDS = "payment_transaction_tagih_clicked"

    // business card
    const val EVENT_SHARE_FROM = "share_business_card_dialog"
    const val EVENT_SHARE_PREVIEW = "business_card_share_preview"
    const val EVENT_EDIT_BUSINESS_CARD = "edit_business_card"
    const val EVENT_PREVIEW_BUSINESS_CARD_OPEN = "preview_business_card_open"

    const val EVENT_PAYMENT_TUTORIAL_OPEN = "payment_tutorial_open"
    const val EVENT_PAYMENT_NEW_USER_BOTTOMSHEET = "Payment_new_user_bottomsheet"
    const val EVENT_CUSTOMER_SUPPORT_REQUESTED = "mh_support_requested"
    const val EVENT_CLICK_PAYMENT_DETAILS_PAGE_INFO = "click_payment_details_page_info"
    const val EVENT_CLICK_REFRESH_STATUS = "click_refresh_status"
    const val EVENT_TAP_PAYMENT_GUIDE = "tap_payment_guide"
    const val EVENT_PAYMENT_EDIT_INVOICE_START = "payment_edit_invoice_start"
    const val EVENT_PAYMENT_EDIT_INVOICE_SAVE = "payment_edit_invoice_save"
    const val EVENT_PAYMENT_VIEW_PRIVACY = "payment_view_privacy"
    const val EVENT_SEND_PPOB_REMINDER = "send_ppob_reminder"
    const val EVENT_PPOB_CONFIRM = "ppob_confirm"


    const val EVENT_PAYMENT_SET_REFUND_BANK = "payment_refund_bank_set_clicked"
    const val EVENT_PAYMENT_SELECT_REFUND_BANK = "payment_refund_bank_selected"
    const val EVENT_PAYMENT_ADD_REFUND_BANK = "payment_refund_bank_added"
    const val EVENT_PAYMENT_CHECK_REFUND_BANK = "payment_refund_bank_checked"
    const val EVENT_PAYMENT_SAVE_REFUND_BANK = "payment_refund_bank_saved"
    const val EVENT_PAYMENT_EDIT_REFUND_BANK = "payment_edit_refund_bank"
    const val EVENT_CUSTOMER_DETAILS_UPDATE = "customer_details_update"
    const val EVENT_CUSTOMER_TRANSACTION_UPDATE = "customer_transaction_update"
    const val EVENT_FORM_EXIT_DIALOG_APPEAR = "form_exit_dialog_appear"
    const val EVENT_FORM_EXIT_DIALOG_CLOSED = "form_exit_dialog_closed"

    const val EVENT_KYC_CTA_CLICKED = "kyc_cta_clicked"
    const val EVENT_KYC_DATA_SENT = "kyc_data_sent"

    const val EVENT_READ_REFERRAL_TERMS_AND_CONDITION = "read_referral_terms_and_condition"
    const val EVENT_OPEN_LEARN_MORE_PAGE = "open_learn_more_page"
    const val EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE = "continue_business_detail_dialogue"

    // Lainnya Redesign
    const val EVENT_LAINNYA_WIDGET_CLICK = "lainyya_widget_click"

    const val EVENT_OPEN_LEFT_DRAWER = "open_left_drawer"
    const val EVENT_CHANGE_ACTIVE_BUSINESS = "change_active_business"

    const val EVENT_UTANG_HOME_REMINDER_CLICK = "utang_home_reminder_click"

    const val EVENT_INPUT_REFERRAL_CODE_SUCCESS = "input_referral_code_success"

    const val EVENT_PAYMENT_REMINDER_ENABLED = "payment_reminder_enabled"

    const val EVENT_FLOATING_ICON_DISMISS = "floating_icon_dismiss"

    const val EVENT_SEARCH_PERFORMED = "search_performed"

    const val EVENT_PAYMENT_INPUT_PIN = "payment_pin_inputted"
    const val EVENT_PAYMENT_FORGOT_PIN = "payment_forget_pin_clicked"

    const val HOME_TUTORIAL_OPEN = "home_tutorial_open"

    // region saldo events
    const val EVENT_WALLET_TOP_UP_CLICK = "wallet_top_up_click"
    const val EVENT_WALLET_AMOUNT_SELECTED = "wallet_amount_selected"
    const val CURRENT_WALLET_BALANCE_SALDO = "current_wallet_balance_saldo"
    // endregion

    // region payment detail screen events
    const val PAYMENT_DETAIL_VISIT = "payment_detail_visit"
    // endregion

    // region qris events
    const val EVENT_QRIS_GEO_LOCATION = "qris_geolocation_click"
    const val EVENT_QRIS_VIEW = "qris_view"
    const val EVENT_QRIS_DOWNLOAD_CLICK = "qris_download_click"
    const val EVENT_QRIS_SHARE_CLICK = "qris_share_click"
    const val EVENT_QRIS_CHANGE_BANK_ACCOUNT = "qris_change_bank_account"
    const val EVENT_QRIS_SET_BANK_ACCOUNT = "qris_set_bank_account"
    const val EVENT_MATCHING_INPUT_BANK_ACCOUNT = "matching_input_bank_account"
    // endregion

    // region charging events
    const val EVENT_FEE_INFO_ICON_CLICK = "fee_info_icon_click"
    const val EVENT_FEE_INFO_PAGE_CLICK = "fee_info_page_click"
    //endregion

    // region payment checkout events
    const val AVAILABLE_PAYMENT_METHOD = "available_payment_method"
    const val NON_AVAILABLE_PAYMENT_METHOD = "non_available_payment_method"
    // endregion

    // region Order history events
    const val EVENT_CLICK_PAYMENT_HISTORY_TAB = "click_payment_history_tab"
    const val EVENT_CLICK_PAYMENT_HISTORY_FILTER = "click_payment_history_filter"
    const val EVENT_CHOOSE_PAYMENT_HISTORY_FILTER = "choose_payment_history_filter"
    // endregion

    const val EVENT_CLICK_CATEGORY_EXPLAIN_DETAIL = "click_category_explain_detail"

    const val EVENT_PPOB_REMINDERS = "ppob_reminders"

    const val EVENT_APP_UPDATE_REDIRECT = "app_update_redirect"

    // bureau events
    const val EVENT_BW_SEND_SESSION_ID = "bw_send_session_id"
    const val EVENT_BUREAU_SESSION_ID_ACKNOWLEDGEMENT = "bureau_session_id_acknowledgement"
    const val EVENT_FRONTEND_SEND_EVENT_API = "frontend_send_event_api"
    const val EVENT_BACKEND_SEND_API_ACKNOWLEDGEMENT = "backend_send_api_acknowledgement"

    // bureau event params
    const val BW_SESSION_ID = "bw_session_id"
    const val BUREAU_RESPONSE = "bureau_response"
    const val SUCCESSFUL = "successful"
    const val FAILED = "failed"
    const val BACKEND_RESPONSE = "backend_response"

    //status value
    const val STATUS_COMPLETE = "complete"
    const val STATUS_SUCCESS = "success"
    const val STATUS_FAIL = "fail"
    const val STATUS_FAILED = "STATUS_FAILED"
    const val STATUS_FAILURE = "FAILURE"
    const val STATUS_FAIL_INVALID_NUMBER = "fail_invalid_number"
    const val STATUS_START = "start"
    const val STATUS_SENT_OTP = "sent_otp"
    const val STATUS_NEW_INSTALL = "new_install"
    const val STATUS_OLD_USER = "old_user"
    const val STATUS_CONNECTION_FAIL = "connection_fail"
    const val STATUS_CONNECTED = "connected"
    const val STATUS_CANCELLED = "cancelled"
    const val STATUS_WA_LINK = "wa_link"
    const val STATUS_RECEIVED_TOKEN = "received_token"
    const val STATUS_F_AUTH = "f_auth"
    const val STATUS_ALLOW = "allow"
    const val STATUS_DENY = "deny"

    //detail value
    const val DETAIL_FIRST_LOGIN = "first_login"
    const val DETAIL_RELOGIN = "relogin"
    const val DETAIL_CLICK_RETRY = "click_retry"
    const val DETAIL_ENTERED_OTP = "entered_otp"
    const val DETAIL_WA_SUCCESS = "wa_success"

    // inconsistent naming
    const val DETAIL_NO_NETWORK = "No network"
    const val DETAIL_OTP_CORRECT = "OTP Correct"
    const val DETAIL_INVALID_OTP = "invalid OTP"

    // event param name
    const val VISIBLE = "visible"
    const val ACTIVATED = "activated"
    const val CHANNEL = "channel"
    const val STATUS = "status"
    const val ACTIVATION_TYPE = "activation_type"
    const val RESEND_COUNT = "resend_count"
    const val DETAIL = "detail"
    const val ERROR_MESSAGE = "message"
    const val METHOD = "method"
    const val OTP_METHOD = "otp_method"
    const val TNC_CHECKBOX = "tnc_checkbox"
    const val TIME_TNC_CHECKED = "tnc_checkbox_timestamp"
    const val LINK_NAME = "link_name"
    const val TIME_TNC_AGREE = "time_tnc_agree"
    const val TERMS_AND_CONDITIONS_ID = "syarat_dan_ketentuan"
    const val PRIVACY_POLICY_ID = "kebijakan_privasi"
    const val CAMPAIGN = "campaign"
    const val NUMBER = "number"
    const val DEFAULT = "default"
    const val PAYMENT_METHOD = "payment_method"
    const val TRANSACTION_TYPE = "transaction_type"
    const val ENABLE_SALDO_BONUS = "enable_saldo_bonus"
    const val NOMINAL_SALDO_BONUS_USED = "nominal_saldo_bonus_used"
    const val CURRENT_SALDO_BONUS_BALANCE = "current_saldo_bonus_balance"
    const val BUSINESS_TYPE = "business_type"
    const val BUSINESS_TYPE_NAME = "business_type_name"
    const val BUSINESS_NAME = "business_name"
    const val ADDED_BY = "added_by"
    const val ADDED_TO = "added_to"
    const val AMOUNT = "amount"
    const val AMOUNT_SOURCE = "amount_source"
    const val DISCOUNT = "discount"
    const val GROSS_FEE = "gross_fee"
    const val LOYALTY_DISCOUNT = "loyalty_discount"
    const val SUBSCRIPTION_DISCOUNT = "subscription_discount"
    const val DATE = "date"
    const val FEATURE = "feature"
    const val BUYING_MODAL = "buying_modal"
    const val STATE = "state"
    const val ID = "id"
    const val TAB = "tab"
    const val DISPLAY_BY = "display_by"
    const val SELECT_ALL_CHECKED = "select_all_checked"
    const val NO_TRANSACTION = "no_transaction"
    const val OLD_CATEGORY = "old_category"
    const val NEW_CATEGORY = "new_category"
    const val LAINNYA_TILE = "lainnya_tiles"
    const val TEXT = "text"
    const val DISMISS_BY = "dismiss_by"
    const val TOTAL_PRODUCT = "total_product"
    const val PROVIDER = "provider"
    const val FREEFORM = "freeform"
    const val NOMINAL = "nominal"
    const val PRODUCT_COUNT = "product_count"
    const val PACKAGE_PRICE = "package_price"
    const val BUYING_PRICE_SNAKE_CASE = "buying_price"
    const val SELLING_PRICE = "selling_price"
    const val PROFIT_MARGIN = "profit_margin"
    const val ROUNDUP_TOGGLE = "roundup_toggle"
    const val PAYMENT_STATUS = "payment_status"
    const val PPOB_STATUS = "ppob_status"
    const val ADDED_NEW = "added_new"
    const val CHANGE_EXISTING = "change_existing"
    const val UPDATE_SELLING_PRICE = "update_selling_price"
    const val SELLING_PRICE_UPDATE_TYPE = "selling_price_update_type"
    const val UPDATED_MINIMUM_STOCK = "updated_minimum_stock"
    const val LAST_MINIMUM_STOCK_VALUE = "last_minimum_stock_value"
    const val UPDATED_MINIMUM_STOCK_VALUE = "updated_minimum_stock_value"
    const val IS_UPDATED_STOCK_VALUE_GREATER = "is_updated_stock_value_greater"
    const val EDIT_PRODUCT_SOURCE = "edit_product_source"
    const val VIA_TRANSAKSI_FORM = "via_transaksi_form"
    const val VIA_STOCK_MENU_VIEW_PRODUCT = "via_stock_menu_view_product"
    const val IS_PHONEBOOK_ENABLED = "is_phonebook_enabled"
    const val SHARE_MEDIUM = "share_medium"
    const val CUST_FAV_PIN_ENABLED = "cust_fav_pin_enabled"
    const val MODE = "mode"
    const val SAVE_BUSINESS_CARD = "save_business_card"

    const val AUTO_MATCHING = "auto_matching"
    const val MATCHING_SCORE = "matching_score"
    const val MATCHING_STATUS = "matching_status"
    const val OWNER = "owner"
    const val API_FAILURE = "api_failure"
    const val MISSING_INFO = "missing_info"
    const val ACCOUNT_DISABLED = "account_disabled"

    const val PROFILE_TYPE = "profile_type"
    const val USER_PROFILE_STATUS = "user_profile_status"
    const val BW_STORY = "BW_story"
    const val NON_BW_STORY = "non_BW_story"
    const val TOUR_FIRSTAUTORECORD_ADDED = "tour_firstautorecord_added"


    const val SHOW_BANK_DETAIL_POPUP = "show_bank_details_popup"


    const val LINK = "link"
    const val TITLE = "title"

    const val BW_POLICY_VISIBLE = "bw_policy_visible"
    const val POLICY_CLICK = "policy_click"
    const val UTANG_TAB_BOTTOM_SHEET = "utang_tab_bottom_sheet"
    const val TRANSAKSI_TAB_BOTTOM_SHEET = "transaksi_tab_bottom_sheet"
    const val INVENTORY_TAB_BOTTOM_SHEET = "inventory_tab_bottom_sheet"
    const val PAYMENT_TAB_BOTTOM_SHEET = "payment_tab_bottom_sheet"
    const val LAINNYA_TAB_BOTTOM_SHEET = "lainnya_tab_bottom_sheet"
    const val POPUP = "popup"
    const val POLICY_PAGE = "policy_page"

    const val ENTRY_POINT2 = "entry_point"
    const val SETTING_REASON = "setting_reason"
    const val FORGOT = "forgot"
    const val CHANGE = "change"
    const val MISSION_PAGE = "mission_page"
    const val TOGGLE_ACTION = "toggle_action"
    const val BANNER_VISIBLE = "banner_visible"
    const val TRANSACTION_COUNT = "transaction_count"
    const val QUALIFY_FOR_PEMBAYARAN_TAB = "qualify_for_pembayaran_tab"
    const val REFUND_REQUEST = "refund_request"
    const val IS_BUSINESS_CARD_TEMPLATE_NEW = "is_business_card_template_new"
    const val REFERRAL_HOME_PAGE = "referral_home_page"
    const val OPTIONS = "options"
    const val BOTH = "both"
    const val WA = "WA"
    const val WA_BUSINESS = "WA_BUSINESS"
    const val LOGO_REMOVAL_TOGGLE = "logo_removal_toggle"
    const val LOGIN_PASSWORD = "login_password_enabled"
    const val ALREADY_SET_PASSWORD = "already_set_password"

    const val SHARED_VIA = "shared_via"
    const val DATE_FILTER_SELECTED = "date_filter_selected"
    const val EVENT_PPOB_TAB_SWITCH = "ppob_tab_switch"
    const val EVENT_PPOB_FETCH_BILL = "ppob_bill_check_clicked"

    // inconsistent naming
    const val COUNTRY_CD = "countryCd"
    const val ENTRY_POINT = "entry_point"
    const val RESULT = "result"
    const val FAVOURITE = "favourite"
    const val UNFAV_TO_FAV = "unfav_to_fav"
    const val FAV_TO_UNFAV = "fav_to_unfav"
    const val PAYMENT_CONFIRMATION_PAGE = "payment_confimration_page"
    const val PAYMENT_INPUT_PAGE = "payment_input_page"
    const val FAVOURITE_TAB = "favorite_tab"
    const val AVAILABLE_METHODS = "available_methods"
    const val IS_METHOD_PREFILLED = "isMethodPrefilled"
    const val RECENT_TAB = "recent_tab"
    const val AMOUNT_SELECTION_PAGE = "amount_selection_page"
    const val ENTRY_SOURCE = "entry_source"
    const val USER_BANK = "user_bank"
    const val RECIPIENT_BANK = "recipient_bank"
    const val BUSINESS_CATEGORY = "business_category"
    const val REFERRAL_SUCCESS_BOTTOMSHEET = "referral_success_bottomsheet"

    // event param value
    const val CATEGORY = "category"
    const val CUSTOMER = "customer"
    const val CASH = "cash"
    const val PAYMENT = "payment"
    const val QRIS = "qris"
    const val LAINNYA = "lainnya"
    const val EDIT_BUTTON = "edit_button"
    const val PREVIEW = "preview"
    const val PRINTER_NAME = "printer_name"
    const val PRINTER_ID = "printer_id"
    const val OLD_PRINTER_NAME = "old_printer_name"
    const val NEW_PRINTER_NAME = "new_printer_name"
    const val FIRST_CONNECT = "first_connect"
    const val PAIRING_STATUS = "pairing_status"
    const val FAIL_REASON = "failed_reason"
    const val TEST_PRINT_STATUS = "test_print_status"
    const val SEARCHING_RESULT = "searching_result"
    const val FAILED_REASON = "failed_reason"
    const val TYPE = "type"
    const val PRODUCT_TYPE = "product_type"
    const val CHANGE_TO = "change_to"
    const val BANK_ACCOUNT_NAME = "bank_account_name"
    const val BANK_ACCOUNT_NUMBER = "bank_account_number"
    const val FIELD = "field"
    const val ALERT = "alert"
    const val TOTAL_BILL = "total_bill"
    const val CURRENT_STATUS = "current_status"
    const val DEBIT = "debit"
    const val CREDIT = "credit"
    const val PEMBAYARAN = "pembayaran"
    const val INSUFFICIENT_BALANCE = "insufficient_balance"
    const val PAYMENT_CHECKOUT = "payment_checkout"
    const val PPOB_CHECKOUT = "ppob_checkout"
    const val BUAT_PEMBAYARAN = "buat_pembayaran"
    const val PPOB = "ppob"
    const val SALDO = "saldo"
    const val SALES = "sales"
    const val EXPENSE = "expense"
    const val PROFILE = "profile"
    const val TUTORIAL = "tutorial"
    const val PROFILE_BTN = "profile_btn"
    const val PAYMENT_IN = "payment_in"
    const val PAYMENT_OUT = "payment_out"
    const val PAYMENT_DETAIL = "payment_detail"
    const val PAYMENT_STATUS_PAGE = "payment_status_page"
    const val UTANG = "utang"
    const val CASH_TRX = "cash_trx"
    const val CREATE_BUSINESS_BTN = "create_business_btn"
    const val TRANSAKSI = "transaksi"
    const val BUSINESS_CARD = "business_card"
    const val LOGIN_PAGE = "login_page"
    const val PASSWORD_PAGE = "password_page"
    const val REGISTRATION_PAGE = "registration_page"
    const val INITIAL = "initial"
    const val PASSWORD_SCREEN = "login_password"
    const val CREATE_PASSWORD_SCREEN = "create_password"
    const val CHANGE_PASSWORD_SCREEN = "change_password"
    const val FORGOT_PASSWORD_SCREEN = "forgot_password"
    const val PASSWORD_MATCH = "password_match"
    const val PASSWORD_UNMATCH = "password_unmatch"
    const val UNACCEPTED_SPECIAL_CHAR = "unaccepted_special_char"

    const val WRONG_PASSWORD = "wrong_password"
    const val CONTAIN_OLD_PASSWORDS = "contain_old_passwords"
    const val WRONG_OTP = "wrong_otp"
    const val SERVER_ERROR = "server_error"
    const val NONE = "none"
    const val NONE_2 = "NONE"
    const val SHOW = "show"
    const val HIDE = "hide"
    const val HOME_PAGE_ICON = "homepage_icon"
    const val HOME_PAGE = "homepage"
    const val HOME = "home"
    const val VERIFY_PAGE = "verify_page"
    const val CHECKMARK = "checkmark"
    const val PLUS_BUTTON = "plus_button"
    const val CUSTOM_QUANTITY = "custom_quantity"
    const val TOOLTIP_CREATE = "tooltip_create"
    const val TOOLTIP_CONTINUE = "tooltip_continue"
    const val CLICK_CLOSE = "click_close"
    const val CLICK_NEXT = "click_next"
    const val CLICK_OUTSIDE = "click_outside"
    const val IS_REMAINING_DEBT_SHOWN = "is_remaining_debt_shown"
    const val SEE_ALL = "see_all"
    const val SEARCH = "search"
    const val FILTER_OPEN = "filter_open"
    const val FILTER_CONFIRM = "filter_confirm"
    const val SCROLL_DOWN = "scroll_down"
    const val SETTINGS = "settings"
    const val LAINNYA_DIRECT_FIRST = "lainnya_direct_first_time"
    const val LAINNYA_DIRECT_EXISTING = "lainnya_direct_existing"
    const val PAYMENT_TUTORIAL_DIRECT_EXISTING = "payment_tutorial_direct_existing"
    const val PAYMENT_TUTORIAL_DIRECT_FIRST = "payment_tutorial_direct_first"
    const val REGISTER = "REGISTER"
    const val ABOUT_APP = "ABOUT_APP"
    const val RECORD_CASH_TRX = "RECORD_CASH_TRX"
    const val CHECK_PROFIT = "CHECK_TRANSAKSI_REPORT"
    const val CASH_TRANSACTION = "cash_transaction"
    const val EDC_ORDER_DETAIL = "edc_order_detail"
    const val CLICK_TUTORIAL = "TUTORIAL"
    const val FORM = "form"
    const val OLD_TRANSAKSI_FORM = "old_transaksi_form"
    const val NEW_TRANSAKSI_FORM = "new_transaksi_form"
    const val PPOB_PULSA = "ppob_pulsa"
    const val PPOB_PULSA_POSTPAID = "pulsa_postpaid"
    const val PPOB_LISTRIK = "ppob_listrik"
    const val PPOB_EWALLET = "ppob_ewallet"
    const val PPOB_PACKET_DATA = "ppob_packet_data"
    const val PPOB_GAMING_VOUCHERS = "ppob_gaming_vouchers"
    const val PPOB_VOUCHER_GAME = "ppob_voucher_game"
    const val PPOB_BPJS = "ppob_bpjs"
    const val BPJS = "bpjs"
    const val PDAM = "pdam"
    const val PPOB_PDAM = "ppob_pdam"
    const val PPOB_MULTIFINANCE = "ppob_multifinance"
    const val PPOB_CABLE = "ppob_cable"
    const val PPOB_VEHICLE_TAX = "ppob_e_samsat"
    const val INCOMPLETE = "incomplete"
    const val COMPLETE = "complete"
    const val PAGE_TYPE = "page_type"
    const val PAGE_NAME = "page_name"
    const val TWO_QUESTIONS = "2 questions"
    const val FOUR_QUESTIONS = "4 questions"
    const val MERCHANT_CUSTOMER_TYPE = "merchant_customer_type"
    const val SELLING_METHOD = "selling_method"
    const val ONLINE = "online"
    const val OFFLINE = "offline"
    const val INWARDS_AMOUNT = "inwards_amount"
    const val BANK = "bank"
    const val LOGIN_FLOW_DESIGN = "login_flow_design"
    const val JUNE21 = "june21"
    const val PRE_JUNE21 = "pre_june21"
    const val TNC = "tnc"
    const val PRIVACY_POLICY = "privacy_policy"
    const val WALLET = "wallet"
    const val PROCEED = "proceed"
    const val REASON = "reason"
    const val TOPUP_INVOICE_INACTIVE = "topup_invoice_active"
    const val PIN_FOR = "pin_for"
    const val AUTORECORD_TRX = "autorecord_trx"
    const val AUTORECORD = "autorecord"
    const val CARD = "card"
    const val NEW_PASSWORD = "new_password"
    const val CONFIRM_PASSWORD = "confirm_password"
    const val CURRENT_PASSWORD = "current_password"

    const val USER_PROFILE = "user_profile"

    const val APP_FORCE_UPDATE = "app_force_update"

    const val USER_PROFILE_MENU = "user_profile_menu"
    const val FILLED = "filled"

    const val OTHER = "other"
    const val WHATSAPP = "whatsapp"
    const val INSTAGRAM = "instagram"
    const val SMS = "sms"

    const val SUCCESS_MESSAGE = "success_message"

    const val REFERRAL_POINTS_BALANCE = "referral_points_balance"
    const val USER_REFERRAL_CODE = "user_referral_code"
    const val TOTAL_INVITES = "total_invites"
    const val SIGNED_UP = "signed_up"
    const val WAITING_TO_SIGN_UP = "waiting_to_sign_up"

    // OnBoarding page names
    const val BUSINESS_NAME_PAGE = "business_name"
    const val BUSINESS_CATEGORY_PAGE = "business_category"
    const val USAGE_PAST_PAGE = "usage_past"
    const val USAGE_GOAL_PAGE = "usage_goal"

    //user properties
    const val IS_GUEST = "is_guest_user"
    const val GUEST_USER_ID = "guest_user_id"
    const val USER_ID = "user_id"
    const val SKIP_LOGIN = "skip_login"
    const val IN = "in"
    const val OUT = "out"
    const val LAST_RECORD = "last_record"
    const val FIRST_RECORD = "first_record"
    const val THIRD_RECORD = "third_record"
    const val FIFTH_RECORD = "fifth_record"
    const val REMAINING_UTANG = "remaining_utang"
    const val CUSTOMER_ID = "customer_id"
    const val CUSTOMER_PHONE = "customer_phone"
    const val SETTLED_UTANG = "settled_utang"
    const val PREVIOUS_RECORD = "previous_record"
    const val IS_SETTLED_RECEIPT = "is_settled_receipt"
    const val PAYMENT_TAB_INTRO = "PAYMENT_TAB_INTRO"
    const val FRESH_RECEIPT = "fresh_receipt"
    const val SHARE_CASH_TRX_RECEIPT = "SHARE_CASH_TRX_RECEIPT"
    const val PRINT_CASH_TRX_RECEIPT = "PRINT_CASH_TRX_RECEIPT"
    const val SETTLE_UTANG = "SETTLE_UTANG"
    const val INVOICE = "invoice"
    const val CASH_TRANSACTION_ID = "cash_transaction_id"
    const val TRANSACTION_ID = "transaction_id"
    const val PAYMENT_DISBURSABLE_ID = "payment_disbursable_id"
    const val PAYMENT_ID = "payment_id"
    const val ORDER_ID = "order_id"
    const val PPOB_TYPE = "ppob_type"
    const val SUM_OF_ITEM_TOTAL = "sum_of_item_subtotal"
    const val TOTAL_PENJUALAN = "total_penjualan"
    const val TOTAL_PENJUALAN_VS_ITEM_SUBTOTAL = "total_penjualan_vs_item_subtotal"
    const val GREATER = "greater"
    const val EQUALS = "equals"
    const val LESS = "less"
    const val STOCK_MENU_SOURCE = "stock_menu"
    const val TXN_PROD_DETAIL_SOURCE = "trans_prod_details_shortcut"
    const val IS_FIRST_PRODUCT = "is_first_product"
    const val ONE_BUTTON = "1 button"
    const val TWO_BUTTON = "2 buttons"
    const val CURRENT_METHOD = "current_method"
    const val NEW_METHOD = "new_method"
    const val IS_OTP_METHOD_CHANGED = "is_otp_method_changed"
    const val FAV_CUST_PIN = "fav_cust_pin"
    const val PHONEBOOK_FAV_CUSTOMER_SECTION = "phonebook_fav_customer_section"
    const val PHONEBOOK_RECOMMENDATION_CUSTOMER_SECTION = "phonebook_recommendation_customer_section"
    const val LOAD_MORE_RECOMMENDATION_CUSTOMER_SECTION = "load_more_recommendation_customer_section"
    const val PHONEBOOK_EXISTING_RECORD_USER_SECTION = "phonebook_existing_record_user_section"
    const val PHONEBOOK_NO_RCRDS__USER_SECTION = "phonebook_no_rcrds_user_section"
    const val BW_TNC_ACCEPT = "bw_tnc_accept"
    const val LAINNYA_BANNER = "lainnya_banner"
    const val DEFAULT_LANDING_PAGE = "default_landing_page"
    const val ACTIVE = "active"
    const val INACTIVE = "inactive"
    const val MACHINE_NUMBER = "machine_number"
    const val VIN_NUMBER = "nik_vin"
    const val POLICY_NUMBER = "policy_number"
    const val SAMSAT_DOMICILE = "samsat_domicile"


    //Inventory history analytics

    // EVENT NAME
    const val EVENT_CLICK_REORDER = "click_reorder"
    const val EVENT_FAVOURITES_POPUP_ACTION = "favourites_popup_action"
    const val EVENT_CLICK_INVENTORY_MANAGE_STOCK = "click_inventory_manage_stock"
    const val EVENT_INVENTORY_VISIT = "inventory_visit"
    const val EVENT_INVENTORY_SELECT_PRODUCT = "inventory_select_product"
    const val EVENT_INVENTORY_SAVE_PRODUCT = "inventory_save_product"
    const val EVENT_INVENTORY_EDIT_STOCK = "inventory_edit_stock"
    const val EVENT_INVENTORY_ADD_PRODUCT = "click_add_product_button"
    const val EVENT_INVENTORY_SET_PRODUCT_UNIT = "inventory_set_product_unit"
    const val EVENT_INVENTORY_EDIT_PRODUCT = "inventory_edit_product"
    const val EVENT_INVENTORY_DELETE_PRODUCT = "inventory_delete_product"
    const val EVENT_INVENTORY_VIEW_PRODUCT = "inventory_view_product"
    const val EVENT_INFORMATION_OPTIONAL_CLICK = "click_optional_info"
    const val EVENT_INVENTORY_FEATURE_UNLOCKED = "inventory_feature_unlocked"
    const val EVENT_BW_POLICY_CLICK = "bw_policy_click"
    const val EVENT_DELETE_BUSINESS_PROFILE = "business_profile_delete"
    const val EVENT_DISMISS_IN_APP_BANNER = "dismiss_in_app_banner"
    const val EVENT_CLICK_CAROUSEL_BANNER = "click_carousel_banner"
    const val EVENT_KYB_TAKE_STORE_PHOTO_CLICKED = "kyb_take_store_photo_clicked"

    // param key/value
    const val STOCK_ITEM_LIST = "stock_list"
    const val STOCK_DETAIL = "stok_detail"
    const val REMAINING_STOCK = "remaining_stock"
    const val CRITICAL_STOCK = "critical_stock"
    const val OLD_STOCK = "old_stock"
    const val STOCK_CHANGE = "stock_change"
    const val MINIMUM_STOCK = "minimum_stock"
    const val MEASUREMENT_UNIT = "measurement_unit"
    const val OPERATION = "operation"
    const val PRODUCT_ID = "product_id"
    const val ADD_STOCK = "add_stock"
    const val REMOVE_STOCK = "remove_stock"
    const val ADD_PRODUCT_SCREEN = "add_product_screen"
    const val START = "start"
    const val CONFIRM = "confirm"
    const val CANCEL = "cancel"
    const val CREATE_NEW = "create_new"
    const val EDIT_STOCK = "edit_stock"
    const val UNIT_OPEN = "unit_open"
    const val STEP = "step"
    const val CANCEL_CREATE_NEW = "cancel_create_new"
    const val OLD_MIN_VALUE = "old_min_value"
    const val NEW_MIN_VALUE = "new_min_value"
    const val VALUE_CHANGE = "value_change"
    const val ACTIVE_TAB = "active_tab"
    const val ALL_PRODUCT = "all_products"
    const val TRUE = "true"
    const val FALSE = "false"
    const val PUSH_NOTIF = "push_notif"
    const val MANUAL_REMINDER = "manual_reminder"
    const val TRIGGER = "trigger"
    const val REACHED_TRANSACTION_GOAL = "reached_transaction_goal"
    const val UPDATE_TYPE = "update_type"
    const val EDIT = "edit"
    const val DELETE = "delete"
    const val CASH_TRANSACTION_TYPE = "cashTransaction_type"
    const val CASH_TRANSACTION_AMOUNT = "cashTransaction_amount"

    const val BUYING_PRICE = "buyingprice"
    const val STOCK = "stock"

    const val PPOB_FORM = "ppob_form"
    const val RETRY = "retry"
    const val USER_NAME = "user_name"
    const val EMAIL = "email"
    const val DATE_OF_BIRTH = "date_of_birth"


    const val PRODUCT_SAVE_SOURCE = "product_save_source"


    const val INVENTORY_VIEW_PRODUCT_EVENT = "inventory_view_product"
    const val INVENTORY_VISIT_EVENT = "inventory_visit"
    const val SOURCE = "source"
    const val START_DATE = "start_date"
    const val END_DATE = "end_date"
    const val FILE_TYPE = "file_type"
    const val CUSTOMER_DETAILS = "customer_details"
    const val CUSTOMER_DETAIL = "customer_detail"
    const val ACCEPTED = "accepted"
    const val REJECTED = "rejected"

    const val TRX_TYPE = "trx_type"

    const val OPEN_NOTIFICATION = "open_notification"

    const val CUSTOMER_EXISTING = "existing"
    const val CUSTOMER_PHONEBOOK = "phonebook"
    const val CUSTOMER_CREATE_MANUAL = "create_manual"

    const val CASH_TRANSACTION_ENTRY_POINT = "cash_transaction"
    const val FULLY_PAID = "fully_paid"
    const val FULLY_SETTLED = "fully_settled"
    const val DESCRIPTION_FILLED = "description_filled"
    const val CUSTOMER_SOURCE = "customer_source"
    const val CUSTOMER_FILLED = "customer_filled"
    const val CATEGORY_FILLED = "category_filled"

    const val COLLAPSE_MENU_OPEN = "collapse_menu_open"

    /**
     * for invoice customization
     * */
    const val INVOICE_SETTING_OPEN = "invoice_setting_open"
    const val INVOICE_SETTING_SAVE = "invoice_setting_save"
    const val SHARE_SINGLE_TRANSACTION_RECEIPT = "invoice_share_completed"
    const val IMAGE_FILLED = "image_filled"
    const val LOCATION_FILLED = "location_filled"
    const val PHONE_FILLED = "phone_filled"

    const val PROPERTY_INFORMATION_OPTIONAL = "current_state"
    const val INFORMATION_OPTIONAL_OPEN_STATE = "open"
    const val INFORMATION_OPTIONAL_CLOSE_STATE = "close"

    /**
     * for Streaks
     * */
    const val EVENT_STREAKS_SIGNUP_BANNER = "streaks_signup_banner"
    const val EVENT_STREAKS_COUPON_PAGE = "streaks_coupon_page"
    const val PROPERTY_STREAKS_BANNER_DISMISS = "Streaks_banner_dismiss"
    const val PROPERTY_DAY = "day"
    const val PROPERTY_DISMISS = "dismiss "
    const val CLAIM = "claim"
    const val STREAKS_CLAIM_REWARDS = "claim rewards"
    const val STREAKS_TERMS_CLICK = "terms_click "

    // Welcome Screen
    const val EVENT_CAROUSEL_SWIPED = "carousel_swiped"
    const val EVENT_PROCEED_CLICKED = "proceed_clicked"

    const val DEFAULT_IMAGE = "default_image"
    const val SCREEN = "screen"
    const val INTERACTION = "interaction"
    const val NEW_SCREEN = "new_screen"
    const val LAST_SCREEN = "last_screen"
    const val SWIPE_DIRECTION = "swipe_direction"

    const val INTERACTION_VAL = "manual"
    const val SWIPE_LEFT = "left"

    const val SWIPE_RIGHT = " right"
    const val BANNER = "banner"
    const val PEMBAYARAN_TUTORIAL = "pembayaran_tutorial"
    const val PAYMENT_NEW_USER_BOTTOMSHEET = "Payment_new_user_bottomsheet"
    const val PAYMENTS = "payments"
    const val PPOB_PAYMENTS = "ppob_payments"
    const val VIDEO = "video"
    const val SERVICE_FEES = "service_fees"
    const val DESCRIPTION = "description"
    const val ADD_BANK_SCREEN = "add_bank_screen"
    const val PENDING_PAYMENT_OUT = "pending_payment_out"

    // Cognitive load
    const val UTANG_HOME_SEARCH_BAR = "utang_home_search_bar"
    const val UTANG_HOME_HORIZONTAL_FILTER = "utang_home_horizontal_filter"

    const val TRANSAKSI_HOME_DATE_FILTER = "transaksi_home_date_filter"
    const val TRANSAKSI_HOME_FUNCTION_TRAY = "transaksi_home_function_tray"

    /*
    For Bulk Transaction
     */
    const val TOTAL_TRANSACTION_COUNT = "total_transaction_count"
    const val CASH_SALES_TRANSACTION_COUNT = "cash_sales_transaction_count"
    const val CASH_EXPENSE_TRANSACTION_COUNT = "cash_expense_transaction_count"
    const val DATE_OF_TRANSACTION = "date_of_transaction"
    const val TOTAL_SALES_AMOUNT = "total_sales_amount"
    const val TOTAL_EXPENSE_AMOUNT = "total_expense_amount"
    const val PROFIT_AMOUNT = "profit_amount"
    const val SAVE_METHOD = "save_method"

    const val BULK_TRANSACTION_ID = "transaction_id"
    const val BULK_DESCRIPTION_FILLED = "description_filled"
    const val BULK_TOTAL_SALES = "total_sales"
    const val BULK_TOTAL_EXPENSE = "total_expense"
    const val BULK_CASHTRANSACTION_TYPE = "cashTransaction_type"
    const val BULK_DATE_OF_TRANSACTION = "date_of_transaction"
    const val BULK_FORM = "form"
    const val REFERRAL_CODE_RECEIVER = "receiver"
    const val REFERRAL_CODE_SENDER = "sender"

    const val BACK_BUTTON_EXIT = "back_button_and_exit"
    const val SAVE_BUTTON = "save_button"
    const val BULK_TRANSAKSI_FORM = "transaksi_form"

    /*
    Transaksi New UI
     */
    const val TRANSAKSI_UI_VARIANT = "transaksi_ui_variant"

    const val REDESIGN_NOW = "redesign_may_2021"
    const val PRE_TRANSAKSI = "pre_may_2021"

    const val SHOW_BULK_TRANSAKSI = "bulk_enabled"

    const val SHOW_APP_SHORTCUTS = "app_shortcut"

    const val PAYMENT_DETAILS_CTA = "payment_details_cta"
    const val PAYMENTS_DETAIL_FAILED_STATE = "payments_detail_failed_state"
    const val REFUND_CTA = "refund_cta"
    const val PAYMENT_DETAILS = "payment_details"
    const val ORDER_HISTORY = "order_history"
    const val QRIS_TRANSACTION_DETAILS = "qris_transaction_details"
    const val QRIS_QR_DETAILS = "qris_qr_details"
    const val IN_APP_TICKET = "in-app ticket"

    // Invoice payment banner
    const val BANNER_LOCATION = "banner_location"
    const val CASH_TRANSACTION_INVOICE = "cash_transaction_invoice"
    const val CLICK_PAYMENT_BANNER = "click_payment_banner"

    // Help button on utang/transaksi
    const val TRANSAKSI_TAB = "transaksi_tab"
    const val HOME_BLANK_TAB_HELP = "home_blank_tab_help"
    const val UTANG_TAB = "utang_tab"
    const val HELP_ICON_REDIRECTED_TO = "redirected_to"
    const val LAINNYA_TAB = "lainnya_tab"
    const val LOGIN_OTP_PAGE = "login_otp_page"

    const val EXIT_DIALOGUE_ENABLED = "exit_dialogue_enabled"
    const val FORM_NAME = "form_name"

    const val UTANG_FORM = "utang_form"
    const val TRANSAKSI_FORM = "transaksi_form"
    const val CONTACT_FRAGMENT = "contact_fragment"

    const val OPTION_SELECTED = "option_selected"

    const val EXIT = "exit"
    const val CONTINUE = "continue"

    const val SEND_SMS_REMINDER = "send_sms_reminder"

    // New Utang Flow
    const val UTANG_UI_VARIATION = "utang_ui_variation"
    const val PRE_JUNE = "pre_june_2021"
    const val UPDATED_JUNE = "updated_june_2021"

    // Laiinya menu redesign
    const val ROW = "row"
    const val COLUMN = "column"
    const val BUTTON = "button"
    const val MISSION_NAME = "mission_name"
    const val REDIRECTED_TO = "redirected_to"

    const val VARIATION = "variation"
    const val WIDGET = "widget"
    const val WITHOUT_WIDGET = "without_widget"

    // User location
    const val USER_LOCATION_LATITUDE = "latitude"
    const val USER_LOCATION_LONGITUDE = "longitude"
    const val USER_LOCATION_PROVINCE = "province"
    const val USER_LOCATION_DISTRICT = "district"
    const val USER_LOCATION_POSTAL_CODE = "postal_code"

    // Missing Events
    const val DEFAULT_CATEGORY = "default_category"
    const val NOT_FILLED = "not_filled"
    const val ACTION_BY = "action_by"
    const val ACCOUNTING = "accounting"
    const val EDIT_NEW = "update_details"

    // All transaksi experiment
    const val REDESIGN_MAY_ALL_HIDDEN = "redesign_may_2021_all_hidden"
    const val REDESIGN_MAY_ALL_SHOWN = "redesign_may_2021_all_shown"
    const val REDESIGN_MAY_INV_HIDDEN = "redesign_may_2021_inv_hidden"
    const val REDESIGN_MAY_INV_CAT_HIDDEN = "redesign_may_2021_inv_cat_hidden"
    const val REDESIGN_MAY_CAT_NOTE_HIDDEN = "redesign_may_2021_cat_note_hidden"
    const val REDESIGN_MAY_CAT_HIDDEN = "redesign_may_2021_cat_hidden"
    const val REDESIGN_MAY_NOTES_HIDDEN = "redesign_may_2021_notes_hidden"

    const val PRE_MAY_2021 = "pre_may_2021"
    const val JULY_2021_ALL = "july_2021_all"
    const val JULY_2021_NO_STATUS_NO_NAME = "july_2021_no_status_no_name"
    const val JULY_2021_NO_STATUS_NO_NAME_NO_PRODUCT = "july_2021_no_status_no_name_no_product"
    const val JULY_2021_NO_PRODUCT = "july_2021_no_product"

    const val DEFAULT_TRANSAKSI_FORM_TAB = "default_transaksi_form_tab"
    const val HARD_CODED = "hard_coded"
    const val LAST_ACTION = "last_action"

    // Decimal qty
    const val QUANTITY_TYPE = "quantity_type"
    const val DECIMAL = "decimal"
    const val INTEGER = "integer"
    const val WHOLE_NUMBER_AS_DECIMAL = "whole_number_as_decimal"

    const val SELECTED_BUSINESS_NAME = "selected_business_name"
    const val NO_OF_BUSINESS = "no_of_business"
    const val ENTRY_POINT_NEW = "entry_point"
    const val HAMBURGER_CLICK = "hamburger_button_click"
    const val LEFT_SWIPE = "left_swipe"
    const val LAST_BUSINESS_NAME = "last_business_name"
    const val ACTIVE_BUSINESS_NAME = "active_business_name"

    const val DESTINATION = "destination"
    const val OPEN_CHATBOX = "open_chatbox"
    const val OPEN_WAPP_HOME = "open_wapphome"
    const val IS_WHATSAPP_INSTALLED = "is_whatsapp_installed"
    const val WA_WITH_CONTACT = "wa_with_contact"
    const val WA_WITH_AMOUNT = "wa_with_amount"

    const val REFERRAL_CODE = "referral_code"
    const val INPUT_METHOD = "input_method"
    const val MANUAL = "manual"
    const val AUTO_SUGGESTION = "auto_suggestion"
    const val DEEPLINK = "deeplink"
    const val CODE_ACCEPTED = "code_accepted"
    const val CODE_NOT_ACCEPTED = "code_not_accepted"
    const val ENTERED_REFERRAL_CODE = "entered_referral_code"
    const val INPUTTED_REFERRAL_CODE = "inputted_referral_code"
    const val REFERRER_ID = "referrer_id"
    const val IS_REFERRAL_CODE_VALID = "is_referral_code_valid"

    // POS
    const val INVENTORY = "inventory"
    const val ENTER_POS_MODE_BUTTON_CLICKED = "enter_pos_mode_button_clicked"
    const val POS_SEARCH_BAR_USED = "pos_search_bar_used"
    const val POS_PLUS_ICON = "pos_plus_icon"
    const val POS_PRODUCT_ADDED_TO_CART = "pos_product_added_to_cart"
    const val POS_PROCEED_TO_CART = "pos_proceed_to_cart"
    const val POS_CLEAR_CART = "pos_clear_cart"
    const val POS_CART_CHECKOUT = "pos_cart_checkout"
    const val POS_PAYMENT_COMPLETE = "pos_payment_complete"
    const val INVOICE_SHOW = "invoice_show"
    const val POS_INVOICE = "pos_invoice"
    const val POS_USER_TRANSACTION = "pos_user_transaction"
    const val PRODUCT_NAME = "product_name"
    const val PRODUCT_QTY = "product_qty"
    const val TOTAL_AMOUNT = "total_amount"
    const val CUSTOM_AMOUNT = "custom_amount"
    const val CHANGE_AMOUNT = "change_amount"
    const val TOTAL_PRODUCT_QTY = "total_product_qty"
    const val TOTAL_QTY = "total_qty"
    const val MODE_OF_PAYMENT = "mode_of_payment"
    const val MORE = "more"
    const val EXACT_AMOUNT = "exact_amount"
    const val TRANSACTION_SOURCE = "transaction_source"
    const val POS_UPDATE_PRODUCT_DETAILS_OPEN = "pos_update_product_details_open"
    const val POS_UPDATE_PRODUCT_DETAILS_SAVED = "pos_update_product_details_saved"
    const val OLD_QUANTITY = "old_quantity"
    const val NEW_QUANTITY = "new_quantity"
    const val EDIT_FROM = "edit_from"
    const val POS_ENABLED = "pos_enabled"
    const val POS = "POS"
    const val UNIQUE_PRODUCT_COUNT = "unique_product_count"
    const val SAVE_TRANSACTION_BUTTON = "save_transaction_button"
    const val PAY_EXACT_AMOUNT_BUTTON = "pay_exact_amount_button"
    const val STOREFRONT = "storefront"
    const val CART = "cart"
    const val DELETE_PRODUCT = "delete_product"
    const val NO_CHANGE = "no_change"
    const val EDIT_QUANTITY = "edit_quantity"
    const val POS_NOTE = "pos_note"
    const val TRANSAKSI_NOTE = "transaksi_note"
    const val DETAIL_TRANSAKSI_NOTE = "detail_transaksi_note"
    const val POS_MODE_TOOLTIP_VISIBLE = "pos_mode_tooltip_visible"
    const val POS_EXIT = "pos_exit"
    const val POS_SHORTCUT = "pos_shortcut"
    const val POS_ADD_PROD_SHORTCUT = "pos_add_prod_shortcut"
    const val POS_STOREFRONT = "pos_storefront"
    const val POS_STOREFRONT_NEXT_BUTTON = "pos_storefront_next_button"
    const val POS_CART = "pos_cart"
    const val POS_NON_CASH_ENABLE = "pos_non_cash_enable"
    const val NON_CASH = "non_cash"
    const val NON_CASH_CHANNEL = "non_cash_channel"

    // Inventory
    const val BUYING_PRICE_ENABLED = "buying_price_enabled"
    const val SUM_PRODUCT_BUYING_PRICE = "sum_product_buying_price"

    const val YES = "YES"
    const val NO = "NO"
    const val BESIDE_AMOUNT = "beside_amount"
    const val CONTACT_ICON = "contact_icon"
    const val ICON_POSITION = "icon_position"

    const val BANNER_CLICK = "banner_click"
    const val EVENT_HOMEPAGE_BANNER_CLICK = "homepage_banner_click"

    const val BANNER_NAME = "banner_name"
    const val NEW_FEATURE_PAYMENT_PULSA_BANNER = "new_feature_payment_pulsa_banner"
    const val HAMBURGER_MENU = "hamburger_menu"
    const val PAYMENT_ZERO_INFO = "payment_zero_info"
    const val CONTENT = "content"

    // stock on/off toggle
    const val USE_STOCK = "use_stock"
    const val STOCK_UI = "stock_ui"
    const val STOCK_TOGGLE_ENABLED = "july_2021_with_stock_toggle"
    const val STOCK_TOGGLE_NOT_ENABLED = "pre_july_2021"

    const val PROFILE_REFERRAL_FLOATING_ICON_VISIBLE = "profile_referral_floating_icon_visible"

    // transaction blank screen experiment
    const val TRANSACTION_0_RECORD_SNACKBAR = "transaksi_0_record_snackbar"
    const val TUTORIAL_OLD = "tutorial_old"
    const val VIDEO_ONLY_NEW = "video_only_new"
    const val VIDEO_AND_COACHMARK = "video_and_coachmark"
    const val NOT_VISIBLE = "not_visible"
    const val TUTORIAL_VIDEO_LOADED = "tutorial_video_loaded"
    const val TUTORIAL_VIDEO_NAME = "tutorial_video_name"
    const val TRANSAKSI_TUTORIAL = "transaksi_tutorial"
    const val TRANSAKSI_SNACK_BAR = "transaksi_snack_bar"
    const val LAINNYA_HELP_SECTION = "lainnya_help_section"
    const val TUTORIAL_VIDEO_DISMISSED = "tutorial_video_dismissed"
    const val AUTO_APPEAR = "auto_appear"
    const val SNACKBAR_CLICK = "snackbar_click"
    const val VIDEO_TUTORIAL = "video_tutorial"
    const val COACHMARK = "coachmark"

    const val FLOATING_ICON = "floating_icon"
    const val ICON_NAME = "icon_name"
    const val MONETORY_BONUS = "monetary_bonus"
    const val BOTTOM_RIGHT = "bottom_right"
    const val BOTTOM_LEFT = "bottom_left"
    const val LAINNYA_MENU = "lainnya_menu"
    const val DISMISS_METHOD = "dismiss_method"
    const val CLICK = "click"
    const val SCREEN_NAME = "screen_name"
    const val CLOSE = "close"
    const val DISMISS = "dismiss"
    const val ACCEPT = "accept"
    const val MONTH = "month"
    const val PDAM_DOMICILE = "pdam_domicile"

    const val OLD_STATUS = "old_status"
    const val NEW_STATUS = "new_status"

    const val EVENT_REPORT_DATE_FILTER = "report_date_filter"
    const val DEFAULT_FILTER = "default_filter"
    const val DATE_FILTER_NAME = "date_filter_name"

    //daily business update
    const val SHARE_DAILY_BUSINESS_UPDATE_CLICK = "share_daily_business_update_click"
    const val EVENT_DAILY_BUSINESS_UPDATE_WEBVIEW_LOADED = "daily_business_update_webview_loaded"
    const val COLOR_VARIANT = "color_variant"
    const val MOTIVATIONAL_TEXT = "motivational_text"
    const val LEFT_DRAWER_HAMBURGER_MENU = "left_drawer_hamburger_menu"
    const val BW_STORY_ENABLE = "bw_story_enable"

    const val TRANSAKSI_HOME_PAGE = "transaksi_home_page"
    const val UTANG_HOME_PAGE = "utang_home_page"

    const val SEARCH_KEYWORD = "search_keyword"

    const val UTANG_DUEDATE_ENABLE = "utang_duedate_enable"
    const val UTANG_FILTER_PIN = "utang_filter_pin"

    const val DUE_TODAY = "due_today"
    const val OVERDUE = "overdue"
    const val CUSTOMERS_UTANG = "customers_utang"
    const val USERS_UTANG = "users_utang"
    const val PAID = "paid"
    const val ALL = "ALL"

    const val LATEST = "latest"
    const val DUEDATE = "duedate"
    const val SMALLEST_AMOUNT = "smallest_amount"
    const val BIGGEST_AMOUNT = "biggest_amount"

    const val UTANG_SORT_BY = "utang_sort_by"

    const val IMAGE_SOURCE = "image_source"
    const val UPLOAD_PHOTO_ENABLED = "upload_foto_enabled"

    const val PREPAID = "prepaid"
    const val POSTPAID = "postpaid"
    const val PULSA_PREPAID = "pulsa_prepaid"
    const val LISTRIK_POSTPAID = "listrik_postpaid"
    const val LISTRIK_PREPAID = "listrik_prepaid"
    const val PARAM_PLN = "pln"
    const val PARAM_REASON = "reason"
    const val PARAM_PHONE_NUMBER = "phone_number"

    const val USER_PROP_ADVERTISING_ID = "advertising_id"
    const val PARAM_INDEX = "index"
    const val PARAM_BANNER_TITLE = "banner_title"
    const val PARAM_LANDING_URL = "landing_url"
    const val PAYMENT_HOME_PAGE = "payments home page"

    const val DAILY_BUSINESS_DATA_AVAILABLE = "daily_business_data_available"

    // region OTP related event properties
    const val ANDROID_ID = "androidId"
    const val IMEI_NUMBER = "imeiNumber"
    const val WIDE_VINE_ID = "wideVineId"
    const val ADVERTISING_ID = "advertisingId"
    const val ONBOARDING_NEEDED = "onboarding_needed"
    const val EXISTING_BOOK_COUNT = "existing_book_count"
    const val OTP_REQUEST_PHONE = "otp_request_phone"
    // endregion

    // region pin related event properties
    const val ADD_BANK_ACCOUNT = "add_bank_account"
    const val DELETE_BANK_ACCOUNT = "delete_bank_account"
    const val SHOW_BANK_ACCOUNT = "show_bank_account"
    const val SWITCH_BANK_ACCOUNT = "switch_bank_account"
    const val ADD_REFUND_BANK_ACCOUNT = "add_refund_bank_account"
    const val SWITCH_REFUND_BANK_ACCOUNT = "switch_refund_bank_account"
    const val CASH_TRANSACTION_DETAIL = "cash_transaction_detail"
    const val CHANGE_PIN = "change_pin"
    const val REFUND_BANK_SELECTED = "refund_bank_selected"
    // endregion

    // region saldo event properties
    const val CURRENT_WALLET_BALANCE = "current_wallet_balance"
    const val PEMBAYARAN_HISTORY = "pembayaran_history"
    const val SALDO_WALLET = "saldo_wallet"
    const val BNPL = "BNPL"
    //endregion

    // region payment detail event properties
    const val PAYMENT_TYPE = "payment_type"
    const val PAYMENTS_HISTORY = "payments_history"
    const val SORT = "sort"
    const val EDC = "edc"
    //endregion

    // region qris related event properties
    const val QRIS_DETAIL = "qris_detail"
    const val CURRENT_BANK = "current_bank"
    const val NEW_BANK = "new_bank"
    const val QRIS_BANNER = "qris_banner"
    const val QRIS_ICON = "qris_icon"
    const val PAY_IN_ICON = "payment_in_icon"
    const val PAY_OUT_ICON = "payment_out_icon"
    const val QRIS_BUTTON = "qris_button"
    const val QRIS_WIDGET = "qris_widget"
    const val LOCATION = "location"
    const val MWEB = "mweb"
    const val REJECTION_REASON = "rejection_reason"
    const val KYC_AND_QRIS_REJECTED = "kyc_and_qris_rejected"
    const val KYC_REJECTED = "kyc_rejected"
    const val QRIS_REJECTED = "qris_rejected"
    const val NAME_MATCHING_FAILED = "name_matching_failed"
    const val POST_ONBOARDING = "post_onboarding"
    const val ONBOARDING = "onboarding"
    const val QRIS_KYB_BOTTOM_SHEET = "qris_kyb_bottom_sheet"
    //endregion

    // region charging related event properties
    const val PLATFORM_FEE = "platform_fee"
    const val TRANSACTION_AMOUNT = "transaction_amount"
    const val CASHBACK_AMOUNT = "cashback_amount"
    const val PAYMENT_SEND = "payment_send"
    const val PAYMENT_REQUEST = "payment_request"
    const val UNDERSTAND = "understand"
    const val MORE_INFO = "more_info"
    //endregion

    const val NEW_TRX_CATEGORY_ENABLED = "new_trx_category_enabled"

    const val INVENTORY_EDIT_PRICE_CLICK = "inventory_edit_price_click"
    const val INVENTORY_EDIT_PRICE_SAVE = "inventory_edit_price_save"
    const val CATALOG = "catalog"
    const val MANUAL_INPUT = "manual_input"
    const val REFERRER_CODE = "referrer_code"
    const val ERROR_REASON = "error_reason"
    const val PRODUCT_SOURCE = "product_source"
    const val _0PRODUCT = "0product"
    const val NON_0_PRODUCT = "non0product"
    const val EDIT_PRICE = "edit_price"
    const val EDIT_PRODUCT_PRICE_SOURCE = "edit_price_source"

    //business profile

    const val NEW_BOOK = "new_book"
    const val BOOK_NAME = "book_name"
    const val BUSINESS_COUNT = "business_count"
    const val SECTION_STATUS = "section_status"
    const val BASIC_SECTION = "basic_section"
    const val OPERATIONAL_SECTION = "operational_section"
    const val ADDITIONAL_SECTION = "additional_section"
    const val EMPTY = "empty"
    const val PARTIALLY_FILLED = "partially_filled"
    const val FULLY_FILLED = "fully_filled"
    const val NEW = "new"

    object ProductCatalog {
        const val PRODUCT_CATALOG_ENABLED = "product_catalog_enabled"
        const val PRODUCT_CATALOG_EXPLANATION = "product_catalog_explanation"
        const val IMPORT_CATALOG_CLICK = "import_catalog_click"
        const val IMPORT_PRODUCT_CATALOG_SAVE = "import_product_catalog_save"
        const val NO_IMPORTED_PRODUCT = "no_imported_product"
        const val PRODUCT_CATEGORY = "product_category"
        const val PRODUCT_SUB_CATEGORY = "product_sub_category"
        const val FILTER_PRODUCT_CLICK = "filter_product_click"
        const val IMPORT_PRODUCT_CATALOG = "import_product_catalog"
    }

    object HomePage {
        const val HOMEPAGE_ENABLE = "homepage_enable"

        const val BANNER_TYPE = "banner_type"
        const val CAROUSEL = "carousel"
        const val BANNER_LOCATION = "banner_location"
        const val HOMEPAGE = "homepage"
        const val LAST_BANNER = "last_banner"
        const val NEW_BANNER = "new_banner"

        const val SECTION = "section"
        const val BUTTON = "button"
        const val BUTTON_NAME = "button_name"
        const val ENTRY_POINT = "entry_point"
        const val BUTTON_CLICK_EVENT = "button_click"
        const val EVENT_HOMEPAGE_BUTTON_CLICK = "homepage_button_click"
        const val BANNER_ORDER = "banner_order"
        const val BACK_TO_TOP = "back_to_top_click"

        const val NAVBAR = "navbar"
        const val INFO = "info"
        const val HOMEPAGE_INFO_BOTTOMSHEET = "homepage_info_bottomsheet"

        const val EVENT_HOME_VISIT = "home_visit"
        const val EVENT_TRANSAKSI_VISIT = "transaksi_visit"
        const val EVENT_INFO_CLICK = "info_click"
        const val HOMEPAGE_PPOB_PROMO_TAG_ENABLED = "is_promo_tag_enabled"
        const val HOMEPAGE_PROMO_TAG_ENABLED = "homepage_promo_tag_enabled"
    }

    const val ACTION = "action"
    const val ETA = "eta"
    const val COLLAPSE = "collapse"
    const val EXPAND = "expand"

    const val HOMEPAGE_INTRO_BANNER_CLICK = "homepage_intro_banner_click"
    const val HOMEPAGE_INTRO_BANNER_APPEAR = "homepage_intro_banner_appear"
    const val HOMEPAGE_BANNER = "homepage_banner"
    const val TRANSACTION_DETAIL = "transaction_detail"

    const val IS_PAYMENT_CATEGORY_MANDATORY = "is_payment_category_mandatory"
    const val USE_CASE = "use_case"
    const val MANDATORY_CATEGORY_ENABLED = "mandatory_category_enabled"
    const val MANDATORY_CATEGORY_ID = "mandatory_category_first_time"
    const val DEFAULT_CATEGORY_EXPENSES = "default_category_expenses"

    const val MANDATORY_CATEGORY_TEXT = "Pilih kategori agar pembukuanmu lebih rapi"

    const val EDIT_BUSINESS_ADDRESS = "edit_business_address"
    const val SAVE_BUSINESS_ADDRESS = "save_business_address"
    const val BUSINESS_PROFILE = "business_profile"
    const val NOTA = "nota"
    const val USER_BUSINESS_PROFILE_FILL_FOR_NM_REWARD = "user_business_profile_fill_for_nm_reward"
    const val NOTA_SETTING = "nota_setting"
    const val LOGO_REMOVAL = "logo_removal_on_nota"
    const val LOGO_REMOVAL_ON_NOTA = "logo_removal_on_nota"
    const val SECTION = "section"
    const val HOMEPAGE_SECTION = "homepage_section"
    const val FIRST = "first"
    const val SECOND = "second"
    const val FIRST_STEP_COMPLETE = "first_step_complete"
    const val SECOND_STEP_COMPLETE = "second_step_complete"
    const val ON = "on"
    const val OFF = "off"


    const val BUSINESS_ADDRESS = "business_address"
    const val BUSINESS_PROVINCE = "business_province"
    const val BUSINESS_CITY = "business_city"
    const val BUSINESS_DISTRICT = "business_district"
    const val BUSINESS_SUB_DISTRICT = "business_sub_district"
    const val BUSINESS_POSTAL_CODE = "business_postal_code"
    const val BUSINESS_STREET_ADDRESS = "business_street_address"
    const val PRODUCT = "product"
    const val METHOD_OF_PAYMENT = "Method of payment"
    const val MERCHANT_ID = "Merchant id"

    const val TRANSACTION_CARD_ENABLED = "transaction_card_enabled"
    const val CAPITAL_CARD_ENABLED = "capital_card_enabled"
    const val MOST_SELLING_ITEM_CARD_ENABLED = "most_selling_item_card_enabled"
    const val UTANG_CARD_ENABLED = "utang_card_enabled"
    const val AUTO_RECORD_CARD = "auto_record_card"
    const val FIRST_CARD = "First_card"
    const val CREATE_NEW_CASH = "create_new_cash"
    const val BD = "BD"
    const val TRANSACTION_CARD = "transaction_card"
    const val AUTORECORD_CARD = "autorecord_card"
    const val TRANSACTION_CATEGORY = "transaction_category"
    const val TRANSACTION = "transaction"
    const val CAPITAL = "capital"


    const val MONTH_SELECTED = "month_selected"


    const val CATEGORY_NAME = "category_name"
    const val NO_TRX = "no_trx"

    const val PER_CATEGORY_DELETE_TRANSACTION_CLICK = "per_category_delete_transaction_click"

    const val CATEGORY_DELETE_CONFIRM_CLICK = "category_delete_confirm_click"

    const val MARK_FAVOURITE_PRODUCT = "mark_favourite_product"
    const val MARK = "mark"
    const val REMOVE = "remove"

    const val SAVE_NEW_PRODUCT_CATEGORY = "save_new_product_category"
    const val CREATE_NEW_PRODUCT_CATEGORY = "create_new_product_category"
    const val SELECT_PRODUCT_CATEGORY_CLICK = "select_product_category_click"
    const val NEW_PRODUCT_CATEGORY_NAME = "new_product_category_name"
    const val OLD_PRODUCT_CATEGORY_NAME = "old_product_category_name"
    const val PRODUCT_CATEGORY_NAME = "product_category_name"
    const val OPEN_PRODUCT_CATEGORY_SECTION = "open_product_category_section"
    const val PRODUCT_FORM = "product_form"
    const val CASHIER_MODE = "cashier_mode"
    const val INVENTORY_TAB = "inventory_tab"
    const val PRODUCT_CATEGORY_SECTION = "product_category_section"
    const val DELETE_PRODUCT_CATEGORY = "delete_product_category"
    const val EDIT_PRODUCT_CATEGORY = "edit_product_category"
    const val PRODUCT_CATEGORY_SOURCE = "product_category_source"


    const val CLICK_SEARCH_BUTTON = "click_search_button"
    const val PERFORM_SEARCH_OPERATION = "perform_search_operation"
    const val SAVE_MANAGE_CATEGORY_CHANGES = "save_manage_category_changes"
    const val CLICK_MANAGE_PRODUCT_CATEGORY = "click_manage_product_category"
    const val CLICK_FILTER_FIELD = "click_filter_field"
    const val SELECT_FILTER_FIELD = "select_filter_field"
    const val SEARCH_CRITERIA = "search_criteria"
    const val FILTER_CRITERIA = "filter_criteria"
    const val CURRENT_FILTER_VALUE = "current_filter_value"
    const val NEW_FILTER_VALUE = "new_filter_value"
    const val OLD_FILTER_VALUE = "old_filter_value"
    const val DEFAULT_VALUE = "default_value"
    const val PRODUCT_CHECKED_COUNT = "product_checked_count"
    const val CHECKED = "checked"

    const val PRODUCT_CATEGORY_FILLED = "product_category_filled"
    const val NEW_PRODUCT_CATEGORY = "new_product_category"
    const val FAVORITE_MARKED = "favorite_marked"
    const val CREATE_MANUAL = "create_manual"
    const val CATEGORY_MOVE_CONFIRM_CLICK = "category_move_confirm_click"
    const val CATEGORY_MOVE_CANCEL_CLICK = "category_move_cancel_click"
    const val BD_CATEGORY_CLICK = "BD_category_click"


    const val PPOB_CARD = "ppob_card"
    const val EDUCATION_CARD = "eductation_card"
    const val EDUCATION_CARD_NAME = "education_card_name"
    const val CROSSSELL_CARD = "crosssell_card"
    const val CROSSSELL_CARD_NAME = "crosssell_card_name"
    const val AUTO_RECORD_ENABLED = "auto_record_card"
    const val CORE_PAYMENT_CARD = "core_payment_card"
    const val CATEGORY_DELETE_TRASACTION_CLICK = "category_delete_transaction_click"
    const val BD_TRY_NEW_FEATURE = "BD_try_new_feature"

    const val FAVOURITE_STATUS = "favourite_status"
    const val RECENT = "recent"
    const val REMINDER_SENT = "reminder_sent"
    const val FAVOURITE_CONTACT_NAME = "favourite_contact_name"
    const val FAVOURITE_CONTACT_NUMBER = "favourite_contact_number"
    const val PPOB_BUY_PAGE = "ppob_buy_page"
    const val ORDER_FORM = "order_form"
    const val PPOB_REMINDERS = "ppob_reminders"
    const val YELLOW = "yellow"
    const val RED = "red"
    const val PENDING_TIME = "pending_time"
    const val SET_SELLING_PRICE_PAGE = "set_selling_price_page"
    const val SMS_CHECKBOX_ENABLED = "SMS_checkbox_enabled"

    const val CATEGORY_PAGE_VIEW_VARIANT = "category_page_view_variant"
    const val NEW_UX_CATEGORY_SELECTION_VARIANT = "new_ux_category_selection_variant"
    const val TRX_CATEGORY_PAGE = "trx_category_page"
    const val TRX_CATEGORY_SOURCE = "trx_category_source"
    const val CATEGORY_PIN = "category_pin"
    const val CATEGORY_PAGE = "category_page"
    const val DEFAULT_CATEGORY_CHOOSEN = "default_category_choosen"
    const val EVENT_CATEGORY_LAINNYA_CLICK = "category_lainnya_click"
    const val EVENT_CATEGORY_FAQ_CLICK = "category_faq_click"
    const val SAVE_FAVOURITE = "save_favourite"
    const val NEVER_SHOW_AGAIN = "never_show_again"
    const val SET_PPOB_SELLING_PRICE = "set_ppob_selling_price"
    const val CATALOG_PAGE = "Catalog_page"
    const val TOOLS_PROMOTION = "tools_promotion"
    const val EVENT_TRAIN_TICKETS = "ppob_train_tickets"
    const val EVENT_CREATE_CATALOG = "create_catalog"
    const val FROM_SELLING_PRICE = "from_selling_price"
    const val TO_SELLING_PRICE = "to_selling_price"
    const val ADMIN_FEE = "admin_fee"
    const val REMINDERS = "reminders"
    const val EVENT_DOWNLOAD_SHARE_CATALOG = "download_share_catalog"
    const val EVENT_DEEPLINK_REDIRECTION_ACTIVITY = "deeplink_redirection_activity"
    const val SHOW_SELLING_PRICE = "show_selling_price"
    const val HIDE_SKU_WITHOUT_SELLING_PRICE = "hide_sku_without_selling_price"
    const val DOWNLOAD = "download"
    const val SHARE = "share"
    const val PEMBAYARAN_TAB = "pembayaran_tab"

    const val ACTION_TYPE = "action_type"
    const val CHANGE_PAYMENT_METHOD = "change_payment_method"
    const val CASHBACK = "cashback"

    const val BD_PAYMENT_IN = "BD_payment_in"
    const val BD_PAYMENT_OUT = "BD_payment_out"
    const val BD_CASHBACK_DETAIL_CLICK = "BD_cashback_detail_click"
    const val BD_PAYMENT = "BD_payment"

    const val CHECK_NOTIFICATION_DETAIL = "check_notification_detail"

    const val CROSS_ADOPTION_FEATURE_ENABLED = "cross_adoption_feature_enabled"
    const val CROSS_ADOPTION_POPUP_APPEAR = "cross_sell_popup_appears"
    const val CROSS_POPUP_COUNT = "popup_appearance_count_for_user"
    const val CROSS_SELL_CTA_CLICK = "cross_sell_cta_click"
    const val CROSS_SELL_CHOICE = "cross_sell_choice"
    const val CROSS_SELL_TUTORIAL_CLICK = "cross_sell_tutorial_cta_click"
    const val CROSS_SELL_PITCH = "cross_sell_pitch"
    const val TUTORIAL_OPTION_SELECTED = "tutorial_option_selected"

    const val EVENT_PAYMENT_FORGET_PIN_OTP = "payment_forget_pin_otp_inputted"
    const val EVENT_PAYMENT_EDIT_PIN_OTP = "payment_edit_pin_otp_inputted"
    const val EVENT_PAYMENT_CREATE_PIN_OTP = "payment_create_pin"

    const val LOYALTY_WIDGET_TYPE = "loyalty_widget_type"

    const val INFO_ICON_POSITION_CHANGED = "info_icon_position_changed"

    const val NOTA_STANDARD_ENABLED = "nota_standard_enabled"

    const val GET_LOCATION_PERMISSION = "get_location_permission"
    const val GET_LOCATION = "get_location"
    const val GET_LOCATION_TIME_IN_SECONDS = "get_location_time_in_seconds"
    const val AMOUNT_FAVOURITE_CONTACTS = "amount_favorite_contacts"

    //bnpl events and properties
    const val DRAWER = "drawer"
    const val SALDO_TALANGIN_DULU = "saldo_talangin_dulu"
    const val PPOB_SELECT_PAYMENT_METHOD = "ppob_select_payment_method"
    const val ANJUKAN = "anjukan"
    const val EVENT_BUTTON_CLICK = "button_click"
    const val EVENT_PPOB_BNPL_TRY_NUDGE = "ppob_bnpl_nudge_try_clicked"

    const val TRAIN_TICKET = "train_ticket"

    const val VERSION = "version"

    // region Time to Load traces
    const val TTL_ORDERS = "TimeToLoadOrders"
    const val API_TIME_ORDERS = "ApiTimeOrders"
    const val API_SERVICE = "api_service"
    const val API_RESPONSE = "api_response"
    const val FINPRO_SERVICE = "finpro_service"
    const val TRANSACTION_SERVICE = "transaction_service"
    const val LIMIT = "limit"
    // endregion

    const val ADD_SUPPLIER_PAGE = "add_supplier_page"

    //campaign
    const val HOMEPAGE_CAMPAIGN_CLICK="homepage:campaign_click"
    const val CAMPAIGN_NAME="campaign_name"

    //Settings Page
    const val EVENT_OPEN_LANGUAGE_DIALOG = "open_language_dialog"
    const val EVENT_CLICK_BACKUP_NOW = "clicked_backup_now"
    const val EVENT_PAYMENT_SETTINGS_EDIT_PIN = "payment_edit_pin_clicked"
    const val EVENT_USE_CASH_FEATURE = "use_cash_feature"
    const val EVENT_MANUAL_BACKUP_SUCCESS = "manual_backup_success"
    const val EVENT_MANUAL_BACKUP_FAIL = "manual_backup_fail"

    const val ANDROID = "android"
    const val MESSAGE = "message"
    const val CUSTOM = "custom"
}
