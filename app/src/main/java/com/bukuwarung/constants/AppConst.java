package com.bukuwarung.constants;

import android.content.Context;
import android.os.Environment;
import android.util.Pair;

import androidx.room.util.TableInfo;

import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.R;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.dialogs.businessselector.BusinessType;
import com.bukuwarung.preference.AppConfigManager;

import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppConst {
    public static final String PRODUCTION_FLAVOR = "prod";
    public static final String CLIENT_NAME = "BUKUWARUNG";

    public static final String PDF_MIME_TYPE = "application/pdf";

    public static final String BUKUWARUNG_EDC = "bukuwarung-edc";

    public static int APP_STATE_FIRST_TIME = 0;
    public static int APP_STATE_ALL_DELETED = 1;
    public static int APP_STATE_EXISTING = 3;

    public static int STALE_DATA = -1;
    public static int TRUE = 1;
    public static int FALSE = 0;

    public static final int SOFT_DELETE = 1;
    public static final int HARD_DELETE = 2;

    public static final int INVENTORY_TRACKING_DISABLED = 0;
    public static final int INVENTORY_TRACKING_ENABLED = 1;
    public static final int INVENTORY_PRODUCT_SUSPENDED = 2;

    public static final int TRANSACTIONS_POPUP = 101;
    public static final int ADD_CUSTOMER_POPUP = 102;

    public static final int GAMIFY_DIALOG = 1;
    public static final int STREAKS_DIALOG = 1;
    public static final int IN_APP_UPDATE = 1;
    public static final double ZERO = (double) 0;
    public static final double PAYMENT_FEE = (double) 6500;
    public static final long ONE_SECOND = 1000L;

    public static final String EMPTY_STRING = "";
    public static final Double ZERO_DOUBLE = 0.0;
    public static final int INT_ZERO = 0;
    public static final String CUSTOM_OP_HEADER = "x-ops-token";
    public static final String ID_HEADER = "x-firebase-auth";
    public static final String X_TOTAL_COUNT = "X-Total-Count";

    public static final String END_DATE = "end_date";
    public static final String SPECIFIC_DATE = "specific_date";
    public static final String START_DATE = "start_date";

    public static final String WA_PACKAGE = "com.whatsapp";

    public static final String DEEPLINK_KEY_TYPE = "type";
    public static final String DEEPLINK_KEY_DATA = "data";
    public static final String DEEPLINK_TYPE_WEB = "web";
    public static final String DEEPLINK_TYPE_WEB_BNPL = "web_bnpl";
    public static final String DEEPLINK_TYPE_ACTIVITY = "act";
    public static final String DEEPLINK_TYPE_REFERRAL = "ref";
    public static final String DEEPLINK_TYPE_REFERRAL2 = "referral";
    public static final String DEEPLINK_TYPE_REFERRAL_CODE = "referral_code";
    public static final String DEEPLINK_TYPE_BUKU = "buku_link";

    public static final String PLAY_STORE_INTENT_URL = "market://details?id=";
    public static final String WA_INTENT_URL = "https://api.whatsapp.com/send?phone=%s&text=%s";
    public static final String TUTORIAL_URL = "https://bukuwarung.com/tutorial";
    public static final String PRIVASI_URL = "https://bukuwarung.com/privasi";
    public static final String IG_BW_URL = "https://www.instagram.com/bukuwarung_/";
    public static final String FB_BW_URL = "https://www.facebook.com/bukuwarung/";
    public static final String FB_BW_GROUP_URL = "https://www.facebook.com/groups/***************";
    public static final String FAQ_BLOCKED_ACCOUNT_BW_URL = "https://bukuwarung.com/rekening-penerima-terkunci/";
    public static final String FAQ_USED_ACCOUNT_BW_URL = "https://bukuwarung.com/verifikasi-rekening-penerima/";
    public static final String FAQ_MATCHING_INFO_URL = "https://bukuwarung.com/verifikasi-akun-rekening-bank/";
    public static final String BANTUAN = "https://bukuwarung.com/bantuan/";
    public static final String PULSA_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/pulsa";
    public static final String EWALLET_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/e-wallet";
    public static final String TOKEN_LISTRIK_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/token-listrik";
    public static final String PAKET_DATA_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/paket-data";
    public static final String TAGIHAN_LISTRIK_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/tagihan-listrik";
    public static final String VOUCHER_GAME_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/voucher-game";
    public static final String BPJS_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/bpjs";
    public static final String PDAM_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/pdam";
    public static final String MULTIFINANCE_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/angsuran-kredit";
    public static final String INTERNET_DAN_TV_CABLE_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/internet-tv-kabel";
    public static final String VEHICLE_TAX_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/e-samsat";
    public static final String TRAIN_TICKET_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/tiket-kereta-api";
    public static final String QRIS_BANTUAN = "https://bukuwarung.com/qris-bukuwarung-sudah-tersedia";
    public static final String ABOUT_ACCOUNT_VERIFICATION = "https://bukuwarung.com/yuk-verifikasi-data/";
    public static final String SALDO_BONUS_URL = "https://bukuwarung.com/bukupoin-berubah-jadi-saldo-bonus/";
    public static final String KYC_TIER_INFO_URL = "https://bukuwarung.com/verifikasi-data/";
    public static final String PAYMENT_ASSIST_URL = "https://bukuwarung1.zohodesk.com/portal/id/kb/bayar-dan-tagih";
    public static final String ZOHODESK_HOME_URL = "https://bukuwarung1.zohodesk.com/portal/id/home";
    public static final String WA_BW_NUMBER = "+***********";
    public static final String PN_ID = "pnId";
    public static final String SILENT_NOTIFICATION_ACTION = "silent_notification_action";
    public static final String LOGOUT_ACTION = "logout";
    public static final String REFRESH_TOKEN = "refresh_token";
    public static final String REFRESH_MAINTENANCE_STATE = "refresh_maintenance_state";
    public static final int OTP_WAIT_TIME = 30;
    public static final String DEFAULT_BUSINESS_NAME = "Usaha Saya";
    public static final String DEFAULT_OWNER_NAME = "BukuWarung";
    public static final String NO_INTERNET_ERROR_MESSAGE = "Terjadi kesalahan dengan permintaanmu, silakan cek koneksi internetmu";
    public static final String UNABLE_TO_PROCESS_THIS_TIME = "Unable to process your request at this time, please try later!";
    public static final String IS_FROM_NOTIF = "isFromNotif";
    public static final String SHOW_LEADERBOARD_DIALOG = "showLeaderboardDialog";

    public static final String DEBT = "debt";
    public static final String CASH = "cash";
    public static final String POS = "pos";

    public static final int DEF_REMINDER_HOUR = 18;
    public static final int DEF_REMINDER_MINUTE = 0;

    public static final int ZERO_TRANSACTIONS = 0;
    public static final int TO_TUTORIAL_FROM_PROFILE_TAB_CODE = 99;

    public static final int DAY_INTERNAL_IN_MS = 24*60*60; // 24 hour timeout, basically almost impossible to exceed
    public static final int APP_LOCK_TIMEOUT_FROM_BACKGROUND_IN_SECOND = 10*60; // 10 minute timeout

    public static final long clickDebounceTime = 500L;

    public static final int CREDIT = 1; //income
    public static final int DEBIT = -1; //expense
    public static final String CATEGORY_CASH_OUT = "cashOut";
    public static final String CATEGORY_CASH_IN = "cashIn";
    public static final String CATEGORY_PEMBELIAN_STOK = "Pembelian stok";

    public static final int CUSTOMER_TRANSACTION = 1;
    public static final int CASH_TRANSACTION = 2;

    public static final int LUNAS = 1;
    public static final int BELUM_LUNAS = 0;

    public static final int STREAK_NOT_AVAILABLE = -1;
    public static final int STREAK_MIN_DAY = 1;
    public static final int STREAK_MAX_DAY = 7;

    public static final long ANIMATION_TIME = 1000;
    public static final long ANIMATION_STOP_TIME = 3000;

    public static final String URL = "url";
    public static final String TITLE = "title";

    public static final int INVOICE_ID_CHARACTERS = 10; // Last N characters from trx id
    public static final int INDONESIA_COUNTRY_CODE = 62;
    public static final int INDIA_COUNTRY_CODE = 91;
    public static final int MALAYSIA_COUNTRY_CODE = 60;
    public static final int SINGAPORE_COUNTRY_CODE = 65;

    public static final String KWH_LIMIT_RESPONSE_CODE = "106";

    public static final String DEEPLINK_SCHEME_BUKUWARUNG = "bukuwarung://";

    public static final String DEEPLINK_INTERNAL_URL = DEEPLINK_SCHEME_BUKUWARUNG + "launch";

    public static final String LEADERBOARD_STR="leaderboard";

    public static final String PLAY_STORE_PACKAGE_NAME = "com.android.vending";

    public static final String HOME = "home";

    public static final String LIVELINESS_DIR = "liveliness";

    public static final Integer FORBIDDEN_STATUS_CODE = 403;

    public static final Integer BAD_REQUEST_STATUS_CODE = 400;

    public static final int LIVELINESS_SUCCESS = 1043;
    public static final int LIVELINESS_FAIL = 1047;
    public static final String KYB_BANNER_ID = "kyb_banner";
    public static final String KYB_BANNER_TITLE = "KYB Banner";

    public static final int LIVELINESS_LIMIT_EXHAUST = 408;
    public static final int LIVELINESS_API_ERROR = 406;
    public static final int LIVELINESS_VIDA_FAILED = 204;
    public static final int NETWORK_CODE_301 = 301;
    public static final int NETWORK_CODE_403 = 403;
    public static final int NETWORK_CODE_400 = 400;



    public static final String LIVELINESS_URL = BuildConfig.API_BASE_URL_LOYALTY + "/los/api/accounts/data/kyc-liveliness";
    public static final String LIVELINESS_CALLBACK_URL = BuildConfig.API_BASE_URL_LOYALTY + "/los-web/saveLoanForm?livelinessCheckResult=";

    public static final String LIVELINESS_SUCCESS_SUFFIX = "success";
    public static final String LIVELINESS_FAIL_SUFFIX =  "failed";

    public static final String BUSINESS_PROFILE_COMPLETION = "BUSINESS_PROFILE_COMPLETION";
    public static final String USER_PROFILE_COMPLETION = "USER_PROFILE_COMPLETION";
    public static final String ANDROID = "android";
    public static final String SMS = "SMS";
    public static final String WA = "WA";
    public static final int PIN_DIGITS = 4;
    public static final int INPUT_TYPE_PASSWORD = 129;

    public static final int USER_ALREADY_REFERRED = 1004;

    public static File getCachedDocsFolder() {
        return new File(Application.getAppContext().getCacheDir(), "docs");
    }

    public static File getPublicDownloadsFolder() {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
    }

    public static List<BusinessType> defaultBusinessType(Context context) {
        ArrayList<BusinessType> businessTypes = new ArrayList();
        boolean hasOldBusinessCategoryInUse = false;
        String[] oldBusinessCategory = context.getResources().getStringArray(R.array.BusinessCategory);
        try{
            int oldBusinessCategoryCount = BusinessRepository.getInstance(context).getOldBusinessCategoryCount(oldBusinessCategory);
            hasOldBusinessCategoryInUse = oldBusinessCategoryCount>0;
        }catch (Exception e){
            hasOldBusinessCategoryInUse = AppConfigManager.getInstance().isFirstSessionAfterInstall();
        }
        String[] predefinedBusinessTypes = context.getResources().getStringArray(R.array.NewBusinessCategory);
        for (int i = 0; i < predefinedBusinessTypes.length; i++) {
            businessTypes.add(
              new BusinessType(
                      i,
                      predefinedBusinessTypes[i],
                      predefinedBusinessTypes[i]
              )
            );
        }

        if(!hasOldBusinessCategoryInUse) {
            businessTypes.add(new BusinessType(
                    36,
                    "Lainnya",
                    "Lainnya"
            ));
        }
        return businessTypes;
    }
    // old category name, new Pair(new category id, new category name)
    public static Map<String, Pair<Integer,String>> businessCategoryMapping = new HashMap<String, Pair<Integer,String>>(){{
        put("Restoran / Kafe / Tempat Makan", new Pair(3,"Makanan atau minuman"));
        put("Toko Roti /Kue", new Pair(3,"Makanan atau minuman"));
        put("Toko Minuman / Warkop / Thai Tea / Bubble", new Pair(3,"Makanan atau minuman"));  //not found

        put("Konter Pulsa, Tagihan, Aksesoris HP", new Pair(0,"Pulsa, token listrik, dan tagihan"));//not found
        put("Pulsa / Token Listrik", new Pair(0,"Pulsa, token listrik, dan tagihan"));//not found



        put("Butik / Pakaian / Aksesoris dan Penampilan", new Pair(1,"Pakaian, tas atau aksesoris"));

        put("Mini Market / Kelontong / Retail", new Pair(2,"Warung sembako/rokok"));
        put("Agen Grosir", new Pair(2,"Warung sembako/rokok"));
        put("Pemasok", new Pair(2,"Warung sembako/rokok"));

        put("Kesehatan dan Kecantikan", new Pair(6,"Produk kesehatan/kecantikan"));
        put("Toko Parfum", new Pair(6,"Produk kesehatan/kecantikan"));

        put("Salon dan Barbershop", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Laundry", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Logistik", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Dropshipping", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Konveksi", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Fotokopi / Printing", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Bengkel", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Distributor", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Penyewaan / Rental", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Warnet", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Koperasi", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Lembaga Organisasi", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Arisan", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));

        put("Makanan Segar", new Pair(5,"Buah, sayur, daging dan makanan segar"));
        put("Pertanian / Kebutuhan Tani", new Pair(8,"Pertanian atau kebutuhan tani"));


        put("Not Selected", new Pair(36,"Lainnya"));   //not found
        put("Olahraga dan Hobi", new Pair(36,"Lainnya"));
        put("Online Shop", new Pair(36,"Lainnya"));
        put("Kerajinan", new Pair(36,"Lainnya"));
        put("Mindring / Pemberi Pinjaman", new Pair(36,"Lainnya"));
        put("Asuransi", new Pair(36,"Lainnya"));
        put("Mebel", new Pair(36,"Lainnya"));
        put("Asongan", new Pair(36,"Lainnya"));
        put("Properti", new Pair(36,"Lainnya"));
        put("Toko Bangunan", new Pair(36,"Lainnya"));
        put("Vape Store", new Pair(36,"Lainnya"));
        put("Lainnya", new Pair(36,"Lainnya"));

        put("Produk kesehatan/kecantikan", new Pair(36,"Produk kesehatan/kecantikan"));
        put("Pertanian atau kebutuhan tani", new Pair(8,"Pertanian atau kebutuhan tani"));
        put("Buah, sayur, daging dan makanan segar", new Pair(5,"Buah, sayur, daging dan makanan segar"));
        put("Jasa (bengkel, laundry, salon, dan lainnya)", new Pair(38,"Jasa (bengkel, laundry, salon, dan lainnya)"));
        put("Produk kesehatan/kecantikan", new Pair(6,"Produk kesehatan/kecantikan"));
        put("Warung sembako/rokok", new Pair(2,"Warung sembako/rokok"));
        put("Makanan atau minuman", new Pair(3,"Makanan atau minuman"));
        put("Pakaian, tas atau aksesoris", new Pair(1,"Pakaian, tas atau aksesoris"));
        put("Pulsa, token listrik, dan tagihan", new Pair(0,"Pulsa, token listrik, dan tagihan"));
        put("Agen pembayaran", new Pair(37,"Agen pembayaran"));
        put("Galon, gas LPG atau bensin", new Pair(4,"Galon, gas LPG atau bensin"));

    }};

    public static Map<Integer, Pair<Integer,Integer>> businessCategoryStringMapping = new HashMap<Integer, Pair<Integer,Integer>>(){{
        put(R.string.reaturant, new Pair(3,R.string.food_and_beverage));
        put(R.string.bakery, new Pair(3,R.string.food_and_beverage));
        put(R.string.drinkShop, new Pair(3,R.string.food_and_beverage));
        put(R.string.creditBill, new Pair(0, R.string.pulsa_token_listrik));
        put(R.string.clothing, new Pair(1, R.string.fashion_accessories));
        put(R.string.minimarket, new Pair(2, R.string.groceries_cigg));
        put(R.string.wholesaler, new Pair(2, R.string.groceries_cigg));
        put(R.string.supplier, new Pair(2, R.string.groceries_cigg));
        put(R.string.health, new Pair(6, R.string.food_snacks));
        put(R.string.perfumeShop, new Pair(6, R.string.food_snacks));
        // AGEN PEMBARAYAN
        put(R.string.hairSalon, new Pair(38, R.string.minimarket_new));
        put(R.string.laundry, new Pair(38, R.string.minimarket_new));
        put(R.string.logistic, new Pair(38, R.string.minimarket_new));
        put(R.string.dropshipper, new Pair(38, R.string.minimarket_new));
        put(R.string.convection, new Pair(38, R.string.minimarket_new));
        put(R.string.photocopy, new Pair(38, R.string.minimarket_new)); // Percetakan
        put(R.string.Bengkel, new Pair(38, R.string.minimarket_new));
        put(R.string.distributor, new Pair(38, R.string.minimarket_new));
        put(R.string.penyewaanRental, new Pair(38, R.string.minimarket_new));
        put(R.string.internetCafe, new Pair(38, R.string.minimarket_new));
        put(R.string.union, new Pair(38, R.string.minimarket_new));
        put(R.string.organization, new Pair(38, R.string.minimarket_new));
        put(R.string.Arisan, new Pair(38, R.string.minimarket_new));
        put(R.string.freshfood, new Pair(5, R.string.agriculture_pertanian));
        // Galon, gas
        put(R.string.agriculture, new Pair(8, R.string.health_beauty));
        // not selected
        put(R.string.sportsHobby, new Pair(36, R.string.others));
        put(R.string.onlineShop, new Pair(36, R.string.others));
        put(R.string.artCraft, new Pair(36, R.string.others));
        put(R.string.mindringPeminjam, new Pair(36, R.string.others));
        put(R.string.insurance, new Pair(36, R.string.others));
        put(R.string.furniture, new Pair(36, R.string.others));
        put(R.string.peddler, new Pair(36, R.string.others));
        put(R.string.property, new Pair(36, R.string.others));
        put(R.string.storeBuilding, new Pair(36, R.string.others));
        put(R.string.vapeStore, new Pair(36, R.string.others));
        put(R.string.others, new Pair(36, R.string.others)); // font in Lainnya
    }};

}
