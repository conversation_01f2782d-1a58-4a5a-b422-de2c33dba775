package com.bukuwarung.constants;

import android.Manifest;

public class PermissionConst {
    public static final int REQ_TAKE_PICTURE_PERMISSON = 100;
    public static final int REQ_PICK_IMAGE_PERMISSON = 1;
    public static final int TAKE_PHOTO = 10;
    public static final int WRITE_EXTERNAL_STORAGE = 331;
    public static final int ACCESS_LOCATION = 212;
    public static final int BLUETOOTH_PERMISSION = 214;
    public static final int READ_PHONE_STATE = 215;
    public static final int SEND_SMS = 216;
    public static final int WRITE_STORAGE = 333;

    public static final int CONTACT_PERMISSION = 324;
    public static final int REQ_READ_WRITE_CONTACTS_PERMISSION = 323;


    public static final int CALL_PHONE = 320;
    public static final int REQUEST_CODE_LAT_LONG = 14;

    public static final String[] CAMER_AND_STORAGE = new String[]{"android.permission.CAMERA", "android.permission-group.STORAGE"};
    public static final String[] READ_WRITE_CONTACTS = new String[]{"android.permission.WRITE_CONTACTS", "android.permission.READ_CONTACTS"};
    public static final String[] WRITE_EXTERNAL_STORAGE_PERMISSION_STR = new String[]{ Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
}
