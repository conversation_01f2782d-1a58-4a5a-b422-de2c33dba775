package com.bukuwarung.constants

import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PaymentHistory

object PaymentConst {
    const val TYPE_PAYMENT_IN = 0
    const val TYPE_PAYMENT_OUT = 1
    const val TYPE_PAYMENT_PIN_CHANGE = 3
    const val TYPE_PPOB = 4
    const val TYPE_QRIS_INT = 5
    const val MANUAL_REMINDER = "manualreminder"
    const val DANA = "DANA"
    const val LINKAJA = "LINKAJA"
    const val OVO = "OVO"
    const val SHOPEEPAY = "SHOPEEPAY"
    const val QRIS = "QRIS"
    const val SALDO = "SALDO"
    const val BNPL = "BNPL"
    const val BNPL_WHITELISTED = "WHITELISTED"
    const val ADVANCE_TIER_WHITELISTED = "ADVANCE_TIER_WHITELISTED"
    const val BUKU = "BUKU"
    const val SALDO_CAMEL = "Saldo"
    const val ALFAMART = "ALFAMART"
    const val GOPAY = "GOPAY"
    const val BCA = "BCA"
    const val BNI = "BNI"
    const val BRI = "BRI"
    const val MANDIRI = "MANDIRI"
    const val PERMATA = "PERMATA"
    const val CIMB = "CIMB"
    const val PAYMENT_VIDEO_TUTORIAL_URL = "https://youtu.be/yMPmKOfCKSc"
    const val SALDO_LOGO = "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/saldo.png"
    const val ABOUT_PAYMENT_CHARGING_URL = "https://bukuwarung.com/charging-info/"
    const val DEBT_PAYMENT_CATEGORY = "Pembayaran Utang/Cicilan"

    const val OTP_WAIT_TIME_IN_SECONDS = 25
    const val ONE_SECOND = 1000L

    const val GAME_ACTIVE = "ACTIVE"
    const val GAME_PAYMENT_OUT_SUBSCRIPTION_MODEL = "payment_out_subscription_model"

    const val PaymentRequest = "PaymentRequest"
    const val DisbursementRequest = "DisbursementRequest"

    const val MILLION = 1000000.0
    const val BILLION = **********.0

    const val RECORD_IN_NONE = "none"
    const val RECORD_IN_DEBT = "debt"
    const val RECORD_IN_CASH = "cash"
    const val RECORD_IN_DEBT_AND_CASH = "debt_and_cash"
    val PAYMENT_ZERO_ANALYTICS_LIST = listOf(AnalyticsConst.EVENT_PAYMENT_ZERO_STRING1,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING2,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING3,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING4,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING5,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING6,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING7,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING8,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING9,
        AnalyticsConst.EVENT_PAYMENT_ZERO_STRING10
    )

    enum class BankAccountOwner {
        SELF, BUSINESS_SELF, BUSINESS_PARTNER, RELATIVE, OTHERS
    }

    const val BANK_TRANSFER = "BANK_TRANSFER"
    const val CONTACTS_LIMIT = 250

    const val QRIS_COMPATIBLE_BANKS_QUERY = "QRIS_COMPATIBLE"
    const val PAYMENT_IN = "payment_in"
    const val PAYMENT_OUT = "payment_out"
    const val QRIS_FORM_RESULT = "qris_form_result"
    const val QRIS_FORM_SUCCESS = "success"
    const val QRIS_BOOK_ID = "qris_book_id"
    const val QRIS_BANK_SET_FOR = "qris_bank_set_for"
    const val REFUND = "refund"
    const val APPEAL_FLOW_SUBMITTED = "appeal_flow_submitted"
    const val SUCCESS_TOP_MESSAGE = "success_top_message"

    const val PAYMENT_HP_HANDLER = "payments_hp_handler"
    const val QRIS_HANDLING_KEY = "qris"
    const val PAY_IN_HANDLING_KEY = "tagih_uang"
    const val PAY_OUT_HANDLING_KEY = "kirim_uang"
    const val IN = "IN"// this constant should be in capitals

    // QRIS status
    const val INITIAL_LOWERCASE = "initial"
    const val PENDING_LOWERCASE = "pending"
    const val APPROVED_LOWERCASE = "approved"
    const val REJECTED_LOWERCASE = "rejected"

    const val DEFAULT_FILTER_CALENDAR_MAX_RANGE = 31

    // Order history related const
    enum class HISTORY_TABS {
        ALL, PPOB, PEMBAYARAN, SALDO, SALDOBONUS
    }

    // Product types
    const val TYPE_DIGITAL_PRODUCT = "DIGITAL_PRODUK"
    const val TYPE_PULSA = PpobConst.CATEGORY_PULSA
    const val TYPE_PULSA_POSTPAID = PpobConst.CATEGORY_PULSA_POSTPAID
    const val TYPE_LISTRIK = PpobConst.CATEGORY_LISTRIK
    const val TYPE_EMONEY = PpobConst.CATEGORY_EWALLET
    const val TYPE_PAKET_DATA = PpobConst.CATEGORY_PAKET_DATA
    const val TYPE_VOUCHER_GAME = PpobConst.CATEGORY_VOUCHER_GAME
    const val TYPE_BPJS = PpobConst.CATEGORY_BPJS
    const val TYPE_PDAM = PpobConst.CATEGORY_PDAM
    const val TYPE_ANGSURAN = PpobConst.CATEGORY_MULTIFINANCE
    const val TYPE_INTERNET_DAN_TV_KABEL = PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE
    const val TYPE_VEHICLE_TAX = PpobConst.CATEGORY_VEHICLE_TAX
    const val TYPE_TRAIN_TICKET = PpobConst.CATEGORY_TRAIN_TICKET

    const val TYPE_PEMBAYARAN = "PEMBAYARAN"
    const val TYPE_PAY_IN = "IN"
    const val TYPE_PAY_OUT = "OUT"
    const val TYPE_QRIS = "QRIS"

    val TYPE_PEMBAYARAN_CHILDREN = arrayListOf(TYPE_PAY_IN, TYPE_PAY_OUT, TYPE_QRIS)

    const val TYPE_SALDO_ALL = "SALDO_ALL"
    const val TYPE_SALDO_IN = "SALDO"
    const val TYPE_SALDO_OUT = "SALDO_OUT"

    const val TYPE_SALDO_TAB = "SALDO"
    const val TYPE_CASHBACK_TAB = "CASHBACK"

    const val CASHBACK_TYPE_VOUCHER = "VOUCHER"

    val TYPE_SALDO_CHILDREN = arrayListOf(TYPE_SALDO_IN, TYPE_SALDO_OUT, PaymentHistory.TYPE_SALDO_BNPL)

    const val TYPE_CASHBACK_ALL = "CASHBACK_ALL"
    const val TYPE_CASHBACK = "SALDO_REDEMPTION"
    const val TYPE_CASHBACK_IN = "CASHBACK_IN"
    const val TYPE_CASHBACK_OUT = "CASHBACK_OUT"
    const val TYPE_PAYMENT_OUT_SUBSCRIPTION = "PAYMENT_OUT_SUBSCRIPTION"

    val TYPE_CASHBACK_CHILDREN = arrayListOf(TYPE_CASHBACK)

    const val TYPE_DEFAULT = "DEFAULT"

    // Date Filters
    enum class DATE_PRESET {
        TODAY, YESTERDAY, THIS_WEEK, LAST_WEEK, THIS_MONTH, LAST_MONTH, THIS_YEAR, LAST_YEAR,
        LAST_SEVEN_DAYS, LAST_TEN_DAYS, ALL, CUSTOM_RANGE
    }

    // Sorting
    const val SORT_BY_CREDIT_TIME_DSC = "CASHBACK_CREDIT_TIME_DESC"
    const val SORT_BY_EXPIRY_DSC = "CASHBACK_EXPIRY_DESC"

    // Tags for order history
    const val TAG_PRICE_LABEL = "TAG_PRICE_LABEL"
    const val TAG_HISTORY_DETAIL = "TAG_HISTORY_DETAIL"
    const val TAG_BUKU_ORIGIN = "TAG_BUKU_ORIGIN"

    fun isCashbackIn(order: FinproOrderResponse): Boolean {
        return order.type.equals(TYPE_CASHBACK_IN, ignoreCase = true)
    }

    fun isCashbackOut(order: FinproOrderResponse): Boolean {
        return order.type.equals(TYPE_CASHBACK_OUT, ignoreCase = true)
    }

    // Constants for access control based on KYC Tier
    const val KYC_PAYMENT_IN = "PAYMENT_IN"
    const val KYC_PAYMENT_OUT = "PAYMENT_OUT"
    const val KYC_SALDO_IN = "SALDO_IN"
    const val KYC_SALDO_OUT_FOR_PPOB = "SALDO_OUT_FOR_PPOB"
    const val KYC_PPOB = "PPOB"
    const val KYC_ADD_BANK_ACCOUNT = "ADD_BANK_ACCOUNT"
    const val KYC_QRIS = "QRIS"

    const val BANK_DOWN_TIME = "DOWN_TIME"
    const val BNPL_DURATION = 14

    const val BILL_AMOUNT_VIEW_TAG = "bill_amount"
    const val BILL_AMOUNT_PARENT_VIEW_TAG = "bill_amount_parent"
    const val SERVICE_FEE_VIEW_TAG = "service_fee"
    const val SERVICE_FEE_PARENT_VIEW_TAG = "service_fee_parent"
    const val TOTAL_PAYMENT_VIEW_TAG = "total_payment"
    const val PAYMENT_AMOUNT_VIEW_TAG = "payment_amount"
    const val PAYMENT_AMOUNT_PARENT_VIEW_TAG = "payment_amount_parent"
    const val SERVICE_FEE_DIVIDER_VIEW_TAG = "service_fee_divider"

    object Feature {
        const val PAYMENT_IN = "PAYMENT_IN"
        const val PAYMENT_OUT = "PAYMENT_OUT"
        const val SALDO_IN = "SALDO_IN"
        const val QRIS = "QRIS"
    }

    const val PAYMENT_IN_FEATURE_LABEL = "Tagih"
    const val PAYMENT_OUT_FEATURE_LABEL = "Bayar"
    const val QRIS_FEATURE_ANALYTICS_NAME = "qris"

    // tags for payment method
    const val DISABLE = "DISABLE"
    const val SALDO_RECHARGE = "SALDO_RECHARGE"
    const val CAMPAIGN = "CAMPAIGN"
    const val ADVERTISEMENT = "ADVERTISEMENT"
    const val DAILY_LIMIT_REACHED = "DAILY_LIMIT_REACHED"
    const val MONTHLY_LIMIT_REACHED = "MONTHLY_LIMIT_REACHED"
    const val SALDO_FREEZE = "SALDO_FREEZE"
}
