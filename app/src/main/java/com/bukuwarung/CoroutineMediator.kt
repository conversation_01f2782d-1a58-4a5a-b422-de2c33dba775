package com.bukuwarung

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.Continuation
import kotlin.coroutines.CoroutineContext

@Singleton
class CoroutineMediator @Inject constructor() {
    fun <T> onResult(callback: (T?, Throwable?) -> Unit): Continuation<T?> {
        return onResult(scope = GlobalScope, context = Dispatchers.Main, callback)
    }

    fun <T> onResult(
        context: CoroutineDispatcher,
        callback: (T?, Throwable?) -> Unit,
    ): Continuation<T?> {
        return onResult(scope = GlobalScope, context, callback)
    }

    fun <T> onResult(
        scope: CoroutineScope,
        callback: (T?, Throwable?) -> Unit,
    ): Continuation<T?> {
        return onResult(scope, context = Dispatchers.Main, callback)
    }

    fun <T> onResult(
        scope: CoroutineScope,
        context: CoroutineDispatcher,
        callback: (T?, Throwable?) -> Unit,
    ): Continuation<T?> = object : Continuation<T?> {
        override val context: CoroutineContext get() = context

        override fun resumeWith(result: Result<T?>) {
            scope.launch(context) {
                callback(result.getOrNull(), result.exceptionOrNull())
            }
        }
    }

    fun <T> collect(
        flow: Flow<T>,
        callback: (T) -> Unit,
    ) = collect(flow, scope = GlobalScope, context = Dispatchers.Main, callback)

    fun <T> collect(
        flow: Flow<T>,
        context: CoroutineDispatcher,
        callback: (T) -> Unit,
    ) = collect(flow, scope = GlobalScope, context, callback)

    fun <T> collect(
        flow: Flow<T>,
        scope: CoroutineScope,
        callback: (T) -> Unit,
    ) = collect(flow, scope, context = Dispatchers.Main, callback)

    fun <T> collect(
        flow: Flow<T>,
        scope: CoroutineScope,
        context: CoroutineDispatcher,
        callback: (T) -> Unit,
    ) {
        scope.launch(context) {
            flow.collect { callback(it) }
        }
    }
}
