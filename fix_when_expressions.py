#!/usr/bin/env python3

import re
import os
import sys

def find_when_expressions_in_file(file_path):
    """Find when expressions that need else branches in a Kotlin file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        when_expressions = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()
            
            # Look for when expressions
            if 'when' in stripped and ('when (' in stripped or 'when(' in stripped):
                # Find the opening brace
                brace_count = 0
                start_line = i
                when_start = i
                
                # Find where the when block starts
                j = i
                while j < len(lines):
                    if '{' in lines[j]:
                        brace_count += lines[j].count('{')
                        brace_count -= lines[j].count('}')
                        break
                    j += 1
                
                if brace_count > 0:
                    # Find the end of the when block
                    j += 1
                    while j < len(lines) and brace_count > 0:
                        brace_count += lines[j].count('{')
                        brace_count -= lines[j].count('}')
                        j += 1
                    
                    end_line = j - 1
                    
                    # Check if there's already an else branch
                    when_block = '\n'.join(lines[start_line:end_line + 1])
                    if 'else ->' not in when_block:
                        when_expressions.append((start_line, end_line, when_block))
                
                i = j if 'j' in locals() else i + 1
            else:
                i += 1
        
        return when_expressions
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []

def add_else_branch_to_when(file_path, when_expressions):
    """Add else -> {} branches to when expressions."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Process from bottom to top to avoid line number shifts
        for start_line, end_line, when_block in reversed(when_expressions):
            # Find the last closing brace of the when expression
            last_line = lines[end_line].rstrip()
            
            # Get the indentation of the closing brace
            indent = len(last_line) - len(last_line.lstrip())
            
            # Insert else branch before the closing brace
            else_branch = ' ' * (indent + 4) + 'else -> {}\n'
            lines.insert(end_line, else_branch)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        return True
    except Exception as e:
        print(f"Error modifying {file_path}: {e}")
        return False

def main():
    # List of files from the error output - batch 3
    error_files = [
        "app/src/main/java/com/bukuwarung/payments/ppob/confirmation/viewmodel/PpobOrderFormViewModel.kt",
        "app/src/main/java/com/bukuwarung/payments/ppob/reminders/view/ReminderFragment.kt",
        "app/src/main/java/com/bukuwarung/payments/selectbank/SelectBankActivity.kt",
        "app/src/main/java/com/bukuwarung/payments/viewmodels/AssistPageViewModel.kt",
        "app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt",
        "app/src/main/java/com/bukuwarung/payments/viewmodels/TopupSaldoViewModel.kt",
        "app/src/main/java/com/bukuwarung/activities/transaction/customer/CustomerTransactionViewModel.kt"
    ]
    
    for file_path in error_files:
        if os.path.exists(file_path):
            print(f"Processing {file_path}...")
            when_expressions = find_when_expressions_in_file(file_path)
            if when_expressions:
                if add_else_branch_to_when(file_path, when_expressions):
                    print(f"  Fixed {len(when_expressions)} when expression(s)")
                else:
                    print(f"  Failed to fix {file_path}")
            else:
                print(f"  No when expressions found needing fixes")
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
