package com.bukuwarung.buildsrc

object Libs {
    const val androidGradlePlugin = "com.android.tools.build:gradle:7.4.2"
    const val androidPerfDependency = "com.google.firebase:perf-plugin:1.4.1"

    object AndroidX {
        const val coreKtx = "androidx.core:core-ktx:1.6.0"
        const val constraintLayout = "androidx.constraintlayout:constraintlayout:2.0.4"
        const val appCompat = "androidx.appcompat:appcompat:1.4.2"
        const val multidex = "androidx.multidex:multidex:2.0.0"
        const val recyclerView = "androidx.recyclerview:recyclerview:1.0.0"
        const val cardView = "androidx.cardview:cardview:1.0.0"
        const val legacySupport = "androidx.legacy:legacy-support-v4:1.0.0"

        const val activityKtx = "androidx.activity:activity-ktx:1.1.0"
        const val browser = "androidx.browser:browser:1.2.0"
        const val swiperefreshlayout = "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"

        object Lifecycle {
            const val liveDataCore = "androidx.lifecycle:lifecycle-livedata-core:2.5.1"
            const val extensions = "androidx.lifecycle:lifecycle-extensions:2.2.0"
            const val compiler = "androidx.lifecycle:lifecycle-compiler:2.5.1"
            const val liveDataKtx = "androidx.lifecycle:lifecycle-livedata-ktx:2.5.1"
            const val runtime = "androidx.lifecycle:lifecycle-runtime-ktx:2.5.1"
            const val viewmodelKtx = "androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0"
            const val viewmodel = "androidx.lifecycle:lifecycle-viewmodel:2.4.0"
        }

        object Pagination {
            const val paginationLibrary = "androidx.paging:paging-runtime-ktx:3.0.0"
        }

        object Fragment {
            const val fragmentKtx = "androidx.fragment:fragment-ktx:1.4.1"
        }

        object Room {
            private const val version = "2.5.0"
            const val runtime = "androidx.room:room-runtime:$version"
            const val compiler = "androidx.room:room-compiler:$version"
            const val coroutine = "androidx.room:room-ktx:$version"
            const val roomRxJava =  "androidx.room:room-rxjava2:$version"
        }

        object Jetpack {
            private const val work_version = "2.7.0"
            const val workmanagerRuntime = "androidx.work:work-runtime-ktx:$work_version"
        }

        object Test {
            const val runner = "androidx.test:runner:1.2.0"
            const val espresso = "androidx.test.espresso:espresso-core:3.4.0"
            const val espressoContrib = "androidx.test.espresso:espresso-contrib:3.3.0-rc01"
            const val rules = "androidx.test:rules:1.3.0-rc01"
            const val uiautomator = "androidx.test.uiautomator:uiautomator:2.2.0"
            const val testExtJunit  = "androidx.test.ext:junit:1.1.3"
        }

        object Navigation {
            const val navigationSafeArgs = "androidx.navigation:navigation-safe-args-gradle-plugin:2.4.1"
            const val navigationFragmentKtx = "androidx.navigation:navigation-fragment-ktx:2.3.0-alpha02"
            const val navigationUIKtx = "androidx.navigation:navigation-ui-ktx:2.3.0-alpha02"

        }

        object Hilt {
            private const val version = "2.44"

            const val hilt = "com.google.dagger:hilt-android:$version"
            const val hiltCompiler = "com.google.dagger:hilt-android-compiler:$version"
            const val hiltTesting = "com.google.dagger:hilt-android-testing:$version"
            const val hiltPlugin = "com.google.dagger:hilt-android-gradle-plugin:$version"
            const val hiltWork = "androidx.hilt:hilt-work:1.0.0"
            const val kotlinHiltCompiler="androidx.hilt:hilt-compiler:1.0.0"
        }

        object  Camera {
            const val camera2 = "androidx.camera:camera-camera2:1.1.0-beta02"
            const val lifecycle = "androidx.camera:camera-lifecycle:1.1.0-beta02"
            const val cameraView = "androidx.camera:camera-view:1.1.0-beta02"
            const val cameraVidio = "androidx.camera:camera-video:1.1.0-beta02"
        }
    }

    object Testing {
        const val mockK = "io.mockk:mockk:1.10.0"
        const val robolectric = "org.robolectric:robolectric:4.3"
        const val jUnit = "junit:junit:4.13.2"
        const val mokito = "org.mockito:mockito-inline:4.2.0"
        const val mokitoCore = "org.mockito:mockito-core:2.1.0"
        const val mokitoKotlin = "org.mockito.kotlin:mockito-kotlin:4.0.0"
        const val coreTesting = "androidx.arch.core:core-testing:2.1.0"
        const val coroutinesTest = "org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.0"
        const val coreKtxTesting = "androidx.test:core-ktx:1.4.0"
    }

    object Google {
        const val googleServices = "com.google.gms:google-services:4.4.2"
        const val material = "com.google.android.material:material:1.9.0"
        const val autoService = "com.google.auto.service:auto-service:1.0.1"

        object Firebase {
            const val platform = "com.google.firebase:firebase-bom:30.2.0"
            const val messaging = "com.google.firebase:firebase-messaging"
            const val auth = "com.google.firebase:firebase-auth:19.2.0"
            const val firestore = "com.google.firebase:firebase-firestore-ktx:20.2.0"
            const val dynamicLinks = "com.google.firebase:firebase-dynamic-links:19.1.0"
            const val analytics = "com.google.firebase:firebase-analytics:17.5.0"
            const val crashlyticsGradle = "com.google.firebase:firebase-crashlytics-gradle:2.9.2"
            const val crashlytics = "com.google.firebase:firebase-crashlytics"
            const val storage = "com.google.firebase:firebase-storage-ktx:19.2.0"
            const val remoteConfig = "com.google.firebase:firebase-config-ktx:20.0.0"
            const val perf = "com.google.firebase:firebase-perf"
            const val perfKtx = "com.google.firebase:firebase-perf-ktx"
        }

        object PlayServices {
            const val location = "com.google.android.gms:play-services-location:21.0.1"
            const val authApiPhone = "com.google.android.gms:play-services-auth-api-phone:17.0.0"
            const val base = "com.google.android.gms:play-services-base:17.0.0"
            const val identity = "com.google.android.gms:play-services-identity:17.0.0"
            const val auth = "com.google.android.gms:play-services-auth:17.0.0"
            const val maps = "com.google.android.gms:play-services-maps:18.0.0"
            const val task = "com.google.android.gms:play-services-tasks:18.0.0"
            const val appUpdate = "com.google.android.play:app-update:2.1.0"
        }

        object Exoplayer {
            const val exoplayer = "com.google.android.exoplayer:exoplayer:2.15.1"
            const val exoplayerHls = "com.google.android.exoplayer:exoplayer-hls:2.15.1"
            const val exoplayerUi = "com.google.android.exoplayer:exoplayer-ui:2.15.1"
        }

        const val gson = "com.google.code.gson:gson:2.9.1"
        const val mlKitFaceDetection = "com.google.mlkit:face-detection:16.1.5"

    }

    object Android {
        const val volley = "com.android.volley:volley:1.2.0"
        const val installReferrer = "com.android.installreferrer:installreferrer:1.1.2"
        const val desugarJdkLibs = "com.android.tools:desugar_jdk_libs:1.1.5"
    }

    object JetBrains {
        object Kotlin {
            private const val version = "1.7.10"
            const val gradlePlugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:$version"
            const val stdLibJDK7 = "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$version"
            const val stdLib = "org.jetbrains.kotlin:kotlin-stdlib:$version"
        }

        object Coroutine {
            private const val version = "1.6.0"
            const val core = "org.jetbrains.kotlinx:kotlinx-coroutines-core:$version"
            const val android = "org.jetbrains.kotlinx:kotlinx-coroutines-android:$version"
            const val test = "org.jetbrains.kotlinx:kotlinx-coroutines-test:$version"
        }
    }

    object SquareUp {
        const val kotlinPoet = "com.squareup:kotlinpoet:1.10.0"

        object Retrofit {
            const val retrofit = "com.squareup.retrofit2:retrofit:2.9.0"
            const val gsonConverter = "com.squareup.retrofit2:converter-gson:2.9.0"
            const val okHttp = "com.squareup.okhttp3:okhttp:4.8.1"
            const val loggingInterceptor = "com.squareup.okhttp3:logging-interceptor:4.8.1"
            const val adapterRxjava2 = "com.squareup.retrofit2:adapter-rxjava2:2.9.0"
        }
    }

    object Glide {
        private const val version = "4.9.0"
        const val glide = "com.github.bumptech.glide:glide:$version"
        const val compiler = "com.github.bumptech.glide:compiler:$version"

    }

    object Facebook {
        const val androidSdk = "com.facebook.android:facebook-android-sdk:[5,9)"
        const val shimmer = "com.facebook.shimmer:shimmer:0.5.0"
    }

    object RxJava {
        const val rxKotlin = "io.reactivex.rxjava2:rxkotlin:2.4.0"
        const val rxJava2 = "io.reactivex.rxjava2:rxandroid:2.1.1"
        const val rxJava2RxJava = "io.reactivex.rxjava2:rxjava:2.2.19"
    }

    object Analytics {
        const val amplitude = "com.amplitude:android-sdk:2.16.0"
        const val appsflyer = "com.appsflyer:af-android-sdk:6.2.3"
    }

    object ThirdParty {
        const val lolliPin = "com.github.angga-bw:LolliPin:bw"
        const val typefaceView = "com.github.omadahealth.typefaceview:typefaceview:2.0.1@aar"
        const val rippleEffect = "com.github.traex.rippleeffect:ripple:1.3.1-OG"
        const val countryCodePicker = "com.hbb20:ccp:2.7.1"
        const val colorPicker = "com.github.angga-bw:ShiftColorPicker:c71dbb1a99"
        const val simpleTooltip = "com.github.angga-bw:android-simple-tooltip:0598b236e0"
        const val lottie = "com.airbnb.android:lottie:3.4.2"
        const val powerspinner = "com.github.skydoves:powerspinner:1.0.9"
        const val keyboardEvent = "net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC3"
        const val dotsIndicator = "com.tbuonomo:dotsindicator:4.3"
        const val zxingAndroid = "com.journeyapps:zxing-android-embedded:4.1.0"
        const val zxingCore = "com.google.zxing:core:3.3.0"
        const val barGraph ="com.github.PhilJay:MPAndroidChart:v3.0.3"
        const val progressbutton = "com.github.razir.progressbutton:progressbutton:2.1.0"
        const val tooLargeTool = "com.gu.android:toolargetool:0.3.0"
        const val zeloryCompressor = "id.zelory:compressor:3.0.1"
        const val kal72RackMonthPicker = "com.github.kal72:RackMonthPicker:1.6.1"
        const val dewinjmMonthyearPicker = "com.github.dewinjm:monthyear-picker:1.0.2"
        const val survicateSdk = "com.survicate:survicate-sdk:1.7.6"
        const val mixPanelAndroid = "com.mixpanel.android:mixpanel-android:7.5.2"
        const val salesiqMobilisten = "com.zoho.salesiq:mobilisten:7.1.1"
        const val mrmikeok2curl = "com.github.mrmike:ok2curl:0.8.0"
        const val paperdb = "io.github.pilgr:paperdb:2.7.1"
        const val bureauDeviceIntelligence = "id.bureau:device-intelligence:4.0.2"
        const val bureauAppnomics = "id.bureau:apponomics:4.0.2"

    }

    object Dagger {
        const val main = "com.google.dagger:dagger:2.31.2"
        const val mainAndroid = "com.google.dagger:dagger-android:2.31.2"
        const val androidSupport = "com.google.dagger:dagger-android-support:2.21"
        const val compiler = "com.google.dagger:dagger-compiler:2.31.2"
        const val processor = "com.google.dagger:dagger-android-processor:2.31.2"
    }

    object OAuth {
        const val oauthJWT = "com.auth0.android:jwtdecode:2.0.1"
    }

    object JavaX {
        const val inject = "javax.inject:javax.inject:1"
    }
}
