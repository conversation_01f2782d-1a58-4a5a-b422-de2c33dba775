name: Android CD [Update Playstore Alpha]

on:
  push:
     branches: [develop, 'release/**']
jobs:
  aab:
    name: Generate AAB
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: 'temurin'

      - name: Configure Gradle distribution
        run: sed -i -e 's/-all.zip/-bin.zip/' gradle/wrapper/gradle-wrapper.properties

      - name: Extract branch name
        shell: bash
        run: echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        id: extract_branch

      - name: Bump version
        run: |
          bash ./scripts/bump_version.sh
        shell: bash

      - name: Commit & Push changes
        uses: actions-js/push@master
        with:
          message: '[skip ci] increase app version code'
          branch: ${{ steps.extract_branch.outputs.branch }}
          github_token: ${{ secrets.ADMIN_WORKFLOW_TOKEN }}

      - name: Write Prod google-services.json
        run: |
          echo "$PROD_GSERVICE_JSON" > ./app/google-services.json
          echo "$(cat ./app/google-services.json)"
        shell: bash
        env:
          PROD_GSERVICE_JSON: ${{secrets.PROD_GSERVICE_JSON}}

      - name: Write Prod google-services.json & Assemble prod app bundle
        run: |
          echo "$PROD_GSERVICE_JSON" > ./app/google-services.json
          echo "$(cat ./app/google-services.json)"
          rm -rf ./app/src/main/res/values-en/strings.xml
          bash ./gradlew bundleProdRelease --stacktrace
        shell: bash
        env:
          PROD_GSERVICE_JSON: ${{secrets.PROD_GSERVICE_JSON}}

      - name: Sign prod app bundle
        id: sign
        uses: r0adkll/sign-android-release@v1
        with:
          releaseDirectory: app/build/outputs/bundle/prodRelease
          signingKeyBase64: ${{ secrets.SIGNING_KEY }}
          alias: ${{ secrets.RELEASE_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.RELEASE_KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.RELEASE_KEY_PASSWORD }}

      - name: Upload prod app bundle
        uses: actions/upload-artifact@v4
        with:
          name: app-prod-release
          path: app/build/outputs/bundle/prodRelease/app-prod-release.aab
          overwrite: true

      - name: Create service_account.json
        id: createServiceAccount
        run: echo '${{ secrets.SERVICE_ACCOUNT_JSON }}' > service_account.json

      - name: Deploy to Play Store (BETA)
        id: deploy
        uses: r0adkll/upload-google-play@v1.0.15
        with:
          serviceAccountJson: service_account.json
          packageName: com.bukuwarung
          releaseFiles: app/build/outputs/bundle/prodRelease/app-prod-release.aab
          track: internal
          whatsNewDirectory: changelog/

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_ICON: https://github.com/bukuwarung.png?size=48
          SLACK_TITLE: Alpha Build
          SLACK_LINK_NAMES: true
          SLACK_MESSAGE: Bhanu <@U03KWHAUF0E> Flo <@U01D1GYDVT9> Hum <@U02E4SA0545> Uday <@U02JA4DAM7X> Alpha build is available in internal track.
          SLACK_USERNAME: ApkBot
