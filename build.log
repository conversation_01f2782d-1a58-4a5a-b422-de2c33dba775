To honour the JVM settings for this build a single-use Daemon process will be forked. See https://docs.gradle.org/7.5/userguide/gradle_daemon.html#sec:disabling_the_daemon.
<PERSON> will be stopped at the end of the build 
> Task :buildSrc:compileKotlin UP-TO-DATE
> Task :buildSrc:compileJava NO-SOURCE
> Task :buildSrc:compileGroovy NO-SOURCE
> Task :buildSrc:pluginDescriptors UP-TO-DATE
> Task :buildSrc:processResources NO-SOURCE
> Task :buildSrc:classes UP-TO-DATE
> Task :buildSrc:inspectClassesForKotlinIC UP-TO-DATE
> Task :buildSrc:jar UP-TO-DATE
> Task :buildSrc:assemble UP-TO-DATE
> Task :buildSrc:compileTestKotlin NO-SOURCE
> Task :buildSrc:pluginUnderTestMetadata UP-TO-DATE
> Task :buildSrc:compileTestJava NO-SOURCE
> Task :buildSrc:compileTestGroovy NO-SOURCE
> Task :buildSrc:processTestResources NO-SOURCE
> Task :buildSrc:testClasses UP-TO-DATE
> Task :buildSrc:test NO-SOURCE
> Task :buildSrc:validatePlugins UP-TO-DATE
> Task :buildSrc:check UP-TO-DATE
> Task :buildSrc:build UP-TO-DATE

> Configure project :app
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
app: 'annotationProcessor' dependencies won't be recognized as kapt annotation processors. Please change the configuration name to 'kapt' for these artifacts: 'androidx.lifecycle:lifecycle-compiler:2.5.1', 'com.github.bumptech.glide:compiler:4.9.0'.

> Configure project :base-android
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.

> Configure project :buku-bluetooth-printer
Warning: The 'kotlin-android-extensions' Gradle plugin is deprecated. Please use this migration guide (https://goo.gle/kotlin-android-extensions-deprecation) to start working with View Binding (https://developer.android.com/topic/libraries/view-binding) and the 'kotlin-parcelize' plugin.
Warning: SDK processing. This version only understands SDK XML versions up to 3 but an SDK XML file of version 4 was encountered. This can happen if you use versions of Android Studio and the command-line tools that were released at different times.
WARNING:We recommend using a newer Android Gradle plugin to use compileSdk = 34

This Android Gradle plugin (7.4.2) was tested up to compileSdk = 33

This warning can be suppressed by adding
    android.suppressUnsupportedCompileSdk=34
to this project's gradle.properties

The build will continue, but you are strongly encouraged to update your project to
use a newer Android Gradle Plugin that has been tested with compileSdk = 34

> Task :buku-bluetooth-printer:preBuild UP-TO-DATE
> Task :neuro:preBuild UP-TO-DATE
> Task :base-android:preBuild UP-TO-DATE
> Task :ui-component:preBuild UP-TO-DATE
> Task :buku-bluetooth-printer:preDebugBuild UP-TO-DATE
> Task :neuro:preDebugBuild UP-TO-DATE
> Task :base-android:preDebugBuild UP-TO-DATE
> Task :ui-component:preDebugBuild UP-TO-DATE
> Task :stealth-compiler:processResources NO-SOURCE
> Task :buku-bluetooth-printer:compileDebugAidl NO-SOURCE
> Task :base-android:compileDebugAidl NO-SOURCE
> Task :neuro:compileDebugAidl NO-SOURCE
> Task :ui-component:compileDebugAidl NO-SOURCE
> Task :buku-bluetooth-printer:packageDebugRenderscript NO-SOURCE
> Task :neuro:packageDebugRenderscript NO-SOURCE
> Task :base-android:packageDebugRenderscript NO-SOURCE
> Task :ui-component:packageDebugRenderscript NO-SOURCE
> Task :buku-bluetooth-printer:compileDebugRenderscript NO-SOURCE
> Task :neuro:compileDebugRenderscript NO-SOURCE
> Task :buku-bluetooth-printer:generateDebugResValues UP-TO-DATE
> Task :ui-component:compileDebugRenderscript NO-SOURCE
> Task :buku-bluetooth-printer:generateDebugResources UP-TO-DATE
> Task :neuro:generateDebugResValues UP-TO-DATE
> Task :ui-component:generateDebugResValues UP-TO-DATE
> Task :neuro:generateDebugResources UP-TO-DATE
> Task :ui-component:generateDebugResources UP-TO-DATE
> Task :buku-bluetooth-printer:packageDebugResources UP-TO-DATE
> Task :neuro:packageDebugResources UP-TO-DATE
> Task :neuro:extractDeepLinksDebug UP-TO-DATE
> Task :neuro:processDebugManifest UP-TO-DATE
> Task :neuro:writeDebugAarMetadata UP-TO-DATE
> Task :neuro:compileDebugLibraryResources UP-TO-DATE
> Task :neuro:parseDebugLocalResources UP-TO-DATE
> Task :ui-component:packageDebugResources UP-TO-DATE
> Task :neuro:generateDebugRFile UP-TO-DATE
> Task :stealth:compileKotlin UP-TO-DATE
> Task :stealth:compileJava NO-SOURCE
> Task :stealth:processResources NO-SOURCE
> Task :stealth:classes UP-TO-DATE
> Task :stealth:inspectClassesForKotlinIC UP-TO-DATE
> Task :stealth:jar UP-TO-DATE
> Task :stealth-compiler:kaptGenerateStubsKotlin UP-TO-DATE
> Task :stealth-compiler:kaptKotlin UP-TO-DATE
> Task :stealth-compiler:compileKotlin UP-TO-DATE
> Task :stealth-compiler:compileJava NO-SOURCE
> Task :stealth-compiler:classes UP-TO-DATE
> Task :stealth-compiler:inspectClassesForKotlinIC UP-TO-DATE
> Task :stealth-compiler:jar UP-TO-DATE
> Task :neuro:compileDebugKotlin UP-TO-DATE
> Task :neuro:javaPreCompileDebug UP-TO-DATE
> Task :neuro:compileDebugJavaWithJavac NO-SOURCE
> Task :neuro:bundleLibCompileToJarDebug UP-TO-DATE
> Task :neuro:mergeDebugShaders UP-TO-DATE
> Task :neuro:compileDebugShaders NO-SOURCE
> Task :neuro:generateDebugAssets UP-TO-DATE
> Task :neuro:packageDebugAssets UP-TO-DATE
> Task :ui-component:dataBindingMergeDependencyArtifactsDebug UP-TO-DATE
> Task :buku-bluetooth-printer:dataBindingMergeDependencyArtifactsDebug UP-TO-DATE
> Task :neuro:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :neuro:processDebugJavaRes NO-SOURCE
> Task :neuro:bundleLibResDebug UP-TO-DATE
> Task :neuro:createFullJarDebug UP-TO-DATE
> Task :neuro:mergeDebugJniLibFolders UP-TO-DATE
> Task :neuro:mergeDebugNativeLibs NO-SOURCE
> Task :neuro:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :base-android:dataBindingMergeDependencyArtifactsDebug UP-TO-DATE
> Task :ui-component:dataBindingGenBaseClassesDebug UP-TO-DATE
> Task :buku-bluetooth-printer:dataBindingGenBaseClassesDebug UP-TO-DATE
> Task :ui-component:extractDeepLinksDebug UP-TO-DATE
> Task :buku-bluetooth-printer:extractDeepLinksDebug UP-TO-DATE
> Task :base-android:compileDebugRenderscript NO-SOURCE
> Task :base-android:generateDebugResValues UP-TO-DATE
> Task :base-android:generateDebugResources UP-TO-DATE
> Task :ui-component:processDebugManifest UP-TO-DATE
> Task :buku-bluetooth-printer:processDebugManifest UP-TO-DATE
> Task :ui-component:writeDebugAarMetadata UP-TO-DATE
> Task :buku-bluetooth-printer:writeDebugAarMetadata UP-TO-DATE
> Task :buku-bluetooth-printer:compileDebugLibraryResources UP-TO-DATE
> Task :base-android:packageDebugResources UP-TO-DATE
> Task :buku-bluetooth-printer:parseDebugLocalResources UP-TO-DATE
> Task :ui-component:compileDebugLibraryResources UP-TO-DATE
> Task :ui-component:parseDebugLocalResources UP-TO-DATE
> Task :buku-bluetooth-printer:generateDebugRFile UP-TO-DATE
> Task :base-android:dataBindingGenBaseClassesDebug UP-TO-DATE
> Task :ui-component:generateDebugRFile UP-TO-DATE
> Task :buku-bluetooth-printer:generateDebugBuildConfig UP-TO-DATE
> Task :ui-component:generateDebugBuildConfig UP-TO-DATE
> Task :base-android:parseDebugLocalResources UP-TO-DATE
> Task :base-android:processDebugManifest UP-TO-DATE
> Task :base-android:generateDebugRFile UP-TO-DATE
> Task :base-android:dataBindingTriggerDebug UP-TO-DATE
> Task :base-android:generateDebugBuildConfig UP-TO-DATE
> Task :ui-component:compileDebugKotlin UP-TO-DATE
> Task :ui-component:javaPreCompileDebug UP-TO-DATE
> Task :buku-bluetooth-printer:kaptGenerateStubsDebugKotlin UP-TO-DATE
> Task :ui-component:compileDebugJavaWithJavac UP-TO-DATE
> Task :ui-component:bundleLibCompileToJarDebug UP-TO-DATE
> Task :ui-component:mergeDebugShaders UP-TO-DATE
> Task :ui-component:compileDebugShaders NO-SOURCE
> Task :ui-component:generateDebugAssets UP-TO-DATE
> Task :ui-component:packageDebugAssets UP-TO-DATE
> Task :ui-component:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :ui-component:processDebugJavaRes NO-SOURCE
> Task :ui-component:bundleLibResDebug UP-TO-DATE
> Task :ui-component:createFullJarDebug UP-TO-DATE
> Task :ui-component:mergeDebugJniLibFolders UP-TO-DATE
> Task :ui-component:mergeDebugNativeLibs NO-SOURCE
> Task :ui-component:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :buku-bluetooth-printer:kaptDebugKotlin UP-TO-DATE
> Task :buku-bluetooth-printer:compileDebugKotlin UP-TO-DATE
> Task :base-android:kaptGenerateStubsDebugKotlin UP-TO-DATE
> Task :buku-bluetooth-printer:javaPreCompileDebug UP-TO-DATE
> Task :buku-bluetooth-printer:compileDebugJavaWithJavac UP-TO-DATE
> Task :buku-bluetooth-printer:bundleLibCompileToJarDebug UP-TO-DATE
> Task :buku-bluetooth-printer:mergeDebugShaders UP-TO-DATE
> Task :buku-bluetooth-printer:compileDebugShaders NO-SOURCE
> Task :buku-bluetooth-printer:generateDebugAssets UP-TO-DATE
> Task :buku-bluetooth-printer:packageDebugAssets UP-TO-DATE
> Task :buku-bluetooth-printer:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :buku-bluetooth-printer:processDebugJavaRes NO-SOURCE
> Task :buku-bluetooth-printer:bundleLibResDebug UP-TO-DATE
> Task :buku-bluetooth-printer:createFullJarDebug UP-TO-DATE
> Task :buku-bluetooth-printer:mergeDebugJniLibFolders UP-TO-DATE
> Task :buku-bluetooth-printer:mergeDebugNativeLibs NO-SOURCE
> Task :buku-bluetooth-printer:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :base-android:kaptDebugKotlin UP-TO-DATE
> Task :base-android:extractDeepLinksDebug UP-TO-DATE
> Task :base-android:writeDebugAarMetadata UP-TO-DATE
> Task :base-android:compileDebugLibraryResources UP-TO-DATE
> Task :base-android:compileDebugKotlin UP-TO-DATE
> Task :base-android:javaPreCompileDebug UP-TO-DATE
> Task :base-android:compileDebugJavaWithJavac UP-TO-DATE
> Task :base-android:bundleLibCompileToJarDebug UP-TO-DATE
> Task :base-android:mergeDebugShaders UP-TO-DATE
> Task :base-android:compileDebugShaders NO-SOURCE
> Task :base-android:generateDebugAssets UP-TO-DATE
> Task :base-android:packageDebugAssets UP-TO-DATE
> Task :base-android:bundleLibRuntimeToJarDebug UP-TO-DATE
> Task :base-android:processDebugJavaRes NO-SOURCE
> Task :base-android:bundleLibResDebug UP-TO-DATE
> Task :base-android:createFullJarDebug UP-TO-DATE
> Task :base-android:mergeDebugJniLibFolders UP-TO-DATE
> Task :base-android:mergeDebugNativeLibs NO-SOURCE
> Task :base-android:copyDebugJniLibsProjectOnly UP-TO-DATE
> Task :app:preBuild UP-TO-DATE
> Task :app:preProdDebugBuild UP-TO-DATE
> Task :app:mergeProdDebugNativeDebugMetadata NO-SOURCE
> Task :app:generateProdDebugResValues UP-TO-DATE
> Task :app:injectCrashlyticsMappingFileIdProdDebug UP-TO-DATE
> Task :app:compileProdDebugAidl NO-SOURCE
> Task :app:compileProdDebugRenderscript NO-SOURCE
> Task :app:dataBindingMergeDependencyArtifactsProdDebug UP-TO-DATE
> Task :app:generateProdDebugResources UP-TO-DATE
> Task :app:processProdDebugGoogleServices UP-TO-DATE
> Task :app:mergeProdDebugResources UP-TO-DATE
> Task :app:dataBindingGenBaseClassesProdDebug UP-TO-DATE
> Task :app:dataBindingTriggerProdDebug UP-TO-DATE
> Task :app:generateProdDebugBuildConfig UP-TO-DATE
> Task :app:generateSafeArgsProdDebug UP-TO-DATE
> Task :app:mapProdDebugSourceSetPaths UP-TO-DATE
> Task :app:createProdDebugCompatibleScreenManifests UP-TO-DATE
> Task :app:extractDeepLinksProdDebug UP-TO-DATE
> Task :app:processProdDebugMainManifest UP-TO-DATE
> Task :app:processProdDebugManifest UP-TO-DATE
> Task :app:processProdDebugManifestForPackage UP-TO-DATE
> Task :app:checkProdDebugAarMetadata UP-TO-DATE
> Task :app:processProdDebugResources UP-TO-DATE
> Task :app:kaptGenerateStubsProdDebugKotlin UP-TO-DATE
> Task :app:kaptProdDebugKotlin UP-TO-DATE
> Task :app:javaPreCompileProdDebug UP-TO-DATE
> Task :app:mergeProdDebugShaders UP-TO-DATE
> Task :app:compileProdDebugShaders NO-SOURCE
> Task :app:generateProdDebugAssets UP-TO-DATE
> Task :app:mergeProdDebugAssets UP-TO-DATE
> Task :app:compressProdDebugAssets UP-TO-DATE
> Task :app:l8DexDesugarLibProdDebug UP-TO-DATE
> Task :app:checkProdDebugDuplicateClasses UP-TO-DATE
> Task :app:desugarProdDebugFileDependencies UP-TO-DATE
> Task :app:mergeExtDexProdDebug UP-TO-DATE
> Task :app:mergeLibDexProdDebug UP-TO-DATE
> Task :app:processProdDebugJavaRes NO-SOURCE
> Task :app:mergeProdDebugJniLibFolders UP-TO-DATE
> Task :app:mergeProdDebugNativeLibs UP-TO-DATE
> Task :app:stripProdDebugDebugSymbols UP-TO-DATE
> Task :app:validateSigningProdDebug UP-TO-DATE
> Task :app:writeProdDebugAppMetadata UP-TO-DATE
> Task :app:writeProdDebugSigningConfigVersions UP-TO-DATE
> Task :app:compileProdDebugKotlin
w: Argument -Xopt-in is deprecated. Please use -opt-in instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/inventory/detail/EditStockActivity.kt: (352, 13): 'when' expression must be exhaustive, add necessary 'is AllStock', 'HideLoading', 'HideStockNumberView', 'is MinMAxStockVisibility', 'is MinimumStock', 'is OpenMeasurementBottomSheet', 'is SetProductCount', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/inventory/detail/EditStockActivity.kt: (405, 13): 'when' expression must be exhaustive, add necessary 'is CloseBottomSheet', 'HideLoading', 'ShowAddUnit', 'ShowLoading' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/inventory/detail/EditStockActivity.kt: (451, 13): 'when' expression must be exhaustive, add necessary 'OnCategoryNameAlreadyExist', 'is ProductsByCategoryId', 'is SetCategorySuggestion' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/inventory/detail/EditStockActivity.kt: (479, 9): 'when' expression must be exhaustive, add necessary 'Loading', 'NotFound' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/LoginActivity.kt: (138, 13): 'when' expression must be exhaustive, add necessary 'is ProceedWithGuestSession' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/NewLoginActivity.kt: (241, 13): 'when' expression must be exhaustive, add necessary 'is ProceedWithGuestSession' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/VerifyOtpActivity.kt: (225, 13): 'when' expression must be exhaustive, add necessary 'is OtpVerified' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/form/DetailedBusinessFormViewModel.kt: (68, 9): 'when' expression must be exhaustive, add necessary 'is BookValidationError', 'BookValidationSuccess' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/form/DetailedBusinessFormViewModel.kt: (74, 9): 'when' expression must be exhaustive, add necessary 'BusinessName', 'UsageGoal', 'UsagePast' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/form/DetailedBusinessFormViewModel.kt: (140, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/form/DetailedBusinessFormViewModel.kt: (187, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/form/FormViewModel.kt: (51, 13): 'when' expression must be exhaustive, add necessary 'BusinessTiming' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/form/FormViewModel.kt: (125, 33): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/onboarding/form/TextFieldFormFragment.kt: (87, 13): 'when' expression must be exhaustive, add necessary 'BookValidationSuccess' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/payment/PaymentTabFragment.kt: (215, 13): 'when' expression must be exhaustive, add necessary 'OnBnplDetailsRequested' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/pos/PosCartFragment.kt: (98, 13): 'when' expression must be exhaustive, add necessary 'is OnProductLoaded', 'is OnTransactionSaved' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/pos/PosPaymentFragment.kt: (116, 13): 'when' expression must be exhaustive, add necessary 'is OnProductLoaded' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/pos/PosStoreFrontFragment.kt: (226, 13): 'when' expression must be exhaustive, add necessary 'is OnTransactionSaved' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/pos/experiments/PosPaymentWalletFragment.kt: (105, 13): 'when' expression must be exhaustive, add necessary 'is OnProductLoaded' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/productcategory/view/CategoryAssociatorActivity.kt: (107, 13): 'when' expression must be exhaustive, add necessary 'OnCategoryNameAlreadyExist', 'OnSelectionCached', 'is SetCategorySuggestion' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/productcategory/view/ProductCategoryActivity.kt: (135, 13): 'when' expression must be exhaustive, add necessary 'is ProductsByCategoryId' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/ProfileTabFragment.kt: (291, 13): 'when' expression must be exhaustive, add necessary 'is Error', 'HideBnplEntryPoint', 'RefreshState', 'ShowBnplEntryPoint', 'is setProfileUrlToUserEntity' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/ProfileTabViewModel.kt: (216, 9): 'when' expression must be exhaustive, add necessary 'DisconnectBrickAccount', 'GetBrickAccounts', 'OnAutoRecordTabClicked', 'is OnAutoRecordTabWarningPopUpSelected' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/ProfileTabViewModel.kt: (456, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/ProfileTabViewModel.kt: (516, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/ProfileTabViewModel.kt: (531, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/businessprofile/CreateBusinessProfileActivity.kt: (263, 13): 'when' expression must be exhaustive, add necessary 'RequestToOpenAddressFlow' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/update/EditUserProfileFragment.kt: (424, 13): 'when' expression must be exhaustive, add necessary 'is AutoRecordTabInitialVisibility', 'is ChangeStockSwitchStatus', 'is ContinueOnUpdateClicked', 'is Error', 'GoToRegisterBankAccount', 'is HandlePaymentVisibility', 'HideBnplEntryPoint', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/update/UserProfileDetailsFragment.kt: (186, 13): 'when' expression must be exhaustive, add necessary 'is AutoRecordTabInitialVisibility', 'is ChangeStockSwitchStatus', 'is ContinueOnUpdateClicked', 'is ContinueUploadImage', 'is HandlePaymentVisibility', 'HideBnplEntryPoint', 'is OnLoyaltySectionVisibility', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/profile/update/dialogs/UserProfileActivity.kt: (115, 13): 'when' expression must be exhaustive, add necessary 'is AutoRecordTabInitialVisibility', 'is ChangeStockSwitchStatus', 'is ContinueOnUpdateClicked', 'is ContinueUploadImage', 'is Error', 'is HandlePaymentVisibility', 'HideBnplEntryPoint', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/referral/payment_referral/ReferralActivity.kt: (445, 13): 'when' expression must be exhaustive, add necessary 'is DisplayContacts', 'is ShowLoaderOnSearchFragment', 'is ShowLoaderOnSearchResultsFragment', 'is UpdateSelectedCustomer' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/supplier/AddSupplierViewModel.kt: (112, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/activities/transaction/customer/CustomerTransactionViewModel.kt: (96, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/appsflyer/AppsFlyerViewModel.kt: (16, 10): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/collectingcalendar/main/CollectingCalendarActivity.kt: (158, 13): 'when' expression must be exhaustive, add necessary 'null' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/commonview/view/BukuTileView.kt: (214, 13): 'when' expression must be exhaustive, add necessary 'is AutoRecordTabInitialVisibility', 'is ChangeStockSwitchStatus', 'is ContinueOnUpdateClicked', 'is ContinueUploadImage', 'is Error', 'GoToRegisterBankAccount', 'is HandlePaymentVisibility', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/commonview/view/BukuTileView.kt: (230, 13): 'when' expression must be exhaustive, add necessary 'is BannerList', 'BnplError', 'is BnplInfo', 'is GoToCreatePayment', 'is IsCRMReminderAvailable', 'is IsPpobAvailable', 'is SaldoState', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/commonview/viewmodel/BukuTileViewModel.kt: (27, 9): 'when' expression must be exhaustive, add necessary 'else' branch
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/contact/ui/ContactSearchFragment.kt: (89, 13): 'when' expression must be exhaustive, add necessary 'is DeviceContactsLoaded', 'is DisplayContacts', 'is ShowLoaderOnSearchFragment', 'is UpdateSelectedCustomer' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/contact/ui/ContactSearchResultsFragment.kt: (83, 13): 'when' expression must be exhaustive, add necessary 'is DeviceContactsLoaded', 'is ShowLoaderOnSearchFragment' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/dialogs/login/LoginBottomSheetDialog.kt: (146, 13): 'when' expression must be exhaustive, add necessary 'is GoToVerifyPassword', 'is ProceedWithGuestSession', 'is SetSkipLoginButton', 'is SkipUserLogin' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/dialogs/login/VerifyOtpBottomSheetDialog.kt: (199, 13): 'when' expression must be exhaustive, add necessary 'NewOtpRequestSuccess', 'NewOtpRequested', 'is OtpChannelChanged', 'is OtpRequestError', 'is OtpVerified', 'is SkipUserLogin' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/game/viewmodel/GameViewModel.kt: (33, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'null' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/inventory/ui/InventoryActivity.kt: (44, 20): Only safe (?.) or non-null asserted (!!.) calls are allowed on a nullable receiver of type FragmentTransaction?
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/inventory/ui/InventoryFragment.kt: (365, 13): 'when' expression must be exhaustive, add necessary 'OnCategoryNameAlreadyExist', 'OnSelectionCached', 'is ProductsByCategoryId' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/inventory/ui/product/AddProductFragment.kt: (320, 13): 'when' expression must be exhaustive, add necessary 'is CloseBottomSheet', 'HideLoading', 'ShowAddUnit', 'ShowLoading' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/addbank/AddBankAccountViewModel.kt: (247, 17): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/bottomsheet/BankAccountListBottomSheetFragment.kt: (258, 13): 'when' expression must be exhaustive, add necessary 'is ApiError', 'is OnBackPressed' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/bottomsheet/PpobBillDetailsBottomSheet.kt: (131, 13): 'when' expression must be exhaustive, add necessary 'is InternetError', 'is OtherError', 'is SearchInternetError', 'is SearchServerError', 'is ServerError', 'is ShowBillerList', 'is ShowProductsList', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/checkout/PaymentCheckoutViewModel.kt: (585, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/view/PaymentConfirmationPageActivity.kt: (201, 13): 'when' expression must be exhaustive, add necessary 'is PaymentMethodsData' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentConfirmationViewModel.kt: (117, 17): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentConfirmationViewModel.kt: (183, 17): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentConfirmationViewModel.kt: (292, 17): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentConfirmationViewModel.kt: (348, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentConfirmationViewModel.kt: (372, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentInputViewModel.kt: (103, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentInputViewModel.kt: (133, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentOutViewModel.kt: (31, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentRecentAndFavouriteViewModel.kt: (96, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentRecentAndFavouriteViewModel.kt: (118, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/PaymentRecentAndFavouriteViewModel.kt: (140, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/SearchBankAccountItemViewModel.kt: (87, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/SearchBankAccountItemViewModel.kt: (108, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/core/viewmodel/SearchBankAccountItemViewModel.kt: (130, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/deeplink/handler/QrisSignalHandler.kt: (90, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/pin/NewPaymentPinViewModel.kt: (136, 17): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/pin/NewPaymentPinViewModel.kt: (185, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/pin/NewPaymentPinViewModel.kt: (209, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/pin/NewPaymentPinViewModel.kt: (246, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/pin/NewPaymentPinViewModel.kt: (272, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/pin/NewPaymentPinViewModel.kt: (286, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/pin/NewPaymentPinViewModel.kt: (320, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/EwalletBillersActivity.kt: (76, 13): 'when' expression must be exhaustive, add necessary 'is OtherError', 'is RefreshFavourite', 'is SearchInternetError', 'is SearchServerError', 'is ShowProductsList', 'ShowProfileDialog', 'is ShowTrainTicketDetail', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/NewFavouriteFragment.kt: (93, 13): 'when' expression must be exhaustive, add necessary 'is InternetError', 'is OtherError', 'is SearchInternetError', 'is SearchServerError', 'is ServerError', 'is ShowBillerList', 'is ShowProductsList', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/NewRecentFragment.kt: (107, 13): 'when' expression must be exhaustive, add necessary 'is InternetError', 'is OtherError', 'is SearchInternetError', 'is SearchServerError', 'is ServerError', 'is ShowBillerList', 'is ShowProductsList', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/NewRecentFragment.kt: (121, 13): 'when' expression must be exhaustive, add necessary 'is SetData', 'is SwitchToRecent' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/PpobActivity.kt: (266, 9): 'when' expression must be exhaustive, add necessary 'null' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/PpobBillersDialog.kt: (116, 17): 'when' expression must be exhaustive, add necessary 'is InternetError', 'is OtherError', 'is RefreshFavourite', 'is ServerError', 'is ShowBillerList', 'ShowProfileDialog', 'is ShowTrainTicketDetail', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/RecentAndFavouriteFragment.kt: (98, 13): 'when' expression must be exhaustive, add necessary 'is ShowPaymentList', 'ShowRecentEmpty', 'is ShowRecentError' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/view/WaterBillAreaDialog.kt: (78, 17): 'when' expression must be exhaustive, add necessary 'is InternetError', 'is OtherError', 'is RefreshFavourite', 'is SearchInternetError', 'is SearchServerError', 'is ServerError', 'is ShowBillerList', ... branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/viewmodel/EwalletBillersViewModel.kt: (28, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/viewmodel/PpobViewModel.kt: (81, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/viewmodel/PpobViewModel.kt: (113, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/viewmodel/PpobViewModel.kt: (142, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/base/viewmodel/PpobViewModel.kt: (329, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/view/CatalogActivity.kt: (80, 17): 'when' expression must be exhaustive, add necessary 'is CurrentProductItem', 'DismissBottomSheet', 'ShowLoadingOnBottomSheet' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/view/PromotionBannerFragment.kt: (54, 17): 'when' expression must be exhaustive, add necessary 'is ShowFilters', 'is ShowInternetError', 'ShowLoader', 'is ShowServerError' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/view/SetSellingPriceBottomSheet.kt: (88, 13): 'when' expression must be exhaustive, add necessary 'is ShowBillerList', 'is ShowFilter', 'is ShowInternetError', 'ShowInternetErrorBottomSheet', 'ShowLoader', 'is ShowServerError', 'is ShowServerErrorBottomSheet' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/viewmodel/PpobCatalogViewModel.kt: (64, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/viewmodel/PpobCatalogViewModel.kt: (85, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/viewmodel/PpobCatalogViewModel.kt: (154, 13): 'when' expression must be exhaustive, add necessary 'is ApiSuccessResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/viewmodel/PromotionViewModel.kt: (44, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/catalog/viewmodel/PromotionViewModel.kt: (63, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/confirmation/viewmodel/LoyaltyTierDiscountsBSViewModel.kt: (31, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/confirmation/viewmodel/PpobOrderFormViewModel.kt: (181, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/confirmation/viewmodel/PpobOrderFormViewModel.kt: (254, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/confirmation/viewmodel/PpobOrderFormViewModel.kt: (268, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/confirmation/viewmodel/PpobOrderFormViewModel.kt: (372, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/confirmation/viewmodel/PpobOrderFormViewModel.kt: (414, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/ppob/reminders/view/ReminderFragment.kt: (104, 13): 'when' expression must be exhaustive, add necessary 'is ProfitChange' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/selectbank/SelectBankActivity.kt: (78, 13): 'when' expression must be exhaustive, add necessary 'is ApiError', 'is ConnectionError', 'is PaymentMethodsData' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/AssistPageViewModel.kt: (100, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (329, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (428, 17): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (628, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (760, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (824, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (882, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (893, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (990, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/PaymentHistoryDetailViewModel.kt: (1012, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/TopupSaldoViewModel.kt: (59, 9): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse', 'is ApiErrorResponse' branches or 'else' branch instead
e: /Users/<USER>/Source/work/bukuwarung-app/app/src/main/java/com/bukuwarung/payments/viewmodels/TopupSaldoViewModel.kt: (157, 13): 'when' expression must be exhaustive, add necessary 'is ApiEmptyResponse' branch or 'else' branch instead

> Task :app:compileProdDebugKotlin FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileProdDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.

* Get more help at https://help.gradle.org

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.5/userguide/command_line_interface.html#sec:command_line_warnings

BUILD FAILED in 53s
138 actionable tasks: 1 executed, 137 up-to-date
